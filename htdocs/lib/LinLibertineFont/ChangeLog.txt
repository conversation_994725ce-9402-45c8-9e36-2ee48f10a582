LOFP -- http://linuxlibertine.sf.net

Please note: The underlined variant is recently not being maintained because 
its concept doesnt seem to be sofware-technically reliable and because of lack of interest. 

Changes to version 2.8.14 regular(|) & italic(/) & capitals (20080619)

- TTF-Hinting improvements because of better TTF-Instruction of FontForge
- some small kerning improvements
- glyphs U+02BE and U+02BF are no combining charakters > corrected
- kerning problem with 9 in italic corrected
- some small corrections as usual
- slavonic church signs added
- Added cartouches for numbers between 0 and 99 at uniE128 till uniE12A
- Change punctuationspace to width of fullstop (.) It is now 450 of width and not 350.


Changes to version 2.7.9 regular(|) & italic(/) & capitals (20071228)

>Inconsistent/incorrect glyph names 2.7.9   	 Private: (?)
>No
>The fonts do not use glyph names consistently. LinLibertineC_Re-2.7.3..ttf
>is currently especially problematic. For example, 
>f_i is called uniE0B8
>f_l uniE0B9
both chars removed (senseless in a caps-font)

>Euro.fitted zero.oldstyle
>Yen.fitted one.oldstyle
>perthousandzero five.oldstyle
>zero.oldstyle six.oldstyle
>one.oldstyle seven.oldstyle
>two.oldstyle uniE022
>three.oldstyle nine.oldstyle
>four.oldstyle uniE024
>five.oldstyle uniE025
>six.oldstyle uniE026
>seven.oldstyle uniE027
>eight.oldstyle uniE028
>nine.oldstyle uniE029
All above corrected


>zero.taboldstyle uniE118
>one.taboldstyle uniE119
>two.taboldstyle uniE11A
>three.taboldstyle uniE11B
>four.taboldstyle uniE11C
>five.taboldstyle uniE11D
>six.taboldstyle uniE11E
>seven.taboldstyle uniE11F
>eight.taboldstyle uniE120
>nine.taboldstyle uniE121
All above corrected

>In LinLibertineU_Re-2.2.7.ttf, for example,
>f_f is called ff
>f_i fi
>f_l fl
>f_f_i ffi
>f_f_l ffl
>s_t st
All above corrected


>In LinLibertine_BI-2.7.2.ttf, for example,
>z.alt is called uniE093
>J.alt uniE094
>and the following names refer to blank glyphs:
>f_b
>f_h
>f_j
>f_f_b
>f_f_j
>f_f_k
>f_f_h
>f_f_t
All above corrected

>The glyph names listed as "correct" above are based on what the majority of
>the fonts in the family use. (E.g. it would actually be easier if the
>ligatures were named ff, fi, fl etc. but since only a few are named in that
>way, I assume f_f, f_i, f_l etc. are the intended names.)
These are actually the names Adobe defined for ligatures in OpenTypes always a_b_c...

>The inconsistent naming of glyphs and the use of names for blank glyphs
>make installing for use with pdftex significantly more complex and much
>more frustrating than is necessary. 
Yes, this is true. That was of course a bug.

>I don't know how it affects other uses
>of the fonts, however.
Wrong PS-Names affect just OpenType-capable programs. Normal office use is not affected.
 
>The examples I've given are those which were most
>important to me when preparing metric files and virtual fonts for use with
>pdftex.










Changes to version 2.7.9 regular(|) & italic(/) & capitals (20071228)
- new Hinting! Now it is possible to use Libertine with Word on Windows properly. The shape on screen should be clearer now on every system!
- new kern pairs for better typography
- some wrong contour directions corrected
- many small corrections
- OT "fina" has been removed from wordend-sigma
- countless small improvements
- kerning for Caps variant
- the conflict (that came up lately with a library update) between underlined and regular is diminished


Changes to version 2.6.9 regular(|) & italic(/) & capitals (20070423)

Gernerally: 
	- Symbols for „page“, „graph“, „picture“ and „table“ added
	- Oldstyle numbers (minuscle numbers) were just contained as proportionals. Somebody wanted them also as table numbers. Ladder became standard oldstyles now. Some of these glyphs look somehow thinner to fit them on the fixed width.
	-bold italic variant had a wrong style entry which seemed to confuse Mac's fonthandling, also other style confusions should be fixed now
	-bold italic also had wrong metrics which resulted in a cutoff of glyph parts and wrong line spacing.

German interests:
	-großes Eszett hinzugefügt (uni1E9E) entsprechend Kapitälchen-Eszett
	-kleines Eszett so verändert, dass es zum großen passt (ſz statt  ſs), das vorige Eszett ist als germandbls.alt weiterenthalten
	-Im Stilsatz ss03 kann für Schweizer nun automatisch von Eszetts auf ss umgestellt werden.
	-die dt. ÄÖÜ sind nun von der Alternativposition an die rechte Stelle gerückt. Für die nichtdeutschsprachige Nutzung empfehlen sich jetzt die (vormals standart, jetzt aber auf die Alternativposition gewechselten Akzentuierten)

Cyrillic:
	Due to A. Panovs help hopefully a lot of design impriovements for the cyrrilic alphabet. 
	Mainly: Є Љ Њ Б З Ф Ш Щ Ъ Ы Ь Э Ю Ө (and its small letter derivates) For details:
		("Ь" in regular typeface has extra contour at left bottom.)
		--corrected--

		Lower part of "Ь" and "Ъ" (and for lowercase letters "ь", "ы", "ъ",
		"ѣ") differ. I think the letter "Ь" (or "ь" for lowercase) should be
		used as base for others.

		("Ф" is still slightly asymmetric with respect to vertical axis. It may produce artefacts with rendering.)
		--corrected--

		(Middle vertical stems in "Ш", "Щ" have not rounded lower edges.)
		--corrected--

		("Ш", "ш" are not quite symmetric agains middle vertical axis (all
		typefaces). "Щ", "щ" must be updated accordingly.)
		--corrected--

		(The tongue in "Э" can have two stylistic variants: straight and
		tilde-like. )
		OK, then tilde-like.

		(The designer decides which version should be selected. Some other letters should follow this decision: U+0472, U+04E8, U+0404, etc.)
		--corrected--

		(The capital letters U+0472 and U+04E8 usually are identical, excepting the case of archaic (before ca. XVIII century) form of U+0472.) 
		--corrected--

		(The U+0473 frequently is narrower than "o" and U+04E9. In case of a tilde-tongue in "Э" a tongue in U+0404 is not flipped, i.e. it has the same diriction as in "Э".)
		--corrected--

Small Caps:
	- Some changes here for better spacing.
	- FO-overlap (due to wrong kerning entry) removed

Bolded:
	- Complete revision of the bold version. Should result in a clearer shape and better thin-width-contrast

OpenType: 
	- Due to better support in the FontForge editor the OpenType-tables have been completely redefined.



Changes to version 2.5.9 regular(|) & italic(/) & capitals (20070423)
- countless improvements as usual
- some unicode standard optimizations (NUL-glyph, .notdef, ...)
- U+0180 group of charakters were sometimes not the way the latest unicode wants them so we changed them. This refers mainly to the differentiation of capital and small letters
- Vietnamese horned vokals have been changed the way, that the horn is now always at the same hight. All accented vietnamese glyphs were revised
- some optic improvements in placement of accents
- added a capital variant of hookabove and changed all the capital letters to this new hookabove form. It is wider and lower for better line-holding
- kerning was greatly improved, i.e.: Kombinations like "f)", "f?", "(j"... shouldn't overlap anymore. Capital letter words should now be better spaced.
- There was one wrong cyrrilic letter Lje (U+0409). 
And there were some correct objections to the handling of the downgoing serif in Dzhe/dzhe, De/de, Tse/tse, Shcha/shcha. These now have the same depth and similar form
- Some small changes in cyrrilics most complex letter Zhe 
but also in Ef 
in the hardening and softening signs (should not look like a reversed P but like the bud of the B), 
Ya shouldn't look like a flipped R, 
be had a too right tail, 
breve.cyr didn't look cyrrillic enough, 
ef shouldn't be too calligraphic, 
dje/she have now x-hight-bars
- due to a bug U+1fc7 (an accented eta) was missing
- some eagled-eyed person had seen that the U+721 was not at its place
- perthousandzero added at U+E00E for full LaTex-support
- some accented smallcaps added to basic font table for LaTex-support (This doesn't affect the Caps-version of Libertine, which already had all these glyphs)
- removed a cyrrilic opentype entry that substituted N-o with the numberligature because a stupid GNOME environment read out this table entry on a Spanish system and messed up typography...


Changes to version 2.4.9 regular(|) & italic(/) & capitals (20070309)
- many countless improvements
- some letters are slimer now: a, k, c, r
this is especially useful for languages with longs words. German in the first place...
- on screen letters with round parts should now look more equal on screen
-C, D, G, S have now better (shorter) serifs which are more useful for the standart printing size of 12-10pt
- the thinnest line should now have a width of at least 50 (improvements for 12-10pt printing)
- the whole range from U+180 -> U+01BF has been reworked
-small improvements in the IPA-Extensions
-for better acception of the old greek we changed the inverted breve to a tilde which is more widely used..., accents also have received some improvements
-The cyrillic alphabeth has had again great changes due to unsatisfied users. We now have more equal cyrillic serifs, some letters are thinner some wider, spacing has been renewed
-p,i have now horizontal serifs in the cyrillic
-the bold regular is greatly improved. It's now fatter, looks more elegant
- the curive has also improvements:
   J is now better
   many things are corresponding to the improvements in the regular
-improvements in the capital variant

Changes to version 2.3.2regular(|) & italic(/) & capitals (20060920)
Countless improvements...
-especially for the italic, the kyrillic
-polish accents ogonki are now what they should be
-new: hebrew alphabeth
-numbers
-accents brought to one hight (some dotted chars weren't)
-Same hight for capitals (already one point difference can the letter look higher on screen. For print this doesn't play any role...
-superior/inferior numbers improved
-ae looks now different: mor organic. If you don't like this. Theres also an old variant at the end of the font
-ij ligature isn't connected anymore 
-greek accented chars improved (accents should now be optically at the same position), kappa improved
-planet signs added
-Sternkreiszeichen hinzugefügt
-male, female,... improved
.
.
.



Changes to version 2.2.0regular(|) & italic(/) & capitals (20060920)
- Viele, viele Änderungen!
- Volle Unterstützung von OpenType:
	Ligaturen
	Kapitälchen (Small capitals)
	Ziffern -> Tabulare, Proportionale, Mediävale
	Gestrichene Null (slashed Zero)

- Kapitälchen komplett neu entworfen
- Kerning vollständig neu entworfen (nach Klassen)
- neue Ligaturen entworfen: ck, ch, ffb, ffk, fk, fb, fh, ffh, 
- Ampersand überarbeitet, 
- Alternatives Ampersand für Kapitälchen
- E und F überarbeitet
- W und V	"
- M und N (letzteres mit verlängerter Diagonale)
- viele Buchstaben für Type1-Verwendung bereiningt!
- Nummern in Kreisen
 

Changes to version 2.1.8regular(|) & italic(/) & capitals (20060901)
Viele Überarbeitungen:
- Kyrillisches Alphabet weiter verbessert
- E, F, L, T, M, B, D, A, C, Z, P, R Serifen verbessert
- b
- f Bogen etwas länger und flacher
- h verändert
- uni03d0 und theta1 kleiner gemacht
- Dje verändert (nun nativer)
- Macron von 80 auf 90 verdickt
- kleine römische Ziffern verändert
- ij ist nun eine verbundene Ligatur
- viele, viele kleine Bereinigungen


Changes to version 2.1.6 regular(|) & italic(/) & capitals (200608024)
- Zeilenabstände und Font-Metrik verbessert, Durchschuss nun geringer.
- becyrillic hat nun angemessene Abstände
- Untere Serifen sind nun 55 dick und breiter -> Gemeine: 436; Versalien: 526
/ Kursive komplett überarbeitet und auf aktuellen Stand gebracht.
und vieles Kleines mehr...


Changes to version 2.1.5 regular(|) & italic(/) & capitals (200608024)
- Tex-Namen für alle Glyphen
- i-Punkt und . dicker
- Sämtliche Akzente überarbeitet
- Neue, flachere Akzente für Großbuchstaben
- viele Griechische Buchstaben überarbeitet -> dynamischer
- Serifen großteils überarbeitet
- Russischer Akzent jetzt im russischen Stil
- uni041A jetzt wie uni416
- Männlich / Weiblich-Zeichen ist jetzt besser an die Grenzen des Fonts angepasst
- viele weitere Änderungen
- Fette Variante heißt jetzt auch wieder Fett -> Problem bei manchen Betriebsystemen behoben
- Leerzeichen ist nun 512 EM breit, so wie's praktisch Standart ist
!!- Namensproblem bei der Fetten behoben

Changes to version 2.1.3 regular(|) & italic(/) & capitals (20060806)
- 6-Problem gelöst
- Phi-Problem gelöst
- Lowcomma neu -> Pinselstil
- epsilon
- und vieles mehr...
- <> kleinergleich, größergleich, und Verwandte auf gleiche Größe
- +, -, = überarbeitet
- Unendlichzeichen
- h und n verbessert (Bogen)
- Serifen der Gemeinen und der Großbuchstaben verbessert


Changes to version 2.1.2 regular(|) & italic(/) & capitals (20060806)
- Rückführung der griechischen Akzente auf U+002...
- Macron verbessert, U+02C9 nun positiv
- Abstände der griechischen Buchstaben verbessert u.a.: iota LBearing +40
- eta, sigma, rho, tau, psi und weitere griechische Gemeine verbessert -> Pinselstilhaftigkeit erhöht

-----------Fertig-nach Tobias Vorschlägen----------------------------
02C4 (MODIFIER LETTER UP ARRORHEAD), * raised articulation (-> 005E ^
circumflex accent, 2303 ^ up arrowhead). Siehe
http://www.unicode.org/charts/PDF/U02B0.pdf
Wenn ich in die PDF-Datei schaue, sehen 02C2 (<), 02C3 (>), 02C4 (^) und
02C4 (v) größer aus als 02C4 (^) und 02C5 (v). Vielleicht sollte man für
02C2 bis 02C3 einfach ein gedrehtes '<' benutzen?
- Zeichenbreite im Block "SPACE MODIFIER LETTERS (02B0–02FF)"
(http://www.unicode.org/charts/PDF/U02B0.pdf) ist zum Teil verkehrt.
Diese Zeichen sollen alle eine endliche Breite haben (im Gegensatz zu
den COMBINING DIACRITICAL MARKS (0300-036F), die auf/unter einem Zeichen
sitzen sollen). Insbesondere:
- zu Breit: hochgestellte h, w und y
- viel zu schmall: T, _|_ und + (etc.)
- Zeichenbreite/Position im Block "COMBINING DIACRITICAL MARKS (0300-036F)
http://www.unicode.org/charts/PDF/U0300.pdf
Bei den meisten Zeichen stimmt die Breite/Position, bis auf:
- 0306 COMBINING BREVE -> zu weit rechts
- 030B COMBING DOUBLE ACUTE ACCENT -> zu weit rechts.
- 0327 COMBINING CEDILLA -> viel, viel zu weit rechts!
- 0338 COMBINING LONG SOLIDUS OVERLY
-> Ich würde den Strich entweder etwas nach unten verlängern oder nach
etwas nach unten schieben; oder etwas nach links schieben? Jedenfalls
sieht "o" + U0338 (= ca. ø) komisch aus.
- Vielleicht ein klein bißchen zu weit rechts: 0323, 0330, 0331, 0342,
- 0384 GREEK TONOS -> Das Zeichen sollte normal breit sein und kein
"combining" Akzent.
- 03F1 GREEK LUNATE SIGMA SYMBOL = GREEK SMALL LETTER LUNATE SIGMA ->
Das Zeichen sollte anders als Sigma aussehen und zwar ohne den Bogen
unten (ca, wie "c", oft spitz zulaufend).
- 03F4 GREEK CAPITAL THETA SYMBOL -> anders als Theta, durchgehender
Strich in der Mitte
- 03F5 GREEK LUNATE EPSILON SYMBOL = straight epsilon -> Wie der Name
schon sagt ein nicht geschwungenes Epsilon. In der Physik benutzt man
oftmals beide Epsilons das gerade und das geschwungene.
(03F6 GREEK REVERSED LUNATE EPSILON = reversed straight epsilon -> bitte
das gedrehte 03F5 hinzufügen)
- 03F9 GREEK CAPITAL LUNATE SIGMA SYMBOL -> sollte anders als Sigma
aussehen, klassischerweise wie "C" (oft ohne Serifen und spitz zulaufend)

NUMBER FORMS
- 215F "1/ " -> Man sollte wohl die Breite so ändern, daß "1/" +
tiefgestellte Zahl (2080 bis 2089) gut aussehen.
> Durch Kerning gelöst

Fehlten:
- Griechisch-Block: 03DA-03E1 fehlen (Archaische Buchstaben)
http://www.unicode.org/charts/PDF/U0370.pdf
(Wieso die in MES-2 sind, weiß ich allerdings nicht)
* 03DA GREEK LETTER STIGMA
* 03DB GREEK SMALL LETTER STIGMA
* 03DC GREEK LETTER DIGAMMA
* 03DD GREEK SMALL LETTER DIGAMMA
* 03DE GREEK LETTER KOPPA
* 03DF GREEK SMALL LETTER KOPPA
* 03E0 GREEK LETTER SAMPI
* 03E1 GREEK SMALL LETTER SAMPI
MATHEMATICAL OPERATORS, http://www.unicode.org/charts/PDF/U2200.pdf
- 2208 ELEMENT OF, 2209 NOT AN ELEMENT OF und 220A SMALL ELEMENT OF
-> das (Nicht-)Element-von-Symbol finde ich zu groß, es sollte nicht so
groß wie Versalien/Großbuchstaben sein, sondern so groß oder etwas
kleiner als Gemeine/Kleinbuchstaben. Das kleine Element-von-Symbol finde
ich zu klein.

Changes to version 2.1.1 regular(|) & italic(/) & capitals (20060729)
- IPA 02D0 Richtung korrigiert
      0258    ''       ''
- Diacritische Zeichen überarbeitet
- i/j ist nun dotlessi/j + dotaccent
- Tux is again @ uniE00E
- MathSymb 2214 corrected wrong contour
- d', l', t'
- OE neu
- iota überarbeitet
- idieresis, edieresis vom Kyrillischen sind nun Verknpf. vom Latein

Changes to version 2.1.0rc regular(|) & italic(/) & capitals (20060729)
- many small unnamed improvements
- implemented the IPA-extensions
- Small numbers -> superiors and inferiors are now bolder
- fractures are greatly improved and some new are added
- We now have proportional numbers and mediävale as some people asked for them
- Kerning for roman Numbers
- Ligatures should now be seen as a combination of different letters in OpenType-Compliant programs (unfortunately aren't there any compliant programs, or are there?)
- added lots of greek special charakters
- korrected lots of greek special charakters
- some small extensions in mathematical symbols
- eth, Thorn and thorn were completely renewed
- bearing of o,d,eth,p better
- some smaller things
- geometric forms are now better adapted to the charakter sizes
- added alternative variants for German Umlauts
- Tilde improved
- removed dublicate glyphs uniE001-004
/ f, g, h and u are improved
U now also a bold variant of the underlined
* bolded versions for all updated

Changes to version 2.0.9 regular(|) & italic(/) & capitals (20060401)
- small changes, used new converter (fontforge-20060408) -> may result in slightly better contours (who knows...)
- first underlined variant
- Bold variant has now better bearings (were to small) now it is what we want!

Changes to version 2.0.8 regular(|) & italic(/) & capitals (20060401)
- completely new bold version! 
- first bold-italic variant!
=> the family has therefore now finally all faces it classically needs, juhu! ;-)
-further small corrections in all faces
- I created TrueTypeInstructed variants of LinuxLibertine named "LinuxLibertine T" they can be therefore installed paralelly to the unhinted variants. 
Note 1: I recommend all users of OpenOffice 2.0.x on Linux to use the unhinted variants. On my linux-systems the unhinted variant is automaticly hinted and better looking than the hinted! On Windows-Systems -- nevertheless -- the unhinted variant is ugly on screen. Here you should take the instructed/hinted variant. 
Note 2: Hints (in the meaning of TrueTypeInstructions) do not have any effect on printing.

Changes to version 2.0.7 regular(|) & italic(/) & capitals (20060227)
Line spacing was again greately improved (I hope)
| many changes
| in cyrillic
| Accented chars optimized (same height for all accents!)
| new accents
| new special characters
| some existing special chars were improved
/ r is now without serif
/ many small changes
and most of it I just forgot

Changes to version 2.0.4 regular(|) & italic(/) & capitals (20060202)
| many changes
| in cyrillic
| polish
| additions to arabic diacritics
| small corrections in overlapping paths and wrong contour directions
| all corrections should be suitable for the other variants as well

Changes to version 2.0.3 regular(|) & italic(/) & capitals (20060120)
| many small changes
| corrections for Cyrillic glyphs
 Mac-Variant for all fonttypes
/ corrections for Cyrillic glyphs
b corrections for Cyrillic glyphs

Changes to version 2.0.0 regular(|) & italic(/) & capitals (20060120)
/ many many great changes in the italic!
| some new symbols and ornaments
| ligatures optimized
| lots of stuff I forgot to write down...
| Oh, yes, the problem with in stalling all variants paralelly on windows is fixed

Changes to version 1.1.9 regular(|) & italic(/) & capitals (20060120)
| numerous changes!
| e.g. LineGap is smaler now -> similar to Times
| some nice decoratives added (oriental leaf, left and right; fleuron, art deco paragraph end symbol, Tux penguin)
| many new characters for special languages
/ italic updated as well
| bold variant also updated
| many, many other useful additions and bugfixes

Changes to version 1.1.1 regular(|) & italic(/) & capitals (20050908)
| many changes, mamy small changes
| Zahlen /numbers sind jetzt fertig
| Korrekturen an Abständen
| Schmälerung des Leerzeichens
| t + f haben jetzt schmalere Querstriche
| r fetter und rechts weniger Abstand
| y neu und besser
| () Klammern überarbeitet, waren zu hoch
| []{} siehe oben
| µ ist ganz  neu
| € auf Zahlenhöhe
| t verändert
| M nach unten noch breiter werdend
| ø und großes O/ pberarbeitet
| Hoch- und Tiefzahlen /Subscripts
| Brüche neu
| Römische Zahlen auf Zahlenhöhe und ganz neu
| und noch viel viel mehr!

Changes to version 1.0.4-10pt regular(|) & italic(/) & capitals (20050908)
| weitere Arbeiten an den Zahlen besonders 2,3,5,6,0
| Korrekturen an Abständen von K, A, N, V, W, v, w, a, 
| W und X waren nicht so hoch wie andere Versalien
| Verschmälerung des Leerzeichens von 690 auf 600pt
| Gesäuberte Buchstaben: i, a, l, longs, ß, r, f,

Changes to version 1.0.3-10pt regular(|) & italic(/) & capitals (20050908)
| Tabellarische Zahlen fast fertig
| Kern-Paare teils korrigiert

Changes to version 1.0.2-10pt regular(|) & italic(/) & capitals (20050412)
| Tabellarische Zahlen (noch in Arbeit)

Changes to version 1.0.1-10pt regular(|) & italic(/) & capitals (20050323)
| all Capitals are 60pt less high
| ? question mark is slightly thinner and less high now
| uni022C (O mit tilde und Macron): Das Macron (¯) ist etwas zu weit rechts
| uni022D (o mit Tilde und Macron): dito
| uni0210 (R mit ") die " sind zuweit links
  (beim r und u mit " könnten sie noch ein Tick weiter rechts sein)
| uni0307 (dotaccent) der Punkt könnte etwas höher sein
| uni0308 (dieresis) hat positive Breite
| 0309 (combininghook) ggf. zu hoch, Breite nicht null
| 030C (umgedrehtes ^): positive Breite
| 0327 (cedille): positive Breite
| 0323 combining dot below: vielleicht noch ein bißchen nach links?
Anmerkung: Wenn man jetzt noch die Diacritical Marks verschiebt, dann verändern sie sich in allen anderen Akzentuierten Zeichen mit, wo sie durch Referenzen eingebunden sind. Daher habe ich die Verschiebungen ersteinmal nicht vorgenommen.
|Beim kleinen psi [03C8] kann ich mich nicht recht an den schrägen Mittelstrich     
  gewöhnen.)
| 1E48: Das _ beim N sollte wohl etwas länger sein oder etwas nach links
  verschoben werden
| Bei Greek Extended (1F00 bis 1FFF) ist bei 'E und 'H (allein und mit
  verschiedenen weiteren Akzenten) das ' vor der position 0,  und
  überschreibt somit das vorherige Zeichen. Das ist wohl nicht Absicht,
  oder? Analog bei `I (1FDA).
| uni2102: Das C (für die Menge komplexe Zahlen) sollte einen
  Doppelstrich haben, wie deine H,Q,Z,R
| 213D bis 2149: das gilt auch für die Zeichen hier (gamma, Gamma, Pi,
  Sigma, D, d, e und j)
| 2153 bis 215A: Die Brüche 1/3, 2/3, 1/5, 2/5, 3/5, 4/5, 1/6, 5/6
  fehlen; besonders 1/3 und 2/3 wären schön
| 2217 und 2218 sollten witer unten stehen (genaus so hoch wie das Minus
  und der Punkt 2219) da das Multiplikationsoperatoren sind.
| 226A und 226B: Das sind vielgrößer/vielkleiner Zeichen. Ich hätte sie
  aber so nicht erkannt. Nimm einfach << und >> und schiebe sie ein wenig
  ineinander.

Changes to version 1.0.0-10pt regular(|) & italic(/) & capitals (20050323)
| accented chars corrected
| quotes (simple and doubles) are moved down slightly 

Changes to version 0.9.8-10pt regular(|) & italic(/) & capitals (20041208)
| all normal latin chars are now some points wider (we want to have a 10pt font! :->)
| some special chars bolded as well about stroke 10
| all greek letters and special chars in between are also expanded
| cyrilliy A has now a more popular form
| some other small changes

Changes to version 0.9.6-10ptL regular(|) & italic(/) & capitals(20041201)
- bigger serifs for l,i,j,f,a,b,c,d,e,m,n,p,r,h,k,q,s,u,v,w,x,y,z,ß and many more

Changes to version 0.9.5 regular(|) & italic(/) & capitals (#)(20041008)
-r rBearing 49 => 55
-s lBearing 117 => 100
   rBearing 90 => 75
-i lBearing 70 => 80
   rBearing 53 => 65
-t rBearing 
-g ear is now more dominant


Changes to version 0.9.4 regular(|) & italic(/) & capitals (#)(20040802)
- P rBearing -17 => 10
- Px kern pairs redone (to bigger distance)
- w/v-x kern pairs
- many small changes
b] bold typeface iniciated

Changes to version 0.9.3 regular(|) & italic(/) & capitals (#)(20040802)
| z is lighter now
| Eurosign is smarter
| numbers and Bearings edited
| v,w,x,y rBearing 12|22 => 24
| i lBearing 64 => 70
| double-quoteds increased distance about 15pt
| O is lighter
| '`changed accents
| * is now typagraphic (I'm really proud of that one!)
| ? small changes
| fi, ff, ffl, ffi redid ligatures
| changes in accented charackters
| ae changed
| Paragraph sign
| hinting
| þ
| M rBearing 60 => 65
# c
many other small changes


Changes to version 0.9.2 regular(|) & italic(/) & capitals (#)(20040802)
| success in line gap setting:
winAscent=1900
winDescent=500
linegap=0
these are quite useful values. We'll see whether it may be better to reduce winAscent. 

Changes to version 0.9.0 regular(|) & italic(/) & capitals (#)(20040727)
| another try in line gap setting

Changes to version 0.8.8 regular(|) & italic(/) & capitals (#)(20040723)
| improvements and great changes in all numbers
| 1 changed from roman to actual latin
| , (komma) improved slightly
| ) rBearing +
| ? improved slightly
| @ greately improved
| [] bracket improved
| + - at one hight
| ~ improved
| § improved
| breve improved
| c/o and other small things
# Capitals initiated

Changes to version 0.8.7 regular(|) & italic(/) (20040715)
| added uni28F
| did some mathematical symbols
| some other small things


Changes to version 0.8.6 regular(|) & italic(/) (20040715)
LineSpacing is great in other programs than OpenOffice
| t improved slightly
| O improved
| u improved
| 0 improved
| and other small things

Changes to version 0.8.5 regular(|) & italic(/) (20040715)
| another try in correct line-spacing
| m corrected

Changes to version 0.8.4 regular(|) & italic(/) (20040630)
| longs improved
| b improved slightly
| c improved
| O further work
| a greatly improved
| 0 another try


Changes to version 0.8.3 regular(|) & italic(/) (20040630)
| y lBearing -31 => 0
| s again changes
| line gap was still too big
| L rBearing 40 => 60
| u changes in the bow 
| 0 is now rounder
| f edited
| g ear and lower bow corrected
| O changes again

Changes to version 0.8.2 regular(|) & italic(/) (20040630)
| line gap was to huge
| s so new slightly to dark
    worked over
| t worked over
| G edited
| r edited, was to dark
| N rBearing 65 => 70
| O yet another trial


Changes to version 0.8.1 regular(|) & italic(/) (20040630)
| D rBearing 110 => 120
| t rBearing 45 => 60
| H rBearing 55 => 70
| M rBearing 53 => 60
| N rBearing 56 => 65
    diagonal was much to fat, now thinner
| S rBearing 90 => 100
| Z rBearing 35 => 60
| l rBearing 60 => 65
    lBearing 66 => 70
| T some changes (higher top)
| t changes in the bow
| & (ampersand) had great changes
| s great changes, serifs were too complex
| j lBearing -50 => -58
| ! (exclamation mark) shrinked to 99%
| a,e upper bow higher
| a bow
| u upper serifs higher
| O still in work

Changes to version 0.8.0 regular(|) & italic(/) (20040627)
| Set WinAsc/Desc to correct wrong line-spacing in most Applications
| ? (Question Mark) was too big
| e edited
| O edited but I'm not confident yet
| J lBearing -157 => -140

Changes to version 0.7.9 regular(|) & italic(/) (20040604)
| B rBearing  80 => 90
| I rBearing  65 => 72
    lBearing  48 => 60
| t rBearing  32 => 45
| s rBearing 105 => 75 
| e lBearing 183 => 88
| b rBearing  85 => 90
| j lBearing -72 => -50  
    rBearing 153 => 160
| 0 Bearings
| l rBearing 50 => 60
| p rBearing 75 => 80
    bow overworked
| AE overworked
| þ overworked
| o overworked
| oe overworked
| uni434 overworked
| uni310e & f made kursive
| uni2070-209F created
| O edited
| S edited
| t edited

    
Changes to version 0.7.8 regular(|) & italic(/) (20040604)
| b cleaned 
| c cleaned
| d cleaned
| f cleaned
| g cleaned
| j cleaned
| k cleaned and combined
| m cleaned
| n cleaned
| o cleaned
| p cleaned
| q cleaned
| r cleaned
| u cleaned
| w cleaned and combined
| y cleaned and combined
| O, P, Q, R overworked
| W combined
| Y wider
| ß overworked


Changes to version 0.7.7 regular(|) & italic(/) (20040604)
| nothing that I can remember

Changes to version 0.7.5 regular(|) & italic(/) (20040604)
| S rBearing corrected (was double size)
| All capital letters shrinked to 97%
| D overworked
| J
| T's cap is 20pts wider
| U bow changed 
| X
| a is now lsightly smaller


Changes to version 0.7.4 regular(|) & italic(/) (20040531)
| small changes in diacritical marks
| s overworked
| E,F VStem is now 10 pt wider and therefore as other       capitals
| did a new hinting on all basic letters

Changes to version 0.7.3 regular(|) & italic(/) (20040531)
| removed some overlappings in e.g. A, K. W a.s.f
| uni431 overworked
| uni 432 overworked
| overworked cyrillic
| added some strange chars uni1E0...

Changes to version 0.7.1 regular(|) & italic(/) (20040525)
| copied some greek letters from RC van Dalen' Garogier
| t overworked, wider
| J overworked
| overworked omega
| overworked phi
| overworked eta
| overworked numbers
| k small corrections
| worked over accented charakters
| many accented chars and special chars added
| Cyrillic charakters added (that was quite a horror of     work and it's not yet finished... spacing leaves to be    done
| B overworked
| C overworked
| G overworked
| O overworked
| U Overworked
| S Overworked

Changes to version 0.7.0 regular(|) & italic(/) (20040522)
| Omicron ist now reference to O
| uni25cb overworked is now reference to Rcircle and others
| Xi overworked
| chi overworked
| gcedilla changed
| k,n,rcedillas changed
| corrected uni21A
and other fixes 

Changes to version 0.6.9 regular(|) & italic(/) (20040519)
| overworked µ
| added 018E
| added 01B6 - 01B9
| added 0283
| overworked 0292
| i,j dot is now better positioned
| created dotlessj
| overworked all accented charakters
| added Tobias' changes, namely:
    Latin Extended-B
    - Added 022E
    - Added 022F
    - Added 01c0
    - Added 01c1
    - Added 01c2
    - Added 0218
    - Added 0219
    - Added 021A
    - Added 021B
    - Added 021E
    - Added 021F

    IPA Symbols:
    - Flipped 025C (reverted epsilon) vertically
    - Added 0287
    - Added 0292

    Combining diacritical marks
    - Added 0307
    - Added 0326


Changes to version 0.6.8 regular(|) & italic(/) (20040517)
| added Tobias' changes

Changes to version 0.6.7 regular(|) & italic(/) (20040513)
| added Tobias' implementations and further ipa-extensions (8 chars)
| added Vienamese special charakters

Changes to version 0.6.6 regular(|) & italic(/) (20040510)
/ updated to recent development stage
many other small things

Changes to version 0.6.5 regular(|) & italic(/) (20040510)
| "" (quoteds) increased the distance between bow commas
| " (quoteds) and kerning
| singlequotes added
| added some mathemagical symbols
| ~n width corrected
| corrections in special characters
| added roman numbers
| all latin standart charakter-lines are now right turning and therefore should
  most Xor-Errors in special chars due to references be corrected now
| updated tobias' corrections




Changes to version 0.6.4 regular(|) & italic(/) (20040508)
| added Tobias' box and arrow symbols
| added a German quoted at uni201F
| U is now 80pt wider and hopefully wide enough now
| kernpair nt, mt 0 => -30
| A serifs shorter

Changes to version 0.6.3 regular(|) & italic(/) (20040415)
| AE corrected
| k rBearing 0 => 25
| t Bearings
| z rBearing 85 => 72
| b bow
| d rBearing 68 => 62
| h lBearing 64 => 58
| m 16pt wider
| x, Bearings 12 => 22
| v,w Bearings 0 => 12
| o smaller
| ,. bigger 103%
| did uni25cf

Changes to version 0.6.2 regular(|) & italic(/) (20040315)
| d has now a new serif
| t changes
| S rBearing 90 => 95
| E rBearing 90 => 100
| z lBearing 95 => 85
| ä,ö,ü a.s.f have bigger dieresis (points)
| k smal changes in the arm
| x l/rBearing 7=> 12
| T kern pairs
| �overworked
| ff overworked
| fi overworked
| dotlessi overworked
| added U+00A0 nonbraking space
        U+014A & B (eng)
	and further uni-charcters to comply MSE-1
        	(European languages standart)
	florin

Changes to version 0.6.1 regular(|) & italic(/) (20040315)
| ligatures overworked
| k left bearing changed: 70 => 58
    right bearing changed: -20 => 0
| x left bearing changed: 8 => 6
    right bearing changed: -20 => 6
| - (hyphen) is now thinner
| :; are now bigger
|  overworked
| J rBearing increased
| d lBearing -5pt
| B rBearing 90 => 80
| l lBearing 74 => 66
    rBearing 57 => 50
| v,w,y lBearing -14 => 0
        rBearing -14 => 0
| Z stronger Serifs
| t new bow
| u thicker bow
    changes in all serifs
| r's bow is more contrastive
| V,W,Y kernpairs overworked
| new anstrich for: b,d,h,i,j,k,l,m,n,p,r
| t lBearing 73 => 60


Changes to version 0.6.0 regular(|) & italic(/) (20040315)
| b has now a foot
| new serifs for H,W,V,T,I,J,K,L,E,F,M,N,P,R,U,X,A,B,D,G,Y
| new serifs for f,h,i,k,l,m,n,p,q,r,v.w.x.y
| Xe kernpair -20 => -40
| B is a bit (27pt) wider
| G had some changes
| some more little changes

Changes to version 0.5.9 regular(|) & italic(/) (20040315)
| did the common latin legatures
| �still working on the bows
| e is a now 2% less wide as before
    had some changes
| dashes overworked

Changes to version 0.5.8 regular(|) & italic(/) (20040315)
/ b, �changes
/ f overwork in the bow
- some other changes


Changes to version 0.5.7 regular(|) & italic(/) (20040315)
-N is now 66pt wider
-^ {Ascicircum} is now better
- {exclamdown} is now availible
- {currency} has been added
| "-" hyphen is the same as softhyphen. length is now 510pt
-bars have been made
-many, many unlisted changes in so called "special" chars
| a,b,c,d,e,f had small overwork
| g's ear still had another overwork. Whether this will be better
    we'll see...
| �is now wider
| ... {Ellipsis} had an overwork
| j Beraings + 10/15pt

Changes to version 0.5.6 regular(|) & italic(/) (20040303)
- t's bow is now stronger
| l has moved 5pt to the right
- L rBearing 29 => 40
| kern pair {space}T -75 => -100
-  is now up to date
| r has a finer drop
- Y has been combined
- M rBearing 68 => 75
    lBearing 78 => 75
- H rBearing 80 => 75
    lBearing  => 75
- s rBearing 115 => 105
- V is now 30pt wider
- E,F combined
| C edded Extrema
|  points are now centered
| e added extrema
- K is now ca 50pt wider
| a,c,f added extrema
- S thicker serifs
- �has gained a lot, though
    in italic the subpart is still missing
| # is corrected
/ & corrected
/ v,w a bit more slanted

Changes to version 0.5.5 and 0.5.5i (20040303)
- did a "correct direction" on all chars
- did a autoinstr on all chars
/ Quotesingle korrigiert
/ hopefully all chars slanted now
- ascicircum scaled to 75%

Changes to version 0.5.4 (20040303)
- n,m rBearing had to be reset to 60
- ~ (tilde) is now better
- ff is much better now
- fi as well
- � � , � � �better now
- change to v, w

Changes to version -italic-0.5.4 (20040303)
- do nearly all characters as they have to be
  in an italic
- v in style of Janson italic

Changes to version 0.5.3 (20040228)
- b. kern-pair -10 => -50
- @ got better
- n,m became 28pt wider
- W thin stems became thinner (ca. 4pt)
- u change in the bow
- �corrected
- D became wider
- 1,2,3,4,5,6,7 had a major overwork
- C,G wider bows


Changes to version 0.5.2 (20040222)
- b rBearing 90 => 80
- r rBearing 66 => 55
- c,d,e,q have now more similar bows
- p has now a better bow
- a has slight changes
- o has now a thinner band, less dominant
- s had some changes in the curves
- f changes in the bow
- h slight changes
- y is hopefully now what I want it to be
- @ has changed in a good way but the design cannot be
    finished yet
- Tried an italic version

Changes to version 0.5.1 (20040214)
- S ground touching bow had an overwork
- U left stem is now 160pt wide
- u is now wider
- a lBearing 90 => 100
- s rBearing 108 => 115
- z has now round edges
- b change in the bow
- g strong changes in the bows
- t doing a step backwards: lifting bow upper again
- H became 25pt wider
- B,D,K,M,R,T,U got the new serifs
- e hStem moved slightly upwards
- rg kernpair 0 => -25
- X completely reworked, added adequate kern pairs
- c overworked
- Z edge improved
- ? (Questionmark) new designed
- ! (Exklamationmark) new designed
- p,q shortened the Unterl�ge

Changes to version 0.5.0 (20040212)
It showed that most of my critics said that a drop
would be better readably and nicer to look at than
a square in letters like r,c,f,a
result is a major design-shift: we are now doing
charakters like r,f,y,a with drops. I'll try to
conservate old forms for a later splitting in two fonts.
- i still reducing rBearing 70 => 65
- n,m,h enlarging rBearing 50 => 60
- all small: Bearings enlarged by 5pt on each site
- all capitals: Bearings enlarged about 10pt on each site
- r,f overworked the new drop
- J,c have now a drop too
- b changes in the bow
- y greatly overworked
- �desiris corrected
- o had some changes in the round
- P new serifs
- E overworked upper serif
- G overworked upper serif
- g earstem is now slighy bigger



Changes to version 0.4.9 (20040205)
- A longer Serifs
- f #balken hochgeschoben
    balken dnner
- Z rBearing 34 => 40
    now wider
- E,F,L Serifs overworked (more dominant now)
- a thicker in the bow
    rBearing 45 => 38
    two versions as with r
- i reduce rBearing 77 => 70
- h,m,n rBearing 45 => 50
- t changes in the bow
- H hstem changed
- b change in vStem
- q ear changed
- u small change in left part of the bow
- s rBearing 90 => 98
    lBearing 100 => 108
- r rBearing 56 => 60
    great change in the arm, two versions,
    one completely new with drop-end
    old form with edge-end (on )
- d rBearing 86 => 90
- b rBearing 80 => 85
- dieresis, accents a.s.f moved 10pt upwards
- o is now rounder
- M arrow is now slightly round
- y had an overwork
- f change in the bow

Changes to version 0.4.8 (20040202)
- h,m,n rBearing 35 => 45
- v,w RBearing 20 => 27
      lBearing 20 => 24
- u rBearing 70 => 75
    vStem left was 2 pt to wide
    vStem right was 1 pt to wide
- d rBearing 78 => 85
    changes in the bow
    vStem was 1 pt to wide
- H rBearing 64 => 70
- r vStem was 1 pt to wide
- t major overwork
- i reduce rBearing 85 => 78
    lBearing 70 => 75
- e rBearing 77 => 75
- l rBearing 60 => 70
    lBearing 73 => 75
- y search for a better design
- M rBearing 70 => 80
- c changes in the bow
- f has a less dominant bow now
- N has now a spice V-Compound
    new Serifs
- E,F new middle hStem
- a now thicker
    rBearing 48 => 45
- W() kerning reduced to -125
- C rBearing 70 => 80
- all small: vStem wider 142 => 143

Changes to version 0.4.7 (20040124)
- E majority overworked
    new serifs
    thinner hline
    wider
    rBearing 30 => 70
- F same as with E
- I rBearing 38 => 65
    new serifs
    hinted
- M rBearing 40 => 70
- H rBearing 40 => 65
    new serifs
    hinted
- K rBearing 10 => 25
- L new Serifs
    hinted
- S rBearing 70 => 80
- b rBearing 70 => 80
    hinted
- e rBearing 70 => 75
    lBearing 70 => 80
    hinted
    some changes in the charakter
- a is fatter now
    overworked whole char

- O rBearing 100 => 105
- m,n,h rBearing 24 => 35
- s rBearing 87 => 90
- r rBearing 48 => 55
- v,w completely new designed

Changes to version 0.4.6 (20040124)
- [space] is now wider 595 => 690
- [small] rBeraing, lBearing extended about 3pt each
          enlarged to 102%
- a upper bow is has now a lower beginning
    has now a "a-typical" serif
    has a thicker bauch
- u is 12pt wider
- n is 8pt wider
- m is 15pt wider
- b is 11pt wider
- c open end lifted up
- i reduce a bit backwards rBearing 95 => 85
- d wider about 10pt
    bow is higher
- e more dynamic now
    wider
- o overworked
- v big change
- z upper serif had a very small change

Changes to version 0.4.5 (20040114)
- U left arm is thinner now
    changes in the bow
- p bow more dynamic
- d bow now more dynamic
- c slightly thinner now
    more points
- L foot is 30pts wider
- N Trick in the edges has been to strong
    diagonal arm is thinner now
- k lBearing 29 => 22
- po, pe, pd, od, a.s.f kerning 0 => +25
- all small: enlarged Bearings to 105%
- space: enlarged to 595
- a new serifs
- b new serifs
- d new serifs
- h new serifs
- i new serifs (upper and below) to test whether more
    points in serifs makes print-outs and desktop-views better)
    gave more rBearing 66 => 90
    dot is now bigger again (105%)
- j new serifs
- k new serifs
- l new serifs
- m new serifs
    bows are much better now
- n new serifs
    the bow is much better now
- p new serifs
    changes in the bow
- q new serifs
- r new serifs
- u new serifs


Changes to version 0.4.4 (20040108)
- M was a little too extravagant hopefully better now
- c was to huge
    bow was to high
- o rBearing 70 => 65
- P  rBearing 28 => 20
- vo,wo, ve, we kerning -15 => -20
- va,wa kerning -5 => -10
- ow,ov kerning -25 => 20
- ev,ew kerning -25 => 20
- y created kernpairs synonym to v,w
- v-family "v-" kerning 0 => -25
- ro, re kerning -30 => -15
- O slightly thinner
- i hinting now better
- all small: overworked hints

Changes to version 0.4.3 (20031219)
- c some small changes
    lbearing 70 => 75
    rBearing 50 => 45
    changes in the bow
    cedilla some points to the left
- ve, we, wo, vo kerning -30 => -15
- va, wa kerning -15 => -5
- M became more dynamic
    rBearing 60 => 40
- H rBearing 50 => 40
- .,i,j... (dots) were slightly oval
- j rBearing 159 => 140
- k arm raised back again
- V,W rBearing 16 => 10
- Vo, Wo, We, Ve kerning -120 => -140
- Wa, Va kerning -120 => -130
- s upper serif now more vertikal
- b rBearing 72 => 65
    bow had some small changes
- R is a little wider now
    bow is a little better
- g many changes in the bow
    more space between the upper and the down circle
- P constructed a new P from the new R
- y great work but design not yet finished
- u some small changes in first vStem to avoid different thickness in print-outs


Changes to version 0.4.2 (20031217)
Greek Symbols will come:
- Delta added
- Psi added
- Sigmal added
- Phi
- Lambda added
- Omega added
- Xi added
- Gamma added
- te kerning -25 => 0
- t rBearing 65 => 75
- c rBearing 60 => 50
- �rBearing 94 => 70
- �rBearing 59 => 65
-  rBearing 62 => 65
- all round small charakters a,b,c,d,e,g,h,i,j,m,n,o,p,q,r,s
  have grown a bit to get a better text-tape
- g rBearing 60 => 50
- u lBearing 60 => 53

Changes to version 0.4.1 (20031214)
- all rounds to Bearing 75 => 70
  (b, c, d, o, p, q)
- e: rBearing 75 => 65
- letters like i, m,n,r, a.s.f. lBearing:
  65 => 60
- i: 70 => 65
- b: round has been overworked
- Vi, Wi kerning: -70 => -50
- Vo, Ve, Va, Wo, We, Wa  kerning -150 => -120
- t: top is a little lower now
- � major overwork
- S: major overwork
- r: a step back to the r in 0.3.9
- P: Bow is better now
- a: overwork of the bow
- ow, ew, ev, ov kerning => -25
- H, I: rBearing 50
- M rBearing 69 => 60
- yc kerning => -25
- N is wider now
- y: rBearing 24 => 19
- r: rBearing 25 => 28

Changes to version 0.4.0 (20031207)
- a, u rightBearing 59 => 65
- v,w,V,W were to far down -22 => -5
- v,w RBearing 24 => 19
- r thicker bow
- all rounds to Bearing 99 => 75
  (b, c, d, e, o, p, q)
- V,W RBearing 20 => 16
- g rBearing 70 => 60
- t lBearing 86 => 75
- n changes in the bow
- s lBearing 82 => 92
- s rBearing 92 => 80
- tt kerning 40 => 15
- f slightly dominanter bow
- ff -100 => -80
- fb 50 => 80
- t rBearing 80 => 65
- ta kern -10 => 0 (deregister kern pair)
- r rBearing 50 => 25
- g lBearing 79 => 69


Changes to version 0.3.9 (20031113)
- the changes of the last version brought some wrong bearings
  following are corrected now:
  - a,u  right bearing 80 => 65
  - a left 85 => 79
  - d right 80 => 80
  - l rigth 69 => 58
  - tt kernpair_dist -80 => -40
  - s left and rigtht bearing 85 => 90
  - i left and rigtht bearing 69 => 76
  - w, v left and rigtht bearing 22 => 28
  - h left 71 => 68
  - o both 89 => 82
  - t left 96 => 86
  - c left 100=> 90
  - re, ke, ko kern pair [di]: -20 = -30
  - rg kern pair [di]: 0 => -20
  - r. kern pair [di]: 0 => -60
  - H had the same bearing as M but M has longer serifs => H 69=>51

- e seemed to be to domaninant, => shrinked to 98%
- s seemed to small => enlarged to x=102% y=101%
- Q got a connection between tilde and the O
- f bow got a bit longer in right direction
- b change in the bow
- longer serifs for the gemeinen
- N got wider
- h,n got wider
- new cedilla
- ? overworked



Changes to version 0.3.8 (20031102)
- v, w are now wider
- a: grave changes
- i,j: Dot got 10% smaller
- .;:? Dots got 10% smaller
- ? complete newdesigned
- , (Komma) major overwork
- �(German sz) overworked
- t RBearing + 10pt
- u more contras and finer connection
- U now wider and more round bow
- N second trick on the angle of left hstem and diagonal
- K: overworked
- J: better bow
- k: overworked
- b,p, D, B, P: better bow
- h, m, n, u: nicer and more contrative bows
- O, Q better rounds now
- g got new ear
- all letters: RBearing, LBearing + 15%
- e: small changes in the bow

Changes to version 0.3.7 (20031028)
- r seemed to thick (VStem) in small printouts => changes in bow
- h right VStem seemed to thick in small printouts => changes in bow
- u is now what I wanted it to be wider and thinner as well more elegant
- c is now as thinn as the bow of d; small changes in R+LBeering
- g became slightly thinner
- y is slightly better now (less extreme) but still needs a lot of work
- t's RBearing was too small (+10pt)
- k's arm was liftet slightly above the x-height, because it seemed to low
- S became slightly thinner
- T's roof won hight because it looked slightly smaller than other Caps
- p's bow is now like the one of d
- e is now wider
- O slight changes

Changes to version 0.3.6 (20031025)
- Bearings for all Gemeine (small letters) changed!
- g: ear isn't so potent anymore, wider, increased Bearings
- m,n,h overworked the bow
- a overworked the bow


Changes to version 0.3.5 (20031019)
- R: lost fracture from last release removed
- | (pipeline): finer outline
- `' (high commas) inserted
- slashes added
- elipses added
- promille added
- <> (dynamic ones) added
- % new design (efont)
- ! had changes
- & (ampersand) from efont
- '," (inch, geometr. minute/second) are now undynamic
- / smaller
- numbers: new medi�al (OSF) numbers from efont
- @ has a new a in the middle
- v, w: finer, make paralell, anti-block-trick
- V, W: anti-block-trick, make parallel
- N,Z,M,K,Y,k,x,y,z,X: make paralell
- (pound) added
- some more little things

Changes to version 0.3.4 (20031019)
- C has greately changed and is now what I wanted it to be.
- S: slight changes in the upper bow and serif
- h has now more point in its bow
- H, A: HLine is now 80pt thick
- G has greately changed and is now what I wanted it to be
- b, d, h, i, j, k, l, m, n, p, r: the upper serif has now a
  certain slight concave bow
- same with a,d,u, just with the under serif
- R got more points
- e: slight changes and new points
- U: more points and wider
- O: slight changes
- Q: RBearing +20pt
- p: slight changes and new points
- Accents are smaller now
- j: changes in the bow
- {},(),[]: new brackets anch important changes in all
- some Bearings slightly corrected

Take this version as a first milestone!
Changes to version 0.3.3 (20031019)
- O, Q became slightly thinner
- P small but important corrections
- d, b better round like already done with p & q
- x had major changes
- s had major changes
- o very light changes
- a, d, u have a walking foot now
- J: slight changes in the bow
- D: major changes in the bow
- B: major changes, grew wider
- c: light changes
- g: light changes
- e: light changes
- stem width of small letters are now 140 instead of 145
- p, q: weniger Unterl�ge
- S: has now more RBearing
- the lBearing and RBearing of the Gemeinen (small letters) have been extended
  about 5pt.
- �(sz, german) had a redesign but still needs a lot of work

Changes to version 0.3.2 (20031018)
- R has been overworked and is now similar to P again
- P has been slightly overworked
- M is now wider
- B had major changes
- T's and Z new serifs had to be reduced a bit again
- z now got the same serif as its father
- r got again more expressiv
- s had an overwork
- x: its thicker leg is now slightly thinner
- l, i, d, b, h, m, n, p, q, r, t, u, k, j, f is now 145pt thick
- H, I, J, E, F is now 160pt thick
- E, F are now wider
- e: slight changes
- C: better but still not like I want it to have
- v, w: feet now beyond groundline
- c is much better now
- o is much better now
- O is much better now
- B is much better now
- S: the upper part is wider now
- q is completely new dedigned. May this be a good example for b, p, d?
- dieresis (german umlaut points) have more space in between themselves
- Q looks wonderful now (at least much much better)
- () are now much better (taken from GPLed efont)
- longs (the old f-like s) has now its real place
- X got the last missing serif of the whole font (!) ;-)

Changes to version 0.3.1 (20031016)
- r's ear has now a lower connection to vStem and looks hopefully better in small points,
  rightBearing increased
- g is now slightly smaller and generally smarter
- dieresis (namely the two dots that belong onto the german umlauts are smaller (85%)
- K: its leg is now doing a step forward
- J has lost its old serif at the ground-line and has other majot changes
- H hStem is not as thick anymore
- A same like with H above
- G's table is now longer
- E,F: hStems are now 85points thick and not 104pt
- D became fat
- T,Z: Serif now grow into the air, hStems are now ca. 85pt
- U has grown wider
- u's left vStem got thinner, rBearing +10
- i: lBearing +10, rightBearing +10
- c: lBearing +
- I: rBearing +
- L: rBearing + and hStem is now ca. 85pt
- dot: lBearing +, rBearing +
- C: it's getting better but much work remains
- a: more contrast in changing line-thickness
- w looks now and again like a real renaissance w
- V gained a serif at his left arm
- W is newly generated from two Vs and now looks again like a real renaissance W
- new kearn pairs for V and W
- y has been newly formed out of v

Changes to version 0.3.0 (20031011)
- big change: All letters became bigger to reduce the lost space above the latters that no
  PC-Font seems to have normally...
- A has now more right bearing
- r got back the 15 points rightBearing it lost while extending its ear in 0.2.8
- G rightBearing is now 55pt
- e has now more left and right bearing
- v same as e above
- o same as e above
- n has now slightly more lBearing
- d, b now got slightly more bearing at the round side
- u got more rBearing (same as "a")
- ko kern pair was to close together, fixed
- k has now more rBearing (not negative anymore)
- ka is a new kern pair
- a & u more rBearing (+6pt)
- B, M, H, O, D got more rBearing
- "V-" is obviously a kern pair and same with T,W,X,...
- AC, AG, AO, AQ, AT, AU, AV, AW, AY are new kern pairs
- small kerning for round letters and "." e.g.: "o.", "p.",...
- c: major changes
- C: major changes


Changes to version 0.2.9 (20031008)
- Added some arrows you need for chemistry-terms (->, <-, <-->, v, �

Changes to version 0.2.8 (20031005)
- did a little work in kerning of pairs like Vx, Wx, fx, ff
- E,F,k: vertical stem is now 2 points thinner
- n overworked. Major changes: left vertikal stem has now lost sharpness;
    bow from n is now just a connect to the left HStem an no part anymore;
    new character balance on the right
- same like above with h, m, r
- r got mor extrem in bow and ear
- b, d, p similar procedure like above with m, n, r
- i, j, l, k, b, d, h the overlength is now slightly unsharpened
- G is now mor round und more proud

Changes to version 0.2.7 (20031002)
- g got a slightly smaller ear
- fi-ligature has better right bearing (the one of "i" of course)
- n has now less right bearing (20 -> 5)
- m has now more right bearing (-8 -> 5)
- h has now less right bearing (12 -> 5)
- c got a bigger left bearing (54 -> 60)
- b's stem seam to be to thick. I shrinked its width from 42 -> 37
- r: increased right bearing (25 -> 35)
- V got a bigger left vertical
- N got a thinner vertical
- 1 (one) is littler thinner now and again
- . (dot) has now greater right bearing (55 -> 65)
- , (comma) has now greater right bearing (55 -> 65)
- t got bigger right bearing (45 -> 50)
- z: for aesthetical reasons the above horizontal stem seems to must be thinner than the one at the
base line, so 9points off

Changes to version 0.2.6 (20030930)
- I overworked all serifs! The results are not visible in printing small sizes in TrueType
but since AutoHinting recognizes the serifs now we should have better printing results at
good postscript printers in PostScript-Fonts.
- Kerning table has been greately expanded even for pairs like ke, ka... Very sad that nearly all
Linux-TrueType programs don't care about the kerning table at all! This must change in future!
- g has been overworked and seems to become pretty at least. Especially the ear had a good change.
- C had major changes and satisfies me now.
- all numbers had some changes and got for the first all the same bearings left and right
- fi-ligature had complete redo, looks better but doesn't satisfies me, yet
- r got a little thicker nipple at the right end of its arm that leads to better printing an
recognition results at small points
- t has a slighly thinner round in its neck now