<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE ldml SYSTEM "../../common/dtd/ldml.dtd">
<!-- Copyright © 1991-2013 Unicode, Inc.
CLDR data files are interpreted according to the LDML specification (http://unicode.org/reports/tr35/)
For terms of use, see http://www.unicode.org/copyright.html
-->
<ldml>
	<identity>
		<version number="$Revision: 9287 $"/>
		<generation date="$Date: 2013-08-28 21:32:04 -0500 (Wed, 28 Aug 2013) $"/>
		<language type="mgo"/>
	</identity>
	<localeDisplayNames>
		<languages>
			<language type="mgo">metaʼ</language>
			<language type="und">ngam tisɔʼ</language>
		</languages>
		<scripts>
			<script type="Latn">ngam ŋwaʼri</script>
			<script type="Zxxx">ngam choʼ</script>
			<script type="Zzzz">abo ŋwaʼri tisɔʼ</script>
		</scripts>
		<territories>
			<territory type="CM">Kamalun</territory>
			<territory type="ZZ">aba aben tisɔ̀</territory>
		</territories>
		<types>
			<type type="gregorian" key="calendar">ngàb mə̀kala</type>
			<type type="latn" key="numbers">inu</type>
		</types>
		<codePatterns>
			<codePattern type="language">{0}</codePattern>
			<codePattern type="script">{0}</codePattern>
			<codePattern type="territory">{0}</codePattern>
		</codePatterns>
	</localeDisplayNames>
	<characters>
		<exemplarCharacters>[a à b {ch} d e è ə {ə\u0300} f g {gh} i ì j k m n ŋ o ò ɔ {ɔ\u0300} p r s t u ù w y z ʼ]</exemplarCharacters>
		<exemplarCharacters type="auxiliary">[c h l q v x]</exemplarCharacters>
		<exemplarCharacters type="index">[A B {CH} D E Ə F G {GH} I J K M N Ŋ O Ɔ P R S T U W Y Z ʼ]</exemplarCharacters>
		<exemplarCharacters type="punctuation">[, ; \: ! ? . ' ‘ ’ &quot; “ ”]</exemplarCharacters>
		<ellipsis type="final">{0}…</ellipsis>
		<ellipsis type="initial">…{0}</ellipsis>
		<ellipsis type="medial">{0}…{1}</ellipsis>
		<moreInformation>?</moreInformation>
	</characters>
	<delimiters>
		<quotationStart>“</quotationStart>
		<quotationEnd>”</quotationEnd>
		<alternateQuotationStart>‘</alternateQuotationStart>
		<alternateQuotationEnd>’</alternateQuotationEnd>
	</delimiters>
	<dates>
		<calendars>
			<calendar type="generic">
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>EEEE, G y MMMM dd</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>G y MMMM d</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>G y MMM d</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>GGGGG y-MM-dd</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<dateTimeFormats>
					<dateTimeFormatLength type="full">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="long">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="medium">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="short">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<availableFormats>
						<dateFormatItem id="d">d</dateFormatItem>
					</availableFormats>
					<intervalFormats>
						<intervalFormatFallback>{0} – {1}</intervalFormatFallback>
					</intervalFormats>
				</dateTimeFormats>
			</calendar>
			<calendar type="gregorian">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<month type="1">mbegtug</month>
							<month type="2">imeg àbùbì</month>
							<month type="3">imeg mbəŋchubi</month>
							<month type="4">iməg ngwə̀t</month>
							<month type="5">iməg fog</month>
							<month type="6">iməg ichiibɔd</month>
							<month type="7">iməg àdùmbə̀ŋ</month>
							<month type="8">iməg ichika</month>
							<month type="9">iməg kud</month>
							<month type="10">iməg tèsiʼe</month>
							<month type="11">iməg zò</month>
							<month type="12">iməg krizmed</month>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">M1</month>
							<month type="2">A2</month>
							<month type="3">M3</month>
							<month type="4">N4</month>
							<month type="5">F5</month>
							<month type="6">I6</month>
							<month type="7">A7</month>
							<month type="8">I8</month>
							<month type="9">K9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">iməg mbegtug</month>
							<month type="2">imeg àbùbì</month>
							<month type="3">imeg mbəŋchubi</month>
							<month type="4">iməg ngwə̀t</month>
							<month type="5">iməg fog</month>
							<month type="6">iməg ichiibɔd</month>
							<month type="7">iməg àdùmbə̀ŋ</month>
							<month type="8">iməg ichika</month>
							<month type="9">iməg kud</month>
							<month type="10">iməg tèsiʼe</month>
							<month type="11">iməg zò</month>
							<month type="12">iməg krizmed</month>
						</monthWidth>
					</monthContext>
					<monthContext type="stand-alone">
						<monthWidth type="abbreviated">
							<month type="1">mbegtug</month>
							<month type="2">imeg àbùbì</month>
							<month type="3">imeg mbəŋchubi</month>
							<month type="4">iməg ngwə̀t</month>
							<month type="5">iməg fog</month>
							<month type="6">iməg ichiibɔd</month>
							<month type="7">iməg àdùmbə̀ŋ</month>
							<month type="8">iməg ichika</month>
							<month type="9">iməg kud</month>
							<month type="10">iməg tèsiʼe</month>
							<month type="11">iməg zò</month>
							<month type="12">iməg krizmed</month>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">M1</month>
							<month type="2">A2</month>
							<month type="3">M3</month>
							<month type="4">N4</month>
							<month type="5">F5</month>
							<month type="6">I6</month>
							<month type="7">A7</month>
							<month type="8">I8</month>
							<month type="9">K9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">iməg mbegtug</month>
							<month type="2">imeg àbùbì</month>
							<month type="3">imeg mbəŋchubi</month>
							<month type="4">iməg ngwə̀t</month>
							<month type="5">iməg fog</month>
							<month type="6">iməg ichiibɔd</month>
							<month type="7">iməg àdùmbə̀ŋ</month>
							<month type="8">iməg ichika</month>
							<month type="9">iməg kud</month>
							<month type="10">iməg tèsiʼe</month>
							<month type="11">iməg zò</month>
							<month type="12">iməg krizmed</month>
						</monthWidth>
					</monthContext>
				</months>
				<days>
					<dayContext type="format">
						<dayWidth type="abbreviated">
							<day type="sun">Aneg 1</day>
							<day type="mon">Aneg 2</day>
							<day type="tue">Aneg 3</day>
							<day type="wed">Aneg 4</day>
							<day type="thu">Aneg 5</day>
							<day type="fri">Aneg 6</day>
							<day type="sat">Aneg 7</day>
						</dayWidth>
						<dayWidth type="narrow">
							<day type="sun">A1</day>
							<day type="mon">A2</day>
							<day type="tue">A3</day>
							<day type="wed">A4</day>
							<day type="thu">A5</day>
							<day type="fri">A6</day>
							<day type="sat">A7</day>
						</dayWidth>
						<dayWidth type="short">
							<day type="sun">1</day>
							<day type="mon">2</day>
							<day type="tue">3</day>
							<day type="wed">4</day>
							<day type="thu">5</day>
							<day type="fri">6</day>
							<day type="sat">7</day>
						</dayWidth>
						<dayWidth type="wide">
							<day type="sun">Aneg 1</day>
							<day type="mon">Aneg 2</day>
							<day type="tue">Aneg 3</day>
							<day type="wed">Aneg 4</day>
							<day type="thu">Aneg 5</day>
							<day type="fri">Aneg 6</day>
							<day type="sat">Aneg 7</day>
						</dayWidth>
					</dayContext>
					<dayContext type="stand-alone">
						<dayWidth type="abbreviated">
							<day type="sun">Aneg 1</day>
							<day type="mon">Aneg 2</day>
							<day type="tue">Aneg 3</day>
							<day type="wed">Aneg 4</day>
							<day type="thu">Aneg 5</day>
							<day type="fri">Aneg 6</day>
							<day type="sat">Aneg 7</day>
						</dayWidth>
						<dayWidth type="narrow">
							<day type="sun">A1</day>
							<day type="mon">A2</day>
							<day type="tue">A3</day>
							<day type="wed">A4</day>
							<day type="thu">A5</day>
							<day type="fri">A6</day>
							<day type="sat">A7</day>
						</dayWidth>
						<dayWidth type="short">
							<day type="sun">1</day>
							<day type="mon">2</day>
							<day type="tue">3</day>
							<day type="wed">4</day>
							<day type="thu">5</day>
							<day type="fri">6</day>
							<day type="sat">7</day>
						</dayWidth>
						<dayWidth type="wide">
							<day type="sun">Aneg 1</day>
							<day type="mon">Aneg 2</day>
							<day type="tue">Aneg 3</day>
							<day type="wed">Aneg 4</day>
							<day type="thu">Aneg 5</day>
							<day type="fri">Aneg 6</day>
							<day type="sat">Aneg 7</day>
						</dayWidth>
					</dayContext>
				</days>
				<dayPeriods>
					<dayPeriodContext type="format">
						<dayPeriodWidth type="wide">
							<dayPeriod type="am">AM</dayPeriod>
							<dayPeriod type="pm">PM</dayPeriod>
						</dayPeriodWidth>
					</dayPeriodContext>
				</dayPeriods>
				<eras>
					<eraAbbr>
						<era type="0">BCE</era>
						<era type="1">CE</era>
					</eraAbbr>
				</eras>
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>EEEE, y MMMM dd</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>y MMMM d</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>y MMM d</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>y-MM-dd</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<timeFormats>
					<timeFormatLength type="full">
						<timeFormat>
							<pattern>HH:mm:ss zzzz</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="long">
						<timeFormat>
							<pattern>HH:mm:ss z</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="medium">
						<timeFormat>
							<pattern>HH:mm:ss</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="short">
						<timeFormat>
							<pattern>HH:mm</pattern>
						</timeFormat>
					</timeFormatLength>
				</timeFormats>
				<dateTimeFormats>
					<dateTimeFormatLength type="full">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="long">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="medium">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="short">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<availableFormats>
						<dateFormatItem id="d">d</dateFormatItem>
					</availableFormats>
					<intervalFormats>
						<intervalFormatFallback>{0} – {1}</intervalFormatFallback>
					</intervalFormats>
				</dateTimeFormats>
			</calendar>
		</calendars>
		<fields>
			<field type="era">
				<displayName>Era</displayName>
			</field>
			<field type="year">
				<displayName>fituʼ</displayName>
			</field>
			<field type="month">
				<displayName>iməg</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">+{0} m</relativeTimePattern>
					<relativeTimePattern count="other">+{0} m</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">-{0} m</relativeTimePattern>
					<relativeTimePattern count="other">-{0} m</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="week">
				<displayName>nkap</displayName>
			</field>
			<field type="day">
				<displayName>anəg</displayName>
				<relative type="-1">ikwiri</relative>
				<relative type="0">tèchɔ̀ŋ</relative>
				<relative type="1">isu</relative>
				<relative type="2">isu ywi</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">+{0} d</relativeTimePattern>
					<relativeTimePattern count="other">+{0} d</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">-{0} d</relativeTimePattern>
					<relativeTimePattern count="other">-{0} d</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="weekday">
				<displayName>anəg agu nkap</displayName>
			</field>
			<field type="dayperiod">
				<displayName>Dayperiod</displayName>
			</field>
			<field type="hour">
				<displayName>Hour</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">+{0} h</relativeTimePattern>
					<relativeTimePattern count="other">+{0} h</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">-{0} h</relativeTimePattern>
					<relativeTimePattern count="other">-{0} h</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="minute">
				<displayName>Minute</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">+{0} min</relativeTimePattern>
					<relativeTimePattern count="other">+{0} min</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">-{0} min</relativeTimePattern>
					<relativeTimePattern count="other">-{0} min</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="second">
				<displayName>Second</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">+{0} s</relativeTimePattern>
					<relativeTimePattern count="other">+{0} s</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">-{0} s</relativeTimePattern>
					<relativeTimePattern count="other">-{0} s</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="zone">
				<displayName>Zone</displayName>
			</field>
		</fields>
		<timeZoneNames>
			<hourFormat>+HH:mm;-HH:mm</hourFormat>
			<gmtFormat>GMT{0}</gmtFormat>
			<gmtZeroFormat>GMT</gmtZeroFormat>
			<regionFormat>{0}</regionFormat>
			<fallbackFormat>{1} ({0})</fallbackFormat>
		</timeZoneNames>
	</dates>
	<numbers>
		<defaultNumberingSystem>latn</defaultNumberingSystem>
		<otherNumberingSystems>
			<native>latn</native>
		</otherNumberingSystems>
		<symbols numberSystem="latn">
			<decimal>.</decimal>
			<group>,</group>
			<list>;</list>
			<percentSign>%</percentSign>
			<plusSign>+</plusSign>
			<minusSign>-</minusSign>
			<exponential>E</exponential>
			<perMille>‰</perMille>
			<infinity>∞</infinity>
			<nan>NaN</nan>
		</symbols>
		<decimalFormats numberSystem="latn">
			<decimalFormatLength>
				<decimalFormat>
					<pattern>#,##0.###</pattern>
				</decimalFormat>
			</decimalFormatLength>
		</decimalFormats>
		<scientificFormats numberSystem="latn">
			<scientificFormatLength>
				<scientificFormat>
					<pattern>#E0</pattern>
				</scientificFormat>
			</scientificFormatLength>
		</scientificFormats>
		<percentFormats numberSystem="latn">
			<percentFormatLength>
				<percentFormat>
					<pattern>#,##0%</pattern>
				</percentFormat>
			</percentFormatLength>
		</percentFormats>
		<currencyFormats numberSystem="latn">
			<currencyFormatLength>
				<currencyFormat type="standard">
					<pattern>¤ #,##0.00</pattern>
				</currencyFormat>
			</currencyFormatLength>
			<unitPattern count="one">{0} {1}</unitPattern>
			<unitPattern count="other">{0} {1}</unitPattern>
		</currencyFormats>
		<currencies>
			<currency type="XAF">
				<displayName>shirè</displayName>
				<symbol>FCFA</symbol>
			</currency>
			<currency type="XXX">
				<displayName>iku ikap mɔʼɔ</displayName>
			</currency>
		</currencies>
	</numbers>
	<units>
		<unitLength type="long">
			<unit type="duration-day">
				<unitPattern count="one">{0} d</unitPattern>
				<unitPattern count="other">{0} d</unitPattern>
			</unit>
			<unit type="duration-hour">
				<unitPattern count="one">{0} h</unitPattern>
				<unitPattern count="other">{0} h</unitPattern>
			</unit>
			<unit type="duration-minute">
				<unitPattern count="one">{0} min</unitPattern>
				<unitPattern count="other">{0} min</unitPattern>
			</unit>
			<unit type="duration-month">
				<unitPattern count="one">{0} m</unitPattern>
				<unitPattern count="other">{0} m</unitPattern>
			</unit>
			<unit type="duration-second">
				<unitPattern count="one">{0} s</unitPattern>
				<unitPattern count="other">{0} s</unitPattern>
			</unit>
		</unitLength>
	</units>
	<posix>
		<messages>
			<yesstr>èè</yesstr>
			<nostr>ideg.</nostr>
		</messages>
	</posix>
</ldml>

