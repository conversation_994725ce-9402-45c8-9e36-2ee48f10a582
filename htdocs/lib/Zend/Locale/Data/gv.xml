<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE ldml SYSTEM "../../common/dtd/ldml.dtd">
<!-- Copyright © 1991-2013 Unicode, Inc.
CLDR data files are interpreted according to the LDML specification (http://unicode.org/reports/tr35/)
For terms of use, see http://www.unicode.org/copyright.html
-->
<ldml>
	<identity>
		<version number="$Revision: 9287 $"/>
		<generation date="$Date: 2013-08-28 21:32:04 -0500 (Wed, 28 Aug 2013) $"/>
		<language type="gv"/>
	</identity>
	<localeDisplayNames>
		<languages>
			<language type="gv">Gaelg</language>
		</languages>
		<territories>
			<territory type="GB">Rywvaneth Unys</territory>
			<territory type="IM">Ella<PERSON></territory>
		</territories>
	</localeDisplayNames>
	<characters>
		<exemplarCharacters>[a b c ç d e f g h i j k l m n o p q r s t u v w x y z]</exemplarCharacters>
		<exemplarCharacters type="index" draft="unconfirmed">[A B C D E F G H I J K L M N O P Q R S T U V W X Y Z]</exemplarCharacters>
	</characters>
	<dates>
		<calendars>
			<calendar type="generic">
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern draft="unconfirmed">EEEE dd MMMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern draft="unconfirmed">dd MMMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern draft="unconfirmed">MMM dd, y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern draft="unconfirmed">dd/MM/yy GGGGG</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
			</calendar>
			<calendar type="gregorian">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<month type="1">J-guer</month>
							<month type="2">T-arree</month>
							<month type="3">Mayrnt</month>
							<month type="4">Avrril</month>
							<month type="5">Boaldyn</month>
							<month type="6">M-souree</month>
							<month type="7">J-souree</month>
							<month type="8">Luanistyn</month>
							<month type="9">M-fouyir</month>
							<month type="10">J-fouyir</month>
							<month type="11">M.Houney</month>
							<month type="12">M.Nollick</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Jerrey-geuree</month>
							<month type="2">Toshiaght-arree</month>
							<month type="3">Mayrnt</month>
							<month type="4">Averil</month>
							<month type="5">Boaldyn</month>
							<month type="6">Mean-souree</month>
							<month type="7">Jerrey-souree</month>
							<month type="8">Luanistyn</month>
							<month type="9">Mean-fouyir</month>
							<month type="10">Jerrey-fouyir</month>
							<month type="11">Mee Houney</month>
							<month type="12">Mee ny Nollick</month>
						</monthWidth>
					</monthContext>
				</months>
				<days>
					<dayContext type="format">
						<dayWidth type="abbreviated">
							<day type="sun">Jed</day>
							<day type="mon">Jel</day>
							<day type="tue">Jem</day>
							<day type="wed">Jerc</day>
							<day type="thu">Jerd</day>
							<day type="fri">Jeh</day>
							<day type="sat">Jes</day>
						</dayWidth>
						<dayWidth type="wide">
							<day type="sun">Jedoonee</day>
							<day type="mon">Jelhein</day>
							<day type="tue">Jemayrt</day>
							<day type="wed">Jercean</day>
							<day type="thu">Jerdein</day>
							<day type="fri">Jeheiney</day>
							<day type="sat">Jesarn</day>
						</dayWidth>
					</dayContext>
				</days>
				<dayPeriods>
					<dayPeriodContext type="format">
						<dayPeriodWidth type="wide">
							<dayPeriod type="am">a.m.</dayPeriod>
							<dayPeriod type="pm">p.m.</dayPeriod>
						</dayPeriodWidth>
					</dayPeriodContext>
				</dayPeriods>
				<eras>
					<eraAbbr>
						<era type="0">RC</era>
						<era type="1">AD</era>
					</eraAbbr>
				</eras>
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern draft="unconfirmed">EEEE dd MMMM y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern draft="unconfirmed">dd MMMM y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern draft="unconfirmed">MMM dd, y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern draft="unconfirmed">dd/MM/yy</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<timeFormats>
					<timeFormatLength type="full">
						<timeFormat>
							<pattern draft="unconfirmed">HH:mm:ss zzzz</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="long">
						<timeFormat>
							<pattern draft="unconfirmed">HH:mm:ss z</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="medium">
						<timeFormat>
							<pattern draft="unconfirmed">HH:mm:ss</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="short">
						<timeFormat>
							<pattern draft="unconfirmed">HH:mm</pattern>
						</timeFormat>
					</timeFormatLength>
				</timeFormats>
			</calendar>
		</calendars>
		<timeZoneNames>
			<metazone type="Europe_Central">
				<short>
					<generic draft="unconfirmed">CET</generic>
					<standard draft="unconfirmed">CET</standard>
					<daylight draft="unconfirmed">CEST</daylight>
				</short>
			</metazone>
			<metazone type="Europe_Eastern">
				<short>
					<generic draft="unconfirmed">EET</generic>
					<standard draft="unconfirmed">EET</standard>
					<daylight draft="unconfirmed">EEST</daylight>
				</short>
			</metazone>
			<metazone type="Europe_Western">
				<short>
					<generic draft="unconfirmed">WET</generic>
					<standard draft="unconfirmed">WET</standard>
					<daylight draft="unconfirmed">WEST</daylight>
				</short>
			</metazone>
			<metazone type="GMT">
				<short>
					<standard draft="unconfirmed">GMT</standard>
				</short>
			</metazone>
		</timeZoneNames>
	</dates>
	<numbers>
		<currencyFormats numberSystem="latn">
			<currencyFormatLength>
				<currencyFormat type="standard">
					<pattern>¤#,##0.00</pattern>
				</currencyFormat>
			</currencyFormatLength>
		</currencyFormats>
	</numbers>
</ldml>

