<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE ldml SYSTEM "../../common/dtd/ldml.dtd">
<!-- Copyright © 1991-2013 Unicode, Inc.
CLDR data files are interpreted according to the LDML specification (http://unicode.org/reports/tr35/)
For terms of use, see http://www.unicode.org/copyright.html
-->
<ldml>
	<identity>
		<version number="$Revision: 9852 $"/>
		<generation date="$Date: 2014-02-28 23:57:43 -0600 (Fri, 28 Feb 2014) $"/>
		<language type="id"/>
	</identity>
	<localeDisplayNames>
		<localeDisplayPattern>
			<localePattern>{0} ({1})</localePattern>
			<localeSeparator>{0}, {1}</localeSeparator>
			<localeKeyTypePattern>{0}: {1}</localeKeyTypePattern>
		</localeDisplayPattern>
		<languages>
			<language type="aa">Afar</language>
			<language type="ab">Abkhaz</language>
			<language type="ace">Aceh</language>
			<language type="ach">Acoli</language>
			<language type="ada">Adangme</language>
			<language type="ady">Adygei</language>
			<language type="ae">Avesta</language>
			<language type="af">Afrikaans</language>
			<language type="afh">Afrihili</language>
			<language type="agq">Aghem</language>
			<language type="ain">Ainu</language>
			<language type="ak">Akan</language>
			<language type="akk">Akkadia</language>
			<language type="ale">Aleut</language>
			<language type="alt">Altai Selatan</language>
			<language type="am">Amharik</language>
			<language type="an">Aragon</language>
			<language type="ang">Inggris Kuno</language>
			<language type="anp">Angika</language>
			<language type="ar">Arab</language>
			<language type="ar_001">Arab Standar Modern</language>
			<language type="arc">Aram</language>
			<language type="arn">Araukan</language>
			<language type="arp">Arapaho</language>
			<language type="arw">Arawak</language>
			<language type="as">Assam</language>
			<language type="asa">Asu</language>
			<language type="ast">Astur</language>
			<language type="av">Avar</language>
			<language type="awa">Awadhi</language>
			<language type="ay">Aymara</language>
			<language type="az">Azerbaijan</language>
			<language type="az" alt="short">Azeri</language>
			<language type="ba">Bashkir</language>
			<language type="bal">Baluchi</language>
			<language type="ban">Bali</language>
			<language type="bas">Basa</language>
			<language type="bax">Bamun</language>
			<language type="bbj">Ghomala</language>
			<language type="be">Belarusia</language>
			<language type="bej">Beja</language>
			<language type="bem">Bemba</language>
			<language type="bez">Bena</language>
			<language type="bfd">Bafut</language>
			<language type="bg">Bulgar</language>
			<language type="bho">Bhojpuri</language>
			<language type="bi">Bislama</language>
			<language type="bik">Bikol</language>
			<language type="bin">Bini</language>
			<language type="bkm">Kom</language>
			<language type="bla">Siksika</language>
			<language type="bm">Bambara</language>
			<language type="bn">Bengali</language>
			<language type="bo">Tibet</language>
			<language type="br">Breton</language>
			<language type="bra">Braj</language>
			<language type="brx">Bodo</language>
			<language type="bs">Bosnia</language>
			<language type="bss">Akoose</language>
			<language type="bua">Buriat</language>
			<language type="bug">Bugis</language>
			<language type="bum">Bulu</language>
			<language type="byn">Blin</language>
			<language type="byv">Medumba</language>
			<language type="ca">Katalan</language>
			<language type="cad">Kado</language>
			<language type="car">Karib</language>
			<language type="cay">Cayuga</language>
			<language type="cch">Atsam</language>
			<language type="ce">Chechen</language>
			<language type="ceb">Sebuano</language>
			<language type="cgg">Kiga</language>
			<language type="ch">Chamorro</language>
			<language type="chb">Chibcha</language>
			<language type="chg">Chagatai</language>
			<language type="chk">Chuuke</language>
			<language type="chm">Mari</language>
			<language type="chn">Jargon Chinook</language>
			<language type="cho">Koktaw</language>
			<language type="chp">Chipewyan</language>
			<language type="chr">Cherokee</language>
			<language type="chy">Cheyenne</language>
			<language type="ckb">Kurdi Sorani</language>
			<language type="co">Korsika</language>
			<language type="cop">Koptik</language>
			<language type="cr">Kree</language>
			<language type="crh">Tatar Krimea</language>
			<language type="cs">Cheska</language>
			<language type="csb">Kashubia</language>
			<language type="cu">Bahasa Gereja Slavonia</language>
			<language type="cv">Chuvash</language>
			<language type="cy">Welsh</language>
			<language type="da">Dansk</language>
			<language type="dak">Dakota</language>
			<language type="dar">Dargwa</language>
			<language type="dav">Taita</language>
			<language type="de">Jerman</language>
			<language type="de_AT">Jerman Austria</language>
			<language type="de_CH">Jerman Tinggi Swiss</language>
			<language type="del">Delaware</language>
			<language type="den">Slave</language>
			<language type="dgr">Dogrib</language>
			<language type="din">Dinka</language>
			<language type="dje">Zarma</language>
			<language type="doi">Dogri</language>
			<language type="dsb">Sorbia Rendah</language>
			<language type="dua">Duala</language>
			<language type="dum">Belanda Tengah</language>
			<language type="dv">Divehi</language>
			<language type="dyo">Jola-Fonyi</language>
			<language type="dyu">Dyula</language>
			<language type="dz">Dzongkha</language>
			<language type="dzg">Dazaga</language>
			<language type="ebu">Embu</language>
			<language type="ee">Ewe</language>
			<language type="efi">Efik</language>
			<language type="egy">Mesir Kuno</language>
			<language type="eka">Ekajuk</language>
			<language type="el">Yunani</language>
			<language type="elx">Elam</language>
			<language type="en">Inggris</language>
			<language type="en_AU">Inggris Australia</language>
			<language type="en_CA">Inggris Kanada</language>
			<language type="en_GB">Inggris Inggris</language>
			<language type="en_GB" alt="short">Inggris U.K.</language>
			<language type="en_US">Inggris Amerika</language>
			<language type="en_US" alt="short">Inggris A.S.</language>
			<language type="enm">Inggris Abad Pertengahan</language>
			<language type="eo">Esperanto</language>
			<language type="es">Spanyol</language>
			<language type="es_419">Spanyol Amerika Latin</language>
			<language type="es_ES">Spanyol Eropa</language>
			<language type="es_MX">Spanyol Meksiko</language>
			<language type="et">Esti</language>
			<language type="eu">Bask</language>
			<language type="ewo">Ewondo</language>
			<language type="fa">Persia</language>
			<language type="fan">Fang</language>
			<language type="fat">Fanti</language>
			<language type="ff">Fula</language>
			<language type="fi">Suomi</language>
			<language type="fil">Filipino</language>
			<language type="fj">Fiji</language>
			<language type="fo">Faro</language>
			<language type="fon">Fon</language>
			<language type="fr">Prancis</language>
			<language type="fr_CA">Prancis Kanada</language>
			<language type="fr_CH">Prancis Swiss</language>
			<language type="frm">Prancis Abad Pertengahan</language>
			<language type="fro">Prancis Kuno</language>
			<language type="frr">Frisia Utara</language>
			<language type="frs">Frisia Timur</language>
			<language type="fur">Friuli</language>
			<language type="fy">Frisia Barat</language>
			<language type="ga">Irlandia</language>
			<language type="gaa">Ga</language>
			<language type="gay">Gayo</language>
			<language type="gba">Gbaya</language>
			<language type="gd">Gaelik Skotlandia</language>
			<language type="gez">Geez</language>
			<language type="gil">Gilbert</language>
			<language type="gl">Galisia</language>
			<language type="gmh">Jerman Abad Pertengahan</language>
			<language type="gn">Guarani</language>
			<language type="goh">Jerman Kuno</language>
			<language type="gon">Gondi</language>
			<language type="gor">Gorontalo</language>
			<language type="got">Gothik</language>
			<language type="grb">Grebo</language>
			<language type="grc">Yunani Kuno</language>
			<language type="gsw">Jerman Swiss</language>
			<language type="gu">Gujarati</language>
			<language type="guz">Gusii</language>
			<language type="gv">Manx</language>
			<language type="gwi">Gwich'in</language>
			<language type="ha">Hausa</language>
			<language type="hai">Haida</language>
			<language type="haw">Hawaii</language>
			<language type="he">Ibrani</language>
			<language type="hi">Hindi</language>
			<language type="hil">Hiligaynon</language>
			<language type="hit">Hitit</language>
			<language type="hmn">Hmong</language>
			<language type="ho">Hiri Motu</language>
			<language type="hr">Kroasia</language>
			<language type="hsb">Sorbia Atas</language>
			<language type="ht">Haiti</language>
			<language type="hu">Hungaria</language>
			<language type="hup">Hupa</language>
			<language type="hy">Armenia</language>
			<language type="hz">Herero</language>
			<language type="ia">Interlingua</language>
			<language type="iba">Iban</language>
			<language type="ibb">Ibibio</language>
			<language type="id">Bahasa Indonesia</language>
			<language type="ie">Interlingue</language>
			<language type="ig">Igbo</language>
			<language type="ii">Sichuan Yi</language>
			<language type="ik">Inupiak</language>
			<language type="ilo">Iloko</language>
			<language type="inh">Ingushetia</language>
			<language type="io">Ido</language>
			<language type="is">Islandia</language>
			<language type="it">Italia</language>
			<language type="iu">Inuktitut</language>
			<language type="ja">Jepang</language>
			<language type="jbo">Lojban</language>
			<language type="jgo" draft="contributed">Ngomba</language>
			<language type="jmc">Machame</language>
			<language type="jpr">Ibrani-Persia</language>
			<language type="jrb">Ibrani-Arab</language>
			<language type="jv">Jawa</language>
			<language type="ka">Georgia</language>
			<language type="kaa">Kara-Kalpak</language>
			<language type="kab">Kabyle</language>
			<language type="kac">Kachin</language>
			<language type="kaj">Jju</language>
			<language type="kam">Kamba</language>
			<language type="kaw">Kawi</language>
			<language type="kbd">Kabardi</language>
			<language type="kbl">Kanembu</language>
			<language type="kcg">Tyap</language>
			<language type="kde">Makonde</language>
			<language type="kea">Kabuverdianu</language>
			<language type="kfo">Koro</language>
			<language type="kg">Kongo</language>
			<language type="kha">Khasi</language>
			<language type="kho">Khotan</language>
			<language type="khq">Koyra Chiini</language>
			<language type="ki">Kikuyu</language>
			<language type="kj">Kuanyama</language>
			<language type="kk">Kazakh</language>
			<language type="kkj">Kako</language>
			<language type="kl">Kalaallisut</language>
			<language type="kln">Kalenjin</language>
			<language type="km">Khmer</language>
			<language type="kmb">Kimbundu</language>
			<language type="kn">Kannada</language>
			<language type="ko">Korea</language>
			<language type="kok">Konkani</language>
			<language type="kos">Kosre</language>
			<language type="kpe">Kpelle</language>
			<language type="kr">Kanuri</language>
			<language type="krc">Karachai Balkar</language>
			<language type="krl">Karelia</language>
			<language type="kru">Kuruk</language>
			<language type="ks">Kashmir</language>
			<language type="ksb">Shambala</language>
			<language type="ksf">Bafia</language>
			<language type="ksh">Dialek Kolsch</language>
			<language type="ku">Kurdi</language>
			<language type="kum">Kumyk</language>
			<language type="kut">Kutenai</language>
			<language type="kv">Komi</language>
			<language type="kw">Kornish</language>
			<language type="ky">Kirgiz</language>
			<language type="la">Latin</language>
			<language type="lad">Ladino</language>
			<language type="lag">Langi</language>
			<language type="lah">Lahnda</language>
			<language type="lam">Lamba</language>
			<language type="lb">Luksemburg</language>
			<language type="lez">Lezghia</language>
			<language type="lg">Ganda</language>
			<language type="li">Limburgia</language>
			<language type="ln">Lingala</language>
			<language type="lo">Lao</language>
			<language type="lol">Mongo</language>
			<language type="loz">Lozi</language>
			<language type="lt">Lituavi</language>
			<language type="lu">Luba-Katanga</language>
			<language type="lua">Luba-Lulua</language>
			<language type="lui">Luiseno</language>
			<language type="lun">Lunda</language>
			<language type="luo">Luo</language>
			<language type="lus">Mizo</language>
			<language type="luy">Luyia</language>
			<language type="lv">Latvi</language>
			<language type="mad">Madura</language>
			<language type="maf">Mafa</language>
			<language type="mag">Magahi</language>
			<language type="mai">Maithili</language>
			<language type="mak">Makasar</language>
			<language type="man">Mandingo</language>
			<language type="mas">Masai</language>
			<language type="mde">Maba</language>
			<language type="mdf">Moksha</language>
			<language type="mdr">Mandar</language>
			<language type="men">Mende</language>
			<language type="mer">Meru</language>
			<language type="mfe">Morisien</language>
			<language type="mg">Malagasi</language>
			<language type="mga">Irlandia Abad Pertengahan</language>
			<language type="mgh">Makhuwa-Meetto</language>
			<language type="mgo" draft="contributed">meta'</language>
			<language type="mh">Marshall</language>
			<language type="mi">Maori</language>
			<language type="mic">Mikmak</language>
			<language type="min">Minangkabau</language>
			<language type="mk">Makedonia</language>
			<language type="ml">Malayalam</language>
			<language type="mn">Mongolia</language>
			<language type="mnc">Manchuria</language>
			<language type="mni">Manipuri</language>
			<language type="moh">Mohawk</language>
			<language type="mos">Mossi</language>
			<language type="mr">Marathi</language>
			<language type="ms">Melayu</language>
			<language type="mt">Malta</language>
			<language type="mua">Mundang</language>
			<language type="mul">Beberapa Bahasa</language>
			<language type="mus">Bahasa Muskogee</language>
			<language type="mwl">Miranda</language>
			<language type="mwr">Marwari</language>
			<language type="my">Burma</language>
			<language type="mye">Myene</language>
			<language type="myv">Eryza</language>
			<language type="na">Nauru</language>
			<language type="nap">Neapolitan</language>
			<language type="naq">Nama</language>
			<language type="nb">Bokmål Norwegia</language>
			<language type="nd">Ndebele Utara</language>
			<language type="nds">Jerman Rendah</language>
			<language type="ne">Nepali</language>
			<language type="new">Newari</language>
			<language type="ng">Ndonga</language>
			<language type="nia">Nias</language>
			<language type="niu">Niuea</language>
			<language type="nl">Belanda</language>
			<language type="nl_BE">Flemish</language>
			<language type="nmg">Kwasio</language>
			<language type="nn">Nynorsk Norwegia</language>
			<language type="nnh">Ngiemboon</language>
			<language type="no">Norwegia</language>
			<language type="nog">Nogai</language>
			<language type="non">Norse Kuno</language>
			<language type="nqo">N'Ko</language>
			<language type="nr">Ndebele Selatan</language>
			<language type="nso">Sotho Utara</language>
			<language type="nus">Nuer</language>
			<language type="nv">Navajo</language>
			<language type="nwc">Newari Klasik</language>
			<language type="ny">Nyanja</language>
			<language type="nym">Nyamwezi</language>
			<language type="nyn">Nyankole</language>
			<language type="nyo">Nyoro</language>
			<language type="nzi">Nzima</language>
			<language type="oc">Ositania</language>
			<language type="oj">Ojibwa</language>
			<language type="om">Oromo</language>
			<language type="or">Oriya</language>
			<language type="os">Ossetia</language>
			<language type="osa">Osage</language>
			<language type="ota">Turki Osmani</language>
			<language type="pa">Punjabi</language>
			<language type="pag">Pangasina</language>
			<language type="pal">Pahlevi</language>
			<language type="pam">Pampanga</language>
			<language type="pap">Papiamento</language>
			<language type="pau">Palau</language>
			<language type="peo">Persia Kuno</language>
			<language type="phn">Funisia</language>
			<language type="pi">Pali</language>
			<language type="pl">Polski</language>
			<language type="pon">Pohnpeia</language>
			<language type="pro">Provencal Lama</language>
			<language type="ps">Pashto</language>
			<language type="ps" alt="variant">Pushto</language>
			<language type="pt">Portugis</language>
			<language type="pt_BR">Portugis Brasil</language>
			<language type="pt_PT">Portugis Eropa</language>
			<language type="qu">Quechua</language>
			<language type="raj">Rajasthani</language>
			<language type="rap">Rapanui</language>
			<language type="rar">Rarotonga</language>
			<language type="rm">Reto-Roman</language>
			<language type="rn">Rundi</language>
			<language type="ro">Rumania</language>
			<language type="ro_MD">Moldavia</language>
			<language type="rof">Rombo</language>
			<language type="rom">Romani</language>
			<language type="root">Root</language>
			<language type="ru">Rusia</language>
			<language type="rup">Makedo-Rumania</language>
			<language type="rw">Kinyarwanda</language>
			<language type="rwk">Rwa</language>
			<language type="sa">Sanskerta</language>
			<language type="sad">Sandawe</language>
			<language type="sah">Sakha</language>
			<language type="sam">Aram Samaria</language>
			<language type="saq">Samburu</language>
			<language type="sas">Sasak</language>
			<language type="sat">Santali</language>
			<language type="sba">Ngambai</language>
			<language type="sbp">Sangu</language>
			<language type="sc">Sardinia</language>
			<language type="scn">Sisilia</language>
			<language type="sco">Skotlandia</language>
			<language type="sd">Sindhi</language>
			<language type="se">Sami Utara</language>
			<language type="see">Seneca</language>
			<language type="seh">Sena</language>
			<language type="sel">Selkup</language>
			<language type="ses">Koyraboro Senni</language>
			<language type="sg">Sango</language>
			<language type="sga">Irlandia Kuno</language>
			<language type="sh">Serbo-Kroasia</language>
			<language type="shi">Tachelhit</language>
			<language type="shn">Shan</language>
			<language type="shu">Arab Suwa</language>
			<language type="si">Sinhala</language>
			<language type="sid">Sidamo</language>
			<language type="sk">Slovak</language>
			<language type="sl">Sloven</language>
			<language type="sm">Samoa</language>
			<language type="sma">Sami Selatan</language>
			<language type="smj">Lule Sami</language>
			<language type="smn">Inari Sami</language>
			<language type="sms">Skolt Sami</language>
			<language type="sn">Shona</language>
			<language type="snk">Soninke</language>
			<language type="so">Somali</language>
			<language type="sog">Sogdien</language>
			<language type="sq">Albania</language>
			<language type="sr">Serb</language>
			<language type="srn">Sranan Tongo</language>
			<language type="srr">Serer</language>
			<language type="ss">Swati</language>
			<language type="ssy">Saho</language>
			<language type="st">Sotho Selatan</language>
			<language type="su">Sunda</language>
			<language type="suk">Sukuma</language>
			<language type="sus">Susu</language>
			<language type="sux">Sumeria</language>
			<language type="sv">Swedia</language>
			<language type="sw">Swahili</language>
			<language type="swb">Komoria</language>
			<language type="swc">Kongo Swahili</language>
			<language type="syc">Suriah Klasik</language>
			<language type="syr">Suriah</language>
			<language type="ta">Tamil</language>
			<language type="te">Telugu</language>
			<language type="tem">Timne</language>
			<language type="teo">Teso</language>
			<language type="ter">Tereno</language>
			<language type="tet">Tetun</language>
			<language type="tg">Tajik</language>
			<language type="th">Thai</language>
			<language type="ti">Tigrinya</language>
			<language type="tig">Tigre</language>
			<language type="tiv">Tiv</language>
			<language type="tk">Turkmen</language>
			<language type="tkl">Tokelau</language>
			<language type="tl">Tagalog</language>
			<language type="tlh">Klingon</language>
			<language type="tli">Tlingit</language>
			<language type="tmh">Tamashek</language>
			<language type="tn">Tswana</language>
			<language type="to">Tonga</language>
			<language type="tog">Nyasa Tonga</language>
			<language type="tpi">Tok Pisin</language>
			<language type="tr">Turki</language>
			<language type="trv">Taroko</language>
			<language type="ts">Tsonga</language>
			<language type="tsi">Tsimshia</language>
			<language type="tt">Tatar</language>
			<language type="tum">Tumbuka</language>
			<language type="tvl">Tuvalu</language>
			<language type="tw">Twi</language>
			<language type="twq">Tasawaq</language>
			<language type="ty">Tahiti</language>
			<language type="tyv">Tuvinia</language>
			<language type="tzm">Tamazight Maroko Tengah</language>
			<language type="udm">Udmurt</language>
			<language type="ug">Uyghur</language>
			<language type="ug" alt="variant">Uighur</language>
			<language type="uga">Ugarit</language>
			<language type="uk">Ukraina</language>
			<language type="umb">Umbundu</language>
			<language type="und">Bahasa Tidak Dikenal</language>
			<language type="ur">Urdu</language>
			<language type="uz">Uzbek</language>
			<language type="vai">Vai</language>
			<language type="ve">Venda</language>
			<language type="vi">Vietnam</language>
			<language type="vo">Volapuk</language>
			<language type="vot">Votia</language>
			<language type="vun">Vunjo</language>
			<language type="wa">Walloon</language>
			<language type="wae">Walser</language>
			<language type="wal">Walamo</language>
			<language type="war">Warai</language>
			<language type="was">Washo</language>
			<language type="wo">Wolof</language>
			<language type="xal">Kalmuk</language>
			<language type="xh">Xhosa</language>
			<language type="xog">Soga</language>
			<language type="yao">Yao</language>
			<language type="yap">Yapois</language>
			<language type="yav">Yangben</language>
			<language type="ybb">Yemba</language>
			<language type="yi">Yiddish</language>
			<language type="yo">Yoruba</language>
			<language type="yue">Kanton</language>
			<language type="za">Zhuang</language>
			<language type="zap">Zapotek</language>
			<language type="zbl">Blissymbol</language>
			<language type="zen">Zenaga</language>
			<language type="zgh">Tamazight Maroko Standar</language>
			<language type="zh">China</language>
			<language type="zh_Hans">China (Aksara Sederhana)</language>
			<language type="zh_Hant">China (Aksara Tradisional)</language>
			<language type="zu">Zulu</language>
			<language type="zun">Zuni</language>
			<language type="zxx">Tidak ada konten linguistik</language>
			<language type="zza">Zaza</language>
		</languages>
		<scripts>
			<script type="Afak">Afaka</script>
			<script type="Arab">Arab</script>
			<script type="Arab" alt="variant">Arab Persia</script>
			<script type="Armi">Aram Imperial</script>
			<script type="Armn">Armenia</script>
			<script type="Avst">Avesta</script>
			<script type="Bali">Bali</script>
			<script type="Bamu">Bamum</script>
			<script type="Bass">Bassa Vah</script>
			<script type="Batk">Batak</script>
			<script type="Beng">Bengali</script>
			<script type="Blis">Blissymbol</script>
			<script type="Bopo">Bopomofo</script>
			<script type="Brah">Brahmi</script>
			<script type="Brai">Braille</script>
			<script type="Bugi">Bugis</script>
			<script type="Buhd">Buhid</script>
			<script type="Cakm">Chakma</script>
			<script type="Cans">Simbol Aborigin Kanada Kesatuan</script>
			<script type="Cari">Karia</script>
			<script type="Cham">Cham</script>
			<script type="Cher">Cherokee</script>
			<script type="Cirt">Cirth</script>
			<script type="Copt">Koptik</script>
			<script type="Cprt">Siprus</script>
			<script type="Cyrl">Sirilik</script>
			<script type="Cyrs">Gereja Slavonia Sirilik Lama</script>
			<script type="Deva">Devanagari</script>
			<script type="Dsrt">Deseret</script>
			<script type="Dupl">Stenografi Duployan</script>
			<script type="Egyd">Demotik Mesir</script>
			<script type="Egyh">Hieratik Mesir</script>
			<script type="Egyp">Hieroglip Mesir</script>
			<script type="Ethi">Etiopia</script>
			<script type="Geok">Georgian Khutsuri</script>
			<script type="Geor">Georgia</script>
			<script type="Glag">Glagolitic</script>
			<script type="Goth">Gothic</script>
			<script type="Gran">Grantha</script>
			<script type="Grek">Yunani</script>
			<script type="Gujr">Gujarati</script>
			<script type="Guru">Gurmukhi</script>
			<script type="Hang">Hangul</script>
			<script type="Hani">Han</script>
			<script type="Hano">Hanunoo</script>
			<script type="Hans">Sederhana</script>
			<script type="Hans" alt="stand-alone">Han Sederhana</script>
			<script type="Hant">Tradisional</script>
			<script type="Hant" alt="stand-alone">Han Tradisional</script>
			<script type="Hebr">Ibrani</script>
			<script type="Hira">Hiragana</script>
			<script type="Hluw">Hieroglif Anatolia</script>
			<script type="Hmng">Pahawh Hmong</script>
			<script type="Hrkt">Katakana atau Hiragana</script>
			<script type="Hung">Hungaria Kuno</script>
			<script type="Inds">Indus</script>
			<script type="Ital">Italia Lama</script>
			<script type="Java">Jawa</script>
			<script type="Jpan">Jepang</script>
			<script type="Jurc">Jurchen</script>
			<script type="Kali">Kayah Li</script>
			<script type="Kana">Katakana</script>
			<script type="Khar">Kharoshthi</script>
			<script type="Khmr">Khmer</script>
			<script type="Khoj">Khojki</script>
			<script type="Knda">Kannada</script>
			<script type="Kore">Korea</script>
			<script type="Kpel">Kpelle</script>
			<script type="Kthi">Kaithi</script>
			<script type="Lana">Lanna</script>
			<script type="Laoo">Laos</script>
			<script type="Latf">Latin Fraktur</script>
			<script type="Latg">Latin Gaelik</script>
			<script type="Latn">Latin</script>
			<script type="Lepc">Lepcha</script>
			<script type="Limb">Limbu</script>
			<script type="Lina">Linear A</script>
			<script type="Linb">Linear B</script>
			<script type="Lisu">Lisu</script>
			<script type="Loma">Loma</script>
			<script type="Lyci">Lycia</script>
			<script type="Lydi">Lydia</script>
			<script type="Mand">Mandae</script>
			<script type="Mani">Manikhei</script>
			<script type="Maya">Hieroglip Maya</script>
			<script type="Mend">Mende</script>
			<script type="Merc">Kursif Meroitik</script>
			<script type="Mero">Meroitik</script>
			<script type="Mlym">Malayalam</script>
			<script type="Mong">Mongolia</script>
			<script type="Moon">Moon</script>
			<script type="Mroo">Mro</script>
			<script type="Mtei">Meitei Mayek</script>
			<script type="Mymr">Myanmar</script>
			<script type="Narb">Arab Utara Kuno</script>
			<script type="Nbat">Nabataea</script>
			<script type="Nkgb">Naxi Geba</script>
			<script type="Nkoo">N'Ko</script>
			<script type="Nshu">Nushu</script>
			<script type="Ogam">Ogham</script>
			<script type="Olck">Chiki Lama</script>
			<script type="Orkh">Orkhon</script>
			<script type="Orya">Oriya</script>
			<script type="Osma">Osmanya</script>
			<script type="Palm">Palmira</script>
			<script type="Perm">Permik Kuno</script>
			<script type="Phag">Phags-pa</script>
			<script type="Phli">Pahlevi</script>
			<script type="Phlp">Mazmur Pahlevi</script>
			<script type="Phlv">Kitab Pahlevi</script>
			<script type="Phnx">Phoenix</script>
			<script type="Plrd">Fonetik Pollard</script>
			<script type="Prti">Prasasti Parthia</script>
			<script type="Rjng">Rejang</script>
			<script type="Roro">Rongorongo</script>
			<script type="Runr">Runik</script>
			<script type="Samr">Samaria</script>
			<script type="Sara">Sarati</script>
			<script type="Sarb">Arab Selatan Kuno</script>
			<script type="Saur">Saurashtra</script>
			<script type="Sgnw">Tulisan Isyarat</script>
			<script type="Shaw">Shavia</script>
			<script type="Shrd">Sharada</script>
			<script type="Sind">Khudawadi</script>
			<script type="Sinh">Sinhala</script>
			<script type="Sora">Sora Sompeng</script>
			<script type="Sund">Sunda</script>
			<script type="Sylo">Syloti Nagri</script>
			<script type="Syrc">Suriah</script>
			<script type="Syre">Suriah Estrangelo</script>
			<script type="Syrj">Suriah Barat</script>
			<script type="Syrn">Suriah Timur</script>
			<script type="Tagb">Tagbanwa</script>
			<script type="Takr">Takri</script>
			<script type="Tale">Tai Le</script>
			<script type="Talu">Tai Lue Baru</script>
			<script type="Taml">Tamil</script>
			<script type="Tang">Tangut</script>
			<script type="Tavt">Tai Viet</script>
			<script type="Telu">Telugu</script>
			<script type="Teng">Tenghwar</script>
			<script type="Tfng">Tifinagh</script>
			<script type="Tglg">Tagalog</script>
			<script type="Thaa">Thaana</script>
			<script type="Thai">Thai</script>
			<script type="Tibt">Tibet</script>
			<script type="Tirh">Tirhuta</script>
			<script type="Ugar">Ugaritik</script>
			<script type="Vaii">Vai</script>
			<script type="Visp">Ucapan Terlihat</script>
			<script type="Wara">Varang Kshiti</script>
			<script type="Wole">Woleai</script>
			<script type="Xpeo">Persia Kuno</script>
			<script type="Xsux">Cuneiform Sumero-Akkadia</script>
			<script type="Yiii">Yi</script>
			<script type="Zinh">Warisan</script>
			<script type="Zmth">Notasi Matematika</script>
			<script type="Zsym">Simbol</script>
			<script type="Zxxx">Tidak Tertulis</script>
			<script type="Zyyy">Umum</script>
			<script type="Zzzz">Skrip Tak Dikenal</script>
		</scripts>
		<territories>
			<territory type="001">Dunia</territory>
			<territory type="002">Afrika</territory>
			<territory type="003">Amerika Utara</territory>
			<territory type="005">Amerika Selatan</territory>
			<territory type="009">Oseania</territory>
			<territory type="011">Afrika Barat</territory>
			<territory type="013">Amerika Tengah</territory>
			<territory type="014">Afrika Timur</territory>
			<territory type="015">Afrika Utara</territory>
			<territory type="017">Afrika Tengah</territory>
			<territory type="018">Afrika Bagian Selatan</territory>
			<territory type="019">Amerika</territory>
			<territory type="021">Amerika Bagian Utara</territory>
			<territory type="029">Kepulauan Karibia</territory>
			<territory type="030">Asia Timur</territory>
			<territory type="034">Asia Selatan</territory>
			<territory type="035">Asia Tenggara</territory>
			<territory type="039">Eropa Selatan</territory>
			<territory type="053">Australasia</territory>
			<territory type="054">Melanesia</territory>
			<territory type="057">Wilayah Mikronesia</territory>
			<territory type="061">Polinesia</territory>
			<territory type="142">Asia</territory>
			<territory type="143">Asia Tengah</territory>
			<territory type="145">Asia Barat</territory>
			<territory type="150">Eropa</territory>
			<territory type="151">Eropa Timur</territory>
			<territory type="154">Eropa Utara</territory>
			<territory type="155">Eropa Barat</territory>
			<territory type="419">Amerika Latin</territory>
			<territory type="AC">Pulau Ascension</territory>
			<territory type="AD">Andorra</territory>
			<territory type="AE">Uni Emirat Arab</territory>
			<territory type="AF">Afganistan</territory>
			<territory type="AG">Antigua dan Barbuda</territory>
			<territory type="AI">Anguilla</territory>
			<territory type="AL">Albania</territory>
			<territory type="AM">Armenia</territory>
			<territory type="AN">Antilla Belanda</territory>
			<territory type="AO">Angola</territory>
			<territory type="AQ">Antarktika</territory>
			<territory type="AR">Argentina</territory>
			<territory type="AS">Samoa Amerika</territory>
			<territory type="AT">Austria</territory>
			<territory type="AU">Australia</territory>
			<territory type="AW">Aruba</territory>
			<territory type="AX">Kepulauan Aland</territory>
			<territory type="AZ">Azerbaijan</territory>
			<territory type="BA">Bosnia dan Herzegovina</territory>
			<territory type="BB">Barbados</territory>
			<territory type="BD">Bangladesh</territory>
			<territory type="BE">Belgia</territory>
			<territory type="BF">Burkina Faso</territory>
			<territory type="BG">Bulgaria</territory>
			<territory type="BH">Bahrain</territory>
			<territory type="BI">Burundi</territory>
			<territory type="BJ">Benin</territory>
			<territory type="BL">Saint Barthelemy</territory>
			<territory type="BM">Bermuda</territory>
			<territory type="BN">Brunei</territory>
			<territory type="BO">Bolivia</territory>
			<territory type="BQ">Karibia Belanda</territory>
			<territory type="BR">Brasil</territory>
			<territory type="BS">Bahama</territory>
			<territory type="BT">Bhutan</territory>
			<territory type="BV">Pulau Bouvet</territory>
			<territory type="BW">Botswana</territory>
			<territory type="BY">Belarus</territory>
			<territory type="BZ">Belize</territory>
			<territory type="CA">Kanada</territory>
			<territory type="CC">Kepulauan Cocos</territory>
			<territory type="CD">Kongo - Kinshasa</territory>
			<territory type="CD" alt="variant">Kongo (RDK)</territory>
			<territory type="CF">Republik Afrika Tengah</territory>
			<territory type="CG">Kongo - Brazzaville</territory>
			<territory type="CG" alt="variant">Kongo (Republik)</territory>
			<territory type="CH">Swiss</territory>
			<territory type="CI">Cote d'Ivoire</territory>
			<territory type="CI" alt="variant">Ivory Coast</territory>
			<territory type="CK">Kepulauan Cook</territory>
			<territory type="CL">Cile</territory>
			<territory type="CM">Kamerun</territory>
			<territory type="CN">China</territory>
			<territory type="CO">Kolombia</territory>
			<territory type="CP">Pulau Clipperton</territory>
			<territory type="CR">Kosta Rika</territory>
			<territory type="CU">Kuba</territory>
			<territory type="CV">Tanjung Verde</territory>
			<territory type="CW">Curaçao</territory>
			<territory type="CX">Pulau Christmas</territory>
			<territory type="CY">Siprus</territory>
			<territory type="CZ">Republik Cheska</territory>
			<territory type="DE">Jerman</territory>
			<territory type="DG">Diego Garcia</territory>
			<territory type="DJ">Jibuti</territory>
			<territory type="DK">Denmark</territory>
			<territory type="DM">Dominika</territory>
			<territory type="DO">Republik Dominika</territory>
			<territory type="DZ">Aljazair</territory>
			<territory type="EA">Ceuta dan Melilla</territory>
			<territory type="EC">Ekuador</territory>
			<territory type="EE">Estonia</territory>
			<territory type="EG">Mesir</territory>
			<territory type="EH">Sahara Barat</territory>
			<territory type="ER">Eritrea</territory>
			<territory type="ES">Spanyol</territory>
			<territory type="ET">Etiopia</territory>
			<territory type="EU">Uni Eropa</territory>
			<territory type="FI">Finlandia</territory>
			<territory type="FJ">Fiji</territory>
			<territory type="FK">Kepulauan Malvinas</territory>
			<territory type="FK" alt="variant">Kepulauan Malvinas (Falkland)</territory>
			<territory type="FM">Mikronesia</territory>
			<territory type="FO">Kepulauan Faroe</territory>
			<territory type="FR">Prancis</territory>
			<territory type="GA">Gabon</territory>
			<territory type="GB">Inggris</territory>
			<territory type="GB" alt="short">GB</territory>
			<territory type="GD">Grenada</territory>
			<territory type="GE">Georgia</territory>
			<territory type="GF">Guyana Prancis</territory>
			<territory type="GG">Guernsey</territory>
			<territory type="GH">Ghana</territory>
			<territory type="GI">Gibraltar</territory>
			<territory type="GL">Grinlandia</territory>
			<territory type="GM">Gambia</territory>
			<territory type="GN">Guinea</territory>
			<territory type="GP">Guadeloupe</territory>
			<territory type="GQ">Guinea Ekuatorial</territory>
			<territory type="GR">Yunani</territory>
			<territory type="GS">Georgia Selatan dan Kepulauan Sandwich Selatan</territory>
			<territory type="GT">Guatemala</territory>
			<territory type="GU">Guam</territory>
			<territory type="GW">Guinea-Bissau</territory>
			<territory type="GY">Guyana</territory>
			<territory type="HK">Hong Kong SAR China</territory>
			<territory type="HK" alt="short">Hong Kong</territory>
			<territory type="HM">Pulau Heard dan Kepulauan McDonald</territory>
			<territory type="HN">Honduras</territory>
			<territory type="HR">Kroasia</territory>
			<territory type="HT">Haiti</territory>
			<territory type="HU">Hungaria</territory>
			<territory type="IC">Kepulauan Canary</territory>
			<territory type="ID">Indonesia</territory>
			<territory type="IE">Irlandia</territory>
			<territory type="IL">Israel</territory>
			<territory type="IM">Pulau Man</territory>
			<territory type="IN">India</territory>
			<territory type="IO">Wilayah Inggris di Samudra Hindia</territory>
			<territory type="IQ">Irak</territory>
			<territory type="IR">Iran</territory>
			<territory type="IS">Islandia</territory>
			<territory type="IT">Italia</territory>
			<territory type="JE">Jersey</territory>
			<territory type="JM">Jamaika</territory>
			<territory type="JO">Yordania</territory>
			<territory type="JP">Jepang</territory>
			<territory type="KE">Kenya</territory>
			<territory type="KG">Kirgistan</territory>
			<territory type="KH">Kamboja</territory>
			<territory type="KI">Kiribati</territory>
			<territory type="KM">Komoro</territory>
			<territory type="KN">Saint Kitts dan Nevis</territory>
			<territory type="KP">Korea Utara</territory>
			<territory type="KR">Korea Selatan</territory>
			<territory type="KW">Kuwait</territory>
			<territory type="KY">Kepulauan Cayman</territory>
			<territory type="KZ">Kazakstan</territory>
			<territory type="LA">Laos</territory>
			<territory type="LB">Lebanon</territory>
			<territory type="LC">Saint Lucia</territory>
			<territory type="LI">Liechtenstein</territory>
			<territory type="LK">Sri Lanka</territory>
			<territory type="LR">Liberia</territory>
			<territory type="LS">Lesotho</territory>
			<territory type="LT">Lituania</territory>
			<territory type="LU">Luksemburg</territory>
			<territory type="LV">Latvia</territory>
			<territory type="LY">Libia</territory>
			<territory type="MA">Maroko</territory>
			<territory type="MC">Monako</territory>
			<territory type="MD">Moldova</territory>
			<territory type="ME">Montenegro</territory>
			<territory type="MF">Saint Martin</territory>
			<territory type="MG">Madagaskar</territory>
			<territory type="MH">Kepulauan Marshall</territory>
			<territory type="MK">Makedonia</territory>
			<territory type="MK" alt="variant">Makedonia (BRY)</territory>
			<territory type="ML">Mali</territory>
			<territory type="MM">Myanmar (Burma)</territory>
			<territory type="MN">Mongolia</territory>
			<territory type="MO">Makau SAR China</territory>
			<territory type="MO" alt="short">Makau</territory>
			<territory type="MP">Kepulauan Mariana Utara</territory>
			<territory type="MQ">Martinik</territory>
			<territory type="MR">Mauritania</territory>
			<territory type="MS">Montserrat</territory>
			<territory type="MT">Malta</territory>
			<territory type="MU">Mauritius</territory>
			<territory type="MV">Maladewa</territory>
			<territory type="MW">Malawi</territory>
			<territory type="MX">Meksiko</territory>
			<territory type="MY">Malaysia</territory>
			<territory type="MZ">Mozambik</territory>
			<territory type="NA">Namibia</territory>
			<territory type="NC">Kaledonia Baru</territory>
			<territory type="NE">Niger</territory>
			<territory type="NF">Kepulauan Norfolk</territory>
			<territory type="NG">Nigeria</territory>
			<territory type="NI">Nikaragua</territory>
			<territory type="NL">Belanda</territory>
			<territory type="NO">Norwegia</territory>
			<territory type="NP">Nepal</territory>
			<territory type="NR">Nauru</territory>
			<territory type="NU">Niue</territory>
			<territory type="NZ">Selandia Baru</territory>
			<territory type="OM">Oman</territory>
			<territory type="PA">Panama</territory>
			<territory type="PE">Peru</territory>
			<territory type="PF">Polinesia Prancis</territory>
			<territory type="PG">Papua Nugini</territory>
			<territory type="PH">Filipina</territory>
			<territory type="PK">Pakistan</territory>
			<territory type="PL">Polandia</territory>
			<territory type="PM">Saint Pierre dan Miquelon</territory>
			<territory type="PN">Kepulauan Pitcairn</territory>
			<territory type="PR">Puerto Riko</territory>
			<territory type="PS">Wilayah Palestina</territory>
			<territory type="PS" alt="short">Palestina</territory>
			<territory type="PT">Portugal</territory>
			<territory type="PW">Palau</territory>
			<territory type="PY">Paraguay</territory>
			<territory type="QA">Qatar</territory>
			<territory type="QO">Oseania Luar</territory>
			<territory type="RE">Réunion</territory>
			<territory type="RO">Rumania</territory>
			<territory type="RS">Serbia</territory>
			<territory type="RU">Rusia</territory>
			<territory type="RW">Rwanda</territory>
			<territory type="SA">Arab Saudi</territory>
			<territory type="SB">Kepulauan Solomon</territory>
			<territory type="SC">Seychelles</territory>
			<territory type="SD">Sudan</territory>
			<territory type="SE">Swedia</territory>
			<territory type="SG">Singapura</territory>
			<territory type="SH">Saint Helena</territory>
			<territory type="SI">Slovenia</territory>
			<territory type="SJ">Kepulauan Svalbard dan Jan Mayen</territory>
			<territory type="SK">Slovakia</territory>
			<territory type="SL">Sierra Leone</territory>
			<territory type="SM">San Marino</territory>
			<territory type="SN">Senegal</territory>
			<territory type="SO">Somalia</territory>
			<territory type="SR">Suriname</territory>
			<territory type="SS">Sudan Selatan</territory>
			<territory type="ST">Sao Tome dan Principe</territory>
			<territory type="SV">El Salvador</territory>
			<territory type="SX">Sint Maarten</territory>
			<territory type="SY">Suriah</territory>
			<territory type="SZ">Swaziland</territory>
			<territory type="TA">Tristan da Cunha</territory>
			<territory type="TC">Kepulauan Turks dan Caicos</territory>
			<territory type="TD">Cad</territory>
			<territory type="TF">Wilayah Kutub Selatan Prancis</territory>
			<territory type="TG">Togo</territory>
			<territory type="TH">Thailand</territory>
			<territory type="TJ">Tajikistan</territory>
			<territory type="TK">Tokelau</territory>
			<territory type="TL">Timor Leste</territory>
			<territory type="TM">Turkimenistan</territory>
			<territory type="TN">Tunisia</territory>
			<territory type="TO">Tonga</territory>
			<territory type="TR">Turki</territory>
			<territory type="TT">Trinidad dan Tobago</territory>
			<territory type="TV">Tuvalu</territory>
			<territory type="TW">Taiwan</territory>
			<territory type="TZ">Tanzania</territory>
			<territory type="UA">Ukraina</territory>
			<territory type="UG">Uganda</territory>
			<territory type="UM">Kepulauan Terluar A.S.</territory>
			<territory type="US">Amerika Serikat</territory>
			<territory type="US" alt="short">A.S.</territory>
			<territory type="UY">Uruguay</territory>
			<territory type="UZ">Uzbekistan</territory>
			<territory type="VA">Vatikan</territory>
			<territory type="VC">Saint Vincent dan Grenadines</territory>
			<territory type="VE">Venezuela</territory>
			<territory type="VG">Kepulauan Virgin Inggris</territory>
			<territory type="VI">Kepulauan Virgin A.S.</territory>
			<territory type="VN">Vietnam</territory>
			<territory type="VU">Vanuatu</territory>
			<territory type="WF">Kepulauan Wallis dan Futuna</territory>
			<territory type="WS">Samoa</territory>
			<territory type="XK">Kosovo</territory>
			<territory type="YE">Yaman</territory>
			<territory type="YT">Mayotte</territory>
			<territory type="ZA">Afrika Selatan</territory>
			<territory type="ZM">Zambia</territory>
			<territory type="ZW">Zimbabwe</territory>
			<territory type="ZZ">Wilayah Tidak Dikenal</territory>
		</territories>
		<variants>
			<variant type="1901">Ortografi Jerman Tradisional</variant>
			<variant type="1994">Ortografi Resia Standar</variant>
			<variant type="1996">Ortografi Jerman 1996</variant>
			<variant type="1606NICT">Prancis Pertengahan Akhir sampai 1606</variant>
			<variant type="1694ACAD">Prancis Modern Awal</variant>
			<variant type="1959ACAD">Akademik</variant>
			<variant type="ALALC97">ALA-LC Latin, edisi 1997</variant>
			<variant type="ALUKU">Dialek Aluku</variant>
			<variant type="AREVELA">Armenia Timur</variant>
			<variant type="AREVMDA">Armenia Barat</variant>
			<variant type="BAKU1926">Alfabet Latin Turki Terpadu</variant>
			<variant type="BISCAYAN">BISKAY</variant>
			<variant type="BISKE">Dialek San Giorgio/Bila</variant>
			<variant type="BOONT">Boontling</variant>
			<variant type="FONIPA">Fonetik IPA</variant>
			<variant type="FONUPA">Fonetik UPA</variant>
			<variant type="HEPBURN">Hepburn Latin</variant>
			<variant type="HOGNORSK">NORWEDIA TINGGI</variant>
			<variant type="KKCOR">Ortografi Umum</variant>
			<variant type="LIPAW">Dialek Lipovaz Resia</variant>
			<variant type="MONOTON">Monoton</variant>
			<variant type="NDYUKA">Dialek Ndyuka</variant>
			<variant type="NEDIS">Dialek Natiso</variant>
			<variant type="NJIVA">Dialek Gniva/Njiva</variant>
			<variant type="OSOJS">Dialek Oseacco/Osojane</variant>
			<variant type="PAMAKA">Dialek Pamaka</variant>
			<variant type="PINYIN">Pinyin Latin</variant>
			<variant type="POLYTON">Politon</variant>
			<variant type="POSIX">Komputer</variant>
			<variant type="REVISED">Ortografi Revisi</variant>
			<variant type="ROZAJ">Resia</variant>
			<variant type="SAAHO">Saho</variant>
			<variant type="SCOTLAND">Inggris Standar Skotlandia</variant>
			<variant type="SCOUSE">Skaus</variant>
			<variant type="SOLBA">Dialek Stolvizza/Solbica</variant>
			<variant type="TARASK">Ortografi Taraskievica</variant>
			<variant type="UCCOR">Ortografi Terpadu</variant>
			<variant type="UCRCOR">Ortografi Revisi Terpadu</variant>
			<variant type="VALENCIA">Valencia</variant>
			<variant type="WADEGILE">Wade-Giles Latin</variant>
		</variants>
		<keys>
			<key type="calendar">Kalender</key>
			<key type="colAlternate">Penyortiran Abaikan Simbol</key>
			<key type="colBackwards">Penyortiran Aksen Terbalik</key>
			<key type="colCaseFirst">Pengurutan Huruf Besar/Huruf Kecil</key>
			<key type="colCaseLevel">Penyortiran Peka Huruf Besar</key>
			<key type="colHiraganaQuaternary">Penyortiran Kana</key>
			<key type="collation">Urutan Sortir</key>
			<key type="colNormalization">Penyortiran Dinormalisasi</key>
			<key type="colNumeric">Penyortiran Numerik</key>
			<key type="colStrength">Kekuatan Penyortiran</key>
			<key type="currency">Mata Uang</key>
			<key type="numbers">Angka</key>
			<key type="timezone">Zona Waktu</key>
			<key type="va">Varian Lokal</key>
			<key type="variableTop">Sortir Sebagai Simbol</key>
			<key type="x">Penggunaan Pribadi</key>
		</keys>
		<types>
			<type type="arab" key="numbers">Angka Arab Timur</type>
			<type type="arabext" key="numbers">Angka Arab Timur Diperluas</type>
			<type type="armn" key="numbers">Angka Armenia</type>
			<type type="armnlow" key="numbers">Angka Huruf Kecil Armenia</type>
			<type type="bali" key="numbers">Angka Bali</type>
			<type type="beng" key="numbers">Angka Bengali</type>
			<type type="big5han" key="collation">Urutan Sortir China Tradisional - Big5</type>
			<type type="buddhist" key="calendar">Kalender Buddha</type>
			<type type="cham" key="numbers">Angka Cham</type>
			<type type="chinese" key="calendar">Kalender China</type>
			<type type="coptic" key="calendar">Kalender Koptik</type>
			<type type="deva" key="numbers">Angka Devanagari</type>
			<type type="dictionary" key="collation">Urutan Sortir Kamus</type>
			<type type="ducet" key="collation">Urutan Sortir Unicode Default</type>
			<type type="eor" key="collation">Aturan Pengurutan Eropa</type>
			<type type="ethi" key="numbers">Angka Etiopia</type>
			<type type="ethiopic" key="calendar">Kalender Etiopia</type>
			<type type="ethiopic-amete-alem" key="calendar">Kalender Amete Alem Etiopia</type>
			<type type="finance" key="numbers">Angka Finansial</type>
			<type type="fullwide" key="numbers">Angka Lebar Penuh</type>
			<type type="gb2312han" key="collation">Urutan Sortir China Aks. Sederhana - GB2312</type>
			<type type="geor" key="numbers">Angka Georgia</type>
			<type type="gregorian" key="calendar">Kalender Gregorian</type>
			<type type="grek" key="numbers">Angka Yunani</type>
			<type type="greklow" key="numbers">Angka Yunani Huruf Kecil</type>
			<type type="gujr" key="numbers">Angka Gujarati</type>
			<type type="guru" key="numbers">Angka Gurmukhi</type>
			<type type="hanidec" key="numbers">Angka Desimal China</type>
			<type type="hans" key="numbers">Angka China Sederhana</type>
			<type type="hansfin" key="numbers">Angka Keuangan China Sederhana</type>
			<type type="hant" key="numbers">Angka China Tradisional</type>
			<type type="hantfin" key="numbers">Angka Keuangan China Tradisional</type>
			<type type="hebr" key="numbers">Angka Ibrani</type>
			<type type="hebrew" key="calendar">Kalender Ibrani</type>
			<type type="identical" key="colStrength">Sortir Semua</type>
			<type type="indian" key="calendar">Kalender Nasional India</type>
			<type type="islamic" key="calendar">Kalender Islam</type>
			<type type="islamic-civil" key="calendar">Kalender Sipil Islam</type>
			<type type="japanese" key="calendar">Kalender Jepang</type>
			<type type="java" key="numbers">Angka Jawa</type>
			<type type="jpan" key="numbers">Angka Jepang</type>
			<type type="jpanfin" key="numbers">Angka Keuangan Jepang</type>
			<type type="kali" key="numbers">Angka Kayah Li</type>
			<type type="khmr" key="numbers">Angka Khmer</type>
			<type type="knda" key="numbers">Angka Kannada</type>
			<type type="lana" key="numbers">Angka Tai Tham Hora</type>
			<type type="lanatham" key="numbers">Angka Tai Tham Tham</type>
			<type type="laoo" key="numbers">Angka Laos</type>
			<type type="latn" key="numbers">Angka Latin</type>
			<type type="lepc" key="numbers">Angka Lepcha</type>
			<type type="limb" key="numbers">Angka Limbu</type>
			<type type="lower" key="colCaseFirst">Sortir Huruf Kecil Dahulu</type>
			<type type="mlym" key="numbers">Angka Malayalam</type>
			<type type="mong" key="numbers">Angka Mongolia</type>
			<type type="mtei" key="numbers">Angka Meetei Mayek</type>
			<type type="mymr" key="numbers">Angka Myanmar</type>
			<type type="mymrshan" key="numbers">Angka Myanmar Shan</type>
			<type type="native" key="numbers">Digit Asli</type>
			<type type="nkoo" key="numbers">Angka N'Ko</type>
			<type type="no" key="colBackwards">Sortir Aksen Secara Normal</type>
			<type type="no" key="colCaseFirst">Sortir Urutan Ukuran Huruf Normal</type>
			<type type="no" key="colCaseLevel">Sortir Tidak Peka Huruf Besar</type>
			<type type="no" key="colHiraganaQuaternary">Sortir Kana Secara Terpisah</type>
			<type type="no" key="colNormalization">Sortir Tanpa Normalisasi</type>
			<type type="no" key="colNumeric">Sortir Digit Satu Per Satu</type>
			<type type="non-ignorable" key="colAlternate">Sortir Simbol</type>
			<type type="olck" key="numbers">Angka Ol Chiki</type>
			<type type="orya" key="numbers">Angka Oriya</type>
			<type type="persian" key="calendar">Kalender Persia</type>
			<type type="phonebook" key="collation">Urutan Sortir Buku Telepon</type>
			<type type="phonetic" key="collation">Urutan Sortir Fonetik</type>
			<type type="pinyin" key="collation">Urutan Sortir Pinyin</type>
			<type type="primary" key="colStrength">Sortir Huruf Dasar Saja</type>
			<type type="quaternary" key="colStrength">Sortir Aksen/Ukuran Huruf/Lebar/Kana</type>
			<type type="reformed" key="collation">Urutan Sortir yang Diubah Bentuknya</type>
			<type type="roc" key="calendar">Kalendar Minguo</type>
			<type type="roman" key="numbers">Angka Romawi</type>
			<type type="romanlow" key="numbers">Angka Huruf Kecil Romawi</type>
			<type type="saur" key="numbers">Angka Saurashtra</type>
			<type type="search" key="collation">Pencarian Tujuan Umum</type>
			<type type="searchjl" key="collation">Pencarian Menurut Konsonan Awal Hangul</type>
			<type type="secondary" key="colStrength">Sortir Aksen</type>
			<type type="shifted" key="colAlternate">Sortir Abaikan Simbol</type>
			<type type="standard" key="collation">Urutan Sortir Standar</type>
			<type type="stroke" key="collation">Urutan Sortir Guratan</type>
			<type type="sund" key="numbers">Angka Sunda</type>
			<type type="talu" key="numbers">Angka Tai Lue Baru</type>
			<type type="taml" key="numbers">Angka Tamil Tradisional</type>
			<type type="tamldec" key="numbers">Angka Tamil</type>
			<type type="telu" key="numbers">Angka Telugu</type>
			<type type="tertiary" key="colStrength">Sortir Aksen/Ukuran Huruf/Lebar</type>
			<type type="thai" key="numbers">Angka Thai</type>
			<type type="tibt" key="numbers">Angka Tibet</type>
			<type type="traditional" key="collation">Urutan Sortir Tradisional</type>
			<type type="traditional" key="numbers">Angka Tradisional</type>
			<type type="unihan" key="collation">Urutan Sortir Guratan Radikal</type>
			<type type="upper" key="colCaseFirst">Sortir Huruf Besar Dahulu</type>
			<type type="vaii" key="numbers">Angka Vai</type>
			<type type="yes" key="colBackwards">Sortir Aksen Terbalik</type>
			<type type="yes" key="colCaseLevel">Sortir Peka Huruf Besar</type>
			<type type="yes" key="colHiraganaQuaternary">Sortir Kana Secara Berbeda</type>
			<type type="yes" key="colNormalization">Sortir Unicode Dinormalisasi</type>
			<type type="yes" key="colNumeric">Sortir Digit Secara Numerik</type>
		</types>
		<transformNames>
			<transformName type="BGN">BGN</transformName>
			<transformName type="Numeric">Angka</transformName>
			<transformName type="Tone">Nada</transformName>
			<transformName type="UNGEGN">UNGEGN</transformName>
			<transformName type="x-Accents">Aksen</transformName>
			<transformName type="x-Fullwidth">Lebar penuh</transformName>
			<transformName type="x-Halfwidth">Lebar separuh</transformName>
			<transformName type="x-Jamo">Jamo</transformName>
			<transformName type="x-Pinyin">Pinyin</transformName>
			<transformName type="x-Publishing">Penerbitan</transformName>
		</transformNames>
		<measurementSystemNames>
			<measurementSystemName type="metric">Metrik</measurementSystemName>
			<measurementSystemName type="UK">BR</measurementSystemName>
			<measurementSystemName type="US">AS</measurementSystemName>
		</measurementSystemNames>
		<codePatterns>
			<codePattern type="language">Bahasa: {0}</codePattern>
			<codePattern type="script">Skrip: {0}</codePattern>
			<codePattern type="territory">Wilayah: {0}</codePattern>
		</codePatterns>
	</localeDisplayNames>
	<contextTransforms>
		<contextTransformUsage type="relative">
			<contextTransform type="stand-alone">titlecase-firstword</contextTransform>
			<contextTransform type="uiListOrMenu">titlecase-firstword</contextTransform>
		</contextTransformUsage>
	</contextTransforms>
	<characters>
		<exemplarCharacters>[a b c d e f g h i j k l m n o p q r s t u v w x y z]</exemplarCharacters>
		<exemplarCharacters type="auxiliary">[å q x z]</exemplarCharacters>
		<exemplarCharacters type="index">[A B C D E F G H I J K L M N O P Q R S T U V W X Y Z]</exemplarCharacters>
		<exemplarCharacters type="punctuation">[‐ – — , ; \: ! ? . … ' ‘ ’ “ ” ( ) \[ \] /]</exemplarCharacters>
		<ellipsis type="final">{0}…</ellipsis>
		<ellipsis type="initial">…{0}</ellipsis>
		<ellipsis type="medial">{0}…{1}</ellipsis>
		<ellipsis type="word-final">{0} …</ellipsis>
		<ellipsis type="word-initial">… {0}</ellipsis>
		<ellipsis type="word-medial">{0} … {1}</ellipsis>
		<moreInformation>?</moreInformation>
	</characters>
	<delimiters>
		<quotationStart>“</quotationStart>
		<quotationEnd>”</quotationEnd>
		<alternateQuotationStart>‘</alternateQuotationStart>
		<alternateQuotationEnd>’</alternateQuotationEnd>
	</delimiters>
	<dates>
		<calendars>
			<calendar type="buddhist">
				<eras>
					<eraAbbr>
						<era type="0">BE</era>
					</eraAbbr>
				</eras>
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>EEEE, dd MMMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>d MMMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>d MMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>d/M/y GGGGG</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<dateTimeFormats>
					<availableFormats>
						<dateFormatItem id="d">d</dateFormatItem>
						<dateFormatItem id="M">L</dateFormatItem>
						<dateFormatItem id="Md">d/M</dateFormatItem>
						<dateFormatItem id="MEd">E, d/M</dateFormatItem>
						<dateFormatItem id="MMM">LLL</dateFormatItem>
						<dateFormatItem id="MMMd">d MMM</dateFormatItem>
						<dateFormatItem id="MMMEd">E, d MMM</dateFormatItem>
						<dateFormatItem id="MMMMd">d MMMM</dateFormatItem>
						<dateFormatItem id="MMMMEd">E, d MMMM</dateFormatItem>
						<dateFormatItem id="y">G y</dateFormatItem>
					</availableFormats>
				</dateTimeFormats>
			</calendar>
			<calendar type="chinese">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
						</monthWidth>
					</monthContext>
					<monthContext type="stand-alone">
						<monthWidth type="abbreviated">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
						</monthWidth>
					</monthContext>
				</months>
				<cyclicNameSets>
					<cyclicNameSet type="dayParts">
						<cyclicNameContext type="format">
							<cyclicNameWidth type="abbreviated">
								<cyclicName type="1">zi</cyclicName>
								<cyclicName type="2">chou</cyclicName>
								<cyclicName type="3">yin</cyclicName>
								<cyclicName type="4">mao</cyclicName>
								<cyclicName type="5">chen</cyclicName>
								<cyclicName type="6">si</cyclicName>
								<cyclicName type="7">wu</cyclicName>
								<cyclicName type="8">wei</cyclicName>
								<cyclicName type="9">shen</cyclicName>
								<cyclicName type="10">you</cyclicName>
								<cyclicName type="11">xu</cyclicName>
								<cyclicName type="12">hai</cyclicName>
							</cyclicNameWidth>
						</cyclicNameContext>
					</cyclicNameSet>
					<cyclicNameSet type="years">
						<cyclicNameContext type="format">
							<cyclicNameWidth type="abbreviated">
								<cyclicName type="1">jia-zi</cyclicName>
								<cyclicName type="2">yi-chou</cyclicName>
								<cyclicName type="3">bing-yin</cyclicName>
								<cyclicName type="4">ding-mao</cyclicName>
								<cyclicName type="5">wu-chen</cyclicName>
								<cyclicName type="6">ji-si</cyclicName>
								<cyclicName type="7">geng-wu</cyclicName>
								<cyclicName type="8">xin-wei</cyclicName>
								<cyclicName type="9">ren-shen</cyclicName>
								<cyclicName type="10">gui-you</cyclicName>
								<cyclicName type="11">jia-xu</cyclicName>
								<cyclicName type="12">yi-hai</cyclicName>
								<cyclicName type="13">bing-zi</cyclicName>
								<cyclicName type="14">ding-chou</cyclicName>
								<cyclicName type="15">wu-yin</cyclicName>
								<cyclicName type="16">ji-mao</cyclicName>
								<cyclicName type="17">geng-chen</cyclicName>
								<cyclicName type="18">xin-si</cyclicName>
								<cyclicName type="19">ren-wu</cyclicName>
								<cyclicName type="20">gui-wei</cyclicName>
								<cyclicName type="21">jia-shen</cyclicName>
								<cyclicName type="22">yi-you</cyclicName>
								<cyclicName type="23">bing-xu</cyclicName>
								<cyclicName type="24">ding-hai</cyclicName>
								<cyclicName type="25">wu-zi</cyclicName>
								<cyclicName type="26">ji-chou</cyclicName>
								<cyclicName type="27">geng-yin</cyclicName>
								<cyclicName type="28">xin-mao</cyclicName>
								<cyclicName type="29">ren-chen</cyclicName>
								<cyclicName type="30">gui-si</cyclicName>
								<cyclicName type="31">jia-wu</cyclicName>
								<cyclicName type="32">yi-wei</cyclicName>
								<cyclicName type="33">bing-shen</cyclicName>
								<cyclicName type="34">ding-you</cyclicName>
								<cyclicName type="35">wu-xu</cyclicName>
								<cyclicName type="36">ji-hai</cyclicName>
								<cyclicName type="37">geng-zi</cyclicName>
								<cyclicName type="38">xin-chou</cyclicName>
								<cyclicName type="39">ren-yin</cyclicName>
								<cyclicName type="40">gui-mao</cyclicName>
								<cyclicName type="41">jia-chen</cyclicName>
								<cyclicName type="42">yi-si</cyclicName>
								<cyclicName type="43">bing-wu</cyclicName>
								<cyclicName type="44">ding-wei</cyclicName>
								<cyclicName type="45">wu-shen</cyclicName>
								<cyclicName type="46">ji-you</cyclicName>
								<cyclicName type="47">geng-xu</cyclicName>
								<cyclicName type="48">xin-hai</cyclicName>
								<cyclicName type="49">ren-zi</cyclicName>
								<cyclicName type="50">gui-chou</cyclicName>
								<cyclicName type="51">jia-yin</cyclicName>
								<cyclicName type="52">yi-mao</cyclicName>
								<cyclicName type="53">bing-chen</cyclicName>
								<cyclicName type="54">ding-si</cyclicName>
								<cyclicName type="55">wu-wu</cyclicName>
								<cyclicName type="56">ji-wei</cyclicName>
								<cyclicName type="57">geng-shen</cyclicName>
								<cyclicName type="58">xin-you</cyclicName>
								<cyclicName type="59">ren-xu</cyclicName>
								<cyclicName type="60">gui-hai</cyclicName>
							</cyclicNameWidth>
						</cyclicNameContext>
					</cyclicNameSet>
					<cyclicNameSet type="zodiacs">
						<cyclicNameContext type="format">
							<cyclicNameWidth type="abbreviated">
								<cyclicName type="1">zi</cyclicName>
								<cyclicName type="2">chou</cyclicName>
								<cyclicName type="3">yin</cyclicName>
								<cyclicName type="4">mao</cyclicName>
								<cyclicName type="5">chen</cyclicName>
								<cyclicName type="6">si</cyclicName>
								<cyclicName type="7">wu</cyclicName>
								<cyclicName type="8">wei</cyclicName>
								<cyclicName type="9">shen</cyclicName>
								<cyclicName type="10">you</cyclicName>
								<cyclicName type="11">xu</cyclicName>
								<cyclicName type="12">hai</cyclicName>
							</cyclicNameWidth>
						</cyclicNameContext>
					</cyclicNameSet>
				</cyclicNameSets>
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>EEEE, U MMMM dd</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>U MMMM d</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>U MMM d</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>y-M-d</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
			</calendar>
			<calendar type="coptic">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<month type="1">Tout</month>
							<month type="2">Baba</month>
							<month type="3">Hator</month>
							<month type="4">Kiahk</month>
							<month type="5">Toba</month>
							<month type="6">Amshir</month>
							<month type="7">Baramhat</month>
							<month type="8">Baramouda</month>
							<month type="9">Bashans</month>
							<month type="10">Paona</month>
							<month type="11">Epep</month>
							<month type="12">Mesra</month>
							<month type="13">Nasie</month>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
							<month type="13">13</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Tout</month>
							<month type="2">Baba</month>
							<month type="3">Hator</month>
							<month type="4">Kiahk</month>
							<month type="5">Toba</month>
							<month type="6">Amshir</month>
							<month type="7">Baramhat</month>
							<month type="8">Baramouda</month>
							<month type="9">Bashans</month>
							<month type="10">Paona</month>
							<month type="11">Epep</month>
							<month type="12">Mesra</month>
							<month type="13">Nasie</month>
						</monthWidth>
					</monthContext>
					<monthContext type="stand-alone">
						<monthWidth type="abbreviated">
							<month type="1">Tout</month>
							<month type="2">Baba</month>
							<month type="3">Hator</month>
							<month type="4">Kiahk</month>
							<month type="5">Toba</month>
							<month type="6">Amshir</month>
							<month type="7">Baramhat</month>
							<month type="8">Baramouda</month>
							<month type="9">Bashans</month>
							<month type="10">Paona</month>
							<month type="11">Epep</month>
							<month type="12">Mesra</month>
							<month type="13">Nasie</month>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
							<month type="13">13</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Tout</month>
							<month type="2">Baba</month>
							<month type="3">Hator</month>
							<month type="4">Kiahk</month>
							<month type="5">Toba</month>
							<month type="6">Amshir</month>
							<month type="7">Baramhat</month>
							<month type="8">Baramouda</month>
							<month type="9">Bashans</month>
							<month type="10">Paona</month>
							<month type="11">Epep</month>
							<month type="12">Mesra</month>
							<month type="13">Nasie</month>
						</monthWidth>
					</monthContext>
				</months>
			</calendar>
			<calendar type="ethiopic">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<month type="1">Meskerem</month>
							<month type="2">Tekemt</month>
							<month type="3">Hedar</month>
							<month type="4">Tahsas</month>
							<month type="5">Ter</month>
							<month type="6">Yekatit</month>
							<month type="7">Megabit</month>
							<month type="8">Miazia</month>
							<month type="9">Genbot</month>
							<month type="10">Sene</month>
							<month type="11">Hamle</month>
							<month type="12">Nehasse</month>
							<month type="13">Pagumen</month>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
							<month type="13">13</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Meskerem</month>
							<month type="2">Tekemt</month>
							<month type="3">Hedar</month>
							<month type="4">Tahsas</month>
							<month type="5">Ter</month>
							<month type="6">Yekatit</month>
							<month type="7">Megabit</month>
							<month type="8">Miazia</month>
							<month type="9">Genbot</month>
							<month type="10">Sene</month>
							<month type="11">Hamle</month>
							<month type="12">Nehasse</month>
							<month type="13">Pagumen</month>
						</monthWidth>
					</monthContext>
					<monthContext type="stand-alone">
						<monthWidth type="abbreviated">
							<month type="1">Meskerem</month>
							<month type="2">Tekemt</month>
							<month type="3">Hedar</month>
							<month type="4">Tahsas</month>
							<month type="5">Ter</month>
							<month type="6">Yekatit</month>
							<month type="7">Megabit</month>
							<month type="8">Miazia</month>
							<month type="9">Genbot</month>
							<month type="10">Sene</month>
							<month type="11">Hamle</month>
							<month type="12">Nehasse</month>
							<month type="13">Pagumen</month>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
							<month type="13">13</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Meskerem</month>
							<month type="2">Tekemt</month>
							<month type="3">Hedar</month>
							<month type="4">Tahsas</month>
							<month type="5">Ter</month>
							<month type="6">Yekatit</month>
							<month type="7">Megabit</month>
							<month type="8">Miazia</month>
							<month type="9">Genbot</month>
							<month type="10">Sene</month>
							<month type="11">Hamle</month>
							<month type="12">Nehasse</month>
							<month type="13">Pagumen</month>
						</monthWidth>
					</monthContext>
				</months>
			</calendar>
			<calendar type="generic">
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>EEEE, dd MMMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>d MMMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>d MMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>dd/MM/yy GGGGG</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<dateTimeFormats>
					<dateTimeFormatLength type="full">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="long">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="medium">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="short">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<availableFormats>
						<dateFormatItem id="d">d</dateFormatItem>
						<dateFormatItem id="Ed">E, d</dateFormatItem>
						<dateFormatItem id="Gy">y G</dateFormatItem>
						<dateFormatItem id="GyMMM">MMM y G</dateFormatItem>
						<dateFormatItem id="GyMMMd">d MMM y G</dateFormatItem>
						<dateFormatItem id="GyMMMEd">E, d MMM y G</dateFormatItem>
						<dateFormatItem id="h">h a</dateFormatItem>
						<dateFormatItem id="H">HH</dateFormatItem>
						<dateFormatItem id="hm">h.mm a</dateFormatItem>
						<dateFormatItem id="Hm">HH.mm</dateFormatItem>
						<dateFormatItem id="hms">h.mm.ss a</dateFormatItem>
						<dateFormatItem id="Hms">HH.mm.ss</dateFormatItem>
						<dateFormatItem id="M">L</dateFormatItem>
						<dateFormatItem id="Md">d/M</dateFormatItem>
						<dateFormatItem id="MEd">E, d/M</dateFormatItem>
						<dateFormatItem id="MMM">LLL</dateFormatItem>
						<dateFormatItem id="MMMd">d MMM</dateFormatItem>
						<dateFormatItem id="MMMEd">E, d MMM</dateFormatItem>
						<dateFormatItem id="MMMMd">d MMMM</dateFormatItem>
						<dateFormatItem id="MMMMEd">E, d MMMM</dateFormatItem>
						<dateFormatItem id="ms">mm.ss</dateFormatItem>
						<dateFormatItem id="y">y G</dateFormatItem>
						<dateFormatItem id="yyyy">y G</dateFormatItem>
						<dateFormatItem id="yyyyM">M/y G</dateFormatItem>
						<dateFormatItem id="yyyyMd">d/M/y G</dateFormatItem>
						<dateFormatItem id="yyyyMEd">E, d/M/y G</dateFormatItem>
						<dateFormatItem id="yyyyMMM">MMM y G</dateFormatItem>
						<dateFormatItem id="yyyyMMMd">d MMM y G</dateFormatItem>
						<dateFormatItem id="yyyyMMMEd">E, d MMM y G</dateFormatItem>
						<dateFormatItem id="yyyyQQQ">QQQ y G</dateFormatItem>
						<dateFormatItem id="yyyyQQQQ">QQQQ y G</dateFormatItem>
					</availableFormats>
					<intervalFormats>
						<intervalFormatFallback>{0} – {1}</intervalFormatFallback>
						<intervalFormatItem id="d">
							<greatestDifference id="d">d–d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="h">
							<greatestDifference id="a">h a – h a</greatestDifference>
							<greatestDifference id="h">h–h a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="H">
							<greatestDifference id="H">HH–HH</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hm">
							<greatestDifference id="a">h.mm a – h.mm a</greatestDifference>
							<greatestDifference id="h">h.mm–h.mm a</greatestDifference>
							<greatestDifference id="m">h.mm–h.mm a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hm">
							<greatestDifference id="H">HH.mm–HH.mm</greatestDifference>
							<greatestDifference id="m">HH.mm–HH.mm</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hmv">
							<greatestDifference id="a">h.mm a – h.mm a v</greatestDifference>
							<greatestDifference id="h">h.mm–h.mm a v</greatestDifference>
							<greatestDifference id="m">h.mm–h.mm a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hmv">
							<greatestDifference id="H">HH.mm–HH.mm v</greatestDifference>
							<greatestDifference id="m">HH.mm–HH.mm v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hv">
							<greatestDifference id="a">h a – h a v</greatestDifference>
							<greatestDifference id="h">h–h a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hv">
							<greatestDifference id="H">HH–HH v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="M">
							<greatestDifference id="M">M–M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Md">
							<greatestDifference id="d">d/M – d/M</greatestDifference>
							<greatestDifference id="M">d/M – d/M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MEd">
							<greatestDifference id="d">E, d/M – E, d/M</greatestDifference>
							<greatestDifference id="M">E, d/M – E, d/M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMM">
							<greatestDifference id="M">MMM–MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMd">
							<greatestDifference id="d">d–d MMM</greatestDifference>
							<greatestDifference id="M">d MMM – d MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMEd">
							<greatestDifference id="d">E, d MMM – E, d MMM</greatestDifference>
							<greatestDifference id="M">E, d MMM – E, d MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="y">
							<greatestDifference id="y">y-y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yM">
							<greatestDifference id="M">M/y – M/y GGGGG</greatestDifference>
							<greatestDifference id="y">M/y – M/y GGGGG</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMd">
							<greatestDifference id="d">d/M/y – d/M/y GGGGG</greatestDifference>
							<greatestDifference id="M">d/M/y – d/M/y GGGGG</greatestDifference>
							<greatestDifference id="y">d/M/y – d/M/y GGGGG</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMEd">
							<greatestDifference id="d">E, d/M/y – E, d/M/y GGGGG</greatestDifference>
							<greatestDifference id="M">E, d/M/y – E, d/M/y GGGGG</greatestDifference>
							<greatestDifference id="y">E, d/M/y – E, d/M/y GGGGG</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMM">
							<greatestDifference id="M">MMM–MMM y G</greatestDifference>
							<greatestDifference id="y">MMM y – MMM y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMd">
							<greatestDifference id="d">d-d MMM y G</greatestDifference>
							<greatestDifference id="M">d MMM – d MMM y G</greatestDifference>
							<greatestDifference id="y">d MMM y – d MMM y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMEd">
							<greatestDifference id="d">E, d MMM – E, d MMM y G</greatestDifference>
							<greatestDifference id="M">E, d MMM – E, d MMM y G</greatestDifference>
							<greatestDifference id="y">E, d MMM y – E, d MMM y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMM">
							<greatestDifference id="M">MMMM – MMMM y G</greatestDifference>
							<greatestDifference id="y">MMMM y – MMMM y G</greatestDifference>
						</intervalFormatItem>
					</intervalFormats>
				</dateTimeFormats>
			</calendar>
			<calendar type="gregorian">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<month type="1">Jan</month>
							<month type="2">Feb</month>
							<month type="3">Mar</month>
							<month type="4">Apr</month>
							<month type="5">Mei</month>
							<month type="6">Jun</month>
							<month type="7">Jul</month>
							<month type="8">Agt</month>
							<month type="9">Sep</month>
							<month type="10">Okt</month>
							<month type="11">Nov</month>
							<month type="12">Des</month>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">J</month>
							<month type="2">F</month>
							<month type="3">M</month>
							<month type="4">A</month>
							<month type="5">M</month>
							<month type="6">J</month>
							<month type="7">J</month>
							<month type="8">A</month>
							<month type="9">S</month>
							<month type="10">O</month>
							<month type="11">N</month>
							<month type="12">D</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Januari</month>
							<month type="2">Februari</month>
							<month type="3">Maret</month>
							<month type="4">April</month>
							<month type="5">Mei</month>
							<month type="6">Juni</month>
							<month type="7">Juli</month>
							<month type="8">Agustus</month>
							<month type="9">September</month>
							<month type="10">Oktober</month>
							<month type="11">November</month>
							<month type="12">Desember</month>
						</monthWidth>
					</monthContext>
					<monthContext type="stand-alone">
						<monthWidth type="abbreviated">
							<month type="1">Jan</month>
							<month type="2">Feb</month>
							<month type="3">Mar</month>
							<month type="4">Apr</month>
							<month type="5">Mei</month>
							<month type="6">Jun</month>
							<month type="7">Jul</month>
							<month type="8">Agt</month>
							<month type="9">Sep</month>
							<month type="10">Okt</month>
							<month type="11">Nov</month>
							<month type="12">Des</month>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">J</month>
							<month type="2">F</month>
							<month type="3">M</month>
							<month type="4">A</month>
							<month type="5">M</month>
							<month type="6">J</month>
							<month type="7">J</month>
							<month type="8">A</month>
							<month type="9">S</month>
							<month type="10">O</month>
							<month type="11">N</month>
							<month type="12">D</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Januari</month>
							<month type="2">Februari</month>
							<month type="3">Maret</month>
							<month type="4">April</month>
							<month type="5">Mei</month>
							<month type="6">Juni</month>
							<month type="7">Juli</month>
							<month type="8">Agustus</month>
							<month type="9">September</month>
							<month type="10">Oktober</month>
							<month type="11">November</month>
							<month type="12">Desember</month>
						</monthWidth>
					</monthContext>
				</months>
				<days>
					<dayContext type="format">
						<dayWidth type="abbreviated">
							<day type="sun">Min</day>
							<day type="mon">Sen</day>
							<day type="tue">Sel</day>
							<day type="wed">Rab</day>
							<day type="thu">Kam</day>
							<day type="fri">Jum</day>
							<day type="sat">Sab</day>
						</dayWidth>
						<dayWidth type="narrow">
							<day type="sun">M</day>
							<day type="mon">S</day>
							<day type="tue">S</day>
							<day type="wed">R</day>
							<day type="thu">K</day>
							<day type="fri">J</day>
							<day type="sat">S</day>
						</dayWidth>
						<dayWidth type="short">
							<day type="sun">Min</day>
							<day type="mon">Sen</day>
							<day type="tue">Sel</day>
							<day type="wed">Rab</day>
							<day type="thu">Kam</day>
							<day type="fri">Jum</day>
							<day type="sat">Sab</day>
						</dayWidth>
						<dayWidth type="wide">
							<day type="sun">Minggu</day>
							<day type="mon">Senin</day>
							<day type="tue">Selasa</day>
							<day type="wed">Rabu</day>
							<day type="thu">Kamis</day>
							<day type="fri">Jumat</day>
							<day type="sat">Sabtu</day>
						</dayWidth>
					</dayContext>
					<dayContext type="stand-alone">
						<dayWidth type="abbreviated">
							<day type="sun">Min</day>
							<day type="mon">Sen</day>
							<day type="tue">Sel</day>
							<day type="wed">Rab</day>
							<day type="thu">Kam</day>
							<day type="fri">Jum</day>
							<day type="sat">Sab</day>
						</dayWidth>
						<dayWidth type="narrow">
							<day type="sun">M</day>
							<day type="mon">S</day>
							<day type="tue">S</day>
							<day type="wed">R</day>
							<day type="thu">K</day>
							<day type="fri">J</day>
							<day type="sat">S</day>
						</dayWidth>
						<dayWidth type="short">
							<day type="sun">Min</day>
							<day type="mon">Sen</day>
							<day type="tue">Sel</day>
							<day type="wed">Rab</day>
							<day type="thu">Kam</day>
							<day type="fri">Jum</day>
							<day type="sat">Sab</day>
						</dayWidth>
						<dayWidth type="wide">
							<day type="sun">Minggu</day>
							<day type="mon">Senin</day>
							<day type="tue">Selasa</day>
							<day type="wed">Rabu</day>
							<day type="thu">Kamis</day>
							<day type="fri">Jumat</day>
							<day type="sat">Sabtu</day>
						</dayWidth>
					</dayContext>
				</days>
				<quarters>
					<quarterContext type="format">
						<quarterWidth type="abbreviated">
							<quarter type="1">K1</quarter>
							<quarter type="2">K2</quarter>
							<quarter type="3">K3</quarter>
							<quarter type="4">K4</quarter>
						</quarterWidth>
						<quarterWidth type="narrow">
							<quarter type="1">1</quarter>
							<quarter type="2">2</quarter>
							<quarter type="3">3</quarter>
							<quarter type="4">4</quarter>
						</quarterWidth>
						<quarterWidth type="wide">
							<quarter type="1">Kuartal ke-1</quarter>
							<quarter type="2">Kuartal ke-2</quarter>
							<quarter type="3">Kuartal ke-3</quarter>
							<quarter type="4">Kuartal ke-4</quarter>
						</quarterWidth>
					</quarterContext>
					<quarterContext type="stand-alone">
						<quarterWidth type="abbreviated">
							<quarter type="1">K1</quarter>
							<quarter type="2">K2</quarter>
							<quarter type="3">K3</quarter>
							<quarter type="4">K4</quarter>
						</quarterWidth>
						<quarterWidth type="narrow">
							<quarter type="1">1</quarter>
							<quarter type="2">2</quarter>
							<quarter type="3">3</quarter>
							<quarter type="4">4</quarter>
						</quarterWidth>
						<quarterWidth type="wide">
							<quarter type="1">Kuartal ke-1</quarter>
							<quarter type="2">Kuartal ke-2</quarter>
							<quarter type="3">Kuartal ke-3</quarter>
							<quarter type="4">Kuartal ke-4</quarter>
						</quarterWidth>
					</quarterContext>
				</quarters>
				<dayPeriods>
					<dayPeriodContext type="format">
						<dayPeriodWidth type="narrow">
							<dayPeriod type="am">AM</dayPeriod>
							<dayPeriod type="pm">PM</dayPeriod>
						</dayPeriodWidth>
						<dayPeriodWidth type="wide">
							<dayPeriod type="am">AM</dayPeriod>
							<dayPeriod type="pm">PM</dayPeriod>
						</dayPeriodWidth>
					</dayPeriodContext>
				</dayPeriods>
				<eras>
					<eraNames>
						<era type="0">SM</era>
						<era type="1">M</era>
					</eraNames>
					<eraAbbr>
						<era type="0">SM</era>
						<era type="0" alt="variant">SEU</era>
						<era type="1">M</era>
						<era type="1" alt="variant">EU</era>
					</eraAbbr>
					<eraNarrow>
						<era type="0">SM</era>
						<era type="1">M</era>
					</eraNarrow>
				</eras>
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>EEEE, dd MMMM y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>d MMMM y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>d MMM y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>dd/MM/yy</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<timeFormats>
					<timeFormatLength type="full">
						<timeFormat>
							<pattern>HH.mm.ss zzzz</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="long">
						<timeFormat>
							<pattern>HH.mm.ss z</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="medium">
						<timeFormat>
							<pattern>HH.mm.ss</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="short">
						<timeFormat>
							<pattern>HH.mm</pattern>
						</timeFormat>
					</timeFormatLength>
				</timeFormats>
				<dateTimeFormats>
					<dateTimeFormatLength type="full">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="long">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="medium">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="short">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<availableFormats>
						<dateFormatItem id="d">d</dateFormatItem>
						<dateFormatItem id="Ed">E, d</dateFormatItem>
						<dateFormatItem id="Ehm">E h.mm a</dateFormatItem>
						<dateFormatItem id="EHm">E HH.mm</dateFormatItem>
						<dateFormatItem id="Ehms">E h.mm.ss a</dateFormatItem>
						<dateFormatItem id="EHms">E HH.mm.ss</dateFormatItem>
						<dateFormatItem id="Gy">y G</dateFormatItem>
						<dateFormatItem id="GyMMM">MMM y G</dateFormatItem>
						<dateFormatItem id="GyMMMd">d MMM y G</dateFormatItem>
						<dateFormatItem id="GyMMMEd">E, d MMM y G</dateFormatItem>
						<dateFormatItem id="h">h a</dateFormatItem>
						<dateFormatItem id="H">HH</dateFormatItem>
						<dateFormatItem id="hm">h.mm a</dateFormatItem>
						<dateFormatItem id="Hm">HH.mm</dateFormatItem>
						<dateFormatItem id="hms">h.mm.ss a</dateFormatItem>
						<dateFormatItem id="Hms">HH.mm.ss</dateFormatItem>
						<dateFormatItem id="M">L</dateFormatItem>
						<dateFormatItem id="Md">d/M</dateFormatItem>
						<dateFormatItem id="MEd">E, d/M</dateFormatItem>
						<dateFormatItem id="MMM">LLL</dateFormatItem>
						<dateFormatItem id="MMMd">d MMM</dateFormatItem>
						<dateFormatItem id="MMMEd">E, d MMM</dateFormatItem>
						<dateFormatItem id="MMMMd">d MMMM</dateFormatItem>
						<dateFormatItem id="MMMMEd">E, d MMMM</dateFormatItem>
						<dateFormatItem id="ms">mm.ss</dateFormatItem>
						<dateFormatItem id="y">y</dateFormatItem>
						<dateFormatItem id="yM">M/y</dateFormatItem>
						<dateFormatItem id="yMd">d/M/y</dateFormatItem>
						<dateFormatItem id="yMEd">E, d/M/y</dateFormatItem>
						<dateFormatItem id="yMMM">MMM y</dateFormatItem>
						<dateFormatItem id="yMMMd">d MMM y</dateFormatItem>
						<dateFormatItem id="yMMMEd">E, d MMM y</dateFormatItem>
						<dateFormatItem id="yQQQ">QQQ y</dateFormatItem>
						<dateFormatItem id="yQQQQ">QQQQ y</dateFormatItem>
					</availableFormats>
					<appendItems>
						<appendItem request="Timezone">{0} {1}</appendItem>
					</appendItems>
					<intervalFormats>
						<intervalFormatFallback>{0} – {1}</intervalFormatFallback>
						<intervalFormatItem id="d">
							<greatestDifference id="d">d–d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="h">
							<greatestDifference id="a">h a – h a</greatestDifference>
							<greatestDifference id="h">h–h a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="H">
							<greatestDifference id="H">HH–HH</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hm">
							<greatestDifference id="a">h.mm a – h.mm a</greatestDifference>
							<greatestDifference id="h">h.mm–h.mm a</greatestDifference>
							<greatestDifference id="m">h.mm–h.mm a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hm">
							<greatestDifference id="H">HH.mm–HH.mm</greatestDifference>
							<greatestDifference id="m">HH.mm–HH.mm</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hmv">
							<greatestDifference id="a">h.mm a – h.mm a v</greatestDifference>
							<greatestDifference id="h">h.mm–h.mm a v</greatestDifference>
							<greatestDifference id="m">h.mm–h.mm a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hmv">
							<greatestDifference id="H">HH.mm–HH.mm v</greatestDifference>
							<greatestDifference id="m">HH.mm–HH.mm v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hv">
							<greatestDifference id="a">h a – h a v</greatestDifference>
							<greatestDifference id="h">h–h a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hv">
							<greatestDifference id="H">HH–HH v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="M">
							<greatestDifference id="M">M–M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Md">
							<greatestDifference id="d">d/M – d/M</greatestDifference>
							<greatestDifference id="M">d/M – d/M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MEd">
							<greatestDifference id="d">E, d/M – E, d/M</greatestDifference>
							<greatestDifference id="M">E, d/M – E, d/M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMM">
							<greatestDifference id="M">MMM–MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMd">
							<greatestDifference id="d">d–d MMM</greatestDifference>
							<greatestDifference id="M">d MMM – d MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMEd">
							<greatestDifference id="d">E, d MMM – E, d MMM</greatestDifference>
							<greatestDifference id="M">E, d MMM – E, d MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="y">
							<greatestDifference id="y">y-y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yM">
							<greatestDifference id="M">M/y – M/y</greatestDifference>
							<greatestDifference id="y">M/y – M/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMd">
							<greatestDifference id="d">d/M/y – d/M/y</greatestDifference>
							<greatestDifference id="M">d/M/y – d/M/y</greatestDifference>
							<greatestDifference id="y">d/M/y – d/M/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMEd">
							<greatestDifference id="d">E, d/M/y – E, d/M/y</greatestDifference>
							<greatestDifference id="M">E, d/M/y – E, d/M/y</greatestDifference>
							<greatestDifference id="y">E, d/M/y – E, d/M/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMM">
							<greatestDifference id="M">MMM–MMM y</greatestDifference>
							<greatestDifference id="y">MMM y – MMM y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMd">
							<greatestDifference id="d">d-d MMM y</greatestDifference>
							<greatestDifference id="M">d MMM – d MMM y</greatestDifference>
							<greatestDifference id="y">d MMM y – d MMM y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMEd">
							<greatestDifference id="d">E, d MMM – E, d MMM y</greatestDifference>
							<greatestDifference id="M">E, d MMM – E, d MMM y</greatestDifference>
							<greatestDifference id="y">E, d MMM y – E, d MMM y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMM">
							<greatestDifference id="M">MMMM–MMMM y</greatestDifference>
							<greatestDifference id="y">MMMM y – MMMM y</greatestDifference>
						</intervalFormatItem>
					</intervalFormats>
				</dateTimeFormats>
			</calendar>
			<calendar type="hebrew">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<month type="1">Tishri</month>
							<month type="2">Heshvan</month>
							<month type="3">Kislev</month>
							<month type="4">Tevet</month>
							<month type="5">Shevat</month>
							<month type="6">Adar I</month>
							<month type="7">Adar</month>
							<month type="7" yeartype="leap">Adar II</month>
							<month type="8">Nisan</month>
							<month type="9">Iyar</month>
							<month type="10">Sivan</month>
							<month type="11">Tamuz</month>
							<month type="12">Av</month>
							<month type="13">Elul</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Tishri</month>
							<month type="2">Heshvan</month>
							<month type="3">Kislev</month>
							<month type="4">Tevet</month>
							<month type="5">Shevat</month>
							<month type="6">Adar I</month>
							<month type="7">Adar</month>
							<month type="7" yeartype="leap">Adar II</month>
							<month type="8">Nisan</month>
							<month type="9">Iyar</month>
							<month type="10">Sivan</month>
							<month type="11">Tamuz</month>
							<month type="12">Av</month>
							<month type="13">Elul</month>
						</monthWidth>
					</monthContext>
					<monthContext type="stand-alone">
						<monthWidth type="abbreviated">
							<month type="1">Tishri</month>
							<month type="2">Heshvan</month>
							<month type="3">Kislev</month>
							<month type="4">Tevet</month>
							<month type="5">Shevat</month>
							<month type="6">Adar I</month>
							<month type="7">Adar</month>
							<month type="7" yeartype="leap">Adar II</month>
							<month type="8">Nisan</month>
							<month type="9">Iyar</month>
							<month type="10">Sivan</month>
							<month type="11">Tamuz</month>
							<month type="12">Av</month>
							<month type="13">Elul</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Tishri</month>
							<month type="2">Heshvan</month>
							<month type="3">Kislev</month>
							<month type="4">Tevet</month>
							<month type="5">Shevat</month>
							<month type="6">Adar I</month>
							<month type="7">Adar</month>
							<month type="7" yeartype="leap">Adar II</month>
							<month type="8">Nisan</month>
							<month type="9">Iyar</month>
							<month type="10">Sivan</month>
							<month type="11">Tamuz</month>
							<month type="12">Av</month>
							<month type="13">Elul</month>
						</monthWidth>
					</monthContext>
				</months>
				<eras>
					<eraAbbr>
						<era type="0">AM</era>
					</eraAbbr>
				</eras>
			</calendar>
			<calendar type="indian">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<month type="1">Chaitra</month>
							<month type="2">Vaisakha</month>
							<month type="3">Jyaistha</month>
							<month type="4">Asadha</month>
							<month type="5">Sravana</month>
							<month type="6">Bhadra</month>
							<month type="7">Asvina</month>
							<month type="8">Kartika</month>
							<month type="9">Agrahayana</month>
							<month type="10">Pausa</month>
							<month type="11">Magha</month>
							<month type="12">Phalguna</month>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Chaitra</month>
							<month type="2">Vaisakha</month>
							<month type="3">Jyaistha</month>
							<month type="4">Asadha</month>
							<month type="5">Sravana</month>
							<month type="6">Bhadra</month>
							<month type="7">Asvina</month>
							<month type="8">Kartika</month>
							<month type="9">Agrahayana</month>
							<month type="10">Pausa</month>
							<month type="11">Magha</month>
							<month type="12">Phalguna</month>
						</monthWidth>
					</monthContext>
					<monthContext type="stand-alone">
						<monthWidth type="abbreviated">
							<month type="1">Chaitra</month>
							<month type="2">Vaisakha</month>
							<month type="3">Jyaistha</month>
							<month type="4">Asadha</month>
							<month type="5">Sravana</month>
							<month type="6">Bhadra</month>
							<month type="7">Asvina</month>
							<month type="8">Kartika</month>
							<month type="9">Agrahayana</month>
							<month type="10">Pausa</month>
							<month type="11">Magha</month>
							<month type="12">Phalguna</month>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Chaitra</month>
							<month type="2">Vaisakha</month>
							<month type="3">Jyaistha</month>
							<month type="4">Asadha</month>
							<month type="5">Sravana</month>
							<month type="6">Bhadra</month>
							<month type="7">Asvina</month>
							<month type="8">Kartika</month>
							<month type="9">Agrahayana</month>
							<month type="10">Pausa</month>
							<month type="11">Magha</month>
							<month type="12">Phalguna</month>
						</monthWidth>
					</monthContext>
				</months>
				<eras>
					<eraAbbr>
						<era type="0">SAKA</era>
					</eraAbbr>
				</eras>
			</calendar>
			<calendar type="islamic">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<month type="1">Muh.</month>
							<month type="2">Saf.</month>
							<month type="3">Rab. I</month>
							<month type="4">Rab. II</month>
							<month type="5">Jum. I</month>
							<month type="6">Jum. II</month>
							<month type="7">Raj.</month>
							<month type="8">Sha.</month>
							<month type="9">Ram.</month>
							<month type="10">Syaw.</month>
							<month type="11">Dhuʻl-Q.</month>
							<month type="12">Dhuʻl-H.</month>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Muharram</month>
							<month type="2">Safar</month>
							<month type="3">Rabiʻ I</month>
							<month type="4">Rabiʻ II</month>
							<month type="5">Jumada I</month>
							<month type="6">Jumada II</month>
							<month type="7">Rajab</month>
							<month type="8">Sya'ban</month>
							<month type="9">Ramadhan</month>
							<month type="10">Syawal</month>
							<month type="11">Dhuʻl-Qiʻdah</month>
							<month type="12">Dhuʻl-Hijjah</month>
						</monthWidth>
					</monthContext>
					<monthContext type="stand-alone">
						<monthWidth type="abbreviated">
							<month type="1">Muh.</month>
							<month type="2">Saf.</month>
							<month type="3">Rab. I</month>
							<month type="4">Rab. II</month>
							<month type="5">Jum. I</month>
							<month type="6">Jum. II</month>
							<month type="7">Raj.</month>
							<month type="8">Sha.</month>
							<month type="9">Ram.</month>
							<month type="10">Syaw.</month>
							<month type="11">Dhuʻl-Q.</month>
							<month type="12">Dhuʻl-H.</month>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Muharram</month>
							<month type="2">Safar</month>
							<month type="3">Rabiʻ I</month>
							<month type="4">Rabiʻ II</month>
							<month type="5">Jumada I</month>
							<month type="6">Jumada II</month>
							<month type="7">Rajab</month>
							<month type="8">Sya'ban</month>
							<month type="9">Ramadhan</month>
							<month type="10">Syawal</month>
							<month type="11">Dhuʻl-Qiʻdah</month>
							<month type="12">Dhuʻl-Hijjah</month>
						</monthWidth>
					</monthContext>
				</months>
				<eras>
					<eraAbbr>
						<era type="0">AH</era>
					</eraAbbr>
				</eras>
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>EEEE, dd MMMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>d MMMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>d MMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>d/M/y GGGGG</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<dateTimeFormats>
					<availableFormats>
						<dateFormatItem id="d">d</dateFormatItem>
						<dateFormatItem id="M">L</dateFormatItem>
						<dateFormatItem id="Md">d/M</dateFormatItem>
						<dateFormatItem id="MEd">E, d/M</dateFormatItem>
						<dateFormatItem id="MMM">LLL</dateFormatItem>
						<dateFormatItem id="MMMd">d MMM</dateFormatItem>
						<dateFormatItem id="MMMEd">E, d MMM</dateFormatItem>
						<dateFormatItem id="MMMMd">d MMMM</dateFormatItem>
						<dateFormatItem id="MMMMEd">E, d MMMM</dateFormatItem>
						<dateFormatItem id="y">y G</dateFormatItem>
					</availableFormats>
				</dateTimeFormats>
			</calendar>
			<calendar type="japanese">
				<eras>
					<eraAbbr>
						<era type="0">Taika (645-650)</era>
						<era type="1">Hakuchi (650-671)</era>
						<era type="2">Hakuhō (672-686)</era>
						<era type="3">Shuchō (686-701)</era>
						<era type="4">Taihō (701-704)</era>
						<era type="5">Keiun (704-708)</era>
						<era type="6">Wadō (708-715)</era>
						<era type="7">Reiki (715-717)</era>
						<era type="8">Yōrō (717-724)</era>
						<era type="9">Jinki (724-729)</era>
						<era type="10">Tempyō (729-749)</era>
						<era type="11">Tempyō-kampō (749-749)</era>
						<era type="12">Tempyō-shōhō (749-757)</era>
						<era type="13">Tempyō-hōji (757-765)</era>
						<era type="14">Temphō-jingo (765-767)</era>
						<era type="15">Jingo-keiun (767-770)</era>
						<era type="16">Hōki (770-780)</era>
						<era type="17">Ten-ō (781-782)</era>
						<era type="18">Enryaku (782-806)</era>
						<era type="19">Daidō (806-810)</era>
						<era type="20">Kōnin (810-824)</era>
						<era type="21">Tenchō (824-834)</era>
						<era type="22">Jōwa (834-848)</era>
						<era type="23">Kajō (848-851)</era>
						<era type="24">Ninju (851-854)</era>
						<era type="25">Saiko (854-857)</era>
						<era type="26">Tennan (857-859)</era>
						<era type="27">Jōgan (859-877)</era>
						<era type="28">Genkei (877-885)</era>
						<era type="29">Ninna (885-889)</era>
						<era type="30">Kampyō (889-898)</era>
						<era type="31">Shōtai (898-901)</era>
						<era type="32">Engi (901-923)</era>
						<era type="33">Enchō (923-931)</era>
						<era type="34">Shōhei (931-938)</era>
						<era type="35">Tengyō (938-947)</era>
						<era type="36">Tenryaku (947-957)</era>
						<era type="37">Tentoku (957-961)</era>
						<era type="38">Ōwa (961-964)</era>
						<era type="39">Kōhō (964-968)</era>
						<era type="40">Anna (968-970)</era>
						<era type="41">Tenroku (970-973)</era>
						<era type="42">Ten-en (973-976)</era>
						<era type="43">Jōgen (976-978)</era>
						<era type="44">Tengen (978-983)</era>
						<era type="45">Eikan (983-985)</era>
						<era type="46">Kanna (985-987)</era>
						<era type="47">Ei-en (987-989)</era>
						<era type="48">Eiso (989-990)</era>
						<era type="49">Shōryaku (990-995)</era>
						<era type="50">Chōtoku (995-999)</era>
						<era type="51">Chōhō (999-1004)</era>
						<era type="52">Kankō (1004-1012)</era>
						<era type="53">Chōwa (1012-1017)</era>
						<era type="54">Kannin (1017-1021)</era>
						<era type="55">Jian (1021-1024)</era>
						<era type="56">Manju (1024-1028)</era>
						<era type="57">Chōgen (1028-1037)</era>
						<era type="58">Chōryaku (1037-1040)</era>
						<era type="59">Chōkyū (1040-1044)</era>
						<era type="60">Kantoku (1044-1046)</era>
						<era type="61">Eishō (1046-1053)</era>
						<era type="62">Tengi (1053-1058)</era>
						<era type="63">Kōhei (1058-1065)</era>
						<era type="64">Jiryaku (1065-1069)</era>
						<era type="65">Enkyū (1069-1074)</era>
						<era type="66">Shōho (1074-1077)</era>
						<era type="67">Shōryaku (1077-1081)</era>
						<era type="68">Eiho (1081-1084)</era>
						<era type="69">Ōtoku (1084-1087)</era>
						<era type="70">Kanji (1087-1094)</era>
						<era type="71">Kaho (1094-1096)</era>
						<era type="72">Eichō (1096-1097)</era>
						<era type="73">Shōtoku (1097-1099)</era>
						<era type="74">Kōwa (1099-1104)</era>
						<era type="75">Chōji (1104-1106)</era>
						<era type="76">Kashō (1106-1108)</era>
						<era type="77">Tennin (1108-1110)</era>
						<era type="78">Ten-ei (1110-1113)</era>
						<era type="79">Eikyū (1113-1118)</era>
						<era type="80">Gen-ei (1118-1120)</era>
						<era type="81">Hoan (1120-1124)</era>
						<era type="82">Tenji (1124-1126)</era>
						<era type="83">Daiji (1126-1131)</era>
						<era type="84">Tenshō (1131-1132)</era>
						<era type="85">Chōshō (1132-1135)</era>
						<era type="86">Hoen (1135-1141)</era>
						<era type="87">Eiji (1141-1142)</era>
						<era type="88">Kōji (1142-1144)</era>
						<era type="89">Tenyō (1144-1145)</era>
						<era type="90">Kyūan (1145-1151)</era>
						<era type="91">Ninpei (1151-1154)</era>
						<era type="92">Kyūju (1154-1156)</era>
						<era type="93">Hogen (1156-1159)</era>
						<era type="94">Heiji (1159-1160)</era>
						<era type="95">Eiryaku (1160-1161)</era>
						<era type="96">Ōho (1161-1163)</era>
						<era type="97">Chōkan (1163-1165)</era>
						<era type="98">Eiman (1165-1166)</era>
						<era type="99">Nin-an (1166-1169)</era>
						<era type="100">Kaō (1169-1171)</era>
						<era type="101">Shōan (1171-1175)</era>
						<era type="102">Angen (1175-1177)</era>
						<era type="103">Jishō (1177-1181)</era>
						<era type="104">Yōwa (1181-1182)</era>
						<era type="105">Juei (1182-1184)</era>
						<era type="106">Genryuku (1184-1185)</era>
						<era type="107">Bunji (1185-1190)</era>
						<era type="108">Kenkyū (1190-1199)</era>
						<era type="109">Shōji (1199-1201)</era>
						<era type="110">Kennin (1201-1204)</era>
						<era type="111">Genkyū (1204-1206)</era>
						<era type="112">Ken-ei (1206-1207)</era>
						<era type="113">Shōgen (1207-1211)</era>
						<era type="114">Kenryaku (1211-1213)</era>
						<era type="115">Kenpō (1213-1219)</era>
						<era type="116">Shōkyū (1219-1222)</era>
						<era type="117">Jōō (1222-1224)</era>
						<era type="118">Gennin (1224-1225)</era>
						<era type="119">Karoku (1225-1227)</era>
						<era type="120">Antei (1227-1229)</era>
						<era type="121">Kanki (1229-1232)</era>
						<era type="122">Jōei (1232-1233)</era>
						<era type="123">Tempuku (1233-1234)</era>
						<era type="124">Bunryaku (1234-1235)</era>
						<era type="125">Katei (1235-1238)</era>
						<era type="126">Ryakunin (1238-1239)</era>
						<era type="127">En-ō (1239-1240)</era>
						<era type="128">Ninji (1240-1243)</era>
						<era type="129">Kangen (1243-1247)</era>
						<era type="130">Hōji (1247-1249)</era>
						<era type="131">Kenchō (1249-1256)</era>
						<era type="132">Kōgen (1256-1257)</era>
						<era type="133">Shōka (1257-1259)</era>
						<era type="134">Shōgen (1259-1260)</era>
						<era type="135">Bun-ō (1260-1261)</era>
						<era type="136">Kōchō (1261-1264)</era>
						<era type="137">Bun-ei (1264-1275)</era>
						<era type="138">Kenji (1275-1278)</era>
						<era type="139">Kōan (1278-1288)</era>
						<era type="140">Shōō (1288-1293)</era>
						<era type="141">Einin (1293-1299)</era>
						<era type="142">Shōan (1299-1302)</era>
						<era type="143">Kengen (1302-1303)</era>
						<era type="144">Kagen (1303-1306)</era>
						<era type="145">Tokuji (1306-1308)</era>
						<era type="146">Enkei (1308-1311)</era>
						<era type="147">Ōchō (1311-1312)</era>
						<era type="148">Shōwa (1312-1317)</era>
						<era type="149">Bunpō (1317-1319)</era>
						<era type="150">Genō (1319-1321)</era>
						<era type="151">Genkyō (1321-1324)</era>
						<era type="152">Shōchū (1324-1326)</era>
						<era type="153">Kareki (1326-1329)</era>
						<era type="154">Gentoku (1329-1331)</era>
						<era type="155">Genkō (1331-1334)</era>
						<era type="156">Kemmu (1334-1336)</era>
						<era type="157">Engen (1336-1340)</era>
						<era type="158">Kōkoku (1340-1346)</era>
						<era type="159">Shōhei (1346-1370)</era>
						<era type="160">Kentoku (1370-1372)</era>
						<era type="161">Bunchũ (1372-1375)</era>
						<era type="162">Tenju (1375-1379)</era>
						<era type="163">Kōryaku (1379-1381)</era>
						<era type="164">Kōwa (1381-1384)</era>
						<era type="165">Genchũ (1384-1392)</era>
						<era type="166">Meitoku (1384-1387)</era>
						<era type="167">Kakei (1387-1389)</era>
						<era type="168">Kōō (1389-1390)</era>
						<era type="169">Meitoku (1390-1394)</era>
						<era type="170">Ōei (1394-1428)</era>
						<era type="171">Shōchō (1428-1429)</era>
						<era type="172">Eikyō (1429-1441)</era>
						<era type="173">Kakitsu (1441-1444)</era>
						<era type="174">Bun-an (1444-1449)</era>
						<era type="176">Kyōtoku (1452-1455)</era>
						<era type="177">Kōshō (1455-1457)</era>
						<era type="178">Chōroku (1457-1460)</era>
						<era type="179">Kanshō (1460-1466)</era>
						<era type="180">Bunshō (1466-1467)</era>
						<era type="181">Ōnin (1467-1469)</era>
						<era type="182">Bunmei (1469-1487)</era>
						<era type="183">Chōkyō (1487-1489)</era>
						<era type="184">Entoku (1489-1492)</era>
						<era type="185">Meiō (1492-1501)</era>
						<era type="186">Bunki (1501-1504)</era>
						<era type="187">Eishō (1504-1521)</era>
						<era type="188">Taiei (1521-1528)</era>
						<era type="189">Kyōroku (1528-1532)</era>
						<era type="190">Tenmon (1532-1555)</era>
						<era type="191">Kōji (1555-1558)</era>
						<era type="192">Eiroku (1558-1570)</era>
						<era type="193">Genki (1570-1573)</era>
						<era type="194">Tenshō (1573-1592)</era>
						<era type="195">Bunroku (1592-1596)</era>
						<era type="196">Keichō (1596-1615)</era>
						<era type="197">Genwa (1615-1624)</era>
						<era type="198">Kan-ei (1624-1644)</era>
						<era type="199">Shōho (1644-1648)</era>
						<era type="200">Keian (1648-1652)</era>
						<era type="201">Shōō (1652-1655)</era>
						<era type="202">Meiryaku (1655-1658)</era>
						<era type="203">Manji (1658-1661)</era>
						<era type="204">Kanbun (1661-1673)</era>
						<era type="205">Enpō (1673-1681)</era>
						<era type="206">Tenwa (1681-1684)</era>
						<era type="207">Jōkyō (1684-1688)</era>
						<era type="208">Genroku (1688-1704)</era>
						<era type="209">Hōei (1704-1711)</era>
						<era type="210">Shōtoku (1711-1716)</era>
						<era type="211">Kyōhō (1716-1736)</era>
						<era type="212">Genbun (1736-1741)</era>
						<era type="213">Kanpō (1741-1744)</era>
						<era type="214">Enkyō (1744-1748)</era>
						<era type="215">Kan-en (1748-1751)</era>
						<era type="216">Hōryaku (1751-1764)</era>
						<era type="217">Meiwa (1764-1772)</era>
						<era type="218">An-ei (1772-1781)</era>
						<era type="219">Tenmei (1781-1789)</era>
						<era type="220">Kansei (1789-1801)</era>
						<era type="221">Kyōwa (1801-1804)</era>
						<era type="222">Bunka (1804-1818)</era>
						<era type="223">Bunsei (1818-1830)</era>
						<era type="224">Tenpō (1830-1844)</era>
						<era type="225">Kōka (1844-1848)</era>
						<era type="226">Kaei (1848-1854)</era>
						<era type="227">Ansei (1854-1860)</era>
						<era type="228">Man-en (1860-1861)</era>
						<era type="229">Bunkyū (1861-1864)</era>
						<era type="230">Genji (1864-1865)</era>
						<era type="231">Keiō (1865-1868)</era>
						<era type="232">Meiji</era>
						<era type="233">Taishō</era>
						<era type="234">Shōwa</era>
						<era type="235">Heisei</era>
					</eraAbbr>
				</eras>
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>EEEE, dd MMMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>d MMMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>d MMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>d/M/y GGGGG</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<dateTimeFormats>
					<dateTimeFormatLength type="full">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="long">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="medium">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="short">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<availableFormats>
						<dateFormatItem id="d">d</dateFormatItem>
						<dateFormatItem id="M">L</dateFormatItem>
						<dateFormatItem id="Md">d/M</dateFormatItem>
						<dateFormatItem id="MEd">E, d/M</dateFormatItem>
						<dateFormatItem id="MMM">LLL</dateFormatItem>
						<dateFormatItem id="MMMd">d MMM</dateFormatItem>
						<dateFormatItem id="MMMEd">E, d MMM</dateFormatItem>
						<dateFormatItem id="MMMMd">d MMMM</dateFormatItem>
						<dateFormatItem id="MMMMEd">E, d MMMM</dateFormatItem>
						<dateFormatItem id="y">G y</dateFormatItem>
					</availableFormats>
				</dateTimeFormats>
			</calendar>
			<calendar type="persian">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<month type="1">Farvardin</month>
							<month type="2">Ordibehesht</month>
							<month type="3">Khordad</month>
							<month type="4">Tir</month>
							<month type="5">Mordad</month>
							<month type="6">Shahrivar</month>
							<month type="7">Mehr</month>
							<month type="8">Aban</month>
							<month type="9">Azar</month>
							<month type="10">Dey</month>
							<month type="11">Bahman</month>
							<month type="12">Esfand</month>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Farvardin</month>
							<month type="2">Ordibehesht</month>
							<month type="3">Khordad</month>
							<month type="4">Tir</month>
							<month type="5">Mordad</month>
							<month type="6">Shahrivar</month>
							<month type="7">Mehr</month>
							<month type="8">Aban</month>
							<month type="9">Azar</month>
							<month type="10">Dey</month>
							<month type="11">Bahman</month>
							<month type="12">Esfand</month>
						</monthWidth>
					</monthContext>
					<monthContext type="stand-alone">
						<monthWidth type="abbreviated">
							<month type="1">Farvardin</month>
							<month type="2">Ordibehesht</month>
							<month type="3">Khordad</month>
							<month type="4">Tir</month>
							<month type="5">Mordad</month>
							<month type="6">Shahrivar</month>
							<month type="7">Mehr</month>
							<month type="8">Aban</month>
							<month type="9">Azar</month>
							<month type="10">Dey</month>
							<month type="11">Bahman</month>
							<month type="12">Esfand</month>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Farvardin</month>
							<month type="2">Ordibehesht</month>
							<month type="3">Khordad</month>
							<month type="4">Tir</month>
							<month type="5">Mordad</month>
							<month type="6">Shahrivar</month>
							<month type="7">Mehr</month>
							<month type="8">Aban</month>
							<month type="9">Azar</month>
							<month type="10">Dey</month>
							<month type="11">Bahman</month>
							<month type="12">Esfand</month>
						</monthWidth>
					</monthContext>
				</months>
				<eras>
					<eraAbbr>
						<era type="0">AP</era>
					</eraAbbr>
				</eras>
			</calendar>
			<calendar type="roc">
				<eras>
					<eraAbbr>
						<era type="0">Sebelum R.O.C.</era>
						<era type="1">R.O.C.</era>
					</eraAbbr>
				</eras>
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>EEEE, dd MMMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>d MMMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>d MMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>d/M/y GGGGG</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<dateTimeFormats>
					<availableFormats>
						<dateFormatItem id="d">d</dateFormatItem>
						<dateFormatItem id="M">L</dateFormatItem>
						<dateFormatItem id="Md">d/M</dateFormatItem>
						<dateFormatItem id="MEd">E, d/M</dateFormatItem>
						<dateFormatItem id="MMM">LLL</dateFormatItem>
						<dateFormatItem id="MMMd">d MMM</dateFormatItem>
						<dateFormatItem id="MMMEd">E, d MMM</dateFormatItem>
						<dateFormatItem id="MMMMd">d MMMM</dateFormatItem>
						<dateFormatItem id="MMMMEd">E, d MMMM</dateFormatItem>
						<dateFormatItem id="y">G y</dateFormatItem>
					</availableFormats>
				</dateTimeFormats>
			</calendar>
		</calendars>
		<fields>
			<field type="era">
				<displayName>Era</displayName>
			</field>
			<field type="year">
				<displayName>Tahun</displayName>
				<relative type="-1">tahun lalu</relative>
				<relative type="0">tahun ini</relative>
				<relative type="1">tahun depan</relative>
				<relativeTime type="future">
					<relativeTimePattern count="other">Dalam {0} tahun</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="other">{0} tahun yang lalu</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="month">
				<displayName>Bulan</displayName>
				<relative type="-1">bulan lalu</relative>
				<relative type="0">bulan ini</relative>
				<relative type="1">Bulan berikutnya</relative>
				<relativeTime type="future">
					<relativeTimePattern count="other">Dalam {0} bulan</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="other">{0} bulan yang lalu</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="week">
				<displayName>Minggu</displayName>
				<relative type="-1">minggu lalu</relative>
				<relative type="0">minggu ini</relative>
				<relative type="1">Minggu berikutnya</relative>
				<relativeTime type="future">
					<relativeTimePattern count="other">Dalam {0} minggu</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="other">{0} minggu yang lalu</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="day">
				<displayName>Hari</displayName>
				<relative type="-2">kemarin lusa</relative>
				<relative type="-1">kemarin</relative>
				<relative type="0">hari ini</relative>
				<relative type="1">besok</relative>
				<relative type="2">lusa</relative>
				<relativeTime type="future">
					<relativeTimePattern count="other">Dalam {0} hari</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="other">{0} hari yang lalu</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="weekday">
				<displayName>Hari dalam Seminggu</displayName>
			</field>
			<field type="sun">
				<relative type="-1">Minggu lalu</relative>
				<relative type="0">Minggu ini</relative>
				<relative type="1">Minggu berikutnya</relative>
			</field>
			<field type="mon">
				<relative type="-1">Senin lalu</relative>
				<relative type="0">Senin ini</relative>
				<relative type="1">Senin berikutnya</relative>
			</field>
			<field type="tue">
				<relative type="-1">Selasa lalu</relative>
				<relative type="0">Selasa ini</relative>
				<relative type="1">Selasa berikutnya</relative>
			</field>
			<field type="wed">
				<relative type="-1">Rabu lalu</relative>
				<relative type="0">Rabu ini</relative>
				<relative type="1">Rabu berikutnya</relative>
			</field>
			<field type="thu">
				<relative type="-1">Kamis lalu</relative>
				<relative type="0">Kamis ini</relative>
				<relative type="1">Kamis berikutnya</relative>
			</field>
			<field type="fri">
				<relative type="-1">Jumat lalu</relative>
				<relative type="0">Jumat ini</relative>
				<relative type="1">Jumat berikutnya</relative>
			</field>
			<field type="sat">
				<relative type="-1">Sabtu lalu</relative>
				<relative type="0">Sabtu ini</relative>
				<relative type="1">Sabtu berikutnya</relative>
			</field>
			<field type="dayperiod">
				<displayName>AM/PM</displayName>
			</field>
			<field type="hour">
				<displayName>Jam</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="other">Dalam {0} jam</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="other">{0} jam yang lalu</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="minute">
				<displayName>Menit</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="other">Dalam {0} menit</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="other">{0} menit yang lalu</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="second">
				<displayName>Detik</displayName>
				<relative type="0">sekarang</relative>
				<relativeTime type="future">
					<relativeTimePattern count="other">Dalam {0} detik</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="other">{0} detik yang lalu</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="zone">
				<displayName>Zona Waktu</displayName>
			</field>
		</fields>
		<timeZoneNames>
			<hourFormat>+HH.mm;-HH.mm</hourFormat>
			<gmtFormat>GMT{0}</gmtFormat>
			<gmtZeroFormat>GMT</gmtZeroFormat>
			<regionFormat>Waktu {0}</regionFormat>
			<regionFormat type="daylight">Waktu Terang {0}</regionFormat>
			<regionFormat type="standard">Waktu Standar {0}</regionFormat>
			<fallbackFormat>{1} ({0})</fallbackFormat>
			<zone type="Etc/Unknown">
				<exemplarCity>Tidak Dikenal</exemplarCity>
			</zone>
			<zone type="America/Anguilla">
				<exemplarCity>Anguila</exemplarCity>
			</zone>
			<zone type="Antarctica/DumontDUrville">
				<exemplarCity>Dumont d’Urville</exemplarCity>
			</zone>
			<zone type="America/Cordoba">
				<exemplarCity>Kordoba</exemplarCity>
			</zone>
			<zone type="Europe/Vienna">
				<exemplarCity>Wina</exemplarCity>
			</zone>
			<zone type="Europe/Brussels">
				<exemplarCity>Brussel</exemplarCity>
			</zone>
			<zone type="America/St_Barthelemy">
				<exemplarCity>Saint Barthélemy</exemplarCity>
			</zone>
			<zone type="America/Cambridge_Bay">
				<exemplarCity>Teluk Cambridge</exemplarCity>
			</zone>
			<zone type="America/Coral_Harbour">
				<exemplarCity>Atikokan</exemplarCity>
			</zone>
			<zone type="America/Thunder_Bay">
				<exemplarCity>Teluk Thunder</exemplarCity>
			</zone>
			<zone type="America/Goose_Bay">
				<exemplarCity>Teluk Goose</exemplarCity>
			</zone>
			<zone type="America/Glace_Bay">
				<exemplarCity>Teluk Glace</exemplarCity>
			</zone>
			<zone type="America/St_Johns">
				<exemplarCity>St. John’s</exemplarCity>
			</zone>
			<zone type="America/Costa_Rica">
				<exemplarCity>Kosta Rika</exemplarCity>
			</zone>
			<zone type="Atlantic/Cape_Verde">
				<exemplarCity>Tanjung Verde</exemplarCity>
			</zone>
			<zone type="America/Curacao">
				<exemplarCity>Curaçao</exemplarCity>
			</zone>
			<zone type="Asia/Nicosia">
				<exemplarCity>Nikosia</exemplarCity>
			</zone>
			<zone type="Europe/Prague">
				<exemplarCity>Praha</exemplarCity>
			</zone>
			<zone type="Africa/Djibouti">
				<exemplarCity>Jibouti</exemplarCity>
			</zone>
			<zone type="Europe/Copenhagen">
				<exemplarCity>Kopenhagen</exemplarCity>
			</zone>
			<zone type="America/Dominica">
				<exemplarCity>Dominika</exemplarCity>
			</zone>
			<zone type="Africa/Algiers">
				<exemplarCity>Aljir</exemplarCity>
			</zone>
			<zone type="Africa/Cairo">
				<exemplarCity>Kairo</exemplarCity>
			</zone>
			<zone type="Africa/Asmera">
				<exemplarCity>Asmara</exemplarCity>
			</zone>
			<zone type="Pacific/Truk">
				<exemplarCity>Chuuk</exemplarCity>
			</zone>
			<zone type="Pacific/Ponape">
				<exemplarCity>Pohnpei</exemplarCity>
			</zone>
			<zone type="Atlantic/Faeroe">
				<exemplarCity>Faroe</exemplarCity>
			</zone>
			<zone type="Europe/London">
				<long>
					<daylight>Musim Panas Inggris</daylight>
				</long>
			</zone>
			<zone type="Africa/Accra">
				<exemplarCity>Akra</exemplarCity>
			</zone>
			<zone type="America/Godthab">
				<exemplarCity>Nuuk</exemplarCity>
			</zone>
			<zone type="America/Scoresbysund">
				<exemplarCity>Ittoqqortoormiit</exemplarCity>
			</zone>
			<zone type="Africa/Conakry">
				<exemplarCity>Konakri</exemplarCity>
			</zone>
			<zone type="Europe/Athens">
				<exemplarCity>Athena</exemplarCity>
			</zone>
			<zone type="Atlantic/South_Georgia">
				<exemplarCity>Georgia Selatan</exemplarCity>
			</zone>
			<zone type="Europe/Dublin">
				<long>
					<daylight>Musim Panas Irlandia</daylight>
				</long>
			</zone>
			<zone type="Asia/Calcutta">
				<exemplarCity>Kolkata</exemplarCity>
			</zone>
			<zone type="Asia/Baghdad">
				<exemplarCity>Bagdad</exemplarCity>
			</zone>
			<zone type="Asia/Tehran">
				<exemplarCity>Teheran</exemplarCity>
			</zone>
			<zone type="Europe/Rome">
				<exemplarCity>Roma</exemplarCity>
			</zone>
			<zone type="America/Jamaica">
				<exemplarCity>Jamaika</exemplarCity>
			</zone>
			<zone type="Indian/Comoro">
				<exemplarCity>Komoro</exemplarCity>
			</zone>
			<zone type="America/St_Kitts">
				<exemplarCity>St. Kitts</exemplarCity>
			</zone>
			<zone type="Asia/Aqtau">
				<exemplarCity>Aktau</exemplarCity>
			</zone>
			<zone type="Asia/Aqtobe">
				<exemplarCity>Aktobe</exemplarCity>
			</zone>
			<zone type="America/St_Lucia">
				<exemplarCity>St. Lucia</exemplarCity>
			</zone>
			<zone type="Asia/Colombo">
				<exemplarCity>Kolombo</exemplarCity>
			</zone>
			<zone type="Europe/Luxembourg">
				<exemplarCity>Luksemburg</exemplarCity>
			</zone>
			<zone type="Europe/Monaco">
				<exemplarCity>Monako</exemplarCity>
			</zone>
			<zone type="Europe/Chisinau">
				<exemplarCity>Kishinev</exemplarCity>
			</zone>
			<zone type="Asia/Macau">
				<exemplarCity>Makau</exemplarCity>
			</zone>
			<zone type="America/Martinique">
				<exemplarCity>Martinik</exemplarCity>
			</zone>
			<zone type="Indian/Maldives">
				<exemplarCity>Maladewa</exemplarCity>
			</zone>
			<zone type="Asia/Katmandu">
				<exemplarCity>Kathmandu</exemplarCity>
			</zone>
			<zone type="Asia/Muscat">
				<exemplarCity>Muskat</exemplarCity>
			</zone>
			<zone type="Europe/Warsaw">
				<exemplarCity>Warsawa</exemplarCity>
			</zone>
			<zone type="America/Puerto_Rico">
				<exemplarCity>Puerto Riko</exemplarCity>
			</zone>
			<zone type="Europe/Lisbon">
				<exemplarCity>Lisboa</exemplarCity>
			</zone>
			<zone type="America/Asuncion">
				<exemplarCity>Asunción</exemplarCity>
			</zone>
			<zone type="Indian/Reunion">
				<exemplarCity>Réunion</exemplarCity>
			</zone>
			<zone type="Europe/Bucharest">
				<exemplarCity>Bukares</exemplarCity>
			</zone>
			<zone type="Europe/Belgrade">
				<exemplarCity>Beograd</exemplarCity>
			</zone>
			<zone type="Europe/Moscow">
				<exemplarCity>Moskwa</exemplarCity>
			</zone>
			<zone type="Asia/Riyadh">
				<exemplarCity>Riyad</exemplarCity>
			</zone>
			<zone type="Pacific/Guadalcanal">
				<exemplarCity>Guadalkanal</exemplarCity>
			</zone>
			<zone type="Africa/Khartoum">
				<exemplarCity>Khartum</exemplarCity>
			</zone>
			<zone type="Asia/Singapore">
				<exemplarCity>Singapura</exemplarCity>
			</zone>
			<zone type="Atlantic/St_Helena">
				<exemplarCity>St. Helena</exemplarCity>
			</zone>
			<zone type="Africa/Sao_Tome">
				<exemplarCity>São Tomé</exemplarCity>
			</zone>
			<zone type="America/Lower_Princes">
				<exemplarCity>Lower Prince's Quarter</exemplarCity>
			</zone>
			<zone type="Asia/Damascus">
				<exemplarCity>Damaskus</exemplarCity>
			</zone>
			<zone type="Europe/Uzhgorod">
				<exemplarCity>Uzhhorod</exemplarCity>
			</zone>
			<zone type="Europe/Zaporozhye">
				<exemplarCity>Zaporizhia</exemplarCity>
			</zone>
			<zone type="America/North_Dakota/Beulah">
				<exemplarCity>Beulah, Dakota Utara</exemplarCity>
			</zone>
			<zone type="America/North_Dakota/New_Salem">
				<exemplarCity>New Salem, Dakota Utara</exemplarCity>
			</zone>
			<zone type="America/North_Dakota/Center">
				<exemplarCity>Center, Dakota Utara</exemplarCity>
			</zone>
			<zone type="America/Indiana/Vincennes">
				<exemplarCity>Vincennes, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Petersburg">
				<exemplarCity>Petersburg, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Tell_City">
				<exemplarCity>Tell City, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Knox">
				<exemplarCity>Knox, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Winamac">
				<exemplarCity>Winamac, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Marengo">
				<exemplarCity>Marengo, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Vevay">
				<exemplarCity>Vevay, Indiana</exemplarCity>
			</zone>
			<zone type="America/Kentucky/Monticello">
				<exemplarCity>Monticello, Kentucky</exemplarCity>
			</zone>
			<zone type="Europe/Vatican">
				<exemplarCity>Vatikan</exemplarCity>
			</zone>
			<zone type="America/St_Vincent">
				<exemplarCity>St. Vincent</exemplarCity>
			</zone>
			<zone type="America/Caracas">
				<exemplarCity>Karakas</exemplarCity>
			</zone>
			<zone type="America/St_Thomas">
				<exemplarCity>St. Thomas</exemplarCity>
			</zone>
			<zone type="Asia/Saigon">
				<exemplarCity>Kota Ho Chi Minh</exemplarCity>
			</zone>
			<metazone type="Acre">
				<long>
					<generic>Waktu Acre</generic>
					<standard>Waktu Standar Acre</standard>
					<daylight>Waktu Musim Panas Acre</daylight>
				</long>
			</metazone>
			<metazone type="Afghanistan">
				<long>
					<standard>Waktu Afganistan</standard>
				</long>
			</metazone>
			<metazone type="Africa_Central">
				<long>
					<standard>Waktu Afrika Tengah</standard>
				</long>
			</metazone>
			<metazone type="Africa_Eastern">
				<long>
					<standard>Waktu Afrika Timur</standard>
				</long>
			</metazone>
			<metazone type="Africa_Southern">
				<long>
					<standard>Waktu Standar Afrika Selatan</standard>
				</long>
			</metazone>
			<metazone type="Africa_Western">
				<long>
					<generic>Waktu Afrika Barat</generic>
					<standard>Waktu Standar Afrika Barat</standard>
					<daylight>Waktu Musim Panas Afrika Barat</daylight>
				</long>
			</metazone>
			<metazone type="Alaska">
				<long>
					<generic>Waktu Alaska</generic>
					<standard>Waktu Standar Alaska</standard>
					<daylight>Waktu Terang Alaska</daylight>
				</long>
			</metazone>
			<metazone type="Almaty">
				<long>
					<generic>Waktu Almaty</generic>
					<standard>Waktu Standar Almaty</standard>
					<daylight>Waktu Musim Panas Almaty</daylight>
				</long>
			</metazone>
			<metazone type="Amazon">
				<long>
					<generic>Waktu Amazon</generic>
					<standard>Waktu Standar Amazon</standard>
					<daylight>Waktu Musim Panas Amazon</daylight>
				</long>
			</metazone>
			<metazone type="America_Central">
				<long>
					<generic>Waktu Tengah</generic>
					<standard>Waktu Standar Tengah</standard>
					<daylight>Waktu Terang Hari Tengah</daylight>
				</long>
			</metazone>
			<metazone type="America_Eastern">
				<long>
					<generic>Waktu Timur</generic>
					<standard>Waktu Standar Timur</standard>
					<daylight>Waktu Terang Hari Timur</daylight>
				</long>
			</metazone>
			<metazone type="America_Mountain">
				<long>
					<generic>Waktu Pegunungan</generic>
					<standard>Waktu Standar Pegunungan</standard>
					<daylight>Waktu Terang Hari Pegunungan</daylight>
				</long>
			</metazone>
			<metazone type="America_Pacific">
				<long>
					<generic>Waktu Pasifik</generic>
					<standard>Waktu Standar Pasifik</standard>
					<daylight>Waktu Terang Hari Pasifik</daylight>
				</long>
			</metazone>
			<metazone type="Anadyr">
				<long>
					<generic>Waktu Anadyr</generic>
					<standard>Waktu Standar Anadyr</standard>
					<daylight>Waktu Musim Panas Anadyr</daylight>
				</long>
			</metazone>
			<metazone type="Aqtau">
				<long>
					<generic>Waktu Aqtau</generic>
					<standard>Waktu Standar Aqtau</standard>
					<daylight>Waktu Musim Panas Aqtau</daylight>
				</long>
			</metazone>
			<metazone type="Aqtobe">
				<long>
					<generic>Waktu Aqtobe</generic>
					<standard>Waktu Standar Aqtobe</standard>
					<daylight>Waktu Musim Panas Aqtobe</daylight>
				</long>
			</metazone>
			<metazone type="Arabian">
				<long>
					<generic>Waktu Arab</generic>
					<standard>Waktu Standar Arab</standard>
					<daylight>Waktu Terang Arab</daylight>
				</long>
			</metazone>
			<metazone type="Argentina">
				<long>
					<generic>Waktu Argentina</generic>
					<standard>Waktu Standar Argentina</standard>
					<daylight>Waktu Musim Panas Argentina</daylight>
				</long>
			</metazone>
			<metazone type="Argentina_Western">
				<long>
					<generic>Waktu Argentina Bagian Barat</generic>
					<standard>Waktu Standar Argentina Bagian Barat</standard>
					<daylight>Waktu Musim Panas Argentina Bagian Barat</daylight>
				</long>
			</metazone>
			<metazone type="Armenia">
				<long>
					<generic>Waktu Armenia</generic>
					<standard>Waktu Standar Armenia</standard>
					<daylight>Waktu Musim Panas Armenia</daylight>
				</long>
			</metazone>
			<metazone type="Atlantic">
				<long>
					<generic>Waktu Atlantik</generic>
					<standard>Waktu Standar Atlantik</standard>
					<daylight>Waktu Terang Atlantik</daylight>
				</long>
			</metazone>
			<metazone type="Australia_Central">
				<long>
					<generic>Waktu Tengah Australia</generic>
					<standard>Waktu Standar Tengah Australia</standard>
					<daylight>Waktu Terang Tengah Australia</daylight>
				</long>
			</metazone>
			<metazone type="Australia_CentralWestern">
				<long>
					<generic>Waktu Barat Tengah Australia</generic>
					<standard>Waktu Standar Barat Tengah Australia</standard>
					<daylight>Waktu Terang Barat Tengah Australia</daylight>
				</long>
			</metazone>
			<metazone type="Australia_Eastern">
				<long>
					<generic>Waktu Timur Australia</generic>
					<standard>Waktu Standar Timur Australia</standard>
					<daylight>Waktu Terang Timur Australia</daylight>
				</long>
			</metazone>
			<metazone type="Australia_Western">
				<long>
					<generic>Waktu Australia Barat</generic>
					<standard>Waktu Standar Barat Australia</standard>
					<daylight>Waktu Terang Barat Australia</daylight>
				</long>
			</metazone>
			<metazone type="Azerbaijan">
				<long>
					<generic>Waktu Azerbaijan</generic>
					<standard>Waktu Standar Azerbaijan</standard>
					<daylight>Waktu Musim Panas Azerbaijan</daylight>
				</long>
			</metazone>
			<metazone type="Azores">
				<long>
					<generic>Waktu Azores</generic>
					<standard>Waktu Standar Azores</standard>
					<daylight>Waktu Musim Panas Azores</daylight>
				</long>
			</metazone>
			<metazone type="Bangladesh">
				<long>
					<generic>Waktu Bangladesh</generic>
					<standard>Waktu Standar Bangladesh</standard>
					<daylight>Waktu Musim Panas Bangladesh</daylight>
				</long>
			</metazone>
			<metazone type="Bhutan">
				<long>
					<standard>Waktu Bhutan</standard>
				</long>
			</metazone>
			<metazone type="Bolivia">
				<long>
					<standard>Waktu Bolivia</standard>
				</long>
			</metazone>
			<metazone type="Brasilia">
				<long>
					<generic>Waktu Brasil</generic>
					<standard>Waktu Standar Brasil</standard>
					<daylight>Waktu Musim Panas Brasil</daylight>
				</long>
			</metazone>
			<metazone type="Brunei">
				<long>
					<standard>Waktu Brunei Darussalam</standard>
				</long>
			</metazone>
			<metazone type="Cape_Verde">
				<long>
					<generic>Waktu Tanjung Verde</generic>
					<standard>Waktu Standar Tanjung Verde</standard>
					<daylight>Waktu Musim Panas Tanjung Verde</daylight>
				</long>
			</metazone>
			<metazone type="Casey">
				<long>
					<standard>Waktu Casey</standard>
				</long>
			</metazone>
			<metazone type="Chamorro">
				<long>
					<standard>Waktu Chamorro</standard>
				</long>
			</metazone>
			<metazone type="Chatham">
				<long>
					<generic>Waktu Chatham</generic>
					<standard>Waktu Standar Chatham</standard>
					<daylight>Waktu Terang Chatham</daylight>
				</long>
			</metazone>
			<metazone type="Chile">
				<long>
					<generic>Waktu Cile</generic>
					<standard>Waktu Standar Cile</standard>
					<daylight>Waktu Musim Panas Cile</daylight>
				</long>
			</metazone>
			<metazone type="China">
				<long>
					<generic>Waktu China</generic>
					<standard>Waktu Standar China</standard>
					<daylight>Waktu Terang China</daylight>
				</long>
			</metazone>
			<metazone type="Choibalsan">
				<long>
					<generic>Waktu Choibalsan</generic>
					<standard>Waktu Standar Choibalsan</standard>
					<daylight>Waktu Musim Panas Choibalsan</daylight>
				</long>
			</metazone>
			<metazone type="Christmas">
				<long>
					<standard>Waktu Pulau Natal</standard>
				</long>
			</metazone>
			<metazone type="Cocos">
				<long>
					<standard>Waktu Kepulauan Cocos</standard>
				</long>
			</metazone>
			<metazone type="Colombia">
				<long>
					<generic>Waktu Kolombia</generic>
					<standard>Waktu Standar Kolombia</standard>
					<daylight>Waktu Musim Panas Kolombia</daylight>
				</long>
			</metazone>
			<metazone type="Cook">
				<long>
					<generic>Waktu Kep. Cook</generic>
					<standard>Waktu Standar Kep. Cook</standard>
					<daylight>Waktu Tengah Musim Panas Kep. Cook</daylight>
				</long>
			</metazone>
			<metazone type="Cuba">
				<long>
					<generic>Waktu Kuba</generic>
					<standard>Waktu Standar Kuba</standard>
					<daylight>Waktu Musim Panas Kuba</daylight>
				</long>
			</metazone>
			<metazone type="Davis">
				<long>
					<standard>Waktu Davis</standard>
				</long>
			</metazone>
			<metazone type="DumontDUrville">
				<long>
					<standard>Waktu Dumont-d’Urville</standard>
				</long>
			</metazone>
			<metazone type="East_Timor">
				<long>
					<standard>Waktu Timor Leste</standard>
				</long>
			</metazone>
			<metazone type="Easter">
				<long>
					<generic>Waktu Pulau Paskah</generic>
					<standard>Waktu Standar Pulau Paskah</standard>
					<daylight>Waktu Musim Panas Pulau Paskah</daylight>
				</long>
			</metazone>
			<metazone type="Ecuador">
				<long>
					<standard>Waktu Ekuador</standard>
				</long>
			</metazone>
			<metazone type="Europe_Central">
				<long>
					<generic>Waktu Eropa Tengah</generic>
					<standard>Waktu Standar Eropa Tengah</standard>
					<daylight>Waktu Musim Panas Eropa Tengah</daylight>
				</long>
			</metazone>
			<metazone type="Europe_Eastern">
				<long>
					<generic>Waktu Eropa Timur</generic>
					<standard>Waktu Standar Eropa Timur</standard>
					<daylight>Waktu Musim Panas Eropa Timur</daylight>
				</long>
			</metazone>
			<metazone type="Europe_Western">
				<long>
					<generic>Waktu Eropa Barat</generic>
					<standard>Waktu Standar Eropa Barat</standard>
					<daylight>Waktu Musim Panas Eropa Barat</daylight>
				</long>
			</metazone>
			<metazone type="Falkland">
				<long>
					<generic>Waktu Kepulauan Falkland</generic>
					<standard>Waktu Standar Kepulauan Falkland</standard>
					<daylight>Waktu Musim Panas Kepulauan Falkland</daylight>
				</long>
			</metazone>
			<metazone type="Fiji">
				<long>
					<generic>Waktu Fiji</generic>
					<standard>Waktu Standar Fiji</standard>
					<daylight>Waktu Musim Panas Fiji</daylight>
				</long>
			</metazone>
			<metazone type="French_Guiana">
				<long>
					<standard>Waktu Guyana Prancis</standard>
				</long>
			</metazone>
			<metazone type="French_Southern">
				<long>
					<standard>Waktu Wilayah Selatan dan Antarktika Prancis</standard>
				</long>
			</metazone>
			<metazone type="Galapagos">
				<long>
					<standard>Waktu Galapagos</standard>
				</long>
			</metazone>
			<metazone type="Gambier">
				<long>
					<standard>Waktu Gambier</standard>
				</long>
			</metazone>
			<metazone type="Georgia">
				<long>
					<generic>Waktu Georgia</generic>
					<standard>Waktu Standar Georgia</standard>
					<daylight>Waktu Musim Panas Georgia</daylight>
				</long>
			</metazone>
			<metazone type="Gilbert_Islands">
				<long>
					<standard>Waktu Kep. Gilbert</standard>
				</long>
			</metazone>
			<metazone type="GMT">
				<long>
					<standard>Waktu Rata-Rata Greenwich</standard>
				</long>
			</metazone>
			<metazone type="Greenland_Eastern">
				<long>
					<generic>Waktu Greenland Timur</generic>
					<standard>Waktu Standar Greenland Timur</standard>
					<daylight>Waktu Musim Panas Greenland Timur</daylight>
				</long>
			</metazone>
			<metazone type="Greenland_Western">
				<long>
					<generic>Waktu Greenland Barat</generic>
					<standard>Waktu Standar Greenland Barat</standard>
					<daylight>Waktu Musim Panas Greenland Barat</daylight>
				</long>
			</metazone>
			<metazone type="Guam">
				<long>
					<standard>Waktu Guam</standard>
				</long>
			</metazone>
			<metazone type="Gulf">
				<long>
					<standard>Waktu Gulf</standard>
				</long>
			</metazone>
			<metazone type="Guyana">
				<long>
					<standard>Waktu Guyana</standard>
				</long>
			</metazone>
			<metazone type="Hawaii_Aleutian">
				<long>
					<generic>Waktu Hawaii-Aleutian</generic>
					<standard>Waktu Standar Hawaii-Aleutian</standard>
					<daylight>Waktu Terang Hawaii-Aleutian</daylight>
				</long>
			</metazone>
			<metazone type="Hong_Kong">
				<long>
					<generic>Waktu Hong Kong</generic>
					<standard>Waktu Standar Hong Kong</standard>
					<daylight>Waktu Musim Panas Hong Kong</daylight>
				</long>
			</metazone>
			<metazone type="Hovd">
				<long>
					<generic>Waktu Hovd</generic>
					<standard>Waktu Standar Hovd</standard>
					<daylight>Waktu Musim Panas Hovd</daylight>
				</long>
			</metazone>
			<metazone type="India">
				<long>
					<standard>Waktu India</standard>
				</long>
			</metazone>
			<metazone type="Indian_Ocean">
				<long>
					<standard>Waktu Samudera Hindia</standard>
				</long>
			</metazone>
			<metazone type="Indochina">
				<long>
					<standard>Waktu Indochina</standard>
				</long>
			</metazone>
			<metazone type="Indonesia_Central">
				<long>
					<standard>Waktu Indonesia Tengah</standard>
				</long>
				<short>
					<standard>WITA</standard>
				</short>
			</metazone>
			<metazone type="Indonesia_Eastern">
				<long>
					<standard>Waktu Indonesia Timur</standard>
				</long>
				<short>
					<standard>WIT</standard>
				</short>
			</metazone>
			<metazone type="Indonesia_Western">
				<long>
					<standard>Waktu Indonesia Barat</standard>
				</long>
				<short>
					<standard>WIB</standard>
				</short>
			</metazone>
			<metazone type="Iran">
				<long>
					<generic>Waktu Iran</generic>
					<standard>Waktu Standar Iran</standard>
					<daylight>Waktu Musim Panas Iran</daylight>
				</long>
			</metazone>
			<metazone type="Irkutsk">
				<long>
					<generic>Waktu Irkutsk</generic>
					<standard>Waktu Standar Irkutsk</standard>
					<daylight>Waktu Musim Panas Irkutsk</daylight>
				</long>
			</metazone>
			<metazone type="Israel">
				<long>
					<generic>Waktu Israel</generic>
					<standard>Waktu Standar Israel</standard>
					<daylight>Waktu Musim Panas Israel</daylight>
				</long>
			</metazone>
			<metazone type="Japan">
				<long>
					<generic>Waktu Jepang</generic>
					<standard>Waktu Standar Jepang</standard>
					<daylight>Waktu Musim Panas Jepang</daylight>
				</long>
			</metazone>
			<metazone type="Kamchatka">
				<long>
					<generic>Waktu Petropavlovsk-Kamchatsky</generic>
					<standard>Waktu Standar Petropavlovsk-Kamchatsky</standard>
					<daylight>Waktu Musim Panas Petropavlovsk-Kamchatski</daylight>
				</long>
			</metazone>
			<metazone type="Kazakhstan_Eastern">
				<long>
					<standard>Waktu Kazakhstan Timur</standard>
				</long>
			</metazone>
			<metazone type="Kazakhstan_Western">
				<long>
					<standard>Waktu Kazakhstan Barat</standard>
				</long>
			</metazone>
			<metazone type="Korea">
				<long>
					<generic>Waktu Korea</generic>
					<standard>Waktu Standar Korea</standard>
					<daylight>Waktu Musim Panas Korea</daylight>
				</long>
			</metazone>
			<metazone type="Kosrae">
				<long>
					<standard>Waktu Kosrae</standard>
				</long>
			</metazone>
			<metazone type="Krasnoyarsk">
				<long>
					<generic>Waktu Krasnoyarsk</generic>
					<standard>Waktu Standar Krasnoyarsk</standard>
					<daylight>Waktu Musim Panas Krasnoyarsk</daylight>
				</long>
			</metazone>
			<metazone type="Kyrgystan">
				<long>
					<standard>Waktu Kirghizia</standard>
				</long>
			</metazone>
			<metazone type="Lanka">
				<long>
					<standard>Waktu Lanka</standard>
				</long>
			</metazone>
			<metazone type="Line_Islands">
				<long>
					<standard>Waktu Kep. Line</standard>
				</long>
			</metazone>
			<metazone type="Lord_Howe">
				<long>
					<generic>Waktu Lord Howe</generic>
					<standard>Waktu Standar Lord Howe</standard>
					<daylight>Waktu Terang Lord Howe</daylight>
				</long>
			</metazone>
			<metazone type="Macau">
				<long>
					<generic>Waktu Makau</generic>
					<standard>Waktu Standar Makau</standard>
					<daylight>Waktu Musim Panas Makau</daylight>
				</long>
			</metazone>
			<metazone type="Macquarie">
				<long>
					<standard>Waktu Kepulauan Macquarie</standard>
				</long>
			</metazone>
			<metazone type="Magadan">
				<long>
					<generic>Waktu Magadan</generic>
					<standard>Waktu Standar Magadan</standard>
					<daylight>Waktu Musim Panas Magadan</daylight>
				</long>
			</metazone>
			<metazone type="Malaysia">
				<long>
					<standard>Waktu Malaysia</standard>
				</long>
			</metazone>
			<metazone type="Maldives">
				<long>
					<standard>Waktu Maladewa</standard>
				</long>
			</metazone>
			<metazone type="Marquesas">
				<long>
					<standard>Waktu Marquesas</standard>
				</long>
			</metazone>
			<metazone type="Marshall_Islands">
				<long>
					<standard>Waktu Kep. Marshall</standard>
				</long>
			</metazone>
			<metazone type="Mauritius">
				<long>
					<generic>Waktu Mauritius</generic>
					<standard>Waktu Standar Mauritius</standard>
					<daylight>Waktu Musim Panas Mauritius</daylight>
				</long>
			</metazone>
			<metazone type="Mawson">
				<long>
					<standard>Waktu Mawson</standard>
				</long>
			</metazone>
			<metazone type="Mongolia">
				<long>
					<generic>Waktu Ulan Bator</generic>
					<standard>Waktu Standar Ulan Bator</standard>
					<daylight>Waktu Musim Panas Ulan Bator</daylight>
				</long>
			</metazone>
			<metazone type="Moscow">
				<long>
					<generic>Waktu Moskow</generic>
					<standard>Waktu Standar Moskow</standard>
					<daylight>Waktu Musim Panas Moskow</daylight>
				</long>
			</metazone>
			<metazone type="Myanmar">
				<long>
					<standard>Waktu Myanmar</standard>
				</long>
			</metazone>
			<metazone type="Nauru">
				<long>
					<standard>Waktu Nauru</standard>
				</long>
			</metazone>
			<metazone type="Nepal">
				<long>
					<standard>Waktu Nepal</standard>
				</long>
			</metazone>
			<metazone type="New_Caledonia">
				<long>
					<generic>Waktu Kaledonia Baru</generic>
					<standard>Waktu Standar Kaledonia Baru</standard>
					<daylight>Waktu Musim Panas Kaledonia Baru</daylight>
				</long>
			</metazone>
			<metazone type="New_Zealand">
				<long>
					<generic>Waktu Selandia Baru</generic>
					<standard>Waktu Standar Selandia Baru</standard>
					<daylight>Waktu Terang Selandia Baru</daylight>
				</long>
			</metazone>
			<metazone type="Newfoundland">
				<long>
					<generic>Waktu Newfoundland</generic>
					<standard>Waktu Standar Newfoundland</standard>
					<daylight>Waktu Siang Hari Newfoundland</daylight>
				</long>
			</metazone>
			<metazone type="Niue">
				<long>
					<standard>Waktu Niue</standard>
				</long>
			</metazone>
			<metazone type="Norfolk">
				<long>
					<standard>Waktu Kepulauan Norfolk</standard>
				</long>
			</metazone>
			<metazone type="Noronha">
				<long>
					<generic>Waktu Fernando de Noronha</generic>
					<standard>Waktu Standar Fernando de Noronha</standard>
					<daylight>Waktu Musim Panas Fernando de Noronha</daylight>
				</long>
			</metazone>
			<metazone type="North_Mariana">
				<long>
					<standard>Waktu Kep. Mariana Utara</standard>
				</long>
			</metazone>
			<metazone type="Novosibirsk">
				<long>
					<generic>Waktu Novosibirsk</generic>
					<standard>Waktu Standar Novosibirsk</standard>
					<daylight>Waktu Musim Panas Novosibirsk</daylight>
				</long>
			</metazone>
			<metazone type="Omsk">
				<long>
					<generic>Waktu Omsk</generic>
					<standard>Waktu Standar Omsk</standard>
					<daylight>Waktu Musim Panas Omsk</daylight>
				</long>
			</metazone>
			<metazone type="Pakistan">
				<long>
					<generic>Waktu Pakistan</generic>
					<standard>Waktu Standar Pakistan</standard>
					<daylight>Waktu Musim Panas Pakistan</daylight>
				</long>
			</metazone>
			<metazone type="Palau">
				<long>
					<standard>Waktu Palau</standard>
				</long>
			</metazone>
			<metazone type="Papua_New_Guinea">
				<long>
					<standard>Waktu Papua Nugini</standard>
				</long>
			</metazone>
			<metazone type="Paraguay">
				<long>
					<generic>Waktu Paraguay</generic>
					<standard>Waktu Standar Paraguay</standard>
					<daylight>Waktu Musim Panas Paraguay</daylight>
				</long>
			</metazone>
			<metazone type="Peru">
				<long>
					<generic>Waktu Peru</generic>
					<standard>Waktu Standar Peru</standard>
					<daylight>Waktu Musim Panas Peru</daylight>
				</long>
			</metazone>
			<metazone type="Philippines">
				<long>
					<generic>Waktu Filipina</generic>
					<standard>Waktu Standar Filipina</standard>
					<daylight>Waktu Musim Panas Filipina</daylight>
				</long>
			</metazone>
			<metazone type="Phoenix_Islands">
				<long>
					<standard>Waktu Kepulauan Phoenix</standard>
				</long>
			</metazone>
			<metazone type="Pierre_Miquelon">
				<long>
					<generic>Waktu Saint Pierre dan Miquelon</generic>
					<standard>Waktu Standar Saint Pierre dan Miquelon</standard>
					<daylight>Waktu Musim Panas Saint Pierre dan Miquelon</daylight>
				</long>
			</metazone>
			<metazone type="Pitcairn">
				<long>
					<standard>Waktu Pitcairn</standard>
				</long>
			</metazone>
			<metazone type="Ponape">
				<long>
					<standard>Waktu Ponape</standard>
				</long>
			</metazone>
			<metazone type="Qyzylorda">
				<long>
					<generic>Waktu Qyzylorda</generic>
					<standard>Waktu Standar Qyzylorda</standard>
					<daylight>Waktu Musim Panas Qyzylorda</daylight>
				</long>
			</metazone>
			<metazone type="Reunion">
				<long>
					<standard>Waktu Reunion</standard>
				</long>
			</metazone>
			<metazone type="Rothera">
				<long>
					<standard>Waktu Rothera</standard>
				</long>
			</metazone>
			<metazone type="Sakhalin">
				<long>
					<generic>Waktu Sakhalin</generic>
					<standard>Waktu Standar Sakhalin</standard>
					<daylight>Waktu Musim Panas Sakhalin</daylight>
				</long>
			</metazone>
			<metazone type="Samara">
				<long>
					<generic>Waktu Samara</generic>
					<standard>Waktu Standar Samara</standard>
					<daylight>Waktu Musim Panas Samara</daylight>
				</long>
			</metazone>
			<metazone type="Samoa">
				<long>
					<generic>Waktu Samoa</generic>
					<standard>Waktu Standar Samoa</standard>
					<daylight>Waktu Musim Panas Samoa</daylight>
				</long>
			</metazone>
			<metazone type="Seychelles">
				<long>
					<standard>Waktu Seychelles</standard>
				</long>
			</metazone>
			<metazone type="Singapore">
				<long>
					<standard>Waktu Standar Singapura</standard>
				</long>
			</metazone>
			<metazone type="Solomon">
				<long>
					<standard>Waktu Kepulauan Solomon</standard>
				</long>
			</metazone>
			<metazone type="South_Georgia">
				<long>
					<standard>Waktu Georgia Selatan</standard>
				</long>
			</metazone>
			<metazone type="Suriname">
				<long>
					<standard>Waktu Suriname</standard>
				</long>
			</metazone>
			<metazone type="Syowa">
				<long>
					<standard>Waktu Syowa</standard>
				</long>
			</metazone>
			<metazone type="Tahiti">
				<long>
					<standard>Waktu Tahiti</standard>
				</long>
			</metazone>
			<metazone type="Taipei">
				<long>
					<generic>Waktu Taipei</generic>
					<standard>Waktu Standar Taipei</standard>
					<daylight>Waktu Musim Panas Taipei</daylight>
				</long>
			</metazone>
			<metazone type="Tajikistan">
				<long>
					<standard>Waktu Tajikistan</standard>
				</long>
			</metazone>
			<metazone type="Tokelau">
				<long>
					<standard>Waktu Tokelau</standard>
				</long>
			</metazone>
			<metazone type="Tonga">
				<long>
					<generic>Waktu Tonga</generic>
					<standard>Waktu Standar Tonga</standard>
					<daylight>Waktu Musim Panas Tonga</daylight>
				</long>
			</metazone>
			<metazone type="Truk">
				<long>
					<standard>Waktu Chuuk</standard>
				</long>
			</metazone>
			<metazone type="Turkmenistan">
				<long>
					<generic>Waktu Turkmenistan</generic>
					<standard>Waktu Standar Turkmenistan</standard>
					<daylight>Waktu Musim Panas Turkmenistan</daylight>
				</long>
			</metazone>
			<metazone type="Tuvalu">
				<long>
					<standard>Waktu Tuvalu</standard>
				</long>
			</metazone>
			<metazone type="Uruguay">
				<long>
					<generic>Waktu Uruguay</generic>
					<standard>Waktu Standar Uruguay</standard>
					<daylight>Waktu Musim Panas Uruguay</daylight>
				</long>
			</metazone>
			<metazone type="Uzbekistan">
				<long>
					<generic>Waktu Uzbekistan</generic>
					<standard>Waktu Standar Uzbekistan</standard>
					<daylight>Waktu Musim Panas Uzbekistan</daylight>
				</long>
			</metazone>
			<metazone type="Vanuatu">
				<long>
					<generic>Waktu Vanuatu</generic>
					<standard>Waktu Standar Vanuatu</standard>
					<daylight>Waktu Musim Panas Vanuatu</daylight>
				</long>
			</metazone>
			<metazone type="Venezuela">
				<long>
					<standard>Waktu Venezuela</standard>
				</long>
			</metazone>
			<metazone type="Vladivostok">
				<long>
					<generic>Waktu Vladivostok</generic>
					<standard>Waktu Standar Vladivostok</standard>
					<daylight>Waktu Musim Panas Vladivostok</daylight>
				</long>
			</metazone>
			<metazone type="Volgograd">
				<long>
					<generic>Waktu Volgograd</generic>
					<standard>Waktu Standar Volgograd</standard>
					<daylight>Waktu Musim Panas Volgograd</daylight>
				</long>
			</metazone>
			<metazone type="Vostok">
				<long>
					<standard>Waktu Vostok</standard>
				</long>
			</metazone>
			<metazone type="Wake">
				<long>
					<standard>Waktu Kepulauan Wake</standard>
				</long>
			</metazone>
			<metazone type="Wallis">
				<long>
					<standard>Waktu Wallis dan Futuna</standard>
				</long>
			</metazone>
			<metazone type="Yakutsk">
				<long>
					<generic>Waktu Yakutsk</generic>
					<standard>Waktu Standar Yakutsk</standard>
					<daylight>Waktu Musim Panas Yakutsk</daylight>
				</long>
			</metazone>
			<metazone type="Yekaterinburg">
				<long>
					<generic>Waktu Yekaterinburg</generic>
					<standard>Waktu Standar Yekaterinburg</standard>
					<daylight>Waktu Musim Panas Yekaterinburg</daylight>
				</long>
			</metazone>
		</timeZoneNames>
	</dates>
	<numbers>
		<symbols numberSystem="latn">
			<decimal>,</decimal>
			<group>.</group>
			<list>;</list>
			<percentSign>%</percentSign>
			<plusSign>+</plusSign>
			<minusSign>-</minusSign>
			<exponential>E</exponential>
			<superscriptingExponent>×</superscriptingExponent>
			<perMille>‰</perMille>
			<infinity>∞</infinity>
			<nan>NaN</nan>
		</symbols>
		<decimalFormats numberSystem="latn">
			<decimalFormatLength>
				<decimalFormat>
					<pattern>#,##0.###</pattern>
				</decimalFormat>
			</decimalFormatLength>
			<decimalFormatLength type="long">
				<decimalFormat>
					<pattern type="1000" count="other">0 ribu</pattern>
					<pattern type="10000" count="other">00 ribu</pattern>
					<pattern type="100000" count="other">000 ribu</pattern>
					<pattern type="1000000" count="other">0 juta</pattern>
					<pattern type="10000000" count="other">00 juta</pattern>
					<pattern type="100000000" count="other">000 juta</pattern>
					<pattern type="1000000000" count="other">0 miliar</pattern>
					<pattern type="10000000000" count="other">00 miliar</pattern>
					<pattern type="100000000000" count="other">000 miliar</pattern>
					<pattern type="1000000000000" count="other">0 triliun</pattern>
					<pattern type="10000000000000" count="other">00 triliun</pattern>
					<pattern type="100000000000000" count="other">000 triliun</pattern>
				</decimalFormat>
			</decimalFormatLength>
			<decimalFormatLength type="short">
				<decimalFormat>
					<pattern type="1000" count="other">0</pattern>
					<pattern type="10000" count="other">00 rb</pattern>
					<pattern type="100000" count="other">000 rb</pattern>
					<pattern type="1000000" count="other">0 jt</pattern>
					<pattern type="10000000" count="other">00 jt</pattern>
					<pattern type="100000000" count="other">000 jt</pattern>
					<pattern type="1000000000" count="other">0 M</pattern>
					<pattern type="10000000000" count="other">00 M</pattern>
					<pattern type="100000000000" count="other">000 M</pattern>
					<pattern type="1000000000000" count="other">0 T</pattern>
					<pattern type="10000000000000" count="other">00 T</pattern>
					<pattern type="100000000000000" count="other">000 T</pattern>
				</decimalFormat>
			</decimalFormatLength>
		</decimalFormats>
		<scientificFormats numberSystem="latn">
			<scientificFormatLength>
				<scientificFormat>
					<pattern>#E0</pattern>
				</scientificFormat>
			</scientificFormatLength>
		</scientificFormats>
		<percentFormats numberSystem="latn">
			<percentFormatLength>
				<percentFormat>
					<pattern>#,##0%</pattern>
				</percentFormat>
			</percentFormatLength>
		</percentFormats>
		<currencyFormats numberSystem="latn">
			<currencyFormatLength>
				<currencyFormat type="standard">
					<pattern>¤#,##0.00</pattern>
				</currencyFormat>
				<currencyFormat type="accounting">
					<pattern>¤#,##0.00;(¤#,##0.00)</pattern>
				</currencyFormat>
			</currencyFormatLength>
			<unitPattern count="other">{0} {1}</unitPattern>
		</currencyFormats>
		<currencies>
			<currency type="ADP">
				<displayName>Peseta Andorra</displayName>
			</currency>
			<currency type="AED">
				<displayName>Dirham Uni Emirat Arab</displayName>
				<displayName count="other">Dirham Uni Emirat Arab</displayName>
			</currency>
			<currency type="AFA">
				<displayName>Afgani Afganistan (1927–2002)</displayName>
			</currency>
			<currency type="AFN">
				<displayName>Afgani Afganistan</displayName>
				<displayName count="other">Afgani Afganistan</displayName>
			</currency>
			<currency type="ALL">
				<displayName>Lek Albania</displayName>
				<displayName count="other">Lek Albania</displayName>
			</currency>
			<currency type="AMD">
				<displayName>Dram Armenia</displayName>
				<displayName count="other">Dram Armenia</displayName>
			</currency>
			<currency type="ANG">
				<displayName>Guilder Antilla Belanda</displayName>
				<displayName count="other">Guilder Antilla Belanda</displayName>
			</currency>
			<currency type="AOA">
				<displayName>Kwanza Angola</displayName>
				<displayName count="other">Kwanza Angola</displayName>
			</currency>
			<currency type="AOK">
				<displayName>Kwanza Angola (1977–1991)</displayName>
			</currency>
			<currency type="AON">
				<displayName>Kwanza Baru Angola (1990–2000)</displayName>
			</currency>
			<currency type="AOR">
				<displayName>Kwanza Angola yang Disesuaikan Lagi (1995–1999)</displayName>
			</currency>
			<currency type="ARA">
				<displayName>Austral Argentina</displayName>
			</currency>
			<currency type="ARL">
				<displayName>Peso Ley Argentina (1970–1983)</displayName>
			</currency>
			<currency type="ARM">
				<displayName>Peso Argentina (1881–1970)</displayName>
			</currency>
			<currency type="ARP">
				<displayName>Peso Argentina (1983–1985)</displayName>
			</currency>
			<currency type="ARS">
				<displayName>Peso Argentina</displayName>
				<displayName count="other">Peso Argentina</displayName>
			</currency>
			<currency type="ATS">
				<displayName>Schilling Austria</displayName>
			</currency>
			<currency type="AUD">
				<displayName>Dolar Australia</displayName>
				<displayName count="other">Dolar Australia</displayName>
				<symbol>AU$</symbol>
			</currency>
			<currency type="AWG">
				<displayName>Florin Aruba</displayName>
				<displayName count="other">Florin Aruba</displayName>
			</currency>
			<currency type="AZM">
				<displayName>Manat Azerbaijan (1993–2006)</displayName>
			</currency>
			<currency type="AZN">
				<displayName>Manat Azerbaijan</displayName>
				<displayName count="other">Manat Azerbaijan</displayName>
			</currency>
			<currency type="BAD">
				<displayName>Dinar Bosnia-Herzegovina (1992–1994)</displayName>
			</currency>
			<currency type="BAM">
				<displayName>Mark Konvertibel Bosnia-Herzegovina</displayName>
				<displayName count="other">Mark Konvertibel Bosnia-Herzegovina</displayName>
			</currency>
			<currency type="BAN">
				<displayName>Dinar Baru Bosnia-Herzegovina (1994–1997)</displayName>
			</currency>
			<currency type="BBD">
				<displayName>Dolar Barbados</displayName>
				<displayName count="other">Dolar Barbados</displayName>
			</currency>
			<currency type="BDT">
				<displayName>Taka Bangladesh</displayName>
				<displayName count="other">Taka Bangladesh</displayName>
			</currency>
			<currency type="BEC">
				<displayName>Franc Belgia (konvertibel)</displayName>
			</currency>
			<currency type="BEF">
				<displayName>Franc Belgia</displayName>
			</currency>
			<currency type="BEL">
				<displayName>Franc Belgia (keuangan)</displayName>
			</currency>
			<currency type="BGL">
				<displayName>Hard Lev Bulgaria</displayName>
			</currency>
			<currency type="BGM">
				<displayName>Socialist Lev Bulgaria</displayName>
			</currency>
			<currency type="BGN">
				<displayName>Lev Bulgaria</displayName>
				<displayName count="other">Lev Bulgaria</displayName>
			</currency>
			<currency type="BGO">
				<displayName>Lev Bulgaria (1879–1952)</displayName>
			</currency>
			<currency type="BHD">
				<displayName>Dinar Bahrain</displayName>
				<displayName count="other">Dinar Bahrain</displayName>
			</currency>
			<currency type="BIF">
				<displayName>Franc Burundi</displayName>
				<displayName count="other">Franc Burundi</displayName>
			</currency>
			<currency type="BMD">
				<displayName>Dolar Bermuda</displayName>
				<displayName count="other">Dolar Bermuda</displayName>
			</currency>
			<currency type="BND">
				<displayName>Dolar Brunei</displayName>
				<displayName count="other">Dolar Brunei</displayName>
			</currency>
			<currency type="BOB">
				<displayName>Boliviano</displayName>
				<displayName count="other">Boliviano</displayName>
			</currency>
			<currency type="BOL">
				<displayName>Boliviano Bolivia (1863–1963)</displayName>
			</currency>
			<currency type="BOP">
				<displayName>Peso Bolivia</displayName>
			</currency>
			<currency type="BOV">
				<displayName>Mvdol Bolivia</displayName>
			</currency>
			<currency type="BRB">
				<displayName>Cruzeiro Baru Brasil (1967–1986)</displayName>
			</currency>
			<currency type="BRC">
				<displayName>Cruzado Brasil (1986–1989)</displayName>
			</currency>
			<currency type="BRE">
				<displayName>Cruzeiro Brasil (1990–1993)</displayName>
			</currency>
			<currency type="BRL">
				<displayName>Real Brasil</displayName>
				<displayName count="other">Real Brasil</displayName>
				<symbol>R$</symbol>
			</currency>
			<currency type="BRN">
				<displayName>Cruzado Baru Brasil (1989–1990)</displayName>
			</currency>
			<currency type="BRR">
				<displayName>Cruzeiro Brasil (1993–1994)</displayName>
			</currency>
			<currency type="BRZ">
				<displayName>Cruzeiro Brasil (1942–1967)</displayName>
			</currency>
			<currency type="BSD">
				<displayName>Dolar Bahama</displayName>
				<displayName count="other">Dolar Bahama</displayName>
			</currency>
			<currency type="BTN">
				<displayName>Ngultrum Bhutan</displayName>
				<displayName count="other">Ngultrum Bhutan</displayName>
			</currency>
			<currency type="BUK">
				<displayName>Kyat Burma</displayName>
			</currency>
			<currency type="BWP">
				<displayName>Pula Botswana</displayName>
				<displayName count="other">Pula Botswana</displayName>
			</currency>
			<currency type="BYB">
				<displayName>Rubel Baru Belarus (1994–1999)</displayName>
			</currency>
			<currency type="BYR">
				<displayName>Rubel Belarusia</displayName>
				<displayName count="other">Rubel Belarusia</displayName>
			</currency>
			<currency type="BZD">
				<displayName>Dolar Belize</displayName>
				<displayName count="other">Dolar Belize</displayName>
			</currency>
			<currency type="CAD">
				<displayName>Dolar Kanada</displayName>
				<displayName count="other">Dolar Kanada</displayName>
				<symbol>CA$</symbol>
			</currency>
			<currency type="CDF">
				<displayName>Franc Kongo</displayName>
				<displayName count="other">Franc Kongo</displayName>
			</currency>
			<currency type="CHE">
				<displayName>Euro WIR</displayName>
			</currency>
			<currency type="CHF">
				<displayName>Franc Swiss</displayName>
				<displayName count="other">Franc Swiss</displayName>
			</currency>
			<currency type="CHW">
				<displayName>Franc WIR</displayName>
			</currency>
			<currency type="CLE">
				<displayName>Escudo Cile</displayName>
			</currency>
			<currency type="CLF">
				<displayName>Satuan Hitung (UF) Cile</displayName>
			</currency>
			<currency type="CLP">
				<displayName>Peso Cile</displayName>
				<displayName count="other">Peso Cile</displayName>
			</currency>
			<currency type="CNY">
				<displayName>Yuan China</displayName>
				<displayName count="other">Yuan China</displayName>
				<symbol>CN¥</symbol>
			</currency>
			<currency type="COP">
				<displayName>Peso Kolombia</displayName>
				<displayName count="other">Peso Kolombia</displayName>
			</currency>
			<currency type="COU">
				<displayName>Unit Nilai Nyata Kolombia</displayName>
			</currency>
			<currency type="CRC">
				<displayName>Colon Kosta Rika</displayName>
				<displayName count="other">Colon Kosta Rika</displayName>
			</currency>
			<currency type="CSD">
				<displayName>Dinar Serbia (2002–2006)</displayName>
			</currency>
			<currency type="CSK">
				<displayName>Hard Koruna Cheska</displayName>
			</currency>
			<currency type="CUC">
				<displayName>Peso Konvertibel Kuba</displayName>
				<displayName count="other">Peso Konvertibel Kuba</displayName>
			</currency>
			<currency type="CUP">
				<displayName>Peso Kuba</displayName>
				<displayName count="other">Peso Kuba</displayName>
			</currency>
			<currency type="CVE">
				<displayName>Escudo Tanjung Verde</displayName>
				<displayName count="other">Escudo Tanjung Verde</displayName>
			</currency>
			<currency type="CYP">
				<displayName>Pound Siprus</displayName>
			</currency>
			<currency type="CZK">
				<displayName>Koruna Cheska</displayName>
				<displayName count="other">Koruna Cheska</displayName>
			</currency>
			<currency type="DDM">
				<displayName>Mark Jerman Timur</displayName>
			</currency>
			<currency type="DEM">
				<displayName>Mark Jerman</displayName>
			</currency>
			<currency type="DJF">
				<displayName>Franc Jibuti</displayName>
				<displayName count="other">Franc Jibuti</displayName>
			</currency>
			<currency type="DKK">
				<displayName>Krone Denmark</displayName>
				<displayName count="other">Krone Denmark</displayName>
			</currency>
			<currency type="DOP">
				<displayName>Peso Dominika</displayName>
				<displayName count="other">Peso Dominika</displayName>
			</currency>
			<currency type="DZD">
				<displayName>Dinar Algeria</displayName>
				<displayName count="other">Dinar Algeria</displayName>
			</currency>
			<currency type="ECS">
				<displayName>Sucre Ekuador</displayName>
			</currency>
			<currency type="ECV">
				<displayName>Satuan Nilai Tetap Ekuador</displayName>
			</currency>
			<currency type="EEK">
				<displayName>Kroon Estonia</displayName>
			</currency>
			<currency type="EGP">
				<displayName>Pound Mesir</displayName>
				<displayName count="other">Pound Mesir</displayName>
			</currency>
			<currency type="ERN">
				<displayName>Nakfa Eritrea</displayName>
				<displayName count="other">Nakfa Eritrea</displayName>
			</currency>
			<currency type="ESA">
				<displayName>Peseta Spanyol (akun)</displayName>
			</currency>
			<currency type="ESB">
				<displayName>Peseta Spanyol (konvertibel)</displayName>
			</currency>
			<currency type="ESP">
				<displayName>Peseta Spanyol</displayName>
			</currency>
			<currency type="ETB">
				<displayName>Birr Etiopia</displayName>
				<displayName count="other">Birr Etiopia</displayName>
			</currency>
			<currency type="EUR">
				<displayName>Euro</displayName>
				<displayName count="other">Euro</displayName>
				<symbol>€</symbol>
			</currency>
			<currency type="FIM">
				<displayName>Markka Finlandia</displayName>
			</currency>
			<currency type="FJD">
				<displayName>Dolar Fiji</displayName>
				<displayName count="other">Dolar Fiji</displayName>
			</currency>
			<currency type="FKP">
				<displayName>Pound Kepulauan Falkland</displayName>
				<displayName count="other">Pound Kepulauan Falkland</displayName>
			</currency>
			<currency type="FRF">
				<displayName>Franc Prancis</displayName>
			</currency>
			<currency type="GBP">
				<displayName>Pound Sterling Inggris</displayName>
				<displayName count="other">Pound Sterling Inggris</displayName>
				<symbol>£</symbol>
			</currency>
			<currency type="GEK">
				<displayName>Kupon Larit Georgia</displayName>
			</currency>
			<currency type="GEL">
				<displayName>Lari Georgia</displayName>
				<displayName count="other">Lari Georgia</displayName>
			</currency>
			<currency type="GHC">
				<displayName>Cedi Ghana (1979–2007)</displayName>
			</currency>
			<currency type="GHS">
				<displayName>Cedi Ghana</displayName>
				<displayName count="other">Cedi Ghana</displayName>
			</currency>
			<currency type="GIP">
				<displayName>Pound Gibraltar</displayName>
				<displayName count="other">Pound Gibraltar</displayName>
			</currency>
			<currency type="GMD">
				<displayName>Dalasi Gambia</displayName>
				<displayName count="other">Dalasi Gambia</displayName>
			</currency>
			<currency type="GNF">
				<displayName>Franc Guinea</displayName>
				<displayName count="other">Franc Guinea</displayName>
			</currency>
			<currency type="GNS">
				<displayName>Syli Guinea</displayName>
			</currency>
			<currency type="GQE">
				<displayName>Ekuele Guinea Ekuatorial</displayName>
			</currency>
			<currency type="GRD">
				<displayName>Drachma Yunani</displayName>
			</currency>
			<currency type="GTQ">
				<displayName>Quetzal Guatemala</displayName>
				<displayName count="other">Quetzal Guatemala</displayName>
			</currency>
			<currency type="GWE">
				<displayName>Escudo Guinea Portugal</displayName>
			</currency>
			<currency type="GWP">
				<displayName>Peso Guinea-Bissau</displayName>
			</currency>
			<currency type="GYD">
				<displayName>Dolar Guyana</displayName>
				<displayName count="other">Dolar Guyana</displayName>
			</currency>
			<currency type="HKD">
				<displayName>Dolar Hong Kong</displayName>
				<displayName count="other">Dolar Hong Kong</displayName>
				<symbol>HK$</symbol>
			</currency>
			<currency type="HNL">
				<displayName>Lempira Honduras</displayName>
				<displayName count="other">Lempira Honduras</displayName>
			</currency>
			<currency type="HRD">
				<displayName>Dinar Kroasia</displayName>
			</currency>
			<currency type="HRK">
				<displayName>Kuna Kroasia</displayName>
				<displayName count="other">Kuna Kroasia</displayName>
			</currency>
			<currency type="HTG">
				<displayName>Gourde Haiti</displayName>
				<displayName count="other">Gourde Haiti</displayName>
			</currency>
			<currency type="HUF">
				<displayName>Forint Hungaria</displayName>
				<displayName count="other">Forint Hungaria</displayName>
			</currency>
			<currency type="IDR">
				<displayName>Rupiah Indonesia</displayName>
				<displayName count="other">Rupiah Indonesia</displayName>
				<symbol>Rp</symbol>
			</currency>
			<currency type="IEP">
				<displayName>Pound Irlandia</displayName>
			</currency>
			<currency type="ILP">
				<displayName>Pound Israel</displayName>
			</currency>
			<currency type="ILS">
				<displayName>Shekel Baru Israel</displayName>
				<symbol>₪</symbol>
			</currency>
			<currency type="INR">
				<displayName>Rupee India</displayName>
				<displayName count="other">Rupee India</displayName>
				<symbol>Rs</symbol>
			</currency>
			<currency type="IQD">
				<displayName>Dinar Irak</displayName>
				<displayName count="other">Dinar Irak</displayName>
			</currency>
			<currency type="IRR">
				<displayName>Rial Iran</displayName>
				<displayName count="other">Rial Iran</displayName>
			</currency>
			<currency type="ISK">
				<displayName>Krona Islandia</displayName>
				<displayName count="other">Krona Islandia</displayName>
			</currency>
			<currency type="ITL">
				<displayName>Lira Italia</displayName>
			</currency>
			<currency type="JMD">
				<displayName>Dolar Jamaika</displayName>
				<displayName count="other">Dolar Jamaika</displayName>
			</currency>
			<currency type="JOD">
				<displayName>Dinar Yordania</displayName>
				<displayName count="other">Dinar Yordania</displayName>
			</currency>
			<currency type="JPY">
				<displayName>Yen Jepang</displayName>
				<displayName count="other">Yen Jepang</displayName>
				<symbol>JP¥</symbol>
			</currency>
			<currency type="KES">
				<displayName>Shilling Kenya</displayName>
				<displayName count="other">Shilling Kenya</displayName>
			</currency>
			<currency type="KGS">
				<displayName>Som Kirgistan</displayName>
				<displayName count="other">Som Kirgistan</displayName>
			</currency>
			<currency type="KHR">
				<displayName>Riel Kamboja</displayName>
				<displayName count="other">Riel Kamboja</displayName>
			</currency>
			<currency type="KMF">
				<displayName>Franc Komoro</displayName>
				<displayName count="other">Franc Komoro</displayName>
			</currency>
			<currency type="KPW">
				<displayName>Won Korea Utara</displayName>
				<displayName count="other">Won Korea Utara</displayName>
			</currency>
			<currency type="KRH">
				<displayName>Hwan Korea Selatan (1953–1962)</displayName>
			</currency>
			<currency type="KRO">
				<displayName>Won Korea Selatan (1945–1953)</displayName>
			</currency>
			<currency type="KRW">
				<displayName>Won Korea Selatan</displayName>
				<displayName count="other">Won Korea Selatan</displayName>
				<symbol>₩</symbol>
			</currency>
			<currency type="KWD">
				<displayName>Dinar Kuwait</displayName>
				<displayName count="other">Dinar Kuwait</displayName>
			</currency>
			<currency type="KYD">
				<displayName>Dolar Kepulauan Cayman</displayName>
				<displayName count="other">Dolar Kepulauan Cayman</displayName>
			</currency>
			<currency type="KZT">
				<displayName>Tenge Kazakstan</displayName>
				<displayName count="other">Tenge Kazakstan</displayName>
			</currency>
			<currency type="LAK">
				<displayName>Kip Laos</displayName>
				<displayName count="other">Kip Laos</displayName>
			</currency>
			<currency type="LBP">
				<displayName>Pound Lebanon</displayName>
				<displayName count="other">Pound Lebanon</displayName>
			</currency>
			<currency type="LKR">
				<displayName>Rupee Sri Lanka</displayName>
				<displayName count="other">Rupee Sri Lanka</displayName>
			</currency>
			<currency type="LRD">
				<displayName>Dolar Liberia</displayName>
				<displayName count="other">Dolar Liberia</displayName>
			</currency>
			<currency type="LSL">
				<displayName>Loti Lesotho</displayName>
			</currency>
			<currency type="LTL">
				<displayName>Litas Lituania</displayName>
				<displayName count="other">Litas Lituania</displayName>
			</currency>
			<currency type="LTT">
				<displayName>Talonas Lituania</displayName>
			</currency>
			<currency type="LUC">
				<displayName>Franc Konvertibel Luksemburg</displayName>
			</currency>
			<currency type="LUF">
				<displayName>Franc Luksemburg</displayName>
			</currency>
			<currency type="LUL">
				<displayName>Financial Franc Luksemburg</displayName>
			</currency>
			<currency type="LVL">
				<displayName>Lats Latvia</displayName>
				<displayName count="other">Lats Latvia</displayName>
			</currency>
			<currency type="LVR">
				<displayName>Rubel Latvia</displayName>
			</currency>
			<currency type="LYD">
				<displayName>Dinar Libya</displayName>
				<displayName count="other">Dinar Libya</displayName>
			</currency>
			<currency type="MAD">
				<displayName>Dirham Maroko</displayName>
				<displayName count="other">Dirham Maroko</displayName>
			</currency>
			<currency type="MAF">
				<displayName>Franc Maroko</displayName>
			</currency>
			<currency type="MCF">
				<displayName>Franc Monegasque</displayName>
			</currency>
			<currency type="MDC">
				<displayName>Cupon Moldova</displayName>
			</currency>
			<currency type="MDL">
				<displayName>Leu Moldova</displayName>
				<displayName count="other">Leu Moldova</displayName>
			</currency>
			<currency type="MGA">
				<displayName>Ariary Madagaskar</displayName>
				<displayName count="other">Ariary Madagaskar</displayName>
			</currency>
			<currency type="MGF">
				<displayName>Franc Malagasi</displayName>
			</currency>
			<currency type="MKD">
				<displayName>Denar Makedonia</displayName>
				<displayName count="other">Denar Makedonia</displayName>
			</currency>
			<currency type="MKN">
				<displayName>Denar Makedonia (1992–1993)</displayName>
			</currency>
			<currency type="MLF">
				<displayName>Franc Mali</displayName>
			</currency>
			<currency type="MMK">
				<displayName>Kyat Myanmar</displayName>
				<displayName count="other">Kyat Myanmar</displayName>
			</currency>
			<currency type="MNT">
				<displayName>Tugrik Mongolia</displayName>
				<displayName count="other">Tugrik Mongolia</displayName>
			</currency>
			<currency type="MOP">
				<displayName>Pataca Makau</displayName>
				<displayName count="other">Pataca Makau</displayName>
			</currency>
			<currency type="MRO">
				<displayName>Ouguiya Mauritania</displayName>
				<displayName count="other">Ouguiya Mauritania</displayName>
			</currency>
			<currency type="MTL">
				<displayName>Lira Malta</displayName>
			</currency>
			<currency type="MTP">
				<displayName>Pound Malta</displayName>
			</currency>
			<currency type="MUR">
				<displayName>Rupee Mauritius</displayName>
				<displayName count="other">Rupee Mauritius</displayName>
			</currency>
			<currency type="MVR">
				<displayName>Rufiyaa Maladewa</displayName>
				<displayName count="other">Rufiyaa Maladewa</displayName>
			</currency>
			<currency type="MWK">
				<displayName>Kwacha Malawi</displayName>
				<displayName count="other">Kwacha Malawi</displayName>
			</currency>
			<currency type="MXN">
				<displayName>Peso Meksiko</displayName>
				<displayName count="other">Peso Meksiko</displayName>
				<symbol>MX$</symbol>
			</currency>
			<currency type="MXP">
				<displayName>Peso Silver Meksiko (1861–1992)</displayName>
			</currency>
			<currency type="MXV">
				<displayName>Unit Investasi Meksiko</displayName>
			</currency>
			<currency type="MYR">
				<displayName>Ringgit Malaysia</displayName>
				<displayName count="other">Ringgit Malaysia</displayName>
			</currency>
			<currency type="MZE">
				<displayName>Escudo Mozambik</displayName>
			</currency>
			<currency type="MZM">
				<displayName>Metical Mozambik (1980–2006)</displayName>
			</currency>
			<currency type="MZN">
				<displayName>Metical Mozambik</displayName>
				<displayName count="other">Metical Mozambik</displayName>
			</currency>
			<currency type="NAD">
				<displayName>Dolar Namibia</displayName>
				<displayName count="other">Dolar Namibia</displayName>
			</currency>
			<currency type="NGN">
				<displayName>Naira Nigeria</displayName>
				<displayName count="other">Naira Nigeria</displayName>
			</currency>
			<currency type="NIC">
				<displayName>Kordoba Nikaragua (1988–1991)</displayName>
			</currency>
			<currency type="NIO">
				<displayName>Cordoba Nikaragua</displayName>
				<displayName count="other">Cordoba Nikaragua</displayName>
			</currency>
			<currency type="NLG">
				<displayName>Guilder Belanda</displayName>
			</currency>
			<currency type="NOK">
				<displayName>Krone Norwegia</displayName>
				<displayName count="other">Krone Norwegia</displayName>
			</currency>
			<currency type="NPR">
				<displayName>Rupee Nepal</displayName>
				<displayName count="other">Rupee Nepal</displayName>
			</currency>
			<currency type="NZD">
				<displayName>Dolar Selandia Baru</displayName>
				<displayName count="other">Dolar Selandia Baru</displayName>
				<symbol>NZ$</symbol>
			</currency>
			<currency type="OMR">
				<displayName>Rial Oman</displayName>
				<displayName count="other">Rial Oman</displayName>
			</currency>
			<currency type="PAB">
				<displayName>Balboa Panama</displayName>
				<displayName count="other">Balboa Panama</displayName>
			</currency>
			<currency type="PEI">
				<displayName>Inti Peru</displayName>
			</currency>
			<currency type="PEN">
				<displayName>Nuevo Sol Peru</displayName>
				<displayName count="other">Nuevo Sol Peru</displayName>
			</currency>
			<currency type="PES">
				<displayName>Sol Peru (1863–1965)</displayName>
			</currency>
			<currency type="PGK">
				<displayName>Kina Papua Nugini</displayName>
				<displayName count="other">Kina Papua Nugini</displayName>
			</currency>
			<currency type="PHP">
				<displayName>Peso Filipina</displayName>
				<displayName count="other">Peso Filipina</displayName>
			</currency>
			<currency type="PKR">
				<displayName>Rupee Pakistan</displayName>
				<displayName count="other">Rupee Pakistan</displayName>
			</currency>
			<currency type="PLN">
				<displayName>Polandia Zloty</displayName>
				<displayName count="other">Polandia Zloty</displayName>
			</currency>
			<currency type="PLZ">
				<displayName>Zloty Polandia (1950–1995)</displayName>
			</currency>
			<currency type="PTE">
				<displayName>Escudo Portugal</displayName>
			</currency>
			<currency type="PYG">
				<displayName>Guarani Paraguay</displayName>
				<displayName count="other">Guarani Paraguay</displayName>
			</currency>
			<currency type="QAR">
				<displayName>Rial Qatar</displayName>
				<displayName count="other">Rial Qatar</displayName>
			</currency>
			<currency type="RHD">
				<displayName>Dolar Rhodesia</displayName>
			</currency>
			<currency type="ROL">
				<displayName>Leu Rumania (1952–2006)</displayName>
			</currency>
			<currency type="RON">
				<displayName>Leu Rumania</displayName>
				<displayName count="other">Leu Rumania</displayName>
			</currency>
			<currency type="RSD">
				<displayName>Dinar Serbia</displayName>
				<displayName count="other">Dinar Serbia</displayName>
			</currency>
			<currency type="RUB">
				<displayName>Rubel Rusia</displayName>
				<displayName count="other">Rubel Rusia</displayName>
			</currency>
			<currency type="RUR">
				<displayName>Rubel Rusia (1991–1998)</displayName>
			</currency>
			<currency type="RWF">
				<displayName>Franc Rwanda</displayName>
				<displayName count="other">Franc Rwanda</displayName>
			</currency>
			<currency type="SAR">
				<displayName>Riyal Arab Saudi</displayName>
				<displayName count="other">Riyal Arab Saudi</displayName>
			</currency>
			<currency type="SBD">
				<displayName>Dolar Kepulauan Solomon</displayName>
				<displayName count="other">Dolar Kepulauan Solomon</displayName>
			</currency>
			<currency type="SCR">
				<displayName>Rupee Seychelles</displayName>
				<displayName count="other">Rupee Seychelles</displayName>
			</currency>
			<currency type="SDD">
				<displayName>Dinar Sudan (1992–2007)</displayName>
			</currency>
			<currency type="SDG">
				<displayName>Pound Sudan</displayName>
				<displayName count="other">Pound Sudan</displayName>
			</currency>
			<currency type="SDP">
				<displayName>Pound Sudan (1957–1998)</displayName>
			</currency>
			<currency type="SEK">
				<displayName>Krona Swedia</displayName>
				<displayName count="other">Krona Swedia</displayName>
			</currency>
			<currency type="SGD">
				<displayName>Dolar Singapura</displayName>
				<displayName count="other">Dolar Singapura</displayName>
			</currency>
			<currency type="SHP">
				<displayName>Pound Saint Helena</displayName>
				<displayName count="other">Pound Saint Helena</displayName>
			</currency>
			<currency type="SIT">
				<displayName>Tolar Slovenia</displayName>
			</currency>
			<currency type="SKK">
				<displayName>Koruna Slovakia</displayName>
			</currency>
			<currency type="SLL">
				<displayName>Leone Sierra Leone</displayName>
				<displayName count="other">Leone Sierra Leone</displayName>
			</currency>
			<currency type="SOS">
				<displayName>Shilling Somalia</displayName>
				<displayName count="other">Shilling Somalia</displayName>
			</currency>
			<currency type="SRD">
				<displayName>Dolar Suriname</displayName>
				<displayName count="other">Dolar Suriname</displayName>
			</currency>
			<currency type="SRG">
				<displayName>Guilder Suriname</displayName>
			</currency>
			<currency type="SSP">
				<displayName>Pound Sudan Selatan</displayName>
				<displayName count="other">Pound Sudan Selatan</displayName>
			</currency>
			<currency type="STD">
				<displayName>Dobra Sao Tome dan Principe</displayName>
				<displayName count="other">Dobra Sao Tome dan Principe</displayName>
			</currency>
			<currency type="SUR">
				<displayName>Rubel Soviet</displayName>
			</currency>
			<currency type="SVC">
				<displayName>Colon El Savador</displayName>
			</currency>
			<currency type="SYP">
				<displayName>Pound Suriah</displayName>
				<displayName count="other">Pound Suriah</displayName>
			</currency>
			<currency type="SZL">
				<displayName>Lilangeni Swaziland</displayName>
				<displayName count="other">Lilangeni Swaziland</displayName>
			</currency>
			<currency type="THB">
				<displayName>Baht Thailand</displayName>
				<displayName count="other">Baht Thailand</displayName>
				<symbol>฿</symbol>
			</currency>
			<currency type="TJR">
				<displayName>Rubel Tajikistan</displayName>
			</currency>
			<currency type="TJS">
				<displayName>Somoni Tajikistan</displayName>
				<displayName count="other">Somoni Tajikistan</displayName>
			</currency>
			<currency type="TMM">
				<displayName>Manat Turkmenistan (1993–2009)</displayName>
			</currency>
			<currency type="TMT">
				<displayName>Manat Turkimenistan</displayName>
				<displayName count="other">Manat Turkimenistan</displayName>
			</currency>
			<currency type="TND">
				<displayName>Dinar Tunisia</displayName>
				<displayName count="other">Dinar Tunisia</displayName>
			</currency>
			<currency type="TOP">
				<displayName>Paʻanga Tonga</displayName>
				<displayName count="other">Paʻanga Tonga</displayName>
			</currency>
			<currency type="TPE">
				<displayName>Escudo Timor</displayName>
			</currency>
			<currency type="TRL">
				<displayName>Lira Turki (1922–2005)</displayName>
			</currency>
			<currency type="TRY">
				<displayName>Lira Turki</displayName>
				<displayName count="other">Lira Turki</displayName>
			</currency>
			<currency type="TTD">
				<displayName>Dolar Trinidad dan Tobago</displayName>
				<displayName count="other">Dolar Trinidad dan Tobago</displayName>
			</currency>
			<currency type="TWD">
				<displayName>Dolar Baru Taiwan</displayName>
				<symbol>NT$</symbol>
			</currency>
			<currency type="TZS">
				<displayName>Shilling Tanzania</displayName>
				<displayName count="other">Shilling Tanzania</displayName>
			</currency>
			<currency type="UAH">
				<displayName>Hryvnia Ukraina</displayName>
				<displayName count="other">Hryvnia Ukraina</displayName>
			</currency>
			<currency type="UAK">
				<displayName>Karbovanet Ukraina</displayName>
			</currency>
			<currency type="UGS">
				<displayName>Shilling Uganda (1966–1987)</displayName>
			</currency>
			<currency type="UGX">
				<displayName>Shilling Uganda</displayName>
				<displayName count="other">Shilling Uganda</displayName>
			</currency>
			<currency type="USD">
				<displayName>Dolar Amerika Serikat</displayName>
				<displayName count="other">Dolar Amerika Serikat</displayName>
				<symbol>US$</symbol>
			</currency>
			<currency type="USN">
				<displayName>Dolar AS (Hari berikutnya)</displayName>
			</currency>
			<currency type="USS">
				<displayName>Dolar AS (Hari yang sama)</displayName>
			</currency>
			<currency type="UYI">
				<displayName>Peso Uruguay (Unit Diindeks)</displayName>
			</currency>
			<currency type="UYP">
				<displayName>Peso Uruguay (1975–1993)</displayName>
			</currency>
			<currency type="UYU">
				<displayName>Peso Uruguay</displayName>
				<displayName count="other">Peso Uruguay</displayName>
			</currency>
			<currency type="UZS">
				<displayName>Som Uzbekistan</displayName>
				<displayName count="other">Som Uzbekistan</displayName>
			</currency>
			<currency type="VEB">
				<displayName>Bolivar Venezuela (1871–2008)</displayName>
			</currency>
			<currency type="VEF">
				<displayName>Bolivar Venezuela</displayName>
				<displayName count="other">Bolivar Venezuela</displayName>
			</currency>
			<currency type="VND">
				<displayName>Dong Vietnam</displayName>
				<displayName count="other">Dong Vietnam</displayName>
				<symbol>₫</symbol>
			</currency>
			<currency type="VNN">
				<displayName>Dong Vietnam (1978–1985)</displayName>
			</currency>
			<currency type="VUV">
				<displayName>Vatu Vanuatu</displayName>
				<displayName count="other">Vatu Vanuatu</displayName>
			</currency>
			<currency type="WST">
				<displayName>Tala Samoa</displayName>
				<displayName count="other">Tala Samoa</displayName>
			</currency>
			<currency type="XAF">
				<displayName>Franc CFA BEAC</displayName>
				<displayName count="other">Franc CFA BEAC</displayName>
				<symbol>FCFA</symbol>
			</currency>
			<currency type="XAG">
				<displayName>Silver</displayName>
			</currency>
			<currency type="XAU">
				<displayName>Emas</displayName>
			</currency>
			<currency type="XBA">
				<displayName>Unit Gabungan Eropa</displayName>
			</currency>
			<currency type="XBB">
				<displayName>Unit Keuangan Eropa</displayName>
			</currency>
			<currency type="XBC">
				<displayName>Satuan Hitung Eropa (XBC)</displayName>
			</currency>
			<currency type="XBD">
				<displayName>Satuan Hitung Eropa (XBD)</displayName>
			</currency>
			<currency type="XCD">
				<displayName>Dolar Karibia Timur</displayName>
				<displayName count="other">Dolar Karibia Timur</displayName>
				<symbol>EC$</symbol>
			</currency>
			<currency type="XDR">
				<displayName>Hak Khusus Menggambar</displayName>
			</currency>
			<currency type="XEU">
				<displayName>Satuan Mata Uang Eropa</displayName>
			</currency>
			<currency type="XFO">
				<displayName>Franc Gold Perancis</displayName>
			</currency>
			<currency type="XFU">
				<displayName>Franc UIC Perancis</displayName>
			</currency>
			<currency type="XOF">
				<displayName>Franc CFA BCEAO</displayName>
				<displayName count="other">Franc CFA BCEAO</displayName>
				<symbol>CFA</symbol>
			</currency>
			<currency type="XPD">
				<displayName>Palladium</displayName>
			</currency>
			<currency type="XPF">
				<displayName>Franc CFP</displayName>
				<displayName count="other">Franc CFP</displayName>
				<symbol>CFPF</symbol>
			</currency>
			<currency type="XPT">
				<displayName>Platinum</displayName>
			</currency>
			<currency type="XRE">
				<displayName>Dana RINET</displayName>
			</currency>
			<currency type="XTS">
				<displayName>Kode Mata Uang Pengujian</displayName>
			</currency>
			<currency type="XXX">
				<displayName>Mata Uang Tidak Dikenal</displayName>
				<displayName count="other">Mata Uang Tidak Dikenal</displayName>
			</currency>
			<currency type="YDD">
				<displayName>Dinar Yaman</displayName>
			</currency>
			<currency type="YER">
				<displayName>Rial Yaman</displayName>
				<displayName count="other">Rial Yaman</displayName>
			</currency>
			<currency type="YUD">
				<displayName>Hard Dinar Yugoslavia (1966–1990)</displayName>
			</currency>
			<currency type="YUM">
				<displayName>Dinar Baru Yugoslavia (1994–2002)</displayName>
			</currency>
			<currency type="YUN">
				<displayName>Dinar Konvertibel Yugoslavia (1990–1992)</displayName>
			</currency>
			<currency type="YUR">
				<displayName>Dinar Reformasi Yugoslavia (1992–1993)</displayName>
			</currency>
			<currency type="ZAL">
				<displayName>Rand Afrika Selatan (Keuangan)</displayName>
			</currency>
			<currency type="ZAR">
				<displayName>Rand Afrika Selatan</displayName>
				<displayName count="other">Rand Afrika Selatan</displayName>
			</currency>
			<currency type="ZMK">
				<displayName>Kwacha Zambia (1968–2012)</displayName>
			</currency>
			<currency type="ZMW">
				<displayName>Kwacha Zambia</displayName>
				<displayName count="other">Kwacha Zambia</displayName>
			</currency>
			<currency type="ZRN">
				<displayName>Zaire Baru Zaire (1993–1998)</displayName>
			</currency>
			<currency type="ZRZ">
				<displayName>Zaire Zaire (1971–1993)</displayName>
			</currency>
			<currency type="ZWD">
				<displayName>Dolar Zimbabwe (1980–2008)</displayName>
			</currency>
			<currency type="ZWL">
				<displayName>Dolar Zimbabwe (2009)</displayName>
			</currency>
			<currency type="ZWR">
				<displayName>Dolar Zimbabwe (2008)</displayName>
			</currency>
		</currencies>
		<miscPatterns numberSystem="latn">
			<pattern type="atLeast">{0}+</pattern>
			<pattern type="range">{0}–{1}</pattern>
		</miscPatterns>
	</numbers>
	<units>
		<unitLength type="long">
			<compoundUnit type="per">
				<compoundUnitPattern>{0} per {1}</compoundUnitPattern>
			</compoundUnit>
			<unit type="acceleration-g-force">
				<unitPattern count="other">{0} g-force</unitPattern>
			</unit>
			<unit type="angle-arc-minute">
				<unitPattern count="other">{0} menit</unitPattern>
			</unit>
			<unit type="angle-arc-second">
				<unitPattern count="other">{0} detik</unitPattern>
			</unit>
			<unit type="angle-degree">
				<unitPattern count="other">{0} derajat</unitPattern>
			</unit>
			<unit type="area-acre">
				<unitPattern count="other">{0} acre</unitPattern>
			</unit>
			<unit type="area-hectare">
				<unitPattern count="other">{0} hektar</unitPattern>
			</unit>
			<unit type="area-square-foot">
				<unitPattern count="other">{0} kaki persegi</unitPattern>
			</unit>
			<unit type="area-square-kilometer">
				<unitPattern count="other">{0} kilometer persegi</unitPattern>
			</unit>
			<unit type="area-square-meter">
				<unitPattern count="other">{0} meter persegi</unitPattern>
			</unit>
			<unit type="area-square-mile">
				<unitPattern count="other">{0} mil persegi</unitPattern>
			</unit>
			<unit type="duration-day">
				<unitPattern count="other">{0} hari</unitPattern>
			</unit>
			<unit type="duration-hour">
				<unitPattern count="other">{0} jam</unitPattern>
			</unit>
			<unit type="duration-millisecond">
				<unitPattern count="other">{0} milidetik</unitPattern>
			</unit>
			<unit type="duration-minute">
				<unitPattern count="other">{0} menit</unitPattern>
			</unit>
			<unit type="duration-month">
				<unitPattern count="other">{0} bulan</unitPattern>
			</unit>
			<unit type="duration-second">
				<unitPattern count="other">{0} detik</unitPattern>
			</unit>
			<unit type="duration-week">
				<unitPattern count="other">{0} minggu</unitPattern>
			</unit>
			<unit type="duration-year">
				<unitPattern count="other">{0} tahun</unitPattern>
			</unit>
			<unit type="length-centimeter">
				<unitPattern count="other">{0} sentimeter</unitPattern>
			</unit>
			<unit type="length-foot">
				<unitPattern count="other">{0} kaki</unitPattern>
			</unit>
			<unit type="length-inch">
				<unitPattern count="other">{0} inci</unitPattern>
			</unit>
			<unit type="length-kilometer">
				<unitPattern count="other">{0} kilometer</unitPattern>
			</unit>
			<unit type="length-light-year">
				<unitPattern count="other">{0} tahun cahaya</unitPattern>
			</unit>
			<unit type="length-meter">
				<unitPattern count="other">{0} meter</unitPattern>
			</unit>
			<unit type="length-mile">
				<unitPattern count="other">{0} mil</unitPattern>
			</unit>
			<unit type="length-millimeter">
				<unitPattern count="other">{0} milimeter</unitPattern>
			</unit>
			<unit type="length-picometer">
				<unitPattern count="other">{0} pikometer</unitPattern>
			</unit>
			<unit type="length-yard">
				<unitPattern count="other">{0} yard</unitPattern>
			</unit>
			<unit type="mass-gram">
				<unitPattern count="other">{0} gram</unitPattern>
			</unit>
			<unit type="mass-kilogram">
				<unitPattern count="other">{0} kilogram</unitPattern>
			</unit>
			<unit type="mass-ounce">
				<unitPattern count="other">{0} ons</unitPattern>
			</unit>
			<unit type="mass-pound">
				<unitPattern count="other">{0} pon</unitPattern>
			</unit>
			<unit type="power-horsepower">
				<unitPattern count="other">{0} daya kuda</unitPattern>
			</unit>
			<unit type="power-kilowatt">
				<unitPattern count="other">{0} kilowatt</unitPattern>
			</unit>
			<unit type="power-watt">
				<unitPattern count="other">{0} watt</unitPattern>
			</unit>
			<unit type="pressure-hectopascal">
				<unitPattern count="other">{0} hektopaskal</unitPattern>
			</unit>
			<unit type="pressure-inch-hg">
				<unitPattern count="other">{0} inci merkuri</unitPattern>
			</unit>
			<unit type="pressure-millibar">
				<unitPattern count="other">{0} milibar</unitPattern>
			</unit>
			<unit type="speed-kilometer-per-hour">
				<unitPattern count="other">{0} kilometer per jam</unitPattern>
			</unit>
			<unit type="speed-meter-per-second">
				<unitPattern count="other">{0} meter per detik</unitPattern>
			</unit>
			<unit type="speed-mile-per-hour">
				<unitPattern count="other">{0} mil per jam</unitPattern>
			</unit>
			<unit type="temperature-celsius">
				<unitPattern count="other">{0} derajat Celsius</unitPattern>
			</unit>
			<unit type="temperature-fahrenheit">
				<unitPattern count="other">{0} derajat Fahrenheit</unitPattern>
			</unit>
			<unit type="volume-cubic-kilometer">
				<unitPattern count="other">{0} kilometer kubik</unitPattern>
			</unit>
			<unit type="volume-cubic-mile">
				<unitPattern count="other">{0} mil kubik</unitPattern>
			</unit>
			<unit type="volume-liter">
				<unitPattern count="other">{0} liter</unitPattern>
			</unit>
		</unitLength>
		<unitLength type="short">
			<compoundUnit type="per">
				<compoundUnitPattern>{0}/{1}</compoundUnitPattern>
			</compoundUnit>
			<unit type="acceleration-g-force">
				<unitPattern count="other">{0} G</unitPattern>
			</unit>
			<unit type="angle-arc-minute">
				<unitPattern count="other">{0} mnt</unitPattern>
			</unit>
			<unit type="angle-arc-second">
				<unitPattern count="other">{0} dtk</unitPattern>
			</unit>
			<unit type="angle-degree">
				<unitPattern count="other">{0}°</unitPattern>
			</unit>
			<unit type="area-acre">
				<unitPattern count="other">{0} ac</unitPattern>
			</unit>
			<unit type="area-hectare">
				<unitPattern count="other">{0} ha</unitPattern>
			</unit>
			<unit type="area-square-foot">
				<unitPattern count="other">{0} ft²</unitPattern>
			</unit>
			<unit type="area-square-kilometer">
				<unitPattern count="other">{0} km²</unitPattern>
			</unit>
			<unit type="area-square-meter">
				<unitPattern count="other">{0} m²</unitPattern>
			</unit>
			<unit type="area-square-mile">
				<unitPattern count="other">{0} mi²</unitPattern>
			</unit>
			<unit type="duration-day">
				<unitPattern count="other">{0} hr</unitPattern>
			</unit>
			<unit type="duration-hour">
				<unitPattern count="other">{0} j</unitPattern>
			</unit>
			<unit type="duration-millisecond">
				<unitPattern count="other">{0} md</unitPattern>
			</unit>
			<unit type="duration-minute">
				<unitPattern count="other">{0} mnt</unitPattern>
			</unit>
			<unit type="duration-month">
				<unitPattern count="other">{0} bln</unitPattern>
			</unit>
			<unit type="duration-second">
				<unitPattern count="other">{0} dtk</unitPattern>
			</unit>
			<unit type="duration-week">
				<unitPattern count="other">{0} mgg</unitPattern>
			</unit>
			<unit type="duration-year">
				<unitPattern count="other">{0} thn</unitPattern>
			</unit>
			<unit type="length-centimeter">
				<unitPattern count="other">{0} cm</unitPattern>
			</unit>
			<unit type="length-foot">
				<unitPattern count="other">{0} ft</unitPattern>
			</unit>
			<unit type="length-inch">
				<unitPattern count="other">{0} in</unitPattern>
			</unit>
			<unit type="length-kilometer">
				<unitPattern count="other">{0} km</unitPattern>
			</unit>
			<unit type="length-light-year">
				<unitPattern count="other">{0} ly</unitPattern>
			</unit>
			<unit type="length-meter">
				<unitPattern count="other">{0} m</unitPattern>
			</unit>
			<unit type="length-mile">
				<unitPattern count="other">{0} mi</unitPattern>
			</unit>
			<unit type="length-millimeter">
				<unitPattern count="other">{0} mm</unitPattern>
			</unit>
			<unit type="length-picometer">
				<unitPattern count="other">{0} pm</unitPattern>
			</unit>
			<unit type="length-yard">
				<unitPattern count="other">{0} yd</unitPattern>
			</unit>
			<unit type="mass-gram">
				<unitPattern count="other">{0} g</unitPattern>
			</unit>
			<unit type="mass-kilogram">
				<unitPattern count="other">{0} kg</unitPattern>
			</unit>
			<unit type="mass-ounce">
				<unitPattern count="other">{0} oz</unitPattern>
			</unit>
			<unit type="mass-pound">
				<unitPattern count="other">{0} lb</unitPattern>
			</unit>
			<unit type="power-horsepower">
				<unitPattern count="other">{0} hp</unitPattern>
			</unit>
			<unit type="power-kilowatt">
				<unitPattern count="other">{0} kW</unitPattern>
			</unit>
			<unit type="power-watt">
				<unitPattern count="other">{0} W</unitPattern>
			</unit>
			<unit type="pressure-hectopascal">
				<unitPattern count="other">{0} hPa</unitPattern>
			</unit>
			<unit type="pressure-inch-hg">
				<unitPattern count="other">{0} inHg</unitPattern>
			</unit>
			<unit type="pressure-millibar">
				<unitPattern count="other">{0} mbar</unitPattern>
			</unit>
			<unit type="speed-kilometer-per-hour">
				<unitPattern count="other">{0} km/jam</unitPattern>
			</unit>
			<unit type="speed-meter-per-second">
				<unitPattern count="other">{0} m/s</unitPattern>
			</unit>
			<unit type="speed-mile-per-hour">
				<unitPattern count="other">{0} mph</unitPattern>
			</unit>
			<unit type="temperature-celsius">
				<unitPattern count="other">{0}°C</unitPattern>
			</unit>
			<unit type="temperature-fahrenheit">
				<unitPattern count="other">{0}°F</unitPattern>
			</unit>
			<unit type="volume-cubic-kilometer">
				<unitPattern count="other">{0} km³</unitPattern>
			</unit>
			<unit type="volume-cubic-mile">
				<unitPattern count="other">{0} mi³</unitPattern>
			</unit>
			<unit type="volume-liter">
				<unitPattern count="other">{0} l</unitPattern>
			</unit>
		</unitLength>
		<unitLength type="narrow">
			<compoundUnit type="per">
				<compoundUnitPattern>{0}/{1}</compoundUnitPattern>
			</compoundUnit>
			<unit type="acceleration-g-force">
				<unitPattern count="other">{0} G</unitPattern>
			</unit>
			<unit type="angle-arc-minute">
				<unitPattern count="other">{0}′</unitPattern>
			</unit>
			<unit type="angle-arc-second">
				<unitPattern count="other">{0}″</unitPattern>
			</unit>
			<unit type="angle-degree">
				<unitPattern count="other">{0}°</unitPattern>
			</unit>
			<unit type="area-acre">
				<unitPattern count="other">{0} ac</unitPattern>
			</unit>
			<unit type="area-hectare">
				<unitPattern count="other">{0} ha</unitPattern>
			</unit>
			<unit type="area-square-foot">
				<unitPattern count="other">{0} ft²</unitPattern>
			</unit>
			<unit type="area-square-kilometer">
				<unitPattern count="other">{0} km²</unitPattern>
			</unit>
			<unit type="area-square-meter">
				<unitPattern count="other">{0} m²</unitPattern>
			</unit>
			<unit type="area-square-mile">
				<unitPattern count="other">{0} mi²</unitPattern>
			</unit>
			<unit type="duration-day">
				<unitPattern count="other">{0} hr</unitPattern>
			</unit>
			<unit type="duration-hour">
				<unitPattern count="other">{0} j</unitPattern>
			</unit>
			<unit type="duration-millisecond">
				<unitPattern count="other">{0} md</unitPattern>
			</unit>
			<unit type="duration-minute">
				<unitPattern count="other">{0} mnt</unitPattern>
			</unit>
			<unit type="duration-month">
				<unitPattern count="other">{0} bln</unitPattern>
			</unit>
			<unit type="duration-second">
				<unitPattern count="other">{0} dtk</unitPattern>
			</unit>
			<unit type="duration-week">
				<unitPattern count="other">{0} mgg</unitPattern>
			</unit>
			<unit type="duration-year">
				<unitPattern count="other">{0} thn</unitPattern>
			</unit>
			<unit type="length-centimeter">
				<unitPattern count="other">{0} cm</unitPattern>
			</unit>
			<unit type="length-foot">
				<unitPattern count="other">{0} ft</unitPattern>
			</unit>
			<unit type="length-inch">
				<unitPattern count="other">{0}″</unitPattern>
			</unit>
			<unit type="length-kilometer">
				<unitPattern count="other">{0} km</unitPattern>
			</unit>
			<unit type="length-light-year">
				<unitPattern count="other">{0} ly</unitPattern>
			</unit>
			<unit type="length-meter">
				<unitPattern count="other">{0} m</unitPattern>
			</unit>
			<unit type="length-mile">
				<unitPattern count="other">{0} mi</unitPattern>
			</unit>
			<unit type="length-millimeter">
				<unitPattern count="other">{0} mm</unitPattern>
			</unit>
			<unit type="length-picometer">
				<unitPattern count="other">{0} pm</unitPattern>
			</unit>
			<unit type="length-yard">
				<unitPattern count="other">{0} yd</unitPattern>
			</unit>
			<unit type="mass-gram">
				<unitPattern count="other">{0} g</unitPattern>
			</unit>
			<unit type="mass-kilogram">
				<unitPattern count="other">{0} kg</unitPattern>
			</unit>
			<unit type="mass-ounce">
				<unitPattern count="other">{0} oz</unitPattern>
			</unit>
			<unit type="mass-pound">
				<unitPattern count="other">{0} lb</unitPattern>
			</unit>
			<unit type="power-horsepower">
				<unitPattern count="other">{0} hp</unitPattern>
			</unit>
			<unit type="power-kilowatt">
				<unitPattern count="other">{0} kW</unitPattern>
			</unit>
			<unit type="power-watt">
				<unitPattern count="other">{0} W</unitPattern>
			</unit>
			<unit type="pressure-hectopascal">
				<unitPattern count="other">{0} hPa</unitPattern>
			</unit>
			<unit type="pressure-inch-hg">
				<unitPattern count="other">{0} inHg</unitPattern>
			</unit>
			<unit type="pressure-millibar">
				<unitPattern count="other">{0} mbar</unitPattern>
			</unit>
			<unit type="speed-kilometer-per-hour">
				<unitPattern count="other">{0} km/jam</unitPattern>
			</unit>
			<unit type="speed-meter-per-second">
				<unitPattern count="other">{0} m/s</unitPattern>
			</unit>
			<unit type="speed-mile-per-hour">
				<unitPattern count="other">{0} mph</unitPattern>
			</unit>
			<unit type="temperature-celsius">
				<unitPattern count="other">{0}°</unitPattern>
			</unit>
			<unit type="temperature-fahrenheit">
				<unitPattern count="other">{0}°F</unitPattern>
			</unit>
			<unit type="volume-cubic-kilometer">
				<unitPattern count="other">{0} km³</unitPattern>
			</unit>
			<unit type="volume-cubic-mile">
				<unitPattern count="other">{0} mi³</unitPattern>
			</unit>
			<unit type="volume-liter">
				<unitPattern count="other">{0} l</unitPattern>
			</unit>
		</unitLength>
		<durationUnit type="hm">
			<durationUnitPattern>h.mm</durationUnitPattern>
		</durationUnit>
		<durationUnit type="hms">
			<durationUnitPattern>h.mm.ss</durationUnitPattern>
		</durationUnit>
		<durationUnit type="ms">
			<durationUnitPattern>m.ss</durationUnitPattern>
		</durationUnit>
	</units>
	<listPatterns>
		<listPattern>
			<listPatternPart type="start">{0}, {1}</listPatternPart>
			<listPatternPart type="middle">{0}, {1}</listPatternPart>
			<listPatternPart type="end">{0}, dan {1}</listPatternPart>
			<listPatternPart type="2">{0} dan {1}</listPatternPart>
		</listPattern>
		<listPattern type="unit">
			<listPatternPart type="start">{0}, {1}</listPatternPart>
			<listPatternPart type="middle">{0}, {1}</listPatternPart>
			<listPatternPart type="end">{0}, {1}</listPatternPart>
			<listPatternPart type="2">{0}, {1}</listPatternPart>
		</listPattern>
		<listPattern type="unit-narrow">
			<listPatternPart type="start">{0}, {1}</listPatternPart>
			<listPatternPart type="middle">{0}, {1}</listPatternPart>
			<listPatternPart type="end">{0}, {1}</listPatternPart>
			<listPatternPart type="2">{0}, {1}</listPatternPart>
		</listPattern>
		<listPattern type="unit-short">
			<listPatternPart type="start">{0}, {1}</listPatternPart>
			<listPatternPart type="middle">{0}, {1}</listPatternPart>
			<listPatternPart type="end">{0}, {1}</listPatternPart>
			<listPatternPart type="2">{0}, {1}</listPatternPart>
		</listPattern>
	</listPatterns>
	<posix>
		<messages>
			<yesstr>ya:y</yesstr>
			<nostr>tidak:t</nostr>
		</messages>
	</posix>
</ldml>

