<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE ldml SYSTEM "../../common/dtd/ldml.dtd">
<!-- Copyright © 1991-2013 Unicode, Inc.
CLDR data files are interpreted according to the LDML specification (http://unicode.org/reports/tr35/)
For terms of use, see http://www.unicode.org/copyright.html
-->
<ldml>
	<identity>
		<version number="$Revision: 9791 $"/>
		<generation date="$Date: 2014-02-25 15:16:49 -0600 (Tue, 25 Feb 2014) $"/>
		<language type="se"/>
	</identity>
	<localeDisplayNames>
		<languages>
			<language type="ace" draft="unconfirmed">acehgiella</language>
			<language type="af" draft="unconfirmed">afrikánsagiella</language>
			<language type="an" draft="unconfirmed">aragoniagiella</language>
			<language type="ang" draft="unconfirmed">boares eaŋgalasgiella</language>
			<language type="ar" draft="unconfirmed">arábagiella</language>
			<language type="ast" draft="unconfirmed">asturiagiella</language>
			<language type="be" draft="unconfirmed">vilges-ruoššagiella</language>
			<language type="bg" draft="unconfirmed">bulgáriagiella</language>
			<language type="bn" draft="unconfirmed">bengalgiella</language>
			<language type="bo" draft="unconfirmed">tibetagiella</language>
			<language type="br" draft="unconfirmed">bretonagiella</language>
			<language type="bs" draft="unconfirmed">bosniagiella</language>
			<language type="ca" draft="unconfirmed">katalánagiella</language>
			<language type="chm" draft="unconfirmed">marigiella</language>
			<language type="co" draft="unconfirmed">corsicagiella</language>
			<language type="cs" draft="unconfirmed">čeahkagiella</language>
			<language type="cy" draft="unconfirmed">kymragiella</language>
			<language type="da" draft="unconfirmed">dánskkagiella</language>
			<language type="de" draft="unconfirmed">duiskkagiella</language>
			<language type="dv" draft="unconfirmed">divehigiella</language>
			<language type="dz" draft="unconfirmed">dzongkhagiella</language>
			<language type="el" draft="unconfirmed">greikkagiella</language>
			<language type="en" draft="unconfirmed">eaŋgalsgiella</language>
			<language type="es" draft="unconfirmed">spánskkagiella</language>
			<language type="et" draft="unconfirmed">esttegiella</language>
			<language type="fa" draft="unconfirmed">persijagiella</language>
			<language type="fi" draft="unconfirmed">suomagiella</language>
			<language type="fil" draft="unconfirmed">filippiinnagiella</language>
			<language type="fj" draft="unconfirmed">fidjigiella</language>
			<language type="fo" draft="unconfirmed">fearagiella</language>
			<language type="fr" draft="unconfirmed">fránskkagiella</language>
			<language type="fy" draft="unconfirmed">oarjifriisagiella</language>
			<language type="ga" draft="unconfirmed">iirragiella</language>
			<language type="gu" draft="unconfirmed">gujaratagiella</language>
			<language type="gv" draft="unconfirmed">manksgiella</language>
			<language type="ha" draft="unconfirmed">haussagiella</language>
			<language type="haw" draft="unconfirmed">hawaiigiella</language>
			<language type="hi" draft="unconfirmed">hindigiella</language>
			<language type="hr" draft="unconfirmed">kroátiagiella</language>
			<language type="ht" draft="unconfirmed">haitigiella</language>
			<language type="hu" draft="unconfirmed">ungárgiella</language>
			<language type="hy" draft="unconfirmed">armeenagiella</language>
			<language type="id" draft="unconfirmed">indonesiagiella</language>
			<language type="is" draft="unconfirmed">islánddagiella</language>
			<language type="it" draft="unconfirmed">itáliagiella</language>
			<language type="ja" draft="unconfirmed">japánagiella</language>
			<language type="jv" draft="unconfirmed">javagiella</language>
			<language type="ka" draft="unconfirmed">georgiagiella</language>
			<language type="kk" draft="unconfirmed">kazakgiella</language>
			<language type="km" draft="unconfirmed">kambodiagiella</language>
			<language type="ko" draft="unconfirmed">koreagiella</language>
			<language type="krl" draft="unconfirmed">gárjilgiella</language>
			<language type="ku" draft="unconfirmed">kurdigiella</language>
			<language type="kv" draft="unconfirmed">komigiella</language>
			<language type="kw" draft="unconfirmed">kornagiella</language>
			<language type="la" draft="unconfirmed">láhtengiella</language>
			<language type="lb" draft="unconfirmed">luxemburggagiella</language>
			<language type="lo" draft="unconfirmed">laogiella</language>
			<language type="lt" draft="unconfirmed">liettuvagiella</language>
			<language type="lv" draft="unconfirmed">látviagiella</language>
			<language type="mdf" draft="unconfirmed">mokšagiella</language>
			<language type="mi" draft="unconfirmed">maorigiella</language>
			<language type="mk" draft="unconfirmed">makedoniagiella</language>
			<language type="mn" draft="unconfirmed">mongoliagiella</language>
			<language type="mt" draft="unconfirmed">maltagiella</language>
			<language type="my" draft="unconfirmed">burmagiella</language>
			<language type="myv" draft="unconfirmed">ersagiella</language>
			<language type="nb" draft="unconfirmed">girjedárogiella</language>
			<language type="ne" draft="unconfirmed">nepaligiella</language>
			<language type="nl" draft="unconfirmed">hollánddagiella</language>
			<language type="nn" draft="unconfirmed">ođđadárogiella</language>
			<language type="no" draft="unconfirmed">dárogiella</language>
			<language type="oc" draft="unconfirmed">oksitánagiella</language>
			<language type="pa" draft="unconfirmed">panjabigiella</language>
			<language type="pl" draft="unconfirmed">polskkagiella</language>
			<language type="pt" draft="unconfirmed">portugálagiella</language>
			<language type="rm" draft="unconfirmed">romanšgiella</language>
			<language type="ro" draft="unconfirmed">romániagiella</language>
			<language type="ru" draft="unconfirmed">ruoššagiella</language>
			<language type="sc" draft="unconfirmed">sardigiella</language>
			<language type="scn" draft="unconfirmed">sisiliagiella</language>
			<language type="se" draft="unconfirmed">davvisámegiella</language>
			<language type="sel" draft="unconfirmed">selkupagiella</language>
			<language type="sh" draft="unconfirmed">serbokroatiagiella</language>
			<language type="sk" draft="unconfirmed">slovákiagiella</language>
			<language type="sl" draft="unconfirmed">slovenagiella</language>
			<language type="sm" draft="unconfirmed">samoagiella</language>
			<language type="sma" draft="unconfirmed">lullisámegiella</language>
			<language type="smj" draft="unconfirmed">julevsámegiella</language>
			<language type="smn" draft="unconfirmed">anárašgiella</language>
			<language type="sms" draft="unconfirmed">nuortalašgiella</language>
			<language type="sq" draft="unconfirmed">albánagiella</language>
			<language type="sr" draft="unconfirmed">serbiagiella</language>
			<language type="sv" draft="unconfirmed">ruoŧagiella</language>
			<language type="swb" draft="unconfirmed">shimaorigiella</language>
			<language type="th" draft="unconfirmed">ŧaigiella</language>
			<language type="tr" draft="unconfirmed">durkagiella</language>
			<language type="ty" draft="unconfirmed">tahitigiella</language>
			<language type="udm" draft="unconfirmed">udmurtagiella</language>
			<language type="uk" draft="unconfirmed">ukrainagiella</language>
			<language type="und" draft="unconfirmed">dovdameahttun giella</language>
			<language type="ur" draft="unconfirmed">urdugiella</language>
			<language type="vi" draft="unconfirmed">vietnamgiella</language>
			<language type="wa" draft="unconfirmed">vallonagiella</language>
			<language type="yue" draft="unconfirmed">kantongiella</language>
			<language type="zh" draft="unconfirmed">kiinnágiella</language>
			<language type="zh_Hans" draft="unconfirmed">álki kiinágiella</language>
			<language type="zh_Hant" draft="unconfirmed">árbevirolaš kiinnágiella</language>
		</languages>
		<scripts>
			<script type="Arab" draft="unconfirmed">arába</script>
			<script type="Cyrl" draft="unconfirmed">kyrillalaš</script>
			<script type="Grek" draft="unconfirmed">greikkalaš</script>
			<script type="Hang" draft="unconfirmed">hangul</script>
			<script type="Hani" draft="unconfirmed">kiinnaš</script>
			<script type="Hans" draft="unconfirmed">álki</script>
			<script type="Hant" draft="unconfirmed">árbevirolaš</script>
			<script type="Hira" draft="unconfirmed">hiragana</script>
			<script type="Kana" draft="unconfirmed">katakana</script>
			<script type="Latn" draft="unconfirmed">láhtenaš</script>
			<script type="Zxxx" draft="unconfirmed">orrut chállojuvvot</script>
			<script type="Zzzz" draft="unconfirmed">dovdameahttun chállin</script>
		</scripts>
		<territories>
			<territory type="001" draft="unconfirmed">máilbmi</territory>
			<territory type="002" draft="unconfirmed">Afrihkká</territory>
			<territory type="003">dávvi-Amerihkká ja gaska-Amerihkká</territory>
			<territory type="005" draft="unconfirmed">mátta-Amerihkká</territory>
			<territory type="009" draft="unconfirmed">Oseania</territory>
			<territory type="011" draft="unconfirmed">oarji-Afrihkká</territory>
			<territory type="013" draft="unconfirmed">gaska-Amerihkká</territory>
			<territory type="014" draft="unconfirmed">nuorta-Afrihkká</territory>
			<territory type="015" draft="unconfirmed">davvi-Afrihkká</territory>
			<territory type="017" draft="unconfirmed">gaska-Afrihkká</territory>
			<territory type="018" draft="unconfirmed">mátta-Afrihkká</territory>
			<territory type="019" draft="unconfirmed">Amerihkká</territory>
			<territory type="021">dávvi-Amerihkká</territory>
			<territory type="029" draft="unconfirmed">Karibia</territory>
			<territory type="030" draft="unconfirmed">nuorta-Ásia</territory>
			<territory type="034" draft="unconfirmed">mátta-Ásia</territory>
			<territory type="035" draft="unconfirmed">mátta-nuorta-Ásia</territory>
			<territory type="039" draft="unconfirmed">mátta-Eurohpá</territory>
			<territory type="053" draft="unconfirmed">Austrália ja Ođđa-Selánda</territory>
			<territory type="054" draft="unconfirmed">Melanesia</territory>
			<territory type="057" draft="unconfirmed">Mikronesia guovllus</territory>
			<territory type="061" draft="unconfirmed">Polynesia</territory>
			<territory type="142" draft="unconfirmed">Ásia</territory>
			<territory type="143" draft="unconfirmed">gaska-Ásia</territory>
			<territory type="145" draft="unconfirmed">oarji-Ásia</territory>
			<territory type="150" draft="unconfirmed">Eurohpá</territory>
			<territory type="151" draft="unconfirmed">nuorta-Eurohpá</territory>
			<territory type="154" draft="unconfirmed">davvi-Eurohpá</territory>
			<territory type="155" draft="unconfirmed">oarji-Eurohpá</territory>
			<territory type="419" draft="unconfirmed">lulli-Amerihkká</territory>
			<territory type="AC" draft="unconfirmed">Ascension</territory>
			<territory type="AD" draft="unconfirmed">Andorra</territory>
			<territory type="AE" draft="unconfirmed">Ovttastuvvan Arábaemiráhtat</territory>
			<territory type="AF" draft="unconfirmed">Afghanistan</territory>
			<territory type="AG" draft="unconfirmed">Antigua ja Barbuda</territory>
			<territory type="AI" draft="unconfirmed">Anguilla</territory>
			<territory type="AL" draft="unconfirmed">Albánia</territory>
			<territory type="AM" draft="unconfirmed">Armenia</territory>
			<territory type="AO" draft="unconfirmed">Angola</territory>
			<territory type="AQ" draft="unconfirmed">Antárktis</territory>
			<territory type="AR" draft="unconfirmed">Argentina</territory>
			<territory type="AS" draft="unconfirmed">Amerihká Samoa</territory>
			<territory type="AT" draft="unconfirmed">Nuortariika</territory>
			<territory type="AU" draft="unconfirmed">Austrália</territory>
			<territory type="AW" draft="unconfirmed">Aruba</territory>
			<territory type="AX" draft="unconfirmed">Ålánda</territory>
			<territory type="AZ" draft="unconfirmed">Aserbaižan</territory>
			<territory type="BA" draft="unconfirmed">Bosnia-Hercegovina</territory>
			<territory type="BB" draft="unconfirmed">Barbados</territory>
			<territory type="BD" draft="unconfirmed">Bangladesh</territory>
			<territory type="BE" draft="unconfirmed">Belgia</territory>
			<territory type="BF" draft="unconfirmed">Burkina Faso</territory>
			<territory type="BG" draft="unconfirmed">Bulgária</territory>
			<territory type="BH" draft="unconfirmed">Bahrain</territory>
			<territory type="BI" draft="unconfirmed">Burundi</territory>
			<territory type="BJ" draft="unconfirmed">Benin</territory>
			<territory type="BL" draft="unconfirmed">Saint Barthélemy</territory>
			<territory type="BM" draft="unconfirmed">Bermuda</territory>
			<territory type="BN" draft="unconfirmed">Brunei</territory>
			<territory type="BO" draft="unconfirmed">Bolivia</territory>
			<territory type="BR" draft="unconfirmed">Brasil</territory>
			<territory type="BS" draft="unconfirmed">Bahamas</territory>
			<territory type="BT" draft="unconfirmed">Bhutan</territory>
			<territory type="BV" draft="unconfirmed">Bouvet-sullot</territory>
			<territory type="BW" draft="unconfirmed">Botswana</territory>
			<territory type="BY" draft="unconfirmed">Vilges-Ruošša</territory>
			<territory type="BZ" draft="unconfirmed">Belize</territory>
			<territory type="CA" draft="unconfirmed">Kanáda</territory>
			<territory type="CC" draft="unconfirmed">Cocos-sullot</territory>
			<territory type="CD" draft="unconfirmed">Kongo-Kinshasa</territory>
			<territory type="CF" draft="unconfirmed">Gaska-Afrihká dásseváldi</territory>
			<territory type="CG" draft="unconfirmed">Kongo-Brazzaville</territory>
			<territory type="CH" draft="unconfirmed">Šveica</territory>
			<territory type="CI" draft="unconfirmed">Elfenbenariddu</territory>
			<territory type="CK" draft="unconfirmed">Cook-sullot</territory>
			<territory type="CL" draft="unconfirmed">Čiile</territory>
			<territory type="CM" draft="unconfirmed">Kamerun</territory>
			<territory type="CN" draft="unconfirmed">Kiinná</territory>
			<territory type="CO" draft="unconfirmed">Kolombia</territory>
			<territory type="CP" draft="unconfirmed">Clipperton-sullot</territory>
			<territory type="CR" draft="unconfirmed">Costa Rica</territory>
			<territory type="CU" draft="unconfirmed">Kuba</territory>
			<territory type="CV" draft="unconfirmed">Kap Verde</territory>
			<territory type="CW" draft="unconfirmed">Curaçao</territory>
			<territory type="CX" draft="unconfirmed">Juovllat-sullot</territory>
			<territory type="CY" draft="unconfirmed">Kypros</territory>
			<territory type="CZ" draft="unconfirmed">Čeahkka</territory>
			<territory type="DE" draft="unconfirmed">Duiska</territory>
			<territory type="DG" draft="unconfirmed">Diego Garcia</territory>
			<territory type="DJ" draft="unconfirmed">Djibouti</territory>
			<territory type="DK" draft="unconfirmed">Dánmárku</territory>
			<territory type="DM" draft="unconfirmed">Dominica</territory>
			<territory type="DO" draft="unconfirmed">Dominikána dásseváldi</territory>
			<territory type="DZ" draft="unconfirmed">Algeria</territory>
			<territory type="EA" draft="unconfirmed">Ceuta ja Melilla</territory>
			<territory type="EC" draft="unconfirmed">Ecuador</territory>
			<territory type="EE" draft="unconfirmed">Estlánda</territory>
			<territory type="EG" draft="unconfirmed">Egypta</territory>
			<territory type="EH" draft="unconfirmed">Oarje-Sahára</territory>
			<territory type="ER" draft="unconfirmed">Eritrea</territory>
			<territory type="ES" draft="unconfirmed">Spánia</territory>
			<territory type="ET" draft="unconfirmed">Etiopia</territory>
			<territory type="EU" draft="unconfirmed">Eurohpa Uniovdna</territory>
			<territory type="FI" draft="unconfirmed">Suopma</territory>
			<territory type="FJ" draft="unconfirmed">Fijisullot</territory>
			<territory type="FK" draft="unconfirmed">Falklandsullot</territory>
			<territory type="FM" draft="unconfirmed">Mikronesia</territory>
			<territory type="FO" draft="unconfirmed">Fearsullot</territory>
			<territory type="FR" draft="unconfirmed">Frankriika</territory>
			<territory type="GA" draft="unconfirmed">Gabon</territory>
			<territory type="GB" draft="unconfirmed">Stuorra-Británnia</territory>
			<territory type="GB" alt="short" draft="unconfirmed">Stuorra-Británnia</territory>
			<territory type="GD" draft="unconfirmed">Grenada</territory>
			<territory type="GE" draft="unconfirmed">Georgia</territory>
			<territory type="GF" draft="unconfirmed">Frankriikka Guayana</territory>
			<territory type="GG" draft="unconfirmed">Guernsey</territory>
			<territory type="GH" draft="unconfirmed">Ghana</territory>
			<territory type="GI" draft="unconfirmed">Gibraltar</territory>
			<territory type="GL" draft="unconfirmed">Kalaallit Nunaat</territory>
			<territory type="GM" draft="unconfirmed">Gámbia</territory>
			<territory type="GN" draft="unconfirmed">Guinea</territory>
			<territory type="GP" draft="unconfirmed">Guadeloupe</territory>
			<territory type="GQ" draft="unconfirmed">Ekvatoriála Guinea</territory>
			<territory type="GR" draft="unconfirmed">Greika</territory>
			<territory type="GS" draft="unconfirmed">Lulli Georgia ja Lulli Sandwich-sullot</territory>
			<territory type="GT" draft="unconfirmed">Guatemala</territory>
			<territory type="GU" draft="unconfirmed">Guam</territory>
			<territory type="GW" draft="unconfirmed">Guinea-Bissau</territory>
			<territory type="GY" draft="unconfirmed">Guyana</territory>
			<territory type="HK" draft="unconfirmed">Hongkong</territory>
			<territory type="HK" alt="short" draft="unconfirmed">Hongkong</territory>
			<territory type="HM" draft="unconfirmed">Heard- ja McDonald-sullot</territory>
			<territory type="HN" draft="unconfirmed">Honduras</territory>
			<territory type="HR" draft="unconfirmed">Kroátia</territory>
			<territory type="HT" draft="unconfirmed">Haiti</territory>
			<territory type="HU" draft="unconfirmed">Ungár</territory>
			<territory type="IC" draft="unconfirmed">Kanáriasullot</territory>
			<territory type="ID" draft="unconfirmed">Indonesia</territory>
			<territory type="IE" draft="unconfirmed">Irlánda</territory>
			<territory type="IL" draft="unconfirmed">Israel</territory>
			<territory type="IM" draft="unconfirmed">Mann-sullot</territory>
			<territory type="IN" draft="unconfirmed">India</territory>
			<territory type="IQ" draft="unconfirmed">Irak</territory>
			<territory type="IR" draft="unconfirmed">Iran</territory>
			<territory type="IS" draft="unconfirmed">Islánda</territory>
			<territory type="IT" draft="unconfirmed">Itália</territory>
			<territory type="JE" draft="unconfirmed">Jersey</territory>
			<territory type="JM" draft="unconfirmed">Jamaica</territory>
			<territory type="JO" draft="unconfirmed">Jordánia</territory>
			<territory type="JP" draft="unconfirmed">Japána</territory>
			<territory type="KE" draft="unconfirmed">Kenia</territory>
			<territory type="KG" draft="unconfirmed">Kirgisistan</territory>
			<territory type="KH" draft="unconfirmed">Kambodža</territory>
			<territory type="KI" draft="unconfirmed">Kiribati</territory>
			<territory type="KM" draft="unconfirmed">Komoros</territory>
			<territory type="KN" draft="unconfirmed">Saint Kitts ja Nevis</territory>
			<territory type="KP" draft="unconfirmed">Davvi-Korea</territory>
			<territory type="KR" draft="unconfirmed">Mátta-Korea</territory>
			<territory type="KW" draft="unconfirmed">Kuwait</territory>
			<territory type="KY" draft="unconfirmed">Cayman-sullot</territory>
			<territory type="KZ" draft="unconfirmed">Kasakstan</territory>
			<territory type="LA" draft="unconfirmed">Laos</territory>
			<territory type="LB" draft="unconfirmed">Libanon</territory>
			<territory type="LC" draft="unconfirmed">Saint Lucia</territory>
			<territory type="LI" draft="unconfirmed">Liechtenstein</territory>
			<territory type="LK" draft="unconfirmed">Sri Lanka</territory>
			<territory type="LR" draft="unconfirmed">Liberia</territory>
			<territory type="LS" draft="unconfirmed">Lesotho</territory>
			<territory type="LT" draft="unconfirmed">Lietuva</territory>
			<territory type="LU" draft="unconfirmed">Luxembourg</territory>
			<territory type="LV" draft="unconfirmed">Látvia</territory>
			<territory type="LY" draft="unconfirmed">Libya</territory>
			<territory type="MA" draft="unconfirmed">Marokko</territory>
			<territory type="MC" draft="unconfirmed">Monaco</territory>
			<territory type="MD" draft="unconfirmed">Moldávia</territory>
			<territory type="ME" draft="unconfirmed">Montenegro</territory>
			<territory type="MF" draft="unconfirmed">Frankriikka Saint Martin</territory>
			<territory type="MG" draft="unconfirmed">Madagaskar</territory>
			<territory type="MH" draft="unconfirmed">Marshallsullot</territory>
			<territory type="MK" draft="unconfirmed">Makedonia</territory>
			<territory type="ML" draft="unconfirmed">Mali</territory>
			<territory type="MM" draft="unconfirmed">Burma</territory>
			<territory type="MN" draft="unconfirmed">Mongolia</territory>
			<territory type="MO" draft="unconfirmed">Makáo</territory>
			<territory type="MO" alt="short" draft="unconfirmed">Makáo</territory>
			<territory type="MP" draft="unconfirmed">Davvi-Mariánat</territory>
			<territory type="MQ" draft="unconfirmed">Martinique</territory>
			<territory type="MR" draft="unconfirmed">Mauretánia</territory>
			<territory type="MS" draft="unconfirmed">Montserrat</territory>
			<territory type="MT" draft="unconfirmed">Málta</territory>
			<territory type="MU" draft="unconfirmed">Mauritius</territory>
			<territory type="MV" draft="unconfirmed">Malediivvat</territory>
			<territory type="MW" draft="unconfirmed">Malawi</territory>
			<territory type="MX" draft="unconfirmed">Meksiko</territory>
			<territory type="MY" draft="unconfirmed">Malesia</territory>
			<territory type="MZ" draft="unconfirmed">Mosambik</territory>
			<territory type="NA" draft="unconfirmed">Namibia</territory>
			<territory type="NC" draft="unconfirmed">Ođđa-Kaledonia</territory>
			<territory type="NE" draft="unconfirmed">Niger</territory>
			<territory type="NF" draft="unconfirmed">Norfolksullot</territory>
			<territory type="NG" draft="unconfirmed">Nigeria</territory>
			<territory type="NI" draft="unconfirmed">Nicaragua</territory>
			<territory type="NL" draft="unconfirmed">Vuolleeatnamat</territory>
			<territory type="NO" draft="unconfirmed">Norga</territory>
			<territory type="NP" draft="unconfirmed">Nepal</territory>
			<territory type="NR" draft="unconfirmed">Nauru</territory>
			<territory type="NU" draft="unconfirmed">Niue</territory>
			<territory type="NZ" draft="unconfirmed">Ođđa-Selánda</territory>
			<territory type="OM" draft="unconfirmed">Oman</territory>
			<territory type="PA" draft="unconfirmed">Panama</territory>
			<territory type="PE" draft="unconfirmed">Peru</territory>
			<territory type="PF" draft="unconfirmed">Frankriikka Polynesia</territory>
			<territory type="PG" draft="unconfirmed">Papua-Ođđa-Guinea</territory>
			<territory type="PH" draft="unconfirmed">Filippiinnat</territory>
			<territory type="PK" draft="unconfirmed">Pakistan</territory>
			<territory type="PL" draft="unconfirmed">Polen</territory>
			<territory type="PM" draft="unconfirmed">Saint Pierre ja Miquelon</territory>
			<territory type="PN" draft="unconfirmed">Pitcairn</territory>
			<territory type="PR" draft="unconfirmed">Puerto Rico</territory>
			<territory type="PS" draft="unconfirmed">Palestina</territory>
			<territory type="PS" alt="short" draft="unconfirmed">Palestina</territory>
			<territory type="PT" draft="unconfirmed">Portugála</territory>
			<territory type="PW" draft="unconfirmed">Palau</territory>
			<territory type="PY" draft="unconfirmed">Paraguay</territory>
			<territory type="QA" draft="unconfirmed">Qatar</territory>
			<territory type="RE" draft="unconfirmed">Réunion</territory>
			<territory type="RO" draft="unconfirmed">Románia</territory>
			<territory type="RS" draft="unconfirmed">Serbia</territory>
			<territory type="RU" draft="unconfirmed">Ruošša</territory>
			<territory type="RW" draft="unconfirmed">Rwanda</territory>
			<territory type="SA" draft="unconfirmed">Saudi-Arábia</territory>
			<territory type="SB" draft="unconfirmed">Salomon-sullot</territory>
			<territory type="SC" draft="unconfirmed">Seychellsullot</territory>
			<territory type="SD" draft="unconfirmed">Davvisudan</territory>
			<territory type="SE" draft="unconfirmed">Ruoŧŧa</territory>
			<territory type="SG" draft="unconfirmed">Singapore</territory>
			<territory type="SH" draft="unconfirmed">Saint Helena</territory>
			<territory type="SI" draft="unconfirmed">Slovenia</territory>
			<territory type="SJ" draft="unconfirmed">Svalbárda ja Jan Mayen</territory>
			<territory type="SK" draft="unconfirmed">Slovákia</territory>
			<territory type="SL" draft="unconfirmed">Sierra Leone</territory>
			<territory type="SM" draft="unconfirmed">San Marino</territory>
			<territory type="SN" draft="unconfirmed">Senegal</territory>
			<territory type="SO" draft="unconfirmed">Somália</territory>
			<territory type="SR" draft="unconfirmed">Surinam</territory>
			<territory type="SS" draft="unconfirmed">Máttasudan</territory>
			<territory type="ST" draft="unconfirmed">São Tomé ja Príncipe</territory>
			<territory type="SV" draft="unconfirmed">El Salvador</territory>
			<territory type="SX" draft="unconfirmed">Vuolleeatnamat Saint Martin</territory>
			<territory type="SY" draft="unconfirmed">Syria</territory>
			<territory type="SZ" draft="unconfirmed">Svazieana</territory>
			<territory type="TA" draft="unconfirmed">Tristan da Cunha</territory>
			<territory type="TC" draft="unconfirmed">Turks ja Caicos-sullot</territory>
			<territory type="TD" draft="unconfirmed">Tčad</territory>
			<territory type="TG" draft="unconfirmed">Togo</territory>
			<territory type="TH" draft="unconfirmed">Thaieana</territory>
			<territory type="TJ" draft="unconfirmed">Tažikistan</territory>
			<territory type="TK" draft="unconfirmed">Tokelau</territory>
			<territory type="TL" draft="unconfirmed">Nuorta-Timor</territory>
			<territory type="TM" draft="unconfirmed">Turkmenistan</territory>
			<territory type="TN" draft="unconfirmed">Tunisia</territory>
			<territory type="TO" draft="unconfirmed">Tonga</territory>
			<territory type="TR" draft="unconfirmed">Durka</territory>
			<territory type="TT" draft="unconfirmed">Trinidad ja Tobago</territory>
			<territory type="TV" draft="unconfirmed">Tuvalu</territory>
			<territory type="TW" draft="unconfirmed">Taiwan</territory>
			<territory type="TZ" draft="unconfirmed">Tanzánia</territory>
			<territory type="UA" draft="unconfirmed">Ukraina</territory>
			<territory type="UG" draft="unconfirmed">Uganda</territory>
			<territory type="US" draft="unconfirmed">Amerihká ovttastuvvan stáhtat</territory>
			<territory type="US" alt="short" draft="unconfirmed">USA</territory>
			<territory type="UY" draft="unconfirmed">Uruguay</territory>
			<territory type="UZ" draft="unconfirmed">Usbekistan</territory>
			<territory type="VA" draft="unconfirmed">Vatikána</territory>
			<territory type="VC" draft="unconfirmed">Saint Vincent ja Grenadine</territory>
			<territory type="VE" draft="unconfirmed">Venezuela</territory>
			<territory type="VG" draft="unconfirmed">Brittania Virgin-sullot</territory>
			<territory type="VI" draft="unconfirmed">AOS Virgin-sullot</territory>
			<territory type="VN" draft="unconfirmed">Vietnam</territory>
			<territory type="VU" draft="unconfirmed">Vanuatu</territory>
			<territory type="WF" draft="unconfirmed">Wallis ja Futuna</territory>
			<territory type="WS" draft="unconfirmed">Samoa</territory>
			<territory type="XK" draft="unconfirmed">Kosovo</territory>
			<territory type="YE" draft="unconfirmed">Jemen</territory>
			<territory type="YT" draft="unconfirmed">Mayotte</territory>
			<territory type="ZA" draft="unconfirmed">Mátta-Afrihká</territory>
			<territory type="ZM" draft="unconfirmed">Zambia</territory>
			<territory type="ZW" draft="unconfirmed">Zimbabwe</territory>
			<territory type="ZZ" draft="unconfirmed">dovdameahttun guovlu</territory>
		</territories>
		<variants>
			<variant type="FONIPA" draft="unconfirmed">IPA</variant>
			<variant type="FONUPA" draft="unconfirmed">UPA</variant>
			<variant type="FONXSAMP" draft="unconfirmed">X-SAMPA</variant>
			<variant type="HEPBURN" draft="unconfirmed">Hepburn</variant>
			<variant type="PINYIN" draft="unconfirmed">pinyin</variant>
			<variant type="WADEGILE" draft="unconfirmed">Wade-Giles</variant>
		</variants>
		<keys>
			<key type="calendar" draft="unconfirmed">kaleandar</key>
			<key type="collation" draft="unconfirmed">ortnet</key>
			<key type="currency" draft="unconfirmed">valuhtta</key>
			<key type="numbers" draft="unconfirmed">numerála</key>
		</keys>
		<types>
			<type type="buddhist" key="calendar" draft="unconfirmed">buddhista kaleander</type>
			<type type="chinese" key="calendar" draft="unconfirmed">kiinna</type>
			<type type="fullwide" key="numbers" draft="unconfirmed">viddis oarjelohkosátni</type>
			<type type="gregorian" key="calendar" draft="unconfirmed">gregoria kaleander</type>
			<type type="latn" key="numbers" draft="unconfirmed">oarjelohkosátni</type>
			<type type="pinyin" key="collation" draft="unconfirmed">pinyin ortnet</type>
			<type type="traditional" key="collation" draft="unconfirmed">árbevirolaš ortnet</type>
		</types>
		<measurementSystemNames>
			<measurementSystemName type="metric" draft="unconfirmed">SI állan</measurementSystemName>
			<measurementSystemName type="UK" draft="unconfirmed">SB állan</measurementSystemName>
			<measurementSystemName type="US" draft="unconfirmed">AOS állan</measurementSystemName>
		</measurementSystemNames>
		<codePatterns>
			<codePattern type="language" draft="unconfirmed">giella: {0}</codePattern>
			<codePattern type="script" draft="unconfirmed">chállin: {0}</codePattern>
			<codePattern type="territory" draft="unconfirmed">guovlu: {0}</codePattern>
		</codePatterns>
	</localeDisplayNames>
	<characters>
		<exemplarCharacters>[a á b c č d đ e f g h i j k l m n ŋ o p r s š t ŧ u v z ž]</exemplarCharacters>
		<exemplarCharacters type="auxiliary">[à ç é è í ń ñ ó ò q ú w x y ü ø æ å ä ã ö]</exemplarCharacters>
		<exemplarCharacters type="index" draft="unconfirmed">[A Á B C Č D Đ E É F G H I J K L M N Ŋ O P Q R S Š T Ŧ U V W X Y Z Ž Ø Æ Å Ä Ö]</exemplarCharacters>
	</characters>
	<delimiters>
		<quotationStart draft="unconfirmed">”</quotationStart>
		<quotationEnd draft="unconfirmed">”</quotationEnd>
		<alternateQuotationStart draft="unconfirmed">’</alternateQuotationStart>
		<alternateQuotationEnd draft="unconfirmed">’</alternateQuotationEnd>
	</delimiters>
	<dates>
		<calendars>
			<calendar type="gregorian">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<month type="1" draft="unconfirmed">ođđj</month>
							<month type="2" draft="unconfirmed">guov</month>
							<month type="3" draft="unconfirmed">njuk</month>
							<month type="4" draft="unconfirmed">cuo</month>
							<month type="5" draft="unconfirmed">mies</month>
							<month type="6" draft="unconfirmed">geas</month>
							<month type="7" draft="unconfirmed">suoi</month>
							<month type="8" draft="unconfirmed">borg</month>
							<month type="9" draft="unconfirmed">čakč</month>
							<month type="10" draft="unconfirmed">golg</month>
							<month type="11" draft="unconfirmed">skáb</month>
							<month type="12" draft="unconfirmed">juov</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1" draft="unconfirmed">ođđajagemánnu</month>
							<month type="2" draft="unconfirmed">guovvamánnu</month>
							<month type="3" draft="unconfirmed">njukčamánnu</month>
							<month type="4" draft="unconfirmed">cuoŋománnu</month>
							<month type="5" draft="unconfirmed">miessemánnu</month>
							<month type="6" draft="unconfirmed">geassemánnu</month>
							<month type="7" draft="unconfirmed">suoidnemánnu</month>
							<month type="8" draft="unconfirmed">borgemánnu</month>
							<month type="9" draft="unconfirmed">čakčamánnu</month>
							<month type="10" draft="unconfirmed">golggotmánnu</month>
							<month type="11" draft="unconfirmed">skábmamánnu</month>
							<month type="12" draft="unconfirmed">juovlamánnu</month>
						</monthWidth>
					</monthContext>
					<monthContext type="stand-alone">
						<monthWidth type="narrow">
							<month type="1" draft="unconfirmed">O</month>
							<month type="2" draft="unconfirmed">G</month>
							<month type="3" draft="unconfirmed">N</month>
							<month type="4" draft="unconfirmed">C</month>
							<month type="5" draft="unconfirmed">M</month>
							<month type="6" draft="unconfirmed">G</month>
							<month type="7" draft="unconfirmed">S</month>
							<month type="8" draft="unconfirmed">B</month>
							<month type="9" draft="unconfirmed">Č</month>
							<month type="10" draft="unconfirmed">G</month>
							<month type="11" draft="unconfirmed">S</month>
							<month type="12" draft="unconfirmed">J</month>
						</monthWidth>
					</monthContext>
				</months>
				<days>
					<dayContext type="format">
						<dayWidth type="abbreviated">
							<day type="sun" draft="unconfirmed">sotn</day>
							<day type="mon" draft="unconfirmed">vuos</day>
							<day type="tue" draft="unconfirmed">maŋ</day>
							<day type="wed" draft="unconfirmed">gask</day>
							<day type="thu" draft="unconfirmed">duor</day>
							<day type="fri" draft="unconfirmed">bear</day>
							<day type="sat" draft="unconfirmed">láv</day>
						</dayWidth>
						<dayWidth type="wide">
							<day type="sun" draft="unconfirmed">sotnabeaivi</day>
							<day type="mon" draft="unconfirmed">vuossárga</day>
							<day type="tue" draft="unconfirmed">maŋŋebárga</day>
							<day type="wed" draft="unconfirmed">gaskavahkku</day>
							<day type="thu" draft="unconfirmed">duorasdat</day>
							<day type="fri" draft="unconfirmed">bearjadat</day>
							<day type="sat" draft="unconfirmed">lávvardat</day>
						</dayWidth>
					</dayContext>
					<dayContext type="stand-alone">
						<dayWidth type="narrow">
							<day type="sun" draft="unconfirmed">S</day>
							<day type="mon" draft="unconfirmed">V</day>
							<day type="tue" draft="unconfirmed">M</day>
							<day type="wed" draft="unconfirmed">G</day>
							<day type="thu" draft="unconfirmed">D</day>
							<day type="fri" draft="unconfirmed">B</day>
							<day type="sat" draft="unconfirmed">L</day>
						</dayWidth>
					</dayContext>
				</days>
				<dayPeriods>
					<dayPeriodContext type="format">
						<dayPeriodWidth type="abbreviated">
							<dayPeriod type="am" draft="unconfirmed">i.b.</dayPeriod>
							<dayPeriod type="pm" draft="unconfirmed">e.b.</dayPeriod>
						</dayPeriodWidth>
						<dayPeriodWidth type="wide">
							<dayPeriod type="am" draft="unconfirmed">iđitbeaivet</dayPeriod>
							<dayPeriod type="pm" draft="unconfirmed">eahketbeaivet</dayPeriod>
						</dayPeriodWidth>
					</dayPeriodContext>
					<dayPeriodContext type="stand-alone">
						<dayPeriodWidth type="wide">
							<dayPeriod type="am" draft="unconfirmed">iđitbeaivi</dayPeriod>
							<dayPeriod type="pm" draft="unconfirmed">eahketbeaivi</dayPeriod>
						</dayPeriodWidth>
					</dayPeriodContext>
				</dayPeriods>
				<eras>
					<eraNames>
						<era type="0" draft="unconfirmed">ovdal Kristtusa</era>
						<era type="1" draft="unconfirmed">maŋŋel Kristtusa</era>
					</eraNames>
					<eraAbbr>
						<era type="0" draft="unconfirmed">o.Kr.</era>
						<era type="1" draft="unconfirmed">m.Kr.</era>
					</eraAbbr>
				</eras>
			</calendar>
		</calendars>
		<fields>
			<field type="era">
				<displayName draft="unconfirmed">éra</displayName>
			</field>
			<field type="year">
				<displayName draft="unconfirmed">jáhki</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">{0} jahki maŋŋilit</relativeTimePattern>
					<relativeTimePattern count="two">{0} jahkki maŋŋilit</relativeTimePattern>
					<relativeTimePattern count="other">{0} jahkki maŋŋilit</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">{0} jahki árat</relativeTimePattern>
					<relativeTimePattern count="two">{0} jahkki árat</relativeTimePattern>
					<relativeTimePattern count="other">{0} jahkki árat</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="month">
				<displayName draft="unconfirmed">mánnu</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">{0} mánotbadji maŋŋilit</relativeTimePattern>
					<relativeTimePattern count="two">{0} mánotbadji maŋŋilit</relativeTimePattern>
					<relativeTimePattern count="other">{0} mánotbadji maŋŋilit</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">{0} mánotbadji árat</relativeTimePattern>
					<relativeTimePattern count="two">{0} mánotbadji árat</relativeTimePattern>
					<relativeTimePattern count="other">{0} mánotbadji árat</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="week">
				<displayName draft="unconfirmed">váhkku</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">{0} vahku maŋŋilit</relativeTimePattern>
					<relativeTimePattern count="two">{0} vahkku maŋŋilit</relativeTimePattern>
					<relativeTimePattern count="other">{0} vahkku maŋŋilit</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">{0} vahku árat</relativeTimePattern>
					<relativeTimePattern count="two">{0} vahkku árat</relativeTimePattern>
					<relativeTimePattern count="other">{0} vahkku árat</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="day">
				<displayName draft="unconfirmed">beaivi</displayName>
				<relative type="-2" draft="unconfirmed">oovdebpeivvi</relative>
				<relative type="-1" draft="unconfirmed">ikte</relative>
				<relative type="0" draft="unconfirmed">odne</relative>
				<relative type="1" draft="unconfirmed">ihttin</relative>
				<relative type="2" draft="unconfirmed">paijeelittáá</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">{0} jándor maŋŋilit</relativeTimePattern>
					<relativeTimePattern count="two">{0} jándor amaŋŋilit</relativeTimePattern>
					<relativeTimePattern count="other">{0} jándora maŋŋilit</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">{0} jándor árat</relativeTimePattern>
					<relativeTimePattern count="two">{0} jándora árat</relativeTimePattern>
					<relativeTimePattern count="other">{0} jándora árat</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="weekday">
				<displayName draft="unconfirmed">váhkkubeaivi</displayName>
			</field>
			<field type="dayperiod">
				<displayName draft="unconfirmed">beaivi ráidodássi</displayName>
			</field>
			<field type="hour">
				<displayName draft="unconfirmed">diibmu</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">{0} diibmu maŋŋilit</relativeTimePattern>
					<relativeTimePattern count="two">{0} diibmur maŋŋilit</relativeTimePattern>
					<relativeTimePattern count="other">{0} diibmur maŋŋilit</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">{0} diibmu árat</relativeTimePattern>
					<relativeTimePattern count="two">{0} diibmur árat</relativeTimePattern>
					<relativeTimePattern count="other">{0} diibmur árat</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="minute">
				<displayName draft="unconfirmed">minuhtta</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">{0} minuhta maŋŋilit</relativeTimePattern>
					<relativeTimePattern count="two">{0} minuhtta maŋŋilit</relativeTimePattern>
					<relativeTimePattern count="other">{0} minuhtta maŋŋilit</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">{0} minuhta árat</relativeTimePattern>
					<relativeTimePattern count="two">{0} minuhtta árat</relativeTimePattern>
					<relativeTimePattern count="other">{0} minuhtta árat</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="second">
				<displayName draft="unconfirmed">sekunda</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">{0} sekunda maŋŋilit</relativeTimePattern>
					<relativeTimePattern count="two">{0} sekundda maŋŋilit</relativeTimePattern>
					<relativeTimePattern count="other">{0} sekundda maŋŋilit</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">{0} sekunda árat</relativeTimePattern>
					<relativeTimePattern count="two">{0} sekundda árat</relativeTimePattern>
					<relativeTimePattern count="other">{0} sekundda árat</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="zone">
				<displayName draft="unconfirmed">áigeavádat</displayName>
			</field>
		</fields>
		<timeZoneNames>
			<hourFormat draft="unconfirmed">+HH:mm;−HH:mm</hourFormat>
			<gmtFormat draft="unconfirmed">UTC{0}</gmtFormat>
			<gmtZeroFormat draft="unconfirmed">UTC</gmtZeroFormat>
			<regionFormat draft="unconfirmed">{0} áigi</regionFormat>
			<fallbackFormat draft="unconfirmed">{0} ({1})</fallbackFormat>
			<zone type="Etc/Unknown">
				<exemplarCity draft="unconfirmed">dovdameahttun áigeavádat</exemplarCity>
			</zone>
			<zone type="Antarctica/DumontDUrville">
				<exemplarCity draft="unconfirmed">Dumont d’Urville</exemplarCity>
			</zone>
			<zone type="America/St_Barthelemy">
				<exemplarCity draft="unconfirmed">Saint Barthélemy</exemplarCity>
			</zone>
			<zone type="America/Sao_Paulo">
				<exemplarCity draft="unconfirmed">São Paulo</exemplarCity>
			</zone>
			<zone type="America/Curacao">
				<exemplarCity draft="unconfirmed">Curaçao</exemplarCity>
			</zone>
			<zone type="America/Merida">
				<exemplarCity draft="unconfirmed">Mérida</exemplarCity>
			</zone>
			<metazone type="Europe_Central">
				<long>
					<generic draft="unconfirmed">gaska-Eurohpá áigi</generic>
					<standard draft="unconfirmed">gaska-Eurohpá dábálašáigi</standard>
					<daylight draft="unconfirmed">gaska-Eurohpá geassiáigi</daylight>
				</long>
				<short>
					<generic draft="unconfirmed">CET</generic>
					<standard draft="unconfirmed">CET</standard>
					<daylight draft="unconfirmed">CEST</daylight>
				</short>
			</metazone>
			<metazone type="Europe_Eastern">
				<long>
					<generic draft="unconfirmed">nuorti-Eurohpá áigi</generic>
					<standard draft="unconfirmed">nuorti-Eurohpá dábálašáigi</standard>
					<daylight draft="unconfirmed">nuorti-Eurohpá geassiáigi</daylight>
				</long>
				<short>
					<generic draft="unconfirmed">EET</generic>
					<standard draft="unconfirmed">EET</standard>
					<daylight draft="unconfirmed">EEST</daylight>
				</short>
			</metazone>
			<metazone type="Europe_Western">
				<long>
					<generic draft="unconfirmed">oarje-Eurohpá áigi</generic>
					<standard draft="unconfirmed">oarje-Eurohpá dábálašáigi</standard>
					<daylight draft="unconfirmed">oarje-Eurohpá geassiáigi</daylight>
				</long>
				<short>
					<generic draft="unconfirmed">WET</generic>
					<standard draft="unconfirmed">WET</standard>
					<daylight draft="unconfirmed">WEST</daylight>
				</short>
			</metazone>
			<metazone type="GMT">
				<long>
					<standard draft="unconfirmed">Greenwich gaskka áigi</standard>
				</long>
				<short>
					<standard draft="unconfirmed">GMT</standard>
				</short>
			</metazone>
			<metazone type="Moscow">
				<long>
					<generic draft="unconfirmed">Moskva-áigi</generic>
					<standard draft="unconfirmed">Moskva-dábálašáigi</standard>
					<daylight draft="unconfirmed">Moskva-geassiáigi</daylight>
				</long>
			</metazone>
		</timeZoneNames>
	</dates>
	<numbers>
		<symbols numberSystem="latn">
			<decimal draft="unconfirmed">,</decimal>
			<group draft="unconfirmed"> </group>
			<list draft="unconfirmed">;</list>
			<percentSign draft="unconfirmed">%</percentSign>
			<plusSign draft="unconfirmed">+</plusSign>
			<minusSign draft="unconfirmed">−</minusSign>
			<exponential draft="unconfirmed">×10^</exponential>
			<superscriptingExponent draft="unconfirmed">·</superscriptingExponent>
			<perMille draft="unconfirmed">‰</perMille>
			<infinity draft="unconfirmed">∞</infinity>
			<nan draft="unconfirmed">¤¤¤</nan>
		</symbols>
		<decimalFormats numberSystem="latn">
			<decimalFormatLength>
				<decimalFormat>
					<pattern draft="unconfirmed">#,##0.###</pattern>
				</decimalFormat>
			</decimalFormatLength>
			<decimalFormatLength type="long">
				<decimalFormat>
					<pattern type="1000" count="one" draft="unconfirmed">0 duhát</pattern>
					<pattern type="1000" count="two" draft="unconfirmed">0 duháhat</pattern>
					<pattern type="1000" count="other" draft="unconfirmed">0 duháhat</pattern>
					<pattern type="10000" count="one" draft="unconfirmed">00 duhát</pattern>
					<pattern type="10000" count="two" draft="unconfirmed">00 duháhat</pattern>
					<pattern type="10000" count="other" draft="unconfirmed">00 duháhat</pattern>
					<pattern type="100000" count="one" draft="unconfirmed">000 duhát</pattern>
					<pattern type="100000" count="two" draft="unconfirmed">000 duháhat</pattern>
					<pattern type="100000" count="other" draft="unconfirmed">000 duháhat</pattern>
					<pattern type="1000000" count="one" draft="unconfirmed">0 miljona</pattern>
					<pattern type="1000000" count="two" draft="unconfirmed">0 miljonat</pattern>
					<pattern type="1000000" count="other" draft="unconfirmed">0 miljonat</pattern>
					<pattern type="10000000" count="one" draft="unconfirmed">00 miljona</pattern>
					<pattern type="10000000" count="two" draft="unconfirmed">00 miljonat</pattern>
					<pattern type="10000000" count="other" draft="unconfirmed">00 miljonat</pattern>
					<pattern type="100000000" count="one" draft="unconfirmed">000 miljona</pattern>
					<pattern type="100000000" count="two" draft="unconfirmed">000 miljonat</pattern>
					<pattern type="100000000" count="other" draft="unconfirmed">000 miljonat</pattern>
					<pattern type="1000000000" count="one" draft="unconfirmed">0 miljardi</pattern>
					<pattern type="1000000000" count="two" draft="unconfirmed">0 miljardit</pattern>
					<pattern type="1000000000" count="other" draft="unconfirmed">0 miljardit</pattern>
					<pattern type="10000000000" count="one" draft="unconfirmed">00 miljardi</pattern>
					<pattern type="10000000000" count="two" draft="unconfirmed">00 miljardit</pattern>
					<pattern type="10000000000" count="other" draft="unconfirmed">00 miljardit</pattern>
					<pattern type="100000000000" count="one" draft="unconfirmed">000 miljardi</pattern>
					<pattern type="100000000000" count="two" draft="unconfirmed">000 miljardit</pattern>
					<pattern type="100000000000" count="other" draft="unconfirmed">000 miljardit</pattern>
					<pattern type="1000000000000" count="one" draft="unconfirmed">0 biljona</pattern>
					<pattern type="1000000000000" count="two" draft="unconfirmed">0 biljonat</pattern>
					<pattern type="1000000000000" count="other" draft="unconfirmed">0 biljonat</pattern>
					<pattern type="10000000000000" count="one" draft="unconfirmed">00 biljona</pattern>
					<pattern type="10000000000000" count="two" draft="unconfirmed">00 biljonat</pattern>
					<pattern type="10000000000000" count="other" draft="unconfirmed">00 biljonat</pattern>
					<pattern type="100000000000000" count="one" draft="unconfirmed">000 biljona</pattern>
					<pattern type="100000000000000" count="two" draft="unconfirmed">000 biljonat</pattern>
					<pattern type="100000000000000" count="other" draft="unconfirmed">000 biljonat</pattern>
				</decimalFormat>
			</decimalFormatLength>
			<decimalFormatLength type="short">
				<decimalFormat>
					<pattern type="1000" count="one" draft="unconfirmed">0 dt</pattern>
					<pattern type="1000" count="two" draft="unconfirmed">0 dt</pattern>
					<pattern type="1000" count="other" draft="unconfirmed">0 dt</pattern>
					<pattern type="10000" count="one" draft="unconfirmed">00 dt</pattern>
					<pattern type="10000" count="two" draft="unconfirmed">00 dt</pattern>
					<pattern type="10000" count="other" draft="unconfirmed">00 dt</pattern>
					<pattern type="100000" count="one" draft="unconfirmed">000 dt</pattern>
					<pattern type="100000" count="two" draft="unconfirmed">000 dt</pattern>
					<pattern type="100000" count="other" draft="unconfirmed">000 dt</pattern>
					<pattern type="1000000" count="one" draft="unconfirmed">0 mn</pattern>
					<pattern type="1000000" count="two" draft="unconfirmed">0 mn</pattern>
					<pattern type="1000000" count="other" draft="unconfirmed">0 mn</pattern>
					<pattern type="10000000" count="one" draft="unconfirmed">00 mn</pattern>
					<pattern type="10000000" count="two" draft="unconfirmed">00 mn</pattern>
					<pattern type="10000000" count="other" draft="unconfirmed">00 mn</pattern>
					<pattern type="100000000" count="one" draft="unconfirmed">000 mn</pattern>
					<pattern type="100000000" count="two" draft="unconfirmed">000 mn</pattern>
					<pattern type="100000000" count="other" draft="unconfirmed">000 mn</pattern>
					<pattern type="1000000000" count="one" draft="unconfirmed">0 md</pattern>
					<pattern type="1000000000" count="two" draft="unconfirmed">0 md</pattern>
					<pattern type="1000000000" count="other" draft="unconfirmed">0 md</pattern>
					<pattern type="10000000000" count="one" draft="unconfirmed">00 md</pattern>
					<pattern type="10000000000" count="two" draft="unconfirmed">00 md</pattern>
					<pattern type="10000000000" count="other" draft="unconfirmed">00 md</pattern>
					<pattern type="100000000000" count="one" draft="unconfirmed">000 md</pattern>
					<pattern type="100000000000" count="two" draft="unconfirmed">000 md</pattern>
					<pattern type="100000000000" count="other" draft="unconfirmed">000 md</pattern>
					<pattern type="1000000000000" count="one" draft="unconfirmed">0 bn</pattern>
					<pattern type="1000000000000" count="two" draft="unconfirmed">0 bn</pattern>
					<pattern type="1000000000000" count="other" draft="unconfirmed">0 bn</pattern>
					<pattern type="10000000000000" count="one" draft="unconfirmed">00 bn</pattern>
					<pattern type="10000000000000" count="two" draft="unconfirmed">00 bn</pattern>
					<pattern type="10000000000000" count="other" draft="unconfirmed">00 bn</pattern>
					<pattern type="100000000000000" count="one" draft="unconfirmed">000 bn</pattern>
					<pattern type="100000000000000" count="two" draft="unconfirmed">000 bn</pattern>
					<pattern type="100000000000000" count="other" draft="unconfirmed">000 bn</pattern>
				</decimalFormat>
			</decimalFormatLength>
		</decimalFormats>
		<scientificFormats numberSystem="latn">
			<scientificFormatLength>
				<scientificFormat>
					<pattern draft="unconfirmed">#E0</pattern>
				</scientificFormat>
			</scientificFormatLength>
		</scientificFormats>
		<percentFormats numberSystem="latn">
			<percentFormatLength>
				<percentFormat>
					<pattern draft="unconfirmed">#,##0 %</pattern>
				</percentFormat>
			</percentFormatLength>
		</percentFormats>
		<currencyFormats numberSystem="latn">
			<currencyFormatLength>
				<currencyFormat type="standard">
					<pattern draft="unconfirmed">#,##0.00 ¤</pattern>
				</currencyFormat>
			</currencyFormatLength>
			<unitPattern count="one" draft="unconfirmed">{0} {1}</unitPattern>
			<unitPattern count="two" draft="unconfirmed">{0} {1}</unitPattern>
			<unitPattern count="other" draft="unconfirmed">{0} {1}</unitPattern>
		</currencyFormats>
		<currencies>
			<currency type="DKK">
				<symbol draft="unconfirmed">Dkr</symbol>
			</currency>
			<currency type="EUR">
				<displayName draft="unconfirmed">euro</displayName>
				<displayName count="one" draft="unconfirmed">euro</displayName>
				<displayName count="two" draft="unconfirmed">euro</displayName>
				<displayName count="other" draft="unconfirmed">euro</displayName>
				<symbol draft="unconfirmed">€</symbol>
			</currency>
			<currency type="FIM">
				<displayName>suoma márkki</displayName>
			</currency>
			<currency type="HKD">
				<symbol draft="unconfirmed">HK$</symbol>
			</currency>
			<currency type="INR">
				<symbol draft="unconfirmed">₹</symbol>
			</currency>
			<currency type="JPY">
				<symbol draft="unconfirmed">JP¥</symbol>
			</currency>
			<currency type="MXN">
				<symbol draft="unconfirmed">MX$</symbol>
			</currency>
			<currency type="NOK">
				<displayName>norgga kruvdno</displayName>
				<displayName count="one">norgga kruvdno</displayName>
				<displayName count="two">norgga kruvdno</displayName>
				<displayName count="other">norgga kruvdno</displayName>
				<symbol>kr</symbol>
			</currency>
			<currency type="SEK">
				<displayName>ruoŧŧa kruvdno</displayName>
				<displayName count="one">ruoŧŧa kruvdno</displayName>
				<displayName count="two">ruoŧŧa kruvdno</displayName>
				<displayName count="other">ruoŧŧa kruvdno</displayName>
				<symbol draft="unconfirmed">Skr</symbol>
			</currency>
			<currency type="THB">
				<symbol draft="unconfirmed">฿</symbol>
			</currency>
			<currency type="XAG">
				<displayName draft="unconfirmed">uns silba</displayName>
			</currency>
			<currency type="XAU">
				<displayName draft="unconfirmed">uns golli</displayName>
			</currency>
		</currencies>
	</numbers>
	<units>
		<unitLength type="long">
			<compoundUnit type="per">
				<compoundUnitPattern draft="unconfirmed">{0} juohke {1}</compoundUnitPattern>
			</compoundUnit>
			<unit type="acceleration-g-force">
				<unitPattern count="one" draft="unconfirmed">{0} Maapallo gravitaatiovoima</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} Maapallo gravitaatiovoimat</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} Maapallo gravitaatiovoimat</unitPattern>
			</unit>
			<unit type="angle-arc-minute">
				<unitPattern count="one" draft="unconfirmed">{0} jorbbas minuhta</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} jorbbas minuhtta</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} jorbbas minuhtta</unitPattern>
			</unit>
			<unit type="angle-arc-second">
				<unitPattern count="one" draft="unconfirmed">{0} jorbbas sekunda</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} jorbbas sekundda</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} jorbbas sekundda</unitPattern>
			</unit>
			<unit type="angle-degree">
				<unitPattern count="one" draft="unconfirmed">{0} grádat</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} grádat</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} grádat</unitPattern>
			</unit>
			<unit type="area-acre">
				<unitPattern count="one" draft="unconfirmed">{0} Amerihká tynnyrinala</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} Amerihká tynnyrinala</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} Amerihká tynnyrinala</unitPattern>
			</unit>
			<unit type="area-hectare">
				<unitPattern count="one" draft="unconfirmed">{0} hehtaari</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} hehtaaria</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} hehtaaria</unitPattern>
			</unit>
			<unit type="area-square-foot">
				<unitPattern count="one" draft="unconfirmed">{0} neliöjuolgi</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} neliöjuolgi</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} neliöjuolgi</unitPattern>
			</unit>
			<unit type="area-square-kilometer">
				<unitPattern count="one" draft="unconfirmed">{0} neliökilomehter</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} neliökilomehtera</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} neliökilomehtera</unitPattern>
			</unit>
			<unit type="area-square-meter">
				<unitPattern count="one" draft="unconfirmed">{0} neliömehter</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} neliömehtera</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} neliömehtera</unitPattern>
			</unit>
			<unit type="area-square-mile">
				<unitPattern count="one" draft="unconfirmed">{0} eangas neliömiil</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} eangas neliömiila</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} eangas neliömiila</unitPattern>
			</unit>
			<unit type="duration-day">
				<unitPattern count="one" draft="unconfirmed">{0} jándor</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} jándora</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} jándora</unitPattern>
			</unit>
			<unit type="duration-hour">
				<unitPattern count="one" draft="unconfirmed">{0} diibmu</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} diimmur</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} diibmur</unitPattern>
			</unit>
			<unit type="duration-millisecond">
				<unitPattern count="one" draft="unconfirmed">{0} millisekunda</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} millisekundda</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} millisekundda</unitPattern>
			</unit>
			<unit type="duration-minute">
				<unitPattern count="one" draft="unconfirmed">{0} minuhta</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} minuhtta</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} minuhtta</unitPattern>
			</unit>
			<unit type="duration-month">
				<unitPattern count="one" draft="unconfirmed">{0} mánotbadji</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} mánotbaji</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} mánotbadji</unitPattern>
			</unit>
			<unit type="duration-second">
				<unitPattern count="one" draft="unconfirmed">{0} sekunda</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} sekundda</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} sekundda</unitPattern>
			</unit>
			<unit type="duration-week">
				<unitPattern count="one" draft="unconfirmed">{0} váhku</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} váhkku</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} váhkku</unitPattern>
			</unit>
			<unit type="duration-year">
				<unitPattern count="one" draft="unconfirmed">{0} jahki</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} jahkki</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} jahkki</unitPattern>
			</unit>
			<unit type="length-centimeter">
				<unitPattern count="one" draft="unconfirmed">{0} sentimehter</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} sentimehtera</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} sentimehtera</unitPattern>
			</unit>
			<unit type="length-foot">
				<unitPattern count="one" draft="unconfirmed">{0} juolgi</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} juolgi</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} juolgi</unitPattern>
			</unit>
			<unit type="length-inch">
				<unitPattern count="one" draft="unconfirmed">{0} bealgi</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} bealgi</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} bealgi</unitPattern>
			</unit>
			<unit type="length-kilometer">
				<unitPattern count="one" draft="unconfirmed">{0} kilomehter</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} kilomehtera</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} kilomehtera</unitPattern>
			</unit>
			<unit type="length-light-year">
				<unitPattern count="one" draft="unconfirmed">{0} chuovgat jagi</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} chuovgat jagi</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} chuovgat jagi</unitPattern>
			</unit>
			<unit type="length-meter">
				<unitPattern count="one" draft="unconfirmed">{0} mehter</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} mehtera</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} mehtera</unitPattern>
			</unit>
			<unit type="length-mile">
				<unitPattern count="one" draft="unconfirmed">{0} eangas miil</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} eangas miila</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} eangas miila</unitPattern>
			</unit>
			<unit type="length-millimeter">
				<unitPattern count="one" draft="unconfirmed">{0} millimehter</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} millimehtera</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} millimehtera</unitPattern>
			</unit>
			<unit type="length-picometer">
				<unitPattern count="one" draft="unconfirmed">{0} pikomehter</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} pikomehtera</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} pikomehtera</unitPattern>
			</unit>
			<unit type="length-yard">
				<unitPattern count="one" draft="unconfirmed">{0} eangas yard</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} eangas yard</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} eangas yard</unitPattern>
			</unit>
			<unit type="mass-gram">
				<unitPattern count="one" draft="unconfirmed">{0} gram</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} gram</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} gram</unitPattern>
			</unit>
			<unit type="mass-kilogram">
				<unitPattern count="one" draft="unconfirmed">{0} kilogram</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} kilogram</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} kilogram</unitPattern>
			</unit>
			<unit type="mass-ounce">
				<unitPattern count="one" draft="unconfirmed">{0} unssi</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} unssi</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} unssi</unitPattern>
			</unit>
			<unit type="mass-pound">
				<unitPattern count="one" draft="unconfirmed">{0} pauna</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} pauna</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} pauna</unitPattern>
			</unit>
			<unit type="power-horsepower">
				<unitPattern count="one" draft="unconfirmed">{0} hevosvoima</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} hevosvoima</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} hevosvoima</unitPattern>
			</unit>
			<unit type="power-kilowatt">
				<unitPattern count="one" draft="unconfirmed">{0} kilowatt</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} kilowatt</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} kilowatt</unitPattern>
			</unit>
			<unit type="power-watt">
				<unitPattern count="one" draft="unconfirmed">{0} watt</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} watt</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} watt</unitPattern>
			</unit>
			<unit type="pressure-hectopascal">
				<unitPattern count="one" draft="unconfirmed">{0} hehtopascal</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} hehtopascal</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} hehtopascal</unitPattern>
			</unit>
			<unit type="pressure-inch-hg">
				<unitPattern count="one" draft="unconfirmed">{0} bealgi kvikksølv</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} bealgi kvikksølv</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} bealgi kvikksølv</unitPattern>
			</unit>
			<unit type="pressure-millibar">
				<unitPattern count="one" draft="unconfirmed">{0} millibar</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} millibar</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} millibar</unitPattern>
			</unit>
			<unit type="speed-kilometer-per-hour">
				<unitPattern count="one" draft="unconfirmed">{0} kilomehter kohti diibmu</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} kilomehtera kohti diibmu</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} kilomehtera kohti diibmu</unitPattern>
			</unit>
			<unit type="speed-meter-per-second">
				<unitPattern count="one" draft="unconfirmed">{0} mehter kohti sekunti</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} mehtera kohti sekunti</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} mehtera kohti sekunti</unitPattern>
			</unit>
			<unit type="speed-mile-per-hour">
				<unitPattern count="one" draft="unconfirmed">{0} eangas miil kohti diibmu</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} eangas miila kohti diibmu</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} eangas miila kohti diibmu</unitPattern>
			</unit>
			<unit type="temperature-celsius">
				<unitPattern count="one" draft="unconfirmed">{0} grádat Celsius</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} grádat Celsius</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} grádat Celsius</unitPattern>
			</unit>
			<unit type="temperature-fahrenheit">
				<unitPattern count="one" draft="unconfirmed">{0} grádat Fahrenheit</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} grádat Fahrenheit</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} grádat Fahrenheit</unitPattern>
			</unit>
			<unit type="volume-cubic-kilometer">
				<unitPattern count="one" draft="unconfirmed">{0} kubikkilomehter</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} kubikkilomehtera</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} kubikkilomehtera</unitPattern>
			</unit>
			<unit type="volume-cubic-mile">
				<unitPattern count="one" draft="unconfirmed">{0} eangas kubikkmiil</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} eangas kubikkmiila</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} eangas kubikkmiila</unitPattern>
			</unit>
			<unit type="volume-liter">
				<unitPattern count="one" draft="unconfirmed">{0} lihtar</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} lihtara</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} lihtara</unitPattern>
			</unit>
		</unitLength>
		<unitLength type="short">
			<compoundUnit type="per">
				<compoundUnitPattern draft="unconfirmed">{0}/{1}</compoundUnitPattern>
			</compoundUnit>
			<unit type="acceleration-g-force">
				<unitPattern count="one" draft="unconfirmed">{0} G</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} G</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} G</unitPattern>
			</unit>
			<unit type="angle-arc-minute">
				<unitPattern count="one" draft="unconfirmed">{0}′</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}′</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}′</unitPattern>
			</unit>
			<unit type="angle-arc-second">
				<unitPattern count="one" draft="unconfirmed">{0}″</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}″</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}″</unitPattern>
			</unit>
			<unit type="angle-degree">
				<unitPattern count="one" draft="unconfirmed">{0}°</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}°</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}°</unitPattern>
			</unit>
			<unit type="area-acre">
				<unitPattern count="one">{0} ac</unitPattern>
				<unitPattern count="two">{0} ac</unitPattern>
				<unitPattern count="other">{0} ac</unitPattern>
			</unit>
			<unit type="area-hectare">
				<unitPattern count="one" draft="unconfirmed">{0} ha</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} ha</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} ha</unitPattern>
			</unit>
			<unit type="area-square-foot">
				<unitPattern count="one">{0} ft²</unitPattern>
				<unitPattern count="two">{0} ft²</unitPattern>
				<unitPattern count="other">{0} ft²</unitPattern>
			</unit>
			<unit type="area-square-kilometer">
				<unitPattern count="one" draft="unconfirmed">{0} km²</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} km²</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} km²</unitPattern>
			</unit>
			<unit type="area-square-meter">
				<unitPattern count="one" draft="unconfirmed">{0} m²</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} m²</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} m²</unitPattern>
			</unit>
			<unit type="area-square-mile">
				<unitPattern count="one">{0} mi²</unitPattern>
				<unitPattern count="two">{0} mi²</unitPattern>
				<unitPattern count="other">{0} mi²</unitPattern>
			</unit>
			<unit type="duration-day">
				<unitPattern count="one" draft="unconfirmed">{0} d</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} d</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} d</unitPattern>
			</unit>
			<unit type="duration-hour">
				<unitPattern count="one" draft="unconfirmed">{0} h</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} h</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} h</unitPattern>
			</unit>
			<unit type="duration-millisecond">
				<unitPattern count="one" draft="unconfirmed">{0} ms</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} ms</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} ms</unitPattern>
			</unit>
			<unit type="duration-minute">
				<unitPattern count="one" draft="unconfirmed">{0} min</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} min</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} min</unitPattern>
			</unit>
			<unit type="duration-month">
				<unitPattern count="one" draft="unconfirmed">{0} mán</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} mán</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} mán</unitPattern>
			</unit>
			<unit type="duration-second">
				<unitPattern count="one" draft="unconfirmed">{0} s</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} s</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} s</unitPattern>
			</unit>
			<unit type="duration-week">
				<unitPattern count="one" draft="unconfirmed">{0} v</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} v</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} v</unitPattern>
			</unit>
			<unit type="duration-year">
				<unitPattern count="one" draft="unconfirmed">{0} jah</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} jah</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} jah</unitPattern>
			</unit>
			<unit type="length-centimeter">
				<unitPattern count="one" draft="unconfirmed">{0} cm</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} cm</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} cm</unitPattern>
			</unit>
			<unit type="length-foot">
				<unitPattern count="one" draft="unconfirmed">{0} juolgi</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} juolgi</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} juolgi</unitPattern>
			</unit>
			<unit type="length-inch">
				<unitPattern count="one" draft="unconfirmed">{0} bealgi</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} bealgi</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} bealgi</unitPattern>
			</unit>
			<unit type="length-kilometer">
				<unitPattern count="one" draft="unconfirmed">{0} km</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} km</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} km</unitPattern>
			</unit>
			<unit type="length-light-year">
				<unitPattern count="one">{0} ly</unitPattern>
				<unitPattern count="two">{0} ly</unitPattern>
				<unitPattern count="other">{0} ly</unitPattern>
			</unit>
			<unit type="length-meter">
				<unitPattern count="one" draft="unconfirmed">{0} m</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} m</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} m</unitPattern>
			</unit>
			<unit type="length-mile">
				<unitPattern count="one">{0} mi</unitPattern>
				<unitPattern count="two">{0} mi</unitPattern>
				<unitPattern count="other">{0} mi</unitPattern>
			</unit>
			<unit type="length-millimeter">
				<unitPattern count="one" draft="unconfirmed">{0} mm</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} mm</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} mm</unitPattern>
			</unit>
			<unit type="length-picometer">
				<unitPattern count="one" draft="unconfirmed">{0} pm</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} pm</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} pm</unitPattern>
			</unit>
			<unit type="length-yard">
				<unitPattern count="one">{0} yd</unitPattern>
				<unitPattern count="two">{0} yd</unitPattern>
				<unitPattern count="other">{0} yd</unitPattern>
			</unit>
			<unit type="mass-gram">
				<unitPattern count="one" draft="unconfirmed">{0} g</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} g</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} g</unitPattern>
			</unit>
			<unit type="mass-kilogram">
				<unitPattern count="one" draft="unconfirmed">{0} kg</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} kg</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} kg</unitPattern>
			</unit>
			<unit type="mass-ounce">
				<unitPattern count="one" draft="unconfirmed">{0} unssi</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} unssi</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} unssi</unitPattern>
			</unit>
			<unit type="mass-pound">
				<unitPattern count="one" draft="unconfirmed">{0} pauna</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} pauna</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} pauna</unitPattern>
			</unit>
			<unit type="power-horsepower">
				<unitPattern count="one" draft="unconfirmed">{0} hv</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} hv</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} hv</unitPattern>
			</unit>
			<unit type="power-kilowatt">
				<unitPattern count="one" draft="unconfirmed">{0} kW</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} kW</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} kW</unitPattern>
			</unit>
			<unit type="power-watt">
				<unitPattern count="one" draft="unconfirmed">{0} W</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} W</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} W</unitPattern>
			</unit>
			<unit type="pressure-hectopascal">
				<unitPattern count="one" draft="unconfirmed">{0} hPa</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} hPa</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} hPa</unitPattern>
			</unit>
			<unit type="pressure-inch-hg">
				<unitPattern count="one" draft="unconfirmed">{0} bealgi Hg</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} bealgi Hg</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} bealgi Hg</unitPattern>
			</unit>
			<unit type="pressure-millibar">
				<unitPattern count="one" draft="unconfirmed">{0} mbar</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} mbar</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} mbar</unitPattern>
			</unit>
			<unit type="speed-kilometer-per-hour">
				<unitPattern count="one" draft="unconfirmed">{0} km/h</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} km/h</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} km/h</unitPattern>
			</unit>
			<unit type="speed-meter-per-second">
				<unitPattern count="one" draft="unconfirmed">{0} m/s</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} m/s</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} m/s</unitPattern>
			</unit>
			<unit type="speed-mile-per-hour">
				<unitPattern count="one">{0} mi/h</unitPattern>
				<unitPattern count="two">{0} mi/h</unitPattern>
				<unitPattern count="other">{0} mi/h</unitPattern>
			</unit>
			<unit type="temperature-celsius">
				<unitPattern count="one" draft="unconfirmed">{0}°C</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}°C</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}°C</unitPattern>
			</unit>
			<unit type="temperature-fahrenheit">
				<unitPattern count="one" draft="unconfirmed">{0}°F</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}°F</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}°F</unitPattern>
			</unit>
			<unit type="volume-cubic-kilometer">
				<unitPattern count="one" draft="unconfirmed">{0} km³</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} km³</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} km³</unitPattern>
			</unit>
			<unit type="volume-cubic-mile">
				<unitPattern count="one">{0} mi³</unitPattern>
				<unitPattern count="two">{0} mi³</unitPattern>
				<unitPattern count="other">{0} mi³</unitPattern>
			</unit>
			<unit type="volume-liter">
				<unitPattern count="one" draft="unconfirmed">{0} l</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} l</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} l</unitPattern>
			</unit>
		</unitLength>
		<unitLength type="narrow">
			<compoundUnit type="per">
				<compoundUnitPattern draft="unconfirmed">{0}/{1}</compoundUnitPattern>
			</compoundUnit>
			<unit type="acceleration-g-force">
				<unitPattern count="one" draft="unconfirmed">{0}G</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}G</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}G</unitPattern>
			</unit>
			<unit type="angle-arc-minute">
				<unitPattern count="one" draft="unconfirmed">{0}′</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}′</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}′</unitPattern>
			</unit>
			<unit type="angle-arc-second">
				<unitPattern count="one" draft="unconfirmed">{0}″</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}″</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}″</unitPattern>
			</unit>
			<unit type="angle-degree">
				<unitPattern count="one" draft="unconfirmed">{0}°</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}°</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}°</unitPattern>
			</unit>
			<unit type="area-acre">
				<unitPattern count="one">{0} ac</unitPattern>
				<unitPattern count="two">{0} ac</unitPattern>
				<unitPattern count="other">{0} ac</unitPattern>
			</unit>
			<unit type="area-hectare">
				<unitPattern count="one" draft="unconfirmed">{0}ha</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}ha</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}ha</unitPattern>
			</unit>
			<unit type="area-square-foot">
				<unitPattern count="one">{0} ft²</unitPattern>
				<unitPattern count="two">{0} ft²</unitPattern>
				<unitPattern count="other">{0} ft²</unitPattern>
			</unit>
			<unit type="area-square-kilometer">
				<unitPattern count="one" draft="unconfirmed">{0}km²</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}km²</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}km²</unitPattern>
			</unit>
			<unit type="area-square-meter">
				<unitPattern count="one" draft="unconfirmed">{0}m²</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}m²</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}m²</unitPattern>
			</unit>
			<unit type="area-square-mile">
				<unitPattern count="one">{0} mi²</unitPattern>
				<unitPattern count="two">{0} mi²</unitPattern>
				<unitPattern count="other">{0} mi²</unitPattern>
			</unit>
			<unit type="duration-day">
				<unitPattern count="one" draft="unconfirmed">{0}d</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}d</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}d</unitPattern>
			</unit>
			<unit type="duration-hour">
				<unitPattern count="one" draft="unconfirmed">{0}h</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}h</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}h</unitPattern>
			</unit>
			<unit type="duration-millisecond">
				<unitPattern count="one" draft="unconfirmed">{0}ms</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}ms</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}ms</unitPattern>
			</unit>
			<unit type="duration-minute">
				<unitPattern count="one" draft="unconfirmed">{0}m</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}m</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}m</unitPattern>
			</unit>
			<unit type="duration-month">
				<unitPattern count="one" draft="unconfirmed">{0}m</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}m</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}m</unitPattern>
			</unit>
			<unit type="duration-second">
				<unitPattern count="one" draft="unconfirmed">{0}s</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}s</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}s</unitPattern>
			</unit>
			<unit type="duration-week">
				<unitPattern count="one" draft="unconfirmed">{0}v</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}v</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}v</unitPattern>
			</unit>
			<unit type="duration-year">
				<unitPattern count="one" draft="unconfirmed">{0}j</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}j</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}j</unitPattern>
			</unit>
			<unit type="length-centimeter">
				<unitPattern count="one" draft="unconfirmed">{0}cm</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}cm</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}cm</unitPattern>
			</unit>
			<unit type="length-foot">
				<unitPattern count="one" draft="unconfirmed">{0} juolgi</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} juolgi</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} juolgi</unitPattern>
			</unit>
			<unit type="length-inch">
				<unitPattern count="one" draft="unconfirmed">{0} bealgi</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} bealgi</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} bealgi</unitPattern>
			</unit>
			<unit type="length-kilometer">
				<unitPattern count="one" draft="unconfirmed">{0}km</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}km</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}km</unitPattern>
			</unit>
			<unit type="length-light-year">
				<unitPattern count="one">{0} ly</unitPattern>
				<unitPattern count="two">{0} ly</unitPattern>
				<unitPattern count="other">{0} ly</unitPattern>
			</unit>
			<unit type="length-meter">
				<unitPattern count="one" draft="unconfirmed">{0}m</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}m</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}m</unitPattern>
			</unit>
			<unit type="length-mile">
				<unitPattern count="one">{0} mi</unitPattern>
				<unitPattern count="two">{0} mi</unitPattern>
				<unitPattern count="other">{0} mi</unitPattern>
			</unit>
			<unit type="length-millimeter">
				<unitPattern count="one" draft="unconfirmed">{0}mm</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}mm</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}mm</unitPattern>
			</unit>
			<unit type="length-picometer">
				<unitPattern count="one" draft="unconfirmed">{0}pm</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}pm</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}pm</unitPattern>
			</unit>
			<unit type="length-yard">
				<unitPattern count="one">{0} yd</unitPattern>
				<unitPattern count="two">{0} yd</unitPattern>
				<unitPattern count="other">{0} yd</unitPattern>
			</unit>
			<unit type="mass-gram">
				<unitPattern count="one" draft="unconfirmed">{0}g</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}g</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}g</unitPattern>
			</unit>
			<unit type="mass-kilogram">
				<unitPattern count="one" draft="unconfirmed">{0}kg</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}kg</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}kg</unitPattern>
			</unit>
			<unit type="mass-ounce">
				<unitPattern count="one" draft="unconfirmed">{0} unssi</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} unssi</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} unssi</unitPattern>
			</unit>
			<unit type="mass-pound">
				<unitPattern count="one" draft="unconfirmed">{0} pauna</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} pauna</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} pauna</unitPattern>
			</unit>
			<unit type="power-horsepower">
				<unitPattern count="one" draft="unconfirmed">{0}hv</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}hv</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}hv</unitPattern>
			</unit>
			<unit type="power-kilowatt">
				<unitPattern count="one" draft="unconfirmed">{0}kW</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}kW</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}kW</unitPattern>
			</unit>
			<unit type="power-watt">
				<unitPattern count="one" draft="unconfirmed">{0}W</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}W</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}W</unitPattern>
			</unit>
			<unit type="pressure-hectopascal">
				<unitPattern count="one" draft="unconfirmed">{0}hPa</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}hPa</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}hPa</unitPattern>
			</unit>
			<unit type="pressure-inch-hg">
				<unitPattern count="one" draft="unconfirmed">{0} bealgi Hg</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0} bealgi Hg</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0} bealgi Hg</unitPattern>
			</unit>
			<unit type="pressure-millibar">
				<unitPattern count="one" draft="unconfirmed">{0}mbar</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}mbar</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}mbar</unitPattern>
			</unit>
			<unit type="speed-kilometer-per-hour">
				<unitPattern count="one" draft="unconfirmed">{0}km/h</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}km/h</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}km/h</unitPattern>
			</unit>
			<unit type="speed-meter-per-second">
				<unitPattern count="one" draft="unconfirmed">{0}m/s</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}m/s</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}m/s</unitPattern>
			</unit>
			<unit type="speed-mile-per-hour">
				<unitPattern count="one">{0} mi/h</unitPattern>
				<unitPattern count="two">{0} mi/h</unitPattern>
				<unitPattern count="other">{0} mi/h</unitPattern>
			</unit>
			<unit type="temperature-celsius">
				<unitPattern count="one" draft="unconfirmed">{0}°</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}°</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}°</unitPattern>
			</unit>
			<unit type="temperature-fahrenheit">
				<unitPattern count="one" draft="unconfirmed">{0}°F</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}°F</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}°F</unitPattern>
			</unit>
			<unit type="volume-cubic-kilometer">
				<unitPattern count="one" draft="unconfirmed">{0}km³</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}km³</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}km³</unitPattern>
			</unit>
			<unit type="volume-cubic-mile">
				<unitPattern count="one">{0} mi³</unitPattern>
				<unitPattern count="two">{0} mi³</unitPattern>
				<unitPattern count="other">{0} mi³</unitPattern>
			</unit>
			<unit type="volume-liter">
				<unitPattern count="one" draft="unconfirmed">{0}L</unitPattern>
				<unitPattern count="two" draft="unconfirmed">{0}L</unitPattern>
				<unitPattern count="other" draft="unconfirmed">{0}L</unitPattern>
			</unit>
		</unitLength>
		<durationUnit type="hm">
			<durationUnitPattern>h:mm</durationUnitPattern>
		</durationUnit>
		<durationUnit type="hms">
			<durationUnitPattern>h:mm:ss</durationUnitPattern>
		</durationUnit>
		<durationUnit type="ms">
			<durationUnitPattern>m:ss</durationUnitPattern>
		</durationUnit>
	</units>
	<listPatterns>
		<listPattern>
			<listPatternPart type="start" draft="unconfirmed">{0}, {1}</listPatternPart>
			<listPatternPart type="middle" draft="unconfirmed">{0}, {1}</listPatternPart>
			<listPatternPart type="end" draft="unconfirmed">{0} ja {1}</listPatternPart>
			<listPatternPart type="2" draft="unconfirmed">{0} ja {1}</listPatternPart>
		</listPattern>
		<listPattern type="unit">
			<listPatternPart type="start" draft="unconfirmed">{0}, {1}</listPatternPart>
			<listPatternPart type="middle" draft="unconfirmed">{0}, {1}</listPatternPart>
			<listPatternPart type="end" draft="unconfirmed">{0}, {1}</listPatternPart>
			<listPatternPart type="2" draft="unconfirmed">{0}, {1}</listPatternPart>
		</listPattern>
		<listPattern type="unit-narrow">
			<listPatternPart type="start" draft="unconfirmed">{0} {1}</listPatternPart>
			<listPatternPart type="middle" draft="unconfirmed">{0} {1}</listPatternPart>
			<listPatternPart type="end" draft="unconfirmed">{0} {1}</listPatternPart>
			<listPatternPart type="2" draft="unconfirmed">{0} {1}</listPatternPart>
		</listPattern>
		<listPattern type="unit-short">
			<listPatternPart type="start" draft="unconfirmed">{0}, {1}</listPatternPart>
			<listPatternPart type="middle" draft="unconfirmed">{0}, {1}</listPatternPart>
			<listPatternPart type="end" draft="unconfirmed">{0}, {1}</listPatternPart>
			<listPatternPart type="2" draft="unconfirmed">{0}, {1}</listPatternPart>
		</listPattern>
	</listPatterns>
	<posix>
		<messages>
			<yesstr draft="unconfirmed">jo</yesstr>
			<nostr draft="unconfirmed">ii</nostr>
		</messages>
	</posix>
</ldml>

