<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE ldml SYSTEM "../../common/dtd/ldml.dtd">
<!-- Copyright © 1991-2013 Unicode, Inc.
CLDR data files are interpreted according to the LDML specification (http://unicode.org/reports/tr35/)
For terms of use, see http://www.unicode.org/copyright.html
-->
<ldml>
	<identity>
		<version number="$Revision: 9625 $"/>
		<generation date="$Date: 2014-01-08 23:53:23 -0600 (Wed, 08 Jan 2014) $"/>
		<language type="zh"/>
		<script type="Hans"/>
		<territory type="HK"/>
	</identity>
	<localeDisplayNames>
		<localeDisplayPattern>
			<localeKeyTypePattern draft="contributed">{0}: {1}</localeKeyTypePattern>
		</localeDisplayPattern>
		<languages>
			<language type="ast" draft="contributed">阿斯图里亚思文</language>
			<language type="es_ES" draft="contributed">伊比利亚西班牙文</language>
			<language type="om" draft="contributed">奥罗莫文</language>
			<language type="pt_PT" draft="contributed">伊比利亚葡萄牙文</language>
		</languages>
		<scripts>
			<script type="Knda" draft="contributed">卡纳塔克文</script>
			<script type="Sinh" draft="contributed">辛哈拉文</script>
			<script type="Thaa" draft="contributed">塔安娜文</script>
			<script type="Zxxx" draft="contributed">非书面文字</script>
			<script type="Zzzz" draft="contributed">未知语系</script>
		</scripts>
		<territories>
			<territory type="GP" draft="contributed">瓜德罗普岛</territory>
			<territory type="ME" draft="contributed">黑山</territory>
			<territory type="PM" draft="contributed">圣皮埃尔和密克隆</territory>
		</territories>
		<variants>
			<variant type="WADEGILE" draft="contributed">韦氏拼音罗马字</variant>
		</variants>
		<types>
			<type type="big5han" key="collation" draft="contributed">繁体中文排序顺序 (Big5)</type>
			<type type="dictionary" key="collation" draft="contributed">字典排序</type>
			<type type="gb2312han" key="collation" draft="contributed">简体中文排序顺序 (GB2312)</type>
			<type type="phonebook" key="collation" draft="contributed">电话簿排序</type>
			<type type="pinyin" key="collation" draft="contributed">拼音排序顺序</type>
			<type type="reformed" key="collation" draft="contributed">改良排序</type>
			<type type="traditional" key="collation" draft="contributed">传统排序</type>
		</types>
		<codePatterns>
			<codePattern type="script" draft="contributed">语系：{0}</codePattern>
		</codePatterns>
	</localeDisplayNames>
	<dates>
		<calendars>
			<calendar type="buddhist">
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern draft="contributed">Gy年M月d日EEEE</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern draft="contributed">Gy年M月d日</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern draft="contributed">Gy年M月d日</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern draft="contributed">Gd/M/yy</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<dateTimeFormats>
					<availableFormats>
						<dateFormatItem id="M" draft="contributed">L</dateFormatItem>
						<dateFormatItem id="MEd" draft="contributed">M/dE</dateFormatItem>
					</availableFormats>
				</dateTimeFormats>
			</calendar>
			<calendar type="chinese">
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern numbers="hanidec" draft="contributed">U年MMMd日EEEE</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern numbers="hanidec" draft="contributed">U年MMMd日</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern numbers="hanidec" draft="contributed">U年MMMd日</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern draft="contributed">U-M-d</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
			</calendar>
			<calendar type="generic">
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern draft="contributed">Gy年M月d日EEEE</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern draft="contributed">Gy年M月d日</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern draft="contributed">Gy年M月d日</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern draft="contributed">d/M/yyGGGGG</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<dateTimeFormats>
					<dateTimeFormatLength type="full">
						<dateTimeFormat>
							<pattern draft="contributed">{1}{0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="long">
						<dateTimeFormat>
							<pattern draft="contributed">{1}{0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="medium">
						<dateTimeFormat>
							<pattern draft="contributed">{1}{0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="short">
						<dateTimeFormat>
							<pattern draft="contributed">{1}{0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<availableFormats>
						<dateFormatItem id="HHmm" draft="contributed">HH:mm</dateFormatItem>
						<dateFormatItem id="M" draft="contributed">L</dateFormatItem>
						<dateFormatItem id="Md" draft="contributed">d/M</dateFormatItem>
						<dateFormatItem id="MEd" draft="contributed">E, d/M</dateFormatItem>
						<dateFormatItem id="MMM" draft="contributed">M月</dateFormatItem>
						<dateFormatItem id="MMMMdd" draft="contributed">M月d日</dateFormatItem>
						<dateFormatItem id="yyyyM" draft="contributed">M/yGGGGG</dateFormatItem>
						<dateFormatItem id="yyyyMd" draft="contributed">d/M/yGGGGG</dateFormatItem>
						<dateFormatItem id="yyyyMEd" draft="contributed">E, d/M/yGGGGG</dateFormatItem>
					</availableFormats>
					<intervalFormats>
						<intervalFormatFallback draft="contributed">{0}–{1}</intervalFormatFallback>
						<intervalFormatItem id="h">
							<greatestDifference id="h" draft="contributed">ah至h时</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hmv">
							<greatestDifference id="H" draft="contributed">vHH:mm–HH:mm</greatestDifference>
							<greatestDifference id="m" draft="contributed">vHH:mm–HH:mm</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hv">
							<greatestDifference id="H" draft="contributed">vHH–HH</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMM">
							<greatestDifference id="M" draft="contributed">M月至M月</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yM">
							<greatestDifference id="M" draft="contributed">y年M月至y年M月</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMd">
							<greatestDifference id="d" draft="contributed">d/M/y至d/M/y</greatestDifference>
							<greatestDifference id="M" draft="contributed">d/M/y至d/M/y</greatestDifference>
							<greatestDifference id="y" draft="contributed">d/M/y至d/M/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMEd">
							<greatestDifference id="d" draft="contributed">d/M/yE至d/M/yE</greatestDifference>
							<greatestDifference id="M" draft="contributed">d/M/yE至d/M/yE</greatestDifference>
							<greatestDifference id="y" draft="contributed">d/M/yE至d/M/yE</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMEd">
							<greatestDifference id="d" draft="contributed">y年M月d日E至M月d日E</greatestDifference>
						</intervalFormatItem>
					</intervalFormats>
				</dateTimeFormats>
			</calendar>
			<calendar type="gregorian">
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern draft="contributed">y年M月d日EEEE</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern draft="contributed">y年M月d日</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern draft="contributed">y年M月d日</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern draft="contributed">d/M/yy</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<dateTimeFormats>
					<dateTimeFormatLength type="full">
						<dateTimeFormat>
							<pattern draft="contributed">{1}{0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="long">
						<dateTimeFormat>
							<pattern draft="contributed">{1}{0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="medium">
						<dateTimeFormat>
							<pattern draft="contributed">{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="short">
						<dateTimeFormat>
							<pattern draft="contributed">{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<availableFormats>
						<dateFormatItem id="HHmm" draft="contributed">HH:mm</dateFormatItem>
						<dateFormatItem id="M" draft="contributed">L</dateFormatItem>
						<dateFormatItem id="Md" draft="contributed">d/M</dateFormatItem>
						<dateFormatItem id="MEd" draft="contributed">E, d/M</dateFormatItem>
						<dateFormatItem id="MMdd" draft="contributed">dd/MM</dateFormatItem>
						<dateFormatItem id="MMMMdd" draft="contributed">M月d日</dateFormatItem>
						<dateFormatItem id="yM" draft="contributed">M/y</dateFormatItem>
						<dateFormatItem id="yMd" draft="contributed">d/M/y</dateFormatItem>
						<dateFormatItem id="yMEd" draft="contributed">d/M/y（E）</dateFormatItem>
						<dateFormatItem id="yMM" draft="contributed">MM/y</dateFormatItem>
					</availableFormats>
					<intervalFormats>
						<intervalFormatFallback draft="contributed">{0}–{1}</intervalFormatFallback>
						<intervalFormatItem id="h">
							<greatestDifference id="h" draft="contributed">ah至h时</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hmv">
							<greatestDifference id="H" draft="contributed">vHH:mm–HH:mm</greatestDifference>
							<greatestDifference id="m" draft="contributed">vHH:mm–HH:mm</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hv">
							<greatestDifference id="H" draft="contributed">vHH–HH</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yM">
							<greatestDifference id="M" draft="contributed">y年M月至y年M月</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMd">
							<greatestDifference id="d" draft="contributed">d/M/y至d/M/y</greatestDifference>
							<greatestDifference id="M" draft="contributed">d/M/y至d/M/y</greatestDifference>
							<greatestDifference id="y" draft="contributed">d/M/y至d/M/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMEd">
							<greatestDifference id="d" draft="contributed">d/M/yE至d/M/yE</greatestDifference>
							<greatestDifference id="M" draft="contributed">d/M/yE至d/M/yE</greatestDifference>
							<greatestDifference id="y" draft="contributed">d/M/yE至d/M/yE</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMEd">
							<greatestDifference id="d" draft="contributed">y年M月d日E至M月d日E</greatestDifference>
						</intervalFormatItem>
					</intervalFormats>
				</dateTimeFormats>
			</calendar>
			<calendar type="islamic">
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern draft="contributed">Gy年M月d日EEEE</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern draft="contributed">Gy年M月d日</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern draft="contributed">Gy年M月d日</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern draft="contributed">Gd/M/yy</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
			</calendar>
			<calendar type="japanese">
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern draft="contributed">Gy年M月d日EEEE</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern draft="contributed">Gy年M月d日</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern draft="contributed">Gy年M月d日</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern draft="contributed">Gd/M/yy</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<dateTimeFormats>
					<availableFormats>
						<dateFormatItem id="Md" draft="contributed">M/d</dateFormatItem>
						<dateFormatItem id="MEd" draft="contributed">M/dE</dateFormatItem>
					</availableFormats>
				</dateTimeFormats>
			</calendar>
			<calendar type="roc">
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern draft="contributed">Gy年M月d日EEEE</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern draft="contributed">Gy年M月d日</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern draft="contributed">Gy年M月d日</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern draft="contributed">Gd/M/yy</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<dateTimeFormats>
					<availableFormats>
						<dateFormatItem id="M" draft="contributed">L</dateFormatItem>
						<dateFormatItem id="Md" draft="contributed">M-d</dateFormatItem>
						<dateFormatItem id="MMM" draft="contributed">M月</dateFormatItem>
					</availableFormats>
				</dateTimeFormats>
			</calendar>
		</calendars>
		<fields>
			<field type="second">
				<relativeTime type="future">
					<relativeTimePattern count="other">{0}秒后</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="other">{0}秒前</relativeTimePattern>
				</relativeTime>
			</field>
		</fields>
		<timeZoneNames>
			<zone type="Antarctica/DumontDUrville">
				<exemplarCity draft="contributed">杜蒙杜威勒</exemplarCity>
			</zone>
			<zone type="America/St_Barthelemy">
				<exemplarCity draft="contributed">圣巴太累米</exemplarCity>
			</zone>
			<zone type="America/St_Johns">
				<exemplarCity draft="contributed">圣约翰</exemplarCity>
			</zone>
			<zone type="America/Santo_Domingo">
				<exemplarCity draft="contributed">圣多明戈</exemplarCity>
			</zone>
			<zone type="America/Danmarkshavn">
				<exemplarCity draft="contributed">丹马克沙伏</exemplarCity>
			</zone>
			<zone type="Europe/Zaporozhye">
				<exemplarCity draft="contributed">扎波罗什</exemplarCity>
			</zone>
			<metazone type="Cocos">
				<long>
					<standard draft="contributed">科科斯岛时间</standard>
				</long>
			</metazone>
			<metazone type="DumontDUrville">
				<long>
					<standard draft="contributed">杜蒙特杜维尔时间</standard>
				</long>
			</metazone>
			<metazone type="French_Southern">
				<long>
					<standard draft="contributed">法属南部和南极洲时间</standard>
				</long>
			</metazone>
			<metazone type="Gilbert_Islands">
				<long>
					<standard draft="contributed">吉尔柏特群岛时间</standard>
				</long>
			</metazone>
			<metazone type="Phoenix_Islands">
				<long>
					<standard draft="contributed">凤凰岛时间</standard>
				</long>
			</metazone>
			<metazone type="Pierre_Miquelon">
				<long>
					<generic draft="contributed">圣皮埃尔和密克隆时间</generic>
					<standard draft="contributed">圣皮埃尔和密克隆标准时间</standard>
					<daylight draft="contributed">圣皮埃尔和密克隆夏令时间</daylight>
				</long>
			</metazone>
			<metazone type="South_Georgia">
				<long>
					<standard draft="contributed">南乔治亚时间</standard>
				</long>
			</metazone>
			<metazone type="Tahiti">
				<long>
					<standard draft="contributed">大溪地时间</standard>
				</long>
			</metazone>
			<metazone type="Truk">
				<long>
					<standard draft="contributed">特鲁克时间</standard>
				</long>
			</metazone>
			<metazone type="Tuvalu">
				<long>
					<standard draft="contributed">吐瓦鲁时间</standard>
				</long>
			</metazone>
		</timeZoneNames>
	</dates>
	<numbers>
		<decimalFormats numberSystem="latn">
			<decimalFormatLength type="long">
				<decimalFormat>
					<pattern type="1000" count="other" draft="contributed">0千</pattern>
					<pattern type="10000" count="other" draft="contributed">0万</pattern>
					<pattern type="100000" count="other" draft="contributed">00万</pattern>
					<pattern type="1000000" count="other" draft="contributed">000万</pattern>
					<pattern type="10000000" count="other" draft="contributed">0000万</pattern>
					<pattern type="100000000" count="other" draft="contributed">0亿</pattern>
					<pattern type="1000000000" count="other" draft="contributed">00亿</pattern>
					<pattern type="10000000000" count="other" draft="contributed">000亿</pattern>
					<pattern type="100000000000" count="other" draft="contributed">0000亿</pattern>
					<pattern type="1000000000000" count="other" draft="contributed">0万亿</pattern>
					<pattern type="**************" count="other" draft="contributed">00万亿</pattern>
					<pattern type="***************" count="other" draft="contributed">000万亿</pattern>
				</decimalFormat>
			</decimalFormatLength>
			<decimalFormatLength type="short">
				<decimalFormat>
					<pattern type="1000" count="other" draft="contributed">0千</pattern>
					<pattern type="10000" count="other" draft="contributed">0万</pattern>
					<pattern type="100000" count="other" draft="contributed">00万</pattern>
					<pattern type="1000000" count="other" draft="contributed">000万</pattern>
					<pattern type="10000000" count="other" draft="contributed">0000万</pattern>
					<pattern type="100000000" count="other" draft="contributed">0亿</pattern>
					<pattern type="1000000000" count="other" draft="contributed">00亿</pattern>
					<pattern type="10000000000" count="other" draft="contributed">000亿</pattern>
					<pattern type="100000000000" count="other" draft="contributed">0000亿</pattern>
					<pattern type="1000000000000" count="other" draft="contributed">0万亿</pattern>
					<pattern type="**************" count="other" draft="contributed">00万亿</pattern>
					<pattern type="***************" count="other" draft="contributed">000万亿</pattern>
				</decimalFormat>
			</decimalFormatLength>
		</decimalFormats>
		<currencyFormats numberSystem="latn">
			<currencyFormatLength>
				<currencyFormat type="standard">
					<pattern draft="contributed">¤#,##0.00</pattern>
				</currencyFormat>
				<currencyFormat type="accounting">
					<pattern draft="contributed">¤#,##0.00;(¤#,##0.00)</pattern>
				</currencyFormat>
			</currencyFormatLength>
			<unitPattern count="other" draft="contributed">{0} {1}</unitPattern>
		</currencyFormats>
		<currencies>
			<currency type="AWG">
				<displayName draft="contributed">阿鲁巴弗罗林</displayName>
				<displayName count="other" draft="contributed">阿鲁巴弗罗林</displayName>
			</currency>
			<currency type="HKD">
				<symbol>$</symbol>
			</currency>
			<currency type="HNL">
				<displayName draft="contributed">洪都拉斯拉伦皮拉</displayName>
				<displayName count="other" draft="contributed">洪都拉斯拉伦皮拉</displayName>
			</currency>
			<currency type="KYD">
				<displayName draft="contributed">开曼群岛元</displayName>
				<displayName count="other" draft="contributed">开曼群岛元</displayName>
			</currency>
			<currency type="KZT">
				<displayName draft="contributed">哈萨克斯坦腾格</displayName>
				<displayName count="other" draft="contributed">哈萨克斯坦腾格</displayName>
			</currency>
			<currency type="NIO">
				<displayName>尼加拉瓜科多巴</displayName>
				<displayName count="other">尼加拉瓜科多巴</displayName>
			</currency>
			<currency type="UAH">
				<displayName draft="contributed">乌克兰赫夫纳</displayName>
				<displayName count="other" draft="contributed">乌克兰赫夫纳</displayName>
			</currency>
			<currency type="XAG">
				<displayName draft="contributed">白银</displayName>
			</currency>
		</currencies>
	</numbers>
	<units>
		<unitLength type="long">
			<compoundUnit type="per">
				<compoundUnitPattern draft="contributed">{0}/{1}</compoundUnitPattern>
			</compoundUnit>
			<unit type="duration-second">
				<unitPattern count="other" draft="contributed">{0}秒</unitPattern>
			</unit>
		</unitLength>
		<unitLength type="short">
			<unit type="length-centimeter">
				<unitPattern count="other" draft="contributed">{0}厘米</unitPattern>
			</unit>
			<unit type="length-foot">
				<unitPattern count="other" draft="contributed">{0}英尺</unitPattern>
			</unit>
			<unit type="length-inch">
				<unitPattern count="other" draft="contributed">{0}英寸</unitPattern>
			</unit>
			<unit type="length-kilometer">
				<unitPattern count="other" draft="contributed">{0}公里</unitPattern>
			</unit>
			<unit type="length-light-year">
				<unitPattern count="other" draft="contributed">{0}光年</unitPattern>
			</unit>
			<unit type="length-meter">
				<unitPattern count="other" draft="contributed">{0}米</unitPattern>
			</unit>
			<unit type="length-mile">
				<unitPattern count="other" draft="contributed">{0}英里</unitPattern>
			</unit>
			<unit type="length-millimeter">
				<unitPattern count="other" draft="contributed">{0}毫米</unitPattern>
			</unit>
			<unit type="length-picometer">
				<unitPattern count="other" draft="contributed">{0}皮米</unitPattern>
			</unit>
			<unit type="length-yard">
				<unitPattern count="other" draft="contributed">{0}码</unitPattern>
			</unit>
		</unitLength>
		<unitLength type="narrow">
			<unit type="length-centimeter">
				<unitPattern count="other" draft="contributed">{0}厘米</unitPattern>
			</unit>
			<unit type="length-foot">
				<unitPattern count="other" draft="contributed">{0}英尺</unitPattern>
			</unit>
			<unit type="length-inch">
				<unitPattern count="other" draft="contributed">{0}英寸</unitPattern>
			</unit>
			<unit type="length-kilometer">
				<unitPattern count="other" draft="contributed">{0}公里</unitPattern>
			</unit>
			<unit type="length-light-year">
				<unitPattern count="other" draft="contributed">{0}光年</unitPattern>
			</unit>
			<unit type="length-meter">
				<unitPattern count="other" draft="contributed">{0}米</unitPattern>
			</unit>
			<unit type="length-mile">
				<unitPattern count="other" draft="contributed">{0}英里</unitPattern>
			</unit>
			<unit type="length-millimeter">
				<unitPattern count="other" draft="contributed">{0}毫米</unitPattern>
			</unit>
			<unit type="length-picometer">
				<unitPattern count="other" draft="contributed">{0}皮米</unitPattern>
			</unit>
			<unit type="length-yard">
				<unitPattern count="other" draft="contributed">{0}码</unitPattern>
			</unit>
		</unitLength>
	</units>
</ldml>

