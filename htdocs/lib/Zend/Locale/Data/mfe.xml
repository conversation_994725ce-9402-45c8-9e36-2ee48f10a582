<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE ldml SYSTEM "../../common/dtd/ldml.dtd">
<!-- Copyright © 1991-2013 Unicode, Inc.
CLDR data files are interpreted according to the LDML specification (http://unicode.org/reports/tr35/)
For terms of use, see http://www.unicode.org/copyright.html
-->
<ldml>
	<identity>
		<version number="$Revision: 9728 $"/>
		<generation date="$Date: 2014-02-12 22:14:13 -0600 (Wed, 12 Feb 2014) $"/>
		<language type="mfe"/>
	</identity>
	<localeDisplayNames>
		<languages>
			<language type="ak">akan</language>
			<language type="am">amarik</language>
			<language type="ar">arab</language>
			<language type="be">bieloris</language>
			<language type="bg">bilgar</language>
			<language type="bn">bengali</language>
			<language type="cs">tchek</language>
			<language type="de">alman</language>
			<language type="el">grek</language>
			<language type="en">angle</language>
			<language type="es">espagnol</language>
			<language type="fa">persan</language>
			<language type="fr">franse</language>
			<language type="ha">haoussa</language>
			<language type="hi">hindi</language>
			<language type="hu">hongrwa</language>
			<language type="id">indonezien</language>
			<language type="ig">igbo</language>
			<language type="it">italien</language>
			<language type="ja">zapone</language>
			<language type="jv">zavane</language>
			<language type="km">khmer, santral</language>
			<language type="ko">koreen</language>
			<language type="mfe">kreol morisien</language>
			<language type="ms">male</language>
			<language type="my">birman</language>
			<language type="ne">nepale</language>
			<language type="nl">olande</language>
			<language type="pa">penjabi</language>
			<language type="pl">polone</language>
			<language type="pt">portige</language>
			<language type="ro">roumin</language>
			<language type="ru">ris</language>
			<language type="rw">rwanda</language>
			<language type="so">somali</language>
			<language type="sv">swedwa</language>
			<language type="ta">tamoul</language>
			<language type="th">thaï</language>
			<language type="tr">tirk</language>
			<language type="uk">ikrenien</language>
			<language type="ur">ourdou</language>
			<language type="vi">vietnamien</language>
			<language type="yo">yoruba</language>
			<language type="zh">sinwa, mandarin</language>
			<language type="zu">zoulou</language>
		</languages>
		<territories>
			<territory type="AD">Andor</territory>
			<territory type="AE">Emira arab ini</territory>
			<territory type="AF">Afganistan</territory>
			<territory type="AG">Antigua-ek-Barbuda</territory>
			<territory type="AI">Anguilla</territory>
			<territory type="AL">Albani</territory>
			<territory type="AM">Armeni</territory>
			<territory type="AN">Antiy neerlande</territory>
			<territory type="AO">Angola</territory>
			<territory type="AR">Larzantinn</territory>
			<territory type="AS">Samoa amerikin</territory>
			<territory type="AT">Lostris</territory>
			<territory type="AU">Lostrali</territory>
			<territory type="AW">Aruba</territory>
			<territory type="AZ">Azerbaïdjan</territory>
			<territory type="BA">Bosni-Herzegovinn</territory>
			<territory type="BB">Barbad</territory>
			<territory type="BD">Banglades</territory>
			<territory type="BE">Belzik</territory>
			<territory type="BF">Burkina Faso</territory>
			<territory type="BG">Bilgari</territory>
			<territory type="BH">Bahreïn</territory>
			<territory type="BI">Burundi</territory>
			<territory type="BJ">Benin</territory>
			<territory type="BM">Bermid</territory>
			<territory type="BN">Brunei</territory>
			<territory type="BO">Bolivi</territory>
			<territory type="BR">Brezil</territory>
			<territory type="BS">Bahamas</territory>
			<territory type="BT">Boutan</territory>
			<territory type="BW">Botswana</territory>
			<territory type="BY">Belaris</territory>
			<territory type="BZ">Beliz</territory>
			<territory type="CA">Kanada</territory>
			<territory type="CD">Repiblik demokratik Kongo</territory>
			<territory type="CF">Repiblik Lafrik Santral</territory>
			<territory type="CG">Kongo</territory>
			<territory type="CH">Laswis</territory>
			<territory type="CI">Côte d'Ivoire</territory>
			<territory type="CK">Zil Cook</territory>
			<territory type="CL">Shili</territory>
			<territory type="CM">Kamerounn</territory>
			<territory type="CN">Lasinn</territory>
			<territory type="CO">Kolonbi</territory>
			<territory type="CR">Costa Rica</territory>
			<territory type="CU">Cuba</territory>
			<territory type="CV">Kap-Ver</territory>
			<territory type="CY">Cyprus</territory>
			<territory type="CZ">Repiblik Chek</territory>
			<territory type="DE">Almagn</territory>
			<territory type="DJ">Djibouti</territory>
			<territory type="DK">Dannmark</territory>
			<territory type="DM">Dominik</territory>
			<territory type="DO">Repiblik dominikin</territory>
			<territory type="DZ">Alzeri</territory>
			<territory type="EC">Ekwater</territory>
			<territory type="EE">Estoni</territory>
			<territory type="EG">Lezipt</territory>
			<territory type="ER">Erythre</territory>
			<territory type="ES">Lespagn</territory>
			<territory type="ET">Letiopi</territory>
			<territory type="FI">Finland</territory>
			<territory type="FJ">Fidji</territory>
			<territory type="FK">Zil malwinn</territory>
			<territory type="FM">Mikronezi</territory>
			<territory type="FR">Lafrans</territory>
			<territory type="GA">Gabon</territory>
			<territory type="GB">United Kingdom</territory>
			<territory type="GD">Grenad</territory>
			<territory type="GE">Zeorzi</territory>
			<territory type="GF">Gwiyann franse</territory>
			<territory type="GH">Ghana</territory>
			<territory type="GI">Zibraltar</territory>
			<territory type="GL">Greenland</territory>
			<territory type="GM">Gambi</territory>
			<territory type="GN">Gine</territory>
			<territory type="GP">Guadloup</territory>
			<territory type="GQ">Gine ekwatoryal</territory>
			<territory type="GR">Gres</territory>
			<territory type="GT">Guatemala</territory>
			<territory type="GU">Guam</territory>
			<territory type="GW">Gine-Bisau</territory>
			<territory type="GY">Guyana</territory>
			<territory type="HN">Honduras</territory>
			<territory type="HR">Kroasi</territory>
			<territory type="HT">Ayti</territory>
			<territory type="HU">Ongri</territory>
			<territory type="ID">Indonezi</territory>
			<territory type="IE">Irland</territory>
			<territory type="IL">Izrael</territory>
			<territory type="IN">Lenn</territory>
			<territory type="IO">Teritwar Britanik Losean Indien</territory>
			<territory type="IQ">Irak</territory>
			<territory type="IR">Iran</territory>
			<territory type="IS">Island</territory>
			<territory type="IT">Itali</territory>
			<territory type="JM">Zamaik</territory>
			<territory type="JO">Zordani</territory>
			<territory type="JP">Zapon</territory>
			<territory type="KE">Kenya</territory>
			<territory type="KG">Kirghizistan</territory>
			<territory type="KH">Kambodj</territory>
			<territory type="KI">Kiribati</territory>
			<territory type="KM">Komor</territory>
			<territory type="KN">Saint-Christophe-ek-Niévès</territory>
			<territory type="KP">Lakore-dinor</territory>
			<territory type="KR">Lakore-disid</territory>
			<territory type="KW">Koweit</territory>
			<territory type="KY">Zil Kayman</territory>
			<territory type="KZ">Kazakstan</territory>
			<territory type="LA">Laos</territory>
			<territory type="LB">Liban</territory>
			<territory type="LC">Sainte-Lucie</territory>
			<territory type="LI">Liechtenstein</territory>
			<territory type="LK">Sri Lanka</territory>
			<territory type="LR">Liberia</territory>
			<territory type="LS">Lezoto</territory>
			<territory type="LT">Lituani</territory>
			<territory type="LU">Luxembourg</territory>
			<territory type="LV">Letoni</territory>
			<territory type="LY">Libi</territory>
			<territory type="MA">Marok</territory>
			<territory type="MC">Monako</territory>
			<territory type="MD">Moldavi</territory>
			<territory type="MG">Madagaskar</territory>
			<territory type="MH">Zil Marshall</territory>
			<territory type="MK">Masedwann</territory>
			<territory type="ML">Mali</territory>
			<territory type="MM">Myanmar</territory>
			<territory type="MN">Mongoli</territory>
			<territory type="MP">Zil Maryann dinor</territory>
			<territory type="MQ">Martinik</territory>
			<territory type="MR">Moritani</territory>
			<territory type="MS">Montsera</territory>
			<territory type="MT">Malt</territory>
			<territory type="MU">Moris</territory>
			<territory type="MV">Maldiv</territory>
			<territory type="MW">Malawi</territory>
			<territory type="MX">Mexik</territory>
			<territory type="MY">Malezi</territory>
			<territory type="MZ">Mozambik</territory>
			<territory type="NA">Namibi</territory>
			<territory type="NC">Nouvel-Kaledoni</territory>
			<territory type="NE">Nizer</territory>
			<territory type="NF">Lil Norfolk</territory>
			<territory type="NG">Nizeria</territory>
			<territory type="NI">Nicaragua</territory>
			<territory type="NL">Oland</territory>
			<territory type="NO">Norvez</territory>
			<territory type="NP">Nepal</territory>
			<territory type="NR">Nauru</territory>
			<territory type="NU">Niowe</territory>
			<territory type="NZ">Nouvel Zeland</territory>
			<territory type="OM">Oman</territory>
			<territory type="PA">Panama</territory>
			<territory type="PE">Perou</territory>
			<territory type="PF">Polinezi franse</territory>
			<territory type="PG">Papouazi-Nouvel-Gine</territory>
			<territory type="PH">Filipinn</territory>
			<territory type="PK">Pakistan</territory>
			<territory type="PL">Pologn</territory>
			<territory type="PM">Saint-Pierre-ek-Miquelon</territory>
			<territory type="PN">Pitcairn</territory>
			<territory type="PR">Porto Rico</territory>
			<territory type="PS">Teritwar Palestinn</territory>
			<territory type="PT">Portigal</territory>
			<territory type="PW">Palau</territory>
			<territory type="PY">Paraguay</territory>
			<territory type="QA">Katar</territory>
			<territory type="RE">Larenion</territory>
			<territory type="RO">Roumani</territory>
			<territory type="RU">Larisi</territory>
			<territory type="RW">Rwanda</territory>
			<territory type="SA">Larabi Saoudit</territory>
			<territory type="SB">Zil Salomon</territory>
			<territory type="SC">Sesel</territory>
			<territory type="SD">Soudan</territory>
			<territory type="SE">Laswed</territory>
			<territory type="SG">Singapour</territory>
			<territory type="SH">Sainte-Hélène</territory>
			<territory type="SI">Sloveni</territory>
			<territory type="SK">Slovaki</territory>
			<territory type="SL">Sierra Leone</territory>
			<territory type="SM">Saint-Marin</territory>
			<territory type="SN">Senegal</territory>
			<territory type="SO">Somali</territory>
			<territory type="SR">Surinam</territory>
			<territory type="ST">São Tome-ek-Prínsip</territory>
			<territory type="SV">Salvador</territory>
			<territory type="SY">Lasiri</territory>
			<territory type="SZ">Swaziland</territory>
			<territory type="TC">Zil Tirk ek Caïcos</territory>
			<territory type="TD">Tchad</territory>
			<territory type="TG">Togo</territory>
			<territory type="TH">Thayland</territory>
			<territory type="TJ">Tadjikistan</territory>
			<territory type="TK">Tokelau</territory>
			<territory type="TL">Timor oriantal</territory>
			<territory type="TM">Turkmenistan</territory>
			<territory type="TN">Tinizi</territory>
			<territory type="TO">Tonga</territory>
			<territory type="TR">Tirki</territory>
			<territory type="TT">Trinite-ek-Tobago</territory>
			<territory type="TV">Tuvalu</territory>
			<territory type="TW">Taiwan</territory>
			<territory type="TZ">Tanzani</territory>
			<territory type="UA">Ikrenn</territory>
			<territory type="UG">Ouganda</territory>
			<territory type="US">Lamerik</territory>
			<territory type="UY">Uruguay</territory>
			<territory type="UZ">Ouzbekistan</territory>
			<territory type="VA">Lata Vatikan</territory>
			<territory type="VC">Saint-Vincent-ek-Grenadines</territory>
			<territory type="VE">Venezuela</territory>
			<territory type="VG">Zil vierz britanik</territory>
			<territory type="VI">Zil Vierz Lamerik</territory>
			<territory type="VN">Vietnam</territory>
			<territory type="VU">Vanuatu</territory>
			<territory type="WF">Wallis-ek-Futuna</territory>
			<territory type="WS">Samoa</territory>
			<territory type="YE">Yemenn</territory>
			<territory type="YT">Mayot</territory>
			<territory type="ZA">Sid-Afrik</territory>
			<territory type="ZM">Zambi</territory>
			<territory type="ZW">Zimbabwe</territory>
		</territories>
	</localeDisplayNames>
	<characters>
		<exemplarCharacters>[a b c d e f g h i j k l m n o p r s t u v w x y z]</exemplarCharacters>
		<exemplarCharacters type="index">[A B C D E F G H I J K L M N O P R S T U V W X Y Z]</exemplarCharacters>
	</characters>
	<delimiters>
		<quotationStart>“</quotationStart>
		<quotationEnd>”</quotationEnd>
		<alternateQuotationStart>‘</alternateQuotationStart>
		<alternateQuotationEnd>’</alternateQuotationEnd>
	</delimiters>
	<dates>
		<calendars>
			<calendar type="generic">
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>EEEE d MMMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>d MMMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>d MMM, y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>d/M/y GGGGG</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<dateTimeFormats>
					<availableFormats>
						<dateFormatItem id="M">M</dateFormatItem>
						<dateFormatItem id="Md">d/M</dateFormatItem>
						<dateFormatItem id="MMd">d/MM</dateFormatItem>
						<dateFormatItem id="MMdd">dd/MM</dateFormatItem>
						<dateFormatItem id="MMM">MMM</dateFormatItem>
						<dateFormatItem id="MMMd">d MMM</dateFormatItem>
						<dateFormatItem id="MMMEd">E d MMM</dateFormatItem>
						<dateFormatItem id="MMMMd">d MMMM</dateFormatItem>
						<dateFormatItem id="MMMMEd">E d MMMM</dateFormatItem>
						<dateFormatItem id="ms">m:ss</dateFormatItem>
						<dateFormatItem id="y">y</dateFormatItem>
						<dateFormatItem id="yM">M/y</dateFormatItem>
						<dateFormatItem id="yMEd">E d/M/y</dateFormatItem>
						<dateFormatItem id="yMM">MM/y</dateFormatItem>
						<dateFormatItem id="yMMM">MMM y</dateFormatItem>
						<dateFormatItem id="yMMMd">d MMM y</dateFormatItem>
						<dateFormatItem id="yMMMEd">E d MMM y</dateFormatItem>
						<dateFormatItem id="yMMMM">MMMM y</dateFormatItem>
						<dateFormatItem id="yQQQ">QQQ y</dateFormatItem>
						<dateFormatItem id="yQQQQ">QQQQ y</dateFormatItem>
					</availableFormats>
				</dateTimeFormats>
			</calendar>
			<calendar type="gregorian">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<month type="1">zan</month>
							<month type="2">fev</month>
							<month type="3">mar</month>
							<month type="4">avr</month>
							<month type="5">me</month>
							<month type="6">zin</month>
							<month type="7">zil</month>
							<month type="8">out</month>
							<month type="9">sep</month>
							<month type="10">okt</month>
							<month type="11">nov</month>
							<month type="12">des</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">zanvie</month>
							<month type="2">fevriye</month>
							<month type="3">mars</month>
							<month type="4">avril</month>
							<month type="5">me</month>
							<month type="6">zin</month>
							<month type="7">zilye</month>
							<month type="8">out</month>
							<month type="9">septam</month>
							<month type="10">oktob</month>
							<month type="11">novam</month>
							<month type="12">desam</month>
						</monthWidth>
					</monthContext>
					<monthContext type="stand-alone">
						<monthWidth type="narrow">
							<month type="1">z</month>
							<month type="2">f</month>
							<month type="3">m</month>
							<month type="4">a</month>
							<month type="5">m</month>
							<month type="6">z</month>
							<month type="7">z</month>
							<month type="8">o</month>
							<month type="9">s</month>
							<month type="10">o</month>
							<month type="11">n</month>
							<month type="12">d</month>
						</monthWidth>
					</monthContext>
				</months>
				<days>
					<dayContext type="format">
						<dayWidth type="abbreviated">
							<day type="sun">dim</day>
							<day type="mon">lin</day>
							<day type="tue">mar</day>
							<day type="wed">mer</day>
							<day type="thu">ze</day>
							<day type="fri">van</day>
							<day type="sat">sam</day>
						</dayWidth>
						<dayWidth type="wide">
							<day type="sun">dimans</day>
							<day type="mon">lindi</day>
							<day type="tue">mardi</day>
							<day type="wed">merkredi</day>
							<day type="thu">zedi</day>
							<day type="fri">vandredi</day>
							<day type="sat">samdi</day>
						</dayWidth>
					</dayContext>
					<dayContext type="stand-alone">
						<dayWidth type="narrow">
							<day type="sun">d</day>
							<day type="mon">l</day>
							<day type="tue">m</day>
							<day type="wed">m</day>
							<day type="thu">z</day>
							<day type="fri">v</day>
							<day type="sat">s</day>
						</dayWidth>
					</dayContext>
				</days>
				<quarters>
					<quarterContext type="format">
						<quarterWidth type="abbreviated">
							<quarter type="1">T1</quarter>
							<quarter type="2">T2</quarter>
							<quarter type="3">T3</quarter>
							<quarter type="4">T4</quarter>
						</quarterWidth>
						<quarterWidth type="wide">
							<quarter type="1">1e trimes</quarter>
							<quarter type="2">2em trimes</quarter>
							<quarter type="3">3em trimes</quarter>
							<quarter type="4">4em trimes</quarter>
						</quarterWidth>
					</quarterContext>
				</quarters>
				<eras>
					<eraNames>
						<era type="0">avan Zezi-Krist</era>
						<era type="1">apre Zezi-Krist</era>
					</eraNames>
					<eraAbbr>
						<era type="0">av. Z-K</era>
						<era type="1">ap. Z-K</era>
					</eraAbbr>
				</eras>
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>EEEE d MMMM y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>d MMMM y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>d MMM, y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>d/M/y</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<timeFormats>
					<timeFormatLength type="full">
						<timeFormat>
							<pattern>HH:mm:ss zzzz</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="long">
						<timeFormat>
							<pattern>HH:mm:ss z</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="medium">
						<timeFormat>
							<pattern>HH:mm:ss</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="short">
						<timeFormat>
							<pattern>HH:mm</pattern>
						</timeFormat>
					</timeFormatLength>
				</timeFormats>
				<dateTimeFormats>
					<availableFormats>
						<dateFormatItem id="M">M</dateFormatItem>
						<dateFormatItem id="Md">d/M</dateFormatItem>
						<dateFormatItem id="MMd">d/MM</dateFormatItem>
						<dateFormatItem id="MMdd">dd/MM</dateFormatItem>
						<dateFormatItem id="MMM">MMM</dateFormatItem>
						<dateFormatItem id="MMMd">d MMM</dateFormatItem>
						<dateFormatItem id="MMMEd">E d MMM</dateFormatItem>
						<dateFormatItem id="MMMMd">d MMMM</dateFormatItem>
						<dateFormatItem id="MMMMEd">E d MMMM</dateFormatItem>
						<dateFormatItem id="ms">m:ss</dateFormatItem>
						<dateFormatItem id="y">y</dateFormatItem>
						<dateFormatItem id="yM">M/y</dateFormatItem>
						<dateFormatItem id="yMEd">E d/M/y</dateFormatItem>
						<dateFormatItem id="yMM">MM/y</dateFormatItem>
						<dateFormatItem id="yMMM">MMM y</dateFormatItem>
						<dateFormatItem id="yMMMd">d MMM y</dateFormatItem>
						<dateFormatItem id="yMMMEd">E d MMM y</dateFormatItem>
						<dateFormatItem id="yMMMM">MMMM y</dateFormatItem>
						<dateFormatItem id="yQQQ">QQQ y</dateFormatItem>
						<dateFormatItem id="yQQQQ">QQQQ y</dateFormatItem>
					</availableFormats>
				</dateTimeFormats>
			</calendar>
		</calendars>
		<fields>
			<field type="era">
				<displayName>Lepok</displayName>
			</field>
			<field type="year">
				<displayName>Lane</displayName>
			</field>
			<field type="month">
				<displayName>Mwa</displayName>
			</field>
			<field type="week">
				<displayName>Semenn</displayName>
			</field>
			<field type="day">
				<displayName>Zour</displayName>
				<relative type="-1">Yer</relative>
				<relative type="0">Zordi</relative>
				<relative type="1">Demin</relative>
			</field>
			<field type="weekday">
				<displayName>Zour lasemenn</displayName>
			</field>
			<field type="dayperiod">
				<displayName>Peryod dan lazourne</displayName>
			</field>
			<field type="hour">
				<displayName>Ler</displayName>
			</field>
			<field type="minute">
				<displayName>Minit</displayName>
			</field>
			<field type="second">
				<displayName>Segonn</displayName>
			</field>
			<field type="zone">
				<displayName>Peryod letan</displayName>
			</field>
		</fields>
	</dates>
	<numbers>
		<symbols numberSystem="latn">
			<group> </group>
		</symbols>
		<currencies>
			<currency type="AED">
				<displayName>dirham Emira arab ini</displayName>
			</currency>
			<currency type="AOA">
				<displayName>kwanza angole</displayName>
			</currency>
			<currency type="AUD">
				<displayName>dolar ostralien</displayName>
			</currency>
			<currency type="BHD">
				<displayName>dinar bahreïn</displayName>
			</currency>
			<currency type="BIF">
				<displayName>fran burunde</displayName>
			</currency>
			<currency type="BWP">
				<displayName>pula ya botswane</displayName>
			</currency>
			<currency type="CAD">
				<displayName>dolar kanadien</displayName>
			</currency>
			<currency type="CDF">
				<displayName>fran kongole</displayName>
			</currency>
			<currency type="CHF">
				<displayName>fran swis</displayName>
			</currency>
			<currency type="CNY">
				<displayName>yuan renminbi sinwa</displayName>
			</currency>
			<currency type="CVE">
				<displayName>eskudo kapverdien</displayName>
			</currency>
			<currency type="DJF">
				<displayName>fran djiboutien</displayName>
			</currency>
			<currency type="DZD">
				<displayName>dinar alzerien</displayName>
			</currency>
			<currency type="EGP">
				<displayName>liv ezipsien</displayName>
			</currency>
			<currency type="ERN">
				<displayName>nafka erythreen</displayName>
			</currency>
			<currency type="ETB">
				<displayName>birr etiopien</displayName>
			</currency>
			<currency type="EUR">
				<displayName>euro</displayName>
			</currency>
			<currency type="GBP">
				<displayName>liv sterlin</displayName>
			</currency>
			<currency type="GHC">
				<displayName>sedi ganeen</displayName>
			</currency>
			<currency type="GMD">
				<displayName>dalasi gambien</displayName>
			</currency>
			<currency type="GNS">
				<displayName>fran gineen</displayName>
			</currency>
			<currency type="INR">
				<displayName>roupi</displayName>
			</currency>
			<currency type="JPY">
				<displayName>yen zapone</displayName>
			</currency>
			<currency type="KES">
				<displayName>shiling kenyan</displayName>
			</currency>
			<currency type="KMF">
				<displayName>fran komorien</displayName>
			</currency>
			<currency type="LRD">
				<displayName>dolar liberien</displayName>
			</currency>
			<currency type="LSL">
				<displayName>loti lezoto</displayName>
			</currency>
			<currency type="LYD">
				<displayName>dinar libien</displayName>
			</currency>
			<currency type="MAD">
				<displayName>dirham marokin</displayName>
			</currency>
			<currency type="MGA">
				<displayName>fran malgas</displayName>
			</currency>
			<currency type="MRO">
				<displayName>ouguiya moritanien</displayName>
			</currency>
			<currency type="MUR">
				<displayName>roupi morisien</displayName>
				<symbol>Rs</symbol>
			</currency>
			<currency type="MWK">
				<displayName>kwacha malawit</displayName>
			</currency>
			<currency type="MZM">
				<displayName>metikal mozanbikin</displayName>
			</currency>
			<currency type="NAD">
				<displayName>dolar namibien</displayName>
			</currency>
			<currency type="NGN">
				<displayName>naira nizerian</displayName>
			</currency>
			<currency type="RWF">
				<displayName>fran rwande</displayName>
			</currency>
			<currency type="SAR">
				<displayName>rial saoudien</displayName>
			</currency>
			<currency type="SCR">
				<displayName>roupi seselwa</displayName>
			</currency>
			<currency type="SDG">
				<displayName>dinar soudane</displayName>
			</currency>
			<currency type="SDP">
				<displayName>liv soudane</displayName>
			</currency>
			<currency type="SHP">
				<displayName>liv Sainte-Hélène</displayName>
			</currency>
			<currency type="SLL">
				<displayName>leonn Sierra-Leone</displayName>
			</currency>
			<currency type="SOS">
				<displayName>shilingi somalien</displayName>
			</currency>
			<currency type="STD">
				<displayName>dobra santomeen</displayName>
			</currency>
			<currency type="SZL">
				<displayName>lilangeni swazi</displayName>
			</currency>
			<currency type="TND">
				<displayName>dinar tinizien</displayName>
			</currency>
			<currency type="TZS">
				<displayName>shiling tanzanien</displayName>
			</currency>
			<currency type="UGX">
				<displayName>shiling ougande</displayName>
			</currency>
			<currency type="USD">
				<displayName>dolar amerikin</displayName>
			</currency>
			<currency type="XAF">
				<displayName>fran CFA (BEAC)</displayName>
			</currency>
			<currency type="XOF">
				<displayName>fran CFA (BCEAO)</displayName>
			</currency>
			<currency type="ZAR">
				<displayName>rand sid-afrikin</displayName>
			</currency>
			<currency type="ZMK">
				<displayName>kwacha zanbien (1968–2012)</displayName>
			</currency>
			<currency type="ZMW">
				<displayName>kwacha zanbien</displayName>
			</currency>
			<currency type="ZWD">
				<displayName>dolar zimbawe</displayName>
			</currency>
		</currencies>
	</numbers>
	<posix>
		<messages>
			<yesstr>Wi:W</yesstr>
			<nostr>Non:N</nostr>
		</messages>
	</posix>
</ldml>

