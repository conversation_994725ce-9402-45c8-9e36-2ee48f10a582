<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE ldml SYSTEM "../../common/dtd/ldml.dtd">
<!-- Copyright © 1991-2013 Unicode, Inc.
CLDR data files are interpreted according to the LDML specification (http://unicode.org/reports/tr35/)
For terms of use, see http://www.unicode.org/copyright.html
-->
<ldml>
	<identity>
		<version number="$Revision: 9852 $"/>
		<generation date="$Date: 2014-02-28 23:57:43 -0600 (Fri, 28 Feb 2014) $"/>
		<language type="af"/>
	</identity>
	<localeDisplayNames>
		<localeDisplayPattern>
			<localePattern>{0} ({1})</localePattern>
			<localeSeparator>{0},{1}</localeSeparator>
			<localeKeyTypePattern>{0}: {1}</localeKeyTypePattern>
		</localeDisplayPattern>
		<languages>
			<language type="ab">Abkasies</language>
			<language type="ach">A<PERSON><PERSON></language>
			<language type="af">Afrikaans</language>
			<language type="ak">Akan</language>
			<language type="am">Amharies</language>
			<language type="ar">Arabies</language>
			<language type="ar_001">Moderne Standaard Arabies</language>
			<language type="arc">Aramees</language>
			<language type="as">Assamees</language>
			<language type="ay">Aymara</language>
			<language type="az">Azerbeidjaans</language>
			<language type="az" alt="short">Azeri</language>
			<language type="be">Wit-Russies</language>
			<language type="bem">Bemba</language>
			<language type="bg">Bulgaars</language>
			<language type="bn">Bengaals</language>
			<language type="bo">Tibettaans</language>
			<language type="br">Bretons</language>
			<language type="bs">Bosnies</language>
			<language type="ca">Katalaans</language>
			<language type="chr">Cherokees</language>
			<language type="ckb">Sorani Koerdies</language>
			<language type="co">Korsikaans</language>
			<language type="cop" draft="unconfirmed">Kopties</language>
			<language type="cs">Tsjeggies</language>
			<language type="cu" draft="unconfirmed">Kerkslawies</language>
			<language type="cy">Wallies</language>
			<language type="da">Deens</language>
			<language type="de">Duits</language>
			<language type="de_AT">Oostenrykse Duits</language>
			<language type="de_CH">Switserse hoog-Duits</language>
			<language type="dv">Divehi</language>
			<language type="dz">Dzongkha</language>
			<language type="ee">Ewe</language>
			<language type="efi">Efik</language>
			<language type="egy" draft="contributed">Antieke Egipties</language>
			<language type="el">Grieks</language>
			<language type="en">Engels</language>
			<language type="en_AU">Australiese Engels</language>
			<language type="en_CA">Kanadese Engels</language>
			<language type="en_GB">Britse Engels</language>
			<language type="en_GB" alt="short">Engels (GB)</language>
			<language type="en_US">Amerikaanse Engels</language>
			<language type="en_US" alt="short">Engels (US)</language>
			<language type="eo">Esperanto</language>
			<language type="es">Spaans</language>
			<language type="es_419">Latyns-Amerikaanse Spaans</language>
			<language type="es_ES">Europese Spaans</language>
			<language type="es_MX">Meksikaanse Spaans</language>
			<language type="et">Estnies</language>
			<language type="eu">Baskies</language>
			<language type="fa">Persies</language>
			<language type="fi">Fins</language>
			<language type="fil">Filippyns</language>
			<language type="fj">Fidjiaans</language>
			<language type="fo">Faroees</language>
			<language type="fr">Frans</language>
			<language type="fr_CA">Kanadese Frans</language>
			<language type="fr_CH">Switserse Frans</language>
			<language type="fy">Wes-Fries</language>
			<language type="ga">Iers</language>
			<language type="gaa">Gaa</language>
			<language type="gd">Skotse Gallies</language>
			<language type="gl">Galisies</language>
			<language type="gn">Guarani</language>
			<language type="got">Goties</language>
			<language type="grc" draft="contributed">Antieke Grieks</language>
			<language type="gsw">Switserse Duits</language>
			<language type="gu">Gudjarati</language>
			<language type="gv" draft="unconfirmed">Manx</language>
			<language type="ha">Hausa</language>
			<language type="haw">Hawaiies</language>
			<language type="he">Hebreeus</language>
			<language type="hi">Hindi</language>
			<language type="hit" draft="unconfirmed">Hetities</language>
			<language type="hr">Kroaties</language>
			<language type="ht">Haïtiaans</language>
			<language type="hu">Hongaars</language>
			<language type="hy">Armeens</language>
			<language type="ia">Interlingua</language>
			<language type="id">Indonesies</language>
			<language type="ie" draft="provisional">Interlingue</language>
			<language type="ig">Igbo</language>
			<language type="is">Yslands</language>
			<language type="it">Italiaans</language>
			<language type="ja">Japannees</language>
			<language type="jv">Javaans</language>
			<language type="ka">Georgies</language>
			<language type="kg">Kongolees</language>
			<language type="kk">Kazak</language>
			<language type="km">Khmer</language>
			<language type="kn">Kannada</language>
			<language type="ko">Koreaans</language>
			<language type="kru" draft="unconfirmed">Kurukh</language>
			<language type="ks">Kasjmirs</language>
			<language type="ku">Koerdies</language>
			<language type="kw" draft="unconfirmed">Kornies</language>
			<language type="ky">Kirgisies</language>
			<language type="la">Latyn</language>
			<language type="lb">Luxemburgs</language>
			<language type="lg">Ganda</language>
			<language type="li" draft="unconfirmed">Limburgs</language>
			<language type="ln">Lingaals</language>
			<language type="lo">Lao</language>
			<language type="loz">Lozi</language>
			<language type="lt">Litaus</language>
			<language type="lua">Luba-Lulua</language>
			<language type="lv">Letties</language>
			<language type="mas" draft="unconfirmed">Masai</language>
			<language type="mfe">Morisjen</language>
			<language type="mg">Malgassies</language>
			<language type="mi">Maori</language>
			<language type="mk">Masedonies</language>
			<language type="ml">Malabaars</language>
			<language type="mn">Mongools</language>
			<language type="mr">Marathi</language>
			<language type="ms">Maleisies</language>
			<language type="mt">Maltees</language>
			<language type="mul">Veelvuldige tale</language>
			<language type="my">Birmaans</language>
			<language type="nb">Noorse Bokmål</language>
			<language type="nd">Noord-Ndebele</language>
			<language type="ne">Nepalees</language>
			<language type="nl">Nederlands</language>
			<language type="nl_BE">Vlaams</language>
			<language type="nn">Noorweegse Nynorsk</language>
			<language type="no" draft="unconfirmed">Noors</language>
			<language type="nr">Suid-Ndebele</language>
			<language type="nso">Noord-Sotho</language>
			<language type="ny">Nyanja</language>
			<language type="nyn">Nyankole</language>
			<language type="oc">Oksitaans</language>
			<language type="om">Oromo</language>
			<language type="or">Oriya</language>
			<language type="os">Osseties</language>
			<language type="pa">Pandjabi</language>
			<language type="phn" draft="unconfirmed">Fenisies</language>
			<language type="pl">Pools</language>
			<language type="ps">Pasjto</language>
			<language type="ps" alt="variant">Pushto</language>
			<language type="pt">Portugees</language>
			<language type="pt_BR">Brasiliaanse Portugees</language>
			<language type="pt_PT">Europese Portugees</language>
			<language type="qu">Quechua</language>
			<language type="rm">Reto-Romaans</language>
			<language type="rn">Rundi</language>
			<language type="ro">Roemeens</language>
			<language type="ru">Russies</language>
			<language type="rw">Rwandees</language>
			<language type="sa">Sanskrit</language>
			<language type="sco" draft="unconfirmed">Skots</language>
			<language type="sd">Sindhi</language>
			<language type="se">Noordelike Sami</language>
			<language type="sg">Sango</language>
			<language type="sh" draft="unconfirmed">Serwo-Kroaties</language>
			<language type="si">Sinhala</language>
			<language type="sk">Slowaaks</language>
			<language type="sl">Sloweens</language>
			<language type="sm">Samoaans</language>
			<language type="sn">Shona</language>
			<language type="so">Somalies</language>
			<language type="sq">Albanees</language>
			<language type="sr">Serwies</language>
			<language type="ss">Swazi</language>
			<language type="st">Suid-Sotho</language>
			<language type="su">Sundanees</language>
			<language type="sv">Sweeds</language>
			<language type="sw">Swahili</language>
			<language type="swb" draft="unconfirmed">Shimaorees</language>
			<language type="ta">Tamil</language>
			<language type="te">Telugu</language>
			<language type="tet">Tetum</language>
			<language type="tg">Tadjik</language>
			<language type="th">Thais</language>
			<language type="ti">Tigrinya</language>
			<language type="tk">Turkmeens</language>
			<language type="tlh">Klingon</language>
			<language type="tn">Tswana</language>
			<language type="to">Tongaans</language>
			<language type="tpi">Tok Pisin</language>
			<language type="tr">Turks</language>
			<language type="ts">Tsonga</language>
			<language type="tt">Tataars</language>
			<language type="tum">Toemboeka</language>
			<language type="tw" draft="unconfirmed">Twi</language>
			<language type="ty">Tahities</language>
			<language type="ug">Uighur</language>
			<language type="ug" alt="variant">Uyghur</language>
			<language type="uk">Oekraïens</language>
			<language type="und">Onbekende of ongeldige taal</language>
			<language type="ur">Oerdoe</language>
			<language type="uz">Oezbeeks</language>
			<language type="ve">Venda</language>
			<language type="vi">Viëtnamees</language>
			<language type="wo">Wolof</language>
			<language type="xh">Xhosa</language>
			<language type="yi">Jiddisj</language>
			<language type="yo">Yoruba</language>
			<language type="yue" draft="unconfirmed">Kantonees</language>
			<language type="zgh">Standaard Marokkaanse Tamazight</language>
			<language type="zh">Sjinees</language>
			<language type="zh_Hans">Vereenvoudigde Chinees</language>
			<language type="zh_Hant">Tradisionele Chinees</language>
			<language type="zu">Zoeloe</language>
			<language type="zxx">Geen linguistiese inhoud</language>
		</languages>
		<scripts>
			<script type="Arab">Arabies</script>
			<script type="Arab" alt="variant">Perso-Arabies</script>
			<script type="Armn">Armeens</script>
			<script type="Beng">Bengaals</script>
			<script type="Bopo">Bopomofo</script>
			<script type="Brai">Braille</script>
			<script type="Copt" draft="unconfirmed">Koptieses</script>
			<script type="Cyrl">Sirillies</script>
			<script type="Cyrs" draft="unconfirmed">Ou Kerkslawiese Sirillieses</script>
			<script type="Deva">Devanagari</script>
			<script type="Egyp" draft="unconfirmed">Egiptieses hiërogliewe</script>
			<script type="Ethi">Etiopies</script>
			<script type="Geor">Georgies</script>
			<script type="Goth" draft="unconfirmed">Gotieses</script>
			<script type="Grek">Grieks</script>
			<script type="Gujr">Gudjarati</script>
			<script type="Guru">Gurmukhi</script>
			<script type="Hang">Hangul</script>
			<script type="Hani">Han</script>
			<script type="Hans">Vereenvoudig</script>
			<script type="Hans" alt="stand-alone">Vereenvoudigde Han</script>
			<script type="Hant">Tradisioneel</script>
			<script type="Hant" alt="stand-alone">Tradisionele Han</script>
			<script type="Hebr">Hebreeus</script>
			<script type="Hira">Hiragana</script>
			<script type="Jpan">Japannees</script>
			<script type="Kana">Katakana</script>
			<script type="Khmr">Khmer</script>
			<script type="Knda">Kannada</script>
			<script type="Kore">Koreaans</script>
			<script type="Laoo">Lao</script>
			<script type="Latn">Latyn</script>
			<script type="Mlym">Malabaars</script>
			<script type="Mong">Mongools</script>
			<script type="Mymr">Mianmar</script>
			<script type="Orya">Oriya</script>
			<script type="Phnx" draft="unconfirmed">Fenisieses</script>
			<script type="Sinh">Sinhala</script>
			<script type="Taml">Tamil</script>
			<script type="Telu">Telugu</script>
			<script type="Thaa">Thaana</script>
			<script type="Thai">Thai</script>
			<script type="Tibt">Tibettaans</script>
			<script type="Ugar" draft="unconfirmed">Ugaritieses</script>
			<script type="Visp" draft="unconfirmed">Visible Speech-karakters</script>
			<script type="Zsym">Simbole</script>
			<script type="Zxxx">Ongeskrewe</script>
			<script type="Zyyy">Algemeen</script>
			<script type="Zzzz">Onbekende skryfstelsel</script>
		</scripts>
		<territories>
			<territory type="001">Wêreld</territory>
			<territory type="002">Afrika</territory>
			<territory type="003">Noord-Amerika</territory>
			<territory type="005">Suid-Amerika</territory>
			<territory type="009">Oseanië</territory>
			<territory type="011">Wes-Afrika</territory>
			<territory type="013">Sentraal-Amerika</territory>
			<territory type="014">Oos-Afrika</territory>
			<territory type="015">Noord-Afrika</territory>
			<territory type="017">Midde-Afrika</territory>
			<territory type="018">Suider-Afrika</territory>
			<territory type="019">Amerikas</territory>
			<territory type="021">Noordelike Amerika</territory>
			<territory type="029">Karibbies</territory>
			<territory type="030">Oos-Asië</territory>
			<territory type="034">Suid-Asië</territory>
			<territory type="035">Suidoos-Asië</territory>
			<territory type="039">Suid-Europa</territory>
			<territory type="053">Australasië</territory>
			<territory type="054">Melanesië</territory>
			<territory type="057">Mikronesiese streek</territory>
			<territory type="061">Polinesië</territory>
			<territory type="142">Asië</territory>
			<territory type="143">Sentraal-Asië</territory>
			<territory type="145">Wes-Asië</territory>
			<territory type="150">Europa</territory>
			<territory type="151">Oos-Europa</territory>
			<territory type="154">Noord-Europa</territory>
			<territory type="155">Wes-Europa</territory>
			<territory type="419">Latyns-Amerika</territory>
			<territory type="AC">Ascensioneiland</territory>
			<territory type="AD">Andorra</territory>
			<territory type="AE">Verenigde Arabiese Emirate</territory>
			<territory type="AF">Afganistan</territory>
			<territory type="AG">Antigua en Barbuda</territory>
			<territory type="AI">Anguilla</territory>
			<territory type="AL">Albanië</territory>
			<territory type="AM">Armenië</territory>
			<territory type="AN">Nederlands-Antille</territory>
			<territory type="AO">Angola</territory>
			<territory type="AQ">Antarktika</territory>
			<territory type="AR">Argentinië</territory>
			<territory type="AS">Amerikaans-Samoa</territory>
			<territory type="AT">Oostenryk</territory>
			<territory type="AU">Australië</territory>
			<territory type="AW">Aruba</territory>
			<territory type="AX">Ålandeilande</territory>
			<territory type="AZ">Azerbeidjan</territory>
			<territory type="BA">Bosnië en Herzegowina</territory>
			<territory type="BB">Barbados</territory>
			<territory type="BD">Bangladesj</territory>
			<territory type="BE">België</territory>
			<territory type="BF">Burkina Faso</territory>
			<territory type="BG">Bulgarye</territory>
			<territory type="BH">Bahrein</territory>
			<territory type="BI">Burundi</territory>
			<territory type="BJ">Benin</territory>
			<territory type="BL">Sint Barthélemy</territory>
			<territory type="BM">Bermuda</territory>
			<territory type="BN">Broenei</territory>
			<territory type="BO">Bolivië</territory>
			<territory type="BQ">Karibiese Nederland</territory>
			<territory type="BR">Brasilië</territory>
			<territory type="BS">Bahamas</territory>
			<territory type="BT">Bhoetan</territory>
			<territory type="BV">Bouveteiland</territory>
			<territory type="BW">Botswana</territory>
			<territory type="BY">Wit-Rusland</territory>
			<territory type="BZ">Belize</territory>
			<territory type="CA">Kanada</territory>
			<territory type="CC">Cocos- (Keeling) eilande</territory>
			<territory type="CD">Demokratiese Republiek van die Kongo</territory>
			<territory type="CD" alt="variant">Kongo (DRK)</territory>
			<territory type="CF">Sentraal-Afrikaanse Republiek</territory>
			<territory type="CG">Kongo</territory>
			<territory type="CG" alt="variant">Kongo (Republiek)</territory>
			<territory type="CH">Switserland</territory>
			<territory type="CI">Ivoorkus</territory>
			<territory type="CK">Cookeilande</territory>
			<territory type="CL">Chili</territory>
			<territory type="CM">Kameroen</territory>
			<territory type="CN">Sjina</territory>
			<territory type="CO">Colombië</territory>
			<territory type="CP">Clippertoneiland</territory>
			<territory type="CR">Costa Rica</territory>
			<territory type="CU">Kuba</territory>
			<territory type="CV">Kaap Verde</territory>
			<territory type="CW">Curaçao</territory>
			<territory type="CX">Kerseiland</territory>
			<territory type="CY">Siprus</territory>
			<territory type="CZ">Tjeggiese Republiek</territory>
			<territory type="DE">Duitsland</territory>
			<territory type="DG">Diego Garcia</territory>
			<territory type="DJ">Djiboeti</territory>
			<territory type="DK">Denemarke</territory>
			<territory type="DM">Dominica</territory>
			<territory type="DO">Dominikaanse Republiek</territory>
			<territory type="DZ">Algerië</territory>
			<territory type="EA">Ceuta en Melilla</territory>
			<territory type="EC">Ecuador</territory>
			<territory type="EE">Estland</territory>
			<territory type="EG">Egipte</territory>
			<territory type="EH">Wes-Sahara</territory>
			<territory type="ER">Eritrea</territory>
			<territory type="ES">Spanje</territory>
			<territory type="ET">Ethiopië</territory>
			<territory type="EU">Europese Unie</territory>
			<territory type="FI">Finland</territory>
			<territory type="FJ">Fidji</territory>
			<territory type="FK">Falklandeilande</territory>
			<territory type="FK" alt="variant">Falkland-eilande (Malvinas)</territory>
			<territory type="FM">Mikronesië</territory>
			<territory type="FO">Faroëreilande</territory>
			<territory type="FR">Frankryk</territory>
			<territory type="GA">Gaboen</territory>
			<territory type="GB">Verenigde Koninkryk</territory>
			<territory type="GB" alt="short">VK</territory>
			<territory type="GD">Grenada</territory>
			<territory type="GE">Georgië</territory>
			<territory type="GF">Frans-Guyana</territory>
			<territory type="GG">Guernsey</territory>
			<territory type="GH">Ghana</territory>
			<territory type="GI">Gibraltar</territory>
			<territory type="GL">Groenland</territory>
			<territory type="GM">Gambië</territory>
			<territory type="GN">Guinee</territory>
			<territory type="GP">Guadeloupe</territory>
			<territory type="GQ">Ekwatoriaal-Guinee</territory>
			<territory type="GR">Griekeland</territory>
			<territory type="GS">Suid-Georgië en die Suidelike Sandwicheilande</territory>
			<territory type="GT">Guatemala</territory>
			<territory type="GU">Guam</territory>
			<territory type="GW">Guinee-Bissau</territory>
			<territory type="GY">Guyana</territory>
			<territory type="HK">Hongkong</territory>
			<territory type="HK" alt="short">Hongkong</territory>
			<territory type="HM">Heard- en McDonaldeilande</territory>
			<territory type="HN">Honduras</territory>
			<territory type="HR">Kroasië</territory>
			<territory type="HT">Haïti</territory>
			<territory type="HU">Hongarye</territory>
			<territory type="IC">Kanarie-eilande</territory>
			<territory type="ID">Indonesië</territory>
			<territory type="IE">Ierland</territory>
			<territory type="IL">Israel</territory>
			<territory type="IM">Eiland Man</territory>
			<territory type="IN">Indië</territory>
			<territory type="IO">Britse Indiese Oseaangebied</territory>
			<territory type="IQ">Irak</territory>
			<territory type="IR">Iran</territory>
			<territory type="IS">Ysland</territory>
			<territory type="IT">Italië</territory>
			<territory type="JE">Jersey</territory>
			<territory type="JM">Jamaika</territory>
			<territory type="JO">Jordanië</territory>
			<territory type="JP">Japan</territory>
			<territory type="KE">Kenia</territory>
			<territory type="KG">Kirgisië</territory>
			<territory type="KH">Kambodja</territory>
			<territory type="KI">Kiribati</territory>
			<territory type="KM">Comore</territory>
			<territory type="KN">Sint Kitts en Nevis</territory>
			<territory type="KP">Noord-Korea</territory>
			<territory type="KR">Suid-Korea</territory>
			<territory type="KW">Koeweit</territory>
			<territory type="KY">Kaaimanseilande</territory>
			<territory type="KZ">Kasakstan</territory>
			<territory type="LA">Laos</territory>
			<territory type="LB">Libanon</territory>
			<territory type="LC">Sint Lucia</territory>
			<territory type="LI">Liechtenstein</territory>
			<territory type="LK">Sri Lanka</territory>
			<territory type="LR">Liberië</territory>
			<territory type="LS">Lesotho</territory>
			<territory type="LT">Litaue</territory>
			<territory type="LU">Luxemburg</territory>
			<territory type="LV">Letland</territory>
			<territory type="LY">Libië</territory>
			<territory type="MA">Marokko</territory>
			<territory type="MC">Monaco</territory>
			<territory type="MD">Moldova</territory>
			<territory type="ME">Montenegro</territory>
			<territory type="MF">Sint Martin</territory>
			<territory type="MG">Madagaskar</territory>
			<territory type="MH">Marshalleilande</territory>
			<territory type="MK">Macedonië</territory>
			<territory type="MK" alt="variant">Macedonië (VYRM)</territory>
			<territory type="ML">Mali</territory>
			<territory type="MM">Mianmar</territory>
			<territory type="MN">Mongolië</territory>
			<territory type="MO">Macau SAR China</territory>
			<territory type="MO" alt="short">Macau</territory>
			<territory type="MP">Noordelike Mariana-eilande</territory>
			<territory type="MQ">Martinique</territory>
			<territory type="MR">Mauritanië</territory>
			<territory type="MS">Montserrat</territory>
			<territory type="MT">Malta</territory>
			<territory type="MU">Mauritius</territory>
			<territory type="MV">Maledive</territory>
			<territory type="MW">Malawi</territory>
			<territory type="MX">Meksiko</territory>
			<territory type="MY">Maleisië</territory>
			<territory type="MZ">Mosambiek</territory>
			<territory type="NA">Namibië</territory>
			<territory type="NC">Nieu-Kaledonië</territory>
			<territory type="NE">Niger</territory>
			<territory type="NF">Norfolkeiland</territory>
			<territory type="NG">Nigerië</territory>
			<territory type="NI">Nicaragua</territory>
			<territory type="NL">Nederland</territory>
			<territory type="NO">Noorweë</territory>
			<territory type="NP">Nepal</territory>
			<territory type="NR">Nauru</territory>
			<territory type="NU">Niue</territory>
			<territory type="NZ">Nieu-Seeland</territory>
			<territory type="OM">Oman</territory>
			<territory type="PA">Panama</territory>
			<territory type="PE">Peru</territory>
			<territory type="PF">Frans-Polinesië</territory>
			<territory type="PG">Papoea-Nieu-Guinee</territory>
			<territory type="PH">Filippyne</territory>
			<territory type="PK">Pakistan</territory>
			<territory type="PL">Pole</territory>
			<territory type="PM">Sint Pierre en Miquelon</territory>
			<territory type="PN">Pitcairneilande</territory>
			<territory type="PR">Puerto Rico</territory>
			<territory type="PS">Palestina</territory>
			<territory type="PS" alt="short">Palestina</territory>
			<territory type="PT">Portugal</territory>
			<territory type="PW">Palau</territory>
			<territory type="PY">Paraguay</territory>
			<territory type="QA">Katar</territory>
			<territory type="QO">Omliggende Oseanië</territory>
			<territory type="RE">Réunion</territory>
			<territory type="RO">Roemenië</territory>
			<territory type="RS">Serwië</territory>
			<territory type="RU">Rusland</territory>
			<territory type="RW">Rwanda</territory>
			<territory type="SA">Saoedi-Arabië</territory>
			<territory type="SB">Solomoneilande</territory>
			<territory type="SC">Seychelle</territory>
			<territory type="SD">Soedan</territory>
			<territory type="SE">Swede</territory>
			<territory type="SG">Singapoer</territory>
			<territory type="SH">Sint Helena</territory>
			<territory type="SI">Slowenië</territory>
			<territory type="SJ">Svalbard en Jan Mayen</territory>
			<territory type="SK">Slowakye</territory>
			<territory type="SL">Sierra Leone</territory>
			<territory type="SM">San Marino</territory>
			<territory type="SN">Senegal</territory>
			<territory type="SO">Somalië</territory>
			<territory type="SR">Suriname</territory>
			<territory type="SS">Suid-Soedan</territory>
			<territory type="ST">Sao Tome en Principe</territory>
			<territory type="SV">El Salvador</territory>
			<territory type="SX">Sint Maarten</territory>
			<territory type="SY">Sirië</territory>
			<territory type="SZ">Swaziland</territory>
			<territory type="TA">Tristan da Cunha</territory>
			<territory type="TC">Turks- en Caicoseilande</territory>
			<territory type="TD">Tsjad</territory>
			<territory type="TF">Franse Suidelike Gebiede</territory>
			<territory type="TG">Togo</territory>
			<territory type="TH">Thailand</territory>
			<territory type="TJ">Tadjikistan</territory>
			<territory type="TK">Tokelau</territory>
			<territory type="TL">Oos-Timor</territory>
			<territory type="TM">Turkmenië</territory>
			<territory type="TN">Tunisië</territory>
			<territory type="TO">Tonga</territory>
			<territory type="TR">Turkye</territory>
			<territory type="TT">Trinidad en Tobago</territory>
			<territory type="TV">Tuvalu</territory>
			<territory type="TW">Taiwan</territory>
			<territory type="TZ">Tanzanië</territory>
			<territory type="UA">Oekraïne</territory>
			<territory type="UG">Uganda</territory>
			<territory type="UM">VS klein omliggende eilande</territory>
			<territory type="US">Verenigde State van Amerika</territory>
			<territory type="US" alt="short">VSA</territory>
			<territory type="UY">Uruguay</territory>
			<territory type="UZ">Oesbekistan</territory>
			<territory type="VA">Vatikaanstad</territory>
			<territory type="VC">Sint Vincent en die Grenadine</territory>
			<territory type="VE">Venezuela</territory>
			<territory type="VG">Britse Maagde-eilande</territory>
			<territory type="VI">Amerikaanse Maagde-eilande</territory>
			<territory type="VN">Viëtnam</territory>
			<territory type="VU">Vanuatu</territory>
			<territory type="WF">Wallis en Futuna</territory>
			<territory type="WS">Samoa</territory>
			<territory type="XK">Kosovo</territory>
			<territory type="YE">Jemen</territory>
			<territory type="YT">Mayotte</territory>
			<territory type="ZA">Suid-Afrika</territory>
			<territory type="ZM">Zambië</territory>
			<territory type="ZW">Zimbabwe</territory>
			<territory type="ZZ">Onbekende gebied</territory>
		</territories>
		<variants>
			<variant type="1901" draft="unconfirmed">Duitse ortografie van 1901</variant>
			<variant type="1996" draft="unconfirmed">Duitse ortografie van 1996</variant>
			<variant type="PINYIN" draft="unconfirmed">pinyin</variant>
			<variant type="REVISED" draft="unconfirmed">hersiene ortografie</variant>
			<variant type="WADEGILE" draft="unconfirmed">Wade-Giles</variant>
		</variants>
		<keys>
			<key type="calendar">Kalender</key>
			<key type="colAlternate">Ignoreer simboolsortering</key>
			<key type="colBackwards">Omgekeerde aksentsortering</key>
			<key type="colCaseFirst">Hoofletters/kleinletters-sortering</key>
			<key type="colCaseLevel">Kassensitiewe sortering</key>
			<key type="colHiraganaQuaternary">Kana-sortering</key>
			<key type="collation">Sorteervolgorde</key>
			<key type="colNormalization">Genormaliseerde sortering</key>
			<key type="colNumeric">Numeriese sortering</key>
			<key type="colStrength">Sorteringssterkte</key>
			<key type="currency">Geldeenheid</key>
			<key type="numbers">Nommers</key>
			<key type="timezone">Tydsone</key>
			<key type="va">Lokaalvariant</key>
			<key type="variableTop">Sorteer as simbole</key>
			<key type="x">Privaat gebruik</key>
		</keys>
		<types>
			<type type="arab" key="numbers">Arabies-Indiese syfers</type>
			<type type="arabext" key="numbers">Uitgebreide Arabies-Indiese syfers</type>
			<type type="armn" key="numbers">Armeense syfers</type>
			<type type="armnlow" key="numbers">Armeense kleinletter-syfers</type>
			<type type="beng" key="numbers">Bengaalse syfers</type>
			<type type="big5han" key="collation">Tradisionele Chinese sorteervolgorde - Groot5</type>
			<type type="buddhist" key="calendar">Boeddhistiese kalender</type>
			<type type="chinese" key="calendar">Chinese kalender</type>
			<type type="coptic" key="calendar">Koptiese kalender</type>
			<type type="deva" key="numbers">Devanagari syfers</type>
			<type type="dictionary" key="collation">Woordeboek-sorteervolgorde</type>
			<type type="ducet" key="collation">Verstek Unicode-sorteervolgorde</type>
			<type type="ethi" key="numbers">Ethiopiese syfers</type>
			<type type="ethiopic" key="calendar">Etiopiese kalender</type>
			<type type="ethiopic-amete-alem" key="calendar">Etiopiese Amete Alem-kalender</type>
			<type type="finance" key="numbers">Finansiële syfers</type>
			<type type="fullwide" key="numbers">Vollewydte-syfers</type>
			<type type="gb2312han" key="collation">Vereenvoudigde Chinese sorteervolgorde - GB2312</type>
			<type type="geor" key="numbers">Georgiese syfers</type>
			<type type="gregorian" key="calendar">Gregoriese kalender</type>
			<type type="grek" key="numbers">Griekse syfers</type>
			<type type="greklow" key="numbers">Griekse kleinletter-syfers</type>
			<type type="gujr" key="numbers">Gudjarati syfers</type>
			<type type="guru" key="numbers">Gurmukhi-syfers</type>
			<type type="hanidec" key="numbers">Chinese desimale syfers</type>
			<type type="hans" key="numbers">Vereenvoudigde Chinese syfers</type>
			<type type="hansfin" key="numbers">Vereenvoudigde Chinese finansiële syfers</type>
			<type type="hant" key="numbers">Tradisionele Chinese syfers</type>
			<type type="hantfin" key="numbers">Tradisionele Chinese finansiële syfers</type>
			<type type="hebr" key="numbers">Hebreeuse syfers</type>
			<type type="hebrew" key="calendar">Hebreeuse kalender</type>
			<type type="identical" key="colStrength">Sorteer almal</type>
			<type type="indian" key="calendar">Indiese nasionale kalender</type>
			<type type="islamic" key="calendar">Islamitiese kalender</type>
			<type type="islamic-civil" key="calendar">Islamitiese siviele kalender</type>
			<type type="japanese" key="calendar">Japannese kalender</type>
			<type type="jpan" key="numbers">Japannese syfers</type>
			<type type="jpanfin" key="numbers">Japannese finansiële syfers</type>
			<type type="khmr" key="numbers">Khmer-syfers</type>
			<type type="knda" key="numbers">Kannada-syfers</type>
			<type type="laoo" key="numbers">Lao-syfers</type>
			<type type="latn" key="numbers">Westerse syfers</type>
			<type type="lower" key="colCaseFirst">Sorteer kleinletters veerste</type>
			<type type="mlym" key="numbers">Malabaarse syfers</type>
			<type type="mong" key="numbers">Mongoliese syfers</type>
			<type type="mymr" key="numbers">Mianmar-syfers</type>
			<type type="native" key="numbers">Inheemse syfers</type>
			<type type="no" key="colBackwards">Sorteer aksente gewoonweg</type>
			<type type="no" key="colCaseFirst">Sorteer gewone letterorde</type>
			<type type="no" key="colCaseLevel">Sorteer nie kassensitief nie</type>
			<type type="no" key="colHiraganaQuaternary">Sorteer Kana afsonderlik</type>
			<type type="no" key="colNormalization">Sorteer sonder normalisering</type>
			<type type="no" key="colNumeric">Sorteer syfers individueel</type>
			<type type="non-ignorable" key="colAlternate">Sorteer simbole</type>
			<type type="orya" key="numbers">Oriya-syfers</type>
			<type type="persian" key="calendar">Persiese kalender</type>
			<type type="phonebook" key="collation">Foonboek-sorteervolgorde</type>
			<type type="phonetic" key="collation">Fonetiese sorteerorde</type>
			<type type="pinyin" key="collation">Pinyin-sorteervolgorde</type>
			<type type="primary" key="colStrength">Sorteer slegs basisletters</type>
			<type type="quaternary" key="colStrength">Sorteer aksente/kas/breedte/Kana</type>
			<type type="reformed" key="collation">Gereformeerde sorteervolgorde</type>
			<type type="roc" key="calendar">Minguo-kalender</type>
			<type type="roman" key="numbers">Romeinse syfers</type>
			<type type="romanlow" key="numbers">Romeinse kleinletter-syfers</type>
			<type type="search" key="collation">Algemenedoel-soektog</type>
			<type type="searchjl" key="collation">Soek volgens Hangul-beginkonsonant</type>
			<type type="secondary" key="colStrength">Sorteer aksente</type>
			<type type="shifted" key="colAlternate">Sorteer ignoreersimbole</type>
			<type type="standard" key="collation">Standaard sorteervolgorde</type>
			<type type="stroke" key="collation">Slag-sorteervolgorde</type>
			<type type="taml" key="numbers">Tradisionele Tamil-syfers</type>
			<type type="tamldec" key="numbers">Tamil-syfers</type>
			<type type="telu" key="numbers">Telugu-syfers</type>
			<type type="tertiary" key="colStrength">Sorteer aksente/kas/breedte</type>
			<type type="thai" key="numbers">Thaise syfers</type>
			<type type="tibt" key="numbers">Tibettaanse syfers</type>
			<type type="traditional" key="collation">Tradisionele sorteervolgorde</type>
			<type type="traditional" key="numbers">Tradisionele syfers</type>
			<type type="unihan" key="collation">Radikale-slag-sorteervolgorde</type>
			<type type="upper" key="colCaseFirst">Sorteer hoofletters eerste</type>
			<type type="vaii" key="numbers">Vai-syfers</type>
			<type type="yes" key="colBackwards">Sorteer aksente omgekeerd</type>
			<type type="yes" key="colCaseLevel">Sorteer kassensitief</type>
			<type type="yes" key="colHiraganaQuaternary">Sorteer Kana anders</type>
			<type type="yes" key="colNormalization">Sorteer Unicode genormaliseer</type>
			<type type="yes" key="colNumeric">Sorteer syfers numeries</type>
		</types>
		<transformNames>
			<transformName type="BGN">BGN</transformName>
			<transformName type="Numeric">Numeries</transformName>
			<transformName type="Tone">Toon</transformName>
			<transformName type="UNGEGN">UNGEGN</transformName>
			<transformName type="x-Accents">Aksente</transformName>
			<transformName type="x-Fullwidth">Vollewydte</transformName>
			<transformName type="x-Halfwidth">halfwydte</transformName>
			<transformName type="x-Jamo">Jamo</transformName>
			<transformName type="x-Pinyin">Pinyin</transformName>
			<transformName type="x-Publishing">Publisering</transformName>
		</transformNames>
		<measurementSystemNames>
			<measurementSystemName type="metric">Metriek</measurementSystemName>
			<measurementSystemName type="UK">VK</measurementSystemName>
			<measurementSystemName type="US">VSA</measurementSystemName>
		</measurementSystemNames>
		<codePatterns>
			<codePattern type="language">Taal: {0}</codePattern>
			<codePattern type="script">Skrif: {0}</codePattern>
			<codePattern type="territory">Streek: {0}</codePattern>
		</codePatterns>
	</localeDisplayNames>
	<characters>
		<exemplarCharacters>[a á â b c d e é è ê ë f g h i î ï j k l m n o ô ö p q r s t u û v w x y z]</exemplarCharacters>
		<exemplarCharacters type="auxiliary">[á à â ä ã æ ç é è ê ë í ì î ï ó ò ô ö ú ù û ü ý]</exemplarCharacters>
		<exemplarCharacters type="index" draft="contributed">[A B C D E F G H I J K L M N O P Q R S T U V W X Y Z]</exemplarCharacters>
		<exemplarCharacters type="punctuation">[\- ‐ – — , ; \: ! ? . … ' ‘ ’ &quot; “ ” ( ) \[ \] § @ * / \&amp; # † ‡ ′ ″]</exemplarCharacters>
		<ellipsis type="final">{0}…</ellipsis>
		<ellipsis type="initial">…{0}</ellipsis>
		<ellipsis type="medial">{0}…{1}</ellipsis>
		<ellipsis type="word-final">{0} …</ellipsis>
		<ellipsis type="word-initial">… {0}</ellipsis>
		<ellipsis type="word-medial">{0} … {1}</ellipsis>
		<moreInformation>?</moreInformation>
	</characters>
	<delimiters>
		<quotationStart>“</quotationStart>
		<quotationEnd>”</quotationEnd>
		<alternateQuotationStart>‘</alternateQuotationStart>
		<alternateQuotationEnd>’</alternateQuotationEnd>
	</delimiters>
	<dates>
		<calendars>
			<calendar type="generic">
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>EEEE dd MMMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>dd MMMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>dd MMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>GGGGG y-MM-dd</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<dateTimeFormats>
					<dateTimeFormatLength type="full">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="long">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="medium">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="short">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<availableFormats>
						<dateFormatItem id="d">d</dateFormatItem>
						<dateFormatItem id="Ed">E d</dateFormatItem>
						<dateFormatItem id="Gy">y G</dateFormatItem>
						<dateFormatItem id="GyMMM">MMM y G</dateFormatItem>
						<dateFormatItem id="GyMMMd">d MMM y G</dateFormatItem>
						<dateFormatItem id="GyMMMEd">E, d MMM y G</dateFormatItem>
						<dateFormatItem id="h">h a</dateFormatItem>
						<dateFormatItem id="H">HH</dateFormatItem>
						<dateFormatItem id="hm">h:mm a</dateFormatItem>
						<dateFormatItem id="Hm">HH:mm</dateFormatItem>
						<dateFormatItem id="hms">h:mm:ss a</dateFormatItem>
						<dateFormatItem id="Hms">HH:mm:ss</dateFormatItem>
						<dateFormatItem id="M">L</dateFormatItem>
						<dateFormatItem id="Md">M/d</dateFormatItem>
						<dateFormatItem id="MEd">E, M/d</dateFormatItem>
						<dateFormatItem id="MMM">LLL</dateFormatItem>
						<dateFormatItem id="MMMd">MMM d</dateFormatItem>
						<dateFormatItem id="MMMEd">E, MMM d</dateFormatItem>
						<dateFormatItem id="MMMMd">MMMM d</dateFormatItem>
						<dateFormatItem id="MMMMdd">dd MMMM</dateFormatItem>
						<dateFormatItem id="MMMMEd">E, MMMM d</dateFormatItem>
						<dateFormatItem id="ms">mm:ss</dateFormatItem>
						<dateFormatItem id="y">y G</dateFormatItem>
						<dateFormatItem id="yyyy">y G</dateFormatItem>
						<dateFormatItem id="yyyyM">M/y G</dateFormatItem>
						<dateFormatItem id="yyyyMd">M/d/y G</dateFormatItem>
						<dateFormatItem id="yyyyMEd">E, d/M/y G</dateFormatItem>
						<dateFormatItem id="yyyyMMM">MMM y G</dateFormatItem>
						<dateFormatItem id="yyyyMMMd">d MMM y G</dateFormatItem>
						<dateFormatItem id="yyyyMMMEd">E, d MMM y G</dateFormatItem>
						<dateFormatItem id="yyyyMMMM">MMMM y G</dateFormatItem>
						<dateFormatItem id="yyyyQQQ">QQQ y G</dateFormatItem>
						<dateFormatItem id="yyyyQQQQ">QQQQ y G</dateFormatItem>
					</availableFormats>
					<intervalFormats>
						<intervalFormatFallback>{0} – {1}</intervalFormatFallback>
						<intervalFormatItem id="d">
							<greatestDifference id="d">d–d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="h">
							<greatestDifference id="a">h a – h a</greatestDifference>
							<greatestDifference id="h">h–h a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="H">
							<greatestDifference id="H">HH–HH</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hm">
							<greatestDifference id="a">h:mm a – h:mm a</greatestDifference>
							<greatestDifference id="h">h:mm–h:mm a</greatestDifference>
							<greatestDifference id="m">h:mm–h:mm a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hm">
							<greatestDifference id="H">HH:mm–HH:mm</greatestDifference>
							<greatestDifference id="m">HH:mm–HH:mm</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hmv">
							<greatestDifference id="a">h:mm a – h:mm a v</greatestDifference>
							<greatestDifference id="h">h:mm–h:mm a v</greatestDifference>
							<greatestDifference id="m">h:mm–h:mm a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hmv">
							<greatestDifference id="H">HH:mm–HH:mm v</greatestDifference>
							<greatestDifference id="m">HH:mm–HH:mm v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hv">
							<greatestDifference id="a">h a – h a v</greatestDifference>
							<greatestDifference id="h">h–h a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hv">
							<greatestDifference id="H">HH–HH v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="M">
							<greatestDifference id="M">M–M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Md">
							<greatestDifference id="d">M/d – M/d</greatestDifference>
							<greatestDifference id="M">M/d – M/d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MEd">
							<greatestDifference id="d">E, M/d – E, M/d</greatestDifference>
							<greatestDifference id="M">E, M/d – E, M/d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMM">
							<greatestDifference id="M">MMM–MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMd">
							<greatestDifference id="d">MMM d–d</greatestDifference>
							<greatestDifference id="M">MMM d – MMM d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMEd">
							<greatestDifference id="d">E, MMM d – E, MMM d</greatestDifference>
							<greatestDifference id="M">E, MMM d – E, MMM d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="y">
							<greatestDifference id="y">y–y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yM">
							<greatestDifference id="M">M/y – M/y G</greatestDifference>
							<greatestDifference id="y">M/y – M/y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMd">
							<greatestDifference id="d">M/d/y – M/d/y G</greatestDifference>
							<greatestDifference id="M">M/d/y – M/d/y G</greatestDifference>
							<greatestDifference id="y">M/d/y – M/d/y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMEd">
							<greatestDifference id="d">E, M/d/y – E, M/d/y G</greatestDifference>
							<greatestDifference id="M">E, M/d/y – E, M/d/y G</greatestDifference>
							<greatestDifference id="y">E, M/d/y – E, M/d/y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMM">
							<greatestDifference id="M">MMM–MMM y G</greatestDifference>
							<greatestDifference id="y">MMM y – MMM y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMd">
							<greatestDifference id="d">d–d MMM, y G</greatestDifference>
							<greatestDifference id="M">MMM d – MMM d, y G</greatestDifference>
							<greatestDifference id="y">d MMM, y – d MMM, y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMEd">
							<greatestDifference id="d">E, d MMM – E, d MMM, y G</greatestDifference>
							<greatestDifference id="M">E, d MMM – E, d MMM, y G</greatestDifference>
							<greatestDifference id="y">E, MMM d, y – E, MMM d, y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMM">
							<greatestDifference id="M">MMMM–MMMM y G</greatestDifference>
							<greatestDifference id="y">MMMM y – MMMM y G</greatestDifference>
						</intervalFormatItem>
					</intervalFormats>
				</dateTimeFormats>
			</calendar>
			<calendar type="gregorian">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<month type="1">Jan</month>
							<month type="2">Feb</month>
							<month type="3">Mar</month>
							<month type="4">Apr</month>
							<month type="5">Mei</month>
							<month type="6">Jun</month>
							<month type="7">Jul</month>
							<month type="8">Aug</month>
							<month type="9">Sep</month>
							<month type="10">Okt</month>
							<month type="11">Nov</month>
							<month type="12">Des</month>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">J</month>
							<month type="2">F</month>
							<month type="3">M</month>
							<month type="4">A</month>
							<month type="5">M</month>
							<month type="6">J</month>
							<month type="7">J</month>
							<month type="8">A</month>
							<month type="9">S</month>
							<month type="10">O</month>
							<month type="11">N</month>
							<month type="12">D</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Januarie</month>
							<month type="2">Februarie</month>
							<month type="3">Maart</month>
							<month type="4">April</month>
							<month type="5">Mei</month>
							<month type="6">Junie</month>
							<month type="7">Julie</month>
							<month type="8">Augustus</month>
							<month type="9">September</month>
							<month type="10">Oktober</month>
							<month type="11">November</month>
							<month type="12">Desember</month>
						</monthWidth>
					</monthContext>
					<monthContext type="stand-alone">
						<monthWidth type="abbreviated">
							<month type="1">Jan</month>
							<month type="2">Feb</month>
							<month type="3">Mar</month>
							<month type="4">Apr</month>
							<month type="5">Mei</month>
							<month type="6">Jun</month>
							<month type="7">Jul</month>
							<month type="8">Aug</month>
							<month type="9">Sep</month>
							<month type="10">Okt</month>
							<month type="11">Nov</month>
							<month type="12">Des</month>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">J</month>
							<month type="2">F</month>
							<month type="3">M</month>
							<month type="4">A</month>
							<month type="5">M</month>
							<month type="6">J</month>
							<month type="7">J</month>
							<month type="8">A</month>
							<month type="9">S</month>
							<month type="10">O</month>
							<month type="11">N</month>
							<month type="12">D</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Januarie</month>
							<month type="2">Februarie</month>
							<month type="3">Maart</month>
							<month type="4">April</month>
							<month type="5">Mei</month>
							<month type="6">Junie</month>
							<month type="7">Julie</month>
							<month type="8">Augustus</month>
							<month type="9">September</month>
							<month type="10">Oktober</month>
							<month type="11">November</month>
							<month type="12">Desember</month>
						</monthWidth>
					</monthContext>
				</months>
				<days>
					<dayContext type="format">
						<dayWidth type="abbreviated">
							<day type="sun">So</day>
							<day type="mon">Ma</day>
							<day type="tue">Di</day>
							<day type="wed">Wo</day>
							<day type="thu">Do</day>
							<day type="fri">Vr</day>
							<day type="sat">Sa</day>
						</dayWidth>
						<dayWidth type="narrow">
							<day type="sun">S</day>
							<day type="mon">M</day>
							<day type="tue">D</day>
							<day type="wed">W</day>
							<day type="thu">D</day>
							<day type="fri">V</day>
							<day type="sat">S</day>
						</dayWidth>
						<dayWidth type="short">
							<day type="sun">So.</day>
							<day type="mon">Ma.</day>
							<day type="tue">Di.</day>
							<day type="wed">Wo.</day>
							<day type="thu">Do.</day>
							<day type="fri">Vr.</day>
							<day type="sat">Sa.</day>
						</dayWidth>
						<dayWidth type="wide">
							<day type="sun">Sondag</day>
							<day type="mon">Maandag</day>
							<day type="tue">Dinsdag</day>
							<day type="wed">Woensdag</day>
							<day type="thu">Donderdag</day>
							<day type="fri">Vrydag</day>
							<day type="sat">Saterdag</day>
						</dayWidth>
					</dayContext>
					<dayContext type="stand-alone">
						<dayWidth type="abbreviated">
							<day type="sun">So</day>
							<day type="mon">Ma</day>
							<day type="tue">Di</day>
							<day type="wed">Wo</day>
							<day type="thu">Do</day>
							<day type="fri">Vr</day>
							<day type="sat">Sa</day>
						</dayWidth>
						<dayWidth type="narrow">
							<day type="sun">S</day>
							<day type="mon">M</day>
							<day type="tue">D</day>
							<day type="wed">W</day>
							<day type="thu">D</day>
							<day type="fri">V</day>
							<day type="sat">S</day>
						</dayWidth>
						<dayWidth type="short">
							<day type="sun">So.</day>
							<day type="mon">Ma.</day>
							<day type="tue">Di.</day>
							<day type="wed">Wo.</day>
							<day type="thu">Do.</day>
							<day type="fri">Vr.</day>
							<day type="sat">Sa.</day>
						</dayWidth>
						<dayWidth type="wide">
							<day type="sun">Sondag</day>
							<day type="mon">Maandag</day>
							<day type="tue">Dinsdag</day>
							<day type="wed">Woensdag</day>
							<day type="thu">Donderdag</day>
							<day type="fri">Vrydag</day>
							<day type="sat">Saterdag</day>
						</dayWidth>
					</dayContext>
				</days>
				<quarters>
					<quarterContext type="format">
						<quarterWidth type="abbreviated">
							<quarter type="1">K1</quarter>
							<quarter type="2">K2</quarter>
							<quarter type="3">K3</quarter>
							<quarter type="4">K4</quarter>
						</quarterWidth>
						<quarterWidth type="narrow">
							<quarter type="1">1</quarter>
							<quarter type="2">2</quarter>
							<quarter type="3">3</quarter>
							<quarter type="4">4</quarter>
						</quarterWidth>
						<quarterWidth type="wide">
							<quarter type="1">1ste kwartaal</quarter>
							<quarter type="2">2de kwartaal</quarter>
							<quarter type="3">3de kwartaal</quarter>
							<quarter type="4">4de kwartaal</quarter>
						</quarterWidth>
					</quarterContext>
					<quarterContext type="stand-alone">
						<quarterWidth type="abbreviated">
							<quarter type="1">K1</quarter>
							<quarter type="2">K2</quarter>
							<quarter type="3">K3</quarter>
							<quarter type="4">K4</quarter>
						</quarterWidth>
						<quarterWidth type="narrow">
							<quarter type="1">1</quarter>
							<quarter type="2">2</quarter>
							<quarter type="3">3</quarter>
							<quarter type="4">4</quarter>
						</quarterWidth>
						<quarterWidth type="wide">
							<quarter type="1">1ste kwartaal</quarter>
							<quarter type="2">2de kwartaal</quarter>
							<quarter type="3">3de kwartaal</quarter>
							<quarter type="4">4de kwartaal</quarter>
						</quarterWidth>
					</quarterContext>
				</quarters>
				<dayPeriods>
					<dayPeriodContext type="format">
						<dayPeriodWidth type="wide">
							<dayPeriod type="am">vm.</dayPeriod>
							<dayPeriod type="pm">nm.</dayPeriod>
						</dayPeriodWidth>
					</dayPeriodContext>
				</dayPeriods>
				<eras>
					<eraNames>
						<era type="0">voor Christus</era>
						<era type="1">na Christus</era>
					</eraNames>
					<eraAbbr>
						<era type="0">v.C.</era>
						<era type="0" alt="variant">v.g.j.</era>
						<era type="1">n.C.</era>
						<era type="1" alt="variant">g.j.</era>
					</eraAbbr>
					<eraNarrow>
						<era type="0">v.C.</era>
						<era type="0" alt="variant" draft="unconfirmed">vgj</era>
						<era type="1">n.C.</era>
						<era type="1" alt="variant" draft="unconfirmed">vg</era>
					</eraNarrow>
				</eras>
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>EEEE dd MMMM y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>dd MMMM y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>dd MMM y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>y-MM-dd</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<timeFormats>
					<timeFormatLength type="full">
						<timeFormat>
							<pattern>h:mm:ss a zzzz</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="long">
						<timeFormat>
							<pattern>h:mm:ss a z</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="medium">
						<timeFormat>
							<pattern>h:mm:ss a</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="short">
						<timeFormat>
							<pattern>h:mm a</pattern>
						</timeFormat>
					</timeFormatLength>
				</timeFormats>
				<dateTimeFormats>
					<dateTimeFormatLength type="full">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="long">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="medium">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="short">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<availableFormats>
						<dateFormatItem id="d">d</dateFormatItem>
						<dateFormatItem id="Ed">E d</dateFormatItem>
						<dateFormatItem id="Ehm">E h:mm a</dateFormatItem>
						<dateFormatItem id="EHm">E HH:mm</dateFormatItem>
						<dateFormatItem id="Ehms">E h:mm:ss a</dateFormatItem>
						<dateFormatItem id="EHms">E HH:mm:ss</dateFormatItem>
						<dateFormatItem id="Gy">y G</dateFormatItem>
						<dateFormatItem id="GyMMM">MMM y G</dateFormatItem>
						<dateFormatItem id="GyMMMd">d MMM y G</dateFormatItem>
						<dateFormatItem id="GyMMMEd">E, d MMM y G</dateFormatItem>
						<dateFormatItem id="h">h a</dateFormatItem>
						<dateFormatItem id="H">HH</dateFormatItem>
						<dateFormatItem id="hm">h:mm a</dateFormatItem>
						<dateFormatItem id="Hm">HH:mm</dateFormatItem>
						<dateFormatItem id="hms">h:mm:ss a</dateFormatItem>
						<dateFormatItem id="Hms">HH:mm:ss</dateFormatItem>
						<dateFormatItem id="M">L</dateFormatItem>
						<dateFormatItem id="Md">M/d</dateFormatItem>
						<dateFormatItem id="MEd">E, M/d</dateFormatItem>
						<dateFormatItem id="MMM">LLL</dateFormatItem>
						<dateFormatItem id="MMMd">MMM d</dateFormatItem>
						<dateFormatItem id="MMMEd">E, MMM d</dateFormatItem>
						<dateFormatItem id="MMMMd">MMMM d</dateFormatItem>
						<dateFormatItem id="MMMMdd">dd MMMM</dateFormatItem>
						<dateFormatItem id="MMMMEd">E, MMMM d</dateFormatItem>
						<dateFormatItem id="ms">mm:ss</dateFormatItem>
						<dateFormatItem id="y">y</dateFormatItem>
						<dateFormatItem id="yM">M/y</dateFormatItem>
						<dateFormatItem id="yMd">M/d/y</dateFormatItem>
						<dateFormatItem id="yMEd">E, d/M/y</dateFormatItem>
						<dateFormatItem id="yMMM">MMM y</dateFormatItem>
						<dateFormatItem id="yMMMd">d MMM y</dateFormatItem>
						<dateFormatItem id="yMMMEd">E, d MMM y</dateFormatItem>
						<dateFormatItem id="yMMMM">MMMM y</dateFormatItem>
						<dateFormatItem id="yQQQ">QQQ y</dateFormatItem>
						<dateFormatItem id="yQQQQ">QQQQ y</dateFormatItem>
					</availableFormats>
					<appendItems>
						<appendItem request="Timezone">{0} {1}</appendItem>
					</appendItems>
					<intervalFormats>
						<intervalFormatFallback>{0} – {1}</intervalFormatFallback>
						<intervalFormatItem id="d">
							<greatestDifference id="d">d–d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="h">
							<greatestDifference id="a">h a – h a</greatestDifference>
							<greatestDifference id="h">h–h a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="H">
							<greatestDifference id="H">HH–HH</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hm">
							<greatestDifference id="a">h:mm a – h:mm a</greatestDifference>
							<greatestDifference id="h">h:mm–h:mm a</greatestDifference>
							<greatestDifference id="m">h:mm–h:mm a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hm">
							<greatestDifference id="H">HH:mm–HH:mm</greatestDifference>
							<greatestDifference id="m">HH:mm–HH:mm</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hmv">
							<greatestDifference id="a">h:mm a – h:mm a v</greatestDifference>
							<greatestDifference id="h">h:mm–h:mm a v</greatestDifference>
							<greatestDifference id="m">h:mm–h:mm a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hmv">
							<greatestDifference id="H">HH:mm–HH:mm v</greatestDifference>
							<greatestDifference id="m">HH:mm–HH:mm v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hv">
							<greatestDifference id="a">h a – h a v</greatestDifference>
							<greatestDifference id="h">h–h a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hv">
							<greatestDifference id="H">HH–HH v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="M">
							<greatestDifference id="M">M–M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Md">
							<greatestDifference id="d">M/d – M/d</greatestDifference>
							<greatestDifference id="M">M/d – M/d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MEd">
							<greatestDifference id="d">E, M/d – E, M/d</greatestDifference>
							<greatestDifference id="M">E, M/d – E, M/d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMM">
							<greatestDifference id="M">MMM–MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMd">
							<greatestDifference id="d">MMM d–d</greatestDifference>
							<greatestDifference id="M">MMM d – MMM d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMEd">
							<greatestDifference id="d">E, MMM d – E, MMM d</greatestDifference>
							<greatestDifference id="M">E, MMM d – E, MMM d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="y">
							<greatestDifference id="y">y–y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yM">
							<greatestDifference id="M">M/y – M/y</greatestDifference>
							<greatestDifference id="y">M/y – M/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMd">
							<greatestDifference id="d">M/d/y – M/d/y</greatestDifference>
							<greatestDifference id="M">M/d/y – M/d/y</greatestDifference>
							<greatestDifference id="y">M/d/y – M/d/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMEd">
							<greatestDifference id="d">E, M/d/y – E, M/d/y</greatestDifference>
							<greatestDifference id="M">E, M/d/y – E, M/d/y</greatestDifference>
							<greatestDifference id="y">E, M/d/y – E, M/d/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMM">
							<greatestDifference id="M">MMM–MMM y</greatestDifference>
							<greatestDifference id="y">MMM y – MMM y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMd">
							<greatestDifference id="d">d–d MMM, y</greatestDifference>
							<greatestDifference id="M">MMM d – MMM d, y</greatestDifference>
							<greatestDifference id="y">d MMM, y – d MMM, y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMEd">
							<greatestDifference id="d">E, d MMM – E, d MMM, y</greatestDifference>
							<greatestDifference id="M">E, d MMM – E, d MMM, y</greatestDifference>
							<greatestDifference id="y">E, MMM d, y – E, MMM d, y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMM">
							<greatestDifference id="M">MMMM–MMMM y</greatestDifference>
							<greatestDifference id="y">MMMM y – MMMM y</greatestDifference>
						</intervalFormatItem>
					</intervalFormats>
				</dateTimeFormats>
			</calendar>
		</calendars>
		<fields>
			<field type="era">
				<displayName>Era</displayName>
			</field>
			<field type="year">
				<displayName>Jaar</displayName>
				<relative type="-1">verlede jaar</relative>
				<relative type="0">hierdie jaar</relative>
				<relative type="1">volgende jaar</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">Oor {0} jaar</relativeTimePattern>
					<relativeTimePattern count="other">Oor {0} jaar</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">{0} jaar gelede</relativeTimePattern>
					<relativeTimePattern count="other">{0} jaar gelede</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="month">
				<displayName>Maand</displayName>
				<relative type="-1">verlede maand</relative>
				<relative type="0">vandeesmaand</relative>
				<relative type="1">volgende maand</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">Oor {0} maand</relativeTimePattern>
					<relativeTimePattern count="other">Oor {0} maande</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">{0} maand gelede</relativeTimePattern>
					<relativeTimePattern count="other">{0} maande gelede</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="week">
				<displayName>Week</displayName>
				<relative type="-1">verlede week</relative>
				<relative type="0">vandeesweek</relative>
				<relative type="1">volgende week</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">Oor {0} week</relativeTimePattern>
					<relativeTimePattern count="other">Oor {0} weke</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">{0} week gelede</relativeTimePattern>
					<relativeTimePattern count="other">{0} weke gelede</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="day">
				<displayName>Dag</displayName>
				<relative type="-2">Die dag voor gister</relative>
				<relative type="-1">gister</relative>
				<relative type="0">vandag</relative>
				<relative type="1">môre</relative>
				<relative type="2">Die dag na môre</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">Oor {0} dag</relativeTimePattern>
					<relativeTimePattern count="other">Oor {0} dae</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">{0} dag gelede</relativeTimePattern>
					<relativeTimePattern count="other">{0} dae gelede</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="weekday">
				<displayName>Dag van die Week</displayName>
			</field>
			<field type="sun">
				<relative type="-1">verlede Sondag</relative>
				<relative type="0">hierdie Sondag</relative>
				<relative type="1">volgende Sondag</relative>
			</field>
			<field type="mon">
				<relative type="-1">verlede Maandag</relative>
				<relative type="0">hierdie Maandag</relative>
				<relative type="1">volgende Maandag</relative>
			</field>
			<field type="tue">
				<relative type="-1">verlede Dinsdag</relative>
				<relative type="0">hierdie Dinsdag</relative>
				<relative type="1">volgende Dinsdag</relative>
			</field>
			<field type="wed">
				<relative type="-1">verlede Woensdag</relative>
				<relative type="0">hierdie Woensdag</relative>
				<relative type="1">volgende Woensdag</relative>
			</field>
			<field type="thu">
				<relative type="-1">verlede Donderdag</relative>
				<relative type="0">hierdie Donderdag</relative>
				<relative type="1">volgende Donderdag</relative>
			</field>
			<field type="fri">
				<relative type="-1">verlede Vrydag</relative>
				<relative type="0">hierdie Vrydag</relative>
				<relative type="1">volgende Vrydag</relative>
			</field>
			<field type="sat">
				<relative type="-1">verlede Saterdag</relative>
				<relative type="0">hierdie Saterdag</relative>
				<relative type="1">volgende Saterdag</relative>
			</field>
			<field type="dayperiod">
				<displayName>VM/NM</displayName>
			</field>
			<field type="hour">
				<displayName>Uur</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">Oor {0} uur</relativeTimePattern>
					<relativeTimePattern count="other">Oor {0} uur</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">{0} uur gelede</relativeTimePattern>
					<relativeTimePattern count="other">{0} uur gelede</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="minute">
				<displayName>Minuut</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">Oor {0} minuut</relativeTimePattern>
					<relativeTimePattern count="other">Oor {0} minute</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">{0} minuut gelede</relativeTimePattern>
					<relativeTimePattern count="other">{0} minute gelede</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="second">
				<displayName>Sekonde</displayName>
				<relative type="0">nou</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">Oor {0} sekonde</relativeTimePattern>
					<relativeTimePattern count="other">Oor {0} sekondes</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">{0} sekonde gelede</relativeTimePattern>
					<relativeTimePattern count="other">{0} sekondes gelede</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="zone">
				<displayName>Tydsone</displayName>
			</field>
		</fields>
		<timeZoneNames>
			<hourFormat>+HH:mm;-HH:mm</hourFormat>
			<gmtFormat>MGT{0}</gmtFormat>
			<gmtZeroFormat>MGT</gmtZeroFormat>
			<regionFormat>{0}-tyd</regionFormat>
			<regionFormat type="daylight">{0}-dagligtyd</regionFormat>
			<regionFormat type="standard">{0}-standaardtyd</regionFormat>
			<fallbackFormat>{1} ({0})</fallbackFormat>
			<zone type="Etc/Unknown">
				<exemplarCity>Onbekende stad</exemplarCity>
			</zone>
			<zone type="Antarctica/DumontDUrville">
				<exemplarCity>Dumont d’Urville</exemplarCity>
			</zone>
			<zone type="Europe/Vienna">
				<exemplarCity>Wene</exemplarCity>
			</zone>
			<zone type="Asia/Baku">
				<exemplarCity>Bakoe</exemplarCity>
			</zone>
			<zone type="Europe/Brussels">
				<exemplarCity>Brussel</exemplarCity>
			</zone>
			<zone type="Asia/Bahrain">
				<exemplarCity>Bahrein</exemplarCity>
			</zone>
			<zone type="America/St_Barthelemy">
				<exemplarCity>Sint Barthélemy</exemplarCity>
			</zone>
			<zone type="Asia/Brunei">
				<exemplarCity>Broenei</exemplarCity>
			</zone>
			<zone type="America/Coral_Harbour">
				<exemplarCity>Atikokan</exemplarCity>
			</zone>
			<zone type="America/St_Johns">
				<exemplarCity>Sint John's</exemplarCity>
			</zone>
			<zone type="Europe/Zurich">
				<exemplarCity>Zürich</exemplarCity>
			</zone>
			<zone type="Pacific/Easter">
				<exemplarCity>Paas</exemplarCity>
			</zone>
			<zone type="Atlantic/Cape_Verde">
				<exemplarCity>Kaap Verde</exemplarCity>
			</zone>
			<zone type="America/Curacao">
				<exemplarCity>Curaçao</exemplarCity>
			</zone>
			<zone type="Indian/Christmas">
				<exemplarCity>Kers</exemplarCity>
			</zone>
			<zone type="Europe/Prague">
				<exemplarCity>Praag</exemplarCity>
			</zone>
			<zone type="Europe/Berlin">
				<exemplarCity>Berlyn</exemplarCity>
			</zone>
			<zone type="Africa/Djibouti">
				<exemplarCity>Djiboeti</exemplarCity>
			</zone>
			<zone type="Europe/Copenhagen">
				<exemplarCity>Kopenhagen</exemplarCity>
			</zone>
			<zone type="Africa/Cairo">
				<exemplarCity>Caro</exemplarCity>
			</zone>
			<zone type="Africa/Asmera">
				<exemplarCity>Asmara</exemplarCity>
			</zone>
			<zone type="Atlantic/Canary">
				<exemplarCity>Kanarie</exemplarCity>
			</zone>
			<zone type="Africa/Addis_Ababa">
				<exemplarCity>Addis Abeba</exemplarCity>
			</zone>
			<zone type="Pacific/Truk">
				<exemplarCity>Chuuk</exemplarCity>
			</zone>
			<zone type="Pacific/Ponape">
				<exemplarCity>Pohnpei</exemplarCity>
			</zone>
			<zone type="Atlantic/Faeroe">
				<exemplarCity>Faroe</exemplarCity>
			</zone>
			<zone type="Europe/Paris">
				<exemplarCity>Parys</exemplarCity>
			</zone>
			<zone type="Europe/London">
				<long>
					<daylight>Britse somertyd</daylight>
				</long>
				<exemplarCity>Londen</exemplarCity>
			</zone>
			<zone type="America/Godthab">
				<exemplarCity>Nuuk</exemplarCity>
			</zone>
			<zone type="America/Scoresbysund">
				<exemplarCity>Ittoqqortoormiit</exemplarCity>
			</zone>
			<zone type="Europe/Athens">
				<exemplarCity>Athene</exemplarCity>
			</zone>
			<zone type="Atlantic/South_Georgia">
				<exemplarCity>Suid-Georgië</exemplarCity>
			</zone>
			<zone type="Asia/Hong_Kong">
				<exemplarCity>Hongkong</exemplarCity>
			</zone>
			<zone type="Europe/Budapest">
				<exemplarCity>Boedapest</exemplarCity>
			</zone>
			<zone type="Europe/Dublin">
				<long>
					<daylight>Ierse somertyd</daylight>
				</long>
			</zone>
			<zone type="Asia/Calcutta">
				<exemplarCity>Kolkata</exemplarCity>
			</zone>
			<zone type="Asia/Baghdad">
				<exemplarCity>Bagdad</exemplarCity>
			</zone>
			<zone type="Asia/Bishkek">
				<exemplarCity>Bisjkek</exemplarCity>
			</zone>
			<zone type="America/St_Kitts">
				<exemplarCity>St. Kitts</exemplarCity>
			</zone>
			<zone type="Asia/Seoul">
				<exemplarCity>Seoel</exemplarCity>
			</zone>
			<zone type="Asia/Kuwait">
				<exemplarCity>Koeweit</exemplarCity>
			</zone>
			<zone type="Asia/Oral">
				<exemplarCity>Oeral</exemplarCity>
			</zone>
			<zone type="Asia/Beirut">
				<exemplarCity>Beiroet</exemplarCity>
			</zone>
			<zone type="America/St_Lucia">
				<exemplarCity>St. Lucia</exemplarCity>
			</zone>
			<zone type="Europe/Luxembourg">
				<exemplarCity>Luxemburg</exemplarCity>
			</zone>
			<zone type="Asia/Rangoon">
				<exemplarCity>Rangoen</exemplarCity>
			</zone>
			<zone type="Asia/Ulaanbaatar">
				<exemplarCity>Ulaanbatar</exemplarCity>
			</zone>
			<zone type="Indian/Maldives">
				<exemplarCity>Maledive</exemplarCity>
			</zone>
			<zone type="America/Mexico_City">
				<exemplarCity>Meksikostad</exemplarCity>
			</zone>
			<zone type="Pacific/Noumea">
				<exemplarCity>Nouméa</exemplarCity>
			</zone>
			<zone type="Asia/Katmandu">
				<exemplarCity>Katmandoe</exemplarCity>
			</zone>
			<zone type="Asia/Muscat">
				<exemplarCity>Muskat</exemplarCity>
			</zone>
			<zone type="Asia/Karachi">
				<exemplarCity>Karatsji</exemplarCity>
			</zone>
			<zone type="Europe/Warsaw">
				<exemplarCity>Warskou</exemplarCity>
			</zone>
			<zone type="Atlantic/Azores">
				<exemplarCity>Asore</exemplarCity>
			</zone>
			<zone type="Europe/Lisbon">
				<exemplarCity>Lissabon</exemplarCity>
			</zone>
			<zone type="America/Asuncion">
				<exemplarCity>Asunción</exemplarCity>
			</zone>
			<zone type="Asia/Qatar">
				<exemplarCity>Katar</exemplarCity>
			</zone>
			<zone type="Indian/Reunion">
				<exemplarCity>Réunion</exemplarCity>
			</zone>
			<zone type="Europe/Bucharest">
				<exemplarCity>Boekarest</exemplarCity>
			</zone>
			<zone type="Europe/Moscow">
				<exemplarCity>Moskou</exemplarCity>
			</zone>
			<zone type="Asia/Yekaterinburg">
				<exemplarCity>Jekaterinburg</exemplarCity>
			</zone>
			<zone type="Asia/Yakutsk">
				<exemplarCity>Jakoetsk</exemplarCity>
			</zone>
			<zone type="Asia/Riyadh">
				<exemplarCity>Riaad</exemplarCity>
			</zone>
			<zone type="Africa/Khartoum">
				<exemplarCity>Kartoem</exemplarCity>
			</zone>
			<zone type="Asia/Singapore">
				<exemplarCity>Singapoer</exemplarCity>
			</zone>
			<zone type="Atlantic/St_Helena">
				<exemplarCity>St. Helena</exemplarCity>
			</zone>
			<zone type="Africa/Sao_Tome">
				<exemplarCity>São Tomé</exemplarCity>
			</zone>
			<zone type="America/Lower_Princes">
				<exemplarCity>Lower Prince's Quarter</exemplarCity>
			</zone>
			<zone type="Asia/Damascus">
				<exemplarCity>Damaskus</exemplarCity>
			</zone>
			<zone type="Asia/Ashgabat">
				<exemplarCity>Asjchabad</exemplarCity>
			</zone>
			<zone type="Europe/Kiev">
				<exemplarCity>Kiëf</exemplarCity>
			</zone>
			<zone type="America/North_Dakota/Beulah">
				<exemplarCity>Beulah, North Dakota</exemplarCity>
			</zone>
			<zone type="America/North_Dakota/New_Salem">
				<exemplarCity>New Salem, North Dakota</exemplarCity>
			</zone>
			<zone type="America/North_Dakota/Center">
				<exemplarCity>Center, North Dakota</exemplarCity>
			</zone>
			<zone type="America/Indiana/Vincennes">
				<exemplarCity>Vincennes, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Petersburg">
				<exemplarCity>Petersburg, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Tell_City">
				<exemplarCity>Tell City, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Knox">
				<exemplarCity>Knox, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Winamac">
				<exemplarCity>Winamac, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Marengo">
				<exemplarCity>Marengo, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Vevay">
				<exemplarCity>Vevay, Indiana</exemplarCity>
			</zone>
			<zone type="America/Kentucky/Monticello">
				<exemplarCity>Monticello, Kentucky</exemplarCity>
			</zone>
			<zone type="Europe/Vatican">
				<exemplarCity>Vatikaanstad</exemplarCity>
			</zone>
			<zone type="America/St_Vincent">
				<exemplarCity>St. Vincent</exemplarCity>
			</zone>
			<zone type="America/St_Thomas">
				<exemplarCity>St. Thomas</exemplarCity>
			</zone>
			<zone type="Asia/Saigon">
				<exemplarCity>Ho Tsji Minhstad</exemplarCity>
			</zone>
			<metazone type="Afghanistan">
				<long>
					<standard>Afghanistan-tyd</standard>
				</long>
			</metazone>
			<metazone type="Africa_Central">
				<long>
					<standard>Sentraal-Afrika-tyd</standard>
				</long>
				<short>
					<standard>CAT</standard>
				</short>
			</metazone>
			<metazone type="Africa_Eastern">
				<long>
					<standard>Oos-Afrika-tyd</standard>
				</long>
				<short>
					<standard>EAT</standard>
				</short>
			</metazone>
			<metazone type="Africa_Southern">
				<long>
					<standard>Suid-Afrika-standaardtyd</standard>
				</long>
				<short>
					<standard>SAST</standard>
				</short>
			</metazone>
			<metazone type="Africa_Western">
				<long>
					<generic>Wes-Afrika-tyd</generic>
					<standard>Wes-Afrika-standaardtyd</standard>
					<daylight>Wes-Afrika-somertyd</daylight>
				</long>
				<short>
					<generic>WAT</generic>
					<standard>WAT</standard>
					<daylight>WAST</daylight>
				</short>
			</metazone>
			<metazone type="Alaska">
				<long>
					<generic>Alaska-tyd</generic>
					<standard>Alaska-standaardtyd</standard>
					<daylight>Alaska-dagligtyd</daylight>
				</long>
			</metazone>
			<metazone type="Amazon">
				<long>
					<generic>Amasone-tyd</generic>
					<standard>Amasone-standaardtyd</standard>
					<daylight>Amasone-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="America_Central">
				<long>
					<generic>Sentrale tyd</generic>
					<standard>Sentrale standaardtyd</standard>
					<daylight>Sentrale dagligtyd</daylight>
				</long>
			</metazone>
			<metazone type="America_Eastern">
				<long>
					<generic>Oostelike tyd</generic>
					<standard>Oostelike standaardtyd</standard>
					<daylight>Oostelike dagligtyd</daylight>
				</long>
			</metazone>
			<metazone type="America_Mountain">
				<long>
					<generic>Bergtyd</generic>
					<standard>Berg-standaardtyd</standard>
					<daylight>Berg-dagligtyd</daylight>
				</long>
			</metazone>
			<metazone type="America_Pacific">
				<long>
					<generic>Pasifiese tyd</generic>
					<standard>Pasifiese standaardtyd</standard>
					<daylight>Pasifiese dagligtyd</daylight>
				</long>
			</metazone>
			<metazone type="Anadyr">
				<long>
					<generic>Anadyr-tyd</generic>
					<standard>Anadyr-standaardtyd</standard>
					<daylight>Anadyr-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Arabian">
				<long>
					<generic>Arabiese tyd</generic>
					<standard>Arabiese standaardtyd</standard>
					<daylight>Arabiese dagligtyd</daylight>
				</long>
			</metazone>
			<metazone type="Argentina">
				<long>
					<generic>Argentinië-tyd</generic>
					<standard>Argentinië-standaardtyd</standard>
					<daylight>Argentinië-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Argentina_Western">
				<long>
					<generic>Argentinië Westelike tyd</generic>
					<standard>Argentinië Westelike standaardtyd</standard>
					<daylight>Argentinië Westelike somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Armenia">
				<long>
					<generic>Armenië-tyd</generic>
					<standard>Armenië-standaardtyd</standard>
					<daylight>Armenië-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Atlantic">
				<long>
					<generic>Atlantiese tyd</generic>
					<standard>Atlantiese standaardtyd</standard>
					<daylight>Atlantiese dagligtyd</daylight>
				</long>
			</metazone>
			<metazone type="Australia_Central">
				<long>
					<generic>Sentraal-Australië-tyd</generic>
					<standard>Australiese sentraal-standaardtyd</standard>
					<daylight>Australiese sentrale dagligtyd</daylight>
				</long>
			</metazone>
			<metazone type="Australia_CentralWestern">
				<long>
					<generic>Australiese sentraal-Westelike tyd</generic>
					<standard>Australiese sentraal-Westelike standaard-tyd</standard>
					<daylight>Australiese sentraal-Westelike dagligtyd</daylight>
				</long>
			</metazone>
			<metazone type="Australia_Eastern">
				<long>
					<generic>Oostelike Australiese tyd</generic>
					<standard>Australiese Oostelike standaardtyd</standard>
					<daylight>Australiese Oostelike dagligtyd</daylight>
				</long>
			</metazone>
			<metazone type="Australia_Western">
				<long>
					<generic>Westelike Australië-tyd</generic>
					<standard>Australiese Westelike standaardtyd</standard>
					<daylight>Australiese Westelike dagligtyd</daylight>
				</long>
			</metazone>
			<metazone type="Azerbaijan">
				<long>
					<generic>Aserbeidjan-tyd</generic>
					<standard>Aserbeidjan-standaardtyd</standard>
					<daylight>Aserbeidjan-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Azores">
				<long>
					<generic>Asore-tyd</generic>
					<standard>Asore-standaardtyd</standard>
					<daylight>Asore-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Bangladesh">
				<long>
					<generic>Bangladesj-tyd</generic>
					<standard>Bangladesj-standaardtyd</standard>
					<daylight>Bangladesj-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Bhutan">
				<long>
					<standard>Bhoetan-tyd</standard>
				</long>
			</metazone>
			<metazone type="Bolivia">
				<long>
					<standard>Bolivia-tyd</standard>
				</long>
			</metazone>
			<metazone type="Brasilia">
				<long>
					<generic>Brasilië-tyd</generic>
					<standard>Brasilië-standaardtyd</standard>
					<daylight>Brasilië somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Brunei">
				<long>
					<standard>Broenei Darussalam-tyd</standard>
				</long>
			</metazone>
			<metazone type="Cape_Verde">
				<long>
					<generic>Kaap Verde-tyd</generic>
					<standard>Kaap Verde-standaardtyd</standard>
					<daylight>Kaap Verde-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Chamorro">
				<long>
					<standard>Chamorro-standaardtyd</standard>
				</long>
			</metazone>
			<metazone type="Chatham">
				<long>
					<generic>Chatham-tyd</generic>
					<standard>Chatham-standaardtyd</standard>
					<daylight>Chatham-dagligtyd</daylight>
				</long>
			</metazone>
			<metazone type="Chile">
				<long>
					<generic>Chili-tyd</generic>
					<standard>Chili-standaardtyd</standard>
					<daylight>Chili-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="China">
				<long>
					<generic>China-tyd</generic>
					<standard>China-standaardtyd</standard>
					<daylight>China-dagligtyd</daylight>
				</long>
			</metazone>
			<metazone type="Choibalsan">
				<long>
					<generic>Choibalsan-tyd</generic>
					<standard>Choibalsan-standaardtyd</standard>
					<daylight>Choibalsan-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Christmas">
				<long>
					<standard>Kersfeeseiland-tyd</standard>
				</long>
			</metazone>
			<metazone type="Cocos">
				<long>
					<standard>Cocoseilande-tyd</standard>
				</long>
			</metazone>
			<metazone type="Colombia">
				<long>
					<generic>Colombië-tyd</generic>
					<standard>Colombië-standaardtyd</standard>
					<daylight>Colombië-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Cook">
				<long>
					<generic>Cookeilande-tyd</generic>
					<standard>Cookeilande-standaardtyd</standard>
					<daylight>Cookeilande-halfsomertyd</daylight>
				</long>
			</metazone>
			<metazone type="Cuba">
				<long>
					<generic>Kuba-tyd</generic>
					<standard>Kuba-standaardtyd</standard>
					<daylight>Kuba-dagligtyd</daylight>
				</long>
			</metazone>
			<metazone type="Davis">
				<long>
					<standard>Davis-tyd</standard>
				</long>
			</metazone>
			<metazone type="DumontDUrville">
				<long>
					<standard>Dumont-d'Urville-tyd</standard>
				</long>
			</metazone>
			<metazone type="East_Timor">
				<long>
					<standard>Oos-Timor-tyd</standard>
				</long>
			</metazone>
			<metazone type="Easter">
				<long>
					<generic>Paaseiland-tyd</generic>
					<standard>Paaseiland-standaardtyd</standard>
					<daylight>Paaseiland-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Ecuador">
				<long>
					<standard>Ecuador-tyd</standard>
				</long>
			</metazone>
			<metazone type="Europe_Central">
				<long>
					<generic>Sentraal-Europese tyd</generic>
					<standard>Sentraal-Europese standaardtyd</standard>
					<daylight>Sentraal-Europese somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Europe_Eastern">
				<long>
					<generic>Oos-Europese tyd</generic>
					<standard>Oos-Europese standaardtyd</standard>
					<daylight>Oos-Europese somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Europe_Western">
				<long>
					<generic>Wes-Europese tyd</generic>
					<standard>Wes-Europese standaardtyd</standard>
					<daylight>Wes-Europese somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Falkland">
				<long>
					<generic>Falklandeilande-tyd</generic>
					<standard>Falklandeilande-standaardtyd</standard>
					<daylight>Falklandeilande-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Fiji">
				<long>
					<generic>Fidji-tyd</generic>
					<standard>Fidji-standaardtyd</standard>
					<daylight>Fidji-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="French_Guiana">
				<long>
					<standard>Frans-Guiana-tyd</standard>
				</long>
			</metazone>
			<metazone type="French_Southern">
				<long>
					<standard>Franse Suider- en Antarktiese tyd</standard>
				</long>
			</metazone>
			<metazone type="Galapagos">
				<long>
					<standard>Galapagos-tyd</standard>
				</long>
			</metazone>
			<metazone type="Gambier">
				<long>
					<standard>Gambier-tyd</standard>
				</long>
			</metazone>
			<metazone type="Georgia">
				<long>
					<generic>Georgië-tyd</generic>
					<standard>Georgië-standaardtyd</standard>
					<daylight>Georgië-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Gilbert_Islands">
				<long>
					<standard>Gilberteilande-tyd</standard>
				</long>
			</metazone>
			<metazone type="GMT">
				<long>
					<standard>Greenwich-mediaantyd</standard>
				</long>
			</metazone>
			<metazone type="Greenland_Eastern">
				<long>
					<generic>Oos-Groenland-tyd</generic>
					<standard>Oos-Groenland-standaardtyd</standard>
					<daylight>Oos-Groenland-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Greenland_Western">
				<long>
					<generic>Wes-Groenland-tyd</generic>
					<standard>Wes-Groenland-standaardtyd</standard>
					<daylight>Wes-Groenland-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Gulf">
				<long>
					<standard>Golf-standaardtyd</standard>
				</long>
			</metazone>
			<metazone type="Guyana">
				<long>
					<standard>Guyana-tyd</standard>
				</long>
			</metazone>
			<metazone type="Hawaii_Aleutian">
				<long>
					<generic>Hawaii-Aleusiër-tyd</generic>
					<standard>Hawaii-Aleusiër-standaardtyd</standard>
					<daylight>Hawaii-Aleusiër-dagligtyd</daylight>
				</long>
			</metazone>
			<metazone type="Hong_Kong">
				<long>
					<generic>Hongkong-tyd</generic>
					<standard>Hongkong-standaardtyd</standard>
					<daylight>Hongkong-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Hovd">
				<long>
					<generic>Hovd-tyd</generic>
					<standard>Hovd-standaardtyd</standard>
					<daylight>Hovd-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="India">
				<long>
					<standard>Indië-standaardtyd</standard>
				</long>
			</metazone>
			<metazone type="Indian_Ocean">
				<long>
					<standard>Indiese Oseaan-tyd</standard>
				</long>
			</metazone>
			<metazone type="Indochina">
				<long>
					<standard>Indosjina-tyd</standard>
				</long>
			</metazone>
			<metazone type="Indonesia_Central">
				<long>
					<standard>Sentraal Indonesië-tyd</standard>
				</long>
			</metazone>
			<metazone type="Indonesia_Eastern">
				<long>
					<standard>Oos-Indonesië-tyd</standard>
				</long>
			</metazone>
			<metazone type="Indonesia_Western">
				<long>
					<standard>Wes-Indonesië-tyd</standard>
				</long>
			</metazone>
			<metazone type="Iran">
				<long>
					<generic>Iran-tyd</generic>
					<standard>Iran-standaardtyd</standard>
					<daylight>Iran-dagligtyd</daylight>
				</long>
			</metazone>
			<metazone type="Irkutsk">
				<long>
					<generic>Irkutsk-tyd</generic>
					<standard>Irkutsk-standaardtyd</standard>
					<daylight>Irkutsk-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Israel">
				<long>
					<generic>Israel-tyd</generic>
					<standard>Israel-standaardtyd</standard>
					<daylight>Israel-dagligtyd</daylight>
				</long>
			</metazone>
			<metazone type="Japan">
				<long>
					<generic>Japan-tyd</generic>
					<standard>Japan-standaardtyd</standard>
					<daylight>Japan-dagligtyd</daylight>
				</long>
			</metazone>
			<metazone type="Kamchatka">
				<long>
					<generic>Petropavlovsk-Kamchatski-tyd</generic>
					<standard>Petropavlovsk-Kamchatski-standaardtyd</standard>
					<daylight>Petropavlovsk-Kamchatski-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Kazakhstan_Eastern">
				<long>
					<standard>Oos-Kazakstan-tyd</standard>
				</long>
			</metazone>
			<metazone type="Kazakhstan_Western">
				<long>
					<standard>Wes-Kazakstan-tyd</standard>
				</long>
			</metazone>
			<metazone type="Korea">
				<long>
					<generic>Koreaanse tyd</generic>
					<standard>Koreaanse standaardtyd</standard>
					<daylight>Koreaanse dagligtyd</daylight>
				</long>
			</metazone>
			<metazone type="Kosrae">
				<long>
					<standard>Kosrae-tyd</standard>
				</long>
			</metazone>
			<metazone type="Krasnoyarsk">
				<long>
					<generic>Krasnojarsk-tyd</generic>
					<standard>Krasnojarsk-standaardtyd</standard>
					<daylight>Krasnojarsk-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Kyrgystan">
				<long>
					<standard>Kirgistan-tyd</standard>
				</long>
			</metazone>
			<metazone type="Line_Islands">
				<long>
					<standard>Line-eilande-tyd</standard>
				</long>
			</metazone>
			<metazone type="Lord_Howe">
				<long>
					<generic>Lord Howe-tyd</generic>
					<standard>Lord Howe-standaardtyd</standard>
					<daylight>Lord Howe-dagligtyd</daylight>
				</long>
			</metazone>
			<metazone type="Macquarie">
				<long>
					<standard>Macquarie-eiland-tyd</standard>
				</long>
			</metazone>
			<metazone type="Magadan">
				<long>
					<generic>Magadan-tyd</generic>
					<standard>Magadan-standaardtyd</standard>
					<daylight>Magadan-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Malaysia">
				<long>
					<standard>Maleisië-tyd</standard>
				</long>
			</metazone>
			<metazone type="Maldives">
				<long>
					<standard>Maledive-tyd</standard>
				</long>
			</metazone>
			<metazone type="Marquesas">
				<long>
					<standard>Marquesas-tyd</standard>
				</long>
			</metazone>
			<metazone type="Marshall_Islands">
				<long>
					<standard>Marshalleilande-tyd</standard>
				</long>
			</metazone>
			<metazone type="Mauritius">
				<long>
					<generic>Mauritius-tyd</generic>
					<standard>Mauritius-standaardtyd</standard>
					<daylight>Mauritius-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Mawson">
				<long>
					<standard>Mawson-tyd</standard>
				</long>
			</metazone>
			<metazone type="Mongolia">
				<long>
					<generic>Ulaanbatar-tyd</generic>
					<standard>Ulaanbatar-standaardtyd</standard>
					<daylight>Ulaanbatar-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Moscow">
				<long>
					<generic>Moskou-tyd</generic>
					<standard>Moskou-standaardtyd</standard>
					<daylight>Moskou-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Myanmar">
				<long>
					<standard>Mianmar-tyd</standard>
				</long>
			</metazone>
			<metazone type="Nauru">
				<long>
					<standard>Nauru-tyd</standard>
				</long>
			</metazone>
			<metazone type="Nepal">
				<long>
					<standard>Nepal-tyd</standard>
				</long>
			</metazone>
			<metazone type="New_Caledonia">
				<long>
					<generic>Nieu-Kaledonië-tyd</generic>
					<standard>Nieu-Kaledonië-standaardtyd</standard>
					<daylight>Nieu-Kaledonië-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="New_Zealand">
				<long>
					<generic>Nieu-Seeland-tyd</generic>
					<standard>Nieu-Seeland-standaardtyd</standard>
					<daylight>Nieu-Seeland-dagligtyd</daylight>
				</long>
			</metazone>
			<metazone type="Newfoundland">
				<long>
					<generic>Newfoundland-tyd</generic>
					<standard>Newfoundland-standaard-tyd</standard>
					<daylight>Newfoundland-dagligtyd</daylight>
				</long>
			</metazone>
			<metazone type="Niue">
				<long>
					<standard>Niue-tyd</standard>
				</long>
			</metazone>
			<metazone type="Norfolk">
				<long>
					<standard>Norfolkeilande-tyd</standard>
				</long>
			</metazone>
			<metazone type="Noronha">
				<long>
					<generic>Fernando de Noronha-tyd</generic>
					<standard>Fernando de Noronha-standaardtyd</standard>
					<daylight>Fernando de Noronha-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Novosibirsk">
				<long>
					<generic>Novosibirsk-tyd</generic>
					<standard>Novosibirsk-standaardtyd</standard>
					<daylight>Novosibirsk-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Omsk">
				<long>
					<generic>Omsk-tyd</generic>
					<standard>Omsk-standaardtyd</standard>
					<daylight>Omsk-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Pakistan">
				<long>
					<generic>Pakistan-tyd</generic>
					<standard>Pakistan-standaardtyd</standard>
					<daylight>Pakistan-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Palau">
				<long>
					<standard>Palau-tyd</standard>
				</long>
			</metazone>
			<metazone type="Papua_New_Guinea">
				<long>
					<standard>Papoea-Nieu-Guinee-tyd</standard>
				</long>
			</metazone>
			<metazone type="Paraguay">
				<long>
					<generic>Paraguay-tyd</generic>
					<standard>Paraguay-standaardtyd</standard>
					<daylight>Paraguay-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Peru">
				<long>
					<generic>Peru-tyd</generic>
					<standard>Peru-standaardtyd</standard>
					<daylight>Peru-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Philippines">
				<long>
					<generic>Filippynse tyd</generic>
					<standard>Filippynse standaardtyd</standard>
					<daylight>Filippynse somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Phoenix_Islands">
				<long>
					<standard>Fenikseilande-tyd</standard>
				</long>
			</metazone>
			<metazone type="Pierre_Miquelon">
				<long>
					<generic>Sint-Pierre en Miquelon-tyd</generic>
					<standard>Sint-Pierre en Miquelon-standaardtyd</standard>
					<daylight>Sint-Pierre en Miquelon-dagligtyd</daylight>
				</long>
			</metazone>
			<metazone type="Pitcairn">
				<long>
					<standard>Pitcairn-tyd</standard>
				</long>
			</metazone>
			<metazone type="Ponape">
				<long>
					<standard>Ponape-tyd</standard>
				</long>
			</metazone>
			<metazone type="Reunion">
				<long>
					<standard>Reunion-tyd</standard>
				</long>
			</metazone>
			<metazone type="Rothera">
				<long>
					<standard>Rothera-tyd</standard>
				</long>
			</metazone>
			<metazone type="Sakhalin">
				<long>
					<generic>Sakhalin-tyd</generic>
					<standard>Sakhalin-standaardtyd</standard>
					<daylight>Sakhalin-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Samara">
				<long>
					<generic>Samara-tyd</generic>
					<standard>Samara-standaardtyd</standard>
					<daylight>Samara-dagligtyd</daylight>
				</long>
			</metazone>
			<metazone type="Samoa">
				<long>
					<generic>Samoa-tyd</generic>
					<standard>Samoa-standaardtyd</standard>
					<daylight>Samoa-dagligtyd</daylight>
				</long>
			</metazone>
			<metazone type="Seychelles">
				<long>
					<standard>Seychelle-tyd</standard>
				</long>
			</metazone>
			<metazone type="Singapore">
				<long>
					<standard>Singapoer-standaardtyd</standard>
				</long>
			</metazone>
			<metazone type="Solomon">
				<long>
					<standard>Solomoneilande-tyd</standard>
				</long>
			</metazone>
			<metazone type="South_Georgia">
				<long>
					<standard>Suid-Georgië-tyd</standard>
				</long>
			</metazone>
			<metazone type="Suriname">
				<long>
					<standard>Suriname-tyd</standard>
				</long>
			</metazone>
			<metazone type="Syowa">
				<long>
					<standard>Syowa-tyd</standard>
				</long>
			</metazone>
			<metazone type="Tahiti">
				<long>
					<standard>Tahiti-tyd</standard>
				</long>
			</metazone>
			<metazone type="Taipei">
				<long>
					<generic>Taipei-tyd</generic>
					<standard>Taipei-standaardtyd</standard>
					<daylight>Taipei-dagligtyd</daylight>
				</long>
			</metazone>
			<metazone type="Tajikistan">
				<long>
					<standard>Tadjikistan-tyd</standard>
				</long>
			</metazone>
			<metazone type="Tokelau">
				<long>
					<standard>Tokelau-tyd</standard>
				</long>
			</metazone>
			<metazone type="Tonga">
				<long>
					<generic>Tonga-tyd</generic>
					<standard>Tonga-standaardtyd</standard>
					<daylight>Tonga-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Truk">
				<long>
					<standard>Chuuk-tyd</standard>
				</long>
			</metazone>
			<metazone type="Turkmenistan">
				<long>
					<generic>Turkmenistan-tyd</generic>
					<standard>Turkmenistan-standaardtyd</standard>
					<daylight>Turkmenistan-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Tuvalu">
				<long>
					<standard>Tuvalu-tyd</standard>
				</long>
			</metazone>
			<metazone type="Uruguay">
				<long>
					<generic>Uruguay-tyd</generic>
					<standard>Uruguay-standaardtyd</standard>
					<daylight>Uruguay-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Uzbekistan">
				<long>
					<generic>Oesbekistan-tyd</generic>
					<standard>Oesbekistan-standaardtyd</standard>
					<daylight>Oesbekistan-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Vanuatu">
				<long>
					<generic>Vanuatu-tyd</generic>
					<standard>Vanuatu-standaardtyd</standard>
					<daylight>Vanuatu-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Venezuela">
				<long>
					<standard>Venezuela-tyd</standard>
				</long>
			</metazone>
			<metazone type="Vladivostok">
				<long>
					<generic>Vladivostok-tyd</generic>
					<standard>Vladivostok-standaardtyd</standard>
					<daylight>Vladivostok-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Volgograd">
				<long>
					<generic>Volgograd-tyd</generic>
					<standard>Volgograd-standaardtyd</standard>
					<daylight>Volgograd-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Vostok">
				<long>
					<standard>Vostok-tyd</standard>
				</long>
			</metazone>
			<metazone type="Wake">
				<long>
					<standard>Wake-eiland-tyd</standard>
				</long>
			</metazone>
			<metazone type="Wallis">
				<long>
					<standard>Wallis en Futuna-tyd</standard>
				</long>
			</metazone>
			<metazone type="Yakutsk">
				<long>
					<generic>Jakoetsk-tyd</generic>
					<standard>Jakoetsk-standaardtyd</standard>
					<daylight>Jakoetsk-somertyd</daylight>
				</long>
			</metazone>
			<metazone type="Yekaterinburg">
				<long>
					<generic>Jekaterinburg-tyd</generic>
					<standard>Jekaterinburg-standaardtyd</standard>
					<daylight>Jekaterinburg-somertyd</daylight>
				</long>
			</metazone>
		</timeZoneNames>
	</dates>
	<numbers>
		<defaultNumberingSystem>latn</defaultNumberingSystem>
		<symbols numberSystem="latn">
			<decimal>,</decimal>
			<group> </group>
			<list>;</list>
			<percentSign>%</percentSign>
			<plusSign>+</plusSign>
			<minusSign>-</minusSign>
			<exponential>E</exponential>
			<superscriptingExponent>×</superscriptingExponent>
			<perMille>‰</perMille>
			<infinity>∞</infinity>
			<nan>NaN</nan>
		</symbols>
		<decimalFormats numberSystem="latn">
			<decimalFormatLength>
				<decimalFormat>
					<pattern>#,##0.###</pattern>
				</decimalFormat>
			</decimalFormatLength>
			<decimalFormatLength type="long">
				<decimalFormat>
					<pattern type="1000" count="one">0 duisend</pattern>
					<pattern type="1000" count="other">0 duisend</pattern>
					<pattern type="10000" count="one">00 duisend</pattern>
					<pattern type="10000" count="other">00 duisend</pattern>
					<pattern type="100000" count="one">000 duisend</pattern>
					<pattern type="100000" count="other">000 duisend</pattern>
					<pattern type="1000000" count="one">0 miljoen</pattern>
					<pattern type="1000000" count="other">0 miljoen</pattern>
					<pattern type="10000000" count="one">00 miljoen</pattern>
					<pattern type="10000000" count="other">00 miljoen</pattern>
					<pattern type="100000000" count="one">000 miljoen</pattern>
					<pattern type="100000000" count="other">000 miljoen</pattern>
					<pattern type="1000000000" count="one">0 miljard</pattern>
					<pattern type="1000000000" count="other">0 miljard</pattern>
					<pattern type="10000000000" count="one">00 miljard</pattern>
					<pattern type="10000000000" count="other">00 miljard</pattern>
					<pattern type="100000000000" count="one">000 miljard</pattern>
					<pattern type="100000000000" count="other">000 miljard</pattern>
					<pattern type="1000000000000" count="one">0 biljoen</pattern>
					<pattern type="1000000000000" count="other">0 biljoen</pattern>
					<pattern type="10000000000000" count="one">00 biljoen</pattern>
					<pattern type="10000000000000" count="other">00 biljoen</pattern>
					<pattern type="100000000000000" count="one">000 biljoen</pattern>
					<pattern type="100000000000000" count="other">000 biljoen</pattern>
				</decimalFormat>
			</decimalFormatLength>
			<decimalFormatLength type="short">
				<decimalFormat>
					<pattern type="1000" count="one">0</pattern>
					<pattern type="1000" count="other">0</pattern>
					<pattern type="10000" count="one">0</pattern>
					<pattern type="10000" count="other">0</pattern>
					<pattern type="100000" count="one">0</pattern>
					<pattern type="100000" count="other">0</pattern>
					<pattern type="1000000" count="one">0 m</pattern>
					<pattern type="1000000" count="other">0 m</pattern>
					<pattern type="10000000" count="one">00 m</pattern>
					<pattern type="10000000" count="other">00 m</pattern>
					<pattern type="100000000" count="one">000 m</pattern>
					<pattern type="100000000" count="other">000 m</pattern>
					<pattern type="1000000000" count="one">0 mjd</pattern>
					<pattern type="1000000000" count="other">0 mjd</pattern>
					<pattern type="10000000000" count="one">00 mjd</pattern>
					<pattern type="10000000000" count="other">00 mjd</pattern>
					<pattern type="100000000000" count="one">000 mjd</pattern>
					<pattern type="100000000000" count="other">000 mjd</pattern>
					<pattern type="1000000000000" count="one">0 bn</pattern>
					<pattern type="1000000000000" count="other">0 bn</pattern>
					<pattern type="10000000000000" count="one">00 bn</pattern>
					<pattern type="10000000000000" count="other">00 bn</pattern>
					<pattern type="100000000000000" count="one">000 bn</pattern>
					<pattern type="100000000000000" count="other">000 bn</pattern>
				</decimalFormat>
			</decimalFormatLength>
		</decimalFormats>
		<scientificFormats numberSystem="latn">
			<scientificFormatLength>
				<scientificFormat>
					<pattern>#E0</pattern>
				</scientificFormat>
			</scientificFormatLength>
		</scientificFormats>
		<percentFormats numberSystem="latn">
			<percentFormatLength>
				<percentFormat>
					<pattern>#,##0%</pattern>
				</percentFormat>
			</percentFormatLength>
		</percentFormats>
		<currencyFormats numberSystem="latn">
			<currencyFormatLength>
				<currencyFormat type="standard">
					<pattern>¤#,##0.00</pattern>
				</currencyFormat>
				<currencyFormat type="accounting">
					<pattern>¤#,##0.00;(¤#,##0.00)</pattern>
				</currencyFormat>
			</currencyFormatLength>
			<unitPattern count="one">{0} {1}</unitPattern>
			<unitPattern count="other">{0} {1}</unitPattern>
		</currencyFormats>
		<currencies>
			<currency type="AED">
				<displayName>Verenigde Arabiese Emirate dirham</displayName>
			</currency>
			<currency type="AFN">
				<displayName>Afgaanse afgani</displayName>
			</currency>
			<currency type="ALL">
				<displayName>Albanese lek</displayName>
			</currency>
			<currency type="AMD">
				<displayName>Armeense dram</displayName>
			</currency>
			<currency type="ANG">
				<displayName>Nederlands-Antilliaanse gulde</displayName>
			</currency>
			<currency type="AOA">
				<displayName>Angolese kwanza</displayName>
			</currency>
			<currency type="ARS">
				<displayName>Argentynse peso</displayName>
			</currency>
			<currency type="AUD">
				<displayName>Australiese dollar</displayName>
				<displayName count="one">Australiese dollar</displayName>
				<displayName count="other">Australiese dollar</displayName>
			</currency>
			<currency type="AWG">
				<displayName>Arubaanse floryn</displayName>
			</currency>
			<currency type="AZN">
				<displayName>Azerbeidjaanse manat</displayName>
			</currency>
			<currency type="BAM">
				<displayName>Bosnië en Herzegowina omskakelbare marka</displayName>
			</currency>
			<currency type="BBD">
				<displayName>Barbados-dollar</displayName>
			</currency>
			<currency type="BDT">
				<displayName>Bangladesjiese taka</displayName>
			</currency>
			<currency type="BGN">
				<displayName>Bulgaarse lev</displayName>
			</currency>
			<currency type="BHD">
				<displayName>Bahrainse dinar</displayName>
			</currency>
			<currency type="BIF">
				<displayName>Burundiese frank</displayName>
			</currency>
			<currency type="BMD">
				<displayName>Bermuda-dollar</displayName>
			</currency>
			<currency type="BND">
				<displayName>Broeneise dollar</displayName>
			</currency>
			<currency type="BOB">
				<displayName>Boliviaanse boliviano</displayName>
			</currency>
			<currency type="BRL">
				<displayName>Brasiliaanse real</displayName>
				<symbol>R$</symbol>
			</currency>
			<currency type="BSD">
				<displayName>Bahamiaanse dollar</displayName>
			</currency>
			<currency type="BTN">
				<displayName>Bhoetanese ngoeltroem</displayName>
			</currency>
			<currency type="BWP">
				<displayName>Botswana pula</displayName>
			</currency>
			<currency type="BYR">
				<displayName>Belo-Russiese roebel</displayName>
			</currency>
			<currency type="BZD">
				<displayName>Beliziese dollar</displayName>
			</currency>
			<currency type="CAD">
				<displayName>Kanadese dollar</displayName>
				<symbol>CA$</symbol>
			</currency>
			<currency type="CDF">
				<displayName>Kongolese frank</displayName>
			</currency>
			<currency type="CHF">
				<displayName>Switserse frank</displayName>
			</currency>
			<currency type="CLP">
				<displayName>Chileense peso</displayName>
			</currency>
			<currency type="CNY">
				<displayName>Sjinese joean renminbi</displayName>
				<symbol>CN¥</symbol>
			</currency>
			<currency type="COP">
				<displayName>Colombiaanse peso</displayName>
			</currency>
			<currency type="CRC">
				<displayName>Costa Ricaanse colón</displayName>
			</currency>
			<currency type="CUC">
				<displayName>Kubaanse omskakelbare peso</displayName>
			</currency>
			<currency type="CUP">
				<displayName>Kubaanse peso</displayName>
			</currency>
			<currency type="CVE">
				<displayName>Kaap Verdiese escudo</displayName>
			</currency>
			<currency type="CZK">
				<displayName>Tsjeggiese kroon</displayName>
			</currency>
			<currency type="DJF">
				<displayName>Djiboeti frank</displayName>
			</currency>
			<currency type="DKK">
				<displayName>Deense kroon</displayName>
			</currency>
			<currency type="DOP">
				<displayName>Dominikaanse peso</displayName>
				<displayName count="one">Dominikaanse peso</displayName>
				<displayName count="other">Dominikaanse peso</displayName>
			</currency>
			<currency type="DZD">
				<displayName>Algeriese dinar</displayName>
			</currency>
			<currency type="EGP">
				<displayName>Egiptiese pond</displayName>
			</currency>
			<currency type="ERN">
				<displayName>Eritrese nakfa</displayName>
			</currency>
			<currency type="ETB">
				<displayName>Etiopiese birr</displayName>
			</currency>
			<currency type="EUR">
				<displayName>Euro</displayName>
				<symbol>€</symbol>
			</currency>
			<currency type="FJD">
				<displayName>Fidjiaanse dollar</displayName>
			</currency>
			<currency type="FKP">
				<displayName>Falkland-eilande pond</displayName>
			</currency>
			<currency type="GBP">
				<displayName>Britse pond</displayName>
				<symbol>£</symbol>
			</currency>
			<currency type="GEL">
				<displayName>Georgiese lari</displayName>
			</currency>
			<currency type="GHC">
				<displayName>Ghanese cedi (1979–2007)</displayName>
			</currency>
			<currency type="GHS">
				<displayName>Ghanese cedi</displayName>
			</currency>
			<currency type="GIP">
				<displayName>Gibraltarese pond</displayName>
			</currency>
			<currency type="GMD">
				<displayName>Gambiese dalasi</displayName>
			</currency>
			<currency type="GNF">
				<displayName>Guinese frank</displayName>
			</currency>
			<currency type="GNS">
				<displayName>Guinese syli</displayName>
			</currency>
			<currency type="GTQ">
				<displayName>Guatemalaanse quetzal</displayName>
			</currency>
			<currency type="GYD">
				<displayName>Guyanese dollar</displayName>
			</currency>
			<currency type="HKD">
				<displayName>Hong Kong dollar</displayName>
				<symbol>HK$</symbol>
			</currency>
			<currency type="HNL">
				<displayName>Hondurese lempira</displayName>
			</currency>
			<currency type="HRK">
				<displayName>Kroatiese kuna</displayName>
			</currency>
			<currency type="HTG">
				<displayName>Haïtiaanse gourde</displayName>
			</currency>
			<currency type="IDR">
				<displayName>Indonesiese roepia</displayName>
			</currency>
			<currency type="ILS">
				<displayName>Israeliese nuwe sikkel</displayName>
				<symbol>₪</symbol>
			</currency>
			<currency type="INR">
				<displayName>Indiese rupee</displayName>
				<symbol>₹</symbol>
			</currency>
			<currency type="IQD">
				<displayName>Irakse dinar</displayName>
			</currency>
			<currency type="IRR">
				<displayName>Iranse rial</displayName>
			</currency>
			<currency type="ISK">
				<displayName>Yslandse kroon</displayName>
			</currency>
			<currency type="ITL">
				<displayName draft="contributed">Italiaanse lier</displayName>
			</currency>
			<currency type="JMD">
				<displayName>Jamaikaanse dollar</displayName>
			</currency>
			<currency type="JOD">
				<displayName>Jordaniese dinar</displayName>
			</currency>
			<currency type="JPY">
				<displayName>Japannese jen</displayName>
				<symbol>JP¥</symbol>
			</currency>
			<currency type="KES">
				<displayName>Keniaanse sjieling</displayName>
			</currency>
			<currency type="KGS">
				<displayName>Kirgisiese som</displayName>
			</currency>
			<currency type="KHR">
				<displayName>Kambodjaanse riel</displayName>
			</currency>
			<currency type="KMF">
				<displayName>Comoriese frank</displayName>
			</currency>
			<currency type="KPW">
				<displayName>Noord-Koreaanse won</displayName>
			</currency>
			<currency type="KRW">
				<displayName>Suid-Koreaanse won</displayName>
				<symbol>₩</symbol>
			</currency>
			<currency type="KWD">
				<displayName>Koeweitse dinar</displayName>
			</currency>
			<currency type="KYD">
				<displayName>Cayman-eilande dollar</displayName>
			</currency>
			<currency type="KZT">
				<displayName>Kazakse tenge</displayName>
			</currency>
			<currency type="LAK">
				<displayName>Laosiaanse kip</displayName>
			</currency>
			<currency type="LBP">
				<displayName>Lebanese pond</displayName>
			</currency>
			<currency type="LKR">
				<displayName>Sri Lankaanse roepee</displayName>
			</currency>
			<currency type="LRD">
				<displayName>Liberiese dollar</displayName>
			</currency>
			<currency type="LSL">
				<displayName>Lesotho loti</displayName>
			</currency>
			<currency type="LTL">
				<displayName>Litause litas</displayName>
			</currency>
			<currency type="LVL">
				<displayName>Lettiese lats</displayName>
			</currency>
			<currency type="LYD">
				<displayName>Libiese dinar</displayName>
			</currency>
			<currency type="MAD">
				<displayName>Marokkaanse dirham</displayName>
			</currency>
			<currency type="MDL">
				<displayName>Moldowiese leu</displayName>
			</currency>
			<currency type="MGA">
				<displayName>Malgassiese ariary</displayName>
			</currency>
			<currency type="MKD">
				<displayName>Macedoniese denar</displayName>
			</currency>
			<currency type="MMK">
				<displayName>Myanma kyat</displayName>
			</currency>
			<currency type="MNT">
				<displayName>Mongoolse toegrik</displayName>
			</currency>
			<currency type="MOP">
				<displayName>Macaose pataca</displayName>
			</currency>
			<currency type="MRO">
				<displayName>Mauritaniese ouguiya</displayName>
			</currency>
			<currency type="MUR">
				<displayName>Mauritiaanse rupee</displayName>
			</currency>
			<currency type="MVR">
				<displayName>Malediviese rufia</displayName>
			</currency>
			<currency type="MWK">
				<displayName>Malawiese kwacha</displayName>
			</currency>
			<currency type="MXN">
				<displayName>Meksikaanse peso</displayName>
				<symbol>MX$</symbol>
			</currency>
			<currency type="MYR">
				<displayName>Maleisiese ringgit</displayName>
			</currency>
			<currency type="MZM">
				<displayName>Mosambiekse metical (1980–2006)</displayName>
			</currency>
			<currency type="MZN">
				<displayName>Mosambiekse metical</displayName>
			</currency>
			<currency type="NAD">
				<displayName>Namibiese dollar</displayName>
			</currency>
			<currency type="NGN">
				<displayName>Nigeriese naira</displayName>
			</currency>
			<currency type="NIO">
				<displayName>Nicaraguaanse córdoba</displayName>
			</currency>
			<currency type="NOK">
				<displayName>Noorse kroon</displayName>
			</currency>
			<currency type="NPR">
				<displayName>Nepalese roepee</displayName>
			</currency>
			<currency type="NZD">
				<displayName>Nieu-Seeland dollar</displayName>
				<symbol>NZ$</symbol>
			</currency>
			<currency type="OMR">
				<displayName>Omaanse rial</displayName>
			</currency>
			<currency type="PAB">
				<displayName>Panamese balboa</displayName>
			</currency>
			<currency type="PEN">
				<displayName>Peruaanse nuwe sol</displayName>
			</currency>
			<currency type="PGK">
				<displayName>Papoease kina</displayName>
			</currency>
			<currency type="PHP">
				<displayName>Filippynse peso</displayName>
			</currency>
			<currency type="PKR">
				<displayName>Pakistanse roepee</displayName>
			</currency>
			<currency type="PLN">
				<displayName>Poolse zloty</displayName>
			</currency>
			<currency type="PYG">
				<displayName>Paraguaanse guarani</displayName>
			</currency>
			<currency type="QAR">
				<displayName>Katarese rial</displayName>
			</currency>
			<currency type="RON">
				<displayName>Roemeense leu</displayName>
				<displayName count="one">Roemeense leu</displayName>
				<displayName count="other">Roemeense leu</displayName>
			</currency>
			<currency type="RSD">
				<displayName>Serbiese dinar</displayName>
			</currency>
			<currency type="RUB">
				<displayName>Russiese roebel</displayName>
			</currency>
			<currency type="RWF">
				<displayName>Rwandiese frank</displayName>
			</currency>
			<currency type="SAR">
				<displayName>Saoedi-Arabiese riyal</displayName>
			</currency>
			<currency type="SBD">
				<displayName>Salomonseilande dollar</displayName>
				<displayName count="one">Salomonseilande dollar</displayName>
				<displayName count="other">Salomonseilande dollar</displayName>
			</currency>
			<currency type="SCR">
				<displayName>Seychellese rupee</displayName>
			</currency>
			<currency type="SDG">
				<displayName>Soedannese pond</displayName>
			</currency>
			<currency type="SDP">
				<displayName>Soedannese pond (1957–1998)</displayName>
			</currency>
			<currency type="SEK">
				<displayName>Sweedse kroon</displayName>
			</currency>
			<currency type="SGD">
				<displayName>Singapoer-dollar</displayName>
			</currency>
			<currency type="SHP">
				<displayName>Sint Helena pond</displayName>
			</currency>
			<currency type="SLL">
				<displayName>Sierra Leonese leone</displayName>
			</currency>
			<currency type="SOS">
				<displayName>Somaliese sjieling</displayName>
			</currency>
			<currency type="SRD">
				<displayName>Surinaamse dollar</displayName>
			</currency>
			<currency type="SSP">
				<displayName>Suid-Soedanese pond</displayName>
				<displayName count="one">Suid-Soedanese pond</displayName>
				<displayName count="other">Suid-Soedanese pond</displayName>
			</currency>
			<currency type="STD">
				<displayName>São Tomé en Príncipe dobra</displayName>
			</currency>
			<currency type="SYP">
				<displayName>Siriese pond</displayName>
			</currency>
			<currency type="SZL">
				<displayName>Swazilandse lilangeni</displayName>
			</currency>
			<currency type="THB">
				<displayName>Thaise baht</displayName>
				<symbol>฿</symbol>
			</currency>
			<currency type="TJS">
				<displayName>Tadjikse roebel</displayName>
			</currency>
			<currency type="TMT">
				<displayName>Toerkmeense manat</displayName>
			</currency>
			<currency type="TND">
				<displayName>Tunisiese dinar</displayName>
			</currency>
			<currency type="TOP">
				<displayName>Tongaanse pa'anga</displayName>
			</currency>
			<currency type="TRL">
				<displayName>Turkse lier (1922–2005)</displayName>
			</currency>
			<currency type="TRY">
				<displayName>Turkse lier</displayName>
			</currency>
			<currency type="TTD">
				<displayName>Trinidad en Tobago dollar</displayName>
			</currency>
			<currency type="TWD">
				<displayName>Nuwe Taiwanese dollar</displayName>
				<symbol>NT$</symbol>
			</currency>
			<currency type="TZS">
				<displayName>Tanzaniese sjieling</displayName>
			</currency>
			<currency type="UAH">
				<displayName>Oekraïnse hriwna</displayName>
			</currency>
			<currency type="UGX">
				<displayName>Ugandese sjieling</displayName>
			</currency>
			<currency type="USD">
				<displayName>Amerikaanse dollar</displayName>
				<symbol>US$</symbol>
			</currency>
			<currency type="UYU">
				<displayName>Uruguaanse peso</displayName>
			</currency>
			<currency type="UZS">
				<displayName>Oezbekiese som</displayName>
			</currency>
			<currency type="VEF">
				<displayName>Venezolaanse bolivar</displayName>
			</currency>
			<currency type="VND">
				<displayName>Viëtnamese dong</displayName>
				<symbol>₫</symbol>
			</currency>
			<currency type="VUV">
				<displayName>Vanuatu vatu</displayName>
			</currency>
			<currency type="WST">
				<displayName>Samoaanse tala</displayName>
			</currency>
			<currency type="XAF">
				<displayName>CFA frank BEAC</displayName>
				<symbol>FCFA</symbol>
			</currency>
			<currency type="XCD">
				<displayName>Oos-Karibbiese dollar</displayName>
				<symbol>EC$</symbol>
			</currency>
			<currency type="XOF">
				<displayName>CFA frank BCEAO</displayName>
				<symbol>CFA</symbol>
			</currency>
			<currency type="XPF">
				<displayName>CFP-frank</displayName>
				<symbol>CFPF</symbol>
			</currency>
			<currency type="XXX">
				<displayName>Onbekende geldeenheid</displayName>
			</currency>
			<currency type="YER">
				<displayName>Jemenitiese rial</displayName>
			</currency>
			<currency type="ZAR">
				<displayName>Suid-Afrikaanse rand</displayName>
				<symbol>R</symbol>
			</currency>
			<currency type="ZMK">
				<displayName>Zambiese kwacha (1968–2012)</displayName>
			</currency>
			<currency type="ZMW">
				<displayName>Zambiese kwacha</displayName>
			</currency>
			<currency type="ZWD">
				<displayName>Zimbabwiese dollar</displayName>
			</currency>
		</currencies>
		<miscPatterns numberSystem="latn">
			<pattern type="atLeast">{0}+</pattern>
			<pattern type="range">{0}–{1}</pattern>
		</miscPatterns>
	</numbers>
	<units>
		<unitLength type="long">
			<compoundUnit type="per">
				<compoundUnitPattern>{0} per {1}</compoundUnitPattern>
			</compoundUnit>
			<unit type="acceleration-g-force">
				<unitPattern count="one">{0} swaartekrag van die Aarde</unitPattern>
				<unitPattern count="other">{0} swaartekrag van die Aarde</unitPattern>
			</unit>
			<unit type="angle-arc-minute">
				<unitPattern count="one">{0} boogminuut</unitPattern>
				<unitPattern count="other">{0} boogminute</unitPattern>
			</unit>
			<unit type="angle-arc-second">
				<unitPattern count="one">{0} boogsekonde</unitPattern>
				<unitPattern count="other">{0} boogsekondes</unitPattern>
			</unit>
			<unit type="angle-degree">
				<unitPattern count="one">{0} booggraad</unitPattern>
				<unitPattern count="other">{0} booggrade</unitPattern>
			</unit>
			<unit type="area-acre">
				<unitPattern count="one">{0} akker</unitPattern>
				<unitPattern count="other">{0} akker</unitPattern>
			</unit>
			<unit type="area-hectare">
				<unitPattern count="one">{0} hektaar</unitPattern>
				<unitPattern count="other">{0} hektaar</unitPattern>
			</unit>
			<unit type="area-square-foot">
				<unitPattern count="one">{0} vierkante voet</unitPattern>
				<unitPattern count="other">{0} vierkante voet</unitPattern>
			</unit>
			<unit type="area-square-kilometer">
				<unitPattern count="one">{0} vierkante kilometer</unitPattern>
				<unitPattern count="other">{0} vierkante kilometer</unitPattern>
			</unit>
			<unit type="area-square-meter">
				<unitPattern count="one">{0} vierkante meter</unitPattern>
				<unitPattern count="other">{0} vierkante meter</unitPattern>
			</unit>
			<unit type="area-square-mile">
				<unitPattern count="one">{0} vierkante myl</unitPattern>
				<unitPattern count="other">{0} vierkante myl</unitPattern>
			</unit>
			<unit type="duration-day">
				<unitPattern count="one">{0} dag</unitPattern>
				<unitPattern count="other">{0} dae</unitPattern>
			</unit>
			<unit type="duration-hour">
				<unitPattern count="one">{0} uur</unitPattern>
				<unitPattern count="other">{0} uur</unitPattern>
			</unit>
			<unit type="duration-millisecond">
				<unitPattern count="one">{0} millisekonde</unitPattern>
				<unitPattern count="other">{0} millisekondes</unitPattern>
			</unit>
			<unit type="duration-minute">
				<unitPattern count="one">{0} minuut</unitPattern>
				<unitPattern count="other">{0} minute</unitPattern>
			</unit>
			<unit type="duration-month">
				<unitPattern count="one">{0} maand</unitPattern>
				<unitPattern count="other">{0} maande</unitPattern>
			</unit>
			<unit type="duration-second">
				<unitPattern count="one">{0} sekonde</unitPattern>
				<unitPattern count="other">{0} sekondes</unitPattern>
			</unit>
			<unit type="duration-week">
				<unitPattern count="one">{0} week</unitPattern>
				<unitPattern count="other">{0} weke</unitPattern>
			</unit>
			<unit type="duration-year">
				<unitPattern count="one">{0} jaar</unitPattern>
				<unitPattern count="other">{0} jaar</unitPattern>
			</unit>
			<unit type="length-centimeter">
				<unitPattern count="one">{0} sentimeter</unitPattern>
				<unitPattern count="other">{0} sentimeter</unitPattern>
			</unit>
			<unit type="length-foot">
				<unitPattern count="one">{0} voet</unitPattern>
				<unitPattern count="other">{0} voete</unitPattern>
			</unit>
			<unit type="length-inch">
				<unitPattern count="one">{0} duim</unitPattern>
				<unitPattern count="other">{0} duim</unitPattern>
			</unit>
			<unit type="length-kilometer">
				<unitPattern count="one">{0} kilometer</unitPattern>
				<unitPattern count="other">{0} kilometer</unitPattern>
			</unit>
			<unit type="length-light-year">
				<unitPattern count="one">{0} lj</unitPattern>
				<unitPattern count="other">{0} lj</unitPattern>
			</unit>
			<unit type="length-meter">
				<unitPattern count="one">{0} meter</unitPattern>
				<unitPattern count="other">{0} meter</unitPattern>
			</unit>
			<unit type="length-mile">
				<unitPattern count="one">{0} myl</unitPattern>
				<unitPattern count="other">{0} myl</unitPattern>
			</unit>
			<unit type="length-millimeter">
				<unitPattern count="one">{0} millimeter</unitPattern>
				<unitPattern count="other">{0} millimeter</unitPattern>
			</unit>
			<unit type="length-picometer">
				<unitPattern count="one">{0} pikometer</unitPattern>
				<unitPattern count="other">{0} pikometer</unitPattern>
			</unit>
			<unit type="length-yard">
				<unitPattern count="one">{0} jaart</unitPattern>
				<unitPattern count="other">{0} jaart</unitPattern>
			</unit>
			<unit type="mass-gram">
				<unitPattern count="one">{0} gram</unitPattern>
				<unitPattern count="other">{0} gram</unitPattern>
			</unit>
			<unit type="mass-kilogram">
				<unitPattern count="one">{0} kilogram</unitPattern>
				<unitPattern count="other">{0} kilogram</unitPattern>
			</unit>
			<unit type="mass-ounce">
				<unitPattern count="one">{0} oz.</unitPattern>
				<unitPattern count="other">{0} oz.</unitPattern>
			</unit>
			<unit type="mass-pound">
				<unitPattern count="one">{0} lb.</unitPattern>
				<unitPattern count="other">{0} lb.</unitPattern>
			</unit>
			<unit type="power-horsepower">
				<unitPattern count="one">{0} perdekrag</unitPattern>
				<unitPattern count="other">{0} perdekrag</unitPattern>
			</unit>
			<unit type="power-kilowatt">
				<unitPattern count="one">{0} kilowatt</unitPattern>
				<unitPattern count="other">{0} kilowatt</unitPattern>
			</unit>
			<unit type="power-watt">
				<unitPattern count="one">{0} watt</unitPattern>
				<unitPattern count="other">{0} watt</unitPattern>
			</unit>
			<unit type="pressure-hectopascal">
				<unitPattern count="one">{0} hektopascal</unitPattern>
				<unitPattern count="other">{0} hektopascal</unitPattern>
			</unit>
			<unit type="pressure-inch-hg">
				<unitPattern count="one">{0} duim kwik</unitPattern>
				<unitPattern count="other">{0} duim kwik</unitPattern>
			</unit>
			<unit type="pressure-millibar">
				<unitPattern count="one">{0} millibar</unitPattern>
				<unitPattern count="other">{0} millibar</unitPattern>
			</unit>
			<unit type="speed-kilometer-per-hour">
				<unitPattern count="one">{0} kilometer per uur</unitPattern>
				<unitPattern count="other">{0} kilometer per uur</unitPattern>
			</unit>
			<unit type="speed-meter-per-second">
				<unitPattern count="one">{0} meter per sekonde</unitPattern>
				<unitPattern count="other">{0} meter per sekonde</unitPattern>
			</unit>
			<unit type="speed-mile-per-hour">
				<unitPattern count="one">{0} myl per uur</unitPattern>
				<unitPattern count="other">{0} myl per uur</unitPattern>
			</unit>
			<unit type="temperature-celsius">
				<unitPattern count="one">{0} graad Celsius</unitPattern>
				<unitPattern count="other">{0} grade Celsius</unitPattern>
			</unit>
			<unit type="temperature-fahrenheit">
				<unitPattern count="one">{0} graad Fahrenheit</unitPattern>
				<unitPattern count="other">{0} grade Fahrenheit</unitPattern>
			</unit>
			<unit type="volume-cubic-kilometer">
				<unitPattern count="one">{0} kubieke kilometer</unitPattern>
				<unitPattern count="other">{0} kubieke kilometer</unitPattern>
			</unit>
			<unit type="volume-cubic-mile">
				<unitPattern count="one">{0} kubieke myl</unitPattern>
				<unitPattern count="other">{0} kubieke myl</unitPattern>
			</unit>
			<unit type="volume-liter">
				<unitPattern count="one">{0} liter</unitPattern>
				<unitPattern count="other">{0} liter</unitPattern>
			</unit>
		</unitLength>
		<unitLength type="short">
			<compoundUnit type="per">
				<compoundUnitPattern>{0}/{1}</compoundUnitPattern>
			</compoundUnit>
			<unit type="acceleration-g-force">
				<unitPattern count="one">{0} G</unitPattern>
				<unitPattern count="other">{0} G</unitPattern>
			</unit>
			<unit type="angle-arc-minute">
				<unitPattern count="one">{0} min</unitPattern>
				<unitPattern count="other">{0} min</unitPattern>
			</unit>
			<unit type="angle-arc-second">
				<unitPattern count="one">{0} sek</unitPattern>
				<unitPattern count="other">{0} sek</unitPattern>
			</unit>
			<unit type="angle-degree">
				<unitPattern count="one">{0} gr.</unitPattern>
				<unitPattern count="other">{0} gr.</unitPattern>
			</unit>
			<unit type="area-acre">
				<unitPattern count="one">{0} ak</unitPattern>
				<unitPattern count="other">{0} ak</unitPattern>
			</unit>
			<unit type="area-hectare">
				<unitPattern count="one">{0} ha</unitPattern>
				<unitPattern count="other">{0} ha</unitPattern>
			</unit>
			<unit type="area-square-foot">
				<unitPattern count="one">{0} vt.²</unitPattern>
				<unitPattern count="other">{0} vt.²</unitPattern>
			</unit>
			<unit type="area-square-kilometer">
				<unitPattern count="one">{0} km²</unitPattern>
				<unitPattern count="other">{0} km²</unitPattern>
			</unit>
			<unit type="area-square-meter">
				<unitPattern count="one">{0} m²</unitPattern>
				<unitPattern count="other">{0} m²</unitPattern>
			</unit>
			<unit type="area-square-mile">
				<unitPattern count="one">{0} myl²</unitPattern>
				<unitPattern count="other">{0} myl²</unitPattern>
			</unit>
			<unit type="duration-day">
				<unitPattern count="one">{0} dag</unitPattern>
				<unitPattern count="other">{0} dae</unitPattern>
			</unit>
			<unit type="duration-hour">
				<unitPattern count="one">{0} u.</unitPattern>
				<unitPattern count="other">{0} u.</unitPattern>
			</unit>
			<unit type="duration-millisecond">
				<unitPattern count="one">{0} ms</unitPattern>
				<unitPattern count="other">{0} ms</unitPattern>
			</unit>
			<unit type="duration-minute">
				<unitPattern count="one">{0} min.</unitPattern>
				<unitPattern count="other">{0} min.</unitPattern>
			</unit>
			<unit type="duration-month">
				<unitPattern count="one">{0} md.</unitPattern>
				<unitPattern count="other">{0} md.</unitPattern>
			</unit>
			<unit type="duration-second">
				<unitPattern count="one">{0} sek.</unitPattern>
				<unitPattern count="other">{0} sek.</unitPattern>
			</unit>
			<unit type="duration-week">
				<unitPattern count="one">{0} w.</unitPattern>
				<unitPattern count="other">{0} w.</unitPattern>
			</unit>
			<unit type="duration-year">
				<unitPattern count="one">{0} j.</unitPattern>
				<unitPattern count="other">{0} j.</unitPattern>
			</unit>
			<unit type="length-centimeter">
				<unitPattern count="one">{0} cm</unitPattern>
				<unitPattern count="other">{0} cm</unitPattern>
			</unit>
			<unit type="length-foot">
				<unitPattern count="one">{0} vt.</unitPattern>
				<unitPattern count="other">{0} vt.</unitPattern>
			</unit>
			<unit type="length-inch">
				<unitPattern count="one">{0} duim</unitPattern>
				<unitPattern count="other">{0} duim</unitPattern>
			</unit>
			<unit type="length-kilometer">
				<unitPattern count="one">{0} km</unitPattern>
				<unitPattern count="other">{0} km</unitPattern>
			</unit>
			<unit type="length-light-year">
				<unitPattern count="one">{0} lj</unitPattern>
				<unitPattern count="other">{0} lj</unitPattern>
			</unit>
			<unit type="length-meter">
				<unitPattern count="one">{0} m</unitPattern>
				<unitPattern count="other">{0} m</unitPattern>
			</unit>
			<unit type="length-mile">
				<unitPattern count="one">{0} myl</unitPattern>
				<unitPattern count="other">{0} myl</unitPattern>
			</unit>
			<unit type="length-millimeter">
				<unitPattern count="one">{0} mm</unitPattern>
				<unitPattern count="other">{0} mm</unitPattern>
			</unit>
			<unit type="length-picometer">
				<unitPattern count="one">{0} pm</unitPattern>
				<unitPattern count="other">{0} pm</unitPattern>
			</unit>
			<unit type="length-yard">
				<unitPattern count="one">{0} jt.</unitPattern>
				<unitPattern count="other">{0} jt.</unitPattern>
			</unit>
			<unit type="mass-gram">
				<unitPattern count="one">{0} g</unitPattern>
				<unitPattern count="other">{0} g</unitPattern>
			</unit>
			<unit type="mass-kilogram">
				<unitPattern count="one">{0} kg</unitPattern>
				<unitPattern count="other">{0} kg</unitPattern>
			</unit>
			<unit type="mass-ounce">
				<unitPattern count="one">{0} oz.</unitPattern>
				<unitPattern count="other">{0} oz.</unitPattern>
			</unit>
			<unit type="mass-pound">
				<unitPattern count="one">{0} lb.</unitPattern>
				<unitPattern count="other">{0} lb.</unitPattern>
			</unit>
			<unit type="power-horsepower">
				<unitPattern count="one">{0} pk.</unitPattern>
				<unitPattern count="other">{0} pk.</unitPattern>
			</unit>
			<unit type="power-kilowatt">
				<unitPattern count="one">{0} kW</unitPattern>
				<unitPattern count="other">{0} kW</unitPattern>
			</unit>
			<unit type="power-watt">
				<unitPattern count="one">{0} W</unitPattern>
				<unitPattern count="other">{0} W</unitPattern>
			</unit>
			<unit type="pressure-hectopascal">
				<unitPattern count="one">{0} hPa</unitPattern>
				<unitPattern count="other">{0} hPa</unitPattern>
			</unit>
			<unit type="pressure-inch-hg">
				<unitPattern count="one">{0} dm.Hg</unitPattern>
				<unitPattern count="other">{0} dm.Hg</unitPattern>
			</unit>
			<unit type="pressure-millibar">
				<unitPattern count="one">{0} mbar</unitPattern>
				<unitPattern count="other">{0} mbar</unitPattern>
			</unit>
			<unit type="speed-kilometer-per-hour">
				<unitPattern count="one">{0} km/h</unitPattern>
				<unitPattern count="other">{0} km/h</unitPattern>
			</unit>
			<unit type="speed-meter-per-second">
				<unitPattern count="one">{0} m/s</unitPattern>
				<unitPattern count="other">{0} m/s</unitPattern>
			</unit>
			<unit type="speed-mile-per-hour">
				<unitPattern count="one">{0} myl/h</unitPattern>
				<unitPattern count="other">{0} myl/h</unitPattern>
			</unit>
			<unit type="temperature-celsius">
				<unitPattern count="one">{0}°C</unitPattern>
				<unitPattern count="other">{0}°C</unitPattern>
			</unit>
			<unit type="temperature-fahrenheit">
				<unitPattern count="one">{0}°F</unitPattern>
				<unitPattern count="other">{0}°F</unitPattern>
			</unit>
			<unit type="volume-cubic-kilometer">
				<unitPattern count="one">{0} km³</unitPattern>
				<unitPattern count="other">{0} km³</unitPattern>
			</unit>
			<unit type="volume-cubic-mile">
				<unitPattern count="one">{0} myl³</unitPattern>
				<unitPattern count="other">{0} myl³</unitPattern>
			</unit>
			<unit type="volume-liter">
				<unitPattern count="one">{0} l</unitPattern>
				<unitPattern count="other">{0} l</unitPattern>
			</unit>
		</unitLength>
		<unitLength type="narrow">
			<compoundUnit type="per">
				<compoundUnitPattern>{0}/{1}</compoundUnitPattern>
			</compoundUnit>
			<unit type="acceleration-g-force">
				<unitPattern count="one">{0}G</unitPattern>
				<unitPattern count="other">{0}G</unitPattern>
			</unit>
			<unit type="angle-arc-minute">
				<unitPattern count="one">{0}′</unitPattern>
				<unitPattern count="other">{0}′</unitPattern>
			</unit>
			<unit type="angle-arc-second">
				<unitPattern count="one">{0}″</unitPattern>
				<unitPattern count="other">{0}″</unitPattern>
			</unit>
			<unit type="angle-degree">
				<unitPattern count="one">{0}°</unitPattern>
				<unitPattern count="other">{0}°</unitPattern>
			</unit>
			<unit type="area-acre">
				<unitPattern count="one">{0} ak</unitPattern>
				<unitPattern count="other">{0} ak</unitPattern>
			</unit>
			<unit type="area-hectare">
				<unitPattern count="one">{0}ha</unitPattern>
				<unitPattern count="other">{0}ha</unitPattern>
			</unit>
			<unit type="area-square-foot">
				<unitPattern count="one">{0} vt.²</unitPattern>
				<unitPattern count="other">{0} vt.²</unitPattern>
			</unit>
			<unit type="area-square-kilometer">
				<unitPattern count="one">{0} km²</unitPattern>
				<unitPattern count="other">{0} km²</unitPattern>
			</unit>
			<unit type="area-square-meter">
				<unitPattern count="one">{0} m²</unitPattern>
				<unitPattern count="other">{0} m²</unitPattern>
			</unit>
			<unit type="area-square-mile">
				<unitPattern count="one">{0}myl²</unitPattern>
				<unitPattern count="other">{0}myl²</unitPattern>
			</unit>
			<unit type="duration-day">
				<unitPattern count="one">{0}d.</unitPattern>
				<unitPattern count="other">{0}d.</unitPattern>
			</unit>
			<unit type="duration-hour">
				<unitPattern count="one">{0}u.</unitPattern>
				<unitPattern count="other">{0}u.</unitPattern>
			</unit>
			<unit type="duration-millisecond">
				<unitPattern count="one">{0} ms</unitPattern>
				<unitPattern count="other">{0} ms</unitPattern>
			</unit>
			<unit type="duration-minute">
				<unitPattern count="one">{0}min.</unitPattern>
				<unitPattern count="other">{0}min.</unitPattern>
			</unit>
			<unit type="duration-month">
				<unitPattern count="one">{0}md.</unitPattern>
				<unitPattern count="other">{0}md.</unitPattern>
			</unit>
			<unit type="duration-second">
				<unitPattern count="one">{0}sek.</unitPattern>
				<unitPattern count="other">{0}sek.</unitPattern>
			</unit>
			<unit type="duration-week">
				<unitPattern count="one">{0}w.</unitPattern>
				<unitPattern count="other">{0}w.</unitPattern>
			</unit>
			<unit type="length-centimeter">
				<unitPattern count="one">{0}cm</unitPattern>
				<unitPattern count="other">{0}cm</unitPattern>
			</unit>
			<unit type="length-foot">
				<unitPattern count="one">{0} vt.</unitPattern>
				<unitPattern count="other">{0} vt.</unitPattern>
			</unit>
			<unit type="length-inch">
				<unitPattern count="one">{0} duim</unitPattern>
				<unitPattern count="other">{0} duim</unitPattern>
			</unit>
			<unit type="length-kilometer">
				<unitPattern count="one">{0}km</unitPattern>
				<unitPattern count="other">{0}km</unitPattern>
			</unit>
			<unit type="length-light-year">
				<unitPattern count="one">{0} lj</unitPattern>
				<unitPattern count="other">{0} lj</unitPattern>
			</unit>
			<unit type="length-meter">
				<unitPattern count="one">{0}m</unitPattern>
				<unitPattern count="other">{0}m</unitPattern>
			</unit>
			<unit type="length-mile">
				<unitPattern count="one">{0} myl</unitPattern>
				<unitPattern count="other">{0} myl</unitPattern>
			</unit>
			<unit type="length-millimeter">
				<unitPattern count="one">{0}mm</unitPattern>
				<unitPattern count="other">{0}mm</unitPattern>
			</unit>
			<unit type="length-picometer">
				<unitPattern count="one">{0}pm</unitPattern>
				<unitPattern count="other">{0}pm</unitPattern>
			</unit>
			<unit type="length-yard">
				<unitPattern count="one">{0} jt.</unitPattern>
				<unitPattern count="other">{0} jt.</unitPattern>
			</unit>
			<unit type="mass-gram">
				<unitPattern count="one">{0}g</unitPattern>
				<unitPattern count="other">{0}g</unitPattern>
			</unit>
			<unit type="mass-kilogram">
				<unitPattern count="one">{0}kg</unitPattern>
				<unitPattern count="other">{0}kg</unitPattern>
			</unit>
			<unit type="mass-ounce">
				<unitPattern count="one">{0} oz.</unitPattern>
				<unitPattern count="other">{0} oz.</unitPattern>
			</unit>
			<unit type="mass-pound">
				<unitPattern count="one">{0} lb.</unitPattern>
				<unitPattern count="other">{0} lb.</unitPattern>
			</unit>
			<unit type="power-horsepower">
				<unitPattern count="one">{0}pk.</unitPattern>
				<unitPattern count="other">{0}pk.</unitPattern>
			</unit>
			<unit type="power-kilowatt">
				<unitPattern count="one">{0}kW</unitPattern>
				<unitPattern count="other">{0}kW</unitPattern>
			</unit>
			<unit type="power-watt">
				<unitPattern count="one">{0}W</unitPattern>
				<unitPattern count="other">{0}W</unitPattern>
			</unit>
			<unit type="pressure-hectopascal">
				<unitPattern count="one">{0}hPa</unitPattern>
				<unitPattern count="other">{0}hPa</unitPattern>
			</unit>
			<unit type="pressure-inch-hg">
				<unitPattern count="one">{0} dm.Hg</unitPattern>
				<unitPattern count="other">{0} dm.Hg</unitPattern>
			</unit>
			<unit type="pressure-millibar">
				<unitPattern count="one">{0}mbar</unitPattern>
				<unitPattern count="other">{0}mbar</unitPattern>
			</unit>
			<unit type="speed-kilometer-per-hour">
				<unitPattern count="one">{0}km/h</unitPattern>
				<unitPattern count="other">{0}km/h</unitPattern>
			</unit>
			<unit type="speed-meter-per-second">
				<unitPattern count="one">{0}m/s</unitPattern>
				<unitPattern count="other">{0}m/s</unitPattern>
			</unit>
			<unit type="speed-mile-per-hour">
				<unitPattern count="one">{0} myl/h</unitPattern>
				<unitPattern count="other">{0} myl/h</unitPattern>
			</unit>
			<unit type="temperature-celsius">
				<unitPattern count="one">{0}°</unitPattern>
				<unitPattern count="other">{0}°</unitPattern>
			</unit>
			<unit type="temperature-fahrenheit">
				<unitPattern count="one">{0}°F</unitPattern>
				<unitPattern count="other">{0}°F</unitPattern>
			</unit>
			<unit type="volume-cubic-kilometer">
				<unitPattern count="one">{0}km³</unitPattern>
				<unitPattern count="other">{0}km³</unitPattern>
			</unit>
			<unit type="volume-cubic-mile">
				<unitPattern count="one">{0} myl³</unitPattern>
				<unitPattern count="other">{0} myl³</unitPattern>
			</unit>
			<unit type="volume-liter">
				<unitPattern count="one">{0} l</unitPattern>
				<unitPattern count="other">{0} l</unitPattern>
			</unit>
		</unitLength>
		<durationUnit type="hm">
			<durationUnitPattern>h:mm</durationUnitPattern>
		</durationUnit>
		<durationUnit type="hms">
			<durationUnitPattern>h:mm:ss</durationUnitPattern>
		</durationUnit>
		<durationUnit type="ms">
			<durationUnitPattern>m:ss</durationUnitPattern>
		</durationUnit>
	</units>
	<listPatterns>
		<listPattern>
			<listPatternPart type="start">{0}, {1}</listPatternPart>
			<listPatternPart type="middle">{0}, {1}</listPatternPart>
			<listPatternPart type="end">{0} en {1}</listPatternPart>
			<listPatternPart type="2">{0} en {1}</listPatternPart>
		</listPattern>
	</listPatterns>
	<posix>
		<messages>
			<yesstr>ja:j</yesstr>
			<nostr>nee:n</nostr>
		</messages>
	</posix>
</ldml>

