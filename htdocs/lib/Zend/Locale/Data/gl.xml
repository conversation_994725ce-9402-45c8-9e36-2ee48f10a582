<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE ldml SYSTEM "../../common/dtd/ldml.dtd">
<!-- Copyright © 1991-2013 Unicode, Inc.
CLDR data files are interpreted according to the LDML specification (http://unicode.org/reports/tr35/)
For terms of use, see http://www.unicode.org/copyright.html
-->
<ldml>
	<identity>
		<version number="$Revision: 9876 $"/>
		<generation date="$Date: 2014-03-05 23:14:25 -0600 (Wed, 05 Mar 2014) $"/>
		<language type="gl"/>
	</identity>
	<localeDisplayNames>
		<localeDisplayPattern>
			<localePattern>{0} ({1})</localePattern>
			<localeSeparator>{0}, {1}</localeSeparator>
			<localeKeyTypePattern>{0}: {1}</localeKeyTypePattern>
		</localeDisplayPattern>
		<languages>
			<language type="ab">abkhazo</language>
			<language type="ach">acoli</language>
			<language type="af">afrikaans</language>
			<language type="ak">akán</language>
			<language type="am">amárico</language>
			<language type="an">aragonés</language>
			<language type="ar">árabe</language>
			<language type="ar_001">árabe estándar moderno</language>
			<language type="arc">arameo</language>
			<language type="as">assamés</language>
			<language type="ast">asturiano</language>
			<language type="ay">aimará</language>
			<language type="az" draft="contributed">azerbaiano</language>
			<language type="az" alt="short">acerbaixano</language>
			<language type="be">bielorruso</language>
			<language type="bem">bemba</language>
			<language type="bg">búlgaro</language>
			<language type="bn">bengalí</language>
			<language type="bo">tibetano</language>
			<language type="br">bretón</language>
			<language type="bs">bosnio</language>
			<language type="ca">catalán</language>
			<language type="chr">cheroqui</language>
			<language type="ckb">curdo soraní</language>
			<language type="co">corso</language>
			<language type="cs">checo</language>
			<language type="cu">eslavo eclesiástico</language>
			<language type="cy">galés</language>
			<language type="da">dinamarqués</language>
			<language type="de">alemán</language>
			<language type="de_AT">alemán de austria</language>
			<language type="de_CH">alto alemán suízo</language>
			<language type="dv">divehi</language>
			<language type="dz">dzongkha</language>
			<language type="ee">ewé</language>
			<language type="efi">ibibio</language>
			<language type="egy" draft="contributed">exipcio antigo</language>
			<language type="el">grego</language>
			<language type="en">inglés</language>
			<language type="en_AU">inglés australiano</language>
			<language type="en_CA">inglés canadiano</language>
			<language type="en_GB">inglés británico</language>
			<language type="en_GB" alt="short">inglés R.U.</language>
			<language type="en_US">inglés dos Estados Unidos</language>
			<language type="en_US" alt="short">inglés EUA</language>
			<language type="eo">esperanto</language>
			<language type="es">español</language>
			<language type="es_419">español latinoamericano</language>
			<language type="es_ES">castelán</language>
			<language type="es_MX">español de México</language>
			<language type="et">estoniano</language>
			<language type="eu">éuscaro</language>
			<language type="fa">persa</language>
			<language type="fi">finés</language>
			<language type="fil">filipino</language>
			<language type="fj">fixiano</language>
			<language type="fo" draft="contributed">faroés</language>
			<language type="fr">francés</language>
			<language type="fr_CA">francés canadiano</language>
			<language type="fr_CH">francés suízo</language>
			<language type="fy">frisón</language>
			<language type="ga">irlandés</language>
			<language type="gaa">ga</language>
			<language type="gd">gaélico escocés</language>
			<language type="gl">galego</language>
			<language type="gn">guaraní</language>
			<language type="grc">grego antigo</language>
			<language type="gsw">alemán suízo</language>
			<language type="gu" draft="contributed">guxaratiano</language>
			<language type="ha">hausa</language>
			<language type="haw">hawaiano</language>
			<language type="he">hebreo</language>
			<language type="hi">hindi</language>
			<language type="hr">croata</language>
			<language type="ht">haitiano</language>
			<language type="hu">húngaro</language>
			<language type="hy">armenio</language>
			<language type="ia">interlingua</language>
			<language type="id">indonesio</language>
			<language type="ig">ibo</language>
			<language type="is">islandés</language>
			<language type="it">italiano</language>
			<language type="ja">xaponés</language>
			<language type="jv">xavanés</language>
			<language type="ka">xeorxiano</language>
			<language type="kg">kongo</language>
			<language type="kk">casaco</language>
			<language type="km">cambodiano</language>
			<language type="kn">kannada</language>
			<language type="ko">coreano</language>
			<language type="ks">cachemir</language>
			<language type="ku" draft="contributed">kurdo</language>
			<language type="ky">quirguiz</language>
			<language type="la">latín</language>
			<language type="lb">luxemburgués</language>
			<language type="lg">ganda</language>
			<language type="ln">lingala</language>
			<language type="lo">laotiano</language>
			<language type="loz">lozi</language>
			<language type="lt">lituano</language>
			<language type="lua">luba-lulua</language>
			<language type="lv">letón</language>
			<language type="mfe">crioulo mauritano</language>
			<language type="mg">malgaxe</language>
			<language type="mi">maorí</language>
			<language type="mk">macedonio</language>
			<language type="ml">malabar</language>
			<language type="mn">mongol</language>
			<language type="mr">marathi</language>
			<language type="ms">malaio</language>
			<language type="mt">maltés</language>
			<language type="mul">varias linguas</language>
			<language type="my">birmano</language>
			<language type="nb">noruegués bokmal</language>
			<language type="nd">ndebele do norte</language>
			<language type="ne">nepalí</language>
			<language type="nl">holandés</language>
			<language type="nl_BE">flamenco</language>
			<language type="nn">noruegués nynorsk</language>
			<language type="no">noruegués</language>
			<language type="nso">sesotho sa leboa</language>
			<language type="ny">chewa</language>
			<language type="nyn">nyankole</language>
			<language type="oc">occitano</language>
			<language type="om">oromo</language>
			<language type="or">oriya</language>
			<language type="os">osetio</language>
			<language type="pa" draft="contributed">punjabi</language>
			<language type="pl">polaco</language>
			<language type="ps">paxtún</language>
			<language type="pt">portugués</language>
			<language type="pt_BR">portugués brasileiro</language>
			<language type="pt_PT">portugués europeo</language>
			<language type="qu">quechua</language>
			<language type="rm">romanche</language>
			<language type="rn">rundi</language>
			<language type="ro">romanés</language>
			<language type="ru">ruso</language>
			<language type="rw">ruandés</language>
			<language type="sa">sánscrito</language>
			<language type="sd">sindhi</language>
			<language type="se">sami do norte</language>
			<language type="sg">sango</language>
			<language type="sh">serbocroata</language>
			<language type="si">cingalés</language>
			<language type="sk">eslovaco</language>
			<language type="sl">esloveno</language>
			<language type="sm">samoano</language>
			<language type="sn">shona</language>
			<language type="so">somalí</language>
			<language type="sq">albanés</language>
			<language type="sr">serbio</language>
			<language type="ss">swati</language>
			<language type="st">sesoto</language>
			<language type="su">sondanés</language>
			<language type="sv">sueco</language>
			<language type="sw">swahili</language>
			<language type="ta">tamil</language>
			<language type="te">telugu</language>
			<language type="tet">tetún</language>
			<language type="tg">taxico</language>
			<language type="th">tailandés</language>
			<language type="ti">tigriña</language>
			<language type="tk">turcomano</language>
			<language type="tl">tagalo</language>
			<language type="tlh">klingon</language>
			<language type="tn">tswana</language>
			<language type="to">tonganés</language>
			<language type="tpi">tok pisin</language>
			<language type="tr">turco</language>
			<language type="ts">xitsonga</language>
			<language type="tt">tártaro</language>
			<language type="tum">tumbuka</language>
			<language type="tw" draft="unconfirmed">twi</language>
			<language type="ty">tahitiano</language>
			<language type="ug">uigur</language>
			<language type="uk">ucraíno</language>
			<language type="und">lingua descoñecida ou non válida</language>
			<language type="ur">urdú</language>
			<language type="uz">uzbeco</language>
			<language type="ve">venda</language>
			<language type="vi">vietnamita</language>
			<language type="wo">wólof</language>
			<language type="xh">xhosa</language>
			<language type="yi">yiddish</language>
			<language type="yo">ioruba</language>
			<language type="zgh">tamazight de Marrocos estándar</language>
			<language type="zh">chinés</language>
			<language type="zh_Hans">chinés simplificado</language>
			<language type="zh_Hant">chinés tradicional</language>
			<language type="zu">zulú</language>
			<language type="zxx">sen contido lingüístico</language>
		</languages>
		<scripts>
			<script type="Arab">Árabe</script>
			<script type="Arab" alt="variant">Perso-Árabe</script>
			<script type="Armn">Armenio</script>
			<script type="Beng">Bengalí</script>
			<script type="Bopo">Bopomofo</script>
			<script type="Brai">Braille</script>
			<script type="Cans">Silabario aborixe canadiano unificado</script>
			<script type="Cyrl">Cirílico</script>
			<script type="Deva">Devanagari</script>
			<script type="Ethi">Etíope</script>
			<script type="Geor">Xeorxiano</script>
			<script type="Grek">Grego</script>
			<script type="Gujr">Guxarati</script>
			<script type="Guru">Gurmukhi</script>
			<script type="Hang">Hangul</script>
			<script type="Hani">Han</script>
			<script type="Hans">Simplificado</script>
			<script type="Hans" alt="stand-alone">Han simplificado</script>
			<script type="Hant">Tradicional</script>
			<script type="Hant" alt="stand-alone">Han tradicional</script>
			<script type="Hebr">Hebreo</script>
			<script type="Hira">Hiragana</script>
			<script type="Jpan">Xaponés</script>
			<script type="Kana">Katakana</script>
			<script type="Khmr">Camboxano</script>
			<script type="Knda">Kannadés</script>
			<script type="Kore">Coreano</script>
			<script type="Laoo">Laosiano</script>
			<script type="Latn">Latino</script>
			<script type="Mlym">Malabar</script>
			<script type="Mong">Mongol</script>
			<script type="Mymr">Birmania</script>
			<script type="Orya">Oriya</script>
			<script type="Sinh">Cingalés</script>
			<script type="Taml">Támil</script>
			<script type="Telu">Telugú</script>
			<script type="Thaa">Thaana</script>
			<script type="Thai">Tailandés</script>
			<script type="Tibt">Tibetano</script>
			<script type="Zsym">Símbolos</script>
			<script type="Zxxx">Non escrita</script>
			<script type="Zyyy">Común</script>
			<script type="Zzzz">Escritura descoñecida</script>
		</scripts>
		<territories>
			<territory type="001">Mundo</territory>
			<territory type="002">África</territory>
			<territory type="003">Norteamérica</territory>
			<territory type="005">Sudamérica</territory>
			<territory type="009">Oceanía</territory>
			<territory type="011">África Occidental</territory>
			<territory type="013">América Central</territory>
			<territory type="014">África Oriental</territory>
			<territory type="015">África Septentrional</territory>
			<territory type="017">África Central</territory>
			<territory type="018">África Meridional</territory>
			<territory type="019">América</territory>
			<territory type="021">América do Norte</territory>
			<territory type="029">Caribe</territory>
			<territory type="030">Asia Oriental</territory>
			<territory type="034">Sul de Asia</territory>
			<territory type="035">Sureste Asiático</territory>
			<territory type="039">Europa Meridional</territory>
			<territory type="053">Australasia</territory>
			<territory type="054">Melanesia</territory>
			<territory type="057">Rexión da Micronesia</territory>
			<territory type="061">Polinesia</territory>
			<territory type="142">Asia</territory>
			<territory type="143">Asia Central</territory>
			<territory type="145">Asia Occidental</territory>
			<territory type="150">Europa</territory>
			<territory type="151">Europa do Leste</territory>
			<territory type="154">Europa Septentrional</territory>
			<territory type="155">Europa Occidental</territory>
			<territory type="419">América Latina</territory>
			<territory type="AC">Illa de Ascensión</territory>
			<territory type="AD">Andorra</territory>
			<territory type="AE">Emiratos Árabes Unidos</territory>
			<territory type="AF">Afganistán</territory>
			<territory type="AG">Antiga e Barbuda</territory>
			<territory type="AI">Anguila</territory>
			<territory type="AL">Albania</territory>
			<territory type="AM">Armenia</territory>
			<territory type="AN" draft="contributed">Antillas Holandesas</territory>
			<territory type="AO">Angola</territory>
			<territory type="AQ">Antártida</territory>
			<territory type="AR">Arxentina</territory>
			<territory type="AS">Samoa Americana</territory>
			<territory type="AT">Austria</territory>
			<territory type="AU">Australia</territory>
			<territory type="AW">Aruba</territory>
			<territory type="AX">Illas Aland</territory>
			<territory type="AZ">Acerbaixán</territory>
			<territory type="BA">Bosnia e Hercegovina</territory>
			<territory type="BB">Barbados</territory>
			<territory type="BD">Bangladesh</territory>
			<territory type="BE">Bélxica</territory>
			<territory type="BF">Burkina Faso</territory>
			<territory type="BG">Bulgaria</territory>
			<territory type="BH">Bahrein</territory>
			<territory type="BI">Burundi</territory>
			<territory type="BJ">Benin</territory>
			<territory type="BL">San Bartolomé</territory>
			<territory type="BM">Bermudas</territory>
			<territory type="BN">Brunei</territory>
			<territory type="BO">Bolivia</territory>
			<territory type="BQ">Caribe neerlandés</territory>
			<territory type="BR">Brasil</territory>
			<territory type="BS">Bahamas</territory>
			<territory type="BT">Bután</territory>
			<territory type="BV">Illa Bouvet</territory>
			<territory type="BW">Botsuana</territory>
			<territory type="BY">Bielorrusia</territory>
			<territory type="BZ">Belice</territory>
			<territory type="CA">Canadá</territory>
			<territory type="CC">Illas Cocos (Keeling)</territory>
			<territory type="CD">República Democrática do Congo</territory>
			<territory type="CD" alt="variant">Congo (RDC)</territory>
			<territory type="CF">República Africana Central</territory>
			<territory type="CG">Congo</territory>
			<territory type="CG" alt="variant">Congo (RC)</territory>
			<territory type="CH">Suíza</territory>
			<territory type="CI">Costa de Marfil</territory>
			<territory type="CI" alt="variant">Costa do Marfil</territory>
			<territory type="CK">Illas Cook</territory>
			<territory type="CL">Chile</territory>
			<territory type="CM">Camerún</territory>
			<territory type="CN">China</territory>
			<territory type="CO">Colombia</territory>
			<territory type="CP">Illa Clipperton</territory>
			<territory type="CR">Costa Rica</territory>
			<territory type="CU">Cuba</territory>
			<territory type="CV">Cabo Verde</territory>
			<territory type="CW">Curaçao</territory>
			<territory type="CX">Illa Christmas</territory>
			<territory type="CY">Chipre</territory>
			<territory type="CZ">República Checa</territory>
			<territory type="DE">Alemaña</territory>
			<territory type="DG">Diego García</territory>
			<territory type="DJ">Xibuti</territory>
			<territory type="DK">Dinamarca</territory>
			<territory type="DM">Dominica</territory>
			<territory type="DO">República Dominicana</territory>
			<territory type="DZ">Arxelia</territory>
			<territory type="EA">Ceuta e Melilla</territory>
			<territory type="EC">Ecuador</territory>
			<territory type="EE">Estonia</territory>
			<territory type="EG">Exipto</territory>
			<territory type="EH">Sahara Occidental</territory>
			<territory type="ER">Eritrea</territory>
			<territory type="ES">España</territory>
			<territory type="ET">Etiopía</territory>
			<territory type="EU">Unión Europea</territory>
			<territory type="FI">Finlandia</territory>
			<territory type="FJ">Fixi</territory>
			<territory type="FK">Illas Malvinas</territory>
			<territory type="FK" alt="variant">Illas Malvinas (Falkland)</territory>
			<territory type="FM">Micronesia</territory>
			<territory type="FO">Illas Feroe</territory>
			<territory type="FR">Francia</territory>
			<territory type="GA">Gabón</territory>
			<territory type="GB">Reino Unido</territory>
			<territory type="GD">Granada</territory>
			<territory type="GE">Xeorxia</territory>
			<territory type="GF">Güiana Francesa</territory>
			<territory type="GG">Guernsey</territory>
			<territory type="GH">Gana</territory>
			<territory type="GI">Xibraltar</territory>
			<territory type="GL">Grenlandia</territory>
			<territory type="GM">Gambia</territory>
			<territory type="GN">Guinea</territory>
			<territory type="GP">Guadalupe</territory>
			<territory type="GQ">Guinea Ecuatorial</territory>
			<territory type="GR">Grecia</territory>
			<territory type="GS">Xeorxia do Sur e Illas Sandwich</territory>
			<territory type="GT">Guatemala</territory>
			<territory type="GU">Guam</territory>
			<territory type="GW">Guinea-Bissau</territory>
			<territory type="GY">Güiana</territory>
			<territory type="HK">Hong Kong RAE de China</territory>
			<territory type="HK" alt="short">Hong Kong</territory>
			<territory type="HM">Illa Heard e Illas McDonald</territory>
			<territory type="HN">Honduras</territory>
			<territory type="HR">Croacia</territory>
			<territory type="HT">Haití</territory>
			<territory type="HU">Hungría</territory>
			<territory type="IC">Illas Canarias</territory>
			<territory type="ID">Indonesia</territory>
			<territory type="IE">Irlanda</territory>
			<territory type="IL">Israel</territory>
			<territory type="IM">Illa de Man</territory>
			<territory type="IN">India</territory>
			<territory type="IO">Territorio Británico do Océano Índico</territory>
			<territory type="IQ">Iraq</territory>
			<territory type="IR">Irán</territory>
			<territory type="IS">Islandia</territory>
			<territory type="IT">Italia</territory>
			<territory type="JE">Jersey</territory>
			<territory type="JM">Xamaica</territory>
			<territory type="JO">Xordania</territory>
			<territory type="JP">Xapón</territory>
			<territory type="KE">Quenia</territory>
			<territory type="KG">Quirguicistán</territory>
			<territory type="KH">Cambodia</territory>
			<territory type="KI">Kiribati</territory>
			<territory type="KM">Comores</territory>
			<territory type="KN">San Cristovo e Nevis</territory>
			<territory type="KP">Corea do Norte</territory>
			<territory type="KR">Corea do Sur</territory>
			<territory type="KW">Kuwait</territory>
			<territory type="KY">Illas Caimán</territory>
			<territory type="KZ">Kazakhstan</territory>
			<territory type="LA">Laos</territory>
			<territory type="LB">Líbano</territory>
			<territory type="LC">Santa Lucía</territory>
			<territory type="LI">Liechtenstein</territory>
			<territory type="LK">Sri Lanka</territory>
			<territory type="LR">Liberia</territory>
			<territory type="LS">Lesotho</territory>
			<territory type="LT">Lituania</territory>
			<territory type="LU">Luxemburgo</territory>
			<territory type="LV">Letonia</territory>
			<territory type="LY">Libia</territory>
			<territory type="MA">Marrocos</territory>
			<territory type="MC">Mónaco</territory>
			<territory type="MD">Moldova</territory>
			<territory type="ME">Montenegro</territory>
			<territory type="MF">San Martiño</territory>
			<territory type="MG">Madagascar</territory>
			<territory type="MH">Illas Marshall</territory>
			<territory type="MK">Macedonia</territory>
			<territory type="MK" alt="variant">Macedonia (ARIM)</territory>
			<territory type="ML">Mali</territory>
			<territory type="MM">Myanmar (Birmania)</territory>
			<territory type="MN">Mongolia</territory>
			<territory type="MO">Macau RAE de China</territory>
			<territory type="MO" alt="short">Macau</territory>
			<territory type="MP">Illas Marianas do norte</territory>
			<territory type="MQ">Martinica</territory>
			<territory type="MR">Mauritania</territory>
			<territory type="MS">Montserrat</territory>
			<territory type="MT">Malta</territory>
			<territory type="MU">Mauricio</territory>
			<territory type="MV">Maldivas</territory>
			<territory type="MW">Malaui</territory>
			<territory type="MX">México</territory>
			<territory type="MY">Malaisia</territory>
			<territory type="MZ">Mozambique</territory>
			<territory type="NA">Namibia</territory>
			<territory type="NC">Nova Caledonia</territory>
			<territory type="NE">Níxer</territory>
			<territory type="NF">Illa Norfolk</territory>
			<territory type="NG">Nixeria</territory>
			<territory type="NI">Nicaragua</territory>
			<territory type="NL">Países Baixos</territory>
			<territory type="NO">Noruega</territory>
			<territory type="NP">Nepal</territory>
			<territory type="NR">Nauru</territory>
			<territory type="NU">Niue</territory>
			<territory type="NZ">Nova Celandia</territory>
			<territory type="OM">Omán</territory>
			<territory type="PA">Panamá</territory>
			<territory type="PE">Perú</territory>
			<territory type="PF">Polinesia Francesa</territory>
			<territory type="PG">Papúa Nova Guinea</territory>
			<territory type="PH">Filipinas</territory>
			<territory type="PK">Paquistán</territory>
			<territory type="PL">Polonia</territory>
			<territory type="PM">San Pedro e Miguelón</territory>
			<territory type="PN">Illas Pitcairn</territory>
			<territory type="PR">Porto Rico</territory>
			<territory type="PS">Territorios palestinos</territory>
			<territory type="PS" alt="short">Palestina</territory>
			<territory type="PT">Portugal</territory>
			<territory type="PW">Palau</territory>
			<territory type="PY">Paraguai</territory>
			<territory type="QA">Qatar</territory>
			<territory type="QO">Oceanía Distante</territory>
			<territory type="RE">Reunión</territory>
			<territory type="RO">Romanía</territory>
			<territory type="RS">Serbia</territory>
			<territory type="RU">Rusia</territory>
			<territory type="RW">Ruanda</territory>
			<territory type="SA">Arabia Saudita</territory>
			<territory type="SB">Illas Salomón</territory>
			<territory type="SC">Seixeles</territory>
			<territory type="SD">Sudán</territory>
			<territory type="SE">Suecia</territory>
			<territory type="SG">Singapur</territory>
			<territory type="SH">Santa Helena</territory>
			<territory type="SI">Eslovenia</territory>
			<territory type="SJ">Svalbard e Jan Mayen</territory>
			<territory type="SK">Eslovaquia</territory>
			<territory type="SL">Serra Leoa</territory>
			<territory type="SM">San Marino</territory>
			<territory type="SN">Senegal</territory>
			<territory type="SO">Somalia</territory>
			<territory type="SR">Surinam</territory>
			<territory type="SS">Sudán do sur</territory>
			<territory type="ST">San Tomé e Príncipe</territory>
			<territory type="SV">El Salvador</territory>
			<territory type="SX">Sint Maarten</territory>
			<territory type="SY">Siria</territory>
			<territory type="SZ">Suacilandia</territory>
			<territory type="TA">Tristán da Cunha</territory>
			<territory type="TC">Illas Turks e Caicos</territory>
			<territory type="TD">Chad</territory>
			<territory type="TF" draft="contributed">Territorios Franceses do Sul</territory>
			<territory type="TG">Togo</territory>
			<territory type="TH">Tailandia</territory>
			<territory type="TJ">Taxiquistán</territory>
			<territory type="TK">Tokelau</territory>
			<territory type="TL">Timor Leste</territory>
			<territory type="TM">Turkmenistán</territory>
			<territory type="TN">Tunisia</territory>
			<territory type="TO">Tonga</territory>
			<territory type="TR">Turquía</territory>
			<territory type="TT">Trindade e Tobago</territory>
			<territory type="TV">Tuvalu</territory>
			<territory type="TW">Taiwán</territory>
			<territory type="TZ">Tanzania</territory>
			<territory type="UA">Ucraína</territory>
			<territory type="UG">Uganda</territory>
			<territory type="UM">Illas Menores Distantes dos EUA.</territory>
			<territory type="US">Estados Unidos de América</territory>
			<territory type="UY">Uruguai</territory>
			<territory type="UZ">Uzbekistán</territory>
			<territory type="VA">Cidade do Vaticano</territory>
			<territory type="VC">San Vicente e Granadinas</territory>
			<territory type="VE">Venezuela</territory>
			<territory type="VG">Illas Virxes Británicas</territory>
			<territory type="VI">Illas Virxes Estadounidenses</territory>
			<territory type="VN">Vietnam</territory>
			<territory type="VU">Vanuatu</territory>
			<territory type="WF">Wallis e Futuna</territory>
			<territory type="WS">Samoa</territory>
			<territory type="XK">Kosovo</territory>
			<territory type="YE">Iemen</territory>
			<territory type="YT">Mayotte</territory>
			<territory type="ZA">Sudáfrica</territory>
			<territory type="ZM">Zambia</territory>
			<territory type="ZW">Cimbabue</territory>
			<territory type="ZZ">Rexión descoñecida</territory>
		</territories>
		<keys>
			<key type="calendar">Calendario</key>
			<key type="colAlternate">Ignorar clasificación de símbolos</key>
			<key type="colBackwards">Clasificación de acentos invertida</key>
			<key type="colCaseFirst">Orde de maiúsculas/minúsculas</key>
			<key type="colCaseLevel">Clasificación que distingue entre maiúsculas e minúsculas</key>
			<key type="colHiraganaQuaternary">Clasificación Kana</key>
			<key type="collation">Orde de clasificación</key>
			<key type="colNormalization">Clasificación normalizada</key>
			<key type="colNumeric">Clasificación numérica</key>
			<key type="colStrength">Forza de clasificación</key>
			<key type="currency">Moeda</key>
			<key type="numbers">Números</key>
			<key type="timezone">Fuso horario</key>
			<key type="va">Variante local</key>
			<key type="variableTop">Clasificar como símbolos</key>
			<key type="x">Uso privado</key>
		</keys>
		<types>
			<type type="arab" key="numbers">Díxitos do árabe oriental</type>
			<type type="arabext" key="numbers">Díxitos arábicos orientais</type>
			<type type="armn" key="numbers">Números armenios</type>
			<type type="armnlow" key="numbers">Números armenios en minúscula</type>
			<type type="beng" key="numbers">Díxitos bengalís</type>
			<type type="big5han" key="collation">Orde de clasificación chinesa tradicional - Big5</type>
			<type type="buddhist" key="calendar">Calendario budista</type>
			<type type="chinese" key="calendar">Calendario chinés</type>
			<type type="coptic" key="calendar">Calendario cóptico</type>
			<type type="dangi" key="calendar">Calendario dangi</type>
			<type type="deva" key="numbers">Díxitos devanagari</type>
			<type type="dictionary" key="collation">Criterio de ordenación do dicionario</type>
			<type type="ducet" key="collation">Criterio de ordenación Unicode predeterminado</type>
			<type type="ethi" key="numbers">Números etíopes</type>
			<type type="ethiopic" key="calendar">Calendario etíope</type>
			<type type="ethiopic-amete-alem" key="calendar">Calendario Amete Alem etíope</type>
			<type type="finance" key="numbers">Números financeiros</type>
			<type type="fullwide" key="numbers">Díxitos de ancho completo</type>
			<type type="gb2312han" key="collation">orde de clasifcación chinesa simplificada - GB2312</type>
			<type type="geor" key="numbers">Números xeorxianos</type>
			<type type="gregorian" key="calendar">Calendario gregoriano</type>
			<type type="grek" key="numbers">Números gregos</type>
			<type type="greklow" key="numbers">Números gregos en minúscula</type>
			<type type="gujr" key="numbers">Díxitos guxarati</type>
			<type type="guru" key="numbers">Díxitos do gurmukhi</type>
			<type type="hanidec" key="numbers">Números decimais chineses</type>
			<type type="hans" key="numbers">Números chineses simplificados</type>
			<type type="hansfin" key="numbers">Números financeiros chineses simplificados</type>
			<type type="hant" key="numbers">Números do chinés tradicional</type>
			<type type="hantfin" key="numbers">Números financeiros do chinés tradicional</type>
			<type type="hebr" key="numbers">Números hebreos</type>
			<type type="hebrew" key="calendar">Calendario hebreo</type>
			<type type="identical" key="colStrength">Clasificar todo</type>
			<type type="indian" key="calendar">Calendario nacional indio</type>
			<type type="islamic" key="calendar">Calendario islámico</type>
			<type type="islamic-civil" key="calendar">Calendario islámico (civil, tabular)</type>
			<type type="islamic-rgsa" key="calendar">Calendario islámico (Arabia Saudita,</type>
			<type type="japanese" key="calendar">Calendario xaponés</type>
			<type type="jpan" key="numbers">Números xaponeses</type>
			<type type="jpanfin" key="numbers">Números financeiros xaponeses</type>
			<type type="khmr" key="numbers">Díxitos do camboxano</type>
			<type type="knda" key="numbers">Díxitos do kannadés</type>
			<type type="laoo" key="numbers">Díxitos laosianos</type>
			<type type="latn" key="numbers">Díxitos occidentais</type>
			<type type="lower" key="colCaseFirst">Clasificar primeiro as minúsculas</type>
			<type type="mlym" key="numbers">Díxitos malabares</type>
			<type type="mong" key="numbers">Díxitos mongoles</type>
			<type type="mymr" key="numbers">Díxitos birmanos</type>
			<type type="native" key="numbers">Díxitos orixinais</type>
			<type type="no" key="colBackwards">Clasificar acentos con normalidade</type>
			<type type="no" key="colCaseFirst">Clasificar orde de maiúsculas e minúsculas normal</type>
			<type type="no" key="colCaseLevel">Clasificar sen distinguir entre maiúsculas e minúsculas</type>
			<type type="no" key="colHiraganaQuaternary">Clasificar Kana por separado</type>
			<type type="no" key="colNormalization">Clasificar sen normalización</type>
			<type type="no" key="colNumeric">Clasificar díxitos individualmente</type>
			<type type="non-ignorable" key="colAlternate">Clasificar símbolos</type>
			<type type="orya" key="numbers">Díxitos oriya</type>
			<type type="persian" key="calendar">Calendario persa</type>
			<type type="phonebook" key="collation">orde de clasificación da guía telefónica</type>
			<type type="phonetic" key="collation">Orde de clasificación fonética</type>
			<type type="pinyin" key="collation">Orde de clasificación pinyin</type>
			<type type="primary" key="colStrength">Clasificar só letras de base</type>
			<type type="quaternary" key="colStrength">Clasificar acentos/maiúsculas e minúsculas/ancho/kana</type>
			<type type="reformed" key="collation">Criterio de ordenación reformado</type>
			<type type="roc" key="calendar">Calendario Minguo</type>
			<type type="roman" key="numbers">Números romanos</type>
			<type type="romanlow" key="numbers">Números romanos en minúsculas</type>
			<type type="search" key="collation">Busca de uso xeral</type>
			<type type="searchjl" key="collation">Clasificar por consonante inicial hangul</type>
			<type type="secondary" key="colStrength">Clasificar acentos</type>
			<type type="shifted" key="colAlternate">Clasificar ignorando símbolos</type>
			<type type="standard" key="collation">Criterio de ordenación estándar</type>
			<type type="stroke" key="collation">Orde de clasificación polo número de trazos</type>
			<type type="taml" key="numbers">Números támil</type>
			<type type="tamldec" key="numbers">Díxitos do támil</type>
			<type type="telu" key="numbers">Díxitos do telugú</type>
			<type type="tertiary" key="colStrength">Clasificar acentos/maiúsculas e minúsculas/ancho</type>
			<type type="thai" key="numbers">Díxitos tailandeses</type>
			<type type="tibt" key="numbers">Díxitos tibetanos</type>
			<type type="traditional" key="collation">Orde de clasificación tradicional</type>
			<type type="traditional" key="numbers">Numeros tradicionais</type>
			<type type="unihan" key="collation">Criterio de ordenación radical-trazo</type>
			<type type="upper" key="colCaseFirst">Clasificar primeiro as maiúsculas</type>
			<type type="vaii" key="numbers">Díxitos Vai</type>
			<type type="yes" key="colBackwards">Clasificar acentos invertidos</type>
			<type type="yes" key="colCaseLevel">Clasificar distinguindo entre maiúsculas e minúsculas</type>
			<type type="yes" key="colHiraganaQuaternary">Clasificar Kana de modo diferente</type>
			<type type="yes" key="colNormalization">Clasificar Unicode normalizado</type>
			<type type="yes" key="colNumeric">Clasificar díxitos numericamente</type>
		</types>
		<transformNames>
			<transformName type="BGN">BGN</transformName>
			<transformName type="Numeric">Numérico</transformName>
			<transformName type="Tone">Ton</transformName>
			<transformName type="UNGEGN">UNGEGN</transformName>
			<transformName type="x-Accents">Acentos</transformName>
			<transformName type="x-Fullwidth">Ancho completo</transformName>
			<transformName type="x-Halfwidth">Ancho medio</transformName>
			<transformName type="x-Jamo">Jamo</transformName>
			<transformName type="x-Pinyin">Pinyin</transformName>
			<transformName type="x-Publishing">Publicación</transformName>
		</transformNames>
		<measurementSystemNames>
			<measurementSystemName type="metric">métrico decimal</measurementSystemName>
			<measurementSystemName type="UK">británico</measurementSystemName>
			<measurementSystemName type="US">americano</measurementSystemName>
		</measurementSystemNames>
		<codePatterns>
			<codePattern type="language">Idioma: {0}</codePattern>
			<codePattern type="script">Alfabeto: {0}</codePattern>
			<codePattern type="territory">Rexión: {0}</codePattern>
		</codePatterns>
	</localeDisplayNames>
	<characters>
		<exemplarCharacters>[a á b c d e é f g h i í j k l m n ñ o ó p q r s t u ú ü v w x y z]</exemplarCharacters>
		<exemplarCharacters type="auxiliary" draft="contributed">[ª à â ä ã ç è ê ë ì î ï º ò ô ö õ ù û]</exemplarCharacters>
		<exemplarCharacters type="index">[A B C D E F G H I J K L M N Ñ O P Q R S T U V W X Y Z]</exemplarCharacters>
		<exemplarCharacters type="punctuation">[\- ‐ – — , ; \: ! ? . … ' ‘ ’ &quot; “ ” ( ) \[ \] § @ * / \&amp; # † ‡ ′ ″]</exemplarCharacters>
		<ellipsis type="final">{0}…</ellipsis>
		<ellipsis type="initial">…{0}</ellipsis>
		<ellipsis type="medial">{0}…{1}</ellipsis>
		<moreInformation>?</moreInformation>
	</characters>
	<delimiters>
		<quotationStart draft="contributed">“</quotationStart>
		<quotationEnd draft="contributed">”</quotationEnd>
		<alternateQuotationStart draft="contributed">‘</alternateQuotationStart>
		<alternateQuotationEnd draft="contributed">’</alternateQuotationEnd>
	</delimiters>
	<dates>
		<calendars>
			<calendar type="generic">
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>EEEE dd MMMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>dd MMMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>d MMM, y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>dd/MM/yy GGGGG</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<dateTimeFormats>
					<dateTimeFormatLength type="full">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="long">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="medium">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="short">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<availableFormats>
						<dateFormatItem id="d">d</dateFormatItem>
						<dateFormatItem id="Ed">d E</dateFormatItem>
						<dateFormatItem id="Gy">G y</dateFormatItem>
						<dateFormatItem id="GyMMM">G y MMM</dateFormatItem>
						<dateFormatItem id="GyMMMd">G y MMM d</dateFormatItem>
						<dateFormatItem id="GyMMMEd">G y MMM d, E</dateFormatItem>
						<dateFormatItem id="h">h a</dateFormatItem>
						<dateFormatItem id="H">HH</dateFormatItem>
						<dateFormatItem id="hm">h:mm a</dateFormatItem>
						<dateFormatItem id="Hm" draft="contributed">HH:mm</dateFormatItem>
						<dateFormatItem id="hms">h:mm:ss a</dateFormatItem>
						<dateFormatItem id="Hms">HH:mm:ss</dateFormatItem>
						<dateFormatItem id="M">L</dateFormatItem>
						<dateFormatItem id="Md">d/M</dateFormatItem>
						<dateFormatItem id="MEd">E, d/M</dateFormatItem>
						<dateFormatItem id="MMdd" draft="contributed">dd/MM</dateFormatItem>
						<dateFormatItem id="MMM">LLL</dateFormatItem>
						<dateFormatItem id="MMMd">d MMM</dateFormatItem>
						<dateFormatItem id="MMMEd">E d MMM</dateFormatItem>
						<dateFormatItem id="MMMMd">d MMMM</dateFormatItem>
						<dateFormatItem id="MMMMEd">E d MMMM</dateFormatItem>
						<dateFormatItem id="ms" draft="contributed">mm:ss</dateFormatItem>
						<dateFormatItem id="y">G y</dateFormatItem>
						<dateFormatItem id="yM">M-y</dateFormatItem>
						<dateFormatItem id="yMd">d/M/y</dateFormatItem>
						<dateFormatItem id="yMEd">E, d/M/y</dateFormatItem>
						<dateFormatItem id="yMM" draft="contributed">MM/y</dateFormatItem>
						<dateFormatItem id="yMMM">MMM y</dateFormatItem>
						<dateFormatItem id="yMMMd">d MMM, y</dateFormatItem>
						<dateFormatItem id="yMMMEd">E, d MMM, y</dateFormatItem>
						<dateFormatItem id="yMMMM">MMMM y</dateFormatItem>
						<dateFormatItem id="yQQQ" draft="contributed">QQQ y</dateFormatItem>
						<dateFormatItem id="yQQQQ">QQQQ y</dateFormatItem>
						<dateFormatItem id="yyyy">G y</dateFormatItem>
						<dateFormatItem id="yyyyM">GGGGG M/y</dateFormatItem>
						<dateFormatItem id="yyyyMd">GGGGG d/M/y</dateFormatItem>
						<dateFormatItem id="yyyyMEd">GGGGG E, d/M/y</dateFormatItem>
						<dateFormatItem id="yyyyMMM">G MMM y</dateFormatItem>
						<dateFormatItem id="yyyyMMMd">G d, MMM y</dateFormatItem>
						<dateFormatItem id="yyyyMMMEd">G E, d MMM y</dateFormatItem>
						<dateFormatItem id="yyyyQQQ">G QQQ y</dateFormatItem>
						<dateFormatItem id="yyyyQQQQ">G y QQQQ</dateFormatItem>
					</availableFormats>
					<intervalFormats>
						<intervalFormatFallback>{0} – {1}</intervalFormatFallback>
						<intervalFormatItem id="d">
							<greatestDifference id="d">d–d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="h">
							<greatestDifference id="a">h a – h a</greatestDifference>
							<greatestDifference id="h">h–h a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="H">
							<greatestDifference id="H">HH–HH</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hm">
							<greatestDifference id="a">h:mm a – h:mm a</greatestDifference>
							<greatestDifference id="h">h:mm–h:mm a</greatestDifference>
							<greatestDifference id="m">h:mm–h:mm a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hm">
							<greatestDifference id="H">HH:mm–HH:mm</greatestDifference>
							<greatestDifference id="m">HH:mm–HH:mm</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hmv">
							<greatestDifference id="a">h:mm a – h:mm a v</greatestDifference>
							<greatestDifference id="h">h:mm–h:mm a v</greatestDifference>
							<greatestDifference id="m">h:mm–h:mm a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hmv">
							<greatestDifference id="H">HH:mm–HH:mm v</greatestDifference>
							<greatestDifference id="m">HH:mm–HH:mm v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hv">
							<greatestDifference id="a">h a – h a v</greatestDifference>
							<greatestDifference id="h">h–h a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hv">
							<greatestDifference id="H">HH–HH v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="M">
							<greatestDifference id="M">M–M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Md">
							<greatestDifference id="d">d/M – d/M</greatestDifference>
							<greatestDifference id="M">d/M – d/M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MEd">
							<greatestDifference id="d">E, d/M – E, d/M</greatestDifference>
							<greatestDifference id="M">E, d/M – E, d/M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMM">
							<greatestDifference id="M">MMM–MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMd">
							<greatestDifference id="d">d–d MMM</greatestDifference>
							<greatestDifference id="M">d MMM – d MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMEd">
							<greatestDifference id="d">E, d MMM – E, d MMM</greatestDifference>
							<greatestDifference id="M">E, d MMM – E, d MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="y">
							<greatestDifference id="y">y–y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yM">
							<greatestDifference id="M">M/y – M/y</greatestDifference>
							<greatestDifference id="y">M/y – M/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMd">
							<greatestDifference id="d">d/M/y – d/M/y</greatestDifference>
							<greatestDifference id="M">d/M/y – d/M/y</greatestDifference>
							<greatestDifference id="y">d/M/y – d/M/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMEd">
							<greatestDifference id="d">E, d/M/y – E, d/M/y</greatestDifference>
							<greatestDifference id="M">E, d/M/y – E, d/M/y</greatestDifference>
							<greatestDifference id="y">E, d/M/y – E, d/M/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMM">
							<greatestDifference id="M">MMM–MMM y</greatestDifference>
							<greatestDifference id="y">MMM y – MMM y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMd">
							<greatestDifference id="d">d–d MMM, y</greatestDifference>
							<greatestDifference id="M">d MMM – d MMM, y</greatestDifference>
							<greatestDifference id="y">d MMM, y – d MMM, y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMEd">
							<greatestDifference id="d">E, d MMM – E, d MMM, y</greatestDifference>
							<greatestDifference id="M">E, d MMM – E, d MMM, y</greatestDifference>
							<greatestDifference id="y">E, d MMM, y – E, d MMM, y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMM">
							<greatestDifference id="M">MMMM–MMMM y</greatestDifference>
							<greatestDifference id="y">MMMM y – MMMM y</greatestDifference>
						</intervalFormatItem>
					</intervalFormats>
				</dateTimeFormats>
			</calendar>
			<calendar type="gregorian">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<month type="1">xan</month>
							<month type="2">feb</month>
							<month type="3">mar</month>
							<month type="4">abr</month>
							<month type="5">mai</month>
							<month type="6">xuñ</month>
							<month type="7">xul</month>
							<month type="8">ago</month>
							<month type="9">set</month>
							<month type="10">out</month>
							<month type="11">nov</month>
							<month type="12">dec</month>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">X</month>
							<month type="2">F</month>
							<month type="3">M</month>
							<month type="4">A</month>
							<month type="5">M</month>
							<month type="6">X</month>
							<month type="7">X</month>
							<month type="8">A</month>
							<month type="9">S</month>
							<month type="10">O</month>
							<month type="11">N</month>
							<month type="12">D</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">xaneiro</month>
							<month type="2">febreiro</month>
							<month type="3">marzo</month>
							<month type="4">abril</month>
							<month type="5">maio</month>
							<month type="6">xuño</month>
							<month type="7">xullo</month>
							<month type="8">agosto</month>
							<month type="9">setembro</month>
							<month type="10">outubro</month>
							<month type="11">novembro</month>
							<month type="12">decembro</month>
						</monthWidth>
					</monthContext>
					<monthContext type="stand-alone">
						<monthWidth type="abbreviated">
							<month type="1">Xan</month>
							<month type="2">Feb</month>
							<month type="3">Mar</month>
							<month type="4">Abr</month>
							<month type="5">Mai</month>
							<month type="6">Xuñ</month>
							<month type="7">Xul</month>
							<month type="8">Ago</month>
							<month type="9">Set</month>
							<month type="10">Out</month>
							<month type="11">Nov</month>
							<month type="12">Dec</month>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">X</month>
							<month type="2">F</month>
							<month type="3">M</month>
							<month type="4">A</month>
							<month type="5">M</month>
							<month type="6">X</month>
							<month type="7">X</month>
							<month type="8">A</month>
							<month type="9">S</month>
							<month type="10">O</month>
							<month type="11">N</month>
							<month type="12">D</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Xaneiro</month>
							<month type="2">Febreiro</month>
							<month type="3">Marzo</month>
							<month type="4">Abril</month>
							<month type="5">Maio</month>
							<month type="6">Xuño</month>
							<month type="7">Xullo</month>
							<month type="8">Agosto</month>
							<month type="9">Setembro</month>
							<month type="10">Outubro</month>
							<month type="11">Novembro</month>
							<month type="12">Decembro</month>
						</monthWidth>
					</monthContext>
				</months>
				<days>
					<dayContext type="format">
						<dayWidth type="abbreviated">
							<day type="sun">dom</day>
							<day type="mon">lun</day>
							<day type="tue">mar</day>
							<day type="wed">mér</day>
							<day type="thu">xov</day>
							<day type="fri">ven</day>
							<day type="sat">sáb</day>
						</dayWidth>
						<dayWidth type="narrow">
							<day type="sun">D</day>
							<day type="mon">L</day>
							<day type="tue">M</day>
							<day type="wed">M</day>
							<day type="thu">X</day>
							<day type="fri">V</day>
							<day type="sat">S</day>
						</dayWidth>
						<dayWidth type="short">
							<day type="sun">dom</day>
							<day type="mon">luns</day>
							<day type="tue">mt</day>
							<day type="wed">mc</day>
							<day type="thu">xv</day>
							<day type="fri">ve</day>
							<day type="sat">sáb</day>
						</dayWidth>
						<dayWidth type="wide">
							<day type="sun">domingo</day>
							<day type="mon">luns</day>
							<day type="tue">martes</day>
							<day type="wed">mércores</day>
							<day type="thu">xoves</day>
							<day type="fri">venres</day>
							<day type="sat">sábado</day>
						</dayWidth>
					</dayContext>
					<dayContext type="stand-alone">
						<dayWidth type="abbreviated">
							<day type="sun">Dom</day>
							<day type="mon">Lun</day>
							<day type="tue">Mar</day>
							<day type="wed">Mér</day>
							<day type="thu">Xov</day>
							<day type="fri">Ven</day>
							<day type="sat">Sáb</day>
						</dayWidth>
						<dayWidth type="narrow">
							<day type="sun">D</day>
							<day type="mon">L</day>
							<day type="tue">M</day>
							<day type="wed">M</day>
							<day type="thu">X</day>
							<day type="fri">V</day>
							<day type="sat">S</day>
						</dayWidth>
						<dayWidth type="short">
							<day type="sun">Dom</day>
							<day type="mon">Luns</day>
							<day type="tue">Mt</day>
							<day type="wed">Mc</day>
							<day type="thu">Xv</day>
							<day type="fri">Ven</day>
							<day type="sat">Sáb</day>
						</dayWidth>
						<dayWidth type="wide">
							<day type="sun">Domingo</day>
							<day type="mon">Luns</day>
							<day type="tue">Martes</day>
							<day type="wed">Mércores</day>
							<day type="thu">Xoves</day>
							<day type="fri">Venres</day>
							<day type="sat">Sábado</day>
						</dayWidth>
					</dayContext>
				</days>
				<quarters>
					<quarterContext type="format">
						<quarterWidth type="abbreviated">
							<quarter type="1">T1</quarter>
							<quarter type="2">T2</quarter>
							<quarter type="3">T3</quarter>
							<quarter type="4">T4</quarter>
						</quarterWidth>
						<quarterWidth type="narrow">
							<quarter type="1">1</quarter>
							<quarter type="2">2</quarter>
							<quarter type="3">3</quarter>
							<quarter type="4">4</quarter>
						</quarterWidth>
						<quarterWidth type="wide">
							<quarter type="1">1o trimestre</quarter>
							<quarter type="2">2o trimestre</quarter>
							<quarter type="3">3o trimestre</quarter>
							<quarter type="4">4o trimestre</quarter>
						</quarterWidth>
					</quarterContext>
					<quarterContext type="stand-alone">
						<quarterWidth type="abbreviated">
							<quarter type="1">T1</quarter>
							<quarter type="2">T2</quarter>
							<quarter type="3">T3</quarter>
							<quarter type="4">T4</quarter>
						</quarterWidth>
						<quarterWidth type="narrow">
							<quarter type="1">1</quarter>
							<quarter type="2">2</quarter>
							<quarter type="3">3</quarter>
							<quarter type="4">4</quarter>
						</quarterWidth>
						<quarterWidth type="wide">
							<quarter type="1">1o trimestre</quarter>
							<quarter type="2">2o trimestre</quarter>
							<quarter type="3">3o trimestre</quarter>
							<quarter type="4">4o trimestre</quarter>
						</quarterWidth>
					</quarterContext>
				</quarters>
				<dayPeriods>
					<dayPeriodContext type="format">
						<dayPeriodWidth type="abbreviated">
							<dayPeriod type="am">a.m.</dayPeriod>
							<dayPeriod type="pm">p.m.</dayPeriod>
						</dayPeriodWidth>
						<dayPeriodWidth type="narrow">
							<dayPeriod type="am">a</dayPeriod>
							<dayPeriod type="pm">p</dayPeriod>
						</dayPeriodWidth>
						<dayPeriodWidth type="wide">
							<dayPeriod type="am">a.m.</dayPeriod>
							<dayPeriod type="pm">p.m.</dayPeriod>
						</dayPeriodWidth>
					</dayPeriodContext>
				</dayPeriods>
				<eras>
					<eraNames>
						<era type="0">antes de Cristo</era>
						<era type="1">despois de Cristo</era>
					</eraNames>
					<eraAbbr>
						<era type="0" draft="contributed">a.C.</era>
						<era type="0" alt="variant">BCE</era>
						<era type="1" draft="contributed">d.C.</era>
						<era type="1" alt="variant">CE</era>
					</eraAbbr>
				</eras>
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>EEEE dd MMMM y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>dd MMMM y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>d MMM, y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>dd/MM/yy</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<timeFormats>
					<timeFormatLength type="full">
						<timeFormat>
							<pattern>HH:mm:ss zzzz</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="long">
						<timeFormat>
							<pattern>HH:mm:ss z</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="medium">
						<timeFormat>
							<pattern>HH:mm:ss</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="short">
						<timeFormat>
							<pattern>HH:mm</pattern>
						</timeFormat>
					</timeFormatLength>
				</timeFormats>
				<dateTimeFormats>
					<dateTimeFormatLength type="full">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="long">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="medium">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="short">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<availableFormats>
						<dateFormatItem id="d">d</dateFormatItem>
						<dateFormatItem id="Ed">d E</dateFormatItem>
						<dateFormatItem id="Ehm">E h:mm a</dateFormatItem>
						<dateFormatItem id="EHm">E HH:mm</dateFormatItem>
						<dateFormatItem id="Ehms">E h:mm:ss a</dateFormatItem>
						<dateFormatItem id="EHms">E HH:mm:ss</dateFormatItem>
						<dateFormatItem id="Gy">G y</dateFormatItem>
						<dateFormatItem id="GyMMM">G y MMM</dateFormatItem>
						<dateFormatItem id="GyMMMd">G y MMM d</dateFormatItem>
						<dateFormatItem id="GyMMMEd">G y MMM d, E</dateFormatItem>
						<dateFormatItem id="h">h a</dateFormatItem>
						<dateFormatItem id="H">HH</dateFormatItem>
						<dateFormatItem id="hm">h:mm a</dateFormatItem>
						<dateFormatItem id="Hm">HH:mm</dateFormatItem>
						<dateFormatItem id="hms">h:mm:ss a</dateFormatItem>
						<dateFormatItem id="Hms">HH:mm:ss</dateFormatItem>
						<dateFormatItem id="M">L</dateFormatItem>
						<dateFormatItem id="Md">d-M</dateFormatItem>
						<dateFormatItem id="MEd">E, d-M</dateFormatItem>
						<dateFormatItem id="MMdd">dd/MM</dateFormatItem>
						<dateFormatItem id="MMM">LLL</dateFormatItem>
						<dateFormatItem id="MMMd">d MMM</dateFormatItem>
						<dateFormatItem id="MMMEd">E d MMM</dateFormatItem>
						<dateFormatItem id="MMMMd">d MMMM</dateFormatItem>
						<dateFormatItem id="MMMMEd">E d MMMM</dateFormatItem>
						<dateFormatItem id="ms">mm:ss</dateFormatItem>
						<dateFormatItem id="y">y</dateFormatItem>
						<dateFormatItem id="yM">M-y</dateFormatItem>
						<dateFormatItem id="yMd">d/M/y</dateFormatItem>
						<dateFormatItem id="yMEd">E, d/M/y</dateFormatItem>
						<dateFormatItem id="yMM">MM/y</dateFormatItem>
						<dateFormatItem id="yMMM">MMM y</dateFormatItem>
						<dateFormatItem id="yMMMd">d MMM, y</dateFormatItem>
						<dateFormatItem id="yMMMEd">E, d MMM, y</dateFormatItem>
						<dateFormatItem id="yMMMM">MMMM y</dateFormatItem>
						<dateFormatItem id="yQQQ">QQQ y</dateFormatItem>
						<dateFormatItem id="yQQQQ">QQQQ y</dateFormatItem>
					</availableFormats>
					<appendItems>
						<appendItem request="Timezone">{0} {1}</appendItem>
					</appendItems>
					<intervalFormats>
						<intervalFormatFallback>{0} – {1}</intervalFormatFallback>
						<intervalFormatItem id="d">
							<greatestDifference id="d">d–d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="h">
							<greatestDifference id="a">h a – h a</greatestDifference>
							<greatestDifference id="h">h–h a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="H">
							<greatestDifference id="H">HH–HH</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hm">
							<greatestDifference id="a">h:mm a – h:mm a</greatestDifference>
							<greatestDifference id="h">h:mm–h:mm a</greatestDifference>
							<greatestDifference id="m">h:mm–h:mm a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hm">
							<greatestDifference id="H">HH:mm–HH:mm</greatestDifference>
							<greatestDifference id="m">HH:mm–HH:mm</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hmv">
							<greatestDifference id="a">h:mm a – h:mm a v</greatestDifference>
							<greatestDifference id="h">h:mm–h:mm a v</greatestDifference>
							<greatestDifference id="m">h:mm–h:mm a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hmv">
							<greatestDifference id="H">HH:mm–HH:mm v</greatestDifference>
							<greatestDifference id="m">HH:mm–HH:mm v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hv">
							<greatestDifference id="a">h a – h a v</greatestDifference>
							<greatestDifference id="h">h–h a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hv">
							<greatestDifference id="H">HH–HH v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="M">
							<greatestDifference id="M">M–M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Md">
							<greatestDifference id="d">d/M – d/M</greatestDifference>
							<greatestDifference id="M">d/M – d/M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MEd">
							<greatestDifference id="d">E, d/M – E, d/M</greatestDifference>
							<greatestDifference id="M">E, d/M – E, d/M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMM">
							<greatestDifference id="M">MMM–MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMd">
							<greatestDifference id="d">d–d MMM</greatestDifference>
							<greatestDifference id="M">d MMM – d MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMEd">
							<greatestDifference id="d">E, d MMM – E, d MMM</greatestDifference>
							<greatestDifference id="M">E, d MMM – E, d MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="y">
							<greatestDifference id="y">y–y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yM">
							<greatestDifference id="M">M/y – M/y</greatestDifference>
							<greatestDifference id="y">M/y – M/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMd">
							<greatestDifference id="d">d/M/y – d/M/y</greatestDifference>
							<greatestDifference id="M">d/M/y – d/M/y</greatestDifference>
							<greatestDifference id="y">d/M/y – d/M/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMEd">
							<greatestDifference id="d">E, d/M/y – E, d/M/y</greatestDifference>
							<greatestDifference id="M">E, d/M/y – E, d/M/y</greatestDifference>
							<greatestDifference id="y">E, d/M/y – E, d/M/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMM">
							<greatestDifference id="M">MMM–MMM y</greatestDifference>
							<greatestDifference id="y">MMM y – MMM y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMd">
							<greatestDifference id="d">d–d MMM, y</greatestDifference>
							<greatestDifference id="M">d MMM – d MMM, y</greatestDifference>
							<greatestDifference id="y">d MMM, y – d MMM, y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMEd">
							<greatestDifference id="d">E, d MMM – E, d MMM, y</greatestDifference>
							<greatestDifference id="M">E, d MMM – E, d MMM, y</greatestDifference>
							<greatestDifference id="y">E, d MMM, y – E, d MMM, y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMM">
							<greatestDifference id="M">MMMM–MMMM y</greatestDifference>
							<greatestDifference id="y">MMMM y – MMMM y</greatestDifference>
						</intervalFormatItem>
					</intervalFormats>
				</dateTimeFormats>
			</calendar>
		</calendars>
		<fields>
			<field type="era">
				<displayName>Era</displayName>
			</field>
			<field type="year">
				<displayName>Ano</displayName>
				<relative type="-1">ano pasado</relative>
				<relative type="0">este ano</relative>
				<relative type="1">seguinte ano</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">En {0} ano</relativeTimePattern>
					<relativeTimePattern count="other">En {0} anos</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">Hai {0} ano</relativeTimePattern>
					<relativeTimePattern count="other">Hai {0} anos</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="month">
				<displayName>Mes</displayName>
				<relative type="-1">mes pasado</relative>
				<relative type="0">este mes</relative>
				<relative type="1">mes seguinte</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">En {0} mes</relativeTimePattern>
					<relativeTimePattern count="other">En {0} meses</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">Hai {0} mes</relativeTimePattern>
					<relativeTimePattern count="other">Hai {0} meses</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="week">
				<displayName>Semana</displayName>
				<relative type="-1">semana pasada</relative>
				<relative type="0">esta semana</relative>
				<relative type="1">semana seguinte</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">En {0} semana</relativeTimePattern>
					<relativeTimePattern count="other">En {0} semanas</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">Hai {0} semana</relativeTimePattern>
					<relativeTimePattern count="other">Hai {0} semanas</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="day">
				<displayName>Día</displayName>
				<relative type="-2">antonte</relative>
				<relative type="-1">onte</relative>
				<relative type="0">hoxe</relative>
				<relative type="1">mañá</relative>
				<relative type="2">pasadomañá</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">En {0} día</relativeTimePattern>
					<relativeTimePattern count="other">En {0} días</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">Hai {0} día</relativeTimePattern>
					<relativeTimePattern count="other">Hai {0} días</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="weekday">
				<displayName>Día da semana</displayName>
			</field>
			<field type="sun">
				<relative type="-1">domingo pasado</relative>
				<relative type="0">este domingo</relative>
				<relative type="1">próximo domingo</relative>
			</field>
			<field type="mon">
				<relative type="-1">luns pasado</relative>
				<relative type="0">este luns</relative>
				<relative type="1">próximo luns</relative>
			</field>
			<field type="tue">
				<relative type="-1">martes pasado</relative>
				<relative type="0">este martes</relative>
				<relative type="1">próximo martes</relative>
			</field>
			<field type="wed">
				<relative type="-1">mércores pasado</relative>
				<relative type="0">este mércores</relative>
				<relative type="1">próximo mércores</relative>
			</field>
			<field type="thu">
				<relative type="-1">xoves pasado</relative>
				<relative type="0">este xoves</relative>
				<relative type="1">próximo xoves</relative>
			</field>
			<field type="fri">
				<relative type="-1">venres pasado</relative>
				<relative type="0">este venres</relative>
				<relative type="1">próximo venres</relative>
			</field>
			<field type="sat">
				<relative type="-1">sábado pasado</relative>
				<relative type="0">este sábado</relative>
				<relative type="1">próximo sábado</relative>
			</field>
			<field type="dayperiod">
				<displayName>a.m./p.m.</displayName>
			</field>
			<field type="hour">
				<displayName>Hora</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">En {0} hora</relativeTimePattern>
					<relativeTimePattern count="other">En {0} horas</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">Hai {0} hora</relativeTimePattern>
					<relativeTimePattern count="other">Hai {0} horas</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="minute">
				<displayName>Minuto</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">En {0} minuto</relativeTimePattern>
					<relativeTimePattern count="other">En {0} minutos</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">Hai {0} minuto</relativeTimePattern>
					<relativeTimePattern count="other">Hai {0} minutos</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="second">
				<displayName>Segundo</displayName>
				<relative type="0">agora</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">En {0} segundo</relativeTimePattern>
					<relativeTimePattern count="other">En {0} segundos</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">Hai {0} segundo</relativeTimePattern>
					<relativeTimePattern count="other">Hai {0} segundos</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="zone">
				<displayName>Fuso horario</displayName>
			</field>
		</fields>
		<timeZoneNames>
			<hourFormat>+HH:mm;-HH:mm</hourFormat>
			<gmtFormat>GMT{0}</gmtFormat>
			<gmtZeroFormat>GMT</gmtZeroFormat>
			<regionFormat>Horario de {0}</regionFormat>
			<regionFormat type="daylight">Horario de verán de {0}</regionFormat>
			<regionFormat type="standard">Horario estándar de {0}</regionFormat>
			<fallbackFormat>{1} ({0})</fallbackFormat>
			<zone type="Etc/Unknown">
				<exemplarCity>Cidade descoñecida</exemplarCity>
			</zone>
			<zone type="Asia/Kabul">
				<exemplarCity>Cabul</exemplarCity>
			</zone>
			<zone type="America/Antigua">
				<exemplarCity>Antiga</exemplarCity>
			</zone>
			<zone type="America/Anguilla">
				<exemplarCity>Anguila</exemplarCity>
			</zone>
			<zone type="Europe/Tirane">
				<exemplarCity>Tirana</exemplarCity>
			</zone>
			<zone type="Asia/Yerevan">
				<exemplarCity>Iereván</exemplarCity>
			</zone>
			<zone type="Antarctica/DumontDUrville">
				<exemplarCity draft="contributed">Dumont-d'Urville</exemplarCity>
			</zone>
			<zone type="America/Argentina/Rio_Gallegos">
				<exemplarCity draft="contributed">Río Gallegos</exemplarCity>
			</zone>
			<zone type="America/Argentina/Tucuman">
				<exemplarCity draft="contributed">Tucumán</exemplarCity>
			</zone>
			<zone type="America/Cordoba">
				<exemplarCity draft="contributed">Córdoba</exemplarCity>
			</zone>
			<zone type="America/Buenos_Aires">
				<exemplarCity draft="contributed">Bos Aires</exemplarCity>
			</zone>
			<zone type="Europe/Vienna">
				<exemplarCity>Viena</exemplarCity>
			</zone>
			<zone type="Australia/Sydney">
				<exemplarCity>Sidney</exemplarCity>
			</zone>
			<zone type="Asia/Baku">
				<exemplarCity>Bacú</exemplarCity>
			</zone>
			<zone type="Europe/Brussels">
				<exemplarCity>Bruxelas</exemplarCity>
			</zone>
			<zone type="Africa/Ouagadougou">
				<exemplarCity>Uagadugú</exemplarCity>
			</zone>
			<zone type="Africa/Porto-Novo">
				<exemplarCity>Porto Novo</exemplarCity>
			</zone>
			<zone type="America/St_Barthelemy">
				<exemplarCity>San Bartolomé</exemplarCity>
			</zone>
			<zone type="Atlantic/Bermuda">
				<exemplarCity>Bermudas</exemplarCity>
			</zone>
			<zone type="America/Rio_Branco">
				<exemplarCity>Río Branco</exemplarCity>
			</zone>
			<zone type="America/Cuiaba">
				<exemplarCity>Cuiabá</exemplarCity>
			</zone>
			<zone type="America/Belem">
				<exemplarCity draft="contributed">Belém</exemplarCity>
			</zone>
			<zone type="America/Araguaina">
				<exemplarCity>Araguaína</exemplarCity>
			</zone>
			<zone type="America/Sao_Paulo">
				<exemplarCity draft="contributed">São Paulo</exemplarCity>
			</zone>
			<zone type="America/Maceio">
				<exemplarCity draft="contributed">Maceió</exemplarCity>
			</zone>
			<zone type="Asia/Thimphu">
				<exemplarCity>Timbu</exemplarCity>
			</zone>
			<zone type="America/Belize">
				<exemplarCity>Belice</exemplarCity>
			</zone>
			<zone type="America/Cambridge_Bay">
				<exemplarCity>Abadía de Cambridge</exemplarCity>
			</zone>
			<zone type="America/Coral_Harbour">
				<exemplarCity>Atikokan</exemplarCity>
			</zone>
			<zone type="America/St_Johns">
				<exemplarCity>St. John’s</exemplarCity>
			</zone>
			<zone type="Europe/Zurich">
				<exemplarCity>Zúric</exemplarCity>
			</zone>
			<zone type="Pacific/Easter">
				<exemplarCity draft="contributed">Illa de Pascua</exemplarCity>
			</zone>
			<zone type="America/Bogota">
				<exemplarCity>Bogotá</exemplarCity>
			</zone>
			<zone type="America/Havana">
				<exemplarCity>Habana</exemplarCity>
			</zone>
			<zone type="Atlantic/Cape_Verde">
				<exemplarCity>Cabo Verde</exemplarCity>
			</zone>
			<zone type="America/Curacao">
				<exemplarCity>Curaçao</exemplarCity>
			</zone>
			<zone type="Indian/Christmas">
				<exemplarCity>Illa de Nadal</exemplarCity>
			</zone>
			<zone type="Europe/Prague">
				<exemplarCity>Praga</exemplarCity>
			</zone>
			<zone type="Europe/Berlin">
				<exemplarCity>Berlín</exemplarCity>
			</zone>
			<zone type="Africa/Djibouti">
				<exemplarCity>Xibutí</exemplarCity>
			</zone>
			<zone type="Europe/Copenhagen">
				<exemplarCity>Copenhaguen</exemplarCity>
			</zone>
			<zone type="Africa/Algiers">
				<exemplarCity>Alxer</exemplarCity>
			</zone>
			<zone type="Pacific/Galapagos">
				<exemplarCity draft="contributed">Illas Galápagos</exemplarCity>
			</zone>
			<zone type="Europe/Tallinn">
				<exemplarCity>Talín</exemplarCity>
			</zone>
			<zone type="Africa/El_Aaiun">
				<exemplarCity>O Aiún</exemplarCity>
			</zone>
			<zone type="Africa/Asmera">
				<exemplarCity>Asmak</exemplarCity>
			</zone>
			<zone type="Atlantic/Canary">
				<exemplarCity draft="contributed">Illas Canarias</exemplarCity>
			</zone>
			<zone type="Africa/Addis_Ababa">
				<exemplarCity>Adís Abeba</exemplarCity>
			</zone>
			<zone type="Pacific/Fiji">
				<exemplarCity>Fidxi</exemplarCity>
			</zone>
			<zone type="Pacific/Truk">
				<exemplarCity>Chuuk</exemplarCity>
			</zone>
			<zone type="Pacific/Ponape">
				<exemplarCity>Pohnpei</exemplarCity>
			</zone>
			<zone type="Europe/Paris">
				<exemplarCity>París</exemplarCity>
			</zone>
			<zone type="Europe/London">
				<exemplarCity>Londres</exemplarCity>
			</zone>
			<zone type="America/Grenada">
				<exemplarCity>Granada</exemplarCity>
			</zone>
			<zone type="America/Cayenne">
				<exemplarCity>Caiena</exemplarCity>
			</zone>
			<zone type="Europe/Guernsey">
				<exemplarCity>Guernesei</exemplarCity>
			</zone>
			<zone type="Africa/Accra">
				<exemplarCity>Acra</exemplarCity>
			</zone>
			<zone type="Europe/Gibraltar">
				<exemplarCity>Xibraltar</exemplarCity>
			</zone>
			<zone type="America/Guadeloupe">
				<exemplarCity>Guadalupe</exemplarCity>
			</zone>
			<zone type="Atlantic/South_Georgia">
				<exemplarCity>Xeorxia do Sur</exemplarCity>
			</zone>
			<zone type="America/Guyana">
				<exemplarCity>Güiana</exemplarCity>
			</zone>
			<zone type="America/Port-au-Prince">
				<exemplarCity>Porto Príncipe</exemplarCity>
			</zone>
			<zone type="Asia/Jakarta">
				<exemplarCity draft="contributed">Iacarta</exemplarCity>
			</zone>
			<zone type="Asia/Jayapura">
				<exemplarCity>Jaiapura</exemplarCity>
			</zone>
			<zone type="Europe/Dublin">
				<exemplarCity>Dublín</exemplarCity>
			</zone>
			<zone type="Europe/Isle_of_Man">
				<exemplarCity>Illa de Man</exemplarCity>
			</zone>
			<zone type="Asia/Calcutta">
				<exemplarCity>Calcuta</exemplarCity>
			</zone>
			<zone type="Asia/Baghdad">
				<exemplarCity>Bagdad</exemplarCity>
			</zone>
			<zone type="Atlantic/Reykjavik">
				<exemplarCity>Reiquiavik</exemplarCity>
			</zone>
			<zone type="America/Jamaica">
				<exemplarCity>Xamaica</exemplarCity>
			</zone>
			<zone type="Asia/Amman">
				<exemplarCity>Amán</exemplarCity>
			</zone>
			<zone type="Asia/Tokyo">
				<exemplarCity>Toquio</exemplarCity>
			</zone>
			<zone type="Indian/Comoro">
				<exemplarCity>Illas Comores</exemplarCity>
			</zone>
			<zone type="America/St_Kitts">
				<exemplarCity>San Cristovo</exemplarCity>
			</zone>
			<zone type="Asia/Seoul">
				<exemplarCity>Seúl</exemplarCity>
			</zone>
			<zone type="America/Cayman">
				<exemplarCity>Caimán</exemplarCity>
			</zone>
			<zone type="Asia/Almaty">
				<exemplarCity>Almati</exemplarCity>
			</zone>
			<zone type="America/St_Lucia">
				<exemplarCity>St. Lucia</exemplarCity>
			</zone>
			<zone type="Europe/Luxembourg">
				<exemplarCity>Luxemburgo</exemplarCity>
			</zone>
			<zone type="Africa/Tripoli">
				<exemplarCity>Trípoli</exemplarCity>
			</zone>
			<zone type="Europe/Monaco">
				<exemplarCity>Mónaco</exemplarCity>
			</zone>
			<zone type="Africa/Bamako">
				<exemplarCity>Bamaco</exemplarCity>
			</zone>
			<zone type="Asia/Ulaanbaatar">
				<exemplarCity>Ulán Bátor</exemplarCity>
			</zone>
			<zone type="Pacific/Saipan">
				<exemplarCity>Saipán</exemplarCity>
			</zone>
			<zone type="America/Martinique">
				<exemplarCity>Martinica</exemplarCity>
			</zone>
			<zone type="Indian/Mauritius">
				<exemplarCity>Mauricio</exemplarCity>
			</zone>
			<zone type="Indian/Maldives">
				<exemplarCity>Maldivas</exemplarCity>
			</zone>
			<zone type="America/Mazatlan">
				<exemplarCity>Mazatlán</exemplarCity>
			</zone>
			<zone type="America/Bahia_Banderas">
				<exemplarCity>Baía de Bandeiras</exemplarCity>
			</zone>
			<zone type="America/Monterrey">
				<exemplarCity>Monterrei</exemplarCity>
			</zone>
			<zone type="America/Mexico_City">
				<exemplarCity>Cidade de México</exemplarCity>
			</zone>
			<zone type="America/Merida">
				<exemplarCity>Mérida</exemplarCity>
			</zone>
			<zone type="America/Cancun">
				<exemplarCity>Cancún</exemplarCity>
			</zone>
			<zone type="Europe/Amsterdam">
				<exemplarCity>Ámsterdan</exemplarCity>
			</zone>
			<zone type="Asia/Katmandu">
				<exemplarCity>Kathmandu</exemplarCity>
			</zone>
			<zone type="Asia/Muscat">
				<exemplarCity>Mascat</exemplarCity>
			</zone>
			<zone type="America/Panama">
				<exemplarCity>Panamá</exemplarCity>
			</zone>
			<zone type="Pacific/Tahiti">
				<exemplarCity>Tahití</exemplarCity>
			</zone>
			<zone type="Europe/Warsaw">
				<exemplarCity>Varsovia</exemplarCity>
			</zone>
			<zone type="America/Puerto_Rico">
				<exemplarCity>Porto Rico</exemplarCity>
			</zone>
			<zone type="Europe/Lisbon">
				<exemplarCity>Lisboa</exemplarCity>
			</zone>
			<zone type="America/Asuncion">
				<exemplarCity>Asunción</exemplarCity>
			</zone>
			<zone type="Indian/Reunion">
				<exemplarCity>Reunión</exemplarCity>
			</zone>
			<zone type="Europe/Bucharest">
				<exemplarCity>Bucarest</exemplarCity>
			</zone>
			<zone type="Europe/Belgrade">
				<exemplarCity>Belgrado</exemplarCity>
			</zone>
			<zone type="Europe/Kaliningrad">
				<exemplarCity draft="contributed">Kaliningrado</exemplarCity>
			</zone>
			<zone type="Europe/Moscow">
				<exemplarCity draft="contributed">Moscova</exemplarCity>
			</zone>
			<zone type="Europe/Volgograd">
				<exemplarCity draft="contributed">Volgogrado</exemplarCity>
			</zone>
			<zone type="Asia/Yekaterinburg">
				<exemplarCity draft="contributed">Ecaterinburgo</exemplarCity>
			</zone>
			<zone type="Asia/Anadyr">
				<exemplarCity>Anadir</exemplarCity>
			</zone>
			<zone type="Asia/Riyadh">
				<exemplarCity>Riad</exemplarCity>
			</zone>
			<zone type="Africa/Khartoum">
				<exemplarCity>Khartún</exemplarCity>
			</zone>
			<zone type="Asia/Singapore">
				<exemplarCity>Singapur</exemplarCity>
			</zone>
			<zone type="Atlantic/St_Helena">
				<exemplarCity>Santa Helena</exemplarCity>
			</zone>
			<zone type="Europe/Ljubljana">
				<exemplarCity>Liubliana</exemplarCity>
			</zone>
			<zone type="Africa/Dakar">
				<exemplarCity>Dacar</exemplarCity>
			</zone>
			<zone type="Africa/Mogadishu">
				<exemplarCity>Mogadixo</exemplarCity>
			</zone>
			<zone type="Africa/Sao_Tome">
				<exemplarCity>São Tomé</exemplarCity>
			</zone>
			<zone type="America/El_Salvador">
				<exemplarCity>O Salvador</exemplarCity>
			</zone>
			<zone type="America/Lower_Princes">
				<exemplarCity>Lower Prince's Quarter</exemplarCity>
			</zone>
			<zone type="Asia/Damascus">
				<exemplarCity>Damasco</exemplarCity>
			</zone>
			<zone type="America/Grand_Turk">
				<exemplarCity>Gran Turca</exemplarCity>
			</zone>
			<zone type="Africa/Ndjamena">
				<exemplarCity>Xamena</exemplarCity>
			</zone>
			<zone type="Africa/Lome">
				<exemplarCity>Lomé</exemplarCity>
			</zone>
			<zone type="Africa/Tunis">
				<exemplarCity>Túnez</exemplarCity>
			</zone>
			<zone type="Europe/Istanbul">
				<exemplarCity>Estanbul</exemplarCity>
			</zone>
			<zone type="America/Port_of_Spain">
				<exemplarCity>Porto España</exemplarCity>
			</zone>
			<zone type="Europe/Zaporozhye">
				<exemplarCity>Zaporizhia</exemplarCity>
			</zone>
			<zone type="Pacific/Honolulu">
				<exemplarCity draft="contributed">Honolulú</exemplarCity>
			</zone>
			<zone type="America/Los_Angeles">
				<exemplarCity>Os Ánxeles</exemplarCity>
			</zone>
			<zone type="America/North_Dakota/Beulah">
				<exemplarCity>Beulah, Dakota do Norte</exemplarCity>
			</zone>
			<zone type="America/North_Dakota/New_Salem">
				<exemplarCity>New Salem, Dakota do Norte</exemplarCity>
			</zone>
			<zone type="America/North_Dakota/Center">
				<exemplarCity>Center, Dakota do Norte</exemplarCity>
			</zone>
			<zone type="America/Indiana/Vincennes">
				<exemplarCity>Vincennes, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Petersburg">
				<exemplarCity>Petersburgo, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Tell_City">
				<exemplarCity>Tell City, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Knox">
				<exemplarCity>Knox, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Winamac">
				<exemplarCity>Winamac, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Marengo">
				<exemplarCity>Marengo, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indianapolis">
				<exemplarCity>Indianápolis</exemplarCity>
			</zone>
			<zone type="America/Indiana/Vevay">
				<exemplarCity>Vevay, Indiana</exemplarCity>
			</zone>
			<zone type="America/Kentucky/Monticello">
				<exemplarCity>Monticello, Kentucky</exemplarCity>
			</zone>
			<zone type="America/New_York">
				<exemplarCity>Nova York</exemplarCity>
			</zone>
			<zone type="Asia/Samarkand">
				<exemplarCity draft="contributed">Samarcanda</exemplarCity>
			</zone>
			<zone type="Europe/Vatican">
				<exemplarCity>Vaticano</exemplarCity>
			</zone>
			<zone type="America/St_Vincent">
				<exemplarCity>San Vicente</exemplarCity>
			</zone>
			<zone type="Asia/Saigon">
				<exemplarCity>Ho Chi Minh</exemplarCity>
			</zone>
			<zone type="Asia/Aden">
				<exemplarCity>Adén</exemplarCity>
			</zone>
			<zone type="Africa/Johannesburg">
				<exemplarCity>Xohanesburgo</exemplarCity>
			</zone>
			<zone type="Africa/Lusaka">
				<exemplarCity>Lusaca</exemplarCity>
			</zone>
			<metazone type="Afghanistan">
				<long>
					<standard>Horario de Afganistán</standard>
				</long>
			</metazone>
			<metazone type="Africa_Central">
				<long>
					<standard>Horario de África Central</standard>
				</long>
			</metazone>
			<metazone type="Africa_Eastern">
				<long>
					<standard>Horario de África Oriental</standard>
				</long>
			</metazone>
			<metazone type="Africa_Southern">
				<long>
					<standard>Horario estándar de Sudáfrica</standard>
				</long>
			</metazone>
			<metazone type="Africa_Western">
				<long>
					<generic>Horario de África Occidental</generic>
					<standard>Horario estándar de África Occidental</standard>
					<daylight>Horario de verán de África Occidental</daylight>
				</long>
			</metazone>
			<metazone type="Alaska">
				<long>
					<generic>Horario de Alasca</generic>
					<standard>Horario estándar de Alasca</standard>
					<daylight>Horario de verán de Alasca</daylight>
				</long>
			</metazone>
			<metazone type="Amazon">
				<long>
					<generic>Horario do Amazonas</generic>
					<standard>Horario estándar do Amazonas</standard>
					<daylight>Horario de verán do Amazonas</daylight>
				</long>
			</metazone>
			<metazone type="America_Central">
				<long>
					<generic>Horario central</generic>
					<standard>Horario estándar central</standard>
					<daylight>Horario de verán da zona central</daylight>
				</long>
			</metazone>
			<metazone type="America_Eastern">
				<long>
					<generic>Horario de América Oriental</generic>
					<standard>Horario estándar América Oriental</standard>
					<daylight>Horario de verán de América Oriental</daylight>
				</long>
			</metazone>
			<metazone type="America_Mountain">
				<long>
					<generic>Horario das montañas de América</generic>
					<standard>Horario estándar das montañas americanas</standard>
					<daylight>Horario de verán das montañas americanas</daylight>
				</long>
			</metazone>
			<metazone type="America_Pacific">
				<long>
					<generic>Horario do Pacífico</generic>
					<standard>Horario estándar do Pacífico</standard>
					<daylight>Horario de verán do Pacífico</daylight>
				</long>
			</metazone>
			<metazone type="Anadyr">
				<long>
					<generic>Horario de Anadir</generic>
					<standard>Horario estándar de Anadir</standard>
					<daylight>Horario de verán de Anadir</daylight>
				</long>
			</metazone>
			<metazone type="Arabian">
				<long>
					<generic>Horario árabe</generic>
					<standard>Horario estándar árabe</standard>
					<daylight>Horario de verán árabe</daylight>
				</long>
			</metazone>
			<metazone type="Argentina">
				<long>
					<generic>Horario de Arxentina</generic>
					<standard>Horario estándar de Arxentina</standard>
					<daylight>Horario de verán de Arxentina</daylight>
				</long>
			</metazone>
			<metazone type="Argentina_Western">
				<long>
					<generic>Horario de Arxentina Occidental</generic>
					<standard>Horario estándar de Arxentina Occidental</standard>
					<daylight>Horario de verán de Arxentina Occidental</daylight>
				</long>
			</metazone>
			<metazone type="Armenia">
				<long>
					<generic>Horario de Armenia</generic>
					<standard>Horario estándar de Armenia</standard>
					<daylight>Horario de verán de Armenia</daylight>
				</long>
			</metazone>
			<metazone type="Atlantic">
				<long>
					<generic>Horario do Atlántico</generic>
					<standard>Horario estándar do Atlántico</standard>
					<daylight>Horario de verán do Atlántico</daylight>
				</long>
			</metazone>
			<metazone type="Australia_Central">
				<long>
					<generic>Horario de Australia Central</generic>
					<standard>Horario estándar de Australia Central</standard>
					<daylight>Horario de verán de Australia Central</daylight>
				</long>
			</metazone>
			<metazone type="Australia_CentralWestern">
				<long>
					<generic>Horario de Australia Occidental Central</generic>
					<standard>Horario estándar de Australia Occidental Central</standard>
					<daylight>Horario de verán de Australia Occidental Central</daylight>
				</long>
			</metazone>
			<metazone type="Australia_Eastern">
				<long>
					<generic>Horario de Australia Oriental</generic>
					<standard>Horario estándar de Australia Oriental</standard>
					<daylight>Horario de verán de Australia Oriental</daylight>
				</long>
			</metazone>
			<metazone type="Australia_Western">
				<long>
					<generic>Horario de Australia Occidental</generic>
					<standard>Horario estándar de Australia Occidental</standard>
					<daylight>Horario de verán de Australia Occidental</daylight>
				</long>
			</metazone>
			<metazone type="Azerbaijan">
				<long>
					<generic>Horario de Acerbaixán</generic>
					<standard>Horario estándar de Acerbaixán</standard>
					<daylight>Horario de verán de Acerbaixán</daylight>
				</long>
			</metazone>
			<metazone type="Azores">
				<long>
					<generic>Horario das Azores</generic>
					<standard>Horario estándar das Azores</standard>
					<daylight>Horario de verán das Azores</daylight>
				</long>
			</metazone>
			<metazone type="Bangladesh">
				<long>
					<generic>Horario de Bangladesh</generic>
					<standard>Horario estándar de Bangladesh</standard>
					<daylight>Horario de verán de Bangladesh</daylight>
				</long>
			</metazone>
			<metazone type="Bhutan">
				<long>
					<standard>Horario de Bután</standard>
				</long>
			</metazone>
			<metazone type="Bolivia">
				<long>
					<standard>Horario de Bolivia</standard>
				</long>
			</metazone>
			<metazone type="Brasilia">
				<long>
					<generic>Horario de Brasilia</generic>
					<standard>Horario estándar de Brasilia</standard>
					<daylight>Horario de verán de Brasilia</daylight>
				</long>
			</metazone>
			<metazone type="Brunei">
				<long>
					<standard>Horario de Brunei Darussalam</standard>
				</long>
			</metazone>
			<metazone type="Cape_Verde">
				<long>
					<generic>Horario de Cabo Verde</generic>
					<standard>Horario estándar de Cabo Verde</standard>
					<daylight>Horario de verán de Cabo Verde</daylight>
				</long>
			</metazone>
			<metazone type="Chamorro">
				<long>
					<standard>Horario estándar de Chamorro</standard>
				</long>
			</metazone>
			<metazone type="Chatham">
				<long>
					<generic>Horario de Chatham</generic>
					<standard>Horario estándar de Chatham</standard>
					<daylight>Horario de verán de Chatham</daylight>
				</long>
			</metazone>
			<metazone type="Chile">
				<long>
					<generic>Horario de Chile</generic>
					<standard>Horario estándar de Chile</standard>
					<daylight>Horario de verán de Chile</daylight>
				</long>
			</metazone>
			<metazone type="China">
				<long>
					<generic>Horario de China</generic>
					<standard>Horario estándar de China</standard>
					<daylight>Horario de verán de China</daylight>
				</long>
			</metazone>
			<metazone type="Choibalsan">
				<long>
					<generic>Horario de Choibalsan</generic>
					<standard>Horario estándar de Choibalsan</standard>
					<daylight>Horario de verán de Choibalsan</daylight>
				</long>
			</metazone>
			<metazone type="Christmas">
				<long>
					<standard>Horario da Illa de Nadal</standard>
				</long>
			</metazone>
			<metazone type="Cocos">
				<long>
					<standard>Horario das Illas Cocos</standard>
				</long>
			</metazone>
			<metazone type="Colombia">
				<long>
					<generic>Horario de Colombia</generic>
					<standard>Horario estándar de Colombia</standard>
					<daylight>Horario de verán de Colombia</daylight>
				</long>
			</metazone>
			<metazone type="Cook">
				<long>
					<generic>Horario das Illas Cook</generic>
					<standard>Horario estándar das Illas Cook</standard>
					<daylight>Horario de verán medio das Illas Cook</daylight>
				</long>
			</metazone>
			<metazone type="Cuba">
				<long>
					<generic>Horario de Cuba</generic>
					<standard>Horario estándar de Cuba</standard>
					<daylight>Horario de verán de Cuba</daylight>
				</long>
			</metazone>
			<metazone type="Davis">
				<long>
					<standard>Horario de Davis</standard>
				</long>
			</metazone>
			<metazone type="DumontDUrville">
				<long>
					<standard>Horario de Dumont-d’Urville</standard>
				</long>
			</metazone>
			<metazone type="East_Timor">
				<long>
					<standard>Horario de Timor Leste</standard>
				</long>
			</metazone>
			<metazone type="Easter">
				<long>
					<generic>Horario da Illa de Pascua</generic>
					<standard>Horario estándar da Illa de Pascua</standard>
					<daylight>Horario de verán da Illa de Pascua</daylight>
				</long>
			</metazone>
			<metazone type="Ecuador">
				<long>
					<standard>Horario de Ecuador</standard>
				</long>
			</metazone>
			<metazone type="Europe_Central">
				<long>
					<generic>Horario de Europa Central</generic>
					<standard>Horario estándar de Europa Central</standard>
					<daylight>Horario de verán de Europa Central</daylight>
				</long>
				<short>
					<generic>CET</generic>
					<standard>CET</standard>
					<daylight>CEST</daylight>
				</short>
			</metazone>
			<metazone type="Europe_Eastern">
				<long>
					<generic>Horario de Europa Oriental</generic>
					<standard>Horario estándar de Europa Oriental</standard>
					<daylight>Horario de verán de Europa Oriental</daylight>
				</long>
				<short>
					<generic>EET</generic>
					<standard>EET</standard>
					<daylight>EEST</daylight>
				</short>
			</metazone>
			<metazone type="Europe_Western">
				<long>
					<generic>Horario de Europa Occidental</generic>
					<standard>Horario estándar de Europa Occidental</standard>
					<daylight>Horario de verán de Europa Occidental</daylight>
				</long>
				<short>
					<generic>WET</generic>
					<standard>WET</standard>
					<daylight>WEST</daylight>
				</short>
			</metazone>
			<metazone type="Falkland">
				<long>
					<generic>Horario das Illas Malvinas</generic>
					<standard>Horario estándar das Illas Malvinas</standard>
					<daylight>Horario de verán das Illas Malvinas</daylight>
				</long>
			</metazone>
			<metazone type="Fiji">
				<long>
					<generic>Horario de Fidxi</generic>
					<standard>Horario estándar de Fidxi</standard>
					<daylight>Horario de verán de Fidxi</daylight>
				</long>
			</metazone>
			<metazone type="French_Guiana">
				<long>
					<standard>Horario da Güiana Francesa</standard>
				</long>
			</metazone>
			<metazone type="French_Southern">
				<long>
					<standard>Horario das Terras Austrais e Antárticas Francesas</standard>
				</long>
			</metazone>
			<metazone type="Galapagos">
				<long>
					<standard>Horario das Galápagos</standard>
				</long>
			</metazone>
			<metazone type="Gambier">
				<long>
					<standard>Horario de Gambier</standard>
				</long>
			</metazone>
			<metazone type="Georgia">
				<long>
					<generic>Horario de Xeorxia</generic>
					<standard>Horario estándar de Xeorxia</standard>
					<daylight>Horario de verán de Xeorxia</daylight>
				</long>
			</metazone>
			<metazone type="Gilbert_Islands">
				<long>
					<standard>Horario das Illas Gilbert</standard>
				</long>
			</metazone>
			<metazone type="GMT">
				<long>
					<standard>Horario do meridiano de Greenwich</standard>
				</long>
				<short>
					<standard>GMT</standard>
				</short>
			</metazone>
			<metazone type="Greenland_Eastern">
				<long>
					<generic>Horario de Grenlandia Oriental</generic>
					<standard>Horario estándar de Grenlandia Oriental</standard>
					<daylight>Horario de verán de Grenlandia Oriental</daylight>
				</long>
			</metazone>
			<metazone type="Greenland_Western">
				<long>
					<generic>Horario de Grenlandia Occidental</generic>
					<standard>Horario estándar de Grenlandia Occidental</standard>
					<daylight>Horario de verán de Grenlandia Occidental</daylight>
				</long>
			</metazone>
			<metazone type="Gulf">
				<long>
					<standard>Horario estándar do Golfo</standard>
				</long>
			</metazone>
			<metazone type="Guyana">
				<long>
					<standard>Horario da Güiana</standard>
				</long>
			</metazone>
			<metazone type="Hawaii_Aleutian">
				<long>
					<generic>Horario de Hawai-Aleutiano</generic>
					<standard>Horario estándar de Hawai-Aleutiano</standard>
					<daylight>Horario de verán de Hawai-Aleutiano</daylight>
				</long>
			</metazone>
			<metazone type="Hong_Kong">
				<long>
					<generic>Horario de Hong Kong</generic>
					<standard>Horario estándar de Hong Kong</standard>
					<daylight>Horario de verán de Hong Kong</daylight>
				</long>
			</metazone>
			<metazone type="Hovd">
				<long>
					<generic>Horario de Hovd</generic>
					<standard>Horario estándar de Hovd</standard>
					<daylight>Horario de verán de Hovd</daylight>
				</long>
			</metazone>
			<metazone type="India">
				<long>
					<standard>Horario estándar da India</standard>
				</long>
			</metazone>
			<metazone type="Indian_Ocean">
				<long>
					<standard>Horario do Océano Índico</standard>
				</long>
			</metazone>
			<metazone type="Indochina">
				<long>
					<standard>Horario de Indochina</standard>
				</long>
			</metazone>
			<metazone type="Indonesia_Central">
				<long>
					<standard>Horario de Indonesia Central</standard>
				</long>
			</metazone>
			<metazone type="Indonesia_Eastern">
				<long>
					<standard>Horario de Indonesia Oriental</standard>
				</long>
			</metazone>
			<metazone type="Indonesia_Western">
				<long>
					<standard>Horario de Indonesia Occidental</standard>
				</long>
			</metazone>
			<metazone type="Iran">
				<long>
					<generic>Horario de Irán</generic>
					<standard>Horario estándar de Irán</standard>
					<daylight>Horario de verán de Irán</daylight>
				</long>
			</metazone>
			<metazone type="Irkutsk">
				<long>
					<generic>Horario de Irkutsk</generic>
					<standard>Horario estándar de Irkutsk</standard>
					<daylight>Horario de verán de Irkutsk</daylight>
				</long>
			</metazone>
			<metazone type="Israel">
				<long>
					<generic>Horario de Israel</generic>
					<standard>Horario estándar de Israel</standard>
					<daylight>Horario de verán de Israel</daylight>
				</long>
			</metazone>
			<metazone type="Japan">
				<long>
					<generic>Horario de Xapón</generic>
					<standard>Horario estándar de Xapón</standard>
					<daylight>Horario de verán de Xapón</daylight>
				</long>
			</metazone>
			<metazone type="Kamchatka">
				<long>
					<generic>Horario de Petropávlovsk-Kamchatski</generic>
					<standard>Horario estándar de Petropávlovsk-Kamchatski</standard>
					<daylight>Horario de verán de Petropávlovsk-Kamchatski</daylight>
				</long>
			</metazone>
			<metazone type="Kazakhstan_Eastern">
				<long>
					<standard>Horario de Casaquistán este</standard>
				</long>
			</metazone>
			<metazone type="Kazakhstan_Western">
				<long>
					<standard>Horario de Casaquistán oeste</standard>
				</long>
			</metazone>
			<metazone type="Korea">
				<long>
					<generic>Horario de Corea</generic>
					<standard>Horario estándar de Corea</standard>
					<daylight>Horario de verán de Corea</daylight>
				</long>
			</metazone>
			<metazone type="Kosrae">
				<long>
					<standard>Horario de Kosrae</standard>
				</long>
			</metazone>
			<metazone type="Krasnoyarsk">
				<long>
					<generic>Horario de Krasnoyarsk</generic>
					<standard>Horario estándar de Krasnoyarsk</standard>
					<daylight>Horario de verán de Krasnoyarsk</daylight>
				</long>
			</metazone>
			<metazone type="Kyrgystan">
				<long>
					<standard>Horario de Quirguicistán</standard>
				</long>
			</metazone>
			<metazone type="Line_Islands">
				<long>
					<standard>Horario das Illas da Liña</standard>
				</long>
			</metazone>
			<metazone type="Lord_Howe">
				<long>
					<generic>Horario de Lord Howe</generic>
					<standard>Horario estándar de Lord Howe</standard>
					<daylight>Horario de verán de Lord Howe</daylight>
				</long>
			</metazone>
			<metazone type="Macquarie">
				<long>
					<standard>Horario da Illa Macquarie</standard>
				</long>
			</metazone>
			<metazone type="Magadan">
				<long>
					<generic>Horario de Magadán</generic>
					<standard>Horario estándar de Magadán</standard>
					<daylight>Horario de verán de Magadán</daylight>
				</long>
			</metazone>
			<metazone type="Malaysia">
				<long>
					<standard>Horario de Malaisia</standard>
				</long>
			</metazone>
			<metazone type="Maldives">
				<long>
					<standard>Horario das Maldivas</standard>
				</long>
			</metazone>
			<metazone type="Marquesas">
				<long>
					<standard>Horario das Marquesas</standard>
				</long>
			</metazone>
			<metazone type="Marshall_Islands">
				<long>
					<standard>Horario das Illas Marshall</standard>
				</long>
			</metazone>
			<metazone type="Mauritius">
				<long>
					<generic>Horario de Mauricio</generic>
					<standard>Horario estándar de Mauricio</standard>
					<daylight>Horario de verán de Mauricio</daylight>
				</long>
			</metazone>
			<metazone type="Mawson">
				<long>
					<standard>Horario de Mawson</standard>
				</long>
			</metazone>
			<metazone type="Mongolia">
				<long>
					<generic>Horario de Ulán Bátor</generic>
					<standard>Horario estándar de Ulán Bátor</standard>
					<daylight>Horario de verán de Ulán Bátor</daylight>
				</long>
			</metazone>
			<metazone type="Moscow">
				<long>
					<generic>Horario de Moscova</generic>
					<standard>Horario estándar de Moscova</standard>
					<daylight>Horario de verán de Moscova</daylight>
				</long>
			</metazone>
			<metazone type="Myanmar">
				<long>
					<standard>Horario de Birmania</standard>
				</long>
			</metazone>
			<metazone type="Nauru">
				<long>
					<standard>Horario de Nauru</standard>
				</long>
			</metazone>
			<metazone type="Nepal">
				<long>
					<standard>Horario de Nepal</standard>
				</long>
			</metazone>
			<metazone type="New_Caledonia">
				<long>
					<generic>Horario de Nova Caledonia</generic>
					<standard>Horario estándar de Nova Caledonia</standard>
					<daylight>Horario de verán de Nova Caledonia</daylight>
				</long>
			</metazone>
			<metazone type="New_Zealand">
				<long>
					<generic>Horario de Nova Celandia</generic>
					<standard>Horario estándar de Nova Celandia</standard>
					<daylight>Horario de verán de Nova Celandia</daylight>
				</long>
			</metazone>
			<metazone type="Newfoundland">
				<long>
					<generic>Horario de Terranova</generic>
					<standard>Horario estándar de Terranova</standard>
					<daylight>Horario de verán de Terranova</daylight>
				</long>
			</metazone>
			<metazone type="Niue">
				<long>
					<standard>Horario de Niue</standard>
				</long>
			</metazone>
			<metazone type="Norfolk">
				<long>
					<standard>Horario das Illas Norfolk</standard>
				</long>
			</metazone>
			<metazone type="Noronha">
				<long>
					<generic>Horario de Fernando de Noronha</generic>
					<standard>Horario estándar de Fernando de Noronha</standard>
					<daylight>Horario de verán de Fernando de Noronha</daylight>
				</long>
			</metazone>
			<metazone type="Novosibirsk">
				<long>
					<generic>Horario de Novosibirsk</generic>
					<standard>Horario estándar de Novosibirsk</standard>
					<daylight>Horario de verán de Novosibirsk</daylight>
				</long>
			</metazone>
			<metazone type="Omsk">
				<long>
					<generic>Horario de Omsk</generic>
					<standard>Horario estándar de Omsk</standard>
					<daylight>Horario de verán de Omsk</daylight>
				</long>
			</metazone>
			<metazone type="Pakistan">
				<long>
					<generic>Horario de Paquistán</generic>
					<standard>Horario estándar de Paquistán</standard>
					<daylight>Horario de verán de Paquistán</daylight>
				</long>
			</metazone>
			<metazone type="Palau">
				<long>
					<standard>Horario de Palau</standard>
				</long>
			</metazone>
			<metazone type="Papua_New_Guinea">
				<long>
					<standard>Horario de Papúa Nova Guinea</standard>
				</long>
			</metazone>
			<metazone type="Paraguay">
				<long>
					<generic>Horario de Paraguai</generic>
					<standard>Horario estándar de Paraguai</standard>
					<daylight>Horario de verán de Paraguai</daylight>
				</long>
			</metazone>
			<metazone type="Peru">
				<long>
					<generic>Horario de Perú</generic>
					<standard>Horario estándar de Perú</standard>
					<daylight>Horario de verán de Perú</daylight>
				</long>
			</metazone>
			<metazone type="Philippines">
				<long>
					<generic>Horario de Filipinas</generic>
					<standard>Horario estándar de Filipinas</standard>
					<daylight>Horario de verán de Filipinas</daylight>
				</long>
			</metazone>
			<metazone type="Phoenix_Islands">
				<long>
					<standard>Horario das Illas Fénix</standard>
				</long>
			</metazone>
			<metazone type="Pierre_Miquelon">
				<long>
					<generic>Horario de San Pedro e Miguelón</generic>
					<standard>Horario estándar de San Pedro e Miguelón</standard>
					<daylight>Horario de verán de San Pedro e Miguelón</daylight>
				</long>
			</metazone>
			<metazone type="Pitcairn">
				<long>
					<standard>Horario de Pitcairn</standard>
				</long>
			</metazone>
			<metazone type="Ponape">
				<long>
					<standard>Horario de Pohnpei</standard>
				</long>
			</metazone>
			<metazone type="Reunion">
				<long>
					<standard>Horario de Reunión</standard>
				</long>
			</metazone>
			<metazone type="Rothera">
				<long>
					<standard>Horario de Rothera</standard>
				</long>
			</metazone>
			<metazone type="Sakhalin">
				<long>
					<generic>Horario de Sakhalin</generic>
					<standard>Horario estándar de Sakhalín</standard>
					<daylight>Horario de verán de Sakhalin</daylight>
				</long>
			</metazone>
			<metazone type="Samara">
				<long>
					<generic>Horario de Samara</generic>
					<standard>Horario estándar de Samara</standard>
					<daylight>Horario de verán de Samara</daylight>
				</long>
			</metazone>
			<metazone type="Samoa">
				<long>
					<generic>Horario de Samoa</generic>
					<standard>Horario estándar de Samoa</standard>
					<daylight>Horario de verán de Samoa</daylight>
				</long>
			</metazone>
			<metazone type="Seychelles">
				<long>
					<standard>Horario das Seixeles</standard>
				</long>
			</metazone>
			<metazone type="Singapore">
				<long>
					<standard>Horario estándar de Singapur</standard>
				</long>
			</metazone>
			<metazone type="Solomon">
				<long>
					<standard>Horario das Illas Salomón</standard>
				</long>
			</metazone>
			<metazone type="South_Georgia">
				<long>
					<standard>Horario de Xeorxia do Sur</standard>
				</long>
			</metazone>
			<metazone type="Suriname">
				<long>
					<standard>Horario de Surinam</standard>
				</long>
			</metazone>
			<metazone type="Syowa">
				<long>
					<standard>Horario de Syowa</standard>
				</long>
			</metazone>
			<metazone type="Tahiti">
				<long>
					<standard>Horario de Tahití</standard>
				</long>
			</metazone>
			<metazone type="Taipei">
				<long>
					<generic>Horario de Taipei</generic>
					<standard>Horario estándar de Taipei</standard>
					<daylight>Horario de verán de Taipei</daylight>
				</long>
			</metazone>
			<metazone type="Tajikistan">
				<long>
					<standard>Horario de Taxiquistán</standard>
				</long>
			</metazone>
			<metazone type="Tokelau">
				<long>
					<standard>Horario de Toquelau</standard>
				</long>
			</metazone>
			<metazone type="Tonga">
				<long>
					<generic>Horario de Tonga</generic>
					<standard>Horario estándar de Tonga</standard>
					<daylight>Horario de verán de Tonga</daylight>
				</long>
			</metazone>
			<metazone type="Truk">
				<long>
					<standard>Horario de Chuuk</standard>
				</long>
			</metazone>
			<metazone type="Turkmenistan">
				<long>
					<generic>Horario de Turcomenistán</generic>
					<standard>Horario estándar de Turcomenistán</standard>
					<daylight>Horario de verán de Turcomenistán</daylight>
				</long>
			</metazone>
			<metazone type="Tuvalu">
				<long>
					<standard>Horario de Tuvalu</standard>
				</long>
			</metazone>
			<metazone type="Uruguay">
				<long>
					<generic>Horario de Uruguai</generic>
					<standard>Horario estándar de Uruguai</standard>
					<daylight>Horario de verán de Uruguai</daylight>
				</long>
			</metazone>
			<metazone type="Uzbekistan">
				<long>
					<generic>Horario de Usbequistán</generic>
					<standard>Horario estándar de Usbequistán</standard>
					<daylight>Horario de verán de Usbequistán</daylight>
				</long>
			</metazone>
			<metazone type="Vanuatu">
				<long>
					<generic>Horario de Vanuatu</generic>
					<standard>Horario estándar de Vanuatu</standard>
					<daylight>Horario de verán de Vanuatu</daylight>
				</long>
			</metazone>
			<metazone type="Venezuela">
				<long>
					<standard>Horario de Venezuela</standard>
				</long>
			</metazone>
			<metazone type="Vladivostok">
				<long>
					<generic>Horario de Vladivostok</generic>
					<standard>Horario estándar de Vladivostok</standard>
					<daylight>Horario de verán de Vladivostok</daylight>
				</long>
			</metazone>
			<metazone type="Volgograd">
				<long>
					<generic>Horario de Volgogrado</generic>
					<standard>Horario estándar de Volgogrado</standard>
					<daylight>Horario de verán de Volgogrado</daylight>
				</long>
			</metazone>
			<metazone type="Vostok">
				<long>
					<standard>Horario de Vostok</standard>
				</long>
			</metazone>
			<metazone type="Wake">
				<long>
					<standard>Horario da Illa Wake</standard>
				</long>
			</metazone>
			<metazone type="Wallis">
				<long>
					<standard>Horario de Wallis e Futuna</standard>
				</long>
			</metazone>
			<metazone type="Yakutsk">
				<long>
					<generic>Horario de Iakutsk</generic>
					<standard>Horario estándar de Yakutsk</standard>
					<daylight>Horario de verán de Yakutsk</daylight>
				</long>
			</metazone>
			<metazone type="Yekaterinburg">
				<long>
					<generic>Horario de Ekaterimburgo</generic>
					<standard>Horario estándar de Ekaterimburgo</standard>
					<daylight>Horario de verán de Ekaterimburgo</daylight>
				</long>
			</metazone>
		</timeZoneNames>
	</dates>
	<numbers>
		<symbols numberSystem="latn">
			<decimal>,</decimal>
			<group>.</group>
			<list>;</list>
			<percentSign>%</percentSign>
			<plusSign>+</plusSign>
			<minusSign>-</minusSign>
			<exponential>E</exponential>
			<superscriptingExponent>×</superscriptingExponent>
			<perMille>‰</perMille>
			<infinity>∞</infinity>
			<nan>NaN</nan>
		</symbols>
		<decimalFormats numberSystem="latn">
			<decimalFormatLength>
				<decimalFormat>
					<pattern>#,##0.###</pattern>
				</decimalFormat>
			</decimalFormatLength>
			<decimalFormatLength type="long">
				<decimalFormat>
					<pattern type="1000" count="one">0 mil</pattern>
					<pattern type="1000" count="other">0 mil</pattern>
					<pattern type="10000" count="one">00 mil</pattern>
					<pattern type="10000" count="other">00 mil</pattern>
					<pattern type="100000" count="one">000 mil</pattern>
					<pattern type="100000" count="other">000 mil</pattern>
					<pattern type="1000000" count="one">0 millón</pattern>
					<pattern type="1000000" count="other">0 millóns</pattern>
					<pattern type="10000000" count="one">00 millón</pattern>
					<pattern type="10000000" count="other">00 millóns</pattern>
					<pattern type="100000000" count="one">000 millón</pattern>
					<pattern type="100000000" count="other">000 millóns</pattern>
					<pattern type="1000000000" count="one">0 mil millóns</pattern>
					<pattern type="1000000000" count="other">0 mil millóns</pattern>
					<pattern type="10000000000" count="one">00 mil millóns</pattern>
					<pattern type="10000000000" count="other">00 mil millóns</pattern>
					<pattern type="100000000000" count="one">000 mil millóns</pattern>
					<pattern type="100000000000" count="other">000 mil millóns</pattern>
					<pattern type="1000000000000" count="one">0 billóns</pattern>
					<pattern type="1000000000000" count="other">0 billóns</pattern>
					<pattern type="10000000000000" count="one">00 billóns</pattern>
					<pattern type="10000000000000" count="other">00 billóns</pattern>
					<pattern type="100000000000000" count="one">000 billóns</pattern>
					<pattern type="100000000000000" count="other">000 billóns</pattern>
				</decimalFormat>
			</decimalFormatLength>
			<decimalFormatLength type="short">
				<decimalFormat>
					<pattern type="1000" count="one">0K</pattern>
					<pattern type="1000" count="other">0K</pattern>
					<pattern type="10000" count="one">00K</pattern>
					<pattern type="10000" count="other">00K</pattern>
					<pattern type="100000" count="one">000K</pattern>
					<pattern type="100000" count="other">000K</pattern>
					<pattern type="1000000" count="one">0M</pattern>
					<pattern type="1000000" count="other">0M</pattern>
					<pattern type="10000000" count="one">00M</pattern>
					<pattern type="10000000" count="other">00M</pattern>
					<pattern type="100000000" count="one">000M</pattern>
					<pattern type="100000000" count="other">000M</pattern>
					<pattern type="1000000000" count="one">0k M</pattern>
					<pattern type="1000000000" count="other">0k M</pattern>
					<pattern type="10000000000" count="one">00k M</pattern>
					<pattern type="10000000000" count="other">00k M</pattern>
					<pattern type="100000000000" count="one" draft="contributed">000k M</pattern>
					<pattern type="100000000000" count="other" draft="contributed">000k M</pattern>
					<pattern type="1000000000000" count="one" draft="contributed">0 B</pattern>
					<pattern type="1000000000000" count="other" draft="contributed">0 B</pattern>
					<pattern type="10000000000000" count="one" draft="contributed">00 B</pattern>
					<pattern type="10000000000000" count="other" draft="contributed">00 B</pattern>
					<pattern type="100000000000000" count="one" draft="contributed">000 B</pattern>
					<pattern type="100000000000000" count="other" draft="contributed">000 B</pattern>
				</decimalFormat>
			</decimalFormatLength>
		</decimalFormats>
		<scientificFormats numberSystem="latn">
			<scientificFormatLength>
				<scientificFormat>
					<pattern>#E0</pattern>
				</scientificFormat>
			</scientificFormatLength>
		</scientificFormats>
		<percentFormats numberSystem="latn">
			<percentFormatLength>
				<percentFormat>
					<pattern>#,##0%</pattern>
				</percentFormat>
			</percentFormatLength>
		</percentFormats>
		<currencyFormats numberSystem="latn">
			<currencyFormatLength>
				<currencyFormat type="standard">
					<pattern>¤#,##0.00</pattern>
				</currencyFormat>
				<currencyFormat type="accounting">
					<pattern>¤#,##0.00;(¤#,##0.00)</pattern>
				</currencyFormat>
			</currencyFormatLength>
			<unitPattern count="one">{0} {1}</unitPattern>
			<unitPattern count="other">{0} {1}</unitPattern>
		</currencyFormats>
		<currencies>
			<currency type="ADP">
				<displayName>peseta andorrana</displayName>
				<displayName count="one" draft="contributed">peseta andorrana</displayName>
				<displayName count="other" draft="contributed">pesetas andorranas</displayName>
			</currency>
			<currency type="AED">
				<displayName>Dirham dos Emiratos Árabes Unidos</displayName>
			</currency>
			<currency type="AFN">
				<displayName>Afgani afgano</displayName>
			</currency>
			<currency type="ALL">
				<displayName>Lek albanés</displayName>
			</currency>
			<currency type="AMD">
				<displayName>Dram armenio</displayName>
			</currency>
			<currency type="ANG">
				<displayName>Florín das Antillas Neerlandesas</displayName>
			</currency>
			<currency type="AOA">
				<displayName>Kwanza angoleño</displayName>
			</currency>
			<currency type="ARP">
				<displayName draft="contributed">Peso arxentino (1983–1985)</displayName>
				<displayName count="one" draft="contributed">peso arxentino (ARP)</displayName>
				<displayName count="other" draft="contributed">pesos arxentinos (ARP)</displayName>
			</currency>
			<currency type="ARS">
				<displayName draft="contributed">Peso arxentino</displayName>
				<displayName count="one" draft="contributed">peso arxentino</displayName>
				<displayName count="other" draft="contributed">pesos arxentinos</displayName>
			</currency>
			<currency type="AUD">
				<displayName>Dólar australiano</displayName>
				<displayName count="one">dólar australiano</displayName>
				<displayName count="other">dólares australianos</displayName>
			</currency>
			<currency type="AWG">
				<displayName>Florín arubeño</displayName>
			</currency>
			<currency type="AZN">
				<displayName>Manat acerbaixano</displayName>
			</currency>
			<currency type="BAM">
				<displayName>Marco convertible de Bosnia e Hercegovina</displayName>
			</currency>
			<currency type="BBD">
				<displayName>Dólar de Barbados</displayName>
			</currency>
			<currency type="BDT">
				<displayName>Taka de Bangladesh</displayName>
			</currency>
			<currency type="BEC">
				<displayName draft="contributed">Franco belga (convertible)</displayName>
				<displayName count="one" draft="contributed">franco belga (convertible)</displayName>
				<displayName count="other" draft="contributed">francos belgas (convertibles)</displayName>
			</currency>
			<currency type="BEF">
				<displayName draft="contributed">Franco belga</displayName>
				<displayName count="one" draft="contributed">franco belga</displayName>
				<displayName count="other" draft="contributed">francos belgas</displayName>
			</currency>
			<currency type="BEL">
				<displayName draft="contributed">Franco belga (financeiro)</displayName>
				<displayName count="one" draft="contributed">franco belga (financeiro)</displayName>
				<displayName count="other" draft="contributed">francos belgas (financeiros)</displayName>
			</currency>
			<currency type="BGN">
				<displayName>Lev búlgaro</displayName>
			</currency>
			<currency type="BHD">
				<displayName>Dinar de Baréin</displayName>
			</currency>
			<currency type="BIF">
				<displayName>Franco burundés</displayName>
			</currency>
			<currency type="BMD">
				<displayName>Dólar das Bemudas</displayName>
			</currency>
			<currency type="BND">
				<displayName>Dólar de Brunei</displayName>
			</currency>
			<currency type="BOB">
				<displayName draft="contributed">Boliviano</displayName>
				<displayName count="one" draft="contributed">boliviano</displayName>
				<displayName count="other" draft="contributed">bolivianos</displayName>
			</currency>
			<currency type="BOP">
				<displayName draft="contributed">Peso boliviano</displayName>
				<displayName count="one" draft="contributed">peso boliviano</displayName>
				<displayName count="other" draft="contributed">pesos bolivianos</displayName>
			</currency>
			<currency type="BOV">
				<displayName draft="contributed">MVDOL boliviano</displayName>
			</currency>
			<currency type="BRB">
				<displayName draft="contributed">Cruzeiro novo brasileiro (1967–1986)</displayName>
				<displayName count="one" draft="contributed">cruzeiro novo brasileiro</displayName>
				<displayName count="other" draft="contributed">cruzeiros novos brasileiros</displayName>
			</currency>
			<currency type="BRC">
				<displayName draft="contributed">Cruzado brasileiro</displayName>
				<displayName count="one" draft="contributed">cruzado brasileiro</displayName>
				<displayName count="other" draft="contributed">cruzados brasileiros</displayName>
			</currency>
			<currency type="BRE">
				<displayName draft="contributed">Cruzeiro brasileiro (1990–1993)</displayName>
				<displayName count="one" draft="contributed">cruzeiro brasileiro (BRE)</displayName>
				<displayName count="other" draft="contributed">cruzeiros brasileiros (BRE)</displayName>
			</currency>
			<currency type="BRL">
				<displayName>Real brasileiro</displayName>
				<displayName count="one">real brasileiro</displayName>
				<displayName count="other">reais brasileiros</displayName>
			</currency>
			<currency type="BRN">
				<displayName draft="contributed">Cruzado novo brasileiro</displayName>
				<displayName count="one" draft="contributed">cruzado novo brasileiro</displayName>
				<displayName count="other" draft="contributed">cruzados novos brasileiros</displayName>
			</currency>
			<currency type="BRR">
				<displayName draft="contributed">Cruzeiro brasileiro</displayName>
				<displayName count="one" draft="contributed">cruzeiro brasileiro</displayName>
				<displayName count="other" draft="contributed">cruzeiros brasileiros</displayName>
			</currency>
			<currency type="BSD">
				<displayName>Dólar das Bahamas</displayName>
			</currency>
			<currency type="BTN">
				<displayName>Ngultrum butanés</displayName>
			</currency>
			<currency type="BWP">
				<displayName>Pula botsuano</displayName>
			</currency>
			<currency type="BYR">
				<displayName>Rublo bielorruso</displayName>
			</currency>
			<currency type="BZD">
				<displayName>Dólar beliceño</displayName>
			</currency>
			<currency type="CAD">
				<displayName>Dólar canadiano</displayName>
				<displayName count="one">dólar canadiano</displayName>
				<displayName count="other">dólares canadianos</displayName>
				<symbol>$CA</symbol>
			</currency>
			<currency type="CDF">
				<displayName>Franco congolés</displayName>
			</currency>
			<currency type="CHF">
				<displayName>Franco suízo</displayName>
				<displayName count="one">franco suízo</displayName>
				<displayName count="other">francos suizos</displayName>
			</currency>
			<currency type="CLF">
				<displayName draft="contributed">Unidades de fomento chilenas</displayName>
				<displayName count="one" draft="contributed">unidade de fomento chilena</displayName>
				<displayName count="other" draft="contributed">unidades de fomento chilenas</displayName>
			</currency>
			<currency type="CLP">
				<displayName draft="contributed">Peso chileno</displayName>
				<displayName count="one" draft="contributed">peso chileno</displayName>
				<displayName count="other" draft="contributed">pesos chilenos</displayName>
			</currency>
			<currency type="CNY">
				<displayName>Iuán chinés</displayName>
				<displayName count="one">iuán chinés</displayName>
				<displayName count="other">iuáns chineses</displayName>
				<symbol>CN¥</symbol>
			</currency>
			<currency type="COP">
				<displayName draft="contributed">Peso colombiano</displayName>
				<displayName count="one" draft="contributed">peso colombiano</displayName>
				<displayName count="other" draft="contributed">pesos colombianos</displayName>
			</currency>
			<currency type="CRC">
				<displayName draft="contributed">Colón costarricense</displayName>
				<displayName count="one" draft="contributed">colón costarricense</displayName>
				<displayName count="other" draft="contributed">colóns costarricenses</displayName>
			</currency>
			<currency type="CUC">
				<displayName>Peso cubano convertible</displayName>
			</currency>
			<currency type="CUP">
				<displayName draft="contributed">Peso cubano</displayName>
				<displayName count="one">peso cubano</displayName>
				<displayName count="other">pesos cubanos</displayName>
			</currency>
			<currency type="CVE">
				<displayName>Escudo caboverdiano</displayName>
			</currency>
			<currency type="CZK">
				<displayName>Coroa checa</displayName>
			</currency>
			<currency type="DEM">
				<displayName draft="contributed">Marco alemán</displayName>
				<displayName count="one" draft="contributed">marco alemán</displayName>
				<displayName count="other" draft="contributed">marcos alemáns</displayName>
			</currency>
			<currency type="DJF">
				<displayName>Franco xibutiano</displayName>
			</currency>
			<currency type="DKK">
				<displayName>Coroa dinamarquesa</displayName>
				<displayName count="one">coroa dinamarquesa</displayName>
				<displayName count="other">coroas dinamarquesas</displayName>
			</currency>
			<currency type="DOP">
				<displayName draft="contributed">Peso dominicano</displayName>
				<displayName count="one" draft="contributed">peso dominicano</displayName>
				<displayName count="other" draft="contributed">pesos dominicanos</displayName>
			</currency>
			<currency type="DZD">
				<displayName>Dinar alxeriano</displayName>
			</currency>
			<currency type="ECS">
				<displayName draft="contributed">Sucre ecuatoriano</displayName>
				<displayName count="one" draft="contributed">sucre ecuatoriano</displayName>
				<displayName count="other" draft="contributed">sucres ecuatorianos</displayName>
			</currency>
			<currency type="ECV">
				<displayName draft="contributed">Unidade de valor constante ecuatoriana</displayName>
			</currency>
			<currency type="EGP">
				<displayName>Libra exipcia</displayName>
			</currency>
			<currency type="ERN">
				<displayName>Nakfa eritreo</displayName>
			</currency>
			<currency type="ESA">
				<displayName draft="contributed">Peseta española (conta A)</displayName>
			</currency>
			<currency type="ESB">
				<displayName draft="contributed">Peseta española (conta convertible)</displayName>
			</currency>
			<currency type="ESP">
				<pattern>#,##0.00 ¤</pattern>
				<displayName draft="contributed">Peseta española</displayName>
				<displayName count="one" draft="contributed">peseta</displayName>
				<displayName count="other" draft="contributed">pesetas</displayName>
				<symbol>₧</symbol>
				<decimal>,</decimal>
				<group>.</group>
			</currency>
			<currency type="ETB">
				<displayName>Birr etíope</displayName>
			</currency>
			<currency type="EUR">
				<displayName>Euro</displayName>
				<displayName count="one">euro</displayName>
				<displayName count="other">euros</displayName>
				<symbol>€</symbol>
			</currency>
			<currency type="FJD">
				<displayName>Dólar fixiano</displayName>
			</currency>
			<currency type="FKP">
				<displayName>Libra das Malvinas</displayName>
			</currency>
			<currency type="FRF">
				<displayName draft="contributed">Franco francés</displayName>
				<displayName count="one" draft="contributed">franco francés</displayName>
				<displayName count="other" draft="contributed">francos franceses</displayName>
			</currency>
			<currency type="GBP">
				<displayName>Libra esterlina</displayName>
				<displayName count="one">libra esterlina</displayName>
				<displayName count="other">libras esterlinas</displayName>
				<symbol>£</symbol>
			</currency>
			<currency type="GEL">
				<displayName>Lari xeorxiano</displayName>
			</currency>
			<currency type="GHS">
				<displayName>Cedi de Gana</displayName>
			</currency>
			<currency type="GIP">
				<displayName draft="contributed">Libra de Xibraltar</displayName>
				<displayName count="one" draft="contributed">libra xibraltareña</displayName>
				<displayName count="other" draft="contributed">libras xibraltareñas</displayName>
			</currency>
			<currency type="GMD">
				<displayName>Dalasi gambiano</displayName>
			</currency>
			<currency type="GNF">
				<displayName draft="contributed">Franco guineano</displayName>
			</currency>
			<currency type="GNS">
				<displayName draft="contributed">Syli guineano</displayName>
			</currency>
			<currency type="GQE">
				<displayName draft="contributed">Ekwele guineana</displayName>
			</currency>
			<currency type="GRD">
				<displayName draft="contributed">Dracma grego</displayName>
			</currency>
			<currency type="GTQ">
				<displayName draft="contributed">Quetzal guatemalteco</displayName>
			</currency>
			<currency type="GYD">
				<displayName>Dólar güianés</displayName>
			</currency>
			<currency type="HKD">
				<displayName>Dólar de Hong Kong</displayName>
				<displayName count="one">dólar de Hong Kong</displayName>
				<displayName count="other">dólares de Hong Kong</displayName>
				<symbol>$HK</symbol>
			</currency>
			<currency type="HNL">
				<displayName draft="contributed">Lempira hondureño</displayName>
			</currency>
			<currency type="HRK">
				<displayName>Kuna croata</displayName>
			</currency>
			<currency type="HTG">
				<displayName>Gourde haitiano</displayName>
			</currency>
			<currency type="HUF">
				<displayName draft="contributed">Florín húngaro</displayName>
			</currency>
			<currency type="IDR">
				<displayName>Rupia indonesia</displayName>
				<displayName count="one">rupia indonesia</displayName>
				<displayName count="other">rupias indonesias</displayName>
			</currency>
			<currency type="IEP">
				<displayName draft="contributed">Libra irlandesa</displayName>
				<displayName count="one" draft="contributed">libra irlandesa</displayName>
				<displayName count="other" draft="contributed">libras irlandesas</displayName>
			</currency>
			<currency type="ILS">
				<displayName>Novo shequel israelí</displayName>
			</currency>
			<currency type="INR">
				<displayName draft="contributed">Rupia india</displayName>
				<displayName count="one">rupia india</displayName>
				<displayName count="other">rupias indias</displayName>
				<symbol>₹</symbol>
			</currency>
			<currency type="IQD">
				<displayName>Dinar iraquí</displayName>
			</currency>
			<currency type="IRR">
				<displayName>Rial iraniano</displayName>
			</currency>
			<currency type="ISK">
				<displayName draft="contributed">Coroa islandesa</displayName>
			</currency>
			<currency type="ITL">
				<displayName draft="contributed">Lira italiana</displayName>
			</currency>
			<currency type="JMD">
				<displayName>Dólar xamaicano</displayName>
			</currency>
			<currency type="JOD">
				<displayName>Dinar xordano</displayName>
			</currency>
			<currency type="JPY">
				<displayName>Ien xaponés</displayName>
				<displayName count="one">ien xaponés</displayName>
				<displayName count="other">iens xaponeses</displayName>
				<symbol>¥JP</symbol>
			</currency>
			<currency type="KES">
				<displayName>Chelín kenyano</displayName>
			</currency>
			<currency type="KGS">
				<displayName>Som quirguizo</displayName>
			</currency>
			<currency type="KHR">
				<displayName>Riel camboxano</displayName>
			</currency>
			<currency type="KMF">
				<displayName>Franco comoriano</displayName>
			</currency>
			<currency type="KPW">
				<displayName>Won norcoreano</displayName>
			</currency>
			<currency type="KRW">
				<displayName>Won surcoreano</displayName>
				<displayName count="one">won surcoreano</displayName>
				<displayName count="other">wons surcoreanos</displayName>
				<symbol>₩</symbol>
			</currency>
			<currency type="KWD">
				<displayName>Dinar kuwaití</displayName>
			</currency>
			<currency type="KYD">
				<displayName>Dólar das Illas Caimán</displayName>
			</currency>
			<currency type="KZT">
				<displayName>Tenge casaco</displayName>
			</currency>
			<currency type="LAK">
				<displayName>Kip laosiano</displayName>
			</currency>
			<currency type="LBP">
				<displayName>Libra libanesa</displayName>
			</currency>
			<currency type="LKR">
				<displayName>Rupia de Sri Lanka</displayName>
			</currency>
			<currency type="LRD">
				<displayName>Dólar liberiano</displayName>
			</currency>
			<currency type="LSL">
				<displayName>Loti de Lesoto</displayName>
			</currency>
			<currency type="LTL">
				<displayName>Litas lituana</displayName>
			</currency>
			<currency type="LUC">
				<displayName draft="contributed">Franco convertible luxemburgués</displayName>
			</currency>
			<currency type="LUF">
				<displayName draft="contributed">Franco luxemburgués</displayName>
			</currency>
			<currency type="LUL">
				<displayName draft="contributed">Franco financeiro luxemburgués</displayName>
			</currency>
			<currency type="LVL">
				<displayName>Lats letón</displayName>
			</currency>
			<currency type="LYD">
				<displayName>Dinar libio</displayName>
			</currency>
			<currency type="MAD">
				<displayName draft="contributed">Dirham marroquí</displayName>
			</currency>
			<currency type="MAF">
				<displayName draft="contributed">Franco marroquí</displayName>
			</currency>
			<currency type="MDL">
				<displayName>Leu moldavo</displayName>
			</currency>
			<currency type="MGA">
				<displayName>Ariary malgaxe</displayName>
			</currency>
			<currency type="MKD">
				<displayName>Dinar macedonio</displayName>
			</currency>
			<currency type="MMK">
				<displayName>Kiat birmano</displayName>
			</currency>
			<currency type="MNT">
				<displayName>Tugrik mongol</displayName>
			</currency>
			<currency type="MOP">
				<displayName>Pataca de Macau</displayName>
			</currency>
			<currency type="MRO">
				<displayName>Ouguiya mauritano</displayName>
			</currency>
			<currency type="MUR">
				<displayName>Rupia de Mauricio</displayName>
			</currency>
			<currency type="MVR">
				<displayName>Rupia maldiva</displayName>
			</currency>
			<currency type="MWK">
				<displayName>Kwacha de Malaui</displayName>
			</currency>
			<currency type="MXN">
				<displayName>Peso mexicano</displayName>
				<displayName count="one">peso mexicano</displayName>
				<displayName count="other">pesos mexicanos</displayName>
				<symbol>$MX</symbol>
			</currency>
			<currency type="MXP">
				<displayName draft="contributed">Peso de prata mexicano (1861–1992)</displayName>
			</currency>
			<currency type="MXV">
				<displayName draft="contributed">Unidade de inversión mexicana</displayName>
			</currency>
			<currency type="MYR">
				<displayName>Ringgit malaio</displayName>
			</currency>
			<currency type="MZN">
				<displayName>Metical de Mozambique</displayName>
				<displayName count="one">metical de Mozambique</displayName>
				<displayName count="other">meticais de Mozambique</displayName>
			</currency>
			<currency type="NAD">
				<displayName>Dólar namibio</displayName>
			</currency>
			<currency type="NGN">
				<displayName>Naira nixeriano</displayName>
			</currency>
			<currency type="NIC">
				<displayName draft="contributed">Córdoba nicaragüense</displayName>
			</currency>
			<currency type="NIO">
				<displayName draft="contributed">Córdoba de ouro nicaragüense</displayName>
			</currency>
			<currency type="NLG">
				<displayName draft="contributed">Florín holandés</displayName>
			</currency>
			<currency type="NOK">
				<displayName>Coroa norueguesa</displayName>
				<displayName count="one">coroa norueguesa</displayName>
				<displayName count="other">coroas norueguesas</displayName>
			</currency>
			<currency type="NPR">
				<displayName>Rupia nepalesa</displayName>
			</currency>
			<currency type="NZD">
				<displayName>Dólar neozelandés</displayName>
			</currency>
			<currency type="OMR">
				<displayName>Rial omaní</displayName>
			</currency>
			<currency type="PAB">
				<displayName draft="contributed">Balboa panameño</displayName>
			</currency>
			<currency type="PEI">
				<displayName draft="contributed">Inti peruano</displayName>
			</currency>
			<currency type="PEN">
				<displayName draft="contributed">Sol novo peruano</displayName>
			</currency>
			<currency type="PES">
				<displayName draft="contributed">Sol peruano</displayName>
			</currency>
			<currency type="PGK">
				<displayName>Kina de Papúa Nova Guinea</displayName>
			</currency>
			<currency type="PHP">
				<displayName draft="contributed">Peso filipino</displayName>
			</currency>
			<currency type="PKR">
				<displayName>Rupia paquistaní</displayName>
			</currency>
			<currency type="PLN">
				<displayName>Zloty polaco</displayName>
				<displayName count="one">zloty polaco</displayName>
				<displayName count="other">zlotys polacos</displayName>
			</currency>
			<currency type="PTE">
				<displayName draft="contributed">Escudo portugués</displayName>
				<displayName count="one" draft="contributed">escudo portugués</displayName>
				<displayName count="other" draft="contributed">escudos portugueses</displayName>
			</currency>
			<currency type="PYG">
				<displayName draft="contributed">Guaraní paraguaio</displayName>
				<displayName count="one">Guaraní paraguaio</displayName>
				<displayName count="other">Guaranís paraguaios</displayName>
			</currency>
			<currency type="QAR">
				<displayName>Rial qatarí</displayName>
			</currency>
			<currency type="RON">
				<displayName>Leu romanés</displayName>
				<displayName count="one">leu romanés</displayName>
				<displayName count="other">lei romanés</displayName>
			</currency>
			<currency type="RSD">
				<displayName>Dinar serbio</displayName>
			</currency>
			<currency type="RUB">
				<displayName>Rublo ruso</displayName>
				<displayName count="one">rublo ruso</displayName>
				<displayName count="other">rublos rusos</displayName>
			</currency>
			<currency type="RUR">
				<displayName draft="contributed">Rublo ruso (1991–1998)</displayName>
			</currency>
			<currency type="RWF">
				<displayName>Franco ruandés</displayName>
			</currency>
			<currency type="SAR">
				<displayName>Rial saudita</displayName>
				<displayName count="one">rial saudita</displayName>
				<displayName count="other">riais sauditas</displayName>
			</currency>
			<currency type="SBD">
				<displayName>Dólar das Illas Salomón</displayName>
			</currency>
			<currency type="SCR">
				<displayName>Rupia de Seixeles</displayName>
			</currency>
			<currency type="SDG">
				<displayName>Libra sudanesa</displayName>
			</currency>
			<currency type="SEK">
				<displayName draft="contributed">Coroa sueca</displayName>
				<displayName count="one">coroa sueca</displayName>
				<displayName count="other">coroas suecas</displayName>
			</currency>
			<currency type="SGD">
				<displayName>Dólar de Singapur</displayName>
			</currency>
			<currency type="SHP">
				<displayName>Libra de Santa Helena</displayName>
			</currency>
			<currency type="SLL">
				<displayName>Leone de Serra Leoa</displayName>
			</currency>
			<currency type="SOS">
				<displayName>Chelín somalí</displayName>
			</currency>
			<currency type="SRD">
				<displayName>Dólar surinamés</displayName>
			</currency>
			<currency type="SSP">
				<displayName>Libra sursudanesa</displayName>
				<displayName count="one">libra sursudanesa</displayName>
				<displayName count="other">libras sursudanesa</displayName>
			</currency>
			<currency type="STD">
				<displayName>Dobra de San Tomé e Príncipe</displayName>
			</currency>
			<currency type="SUR">
				<displayName draft="contributed">Rublo soviético</displayName>
				<displayName count="one" draft="contributed">rublo soviético</displayName>
				<displayName count="other" draft="contributed">rublos soviéticos</displayName>
			</currency>
			<currency type="SVC">
				<displayName draft="contributed">Colón salvadoreño</displayName>
				<displayName count="one" draft="contributed">colón salvadoreño</displayName>
				<displayName count="other" draft="contributed">colóns salvadoreños</displayName>
			</currency>
			<currency type="SYP">
				<displayName>Libra siria</displayName>
			</currency>
			<currency type="SZL">
				<displayName>Lilanxeni de Suacilandia</displayName>
			</currency>
			<currency type="THB">
				<displayName>Baht tailandés</displayName>
				<displayName count="one">baht tailandés</displayName>
				<displayName count="other">bahts tailandeses</displayName>
				<symbol>฿</symbol>
			</currency>
			<currency type="TJS">
				<displayName>Somoni taxico</displayName>
			</currency>
			<currency type="TMT">
				<displayName>Manat turcomano</displayName>
			</currency>
			<currency type="TND">
				<displayName>Dinar tunesino</displayName>
			</currency>
			<currency type="TOP">
				<displayName>Paʻanga de Tonga</displayName>
			</currency>
			<currency type="TRY">
				<displayName>Lira turca</displayName>
				<displayName count="one">lira turca</displayName>
				<displayName count="other">liras turcas</displayName>
			</currency>
			<currency type="TTD">
				<displayName>Dólar de Trinidade e Tobago</displayName>
			</currency>
			<currency type="TWD">
				<displayName>Novo dólar taiwanés</displayName>
				<displayName count="one">novo dólar taiwanés</displayName>
				<displayName count="other">novos dólares taiwaneses</displayName>
			</currency>
			<currency type="TZS">
				<displayName>Chelín tanzano</displayName>
			</currency>
			<currency type="UAH">
				<displayName>Grivna ucraína</displayName>
			</currency>
			<currency type="UGX">
				<displayName>Chelín ugandés</displayName>
			</currency>
			<currency type="USD">
				<displayName>Dólar estadounidense</displayName>
				<displayName count="one">dólar estadounidense</displayName>
				<displayName count="other">dólares estadounidenses</displayName>
				<symbol>$</symbol>
			</currency>
			<currency type="UYI">
				<displayName draft="contributed">Peso en unidades indexadas uruguaio</displayName>
			</currency>
			<currency type="UYP">
				<displayName draft="contributed">Peso uruguaio (1975–1993)</displayName>
			</currency>
			<currency type="UYU">
				<displayName draft="contributed">Peso uruguaio</displayName>
				<displayName count="one" draft="contributed">peso uruguaio</displayName>
				<displayName count="other" draft="contributed">pesos uruguaios</displayName>
			</currency>
			<currency type="UZS">
				<displayName>Som usbeco</displayName>
			</currency>
			<currency type="VEB">
				<displayName draft="contributed">Bolívar venezolano (1871–2008)</displayName>
				<displayName count="one" draft="contributed">bolívar venezolano (1871–2008)</displayName>
				<displayName count="other" draft="contributed">bolívares venezolanos (1871–2008)</displayName>
			</currency>
			<currency type="VEF">
				<displayName draft="contributed">Bolívar venezolano</displayName>
				<displayName count="one" draft="contributed">bolívar venezolano</displayName>
				<displayName count="other" draft="contributed">bolívares venezolanos</displayName>
			</currency>
			<currency type="VND">
				<displayName>Dong vietnamita</displayName>
			</currency>
			<currency type="VUV">
				<displayName>Vatu vanuatense</displayName>
			</currency>
			<currency type="WST">
				<displayName>Tala samoano</displayName>
			</currency>
			<currency type="XAF">
				<displayName>Franco CFA BEAC</displayName>
			</currency>
			<currency type="XAG">
				<displayName draft="contributed">Prata</displayName>
			</currency>
			<currency type="XAU">
				<displayName draft="contributed">Ouro</displayName>
			</currency>
			<currency type="XCD">
				<displayName>Dólar Caribe-Leste</displayName>
			</currency>
			<currency type="XOF">
				<displayName>Franco CFA BCEAO</displayName>
			</currency>
			<currency type="XPD">
				<displayName draft="contributed">Paladio</displayName>
			</currency>
			<currency type="XPF">
				<displayName>Franco CFP</displayName>
			</currency>
			<currency type="XPT">
				<displayName draft="contributed">Platino</displayName>
			</currency>
			<currency type="XXX">
				<displayName>Unidade monetaria descoñecida ou non válida</displayName>
				<displayName count="one">unidade monetaria descoñecida ou non válida</displayName>
				<displayName count="other">unidades monetarias descoñecidas ou non válidas</displayName>
			</currency>
			<currency type="YER">
				<displayName>Rial iemení</displayName>
			</currency>
			<currency type="ZAR">
				<displayName>Rand sudafricano</displayName>
				<displayName count="one">rand sudafricano</displayName>
				<displayName count="other">rands sudafricanos</displayName>
			</currency>
			<currency type="ZMK">
				<displayName>Kwacha zambiano (1968–2012)</displayName>
			</currency>
			<currency type="ZMW">
				<displayName>Kwacha zambiano</displayName>
			</currency>
		</currencies>
		<miscPatterns numberSystem="latn">
			<pattern type="atLeast">⩾{0}</pattern>
			<pattern type="range">{0}–{1}</pattern>
		</miscPatterns>
	</numbers>
	<units>
		<unitLength type="long">
			<unit type="duration-day">
				<unitPattern count="one">{0} día</unitPattern>
				<unitPattern count="other">{0} días</unitPattern>
			</unit>
			<unit type="duration-hour">
				<unitPattern count="one">{0} hora</unitPattern>
				<unitPattern count="other">{0} horas</unitPattern>
			</unit>
			<unit type="duration-millisecond">
				<unitPattern count="one">{0} milisegundo</unitPattern>
				<unitPattern count="other">{0} milisegundos</unitPattern>
			</unit>
			<unit type="duration-minute">
				<unitPattern count="one">{0} minuto</unitPattern>
				<unitPattern count="other">{0} minutos</unitPattern>
			</unit>
			<unit type="duration-month">
				<unitPattern count="one">{0} mes</unitPattern>
				<unitPattern count="other">{0} meses</unitPattern>
			</unit>
			<unit type="duration-second">
				<unitPattern count="one">{0} segundo</unitPattern>
				<unitPattern count="other">{0} segundos</unitPattern>
			</unit>
			<unit type="duration-week">
				<unitPattern count="one">{0} semana</unitPattern>
				<unitPattern count="other">{0} semanas</unitPattern>
			</unit>
			<unit type="duration-year">
				<unitPattern count="one">{0} ano</unitPattern>
				<unitPattern count="other">{0} anos</unitPattern>
			</unit>
			<unit type="length-centimeter">
				<unitPattern count="one">{0} centímetro</unitPattern>
				<unitPattern count="other">{0} centímetros</unitPattern>
			</unit>
			<unit type="length-kilometer">
				<unitPattern count="one">{0} quilómetro</unitPattern>
				<unitPattern count="other">{0} quilómetros</unitPattern>
			</unit>
			<unit type="length-meter">
				<unitPattern count="one">{0} metro</unitPattern>
				<unitPattern count="other">{0} metros</unitPattern>
			</unit>
			<unit type="length-millimeter">
				<unitPattern count="one">{0} milímetro</unitPattern>
				<unitPattern count="other">{0} milímetros</unitPattern>
			</unit>
			<unit type="mass-gram">
				<unitPattern count="one">{0} gramo</unitPattern>
				<unitPattern count="other">{0} gramos</unitPattern>
			</unit>
			<unit type="mass-kilogram">
				<unitPattern count="one">{0} quilogramo</unitPattern>
				<unitPattern count="other">{0} quilogramos</unitPattern>
			</unit>
			<unit type="speed-kilometer-per-hour">
				<unitPattern count="one">{0} quilómetro por hora</unitPattern>
				<unitPattern count="other">{0} quilómetros por hora</unitPattern>
			</unit>
			<unit type="temperature-celsius">
				<unitPattern count="one">{0}grado Celsius</unitPattern>
				<unitPattern count="other">{0}grados Celsius</unitPattern>
			</unit>
			<unit type="volume-liter">
				<unitPattern count="one">{0} litro</unitPattern>
				<unitPattern count="other">{0} litros</unitPattern>
			</unit>
		</unitLength>
		<unitLength type="short">
			<unit type="duration-day">
				<unitPattern count="one">{0} día</unitPattern>
				<unitPattern count="other">{0} días</unitPattern>
			</unit>
			<unit type="duration-hour">
				<unitPattern count="one">{0} hora</unitPattern>
				<unitPattern count="other">{0} horas</unitPattern>
			</unit>
			<unit type="duration-millisecond">
				<unitPattern count="one">{0} miliseg</unitPattern>
				<unitPattern count="other">{0} miliseg</unitPattern>
			</unit>
			<unit type="duration-minute">
				<unitPattern count="one">{0} min</unitPattern>
				<unitPattern count="other">{0} min</unitPattern>
			</unit>
			<unit type="duration-month">
				<unitPattern count="one">{0} mes</unitPattern>
				<unitPattern count="other">{0} meses</unitPattern>
			</unit>
			<unit type="duration-second">
				<unitPattern count="one">{0} seg</unitPattern>
				<unitPattern count="other">{0} seg</unitPattern>
			</unit>
			<unit type="duration-week">
				<unitPattern count="one">{0} sem</unitPattern>
				<unitPattern count="other">{0} sem</unitPattern>
			</unit>
			<unit type="duration-year">
				<unitPattern count="one">{0} ano</unitPattern>
				<unitPattern count="other">{0} anos</unitPattern>
			</unit>
			<unit type="length-centimeter">
				<unitPattern count="one">{0} cent</unitPattern>
				<unitPattern count="other">{0} cent</unitPattern>
			</unit>
			<unit type="length-kilometer">
				<unitPattern count="one">{0} quilóm</unitPattern>
				<unitPattern count="other">{0} quilóm</unitPattern>
			</unit>
			<unit type="length-meter">
				<unitPattern count="one">{0} met</unitPattern>
				<unitPattern count="other">{0} met</unitPattern>
			</unit>
			<unit type="length-millimeter">
				<unitPattern count="one">{0} milím</unitPattern>
				<unitPattern count="other">{0} milím</unitPattern>
			</unit>
			<unit type="mass-gram">
				<unitPattern count="one">{0} gram</unitPattern>
				<unitPattern count="other">{0} gram</unitPattern>
			</unit>
			<unit type="mass-kilogram">
				<unitPattern count="one">{0} quilog</unitPattern>
				<unitPattern count="other">{0} quilog</unitPattern>
			</unit>
			<unit type="speed-kilometer-per-hour">
				<unitPattern count="one">{0} km por hora</unitPattern>
				<unitPattern count="other">{0} km por hora</unitPattern>
			</unit>
			<unit type="temperature-celsius">
				<unitPattern count="one">{0}g Celsius</unitPattern>
				<unitPattern count="other">{0} g Celsius</unitPattern>
			</unit>
			<unit type="volume-liter">
				<unitPattern count="one">{0} lit</unitPattern>
				<unitPattern count="other">{0} lit</unitPattern>
			</unit>
		</unitLength>
		<unitLength type="narrow">
			<unit type="duration-day">
				<unitPattern count="one">{0} d</unitPattern>
				<unitPattern count="other">{0} d</unitPattern>
			</unit>
			<unit type="duration-hour">
				<unitPattern count="one">{0} h</unitPattern>
				<unitPattern count="other">{0} h</unitPattern>
			</unit>
			<unit type="duration-millisecond">
				<unitPattern count="one">{0} ms</unitPattern>
				<unitPattern count="other">{0} ms</unitPattern>
			</unit>
			<unit type="duration-minute">
				<unitPattern count="one">{0} m</unitPattern>
				<unitPattern count="other">{0} m</unitPattern>
			</unit>
			<unit type="duration-month">
				<unitPattern count="one">{0} m</unitPattern>
				<unitPattern count="other">{0} m</unitPattern>
			</unit>
			<unit type="duration-second">
				<unitPattern count="one">{0} s</unitPattern>
				<unitPattern count="other">{0} s</unitPattern>
			</unit>
			<unit type="duration-week">
				<unitPattern count="one">{0} s</unitPattern>
				<unitPattern count="other">{0} s</unitPattern>
			</unit>
			<unit type="duration-year">
				<unitPattern count="one">{0} a</unitPattern>
				<unitPattern count="other">{0} a</unitPattern>
			</unit>
			<unit type="length-centimeter">
				<unitPattern count="one">{0} cm</unitPattern>
				<unitPattern count="other">{0} cm</unitPattern>
			</unit>
			<unit type="length-kilometer">
				<unitPattern count="one">{0} km</unitPattern>
				<unitPattern count="other">{0} km</unitPattern>
			</unit>
			<unit type="length-meter">
				<unitPattern count="one">{0} m</unitPattern>
				<unitPattern count="other">{0} m</unitPattern>
			</unit>
			<unit type="length-millimeter">
				<unitPattern count="one">{0} mm</unitPattern>
				<unitPattern count="other">{0} mm</unitPattern>
			</unit>
			<unit type="mass-gram">
				<unitPattern count="one">{0} g</unitPattern>
				<unitPattern count="other">{0} g</unitPattern>
			</unit>
			<unit type="mass-kilogram">
				<unitPattern count="one">{0} kg</unitPattern>
				<unitPattern count="other">{0} kg</unitPattern>
			</unit>
			<unit type="speed-kilometer-per-hour">
				<unitPattern count="one">{0} km/h</unitPattern>
				<unitPattern count="other">{0} km/h</unitPattern>
			</unit>
			<unit type="temperature-celsius">
				<unitPattern count="one">{0}°</unitPattern>
				<unitPattern count="other">{0}°</unitPattern>
			</unit>
			<unit type="temperature-fahrenheit">
				<unitPattern count="one">{0}°F</unitPattern>
				<unitPattern count="other">{0}°F</unitPattern>
			</unit>
			<unit type="volume-liter">
				<unitPattern count="one">{0} l</unitPattern>
				<unitPattern count="other">{0} l</unitPattern>
			</unit>
		</unitLength>
		<durationUnit type="hm">
			<durationUnitPattern>h:mm</durationUnitPattern>
		</durationUnit>
		<durationUnit type="hms">
			<durationUnitPattern>h:mm:ss</durationUnitPattern>
		</durationUnit>
		<durationUnit type="ms">
			<durationUnitPattern>m:ss</durationUnitPattern>
		</durationUnit>
	</units>
	<listPatterns>
		<listPattern>
			<listPatternPart type="start">{0}, {1}</listPatternPart>
			<listPatternPart type="middle">{0}, {1}</listPatternPart>
			<listPatternPart type="end">{0} e {1}</listPatternPart>
			<listPatternPart type="2">{0} e {1}</listPatternPart>
		</listPattern>
	</listPatterns>
	<posix>
		<messages>
			<yesstr draft="contributed">si:s</yesstr>
			<nostr draft="contributed">non:n</nostr>
		</messages>
	</posix>
</ldml>

