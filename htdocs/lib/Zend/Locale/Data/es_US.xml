<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE ldml SYSTEM "../../common/dtd/ldml.dtd">
<!-- Copyright © 1991-2013 Unicode, Inc.
CLDR data files are interpreted according to the LDML specification (http://unicode.org/reports/tr35/)
For terms of use, see http://www.unicode.org/copyright.html
-->
<ldml>
	<identity>
		<version number="$Revision: 9876 $"/>
		<generation date="$Date: 2014-03-05 23:14:25 -0600 (Wed, 05 Mar 2014) $"/>
		<language type="es"/>
		<territory type="US"/>
	</identity>
	<dates>
		<calendars>
			<calendar type="generic">
				<dateFormats>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>MMM d, y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>M/d/yy GGGGG</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<dateTimeFormats>
					<availableFormats>
						<dateFormatItem id="GyMMMd">MMM d, y G</dateFormatItem>
						<dateFormatItem id="GyMMMEd">E, MMM d, y G</dateFormatItem>
						<dateFormatItem id="GyMMMM">MMMM 'de' y G</dateFormatItem>
						<dateFormatItem id="GyMMMMd">d 'de' MMMM 'de' y G</dateFormatItem>
						<dateFormatItem id="GyMMMMEd">E, d 'de' MMMM 'de' y G</dateFormatItem>
						<dateFormatItem id="Md">M/d</dateFormatItem>
						<dateFormatItem id="MEd">E, MM/dd</dateFormatItem>
						<dateFormatItem id="MMd">MM/d</dateFormatItem>
						<dateFormatItem id="MMMd">MMM d</dateFormatItem>
						<dateFormatItem id="MMMEd">E, MMM d</dateFormatItem>
						<dateFormatItem id="MMMMEd">E, d 'de' MMMM</dateFormatItem>
						<dateFormatItem id="yyyyM">M/y G</dateFormatItem>
						<dateFormatItem id="yyyyMd">M/d/y G</dateFormatItem>
						<dateFormatItem id="yyyyMEd">E, M/d/y G</dateFormatItem>
						<dateFormatItem id="yyyyMMMd">MMM d, y G</dateFormatItem>
						<dateFormatItem id="yyyyMMMEd">E, MMM d, y G</dateFormatItem>
						<dateFormatItem id="yyyyMMMMd">d 'de' MMMM 'de' y G</dateFormatItem>
						<dateFormatItem id="yyyyMMMMEd">E, d 'de' MMMM 'de' y G</dateFormatItem>
					</availableFormats>
					<intervalFormats>
						<intervalFormatFallback draft="unconfirmed">{0} a el {1}</intervalFormatFallback>
						<intervalFormatItem id="d">
							<greatestDifference id="d" draft="unconfirmed">d-d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="h">
							<greatestDifference id="a" draft="unconfirmed">h a - h a</greatestDifference>
							<greatestDifference id="h" draft="unconfirmed">h-h a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hm">
							<greatestDifference id="a" draft="unconfirmed">h:mm a - h:mm a</greatestDifference>
							<greatestDifference id="h" draft="unconfirmed">h:mm-h:mm a</greatestDifference>
							<greatestDifference id="m" draft="unconfirmed">h:mm-h:mm a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hmv">
							<greatestDifference id="a" draft="unconfirmed">h:mm a - h:mm a v</greatestDifference>
							<greatestDifference id="h" draft="unconfirmed">h:mm-h:mm a v</greatestDifference>
							<greatestDifference id="m" draft="unconfirmed">h:mm-h:mm a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hv">
							<greatestDifference id="a" draft="unconfirmed">h a - h a v</greatestDifference>
							<greatestDifference id="h" draft="unconfirmed">h-h a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="M">
							<greatestDifference id="M" draft="unconfirmed">M-M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Md">
							<greatestDifference id="d" draft="unconfirmed">M/d - M/d</greatestDifference>
							<greatestDifference id="M" draft="unconfirmed">M/d - M/d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MEd">
							<greatestDifference id="d" draft="unconfirmed">E M/d - E M/d</greatestDifference>
							<greatestDifference id="M" draft="unconfirmed">E M/d - E M/d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMM">
							<greatestDifference id="M" draft="unconfirmed">MMM-MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMd">
							<greatestDifference id="d" draft="unconfirmed">d-d 'de' MMM</greatestDifference>
							<greatestDifference id="M" draft="unconfirmed">d 'de' MMM 'al' d 'de' MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMEd">
							<greatestDifference id="d" draft="unconfirmed">E d 'al' E d 'de' MMM</greatestDifference>
							<greatestDifference id="M" draft="unconfirmed">E d 'de' MMM 'al' E d 'de' MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="y">
							<greatestDifference id="y" draft="unconfirmed">y-y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yM">
							<greatestDifference id="M" draft="unconfirmed">M/y - M/y G</greatestDifference>
							<greatestDifference id="y" draft="unconfirmed">M/y - M/y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMd">
							<greatestDifference id="d" draft="unconfirmed">M/d/y - M/d/y G</greatestDifference>
							<greatestDifference id="M" draft="unconfirmed">M/d/y - M/d/y G</greatestDifference>
							<greatestDifference id="y" draft="unconfirmed">M/d/y - M/d/y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMEd">
							<greatestDifference id="d" draft="unconfirmed">E M/d/y - E M/d/y G</greatestDifference>
							<greatestDifference id="M" draft="unconfirmed">E M/d/y - E M/d/y G</greatestDifference>
							<greatestDifference id="y" draft="unconfirmed">E M/d/y - E M/d/y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMM">
							<greatestDifference id="M" draft="unconfirmed">MMM-MMM 'de' y G</greatestDifference>
							<greatestDifference id="y" draft="unconfirmed">MMM 'de' y 'a' MMM 'de' y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMd">
							<greatestDifference id="d" draft="unconfirmed">d-d 'de' MMM 'de' y G</greatestDifference>
							<greatestDifference id="M" draft="unconfirmed">d 'de' MMM 'al' d 'de' MMM 'de' y G</greatestDifference>
							<greatestDifference id="y" draft="unconfirmed">d 'de' MMM 'de' y 'al' d 'de' MMM 'de' y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMEd">
							<greatestDifference id="d" draft="unconfirmed">E d 'al' E d 'de' MMM 'de' y G</greatestDifference>
							<greatestDifference id="M" draft="unconfirmed">E d 'de' MMM 'al' E d 'de' MMM 'de' y G</greatestDifference>
							<greatestDifference id="y" draft="unconfirmed">E d 'de' MMM 'de' y 'al' E d 'de' MMM 'de' y G</greatestDifference>
						</intervalFormatItem>
					</intervalFormats>
				</dateTimeFormats>
			</calendar>
			<calendar type="gregorian">
				<dayPeriods>
					<dayPeriodContext type="format">
						<dayPeriodWidth type="wide">
							<dayPeriod type="am">AM</dayPeriod>
							<dayPeriod type="pm">PM</dayPeriod>
						</dayPeriodWidth>
					</dayPeriodContext>
				</dayPeriods>
				<dateFormats>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>MMM d, y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>M/d/yy</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<timeFormats>
					<timeFormatLength type="full">
						<timeFormat>
							<pattern>h:mm:ss a zzzz</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="long">
						<timeFormat>
							<pattern>h:mm:ss a z</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="medium">
						<timeFormat>
							<pattern>h:mm:ss a</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="short">
						<timeFormat>
							<pattern>h:mm a</pattern>
						</timeFormat>
					</timeFormatLength>
				</timeFormats>
				<dateTimeFormats>
					<availableFormats>
						<dateFormatItem id="GyMMM">MMM y G</dateFormatItem>
						<dateFormatItem id="GyMMMd">MMM d, y G</dateFormatItem>
						<dateFormatItem id="GyMMMEd">E, MMM d, y G</dateFormatItem>
						<dateFormatItem id="GyMMMM">MMMM 'de' y G</dateFormatItem>
						<dateFormatItem id="GyMMMMd">d 'de' MMMM 'de' y G</dateFormatItem>
						<dateFormatItem id="GyMMMMEd">E, d 'de' MMMM 'de' y G</dateFormatItem>
						<dateFormatItem id="Md">M/d</dateFormatItem>
						<dateFormatItem id="MEd">E, MM/dd</dateFormatItem>
						<dateFormatItem id="MMd">MM/d</dateFormatItem>
						<dateFormatItem id="MMMd">MMM d</dateFormatItem>
						<dateFormatItem id="MMMEd">E, MMM d</dateFormatItem>
						<dateFormatItem id="MMMMEd">E, d 'de' MMMM</dateFormatItem>
						<dateFormatItem id="yMd">M/d/y</dateFormatItem>
						<dateFormatItem id="yMEd">E, M/d/y</dateFormatItem>
						<dateFormatItem id="yMMMd">MMM d, y</dateFormatItem>
						<dateFormatItem id="yMMMEd">E, MMM d, y</dateFormatItem>
						<dateFormatItem id="yMMMMd">d 'de' MMMM 'de' y</dateFormatItem>
						<dateFormatItem id="yMMMMEd">E, d 'de' MMMM 'de' y</dateFormatItem>
					</availableFormats>
					<intervalFormats>
						<intervalFormatFallback draft="unconfirmed">{0} a el {1}</intervalFormatFallback>
						<intervalFormatItem id="d">
							<greatestDifference id="d" draft="unconfirmed">d-d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="h">
							<greatestDifference id="a" draft="unconfirmed">h a - h a</greatestDifference>
							<greatestDifference id="h" draft="unconfirmed">h-h a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hm">
							<greatestDifference id="a" draft="unconfirmed">h:mm a - h:mm a</greatestDifference>
							<greatestDifference id="h" draft="unconfirmed">h:mm-h:mm a</greatestDifference>
							<greatestDifference id="m" draft="unconfirmed">h:mm-h:mm a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hmv">
							<greatestDifference id="a" draft="unconfirmed">h:mm a - h:mm a v</greatestDifference>
							<greatestDifference id="h" draft="unconfirmed">h:mm-h:mm a v</greatestDifference>
							<greatestDifference id="m" draft="unconfirmed">h:mm-h:mm a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hv">
							<greatestDifference id="a" draft="unconfirmed">h a - h a v</greatestDifference>
							<greatestDifference id="h" draft="unconfirmed">h-h a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="M">
							<greatestDifference id="M" draft="unconfirmed">M-M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Md">
							<greatestDifference id="d" draft="unconfirmed">M/d - M/d</greatestDifference>
							<greatestDifference id="M" draft="unconfirmed">M/d - M/d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MEd">
							<greatestDifference id="d" draft="unconfirmed">E M/d - E M/d</greatestDifference>
							<greatestDifference id="M" draft="unconfirmed">E M/d - E M/d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMM">
							<greatestDifference id="M" draft="unconfirmed">MMM-MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMd">
							<greatestDifference id="d" draft="unconfirmed">d-d 'de' MMM</greatestDifference>
							<greatestDifference id="M" draft="unconfirmed">d 'de' MMM 'al' d 'de' MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMEd">
							<greatestDifference id="d" draft="unconfirmed">E d 'al' E d 'de' MMM</greatestDifference>
							<greatestDifference id="M" draft="unconfirmed">E d 'de' MMM 'al' E d 'de' MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="y">
							<greatestDifference id="y" draft="unconfirmed">y-y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yM">
							<greatestDifference id="M" draft="unconfirmed">M/y - M/y</greatestDifference>
							<greatestDifference id="y" draft="unconfirmed">M/y - M/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMd">
							<greatestDifference id="d" draft="unconfirmed">M/d/y - M/d/y</greatestDifference>
							<greatestDifference id="M" draft="unconfirmed">M/d/y - M/d/y</greatestDifference>
							<greatestDifference id="y" draft="unconfirmed">M/d/y - M/d/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMEd">
							<greatestDifference id="d" draft="unconfirmed">E M/d/y - E M/d/y</greatestDifference>
							<greatestDifference id="M" draft="unconfirmed">E M/d/y - E M/d/y</greatestDifference>
							<greatestDifference id="y" draft="unconfirmed">E M/d/y - E M/d/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMM">
							<greatestDifference id="M" draft="unconfirmed">MMM-MMM 'de' y</greatestDifference>
							<greatestDifference id="y" draft="unconfirmed">MMM 'de' y 'a' MMM 'de' y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMd">
							<greatestDifference id="d" draft="unconfirmed">d-d 'de' MMM 'de' y</greatestDifference>
							<greatestDifference id="M" draft="unconfirmed">d 'de' MMM 'al' d 'de' MMM 'de' y</greatestDifference>
							<greatestDifference id="y" draft="unconfirmed">d 'de' MMM 'de' y 'al' d 'de' MMM 'de' y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMEd">
							<greatestDifference id="d" draft="unconfirmed">E d 'al' E d 'de' MMM 'de' y</greatestDifference>
							<greatestDifference id="M" draft="unconfirmed">E d 'de' MMM 'al' E d 'de' MMM 'de' y</greatestDifference>
							<greatestDifference id="y" draft="unconfirmed">E d 'de' MMM 'de' y 'al' E d 'de' MMM 'de' y</greatestDifference>
						</intervalFormatItem>
					</intervalFormats>
				</dateTimeFormats>
			</calendar>
		</calendars>
		<timeZoneNames>
			<zone type="Pacific/Honolulu">
				<short>
					<generic>HST</generic>
					<standard>HST</standard>
					<daylight>HDT</daylight>
				</short>
			</zone>
			<metazone type="Alaska">
				<short>
					<generic>AKT</generic>
					<standard>AKST</standard>
					<daylight>AKDT</daylight>
				</short>
			</metazone>
			<metazone type="America_Central">
				<short>
					<generic>CT</generic>
					<standard>CST</standard>
					<daylight>CDT</daylight>
				</short>
			</metazone>
			<metazone type="America_Eastern">
				<short>
					<generic>ET</generic>
					<standard>EST</standard>
					<daylight>EDT</daylight>
				</short>
			</metazone>
			<metazone type="America_Mountain">
				<short>
					<generic>MT</generic>
					<standard>MST</standard>
					<daylight>MDT</daylight>
				</short>
			</metazone>
			<metazone type="America_Pacific">
				<short>
					<generic>PT</generic>
					<standard>PST</standard>
					<daylight>PDT</daylight>
				</short>
			</metazone>
			<metazone type="Atlantic">
				<short>
					<generic>AT</generic>
					<standard>AST</standard>
					<daylight>ADT</daylight>
				</short>
			</metazone>
			<metazone type="Hawaii_Aleutian">
				<short>
					<generic>HAT</generic>
					<standard>HAST</standard>
					<daylight>HADT</daylight>
				</short>
			</metazone>
		</timeZoneNames>
	</dates>
	<numbers>
		<currencies>
			<currency type="JPY">
				<symbol>¥</symbol>
			</currency>
			<currency type="USD">
				<symbol>$</symbol>
			</currency>
		</currencies>
	</numbers>
	<units>
		<unitLength type="narrow">
			<unit type="temperature-celsius">
				<unitPattern count="one">{0}°C</unitPattern>
				<unitPattern count="other">{0}°C</unitPattern>
			</unit>
			<unit type="temperature-fahrenheit">
				<unitPattern count="one">{0}°</unitPattern>
				<unitPattern count="other">{0}°</unitPattern>
			</unit>
		</unitLength>
	</units>
</ldml>

