<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE supplementalData SYSTEM "../../common/dtd/ldmlSupplemental.dtd">
<!--
Copyright © 1991-2014 Unicode, Inc.
CLDR data files are interpreted according to the LDML specification (http://unicode.org/reports/tr35/)
For terms of use, see http://www.unicode.org/copyright.html
-->

<supplementalData>
	<version number="$Revision: 9732 $"/>
	<generation date="$Date: 2014-02-13 11:57:02 -0600 (Thu, 13 Feb 2014) $"/>
	<coverageLevels>
		<approvalRequirements>
			<!--  "high bar" items -->
			<approvalRequirement votes="20" locales="*" paths="//ldml/numbers/symbols[^/]++/(decimal|group)"/>
			<!--  established locales - http://cldr.unicode.org/index/process#TOC-Draft-Status-of-Optimal-Field-Value -->
			<approvalRequirement votes="8" locales="ar ca cs da de el es fi fr he hi hr hu it ja ko nb nl pl pt pt_PT ro ru sk sl sr sv th tr uk vi zh zh_Hant" paths=""/>
			<!--  all other items -->
			<approvalRequirement votes="4" locales="*" paths=""/>
		</approvalRequirements>
		
		<coverageVariable key="%acctPattern" value="[@type='accounting']/pattern[@type='standard']"/>
		<coverageVariable key="%allPlurals" value="(zero|one|two|few|many|other)"/>
		<coverageVariable key="%allWidths" value="(wide|abbreviated|narrow)"/>
		<coverageVariable key="%ampmTypes" value="(am|pm|noon|earlyMorning|morning|lateMorning|midDay|earlyAfternoon|afternoon|evening|night|weeHours)"/>
		<coverageVariable key="%arabextLanguages" value="(fa|pa|ps|uz)"/>
		<coverageVariable key="%calendarType100" value="(buddhist|chinese|coptic|dangi|ethiopic(-amete-alem)?|hebrew|indian|islamic(-(civil|rgsa|tbla|umalqura))?|iso8601|japanese|persian|roc)"/>
		<coverageVariable key="%CJK_Languages" value="(ja|ko|zh)"/>
		<coverageVariable key="%chineseCalendarTerritories" value="(CN|CX|HK|MO|SG|TW)"/>
		<coverageVariable key="%collationType80" value="(ducet|search)"/>
		<coverageVariable key="%collationType100" value="(big5han|dictionary|eor|gb2312han|phonebook|phonetic|pinyin|reformed|searchjl|stroke|traditional|unihan|zhuyin)"/>
		<coverageVariable key="%collationAlternateValues" value="(non-ignorable|shifted)"/>
		<coverageVariable key="%collationCases" value="(upper|lower)"/>
		<coverageVariable key="%collationStrengths" value="(primary|secondary|tertiary|quaternary|identical)"/>
		<coverageVariable key="%collationYesNoOptions" value="(colBackwards|colCaseFirst|colCaseLevel|colHiraganaQuaternary|colNormalization|colNumeric)"/>
		<coverageVariable key="%compactDecimalTypes" value="(10{3,14})"/>
		<coverageVariable key="%contextTypes" value="(format|stand-alone)"/>
		<coverageVariable key="%currency30" value="(XXX)"/>
		<coverageVariable key="%currency40" value="(BRL|CNY|EUR|GBP|INR|JPY|RUB|USD)"/>
		<coverageVariable key="%currency60" value="(AUD|CAD|CHF|DKK|HKD|IDR|KRW|MXN|NOK|PLN|SAR|SEK|THB|TRY|TWD|ZAR)"/>
		<coverageVariable key="%currency60_EU" value="(CZK|HUF|LTL|LVL)"/>
		<coverageVariable key="%currency80" value="(AED|AFN|ALL|AMD|ANG|AOA|ARS|AWG|AZN|BAM|BBD|BDT|BGN|BHD|BIF|BMD|BND|BOB|BSD|BTN|BWP|BYR|BZD|CDF|CLP|COP|CRC|CUC|CUP|CVE|CZK|DJF|DOP|DZD|EGP|ERN|ETB|FJD|FKP|GEL|GHS|GIP|GMD|GNF|GTQ|GYD|HNL|HRK|HTG|HUF|ILS|IQD|IRR|ISK|JMD|JOD|KES|KGS|KHR|KMF|KPW|KWD|KYD|KZT|LAK|LBP|LKR|LRD|LTL|LVL|LYD|MAD|MDL|MGA|MKD|MMK|MNT|MOP|MRO|MUR|MVR|MWK|MYR|MZN|NAD|NGN|NIO|NPR|NZD|OMR|PAB|PEN|PGK|PHP|PKR|PYG|QAR|RON|RSD|RWF|SBD|SCR|SDG|SGD|SHP|SLL|SOS|SRD|SSP|STD|SYP|SZL|TJS|TMT|TND|TOP|TTD|TZS|UAH|UGX|UYU|UZS|VEF|VND|VUV|WST|XCD|XAF|XOF|XPF|YER|ZMW)"/>
		<coverageVariable key="%currency100" value="(AFA|ADP|ALK|AO[KNR]|AR[ALMP]|ATS|AZM|BA[DN]|BE[CFL]|BG[LM]|BGO|BO[LPV]|BR[BCENRZ]|BUK|BYB|CH[EW]|CL[EF]|CNX|COU|CS[DK]|CYP|DDM|DEM|EC[SV]|EEK|ES[ABP]|FIM|FRF|GEK|GHC|GNS|GQE|GRD|GW[EP]|HRD|IEP|IL[PR]|ISJ|ITL|KR[HO]|LSL|LTT|LU[CFL]|LVR|MAF|MCF|MDC|MGF|MKN|MLF|MT[LP]|MVP|MX[PV]|MZ[EM]|NIC|NLG|PE[IS]|PLZ|PTE|RHD|ROL|RUR|SD[DP]|SIT|SKK|SRG|SUR|SVC|TJR|TMM|TPE|TRL|UAK|UGS|US[NS]|UY[IP]|VEB|VNN|XA[GU]|XB[ABCD]|XDR|XEU|XF[OU]|XP[DT]|XRE|XSU|XTS|XUA|YDD|YU[DMNR]|ZAL|ZMK|ZR[NZ]|ZW[DLR])"/>
		<coverageVariable key="%cyclicNameTypes" value="([1-9]?[0-9])"/>
		<coverageVariable key="%dateFormatItems" value="((d|Ed|EEEEd)|((Gy|y|yyyy)?(M|Md|MEd|MEEEEd|MMM|MMMd|MMMEd|MMMEEEEd|MMMM|MMMMd|MMMMEd|MMMMEEEEd))|((Gy|y|yyyy)(QQQ|QQQQ)?))"/>
		<coverageVariable key="%dateFormatItemsAll" value="(G{0,5}y{0,4}Q{0,4}(M|L){0,5}(E|c){0,5}d{0,2}(H|h){0,2}m{0,2}s{0,2}Z{0,4})"/>
		<coverageVariable key="%dateTimeFormatLengths" value="(full|long|medium|short)"/>
		<coverageVariable key="%fullMedium" value="(full|medium)"/>
		<coverageVariable key="%fullShort" value="(full|short)"/>
		<coverageVariable key="%futurePast" value="(future|past)"/>
		<coverageVariable key="%timeFormatItems" value="(E?(H|h)(ms?)?|ms)"/>
		<coverageVariable key="%dayFieldTypes" value="(era|year|month|week|day|weekday|hour|minute|second|dayperiod|zone)"/>
		<coverageVariable key="%dayTypes" value="(sun|mon|tue|wed|thu|fri|sat)"/>
		<coverageVariable key="%devaLanguages" value="(mr|ne)"/>
		<coverageVariable key="%ellipsisTypes" value="(word-)?(initial|medial|final)"/>
		<coverageVariable key="%exemplarTypes" value="(auxiliary|index|punctuation)"/>
		<coverageVariable key="%intervalFormatDateItems" value="(d|y|y?(M(MM)?)(E?d)?|y?MMMM)"/>
		<coverageVariable key="%intervalFormatTimeItems" value="((h|H)m?v?)"/>
		<coverageVariable key="%intervalFormatGDiff" value="([yMdaHhm])"/>
		<coverageVariable key="%islamicCalendarTerritories" value="(AE|AF|BH|DJ|DZ|EG|EH|ER|IL|IQ|IR|JO|KM|KW|LB|LY|MA|MR|OM|PS|QA|SA|SD|SY|TN|YE)"/>
		<coverageVariable key="%japaneseEras" value="([0-9]{1,3})"/>
		<coverageVariable key="%keys80" value="(calendar|collation|currency|numbers)"/>
		<coverageVariable key="%keys100" value="(col(Alternate|Backwards|CaseFirst|CaseLevel|HiraganaQuaternary|Normalization|Numeric|Reorder|Strength)|timezone|va|variableTop|x)"/>
		<coverageVariable key="%language30" value="und"/>
		<coverageVariable key="%language40" value="(de(_(AT|CH))?|en(_(AU|CA|GB|US))?|es(_(ES|419|MX))?|fr(_(CA|CH))?|it|ja|pt(_(BR|PT))?|ru|zh(_(Hans|Hant))?)"/>
		<coverageVariable key="%language60" value="(ar(_001)?|bn|hi|id|ko|nl(_BE)?|pl|th|tr)"/>
		<coverageVariable key="%language60_CM" value="(bas|bax|bbj|bfd|bkm|bss|bum|byv|ewo|ff|kkj|maf|nnh|yav|ybb)"/>
		<coverageVariable key="%language60_EU" value="(cs|da|e[lt]|fi|hu|lv|mt|s[klv])"/>
		<coverageVariable key="%language60_GA" value="(fan|mye)"/>
		<coverageVariable key="%language60_NG" value="(ff|ha|ibb|ig|kr|yo)"/>
		<coverageVariable key="%language60_TD" value="(shu|dzg|kbl|mde|mua|sba)"/>
		<coverageVariable key="%language80" value="(a[bfmsz]|b[egos]|c[asy]|da|e[lotu]|f[aijoy]|fil|g[alnu]|gag|gsw|haw?|h[ertuy]|i[gs]|jv|k[akmnsuy]|koi|kpv|l[abotv]|lbe|mdh|m[gkilrsty]|n[ben]|or|p[as]|qu|rm|ro(_MD)?|s[adikloqruvw]|t[aegikot]|tsg|u[gkrz]|uli|vi|wo|xh|yo|zdj|zgh|zu|zxx)"/>
		<coverageVariable key="%language100" value="(aa|ac[eh]|ad[ay]|ae|af[ah]|agq|ain|akk?|al[egt]|an[gp]?|apa|ar[cnptw]|as[at]|ath|aus|av|awa|ay|ba[dilnstx]?|bbj|be[jmrz]|bfd|bho?|bkm|bi[kn]?|bla|bm|bnt|br[ax]?|bss|btk|bu[amg]|by[nv]|ca[diruy]|cch|ce[bl]?|cgg|ch[bgkmnopry]?|ckb|cmc|cop?|cp[efp]|cr[hp]?|csb|cus?|cv|da[krvy]|de[ln]|dgr|din|dje|doi|dra|dsb|du[am]|dv|dy[ou]|dzg?|ebu|ee|efi|egy|eka|elx|enm|ewo|fa[nt]|ff|fiu|fon|fr[mors]|fur|ga[ay]|gba|gd|ge[mz]|gil|gmh|go[hnrt]|gr[bc]|guz|gv|gwi|hai|hi[lmt]|hmn|ho|hsb|hup|hz|ia|ib[ab]|i[ei]|ijo|ik|ilo|in[ceh]|io|ir[ao]|iu|jbo|jgo|jmc|jpr|jrb|ka[abcjmrw]|kb[dl]|kc[bg]|kde|kea|kfo|kg|kh[aioq]|ki|kkj|kj|kln?|kmb|ko[ks]|kpe|kr[clou]?|ks[bfh]|ku[mt]|k[vw]|la[dghm]|lez|l[gin]|lo[lz]|lu[ainosy]?|ma[dfgiknps]|md[efr]|me[nr]|mfe|mg[aho]|mh|mi[cns]|mkh|mn[cio]?|mo[hs]+|mu[alns]|mw[lr]|my[env]|na[hipq]?|nds?|new|ng|ni[acu]|nmg|nnh|no[gn]?|nqo|nr|nso|nu[bs]|nv|nwc|ny[mno]?|nzi|o[cjm]|osa?|ot[ao]|pa[aglmpu]|peo|ph[in]|pi|pon|pr[ao]|ra[jpr]|ro[afm]|root|rn|rup|rwk?|sa[dhilmqst]|sb[ap]|sc[no]?|se[ehlms]?|sg[an]?|sh[inu]?|si[dot]|sla|sm[aijns]?|snk?|so[gn]|sr[nr]|ss[ay]?|st|su[ksx]|sw[bc]|sy[cr]|tai|te[mort]|ti[gv]|tkl|tl[hi]?|tmh|tn|tog|tpi|trv|tsi?|tu[mpt]|tvl|twq?|tyv?|tzm|udm|uga|umb|vai|ve|vot?|vun|wa[eklrs]?||wen|xal|xog|ya[opv]|ybb|yi|ypk|yue|zap?|zbl|zen|znd|zun|zza)"/>
		<coverageVariable key="%medLong" value="(medium|long)"/>
		<coverageVariable key="%metazone30_AR" value="Argentina(_Western)?"/>
		<coverageVariable key="%metazone30_AU" value="(Australia_(Central(Western)?|(East|West)ern)|Lord_Howe)"/>
		<coverageVariable key="%metazone30_AU_stdonly" value="Macquarie"/> 
		<coverageVariable key="%metazone30_BR" value="(Amazon|Brasilia|Noronha)"/>
		<coverageVariable key="%metazone30_CA" value="(America_(Eastern|Central|Mountain|Pacific)|Newfoundland)"/>
		<coverageVariable key="%metazone30_EU" value="Europe_(Central|(East|West)ern)"/>
		<coverageVariable key="%metazone30_ID" value="Indonesia_(Central|(East|West)ern)"/>
		<coverageVariable key="%metazone30_KZ" value="Kazakhstan_(East|West)ern"/>
		<coverageVariable key="%metazone30_MX" value="Mexico_(Northwest|Pacific)"/>
		<coverageVariable key="%metazone30_RU" value="(Europe_Eastern|Moscow|Yekaterinburg|Omsk|Novosibirsk|Krasnoyarsk|Irkutsk|Yakutsk|Vladivostok|Magadan)"/>
		<coverageVariable key="%metazone30_US" value="(America_(Eastern|Central|Mountain|Pacific)|Alaska|Hawaii_Aleutian)"/>
		<coverageVariable key="%metazone40" value="(America_(Eastern|Central|Mountain|Pacific)|Europe_(Central|(East|West)ern)|Atlantic)"/>
		<coverageVariable key="%metazone60" value="(Africa_Western|Arabian|Australia_(Central(Western)?|(East|West)ern)|China|Israel|Japan|Korea|Moscow)"/> 
		<coverageVariable key="%metazone60_stdonly" value="(Africa_(Central|(East|South)ern)|India|Indochina|Indonesia_(Central|(East|West)ern))"/> 
		<coverageVariable key="%metazone80" value="(Alaska|Amazon|Argentina(_Western)?|Armenia|Azerbaijan|Azores|Bangladesh|Brasilia|Cape_Verde|Chatham|Chile|Choibalsan|Colombia|Cook|Cuba|Easter|Falkland|Fiji|Georgia|Greenland_(East|West)ern|Hawaii_Aleutian|Hong_Kong|Hovd|Iran|Irkutsk|Krasnoyarsk|Lord_Howe|Magadan|Mauritius|Mexico_(Northwest|Pacific)|Mongolia|New_(Caledonia|Zealand)|Newfoundland|Noronha|Novosibirsk|Omsk|Pakistan|Paraguay|Peru|Philippines|Pierre_Miquelon|Sakhalin|Samoa|Taipei|Tonga|Turkmenistan|Uruguay|Uzbekistan|Vanuatu|Vladivostok|Volgograd|Yakutsk|Yekaterinburg)"/>
		<coverageVariable key="%metazone80_stdonly" value="(Afghanistan|Bhutan|Bolivia|Brunei|Chamorro|Christmas|Cocos|Davis|DumontDUrville|East_Timor|Ecuador|French_(Guiana|Southern)|Gambier|Galapagos|Gilbert_Islands|Gulf|Guyana|Indian_Ocean|Kazakhstan_(East|West)ern|Kosrae|Kyrgystan|Line_Islands|Macquarie|Malaysia|Maldives|Marquesas|Marshall_Islands|Mawson|Myanmar|Nauru|Nepal|Niue|Norfolk|Palau|Papua_New_Guinea|Phoenix_Islands|Pitcairn|Ponape|Reunion|Rothera|Seychelles|Singapore|Solomon|South_Georgia|Suriname|Syowa|Tahiti|Tajikistan|Tokelau|Truk|Tuvalu|Venezuela|Vostok|Wake|Wallis)"/>
		<coverageVariable key="%metazone100" value="(Acre|Almaty|Anadyr|Aqtau|Aqtobe|Kamchatka|Macau|Qyzylorda|Samara)"/>
		<coverageVariable key="%metazone100_stdonly" value="(Casey|Guam|Lanka|North_Mariana)"/>
		<coverageVariable key="%miscPatternTypes" value="(atLeast|range)"/>
		<coverageVariable key="%monthTypes" value="(1[0-3]?|[2-9])"/>
		<coverageVariable key="%numberingSystem80" value="(arab(ext)?|armn(low)?|beng|deva|ethi|fullwide|geor|grek(low)?|gujr|guru|hanidec|han[st](fin)?|hebr|jpan(fin)?|khmr|knda|laoo|mlym|mymr|orya|roman(low)?|taml(dec)?|telu|thai|tibt)"/>
		<coverageVariable key="%numberingSystem100" value="(finance|native|traditional|bali|brah|cakm|cham|hanidays|java|kali|lana(tham)?|lepc|limb|mong|mtei|mymrshan|nkoo|olck|osma|saur|shrd|sora|sund|takr|talu|vaii)"/>
		<coverageVariable key="%persianCalendarTerritories" value="(AF|IR)"/>
		<coverageVariable key="%phonebookCollationLanguages" value="(de|fi)"/>
		<coverageVariable key="%quarterTypes" value="([1-4])"/>
		<coverageVariable key="%regionFormatTypes" value="(daylight|standard)"/>
		<coverageVariable key="%relativeTimeTypes" value="(year|month|week|day|hour|minute|second)"/>
		<coverageVariable key="%script30" value="(Zxxx|Zzzz)"/>
		<coverageVariable key="%script40" value="(Latn|Hans|Hant|Cyrl|Arab)"/>
		<coverageVariable key="%script60" value="(Jpan|Kore)"/>
		<coverageVariable key="%script80" value="(Zyyy|Armn|Beng|Bopo|Brai|Deva|Ethi|Geor|Grek|Gujr|Guru|Hani|Hang|Hebr|Hira|Knda|Kana|Khmr|Laoo|Mlym|Mong|Mymr|Orya|Sinh|Taml|Telu|Thaa|Thai|Tibt|Zsym)"/>
		<coverageVariable key="%script100" value="(Afak|Armi|Avst|Bali|Bamu|Bass|Batk|Blis|Brah|Bugi|Buhd|Cakm|Cans|Cari|Cham|Cher|Cirt|Copt|Cprt|Cyrs|Dsrt|Dupl|Egy[dhp]|Geok|Glag|Goth|Gran|Hano|Hluw|Hmng|Hrkt|Hung|Inds|Ital|Java|Jurc|Kali|Khar|Khoj|Kpel|Kthi|Lana|Lat[fg]|Lepc|Limb|Lin[ab]|Lisu|Loma|Ly[cd]i|Man[di]|Maya|Mend|Mer[co]|Moon|Mroo|Mtei|Narb|Nbat|Nkgb|Nkoo|Nshu|Ogam|Olck|Orkh|Osma|Palm|Perm|Phag|Phl[ipv]|Phnx|Plrd|Prti|Rjng|Roro|Runr|Samr|Sar[ab]|Saur|Sgnw|Shaw|Shrd|Sind|Sora|Sund|Sylo|Syr[cejn]|Tagb|Takr|Tal[eu]|Tang|Tavt|Teng|Tfng|Tglg|Tirh|Ugar|Vaii|Visp|Wara|Wole|Xpeo|Xsux|Yiii|Zinh|Zmth)"/>
		<coverageVariable key="%shortLong" value="(short|long)"/>
		<coverageVariable key="%shortVariant" value="(short|variant)"/>
		<coverageVariable key="%standaloneVariant" value="(stand-alone|variant)"/>
		<coverageVariable key="%stdPattern" value="[@type='standard']/pattern[@type='standard']"/>
		<coverageVariable key="%territory30" value="ZZ"/>
		<coverageVariable key="%territory40" value="(BR|CN|DE|GB|FR|IN|IT|JP|RU|US)"/>
		<coverageVariable key="%territory60" value="(AT|AU|BE|CA|CH|DK|ES|FI|GR|HK|ID|IE|KR|MX|NL|NO|PL|PT|SA|SE|TH|TR|TW|ZA)"/>
		<coverageVariable key="%territory60_EU" value="(CZ|EE|HU|LT|LU|LV|MT|SI|SK)"/>
		<coverageVariable key="%territory80" value="(0(0[12359]|1[1345789]|2[19]|3[0459]|5[347]|61)|1(4[235]|5[01459])|419|A[CDEFGILMOQRSWXZ]|B[ABDFGHIJLMNOQSTUVWYZ]|C[CDFGIKLMOPRUVWXYZ]|D[GJMOZ]|E[ACEGHRTU]|F[JKMO]|G[ADEFGHILMNPQSTUWY]|H[MNRTU]|I[CLMOQRS]|J[EMO]|K[EGHIMNPWYZ]|L[ABCIKRSTUVY]|M[ACDEFGHKLMNOPQRSTUVWYZ]|N[ACEFGIPRUZ]|OM|P[AEFGHKMNRSWY]|Q[AO]|R[EOSW]|S[BCDGHIJKLMNORSTVXYZ]|T[ACDFGJKLMNOPTVZ]|U[AGMYZ]|V[ACEGINU]|W[FS]|XK|Y[ET]|Z[MW])"/>
		<coverageVariable key="%territory80short" value="(GB|HK|MO|PS|SA|US)"/>
		<coverageVariable key="%territory100" value="AN"/>
		<coverageVariable key="%timeZones" value="(Africa|America|Antarctica|Arctic|Asia|Australia|Atlantic|Europe|Indian|Pacific)(/[A-Za-z_\-]++){1,2}"/>
		<coverageVariable key="%traditionalCollationLanguages" value="(bn|es|kn|sa)"/>
		<coverageVariable key="%transformNameTypes" value="(BGN|Numeric|Tone|UNGEGN|x-(Accents|Fullwidth|Halfwidth|Jamo|Pinyin|Publishing))"/>
		<coverageVariable key="%unitDurationTypes" value="duration-(year|month|week|day|hour|minute|second|millisecond)"/>
		<coverageVariable key="%unitLengths" value="(long|short|narrow)"/>
		<coverageVariable key="%unitsCommonMetric" value="(length-(centi|milli|kilo)?meter|mass-(kilo)?gram|temperature-celsius|speed-kilometer-per-hour|volume-liter)"/>
		<coverageVariable key="%unitsCommonUS" value="(length-(inch|foot|yard|mile)|mass-(ounce|pound)|temperature-fahrenheit|speed-mile-per-hour)"/>
		<coverageVariable key="%unitsOther" value="(length-(picometer|light-year)|pressure-(hectopascal|inch-hg|millibar)|acceleration-g-force|angle-(degree|minute|second)|area-(acre|hectare|square-(foot|kilometer|meter|mile))|power-(horsepower|kilowatt|watt)|speed-meter-per-second|volume-cubic-(mile|kilometer))"/>
		<coverageVariable key="%variantTypes" value="([A-Z0-9]++)"/>
		<coverageVariable key="%wideAbbr" value="(wide|abbreviated)"/>
		<coverageVariable key="%wideNarrow" value="(wide|narrow)"/>
		<coverageVariable key="%yesNo" value="(yes|no)"/>
		<!-- -->
		<!-- Coverage levels begin here -->
		<!-- -->
		<coverageLevel value="10" match="characters/exemplarCharacters"/>
		<coverageLevel value="10" match="characters/exemplarCharacters[@type='%exemplarTypes']"/>
		<coverageLevel value="20" match="dates/calendars/calendar[@type='gregorian']/days/dayContext[@type='format']/dayWidth[@type='%wideAbbr']/day[@type='%dayTypes']"/>
		<coverageLevel value="20" match="dates/calendars/calendar[@type='gregorian']/months/monthContext[@type='format']/monthWidth[@type='%wideAbbr']/month[@type='%monthTypes']"/>
		<coverageLevel value="20" match="dates/calendars/calendar[@type='gregorian']/dateFormats/dateFormatLength[@type='%shortLong']/dateFormat%stdPattern"/>
		<coverageLevel value="20" match="dates/calendars/calendar[@type='generic']/dateFormats/dateFormatLength[@type='%shortLong']/dateFormat%stdPattern"/>
		<coverageLevel value="20" match="dates/calendars/calendar[@type='gregorian']/timeFormats/timeFormatLength[@type='%medLong']/timeFormat%stdPattern"/>
		<coverageLevel value="20" match="numbers/symbols[@numberSystem='latn']/decimal"/>
		<coverageLevel value="20" match="numbers/symbols[@numberSystem='latn']/group"/>
		<coverageLevel value="20" match="posix/messages/yesstr"/>
		<coverageLevel value="20" match="posix/messages/nostr"/>
		<coverageLevel value="20" match="numbers/defaultNumberingSystem"/>
		<coverageLevel value="20" match="numbers/otherNumberingSystems/native"/>
		<coverageLevel value="20" match="numbers/otherNumberingSystems/traditional"/>
		<coverageLevel value="20" match="numbers/otherNumberingSystems/finance"/>
		<coverageLevel value="20" match="numbers/decimalFormats[@numberSystem='latn']/decimalFormatLength/decimalFormat%stdPattern"/>
		<coverageLevel value="30" match="localeDisplayNames/languages/language[@type='%language30']"/>
		<coverageLevel value="30" match="localeDisplayNames/languages/language[@type='${Target-Language}']"/>
		<coverageLevel value="30" match="localeDisplayNames/languages/language[@type='${Target-Language}'][@alt='%shortVariant']"/>
		<coverageLevel value="30" match="localeDisplayNames/scripts/script[@type='%script30']"/>
		<coverageLevel value="30" match="localeDisplayNames/scripts/script[@type='${Target-Scripts}']"/>
		<coverageLevel value="30" match="localeDisplayNames/territories/territory[@type='${Target-Territories}']"/>
		<coverageLevel value="30" match="localeDisplayNames/territories/territory[@type='${Target-Territories}'][@alt='(%shortVariant)']"/>
		<coverageLevel value="30" match="localeDisplayNames/territories/territory[@type='%territory30']"/>
		<coverageLevel value="30" match="localeDisplayNames/types/type[@type='${Calendar-List}'][@key='calendar']"/>
		<coverageLevel value="30" match="localeDisplayNames/types/type[@type='gregorian'][@key='calendar']"/>
		<coverageLevel value="30" match="localeDisplayNames/types/type[@type='standard'][@key='collation']"/>
		<coverageLevel inLanguage="%phonebookCollationLanguages" value="30" match="localeDisplayNames/types/type[@type='phonebook'][@key='collation']"/>
		<coverageLevel inLanguage="%traditionalCollationLanguages" value="30" match="localeDisplayNames/types/type[@type='traditional'][@key='collation']"/>
		<coverageLevel inLanguage="%CJK_Languages" value="30" match="localeDisplayNames/types/type[@type='unihan'][@key='collation']"/>
		<coverageLevel inLanguage="si" value="30" match="localeDisplayNames/types/type[@type='dictionary'][@key='collation']"/>
		<coverageLevel inLanguage="sv" value="30" match="localeDisplayNames/types/type[@type='reformed'][@key='collation']"/>
		<coverageLevel inLanguage="zh" value="30" match="localeDisplayNames/types/type[@type='(big5han|gb2312han|pinyin|stroke|zhuyin)'][@key='collation']"/>
		<coverageLevel inScript="Arab" value="30" match="localeDisplayNames/types/type[@type='arab'][@key='numbers']"/>
		<coverageLevel inScript="Armn" value="30" match="localeDisplayNames/types/type[@type='armn(low)?'][@key='numbers']"/>
		<coverageLevel inLanguage="%arabextLanguages" value="30" match="localeDisplayNames/types/type[@type='arabext'][@key='numbers']"/>
		<coverageLevel inScript="Beng" value="30" match="localeDisplayNames/types/type[@type='beng'][@key='numbers']"/>
		<coverageLevel inScript="Deva" value="30" match="localeDisplayNames/types/type[@type='deva'][@key='numbers']"/>
		<coverageLevel inScript="Ethi" value="30" match="localeDisplayNames/types/type[@type='ethi'][@key='numbers']"/>
		<coverageLevel inLanguage="%CJK_Languages" value="30" match="localeDisplayNames/types/type[@type='fullwide'][@key='numbers']"/>
		<coverageLevel inScript="Geor" value="30" match="localeDisplayNames/types/type[@type='geor'][@key='numbers']"/>
		<coverageLevel inScript="Grek" value="30" match="localeDisplayNames/types/type[@type='grek(low)?'][@key='numbers']"/>
		<coverageLevel inScript="Gujr" value="30" match="localeDisplayNames/types/type[@type='gujr'][@key='numbers']"/>
		<coverageLevel inScript="Guru" value="30" match="localeDisplayNames/types/type[@type='guru'][@key='numbers']"/>
		<coverageLevel inLanguage="zh" value="30" match="localeDisplayNames/types/type[@type='(han(s|t)(fin)?|hanidec)'][@key='numbers']"/>
		<coverageLevel inScript="Hebr" value="30" match="localeDisplayNames/types/type[@type='hebr'][@key='numbers']"/>
		<coverageLevel inScript="Guru" value="30" match="localeDisplayNames/types/type[@type='guru'][@key='numbers']"/>
		<coverageLevel inLanguage="ja" value="30" match="localeDisplayNames/types/type[@type='jpan(fin)?'][@key='numbers']"/>
		<coverageLevel inScript="Khmr" value="30" match="localeDisplayNames/types/type[@type='khmr'][@key='numbers']"/>
		<coverageLevel inScript="Knda" value="30" match="localeDisplayNames/types/type[@type='knda'][@key='numbers']"/>
		<coverageLevel inScript="Laoo" value="30" match="localeDisplayNames/types/type[@type='laoo'][@key='numbers']"/>
		<coverageLevel value="30" match="localeDisplayNames/types/type[@type='latn'][@key='numbers']"/>
		<coverageLevel inScript="Mong" value="30" match="localeDisplayNames/types/type[@type='mong'][@key='numbers']"/>
		<coverageLevel inScript="Mlym" value="30" match="localeDisplayNames/types/type[@type='mlym'][@key='numbers']"/>
		<coverageLevel inScript="Mymr" value="30" match="localeDisplayNames/types/type[@type='mymr(shan)?'][@key='numbers']"/>
		<coverageLevel inScript="Orya" value="30" match="localeDisplayNames/types/type[@type='orya'][@key='numbers']"/>
		<coverageLevel inScript="Taml" value="30" match="localeDisplayNames/types/type[@type='taml'][@key='numbers']"/>
		<coverageLevel inScript="Telu" value="30" match="localeDisplayNames/types/type[@type='telu'][@key='numbers']"/>
		<coverageLevel inScript="Thai" value="30" match="localeDisplayNames/types/type[@type='thai'][@key='numbers']"/>
		<coverageLevel inScript="Tibt" value="30" match="localeDisplayNames/types/type[@type='tibt'][@key='numbers']"/>
		<coverageLevel inLanguage="vai" value="30" match="localeDisplayNames/types/type[@type='vaii'][@key='numbers']"/>
		<coverageLevel value="30" match="numbers/currencies/currency[@type='%currency30']/symbol[@alt='narrow']"/>
		<coverageLevel value="30" match="numbers/currencies/currency[@type='%currency30']/displayName"/>
		<coverageLevel value="30" match="numbers/currencies/currency[@type='%currency30']/displayName[@count='${Target-Plurals}']"/>
		<coverageLevel value="30" match="numbers/currencies/currency[@type='${Target-Currencies}']/symbol"/>
		<coverageLevel value="30" match="numbers/currencies/currency[@type='${Target-Currencies}']/displayName"/>
		<coverageLevel value="30" match="numbers/currencies/currency[@type='${Target-Currencies}']/displayName[@count='%allPlurals']"/>
		<coverageLevel value="30" match="numbers/currencyFormats[@numberSystem='latn']/currencyFormatLength/currencyFormat%stdPattern"/>
		<coverageLevel value="30" match="numbers/percentFormats[@numberSystem='latn']/percentFormatLength/percentFormat%stdPattern"/>
		<coverageLevel value="30" match="numbers/scientificFormats[@numberSystem='latn']/scientificFormatLength/scientificFormat%stdPattern"/>
		<coverageLevel value="30" match="numbers/symbols[@numberSystem='latn']/plusSign"/>
		<coverageLevel value="30" match="numbers/symbols[@numberSystem='latn']/minusSign"/>
		<coverageLevel value="30" match="numbers/symbols[@numberSystem='latn']/percentSign"/>
		<coverageLevel inLanguage="ar" value="30" match="numbers/symbols[@numberSystem='arab']/decimal"/>
		<coverageLevel inLanguage="ar" value="30" match="numbers/symbols[@numberSystem='arab']/group"/>
		<coverageLevel inLanguage="ar" value="30" match="numbers/symbols[@numberSystem='arab']/plusSign"/>
		<coverageLevel inLanguage="ar" value="30" match="numbers/symbols[@numberSystem='arab']/minusSign"/>
		<coverageLevel inLanguage="ar" value="30" match="numbers/symbols[@numberSystem='arab']/percentSign"/>
		<coverageLevel inLanguage="ar" value="30" match="numbers/decimalFormats[@numberSystem='arab']/decimalFormatLength/decimalFormat%stdPattern"/>
		<coverageLevel inLanguage="ar" value="30" match="numbers/currencyFormats[@numberSystem='arab']/currencyFormatLength/currencyFormat%stdPattern"/>
		<coverageLevel inLanguage="ar" value="30" match="numbers/scientificFormats[@numberSystem='arab']/scientificFormatLength/scientificFormat%stdPattern"/>
		<coverageLevel inLanguage="ar" value="30" match="numbers/percentFormats[@numberSystem='arab']/percentFormatLength/percentFormat%stdPattern"/>
		<coverageLevel inLanguage="%arabextLanguages" value="30" match="numbers/symbols[@numberSystem='arabext']/decimal"/>
		<coverageLevel inLanguage="%arabextLanguages" value="30" match="numbers/symbols[@numberSystem='arabext']/group"/>
		<coverageLevel inLanguage="%arabextLanguages" value="30" match="numbers/symbols[@numberSystem='arabext']/plusSign"/>
		<coverageLevel inLanguage="%arabextLanguages" value="30" match="numbers/symbols[@numberSystem='arabext']/minusSign"/>
		<coverageLevel inLanguage="%arabextLanguages" value="30" match="numbers/symbols[@numberSystem='arabext']/percentSign"/>
		<coverageLevel inLanguage="%arabextLanguages" value="30" match="numbers/decimalFormats[@numberSystem='arabext']/decimalFormatLength/decimalFormat%stdPattern"/>
		<coverageLevel inLanguage="%arabextLanguages" value="30" match="numbers/currencyFormats[@numberSystem='arabext']/currencyFormatLength/currencyFormat%stdPattern"/>
		<coverageLevel inLanguage="%arabextLanguages" value="30" match="numbers/scientificFormats[@numberSystem='arabext']/scientificFormatLength/scientificFormat%stdPattern"/>
		<coverageLevel inLanguage="%arabextLanguages" value="30" match="numbers/percentFormats[@numberSystem='arabext']/percentFormatLength/percentFormat%stdPattern"/>
		<coverageLevel inLanguage="%devaLanguages" value="30" match="numbers/symbols[@numberSystem='deva']/decimal"/>
		<coverageLevel inLanguage="%devaLanguages" value="30" match="numbers/symbols[@numberSystem='deva']/group"/>
		<coverageLevel inLanguage="%devaLanguages" value="30" match="numbers/symbols[@numberSystem='deva']/plusSign"/>
		<coverageLevel inLanguage="%devaLanguages" value="30" match="numbers/symbols[@numberSystem='deva']/minusSign"/>
		<coverageLevel inLanguage="%devaLanguages" value="30" match="numbers/symbols[@numberSystem='deva']/percentSign"/>
		<coverageLevel inLanguage="%devaLanguages" value="30" match="numbers/decimalFormats[@numberSystem='deva']/decimalFormatLength/decimalFormat%stdPattern"/>
		<coverageLevel inLanguage="%devaLanguages" value="30" match="numbers/currencyFormats[@numberSystem='deva']/currencyFormatLength/currencyFormat%stdPattern"/>
		<coverageLevel inLanguage="%devaLanguages" value="30" match="numbers/scientificFormats[@numberSystem='deva']/scientificFormatLength/scientificFormat%stdPattern"/>
		<coverageLevel inLanguage="%devaLanguages" value="30" match="numbers/percentFormats[@numberSystem='deva']/percentFormatLength/percentFormat%stdPattern"/>
		<coverageLevel inLanguage="dz" value="30" match="numbers/symbols[@numberSystem='tibt']/decimal"/>
		<coverageLevel inLanguage="dz" value="30" match="numbers/symbols[@numberSystem='tibt']/group"/>
		<coverageLevel inLanguage="dz" value="30" match="numbers/symbols[@numberSystem='tibt']/plusSign"/>
		<coverageLevel inLanguage="dz" value="30" match="numbers/symbols[@numberSystem='tibt']/minusSign"/>
		<coverageLevel inLanguage="dz" value="30" match="numbers/symbols[@numberSystem='tibt']/percentSign"/>
		<coverageLevel inLanguage="dz" value="30" match="numbers/decimalFormats[@numberSystem='tibt']/decimalFormatLength/decimalFormat%stdPattern"/>
		<coverageLevel inLanguage="dz" value="30" match="numbers/currencyFormats[@numberSystem='tibt']/currencyFormatLength/currencyFormat%stdPattern"/>
		<coverageLevel inLanguage="dz" value="30" match="numbers/scientificFormats[@numberSystem='tibt']/scientificFormatLength/scientificFormat%stdPattern"/>
		<coverageLevel inLanguage="dz" value="30" match="numbers/percentFormats[@numberSystem='tibt']/percentFormatLength/percentFormat%stdPattern"/>
		<coverageLevel value="30" match="dates/calendars/calendar[@type='gregorian']/dateFormats/dateFormatLength[@type='%fullMedium']/dateFormat%stdPattern"/>
		<coverageLevel value="30" match="dates/calendars/calendar[@type='gregorian']/dateTimeFormats/dateTimeFormatLength[@type='%dateTimeFormatLengths']/dateTimeFormat%stdPattern"/>
		<coverageLevel value="30" match="dates/calendars/calendar[@type='gregorian']/dateTimeFormats/intervalFormats/intervalFormatFallback"/>
		<coverageLevel value="30" match="dates/calendars/calendar[@type='generic']/dateFormats/dateFormatLength[@type='%fullMedium']/dateFormat%stdPattern"/>
		<coverageLevel value="30" match="dates/calendars/calendar[@type='generic']/dateTimeFormats/dateTimeFormatLength[@type='%dateTimeFormatLengths']/dateTimeFormat%stdPattern"/>
		<coverageLevel value="30" match="dates/calendars/calendar[@type='generic']/dateTimeFormats/intervalFormats/intervalFormatFallback"/>
		<coverageLevel value="30" match="dates/timeZoneNames/zone[@type='${Target-TimeZones}']/exemplarCity"/>
		<coverageLevel value="30" match="dates/timeZoneNames/zone[@type='Etc/Unknown']/exemplarCity"/>
		<coverageLevel value="30" match="dates/timeZoneNames/fallbackFormat"/>
		<coverageLevel value="30" match="dates/timeZoneNames/gmtFormat"/>
		<coverageLevel value="30" match="dates/timeZoneNames/gmtZeroFormat"/>
		<coverageLevel value="30" match="dates/timeZoneNames/regionFormat"/>
		<coverageLevel value="30" match="dates/timeZoneNames/regionFormat[@type='%regionFormatTypes']"/>
		<coverageLevel value="30" match="dates/timeZoneNames/hourFormat"/>
		<coverageLevel value="30" match="dates/calendars/calendar[@type='gregorian']/dayPeriods/dayPeriodContext[@type='format']/dayPeriodWidth[@type='wide']/dayPeriod[@type='(am|pm)']"/>
		<coverageLevel value="30" match="dates/calendars/calendar[@type='gregorian']/days/dayContext[@type='format']/dayWidth[@type='narrow']/day[@type='%dayTypes']"/>
		<coverageLevel value="30" match="dates/calendars/calendar[@type='gregorian']/days/dayContext[@type='stand-alone']/dayWidth[@type='%allWidths']/day[@type='%dayTypes']"/>
		<coverageLevel value="30" match="dates/calendars/calendar[@type='gregorian']/eras/eraAbbr/era[@type='(0|1)']"/>
		<coverageLevel value="30" match="dates/calendars/calendar[@type='gregorian']/eras/eraAbbr/era[@type='(0|1)'][@alt='variant']"/>
		<coverageLevel value="30" match="dates/calendars/calendar[@type='gregorian']/months/monthContext[@type='format']/monthWidth[@type='narrow']/month[@type='%monthTypes']"/>
		<coverageLevel value="30" match="dates/calendars/calendar[@type='gregorian']/months/monthContext[@type='stand-alone']/monthWidth[@type='%allWidths']/month[@type='%monthTypes']"/>
		<coverageLevel value="30" match="dates/calendars/calendar[@type='gregorian']/timeFormats/timeFormatLength[@type='%fullShort']/timeFormat%stdPattern"/>
		<coverageLevel inTerritory="TH" value="30" match="dates/calendars/calendar[@type='buddhist']/eras/eraAbbr/era[@type='0']"/>
		<coverageLevel inTerritory="TH" value="60" match="dates/calendars/calendar[@type='buddhist']/dateFormats/dateFormatLength[@type='%dateTimeFormatLengths']/dateFormat%stdPattern"/>
		<coverageLevel inTerritory="AR" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_AR']/long/generic"/>
		<coverageLevel inTerritory="AR" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_AR']/long/standard"/>
		<coverageLevel inTerritory="AR" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_AR']/long/daylight"/>
		<coverageLevel inTerritory="AU" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_AU']/long/generic"/>
		<coverageLevel inTerritory="AU" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_AU']/long/standard"/>
		<coverageLevel inTerritory="AU" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_AU']/long/daylight"/>
		<coverageLevel inTerritory="AU" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_AU_stdonly']/long/standard"/>
		<coverageLevel inTerritory="BR" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_BR']/long/generic"/>
		<coverageLevel inTerritory="BR" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_BR']/long/standard"/>
		<coverageLevel inTerritory="BR" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_BR']/long/daylight"/>
		<coverageLevel inTerritory="CA" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_CA']/long/generic"/>
		<coverageLevel inTerritory="CA" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_CA']/long/standard"/>
		<coverageLevel inTerritory="CA" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_CA']/long/daylight"/>
		<coverageLevel inTerritory="EU" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_EU']/long/generic"/>
		<coverageLevel inTerritory="EU" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_EU']/long/standard"/>
		<coverageLevel inTerritory="EU" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_EU']/long/daylight"/>
		<coverageLevel inTerritory="ID" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_ID']/long/standard"/>
		<coverageLevel inTerritory="KZ" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_KZ']/long/standard"/>
		<coverageLevel inTerritory="MX" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_MX']/long/generic"/>
		<coverageLevel inTerritory="MX" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_MX']/long/standard"/>
		<coverageLevel inTerritory="MX" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_MX']/long/daylight"/>
		<coverageLevel inTerritory="RU" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_RU']/long/generic"/>
		<coverageLevel inTerritory="RU" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_RU']/long/standard"/>
		<coverageLevel inTerritory="RU" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_RU']/long/daylight"/>
		<coverageLevel inTerritory="US" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_US']/long/generic"/>
		<coverageLevel inTerritory="US" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_US']/long/standard"/>
		<coverageLevel inTerritory="US" value="30" match="dates/timeZoneNames/metazone[@type='%metazone30_US']/long/daylight"/>
		<coverageLevel inTerritory="GB" value="30" match="dates/timeZoneNames/zone[@type='Europe/London']/long/daylight"/>
		<coverageLevel inTerritory="GB" value="30" match="dates/timeZoneNames/zone[@type='Europe/London']/short/daylight"/>
		<coverageLevel inTerritory="IE" value="30" match="dates/timeZoneNames/zone[@type='Europe/Dublin']/long/daylight"/>
		<coverageLevel inTerritory="IE" value="30" match="dates/timeZoneNames/zone[@type='Europe/Dublin']/short/daylight"/>
		<coverageLevel value="40" match="localeDisplayNames/languages/language[@type='%language40']"/>
		<coverageLevel value="40" match="localeDisplayNames/languages/language[@type='%language40'][@alt='%shortVariant']"/>
		<coverageLevel value="40" match="localeDisplayNames/scripts/script[@type='%script40']"/>
		<coverageLevel value="40" match="localeDisplayNames/scripts/script[@type='%script40'][@alt='(variant|stand-alone)']"/>
		<coverageLevel value="40" match="localeDisplayNames/territories/territory[@type='%territory40']"/>
		<coverageLevel value="40" match="localeDisplayNames/codePatterns/codePattern[@type='(language|script|territory)']"/>
		<coverageLevel value="40" match="localeDisplayNames/measurementSystemNames/measurementSystemName[@type='(metric|UK|US)']"/>
		<coverageLevel value="40" match="localeDisplayNames/localeDisplayPattern/localePattern"/>
		<coverageLevel value="40" match="localeDisplayNames/localeDisplayPattern/localeSeparator"/>
		<coverageLevel value="40" match="localeDisplayNames/localeDisplayPattern/localeKeyTypePattern"/>
		<coverageLevel value="40" match="numbers/currencyFormats[@numberSystem='latn']/unitPattern[@count='${Target-Plurals}']"/>
		<coverageLevel value="40" match="numbers/currencies/currency[@type='%currency40']/symbol"/>
		<coverageLevel value="40" match="numbers/currencies/currency[@type='%currency40']/symbol[@alt='narrow']"/>
		<coverageLevel value="40" match="numbers/currencies/currency[@type='%currency40']/displayName"/>
		<coverageLevel value="40" match="numbers/currencies/currency[@type='%currency40']/displayName[@count='${Target-Plurals}']"/>
		<coverageLevel value="40" match="dates/calendars/calendar[@type='gregorian']/quarters/quarterContext[@type='%contextTypes']/quarterWidth[@type='%allWidths']/quarter[@type='%quarterTypes']"/>
		<coverageLevel value="40" match="dates/fields/field[@type='%dayFieldTypes']/displayName"/>
		<coverageLevel value="40" match="dates/fields/field[@type='day']/relative[@type='(-1|0|1)']"/>
		<coverageLevel inTerritory="%chineseCalendarTerritories" value="40" match="dates/calendars/calendar[@type='chinese']/months/monthContext[@type='%contextTypes']/monthWidth[@type='%allWidths']/month[@type='%monthTypes']"/>
		<coverageLevel inTerritory="%chineseCalendarTerritories" value="40" match="dates/calendars/calendar[@type='chinese']/dateFormats/dateFormatLength[@type='%dateTimeFormatLengths']/dateFormat%stdPattern"/>
		<coverageLevel inTerritory="EG" value="40" match="dates/calendars/calendar[@type='coptic']/months/monthContext[@type='%contextTypes']/monthWidth[@type='%allWidths']/month[@type='%monthTypes']"/>
		<coverageLevel inTerritory="ET" value="40" match="dates/calendars/calendar[@type='ethiopic']/months/monthContext[@type='%contextTypes']/monthWidth[@type='%allWidths']/month[@type='%monthTypes']"/>
		<coverageLevel inLanguage="yi" inTerritory="IL" value="40" match="dates/calendars/calendar[@type='hebrew']/eras/eraAbbr/era[@type='0']"/>
		<coverageLevel inLanguage="yi" inTerritory="IL" value="40" match="dates/calendars/calendar[@type='hebrew']/months/monthContext[@type='%contextTypes']/monthWidth[@type='%wideAbbr']/month[@type='%monthTypes']"/>
		<coverageLevel inLanguage="yi" inTerritory="IL" value="40" match="dates/calendars/calendar[@type='hebrew']/months/monthContext[@type='%contextTypes']/monthWidth[@type='%wideAbbr']/month[@type='7'][@yeartype='leap']"/>
		<coverageLevel inLanguage="yi" inTerritory="IL" value="40" match="dates/calendars/calendar[@type='hebrew']/dateFormats/dateFormatLength[@type='%dateTimeFormatLengths']/dateFormat%stdPattern"/>
		<coverageLevel inTerritory="IN" value="40" match="dates/calendars/calendar[@type='indian']/eras/eraAbbr/era[@type='0']"/>
		<coverageLevel inTerritory="IN" value="40" match="dates/calendars/calendar[@type='indian']/months/monthContext[@type='%contextTypes']/monthWidth[@type='%allWidths']/month[@type='%monthTypes']"/>
		<coverageLevel inTerritory="%islamicCalendarTerritories" value="40" match="dates/calendars/calendar[@type='islamic']/months/monthContext[@type='%contextTypes']/monthWidth[@type='%allWidths']/month[@type='%monthTypes']"/>
		<coverageLevel inTerritory="%islamicCalendarTerritories" value="40" match="dates/calendars/calendar[@type='islamic']/eras/eraAbbr/era[@type='0']"/>
		<coverageLevel inTerritory="%islamicCalendarTerritories" value="60" match="dates/calendars/calendar[@type='islamic']/dateFormats/dateFormatLength[@type='%dateTimeFormatLengths']/dateFormat%stdPattern"/>
		<coverageLevel inTerritory="JP" value="40" match="dates/calendars/calendar[@type='japanese']/eras/eraAbbr/era[@type='%japaneseEras']"/>
		<coverageLevel inTerritory="JP" value="40" match="dates/calendars/calendar[@type='japanese']/eras/eraNarrow/era[@type='%japaneseEras']"/>
		<coverageLevel inTerritory="JP" value="60" match="dates/calendars/calendar[@type='japanese']/dateFormats/dateFormatLength[@type='%dateTimeFormatLengths']/dateFormat%stdPattern"/>
		<coverageLevel inTerritory="JP" value="60" match="dates/calendars/calendar[@type='japanese']/dateTimeFormats/dateTimeFormatLength[@type='%dateTimeFormatLengths']/dateTimeFormat%stdPattern"/>
		<coverageLevel inTerritory="KR" value="40" match="dates/calendars/calendar[@type='dangi']/months/monthContext[@type='%contextTypes']/monthWidth[@type='%allWidths']/month[@type='%monthTypes']"/>
		<coverageLevel inTerritory="KR" value="40" match="dates/calendars/calendar[@type='dangi']/dateFormats/dateFormatLength[@type='%dateTimeFormatLengths']/dateFormat%stdPattern"/>
		<coverageLevel inTerritory="%persianCalendarTerritories" value="40" match="dates/calendars/calendar[@type='persian']/eras/eraAbbr/era[@type='0']"/>
		<coverageLevel inTerritory="%persianCalendarTerritories" value="40" match="dates/calendars/calendar[@type='persian']/months/monthContext[@type='%contextTypes']/monthWidth[@type='%allWidths']/month[@type='%monthTypes']"/>
		<coverageLevel inTerritory="TW" value="40" match="dates/calendars/calendar[@type='roc']/eras/eraAbbr/era[@type='(0|1)']"/>
		<coverageLevel inTerritory="TW" value="60" match="dates/calendars/calendar[@type='roc']/dateFormats/dateFormatLength[@type='%dateTimeFormatLengths']/dateFormat%stdPattern"/>
		<coverageLevel value="40" match="dates/timeZoneNames/metazone[@type='%metazone40']/long/generic"/>
		<coverageLevel value="40" match="dates/timeZoneNames/metazone[@type='%metazone40']/long/standard"/>
		<coverageLevel value="40" match="dates/timeZoneNames/metazone[@type='%metazone40']/long/daylight"/>
		<coverageLevel value="40" match="dates/timeZoneNames/metazone[@type='GMT']/long/standard"/>
		<coverageLevel value="40" match="delimiters/quotationStart"/>
		<coverageLevel value="40" match="delimiters/quotationEnd"/>
		<coverageLevel value="40" match="delimiters/alternateQuotationStart"/>
		<coverageLevel value="40" match="delimiters/alternateQuotationEnd"/>
		<coverageLevel inLanguage="ar" value="40" match="numbers/decimalFormats[@numberSystem='arab']/decimalFormatLength[@type='%shortLong']/decimalFormat[@type='standard']/pattern[@type='%compactDecimalTypes']"/>
		<coverageLevel inLanguage="%arabextLanguages" value="40" match="numbers/decimalFormats[@numberSystem='arabext']/decimalFormatLength[@type='%shortLong']/decimalFormat[@type='standard']/pattern[@type='%compactDecimalTypes']"/>
		<coverageLevel inLanguage="%devaLanguages" value="40" match="numbers/decimalFormats[@numberSystem='deva']/decimalFormatLength[@type='%shortLong']/decimalFormat[@type='standard']/pattern[@type='%compactDecimalTypes']"/>
		<coverageLevel inLanguage="dz" value="40" match="numbers/decimalFormats[@numberSystem='tibt']/decimalFormatLength[@type='%shortLong']/decimalFormat[@type='standard']/pattern[@type='%compactDecimalTypes']"/>
		<coverageLevel value="40" match="listPatterns/listPattern/listPatternPart[@type='(2|start|middle|end)']"/>
		<coverageLevel value="40" match="units/durationUnit[@type='(hm|ms|hms)']/durationUnitPattern"/>
		<coverageLevel value="60" match="localeDisplayNames/languages/language[@type='%language60']"/>
		<coverageLevel value="60" match="localeDisplayNames/languages/language[@type='%language60'][@alt='%shortVariant']"/>
		<coverageLevel inTerritory="CF" value="60" match="localeDisplayNames/languages/language[@type='sg']"/>
		<coverageLevel inTerritory="CF" value="60" match="localeDisplayNames/languages/language[@type='sg'][@alt='%shortVariant']"/>
		<coverageLevel inTerritory="CG" value="60" match="localeDisplayNames/languages/language[@type='(kg|ln)']"/>
		<coverageLevel inTerritory="CG" value="60" match="localeDisplayNames/languages/language[@type='(kg|ln)'][@alt='%shortVariant']"/>
		<coverageLevel inTerritory="CM" value="60" match="localeDisplayNames/languages/language[@type='%language60_CM']"/>
		<coverageLevel inTerritory="CM" value="60" match="localeDisplayNames/languages/language[@type='%language60_CM'][@alt='%shortVariant']"/>
		<coverageLevel inTerritory="EU" value="60" match="localeDisplayNames/languages/language[@type='%language60_EU']"/>
		<coverageLevel inTerritory="EU" value="60" match="localeDisplayNames/languages/language[@type='%language60_EU'][@alt='%shortVariant']"/>
		<coverageLevel inTerritory="GA" value="60" match="localeDisplayNames/languages/language[@type='%language60_GA']"/>
		<coverageLevel inTerritory="GA" value="60" match="localeDisplayNames/languages/language[@type='%language60_GA'][@alt='%shortVariant']"/>
		<coverageLevel inTerritory="NG" value="60" match="localeDisplayNames/languages/language[@type='%language60_NG']"/>
		<coverageLevel inTerritory="NG" value="60" match="localeDisplayNames/languages/language[@type='%language60_NG'][@alt='%shortVariant']"/>
		<coverageLevel inTerritory="TD" value="60" match="localeDisplayNames/languages/language[@type='%language60_TD']"/>
		<coverageLevel inTerritory="TD" value="60" match="localeDisplayNames/languages/language[@type='%language60_TD'][@alt='%shortVariant']"/>
		<coverageLevel value="60" match="localeDisplayNames/scripts/script[@type='%script60']"/>
		<coverageLevel value="60" match="localeDisplayNames/territories/territory[@type='%territory60']"/>
		<coverageLevel inTerritory="EU" value="60" match="localeDisplayNames/territories/territory[@type='%territory60_EU']"/>
		<coverageLevel value="60" match="numbers/currencies/currency[@type='%currency60']/symbol"/>
		<coverageLevel value="60" match="numbers/currencies/currency[@type='%currency60']/symbol[@alt='narrow']"/>
		<coverageLevel value="60" match="numbers/currencies/currency[@type='%currency60']/displayName"/>
		<coverageLevel value="60" match="numbers/currencies/currency[@type='%currency60']/displayName[@count='${Target-Plurals}']"/>
		<coverageLevel inTerritory="EU" value="60" match="numbers/currencies/currency[@type='%currency60_EU']/symbol"/>
		<coverageLevel inTerritory="EU" value="60" match="numbers/currencies/currency[@type='%currency60_EU']/symbol[@alt='narrow']"/>
		<coverageLevel inTerritory="EU" value="60" match="numbers/currencies/currency[@type='%currency60_EU']/displayName"/>
		<coverageLevel inTerritory="EU" value="60" match="numbers/currencies/currency[@type='%currency60_EU']/displayName[@count='${Target-Plurals}']"/>
		<coverageLevel value="60" match="dates/calendars/calendar[@type='gregorian']/dateTimeFormats/appendItems/appendItem[@request='Timezone']"/>
		<coverageLevel value="60" match="dates/calendars/calendar[@type='gregorian']/dateTimeFormats/availableFormats/dateFormatItem[@id='%dateFormatItems']"/>
		<coverageLevel value="60" match="dates/calendars/calendar[@type='gregorian']/dateTimeFormats/availableFormats/dateFormatItem[@id='%timeFormatItems']"/>
		<coverageLevel value="60" match="dates/calendars/calendar[@type='generic']/dateTimeFormats/availableFormats/dateFormatItem[@id='%dateFormatItems']"/>
		<coverageLevel inTerritory="TH" value="80" match="dates/calendars/calendar[@type='buddhist']/dateTimeFormats/availableFormats/dateFormatItem[@id='%dateFormatItems']"/>
		<coverageLevel inTerritory="%islamicCalendarTerritories" value="80" match="dates/calendars/calendar[@type='islamic']/dateTimeFormats/availableFormats/dateFormatItem[@id='%dateFormatItems']"/>
		<coverageLevel inTerritory="JP" value="80" match="dates/calendars/calendar[@type='japanese']/dateTimeFormats/availableFormats/dateFormatItem[@id='%dateFormatItems']"/>
		<coverageLevel inTerritory="TW" value="80" match="dates/calendars/calendar[@type='roc']/dateTimeFormats/availableFormats/dateFormatItem[@id='%dateFormatItems']"/>
		<coverageLevel value="60" match="dates/calendars/calendar[@type='gregorian']/dateTimeFormats/intervalFormats/intervalFormatItem[@id='%intervalFormatDateItems']/greatestDifference[@id='%intervalFormatGDiff']"/>
		<coverageLevel value="60" match="dates/calendars/calendar[@type='gregorian']/dateTimeFormats/intervalFormats/intervalFormatItem[@id='%intervalFormatTimeItems']/greatestDifference[@id='%intervalFormatGDiff']"/>
		<coverageLevel value="60" match="dates/calendars/calendar[@type='generic']/dateTimeFormats/intervalFormats/intervalFormatItem[@id='%intervalFormatDateItems']/greatestDifference[@id='%intervalFormatGDiff']"/>
		<coverageLevel value="60" match="dates/fields/field[@type='(year|month|week)']/relative[@type='(-1|0|1)']"/>
		<coverageLevel value="60" match="dates/timeZoneNames/metazone[@type='%metazone60']/long/generic"/>
		<coverageLevel value="60" match="dates/timeZoneNames/metazone[@type='%metazone60']/long/standard"/>
		<coverageLevel value="60" match="dates/timeZoneNames/metazone[@type='%metazone60']/long/daylight"/>
		<coverageLevel value="60" match="dates/timeZoneNames/metazone[@type='%metazone60_stdonly']/long/standard"/>
		<coverageLevel value="60" match="units/unitLength[@type='%unitLengths']/unit[@type='%unitDurationTypes']/unitPattern[@count='${Target-Plurals}']"/>
		<coverageLevel value="60" match="units/unitLength[@type='%unitLengths']/unit[@type='%unitsCommonMetric']/unitPattern[@count='${Target-Plurals}']"/>
		<coverageLevel inTerritory="US" value="60" match="units/unitLength[@type='%unitLengths']/unit[@type='%unitsCommonUS']/unitPattern[@count='${Target-Plurals}']"/>
		<coverageLevel value="80" match="localeDisplayNames/languages/language[@type='%language80']"/> 
		<coverageLevel value="80" match="localeDisplayNames/languages/language[@type='%language80'][@alt='%shortVariant']"/> 
		<coverageLevel value="80" match="localeDisplayNames/scripts/script[@type='%script80']"/>
		<coverageLevel value="80" match="localeDisplayNames/territories/territory[@type='%territory80']"/>
		<coverageLevel value="80" match="localeDisplayNames/territories/territory[@type='%territory40'][@alt='%shortVariant']"/>
		<coverageLevel value="80" match="localeDisplayNames/territories/territory[@type='%territory60'][@alt='%shortVariant']"/>
		<coverageLevel value="80" match="localeDisplayNames/territories/territory[@type='%territory80'][@alt='%shortVariant']"/>
		<coverageLevel value="80" match="localeDisplayNames/keys/key[@type='%keys80']"/>
		<coverageLevel value="80" match="localeDisplayNames/types/type[@type='%collationType80'][@key='collation']"/>
		<coverageLevel value="80" match="localeDisplayNames/types/type[@type='%numberingSystem80'][@key='numbers']"/>
		<coverageLevel value="80" match="dates/calendars/calendar[@type='gregorian']/days/dayContext[@type='%contextTypes']/dayWidth[@type='short']/day[@type='%dayTypes']"/>
		<coverageLevel value="80" match="dates/calendars/calendar[@type='gregorian']/dateTimeFormats/availableFormats/dateFormatItem[@id='%dateFormatItemsAll']"/>
		<coverageLevel value="80" match="dates/fields/field[@type='day']/relative[@type='(-3|-2|2|3)']"/>
		<coverageLevel value="80" match="dates/fields/field[@type='%dayTypes']/relative[@type='(-1|0|1)']"/>
		<coverageLevel value="80" match="dates/fields/field[@type='%relativeTimeTypes']/relativeTime[@type='%futurePast']/relativeTimePattern[@count='${Target-Plurals}']"/>		
		<coverageLevel value="80" match="dates/timeZoneNames/metazone[@type='%metazone80']/long/generic"/>
		<coverageLevel value="80" match="dates/timeZoneNames/metazone[@type='%metazone80']/long/standard"/>
		<coverageLevel value="80" match="dates/timeZoneNames/metazone[@type='%metazone80']/long/daylight"/>
		<coverageLevel value="80" match="dates/timeZoneNames/metazone[@type='%metazone80_stdonly']/long/standard"/>
		<coverageLevel value="80" match="dates/timeZoneNames/zone[@type='%timeZones']/exemplarCity"/>
		<coverageLevel value="80" match="dates/timeZoneNames/zone[@type='Europe/(Dublin|London)']/long/daylight"/>
		<coverageLevel value="80" match="dates/timeZoneNames/zone[@type='Europe/(Dublin|London)']/short/daylight"/>
		<coverageLevel value="80" match="numbers/symbols[@numberSystem='latn']/exponential"/>
		<coverageLevel value="80" match="numbers/symbols[@numberSystem='latn']/superscriptingExponent"/>
		<coverageLevel value="80" match="numbers/symbols[@numberSystem='latn']/perMille"/>
		<coverageLevel value="80" match="numbers/symbols[@numberSystem='latn']/infinity"/>
		<coverageLevel value="80" match="numbers/symbols[@numberSystem='latn']/nan"/>
		<coverageLevel inLanguage="ar" value="80" match="numbers/symbols[@numberSystem='arab']/exponential"/>
		<coverageLevel inLanguage="ar" value="80" match="numbers/symbols[@numberSystem='arab']/superscriptingExponent"/>
		<coverageLevel inLanguage="ar" value="80" match="numbers/symbols[@numberSystem='arab']/perMille"/>
		<coverageLevel inLanguage="ar" value="80" match="numbers/symbols[@numberSystem='arab']/infinity"/>
		<coverageLevel inLanguage="ar" value="80" match="numbers/symbols[@numberSystem='arab']/nan"/>
		<coverageLevel inLanguage="%arabextLanguages" value="80" match="numbers/symbols[@numberSystem='arabext']/exponential"/>
		<coverageLevel inLanguage="%arabextLanguages" value="80" match="numbers/symbols[@numberSystem='arabext']/superscriptingExponent"/>
		<coverageLevel inLanguage="%arabextLanguages" value="80" match="numbers/symbols[@numberSystem='arabext']/perMille"/>
		<coverageLevel inLanguage="%arabextLanguages" value="80" match="numbers/symbols[@numberSystem='arabext']/infinity"/>
		<coverageLevel inLanguage="%arabextLanguages" value="80" match="numbers/symbols[@numberSystem='arabext']/nan"/>
		<coverageLevel inLanguage="%devaLanguages" value="80" match="numbers/symbols[@numberSystem='deva']/exponential"/>
		<coverageLevel inLanguage="%devaLanguages" value="80" match="numbers/symbols[@numberSystem='deva']/superscriptingExponent"/>
		<coverageLevel inLanguage="%devaLanguages" value="80" match="numbers/symbols[@numberSystem='deva']/perMille"/>
		<coverageLevel inLanguage="%devaLanguages" value="80" match="numbers/symbols[@numberSystem='deva']/infinity"/>
		<coverageLevel inLanguage="%devaLanguages" value="80" match="numbers/symbols[@numberSystem='deva']/nan"/>
		<coverageLevel inLanguage="dz" value="80" match="numbers/symbols[@numberSystem='tibt']/exponential"/>
		<coverageLevel inLanguage="dz" value="80" match="numbers/symbols[@numberSystem='tibt']/superscriptingExponent"/>
		<coverageLevel inLanguage="dz" value="80" match="numbers/symbols[@numberSystem='tibt']/perMille"/>
		<coverageLevel inLanguage="dz" value="80" match="numbers/symbols[@numberSystem='tibt']/infinity"/>
		<coverageLevel inLanguage="dz" value="80" match="numbers/symbols[@numberSystem='tibt']/nan"/>
		<coverageLevel value="80" match="numbers/currencyFormats[@numberSystem='latn']/currencyFormatLength/currencyFormat%acctPattern"/>
		<coverageLevel value="80" match="numbers/currencies/currency[@type='%currency80']/symbol"/>
		<coverageLevel value="80" match="numbers/currencies/currency[@type='%currency80']/symbol[@alt='narrow']"/>
		<coverageLevel value="80" match="numbers/currencies/currency[@type='%currency80']/displayName"/>
		<coverageLevel value="80" match="numbers/currencies/currency[@type='%currency80']/displayName[@count='${Target-Plurals}']"/>
		<coverageLevel value="80" match="numbers/miscPatterns[@numberSystem='latn']/pattern[@type='%miscPatternTypes']"/>
		<coverageLevel value="80" match="characters/ellipsis[@type='%ellipsisTypes']"/>
		<coverageLevel value="80" match="characters/moreInformation"/>
		<coverageLevel value="80" match="numbers/decimalFormats[@numberSystem='latn']/decimalFormatLength[@type='%shortLong']/decimalFormat[@type='standard']/pattern[@type='%compactDecimalTypes'][@count='${Target-Plurals}']"/>
		<coverageLevel value="80" match="units/unitLength[@type='%unitLengths']/unit[@type='%unitsCommonUS']/unitPattern[@count='${Target-Plurals}']"/>
		<coverageLevel value="80" match="units/unitLength[@type='%unitLengths']/unit[@type='%unitsOther']/unitPattern[@count='${Target-Plurals}']"/>
		<coverageLevel value="80" match="units/unitLength[@type='%unitLengths']/compoundUnit[@type='per']/compoundUnitPattern"/>
		<coverageLevel value="80" match="listPatterns/listPattern[@type='duration(-(short|narrow))?']/listPatternPart[@type='(2|start|middle|end)']"/>
		<coverageLevel value="100" match="dates/calendars/calendar[@type='(buddhist|hebrew|indian|islamic|persian)']/eras/eraAbbr/era[@type='0']"/>
		<coverageLevel value="100" match="dates/calendars/calendar[@type='roc']/eras/eraAbbr/era[@type='(0|1)']"/>
		<coverageLevel value="100" match="dates/calendars/calendar[@type='japanese']/eras/eraAbbr/era[@type='%japaneseEras']"/>
		<coverageLevel value="100" match="dates/calendars/calendar[@type='(buddhist|chinese|dangi|hebrew|islamic|japanese|roc)']/dateFormats/dateFormatLength[@type='%dateTimeFormatLengths']/dateFormat%stdPattern"/>
		<coverageLevel value="100" match="dates/calendars/calendar[@type='japanese']/dateTimeFormats/dateTimeFormatLength[@type='%dateTimeFormatLengths']/dateTimeFormat%stdPattern"/>
		<coverageLevel value="100" match="dates/calendars/calendar[@type='(buddhist|islamic|japanese|roc)']/dateTimeFormats/availableFormats/dateFormatItem[@id='%dateFormatItems']"/>
		<coverageLevel value="100" match="dates/calendars/calendar[@type='(chinese|coptic|dangi|ethiopic|hebrew|indian|islamic|persian)']/months/monthContext[@type='%contextTypes']/monthWidth[@type='%allWidths']/month[@type='%monthTypes']"/>
		<coverageLevel value="100" match="dates/calendars/calendar[@type='(chinese|dangi)']/cyclicNameSets/cyclicNameSet[@type='(dayParts|years|zodiacs)']/cyclicNameContext[@type='format']/cyclicNameWidth[@type='abbreviated']/cyclicName[@type='%cyclicNameTypes']"/>
		<coverageLevel value="100" match="dates/calendars/calendar[@type='hebrew']/months/monthContext[@type='%contextTypes']/monthWidth[@type='%allWidths']/month[@type='7'][@yeartype='leap']"/>
		<coverageLevel value="100" match="localeDisplayNames/languages/language[@type='%language100']"/>
		<coverageLevel value="100" match="localeDisplayNames/languages/language[@type='%language100'][@alt='%shortVariant']"/>
		<coverageLevel value="100" match="localeDisplayNames/scripts/script[@type='%script100']"/>
		<coverageLevel value="100" match="localeDisplayNames/territories/territory[@type='%territory100']"/>
		<coverageLevel value="100" match="localeDisplayNames/territories/territory[@type='%territory100'][@alt='%shortVariant']"/>
		<coverageLevel value="100" match="localeDisplayNames/variants/variant[@type='%variantTypes']"/>
		<coverageLevel value="100" match="localeDisplayNames/keys/key[@type='%keys100']"/>
		<coverageLevel value="100" match="localeDisplayNames/types/type[@type='%numberingSystem100'][@key='numbers']"/>
		<coverageLevel value="100" match="localeDisplayNames/types/type[@type='%calendarType100'][@key='calendar']"/>
		<coverageLevel value="100" match="localeDisplayNames/types/type[@type='%collationType100'][@key='collation']"/>
		<coverageLevel value="100" match="localeDisplayNames/types/type[@type='%collationAlternateValues'][@key='colAlternate']"/>
		<coverageLevel value="100" match="localeDisplayNames/types/type[@type='%collationCases'][@key='colCaseFirst']"/>
		<coverageLevel value="100" match="localeDisplayNames/types/type[@type='%collationStrengths'][@key='colStrength']"/>
		<coverageLevel value="100" match="localeDisplayNames/types/type[@type='%yesNo'][@key='%collationYesNoOptions']"/>
		<coverageLevel value="100" match="localeDisplayNames/types/type[@type='posix'][@key='va']"/>
		<coverageLevel value="100" match="localeDisplayNames/transformNames/transformName[@type='%transformNameTypes']"/>
		<coverageLevel value="100" match="numbers/currencies/currency[@type='%currency30']/symbol"/>
		<coverageLevel value="100" match="numbers/currencies/currency[@type='%currency100']/symbol"/>
		<coverageLevel value="100" match="numbers/currencies/currency[@type='%currency100']/symbol[@alt='narrow']"/>
		<coverageLevel value="100" match="numbers/currencies/currency[@type='%currency100']/displayName"/>
		<coverageLevel value="100" match="numbers/currencies/currency[@type='%currency100']/displayName[@count='${Target-Plurals}']"/>
		<coverageLevel value="100" match="dates/calendars/calendar[@type='gregorian']/eras/eraNames/era[@type='(0|1)']"/>
		<coverageLevel value="100" match="dates/calendars/calendar[@type='gregorian']/eras/eraNames/era[@type='(0|1)'][@alt='variant']"/>
		<coverageLevel value="100" match="dates/calendars/calendar[@type='gregorian']/eras/eraNarrow/era[@type='(0|1)']"/>
		<coverageLevel value="100" match="dates/calendars/calendar[@type='gregorian']/eras/eraNarrow/era[@type='(0|1)'][@alt='variant']"/>
		<coverageLevel value="100" match="dates/calendars/calendar[@type='gregorian']/dayPeriods/dayPeriodContext[@type='format']/dayPeriodWidth[@type='%wideNarrow']/dayPeriod[@type='%ampmTypes']"/>
		<coverageLevel value="100" match="dates/timeZoneNames/metazone[@type='%metazone100']/long/generic"/>
		<coverageLevel value="100" match="dates/timeZoneNames/metazone[@type='%metazone100']/long/standard"/>
		<coverageLevel value="100" match="dates/timeZoneNames/metazone[@type='%metazone100']/long/daylight"/>
		<coverageLevel value="100" match="dates/timeZoneNames/metazone[@type='%metazone100_stdonly']/long/standard"/>
	</coverageLevels>
</supplementalData>
