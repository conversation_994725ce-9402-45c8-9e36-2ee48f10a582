<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php echo $this->template('header.phtml') ?>
<?php if ($returnUrl = $this->controller()->session()->getReturnUrl()): ?>
<a class="f-right" href="<?php echo htmlentities($returnUrl) ?>">Return to Magento Administration</a>
<?php endif ?>
<div style="width:300px; padding:20px; margin:90px auto !important; background:#f6f6f6;">
<form method="post" action="#">
    <input name="form_key" type="hidden" value="<?php echo $this->getFormKey() ?>" />
    <h2 class="page-head">Log In</h2>
    <p><small>Please re-enter your Magento Adminstration Credentials.<br/>Only administrators with full permissions will be able to log in.</small></p>
    <table class="form-list">
        <tr><td class="label"><label for="username">Username:</label></td><td class="value"><input id="username" name="username" value=""/></td></tr>
        <!-- This is a dummy hidden field to trick firefox from auto filling the password -->
        <input type="password" class="input-text no-display" name="dummy" id="dummy" />
        <tr><td class="label"><label for="password">Password:</label></td><td class="value"><input type="password" id="password" name="password" autocomplete="new-password"/></td></tr>
        <tr><td></td>
            <td class="value"><button type="submit">Log In</button></td></tr>
        </table>
</form>
</div>
<?php echo $this->template('footer.phtml') ?>
