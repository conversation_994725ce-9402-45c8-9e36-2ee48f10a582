<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/fixture.xsd">
  <fixture name="user" module="Mage_Admin" type="flat" entity_type="admin_user" collection="Mage\Admin\Model\Resource\User\Collection" repository_class="Mage\Admin\Test\Repository\User" handler_interface="Mage\Admin\Test\Handler\User\UserInterface" class="Mage\Admin\Test\Fixture\User">
    <field name="user_id" is_required="1"/>
    <field name="firstname" is_required="0" group="user-info"/>
    <field name="lastname" is_required="0" group="user-info"/>
    <field name="email" is_required="0" group="user-info"/>
    <field name="username" is_required="0" group="user-info"/>
    <field name="password" is_required="0" group="user-info"/>
    <field name="password_confirmation" is_required="0" group="user-info"/>
    <field name="created" is_required="0"/>
    <field name="modified" is_required="0"/>
    <field name="logdate" is_required="0"/>
    <field name="lognum" is_required="0"/>
    <field name="reload_acl_flag" is_required="0"/>
    <field name="is_active" is_required="0" group="user-info"/>
    <field name="extra" is_required="0"/>
    <field name="rp_token" is_required="0"/>
    <field name="rp_token_created_at" is_required="0"/>
    <field name="failures_num" is_required="0"/>
    <field name="first_failure" is_required="0"/>
    <field name="lock_expires" is_required="0"/>
    <field name="role_id" source="Mage\Admin\Test\Fixture\User\RoleId" group="user-role"/>
    <field name="current_password" group="user-info"/>
  </fixture>
</config>
