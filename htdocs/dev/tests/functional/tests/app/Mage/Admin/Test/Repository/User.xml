<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/Magento/Mtf/Repository/etc/repository.xsd">
    <repository class="Mage\Admin\Test\Repository\User">
        <dataset name="default">
            <field name="username" xsi:type="string">AdminUser%isolation%</field>
            <field name="firstname" xsi:type="string">FirstName%isolation%</field>
            <field name="lastname" xsi:type="string">LastName%isolation%</field>
            <field name="email" xsi:type="string"><EMAIL></field>
            <field name="password" xsi:type="string">123123q123123q</field>
            <field name="password_confirmation" xsi:type="string">123123q123123q</field>
            <field name="is_active" xsi:type="string">Active</field>
            <field name="current_password" xsi:type="string">123123q123123q</field>
        </dataset>
        <dataset name="custom_admin">
            <field name="username" xsi:type="string">AdminUser%isolation%</field>
            <field name="firstname" xsi:type="string">FirstName%isolation%</field>
            <field name="lastname" xsi:type="string">LastName%isolation%</field>
            <field name="email" xsi:type="string"><EMAIL></field>
            <field name="password" xsi:type="string">123123q123123q</field>
            <field name="password_confirmation" xsi:type="string">123123q123123q</field>
            <field name="current_password" xsi:type="string">123123q123123q</field>
            <field name="is_active" xsi:type="string">Active</field>
            <field name="role_id" xsi:type="array">
                <item name="dataset" xsi:type="string">administrators</item>
            </field>
        </dataset>
        <dataset name="admin_without_role">
            <field name="username" xsi:type="string">AdminUser%isolation%</field>
            <field name="firstname" xsi:type="string">FirstName%isolation%</field>
            <field name="lastname" xsi:type="string">LastName%isolation%</field>
            <field name="email" xsi:type="string"><EMAIL></field>
            <field name="password" xsi:type="string">123123q123123q</field>
            <field name="password_confirmation" xsi:type="string">123123q123123q</field>
            <field name="current_password" xsi:type="string">123123q123123q</field>
            <field name="is_active" xsi:type="string">Active</field>
        </dataset>
        <dataset name="admin_for_installation">
            <field name="username" xsi:type="string">AdminUserInstall%isolation%</field>
            <field name="firstname" xsi:type="string">FirstName%isolation%</field>
            <field name="lastname" xsi:type="string">LastName%isolation%</field>
            <field name="email" xsi:type="string"><EMAIL></field>
            <field name="password" xsi:type="string">123123q123123q</field>
            <field name="password_confirmation" xsi:type="string">123123q123123q</field>
        </dataset>
        <dataset name="admin_install_admin">
            <field name="username" xsi:type="string">admin</field>
            <field name="firstname" xsi:type="string">FirstName%isolation%</field>
            <field name="lastname" xsi:type="string">LastName%isolation%</field>
            <field name="email" xsi:type="string"><EMAIL></field>
            <field name="password" xsi:type="string">123123q123123q</field>
            <field name="password_confirmation" xsi:type="string">123123q123123q</field>
        </dataset>
    </repository>
</config>
