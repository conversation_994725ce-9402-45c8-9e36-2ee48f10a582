<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Mage\Downloadable\Test\TestCase\CreateDownloadableProductEntityTest" summary="CreateDownloadableProductEntityTest">
        <variation name="CreateDownloadableProductEntityTestVariation1" firstConstraint="Mage\Catalog\Test\Constraint\AssertProductSaveMessage" method="test">
            <data name="description" xsi:type="string">Create downloadable product with downloadable samples</data>
            <data name="product/data/name" xsi:type="string">DownloadableProduct_%isolation%</data>
            <data name="product/data/url_key" xsi:type="string">downloadable-product-%isolation%</data>
            <data name="product/data/visibility" xsi:type="string">Catalog, Search</data>
            <data name="product/data/sku" xsi:type="string">DownloadableProduct_%isolation%</data>
            <data name="product/data/tax_class_id/dataset" xsi:type="string">Taxable Goods</data>
            <data name="product/data/price/value" xsi:type="string">1</data>
            <data name="product/data/price/dataset" xsi:type="string">-</data>
            <data name="product/data/special_price" xsi:type="string">-</data>
            <data name="product/data/status" xsi:type="string">Enabled</data>
            <data name="product/data/description" xsi:type="string">This is description for downloadable product</data>
            <data name="product/data/short_description" xsi:type="string">This is short description for downloadable product</data>
            <data name="product/data/stock_data/qty" xsi:type="string">10</data>
            <data name="product/data/stock_data/is_in_stock" xsi:type="string">In Stock</data>
            <data name="product/data/is_virtual" xsi:type="string">Yes</data>
            <data name="product/data/stock_data/use_config_min_qty" xsi:type="string">-</data>
            <data name="product/data/stock_data/min_qty" xsi:type="string">-</data>
            <data name="product/data/downloadable_sample/dataset" xsi:type="string">default</data>
            <data name="product/data/downloadable_links/dataset" xsi:type="string">default</data>
            <data name="product/data/custom_options/dataset" xsi:type="string">-</data>
            <data name="product/data/group_price/dataset" xsi:type="string">-</data>
            <data name="product/data/tier_price/dataset" xsi:type="string">-</data>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductSaveMessage" next="Mage\Catalog\Test\Constraint\AssertProductInGrid"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductInGrid" next="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm" prev="Mage\Catalog\Test\Constraint\AssertProductSaveMessage"/>
            <constraint name="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm" next="Mage\Catalog\Test\Constraint\AssertProductVisibleInCategory" prev="Mage\Catalog\Test\Constraint\AssertProductInGrid"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductVisibleInCategory" next="Mage\Downloadable\Test\Constraint\AssertDownloadableSamplesData" prev="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm"/>
            <constraint name="Mage\Downloadable\Test\Constraint\AssertDownloadableSamplesData" next="Mage\Downloadable\Test\Constraint\AssertDownloadableLinksData" prev="Mage\Catalog\Test\Constraint\AssertProductVisibleInCategory"/>
            <constraint name="Mage\Downloadable\Test\Constraint\AssertDownloadableLinksData" prev="Mage\Downloadable\Test\Constraint\AssertDownloadableSamplesData"/>
        </variation>
        <variation name="CreateDownloadableProductEntityTestVariation2" firstConstraint="Mage\Catalog\Test\Constraint\AssertProductSaveMessage" method="test">
            <data name="description" xsi:type="string">Create downloadable product with two downloadable links, downloadable samples and custom options </data>
            <data name="product/data/name" xsi:type="string">DownloadableProduct_%isolation%</data>
            <data name="product/data/url_key" xsi:type="string">downloadable-product-%isolation%</data>
            <data name="product/data/visibility" xsi:type="string">Catalog, Search</data>
            <data name="product/data/sku" xsi:type="string">DownloadableProduct_%isolation%</data>
            <data name="product/data/tax_class_id/dataset" xsi:type="string">Taxable Goods</data>
            <data name="product/data/price/value" xsi:type="string">65</data>
            <data name="product/data/price/dataset" xsi:type="string">-</data>
            <data name="product/data/special_price" xsi:type="string">-</data>
            <data name="product/data/status" xsi:type="string">Enabled</data>
            <data name="product/data/description" xsi:type="string">This is description for downloadable product</data>
            <data name="product/data/short_description" xsi:type="string">This is short description for downloadable product</data>
            <data name="product/data/stock_data/qty" xsi:type="string">11</data>
            <data name="product/data/stock_data/is_in_stock" xsi:type="string">In Stock</data>
            <data name="product/data/is_virtual" xsi:type="string">Yes</data>
            <data name="product/data/stock_data/use_config_min_qty" xsi:type="string">-</data>
            <data name="product/data/stock_data/min_qty" xsi:type="string">-</data>
            <data name="product/data/downloadable_sample/dataset" xsi:type="string">default</data>
            <data name="product/data/downloadable_links/dataset" xsi:type="string">with_two_separately_links</data>
            <data name="product/data/custom_options/dataset" xsi:type="string">default</data>
            <data name="product/data/group_price/dataset" xsi:type="string">-</data>
            <data name="product/data/tier_price/dataset" xsi:type="string">-</data>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductSaveMessage" next="Mage\Catalog\Test\Constraint\AssertProductPage"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductPage" next="Mage\Catalog\Test\Constraint\AssertProductInGrid" prev="Mage\Catalog\Test\Constraint\AssertProductSaveMessage"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductInGrid" next="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm" prev="Mage\Catalog\Test\Constraint\AssertProductPage"/>
            <constraint name="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm" next="Mage\Catalog\Test\Constraint\AssertProductCustomOptionsOnProductPage" prev="Mage\Catalog\Test\Constraint\AssertProductInGrid"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductCustomOptionsOnProductPage" next="Mage\Downloadable\Test\Constraint\AssertDownloadableSamplesData" prev="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm"/>
            <constraint name="Mage\Downloadable\Test\Constraint\AssertDownloadableSamplesData" next="Mage\Downloadable\Test\Constraint\AssertDownloadableLinksData" prev="Mage\Catalog\Test\Constraint\AssertProductCustomOptionsOnProductPage"/>
            <constraint name="Mage\Downloadable\Test\Constraint\AssertDownloadableLinksData" prev="Mage\Downloadable\Test\Constraint\AssertDownloadableSamplesData"/>
        </variation>
        <variation name="CreateDownloadableProductEntityTestVariation3" firstConstraint="Mage\Catalog\Test\Constraint\AssertProductSaveMessage" method="test">
            <data name="description" xsi:type="string">Create downloadable product with downloadable links without tax class</data>
            <data name="product/data/name" xsi:type="string">DownloadableProduct_%isolation%</data>
            <data name="product/data/url_key" xsi:type="string">downloadable-product-%isolation%</data>
            <data name="product/data/visibility" xsi:type="string">Catalog, Search</data>
            <data name="product/data/sku" xsi:type="string">DownloadableProduct_%isolation%</data>
            <data name="product/data/tax_class_id/dataset" xsi:type="string">None</data>
            <data name="product/data/price/value" xsi:type="string">98</data>
            <data name="product/data/price/dataset" xsi:type="string">-</data>
            <data name="product/data/special_price" xsi:type="string">-</data>
            <data name="product/data/status" xsi:type="string">Enabled</data>
            <data name="product/data/description" xsi:type="string">This is description for downloadable product</data>
            <data name="product/data/short_description" xsi:type="string">This is short description for downloadable product</data>
            <data name="product/data/stock_data/qty" xsi:type="string">5</data>
            <data name="product/data/stock_data/is_in_stock" xsi:type="string">In Stock</data>
            <data name="product/data/is_virtual" xsi:type="string">Yes</data>
            <data name="product/data/stock_data/use_config_min_qty" xsi:type="string">-</data>
            <data name="product/data/stock_data/min_qty" xsi:type="string">-</data>
            <data name="product/data/downloadable_sample/dataset" xsi:type="string">-</data>
            <data name="product/data/downloadable_links/dataset" xsi:type="string">default</data>
            <data name="product/data/custom_options/dataset" xsi:type="string">-</data>
            <data name="product/data/group_price/dataset" xsi:type="string">-</data>
            <data name="product/data/tier_price/dataset" xsi:type="string">-</data>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductSaveMessage" next="Mage\Catalog\Test\Constraint\AssertProductInGrid"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductInGrid" next="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm" prev="Mage\Catalog\Test\Constraint\AssertProductSaveMessage"/>
            <constraint name="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm" next="Mage\Catalog\Test\Constraint\AssertProductPage" prev="Mage\Catalog\Test\Constraint\AssertProductInGrid"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductPage" prev="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm"/>
        </variation>
        <variation name="CreateDownloadableProductEntityTestVariation4" firstConstraint="Mage\Catalog\Test\Constraint\AssertProductSaveMessage" method="test">
            <data name="description" xsi:type="string">Create downloadable product with custom options</data>
            <data name="product/data/name" xsi:type="string">DownloadableProduct_%isolation%</data>
            <data name="product/data/url_key" xsi:type="string">downloadable-product-%isolation%</data>
            <data name="product/data/visibility" xsi:type="string">Catalog, Search</data>
            <data name="product/data/sku" xsi:type="string">DownloadableProduct_%isolation%</data>
            <data name="product/data/tax_class_id/dataset" xsi:type="string">Taxable Goods</data>
            <data name="product/data/price/value" xsi:type="string">33</data>
            <data name="product/data/price/dataset" xsi:type="string">-</data>
            <data name="product/data/special_price" xsi:type="string">-</data>
            <data name="product/data/status" xsi:type="string">Enabled</data>
            <data name="product/data/description" xsi:type="string">This is description for downloadable product</data>
            <data name="product/data/short_description" xsi:type="string">This is short description for downloadable product</data>
            <data name="product/data/stock_data/qty" xsi:type="string">10</data>
            <data name="product/data/stock_data/is_in_stock" xsi:type="string">In Stock</data>
            <data name="product/data/is_virtual" xsi:type="string">Yes</data>
            <data name="product/data/stock_data/use_config_min_qty" xsi:type="string">-</data>
            <data name="product/data/stock_data/min_qty" xsi:type="string">-</data>
            <data name="product/data/downloadable_sample/dataset" xsi:type="string">-</data>
            <data name="product/data/downloadable_links/dataset" xsi:type="string">default</data>
            <data name="product/data/custom_options/dataset" xsi:type="string">default</data>
            <data name="product/data/group_price/dataset" xsi:type="string">-</data>
            <data name="product/data/tier_price/dataset" xsi:type="string">-</data>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductSaveMessage" next="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm"/>
            <constraint name="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm" next="Mage\Catalog\Test\Constraint\AssertProductCustomOptionsOnProductPage" prev="Mage\Catalog\Test\Constraint\AssertProductSaveMessage"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductCustomOptionsOnProductPage" next="Mage\Catalog\Test\Constraint\AssertProductVisibleInCategory" prev="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductVisibleInCategory" next="Mage\Catalog\Test\Constraint\AssertProductPage" prev="Mage\Catalog\Test\Constraint\AssertProductCustomOptionsOnProductPage"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductPage" next="Mage\Downloadable\Test\Constraint\AssertDownloadableLinksData" prev="Mage\Catalog\Test\Constraint\AssertProductVisibleInCategory"/>
            <constraint name="Mage\Downloadable\Test\Constraint\AssertDownloadableLinksData" prev="Mage\Catalog\Test\Constraint\AssertProductPage"/>
        </variation>
        <variation name="CreateDownloadableProductEntityTestVariation5" firstConstraint="Mage\Catalog\Test\Constraint\AssertProductSaveMessage" method="test">
            <data name="description" xsi:type="string">Create downloadable product with downloadable links and tax class</data>
            <data name="product/data/name" xsi:type="string">DownloadableProduct_%isolation%</data>
            <data name="product/data/url_key" xsi:type="string">downloadable-product-%isolation%</data>
            <data name="product/data/visibility" xsi:type="string">Catalog, Search</data>
            <data name="product/data/sku" xsi:type="string">DownloadableProduct_%isolation%</data>
            <data name="product/data/tax_class_id/dataset" xsi:type="string">Taxable Goods</data>
            <data name="product/data/price/value" xsi:type="string">100</data>
            <data name="product/data/price/dataset" xsi:type="string">-</data>
            <data name="product/data/special_price" xsi:type="string">-</data>
            <data name="product/data/status" xsi:type="string">Enabled</data>
            <data name="product/data/description" xsi:type="string">This is description for downloadable product</data>
            <data name="product/data/short_description" xsi:type="string">This is short description for downloadable product</data>
            <data name="product/data/stock_data/qty" xsi:type="string">1</data>
            <data name="product/data/stock_data/is_in_stock" xsi:type="string">In Stock</data>
            <data name="product/data/is_virtual" xsi:type="string">Yes</data>
            <data name="product/data/stock_data/use_config_min_qty" xsi:type="string">-</data>
            <data name="product/data/stock_data/min_qty" xsi:type="string">-</data>
            <data name="product/data/downloadable_sample/dataset" xsi:type="string">-</data>
            <data name="product/data/downloadable_links/dataset" xsi:type="string">default</data>
            <data name="product/data/custom_options/dataset" xsi:type="string">-</data>
            <data name="product/data/group_price/dataset" xsi:type="string">-</data>
            <data name="product/data/tier_price/dataset" xsi:type="string">-</data>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductSaveMessage" next="Mage\Catalog\Test\Constraint\AssertProductInGrid"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductInGrid" next="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm" prev="Mage\Catalog\Test\Constraint\AssertProductSaveMessage"/>
            <constraint name="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm" next="Mage\Catalog\Test\Constraint\AssertProductVisibleInCategory" prev="Mage\Catalog\Test\Constraint\AssertProductInGrid"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductVisibleInCategory" next="Mage\Catalog\Test\Constraint\AssertProductPage" prev="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductPage" next="Mage\Catalog\Test\Constraint\AssertProductInStock" prev="Mage\Catalog\Test\Constraint\AssertProductVisibleInCategory"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductInStock" prev="Mage\Catalog\Test\Constraint\AssertProductPage"/>
        </variation>
        <variation name="CreateDownloadableProductEntityTestVariation6" firstConstraint="Mage\Catalog\Test\Constraint\AssertProductSaveMessage" method="test">
            <data name="description" xsi:type="string">Create downloadable product with stock data min qty</data>
            <data name="product/data/name" xsi:type="string">DownloadableProduct_%isolation%</data>
            <data name="product/data/url_key" xsi:type="string">downloadable-product-%isolation%</data>
            <data name="product/data/visibility" xsi:type="string">Catalog, Search</data>
            <data name="product/data/sku" xsi:type="string">DownloadableProduct_%isolation%</data>
            <data name="product/data/tax_class_id/dataset" xsi:type="string">Taxable Goods</data>
            <data name="product/data/price/value" xsi:type="string">9999</data>
            <data name="product/data/price/dataset" xsi:type="string">-</data>
            <data name="product/data/special_price" xsi:type="string">-</data>
            <data name="product/data/status" xsi:type="string">Enabled</data>
            <data name="product/data/description" xsi:type="string">This is description for downloadable product</data>
            <data name="product/data/short_description" xsi:type="string">This is short description for downloadable product</data>
            <data name="product/data/stock_data/qty" xsi:type="string">-</data>
            <data name="product/data/stock_data/is_in_stock" xsi:type="string">-</data>
            <data name="product/data/is_virtual" xsi:type="string">Yes</data>
            <data name="product/data/stock_data/use_config_min_qty" xsi:type="string">No</data>
            <data name="product/data/stock_data/min_qty" xsi:type="string">123</data>
            <data name="product/data/downloadable_sample/dataset" xsi:type="string">-</data>
            <data name="product/data/downloadable_links/dataset" xsi:type="string">default</data>
            <data name="product/data/custom_options/dataset" xsi:type="string">-</data>
            <data name="product/data/group_price/dataset" xsi:type="string">-</data>
            <data name="product/data/tier_price/dataset" xsi:type="string">-</data>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductSaveMessage" next="Mage\Catalog\Test\Constraint\AssertProductInGrid"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductInGrid" next="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm" prev="Mage\Catalog\Test\Constraint\AssertProductSaveMessage"/>
            <constraint name="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm" next="Mage\Catalog\Test\Constraint\AssertProductOutOfStock" prev="Mage\Catalog\Test\Constraint\AssertProductInGrid"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductOutOfStock" prev="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm"/>
        </variation>
        <variation name="CreateDownloadableProductEntityTestVariation7" firstConstraint="Mage\Catalog\Test\Constraint\AssertProductSaveMessage" method="test">
            <data name="description" xsi:type="string">Create downloadable product with downloadable links</data>
            <data name="product/data/name" xsi:type="string">DownloadableProduct_%isolation%</data>
            <data name="product/data/url_key" xsi:type="string">downloadable-product-%isolation%</data>
            <data name="product/data/visibility" xsi:type="string">Catalog, Search</data>
            <data name="product/data/sku" xsi:type="string">DownloadableProduct_%isolation%</data>
            <data name="product/data/tax_class_id/dataset" xsi:type="string">Taxable Goods</data>
            <data name="product/data/price/value" xsi:type="string">100</data>
            <data name="product/data/price/dataset" xsi:type="string">-</data>
            <data name="product/data/special_price" xsi:type="string">-</data>
            <data name="product/data/status" xsi:type="string">Enabled</data>
            <data name="product/data/description" xsi:type="string">This is description for downloadable product</data>
            <data name="product/data/short_description" xsi:type="string">This is short description for downloadable product</data>
            <data name="product/data/stock_data/qty" xsi:type="string">50</data>
            <data name="product/data/stock_data/is_in_stock" xsi:type="string">In Stock</data>
            <data name="product/data/is_virtual" xsi:type="string">Yes</data>
            <data name="product/data/stock_data/use_config_min_qty" xsi:type="string">-</data>
            <data name="product/data/stock_data/min_qty" xsi:type="string">-</data>
            <data name="product/data/downloadable_sample/dataset" xsi:type="string">-</data>
            <data name="product/data/downloadable_links/dataset" xsi:type="string">default</data>
            <data name="product/data/custom_options/dataset" xsi:type="string">-</data>
            <data name="product/data/group_price/dataset" xsi:type="string">-</data>
            <data name="product/data/tier_price/dataset" xsi:type="string">-</data>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductSaveMessage" next="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm"/>
            <constraint name="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm" next="Mage\Catalog\Test\Constraint\AssertProductVisibleInCategory" prev="Mage\Catalog\Test\Constraint\AssertProductSaveMessage"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductVisibleInCategory" next="Mage\Catalog\Test\Constraint\AssertProductPage" prev="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductPage" prev="Mage\Catalog\Test\Constraint\AssertProductVisibleInCategory"/>
        </variation>
        <variation name="CreateDownloadableProductEntityTestVariation8" firstConstraint="Mage\Catalog\Test\Constraint\AssertProductSaveMessage" method="test">
            <data name="description" xsi:type="string">Create downloadable product with three downloadable links, downloadable samples and custom options </data>
            <data name="product/data/name" xsi:type="string">DownloadableProduct_%isolation%</data>
            <data name="product/data/url_key" xsi:type="string">downloadable-product-%isolation%</data>
            <data name="product/data/visibility" xsi:type="string">Catalog, Search</data>
            <data name="product/data/sku" xsi:type="string">DownloadableProduct_%isolation%</data>
            <data name="product/data/tax_class_id/dataset" xsi:type="string">Taxable Goods</data>
            <data name="product/data/price/value" xsi:type="string">65</data>
            <data name="product/data/price/dataset" xsi:type="string">-</data>
            <data name="product/data/special_price" xsi:type="string">-</data>
            <data name="product/data/status" xsi:type="string">Enabled</data>
            <data name="product/data/description" xsi:type="string">This is description for downloadable product</data>
            <data name="product/data/short_description" xsi:type="string">This is short description for downloadable product</data>
            <data name="product/data/stock_data/qty" xsi:type="string">11</data>
            <data name="product/data/stock_data/is_in_stock" xsi:type="string">In Stock</data>
            <data name="product/data/is_virtual" xsi:type="string">Yes</data>
            <data name="product/data/stock_data/use_config_min_qty" xsi:type="string">-</data>
            <data name="product/data/stock_data/min_qty" xsi:type="string">-</data>
            <data name="product/data/downloadable_sample/dataset" xsi:type="string">default</data>
            <data name="product/data/downloadable_links/dataset" xsi:type="string">with_three_links</data>
            <data name="product/data/custom_options/dataset" xsi:type="string">default</data>
            <data name="product/data/group_price/dataset" xsi:type="string">-</data>
            <data name="product/data/tier_price/dataset" xsi:type="string">-</data>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductSaveMessage" next="Mage\Catalog\Test\Constraint\AssertProductInGrid"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductInGrid" next="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm" prev="Mage\Catalog\Test\Constraint\AssertProductSaveMessage"/>
            <constraint name="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm" next="Mage\Catalog\Test\Constraint\AssertProductPage" prev="Mage\Catalog\Test\Constraint\AssertProductInGrid"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductPage" next="Mage\Downloadable\Test\Constraint\AssertDownloadableLinksData" prev="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm"/>
            <constraint name="Mage\Downloadable\Test\Constraint\AssertDownloadableLinksData" next="Mage\Catalog\Test\Constraint\AssertProductCustomOptionsOnProductPage" prev="Mage\Catalog\Test\Constraint\AssertProductPage"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductCustomOptionsOnProductPage" prev="Mage\Downloadable\Test\Constraint\AssertDownloadableLinksData"/>
        </variation>
        <variation name="CreateDownloadableProductEntityTestVariation9" firstConstraint="Mage\Catalog\Test\Constraint\AssertProductSaveMessage" method="test">
            <data name="description" xsi:type="string">Create downloadable product out of stock</data>
            <data name="product/data/name" xsi:type="string">DownloadableProduct_%isolation%</data>
            <data name="product/data/url_key" xsi:type="string">downloadable-product-%isolation%</data>
            <data name="product/data/visibility" xsi:type="string">Catalog, Search</data>
            <data name="product/data/sku" xsi:type="string">DownloadableProduct_%isolation%</data>
            <data name="product/data/tax_class_id/dataset" xsi:type="string">Taxable Goods</data>
            <data name="product/data/price/value" xsi:type="string">100</data>
            <data name="product/data/price/dataset" xsi:type="string">-</data>
            <data name="product/data/special_price" xsi:type="string">-</data>
            <data name="product/data/status" xsi:type="string">Enabled</data>
            <data name="product/data/description" xsi:type="string">This is description for downloadable product</data>
            <data name="product/data/short_description" xsi:type="string">This is short description for downloadable product</data>
            <data name="product/data/stock_data/qty" xsi:type="string">50</data>
            <data name="product/data/stock_data/is_in_stock" xsi:type="string">Out of Stock</data>
            <data name="product/data/is_virtual" xsi:type="string">Yes</data>
            <data name="product/data/stock_data/use_config_min_qty" xsi:type="string">-</data>
            <data name="product/data/stock_data/min_qty" xsi:type="string">-</data>
            <data name="product/data/downloadable_sample/dataset" xsi:type="string">-</data>
            <data name="product/data/downloadable_links/dataset" xsi:type="string">default</data>
            <data name="product/data/custom_options/dataset" xsi:type="string">-</data>
            <data name="product/data/group_price/dataset" xsi:type="string">-</data>
            <data name="product/data/tier_price/dataset" xsi:type="string">-</data>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductSaveMessage" next="Mage\Catalog\Test\Constraint\AssertProductOutOfStock"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductOutOfStock" next="Mage\Catalog\Test\Constraint\AssertProductInGrid" prev="Mage\Catalog\Test\Constraint\AssertProductSaveMessage"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductInGrid" next="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm" prev="Mage\Catalog\Test\Constraint\AssertProductOutOfStock"/>
            <constraint name="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm" prev="Mage\Catalog\Test\Constraint\AssertProductInGrid"/>
        </variation>
        <variation name="CreateDownloadableProductEntityTestVariation10" firstConstraint="Mage\Catalog\Test\Constraint\AssertProductSaveMessage" method="test">
            <data name="description" xsi:type="string">Create downloadable product with special price</data>
            <data name="product/data/name" xsi:type="string">DownloadableProduct_%isolation%</data>
            <data name="product/data/url_key" xsi:type="string">downloadable-product-%isolation%</data>
            <data name="product/data/visibility" xsi:type="string">Catalog, Search</data>
            <data name="product/data/sku" xsi:type="string">DownloadableProduct_%isolation%</data>
            <data name="product/data/tax_class_id/dataset" xsi:type="string">Taxable Goods</data>
            <data name="product/data/price/value" xsi:type="string">10</data>
            <data name="product/data/price/dataset" xsi:type="string">-</data>
            <data name="product/data/special_price" xsi:type="string">5</data>
            <data name="product/data/status" xsi:type="string">Enabled</data>
            <data name="product/data/description" xsi:type="string">This is description for downloadable product</data>
            <data name="product/data/short_description" xsi:type="string">This is short description for downloadable product</data>
            <data name="product/data/stock_data/qty" xsi:type="string">10</data>
            <data name="product/data/stock_data/is_in_stock" xsi:type="string">In Stock</data>
            <data name="product/data/is_virtual" xsi:type="string">Yes</data>
            <data name="product/data/stock_data/use_config_min_qty" xsi:type="string">-</data>
            <data name="product/data/stock_data/min_qty" xsi:type="string">-</data>
            <data name="product/data/downloadable_sample/dataset" xsi:type="string">-</data>
            <data name="product/data/downloadable_links/dataset" xsi:type="string">default</data>
            <data name="product/data/custom_options/dataset" xsi:type="string">-</data>
            <data name="product/data/group_price/dataset" xsi:type="string">-</data>
            <data name="product/data/tier_price/dataset" xsi:type="string">-</data>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductSaveMessage" next="Mage\Catalog\Test\Constraint\AssertProductInGrid"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductInGrid" next="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm" prev="Mage\Catalog\Test\Constraint\AssertProductSaveMessage"/>
            <constraint name="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm" next="Mage\Catalog\Test\Constraint\AssertProductPage" prev="Mage\Catalog\Test\Constraint\AssertProductInGrid"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductPage" next="Mage\Catalog\Test\Constraint\AssertProductSpecialPriceOnProductPage" prev="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductSpecialPriceOnProductPage" prev="Mage\Catalog\Test\Constraint\AssertProductPage"/>
        </variation>
        <variation name="CreateDownloadableProductEntityTestVariation11" firstConstraint="Mage\Catalog\Test\Constraint\AssertProductSaveMessage" method="test">
            <data name="description" xsi:type="string">Create downloadable product with group price</data>
            <data name="product/data/name" xsi:type="string">DownloadableProduct_%isolation%</data>
            <data name="product/data/url_key" xsi:type="string">downloadable-product-%isolation%</data>
            <data name="product/data/visibility" xsi:type="string">Catalog, Search</data>
            <data name="product/data/sku" xsi:type="string">DownloadableProduct_%isolation%</data>
            <data name="product/data/tax_class_id/dataset" xsi:type="string">Taxable Goods</data>
            <data name="product/data/price/value" xsi:type="string">365</data>
            <data name="product/data/price/dataset" xsi:type="string">-</data>
            <data name="product/data/special_price" xsi:type="string">-</data>
            <data name="product/data/status" xsi:type="string">Enabled</data>
            <data name="product/data/description" xsi:type="string">This is description for downloadable product</data>
            <data name="product/data/short_description" xsi:type="string">This is short description for downloadable product</data>
            <data name="product/data/stock_data/qty" xsi:type="string">23</data>
            <data name="product/data/stock_data/is_in_stock" xsi:type="string">In Stock</data>
            <data name="product/data/is_virtual" xsi:type="string">Yes</data>
            <data name="product/data/stock_data/use_config_min_qty" xsi:type="string">-</data>
            <data name="product/data/stock_data/min_qty" xsi:type="string">-</data>
            <data name="product/data/downloadable_sample/dataset" xsi:type="string">-</data>
            <data name="product/data/downloadable_links/dataset" xsi:type="string">default</data>
            <data name="product/data/custom_options/dataset" xsi:type="string">-</data>
            <data name="product/data/group_price/dataset" xsi:type="string">for_not_logged_users</data>
            <data name="product/data/tier_price/dataset" xsi:type="string">-</data>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductSaveMessage" next="Mage\Catalog\Test\Constraint\AssertProductInGrid"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductInGrid" next="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm" prev="Mage\Catalog\Test\Constraint\AssertProductSaveMessage"/>
            <constraint name="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm" next="Mage\Catalog\Test\Constraint\AssertProductPage" prev="Mage\Catalog\Test\Constraint\AssertProductInGrid"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductPage" next="Mage\Catalog\Test\Constraint\AssertProductGroupedPriceOnProductPage" prev="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductGroupedPriceOnProductPage" prev="Mage\Catalog\Test\Constraint\AssertProductPage"/>
        </variation>
        <variation name="CreateDownloadableProductEntityTestVariation12" firstConstraint="Mage\Catalog\Test\Constraint\AssertProductSaveMessage" method="test">
            <data name="description" xsi:type="string">Create downloadable product with tier price</data>
            <data name="product/data/name" xsi:type="string">DownloadableProduct_%isolation%</data>
            <data name="product/data/url_key" xsi:type="string">downloadable-product-%isolation%</data>
            <data name="product/data/visibility" xsi:type="string">Catalog, Search</data>
            <data name="product/data/sku" xsi:type="string">DownloadableProduct_%isolation%</data>
            <data name="product/data/tax_class_id/dataset" xsi:type="string">Taxable Goods</data>
            <data name="product/data/price/value" xsi:type="string">250</data>
            <data name="product/data/price/dataset" xsi:type="string">with_tier_price</data>
            <data name="product/data/special_price" xsi:type="string">-</data>
            <data name="product/data/status" xsi:type="string">Enabled</data>
            <data name="product/data/description" xsi:type="string">This is description for downloadable product</data>
            <data name="product/data/short_description" xsi:type="string">This is short description for downloadable product</data>
            <data name="product/data/stock_data/qty" xsi:type="string">65</data>
            <data name="product/data/stock_data/is_in_stock" xsi:type="string">In Stock</data>
            <data name="product/data/is_virtual" xsi:type="string">Yes</data>
            <data name="product/data/stock_data/use_config_min_qty" xsi:type="string">-</data>
            <data name="product/data/stock_data/min_qty" xsi:type="string">-</data>
            <data name="product/data/downloadable_sample/dataset" xsi:type="string">-</data>
            <data name="product/data/downloadable_links/dataset" xsi:type="string">default</data>
            <data name="product/data/custom_options/dataset" xsi:type="string">-</data>
            <data name="product/data/group_price/dataset" xsi:type="string">-</data>
            <data name="product/data/tier_price/dataset" xsi:type="string">for_all_groups</data>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductSaveMessage" next="Mage\Catalog\Test\Constraint\AssertProductInGrid"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductInGrid" next="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm" prev="Mage\Catalog\Test\Constraint\AssertProductSaveMessage"/>
            <constraint name="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm" next="Mage\Catalog\Test\Constraint\AssertProductPage" prev="Mage\Catalog\Test\Constraint\AssertProductInGrid"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductPage" next="Mage\Catalog\Test\Constraint\AssertProductTierPriceOnProductPage" prev="Mage\Downloadable\Test\Constraint\AssertDownloadableProductForm"/>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductTierPriceOnProductPage" prev="Mage\Catalog\Test\Constraint\AssertProductPage"/>
        </variation>
    </testCase>
</config>
