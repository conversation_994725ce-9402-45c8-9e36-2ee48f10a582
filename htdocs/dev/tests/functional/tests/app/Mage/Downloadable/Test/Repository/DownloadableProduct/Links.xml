<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="../../../../../../../vendor/magento/mtf/Magento/Mtf/Repository/etc/repository.xsd">
    <repository class="Mage\Downloadable\Test\Repository\DownloadableProduct\Links">
        <dataset name="default">
            <field name="title" xsi:type="string">Links%isolation%</field>
            <field name="links_purchased_separately" xsi:type="string">Yes</field>
            <field name="downloadable" xsi:type="array">
                <item name="link" xsi:type="array">
                    <item name="0" xsi:type="array">
                        <item name="title" xsi:type="string">link1%isolation%</item>
                        <item name="price" xsi:type="string">2.43</item>
                        <item name="number_of_downloads" xsi:type="string">2</item>
                        <item name="sample" xsi:type="array">
                            <item name="sample_type_url" xsi:type="string">Yes</item>
                            <item name="sample_url" xsi:type="string">http://example.com</item>
                        </item>
                        <item name="file_type_url" xsi:type="string">Yes</item>
                        <item name="file_link_url" xsi:type="string">http://example.com</item>
                        <item name="is_shareable" xsi:type="string">No</item>
                        <item name="sort_order" xsi:type="string">1</item>
                    </item>
                    <item name="1" xsi:type="array">
                        <item name="title" xsi:type="string">link2%isolation%</item>
                        <item name="price" xsi:type="string">3</item>
                        <item name="number_of_downloads" xsi:type="string">3</item>
                        <item name="sample" xsi:type="array">
                            <item name="sample_type_url" xsi:type="string">Yes</item>
                            <item name="sample_url" xsi:type="string">http://example.net</item>
                        </item>
                        <item name="file_type_url" xsi:type="string">Yes</item>
                        <item name="file_link_url" xsi:type="string">http://example.net</item>
                        <item name="is_shareable" xsi:type="string">Yes</item>
                        <item name="sort_order" xsi:type="string">0</item>
                    </item>
                </item>
            </field>
        </dataset>

        <dataset name="with_two_separately_links">
            <field name="title" xsi:type="string">Links%isolation%</field>
            <field name="links_purchased_separately" xsi:type="string">Yes</field>
            <field name="downloadable" xsi:type="array">
                <item name="link" xsi:type="array">
                    <item name="0" xsi:type="array">
                        <item name="title" xsi:type="string">link1%isolation%</item>
                        <item name="price" xsi:type="string">2.43</item>
                        <item name="number_of_downloads" xsi:type="string">2</item>
                        <item name="is_shareable" xsi:type="string">No</item>
                        <item name="sample" xsi:type="array">
                            <item name="sample_type_url" xsi:type="string">Yes</item>
                            <item name="sample_url" xsi:type="string">http://example.com/sample</item>
                        </item>
                        <item name="file_type_url" xsi:type="string">Yes</item>
                        <item name="file_link_url" xsi:type="string">http://example.com</item>
                        <item name="sort_order" xsi:type="string">0</item>
                    </item>
                    <item name="1" xsi:type="array">
                        <item name="title" xsi:type="string">link2%isolation%</item>
                        <item name="price" xsi:type="string">3</item>
                        <item name="number_of_downloads" xsi:type="string">3</item>
                        <item name="is_shareable" xsi:type="string">Yes</item>
                        <item name="sample" xsi:type="array">
                            <item name="sample_type_url" xsi:type="string">Yes</item>
                            <item name="sample_url" xsi:type="string">http://example.net/sample2</item>
                        </item>
                        <item name="file_type_url" xsi:type="string">Yes</item>
                        <item name="file_link_url" xsi:type="string">http://example.net/</item>
                        <item name="sort_order" xsi:type="string">1</item>
                    </item>
                </item>
            </field>
        </dataset>

        <dataset name="with_three_links">
            <field name="title" xsi:type="string">Links%isolation%</field>
            <field name="links_purchased_separately" xsi:type="string">Yes</field>
            <field name="downloadable" xsi:type="array">
                <item name="link" xsi:type="array">
                    <item name="0" xsi:type="array">
                        <item name="title" xsi:type="string">link1%isolation%</item>
                        <item name="price" xsi:type="string">2.43</item>
                        <item name="number_of_downloads" xsi:type="string">2</item>
                        <item name="sample" xsi:type="array">
                            <item name="sample_type_url" xsi:type="string">Yes</item>
                            <item name="sample_url" xsi:type="string">http://example.com</item>
                        </item>
                        <item name="file_type_url" xsi:type="string">Yes</item>
                        <item name="file_link_url" xsi:type="string">http://example.com</item>
                        <item name="is_shareable" xsi:type="string">No</item>
                        <item name="sort_order" xsi:type="string">0</item>
                    </item>
                    <item name="1" xsi:type="array">
                        <item name="title" xsi:type="string">link2%isolation%</item>
                        <item name="price" xsi:type="string">3</item>
                        <item name="number_of_downloads" xsi:type="string">3</item>
                        <item name="sample" xsi:type="array">
                            <item name="sample_type_url" xsi:type="string">Yes</item>
                            <item name="sample_url" xsi:type="string">http://example.net</item>
                        </item>
                        <item name="file_type_url" xsi:type="string">Yes</item>
                        <item name="file_link_url" xsi:type="string">http://example.net</item>
                        <item name="is_shareable" xsi:type="string">Yes</item>
                        <item name="sort_order" xsi:type="string">1</item>
                    </item>
                    <item name="2" xsi:type="array">
                        <item name="title" xsi:type="string">link3%isolation%</item>
                        <item name="price" xsi:type="string">5.43</item>
                        <item name="number_of_downloads" xsi:type="string">5</item>
                        <item name="sample" xsi:type="array">
                            <item name="sample_type_url" xsi:type="string">Yes</item>
                            <item name="sample_url" xsi:type="string">http://example.net</item>
                        </item>
                        <item name="file_type_url" xsi:type="string">Yes</item>
                        <item name="file_link_url" xsi:type="string">http://example.net</item>
                        <item name="is_shareable" xsi:type="string">Yes</item>
                        <item name="sort_order" xsi:type="string">2</item>
                    </item>
                </item>
            </field>
        </dataset>
    </repository>
</config>
