<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/fixture.xsd">
    <fixture name="widget" module="Mage_Widget" type="flat" entity_type="widget_instance"
             collection="Mage\Widget\Model\Resource\Widget\Instance\Collection" identifier="parameters"
             repository_class="Mage\Widget\Test\Repository\Widget"
             handler_interface="Mage\Widget\Test\Handler\Widget\WidgetInterface"
             class="Mage\Widget\Test\Fixture\Widget">
        <field name="instance_id" is_required="1"/>
        <field name="instance_type" is_required="0"/>
        <field name="package_theme" is_required="0" group="settings"/>
        <field name="title" is_required="0" group="frontend_properties"/>
        <field name="store_ids" source="Mage\Widget\Test\Fixture\Widget\StoreIds"/>
        <field name="parameters" is_required="0"/>
        <field name="sort_order" is_required="0"/>
        <field name="type" group="settings"/>
        <field name="widget_instance"/>
        <field name="id"/>
        <field name="page_id"/>
        <field name="layout" group="layout_updates" source="Mage\Widget\Test\Fixture\Widget\LayoutUpdates" repository="Mage\Widget\Test\Repository\Widget\LayoutUpdates"/>
        <field name="widgetOptions" group="widget_options" source="Mage\Widget\Test\Fixture\Widget\WidgetOptions" repository="Mage\Widget\Test\Repository\Widget"/>
    </fixture>
</config>
