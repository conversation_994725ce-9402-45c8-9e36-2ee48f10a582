<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<mapping strict="0">
    <fields>
        <dropdown>
            <selector>.//select</selector>
            <input>select</input>
            <strategy>xpath</strategy>
        </dropdown>
        <multipleselect>
            <selector>.//select</selector>
            <input>multiselect</input>
            <strategy>xpath</strategy>
        </multipleselect>
        <checkbox>
            <selector>.//div[label[span[contains(text(), "%option_name%")]]]/input</selector>
            <input>checkbox</input>
            <strategy>xpath</strategy>
        </checkbox>
        <radiobuttons>
            <selector>.//div[label[span[contains(text(), "%option_name%")]]]/input</selector>
            <input>checkbox</input>
            <strategy>xpath</strategy>
        </radiobuttons>
        <date composite="1">
            <month>
                <selector>[name$="[month]"]</selector>
                <input>select</input>
            </month>
            <day>
                <selector>[name$="[day]"]</selector>
                <input>select</input>
            </day>
            <year>
                <selector>[name$="[year]"]</selector>
                <input>select</input>
            </year>
        </date>
        <datetime composite="1">
            <month>
                <selector>[name$="[month]"]</selector>
                <input>select</input>
            </month>
            <day>
                <selector>[name$="[day]"]</selector>
                <input>select</input>
            </day>
            <year>
                <selector>[name$="[year]"]</selector>
                <input>select</input>
            </year>
            <hour>
                <selector>[name$="[hour]"]</selector>
                <input>select</input>
            </hour>
            <minute>
                <selector>[name$="[minute]"]</selector>
                <input>select</input>
            </minute>
            <day_part>
                <selector>[name$="[day_part]"]</selector>
                <input>select</input>
            </day_part>
        </datetime>
        <time composite="1">
            <hour>
                <selector>[name$="[hour]"]</selector>
                <input>select</input>
            </hour>
            <minute>
                <selector>[name$="[minute]"]</selector>
                <input>select</input>
            </minute>
            <day_part>
                <selector>[name$="[day_part]"]</selector>
                <input>select</input>
            </day_part>
        </time>
        <area>
            <selector>.//textarea</selector>
            <strategy>xpath</strategy>
        </area>
        <file>
            <selector>.//input</selector>
            <strategy>xpath</strategy>
        </file>
        <field>
            <selector>.//input</selector>
            <strategy>xpath</strategy>
        </field>
    </fields>
</mapping>
