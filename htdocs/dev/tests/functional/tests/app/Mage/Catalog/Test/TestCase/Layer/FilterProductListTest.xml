<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Mage\Catalog\Test\TestCase\Layer\FilterProductListTest" summary="FilterProductListTest">
        <variation name="FilterProductListTestVariation1" method="test">
            <data name="description" xsi:type="string">Filter by price</data>
            <data name="products" xsi:type="string">downloadableProduct::with_two_separately_links,catalogProductSimple::50_dollar_product,catalogProductVirtual::order_default</data>
            <data name="filterLink" xsi:type="string">$20.00</data>
            <data name="searchProductsIndexes" xsi:type="string">0</data>
            <data name="tag" xsi:type="string">main:ce</data>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductsVisibleOnCategoryPageShopByPrice" />
        </variation>
        <variation name="FilterProductListTestVariation2" method="test">
            <data name="description" xsi:type="string">Filter by price</data>
            <data name="products" xsi:type="string">catalogProductSimple::order_default,catalogProductSimple::50_dollar_product,catalogProductVirtual::order_default</data>
            <data name="filterLink" xsi:type="string">$50.00</data>
            <data name="searchProductsIndexes" xsi:type="string">1</data>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductsVisibleOnCategoryPageShopByPrice" />
        </variation>
        <variation name="FilterProductListTestVariation3" method="test">
            <data name="description" xsi:type="string">Filter by price</data>
            <data name="products" xsi:type="string">groupedProduct::three_simple_products_without_category,catalogProductSimple::50_dollar_product,catalogProductSimple::50_dollar_product</data>
            <data name="filterLink" xsi:type="string">$100.00</data>
            <data name="searchProductsIndexes" xsi:type="string">0</data>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductsVisibleOnCategoryPageShopByPrice" />
        </variation>
        <variation name="FilterProductListTestVariation4" method="test">
            <data name="description" xsi:type="string">Filter by price</data>
            <data name="products" xsi:type="string">bundleProduct::bundle_fixed_product,catalogProductSimple::50_dollar_product,catalogProductVirtual::order_default</data>
            <data name="filterLink" xsi:type="string">$760.00</data>
            <data name="searchProductsIndexes" xsi:type="string">0</data>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductsVisibleOnCategoryPageShopByPrice" />
        </variation>
        <variation name="FilterProductListTestVariation5" method="test">
            <data name="description" xsi:type="string">Filter by price</data>
            <data name="products" xsi:type="string">catalogProductSimple::order_default,catalogProductVirtual::order_default_expensive,catalogProductVirtual::order_default</data>
            <data name="filterLink" xsi:type="string">$1,000.00</data>
            <data name="searchProductsIndexes" xsi:type="string">1</data>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductsVisibleOnCategoryPageShopByPrice" />
        </variation>
        <variation name="FilterProductListTestVariation6" method="test">
            <data name="description" xsi:type="string">Filter by configurable attribute</data>
            <data name="products" xsi:type="string">catalogProductSimple::order_default,catalogProductVirtual::order_default,configurableProduct::with_filterable_options</data>
            <data name="filterLink" xsi:type="string">attribute_key_0::option_key_0</data>
            <data name="searchProductsIndexes" xsi:type="string">2</data>
            <constraint name="Mage\Catalog\Test\Constraint\AssertProductsVisibleOnCategoryPageShopByAttribute" />
        </variation>
    </testCase>
</config>
