<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/Magento/Mtf/Repository/etc/repository.xsd">
    <repository class="Mage\Core\Test\Repository\ConfigData">
        <dataset name="default_tax_configuration">
            <field name="tax/weee/enable" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/weee/display_list" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including FPT and FPT description</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/display" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including FPT and FPT description</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/display_sales" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including FPT and FPT description</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/apply_vat" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/weee/include_in_subtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
        </dataset>
        <dataset name="tax_with_fpt_taxed_cat_excl_disc_on_excl">
            <field name="tax/calculation/algorithm" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Total</item>
                <item name="value" xsi:type="string">TOTAL_BASE_CALCULATION</item>
            </field>
            <field name="tax/calculation/price_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/shipping_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/apply_after_discount" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">After Discount</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/discount_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/cross_border_trade_enabled" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/weee/enable" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/display_list" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding FPT, FPT description, final price</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/weee/display" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding FPT, FPT description, final price</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/weee/display_sales" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding FPT, FPT description, final price</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/weee/apply_vat" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/include_in_subtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
        </dataset>
        <dataset name="tax_with_fpt_taxed_cat_excl_disc_on_incl">
            <field name="tax/calculation/algorithm" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Total</item>
                <item name="value" xsi:type="string">TOTAL_BASE_CALCULATION</item>
            </field>
            <field name="tax/calculation/price_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/shipping_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/apply_after_discount" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string" />
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/discount_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/cross_border_trade_enabled" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/weee/enable" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/display_list" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including FPT and FPT description</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/display" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including FPT and FPT description</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/display_sales" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including FPT and FPT description</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/apply_vat" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/include_in_subtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
        </dataset>
        <dataset name="tax_with_fpt_taxed_cat_incl_disc_on_excl">
            <field name="tax/calculation/algorithm" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Total</item>
                <item name="value" xsi:type="string">TOTAL_BASE_CALCULATION</item>
            </field>
            <field name="tax/calculation/price_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/shipping_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/apply_after_discount" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">After Discount</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/discount_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/cross_border_trade_enabled" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/weee/enable" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/display_list" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding FPT, FPT description, final price</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/weee/display" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding FPT, FPT description, final price</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/weee/display_sales" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding FPT, FPT description, final price</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/weee/apply_vat" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/include_in_subtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
        </dataset>
        <dataset name="tax_with_fpt_taxed_cat_incl_disc_on_incl">
            <field name="tax/calculation/algorithm" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Total</item>
                <item name="value" xsi:type="string">TOTAL_BASE_CALCULATION</item>
            </field>
            <field name="tax/calculation/price_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/shipping_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/apply_after_discount" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string" />
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/discount_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/cross_border_trade_enabled" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/weee/enable" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/display_list" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including FPT and FPT description</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/display" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including FPT and FPT description</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/display_sales" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including FPT and FPT description</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/apply_vat" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/include_in_subtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
        </dataset>
        <dataset name="tax_with_fpt_cat_excl_disc_on_excl">
            <field name="tax/calculation/algorithm" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Total</item>
                <item name="value" xsi:type="string">TOTAL_BASE_CALCULATION</item>
            </field>
            <field name="tax/calculation/price_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/shipping_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/apply_after_discount" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">After Discount</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/discount_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/cross_border_trade_enabled" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/weee/enable" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/display_list" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding FPT, FPT description, final price</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/weee/display" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding FPT, FPT description, final price</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/weee/display_sales" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding FPT, FPT description, final price</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/weee/apply_vat" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/weee/include_in_subtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
        </dataset>
        <dataset name="tax_with_fpt_cat_excl_disc_on_incl">
            <field name="tax/calculation/algorithm" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Total</item>
                <item name="value" xsi:type="string">TOTAL_BASE_CALCULATION</item>
            </field>
            <field name="tax/calculation/price_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/shipping_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/apply_after_discount" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string" />
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/discount_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/cross_border_trade_enabled" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/weee/enable" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/display_list" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding FPT, FPT description, final price</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/weee/display" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding FPT, FPT description, final price</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/weee/display_sales" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding FPT, FPT description, final price</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/weee/apply_vat" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/weee/include_in_subtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
        </dataset>
        <dataset name="tax_with_fpt_cat_incl_disc_on_excl">
            <field name="tax/calculation/algorithm" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Total</item>
                <item name="value" xsi:type="string">TOTAL_BASE_CALCULATION</item>
            </field>
            <field name="tax/calculation/price_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/shipping_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/apply_after_discount" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">After Discount</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/discount_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding Tax</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/cross_border_trade_enabled" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/weee/enable" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/display_list" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding FPT, FPT description, final price</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/weee/display" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding FPT, FPT description, final price</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/weee/display_sales" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Excluding FPT, FPT description, final price</item>
                <item name="value" xsi:type="string">2</item>
            </field>
            <field name="tax/weee/apply_vat" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/weee/include_in_subtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
        </dataset>
        <dataset name="tax_with_fpt_cat_incl_disc_on_incl">
            <field name="tax/calculation/algorithm" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Total</item>
                <item name="value" xsi:type="string">TOTAL_BASE_CALCULATION</item>
            </field>
            <field name="tax/calculation/price_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/shipping_includes_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/apply_after_discount" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string" />
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/calculation/discount_tax" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including Tax</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/calculation/cross_border_trade_enabled" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/weee/enable" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/display_list" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including FPT and FPT description</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/display" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including FPT and FPT description</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/display_sales" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Including FPT and FPT description</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="tax/weee/apply_vat" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="tax/weee/include_in_subtotal" xsi:type="array">
                <item name="scope" xsi:type="string">tax</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
        </dataset>
    </repository>
</config>
