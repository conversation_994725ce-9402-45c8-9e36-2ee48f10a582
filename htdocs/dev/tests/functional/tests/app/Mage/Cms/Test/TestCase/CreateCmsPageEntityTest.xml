<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Mage\Cms\Test\TestCase\CreateCmsPageEntityTest" summary="CreateCmsPageEntityTest">
        <variation name="CreateCmsPageEntityTestVariation1" method="test">
            <data name="cms/data/title" xsi:type="string">NewCmsPage%isolation%</data>
            <data name="cms/data/identifier" xsi:type="string">new_cms_page%isolation%</data>
            <data name="cms/data/content_heading" xsi:type="string">cms_page_text_content_heading%isolation%</data>
            <data name="cms/data/content/content" xsi:type="string">cms_page_text_content%isolation%</data>
            <constraint name="Mage\Cms\Test\Constraint\AssertCmsPageSuccessSaveMessage" />
            <constraint name="Mage\Cms\Test\Constraint\AssertCmsPageForm" />
            <constraint name="Mage\Cms\Test\Constraint\AssertCmsPageInGrid" />
            <constraint name="Mage\Cms\Test\Constraint\AssertCmsPagePreview" />
        </variation>
        <variation name="CreateCmsPageEntityTestVariation2" method="test">
            <data name="cms/data/title" xsi:type="string">NewCmsPage%isolation%</data>
            <data name="cms/data/identifier" xsi:type="string">new_cms_page%isolation%</data>
            <data name="cms/data/is_active" xsi:type="string">Disabled</data>
            <data name="cms/data/content_heading" xsi:type="string">cms_page_text_content_heading%isolation%</data>
            <data name="cms/data/content/content" xsi:type="string">cms_page_text_content%isolation%</data>
            <constraint name="Mage\Cms\Test\Constraint\AssertCmsPageSuccessSaveMessage" />
            <constraint name="Mage\Cms\Test\Constraint\AssertCmsPageForm" />
            <constraint name="Mage\Cms\Test\Constraint\AssertCmsPageInGrid" />
            <constraint name="Mage\Cms\Test\Constraint\AssertCmsPageDisabledOnFrontend" />
        </variation>
        <variation name="CreateCmsPageEntityTestVariation3" method="test">
            <data name="cms/data/title" xsi:type="string">NewCmsPage%isolation%</data>
            <data name="cms/data/identifier" xsi:type="string">new_cms_page%isolation%</data>
            <data name="cms/data/store_id/dataset/0" xsi:type="string">custom</data>
            <data name="cms/data/content_heading" xsi:type="string">cms_page_text_content_heading%isolation%</data>
            <data name="cms/data/content/content" xsi:type="string">cms_page_text_content%isolation%</data>
            <constraint name="Mage\Cms\Test\Constraint\AssertCmsPageSuccessSaveMessage" />
            <constraint name="Mage\Cms\Test\Constraint\AssertCmsPageForm" />
            <constraint name="Mage\Cms\Test\Constraint\AssertCmsPageInGrid" />
            <constraint name="Mage\Cms\Test\Constraint\AssertCmsPagePreview" />
            <constraint name="Mage\Cms\Test\Constraint\AssertCmsPageDisabledOnUnassignedStoreView" />
        </variation>
        <variation name="CreateCmsPageEntityTestVariation4">
            <data name="cms/data/title" xsi:type="string">NewCmsPage%isolation%</data>
            <data name="cms/data/identifier" xsi:type="string">new_cms_page%isolation%</data>
            <data name="cms/data/content_heading" xsi:type="string">cms_page_text_content_heading%isolation%</data>
            <data name="cms/data/content/content" xsi:type="string">cms_page_text_content%isolation%</data>
            <data name="cms/data/content/widget/preset" xsi:type="string">default</data>
            <data name="cms/data/content/variable" xsi:type="string">General Contact Name</data>
            <constraint name="Mage\Cms\Test\Constraint\AssertCmsPageSuccessSaveMessage" />
            <constraint name="Mage\Cms\Test\Constraint\AssertCmsPageForm" />
            <constraint name="Mage\Cms\Test\Constraint\AssertCmsPageInGrid" />
            <constraint name="Mage\Cms\Test\Constraint\AssertCmsPagePreview" />
        </variation>
    </testCase>
</config>
