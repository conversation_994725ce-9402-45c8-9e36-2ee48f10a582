<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/fixture.xsd">
    <fixture name="cmsPage" module="Mage_Cms" type="flat" entity_type="cms_page"
             repository_class="Mage\Cms\Test\Repository\CmsPage"
             handler_interface="Mage\Cms\Test\Handler\CmsPage\CmsPageInterface"
             class="Mage\Cms\Test\Fixture\CmsPage">
        <field name="page_id" is_required="1"/>
        <field name="title" is_required="0" group="page_information"/>
        <field name="root_template" is_required="0"/>
        <field name="meta_keywords" is_required="0"/>
        <field name="meta_description" is_required="0"/>
        <field name="identifier" is_required="0" group="page_information"/>
        <field name="content_heading" is_required="0" group="content"/>
        <field name="content" is_required="0" group="content" source="Mage\Cms\Test\Fixture\CmsPage\Content"/>
        <field name="creation_time" is_required="0"/>
        <field name="update_time" is_required="0"/>
        <field name="is_active" is_required="0" group="page_information"/>
        <field name="sort_order" is_required="0"/>
        <field name="layout_update_xml" is_required="0"/>
        <field name="custom_theme" is_required="0"/>
        <field name="custom_root_template" is_required="0"/>
        <field name="custom_layout_update_xml" is_required="0"/>
        <field name="custom_theme_from" is_required="0"/>
        <field name="custom_theme_to" is_required="0"/>
        <field name="published_revision_id" is_required="0"/>
        <field name="website_root" is_required="0"/>
        <field name="under_version_control" is_required="0" group="page_information"/>
        <field name="store_id" is_required="1" group="page_information" source="Mage\Cms\Test\Fixture\CmsPage\StoreId"/>
    </fixture>
</config>
