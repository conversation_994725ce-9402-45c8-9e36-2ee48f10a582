<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/Magento/Mtf/Repository/etc/repository.xsd">
    <repository class="Mage\Cms\Test\Repository\CmsPage">
        <dataset name="default">
            <field name="title" xsi:type="string">test-%isolation%</field>
            <field name="identifier" xsi:type="string">test-%isolation%</field>
            <field name="store_id" xsi:type="array">
                <item name="dataset" xsi:type="array">
                    <item name="0" xsi:type="string">default</item>
                </item>
            </field>
            <field name="content" xsi:type="array">
                <item name="content" xsi:type="string">text content</item>
            </field>
            <field name="content_heading" xsi:type="string">Test-%isolation%</field>
            <field name="page_layout" xsi:type="string">1 column</field>
        </dataset>

        <dataset name="3_column_template">
            <field name="title" xsi:type="string">page-compare-%isolation%</field>
            <field name="identifier" xsi:type="string">page-compare-%isolation%</field>
            <field name="store_id" xsi:type="array">
                <item name="dataset" xsi:type="array">
                    <item name="0" xsi:type="string">default</item>
                </item>
            </field>
            <field name="content" xsi:type="array">
                <item name="content" xsi:type="string">Test Content</item>
            </field>
            <field name="page_layout" xsi:type="string">3 columns</field>
        </dataset>
    </repository>
</config>
