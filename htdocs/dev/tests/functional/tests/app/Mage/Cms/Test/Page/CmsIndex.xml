<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/pages.xsd">
    <page name="CmsIndex" mca="cms/index" module="Mage_Cms">
        <block name="topmenu" class="Mage\Page\Test\Block\Html\Topmenu" locator="#nav" strategy="css selector"/>
        <block name="searchBlock" class="Mage\Catalog\Test\Block\Search" locator="#search_mini_form" strategy="css selector"/>
        <block name="linksBlock" class="Mage\Theme\Test\Block\Links" locator="#header-account .links" strategy="css selector"/>
        <block name="topLinksBlock" class="Mage\Theme\Test\Block\TopLinks" locator=".account-cart-wrapper" strategy="css selector"/>
        <block name="footerBlock" class="Mage\Page\Test\Block\Html\Footer" locator=".footer" strategy="css selector"/>
        <block name="headerBlock" class="Mage\Page\Test\Block\Html\Header" locator=".header-language-background" strategy="css selector"/>
        <block name="currencyBlock" class="Mage\Directory\Test\Block\Currency\Switcher" locator=".currency-switcher" strategy="css selector"/>
        <block name="cmsPageBlock" class="Mage\Cms\Test\Block\Page" locator=".main-container" strategy="css selector"/>
        <block name="compareBlock" class="Mage\Catalog\Test\Block\Product\ProductList\Compare" locator=".block-compare" strategy="css selector"/>
    </page>
</config>
