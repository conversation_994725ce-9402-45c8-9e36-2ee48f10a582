<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="../../../../../../../vendor/magento/mtf/Magento/Mtf/Repository/etc/repository.xsd">
    <repository class="Mage\Bundle\Test\Repository\BundleProduct\CheckoutData">
        <dataset name="default_dynamic">
            <field name="options" xsi:type="array">
                <item name="bundle_options" xsi:type="array">
                    <item name="option_key_0" xsi:type="array">
                        <item name="title" xsi:type="string">Drop-down Option</item>
                        <item name="type" xsi:type="string">Drop-down</item>
                    <item name="value" xsi:type="array">
                        <item name="name" xsi:type="string">product_key_1</item>
                        <item name="qty" xsi:type="string">2</item>
                     </item>
                    </item>
                </item>
            </field>
            <field name="cartItem" xsi:type="array">
                <item name="options" xsi:type="array">
                    <item name="bundle_options" xsi:type="array">
                        <item name="option_key_0" xsi:type="array">
                            <item name="price" xsi:type="string">50</item>
                        </item>
                    </item>
                </item>
                <item name="price" xsi:type="string">100</item>
                <item name="subtotal" xsi:type="string">100</item>
            </field>
        </dataset>

        <dataset name="default_fixed">
            <field name="options" xsi:type="array">
                <item name="bundle_options" xsi:type="array">
                    <item name="option_key_0" xsi:type="array">
                        <item name="title" xsi:type="string">Drop-down Option</item>
                        <item name="type" xsi:type="string">Drop-down</item>
                    <item name="value" xsi:type="array">
                        <item name="name" xsi:type="string">product_key_1</item>
                        <item name="qty" xsi:type="string">3</item>
                    </item>
                    </item>
                </item>
            </field>
            <field name="qty" xsi:type="string">1</field>
            <field name="cartItem" xsi:type="array">
                <item name="options" xsi:type="array">
                    <item name="bundle_options" xsi:type="array">
                        <item name="option_key_0" xsi:type="array">
                            <item name="price" xsi:type="string">6</item>
                        </item>
                    </item>
                </item>
                <item name="price" xsi:type="string">768</item>
                <item name="subtotal" xsi:type="string">768</item>
            </field>
        </dataset>

        <dataset name="dynamic_with_tier_price">
            <field name="options" xsi:type="array">
                <item name="bundle_options" xsi:type="array">
                    <item name="option_key_0" xsi:type="array">
                        <item name="value" xsi:type="array">
                            <item name="name" xsi:type="string">product_key_0</item>
                            <item name="qty" xsi:type="string">2</item>
                        </item>
                    </item>
                </item>
            </field>
            <field name="qty" xsi:type="string">15</field>
            <field name="cartItem" xsi:type="array">
                <item name="options" xsi:type="array">
                    <item name="bundle_options" xsi:type="array">
                        <item name="option_key_0" xsi:type="array">
                            <item name="price" xsi:type="string">76</item>
                        </item>
                    </item>
                </item>
                <item name="price" xsi:type="string">152</item>
                <item name="subtotal" xsi:type="string">2,280.00</item>
            </field>
        </dataset>

        <dataset name="dynamic_with_group_price">
            <field name="options" xsi:type="array">
                <item name="bundle_options" xsi:type="array">
                    <item name="option_key_0" xsi:type="array">
                        <item name="value" xsi:type="array">
                            <item name="name" xsi:type="string">product_key_0</item>
                            <item name="qty" xsi:type="string">4</item>
                        </item>
                    </item>
                </item>
            </field>
            <field name="qty" xsi:type="string">7</field>
            <field name="cartItem" xsi:type="array">
                <item name="options" xsi:type="array">
                    <item name="bundle_options" xsi:type="array">
                        <item name="option_key_0" xsi:type="array">
                            <item name="price" xsi:type="string">10</item>
                        </item>
                    </item>
                </item>
                <item name="price" xsi:type="string">40</item>
                <item name="subtotal" xsi:type="string">40</item>
            </field>
        </dataset>

        <dataset name="fixed_with_special_price">
            <field name="options" xsi:type="array">
                <item name="bundle_options" xsi:type="array">
                    <item name="option_key_0" xsi:type="array">
                        <item name="value" xsi:type="array">
                            <item name="name" xsi:type="string">product_key_1</item>
                            <item name="qty" xsi:type="string">3</item>
                        </item>
                    </item>
                </item>
            </field>
            <field name="qty" xsi:type="string">2</field>
            <field name="cartItem" xsi:type="array">
                <item name="options" xsi:type="array">
                    <item name="bundle_options" xsi:type="array">
                        <item name="option_key_0" xsi:type="array">
                            <item name="price" xsi:type="string">0.6</item>
                        </item>
                    </item>
                </item>
                <item name="price" xsi:type="string">11.8</item>
                <item name="subtotal" xsi:type="string">23.6</item>
            </field>
        </dataset>

        <dataset name="dynamic_as_low_as_price">
            <field name="options" xsi:type="array">
                <item name="bundle_options" xsi:type="array">
                    <item name="option_key_0" xsi:type="array">
                        <item name="value" xsi:type="array">
                            <item name="name" xsi:type="string">product_key_1</item>
                            <item name="qty" xsi:type="string">1</item>
                        </item>
                    </item>
                </item>
            </field>
            <field name="qty" xsi:type="string">3</field>
            <field name="cartItem" xsi:type="array">
                <item name="options" xsi:type="array">
                    <item name="bundle_options" xsi:type="array">
                        <item name="option_key_0" xsi:type="array">
                            <item name="price" xsi:type="string">50</item>
                        </item>
                    </item>
                </item>
                <item name="price" xsi:type="string">50</item>
                <item name="subtotal" xsi:type="string">150</item>
            </field>
        </dataset>

        <dataset name="fixed_as_low_as_price">
            <field name="options" xsi:type="array">
                <item name="bundle_options" xsi:type="array">
                    <item name="option_key_0" xsi:type="array">
                        <item name="value" xsi:type="array">
                            <item name="name" xsi:type="string">product_key_1</item>
                            <item name="qty" xsi:type="string">2</item>
                        </item>
                    </item>
                </item>
            </field>
            <field name="qty" xsi:type="string">4</field>
            <field name="cartItem" xsi:type="array">
                <item name="options" xsi:type="array">
                    <item name="bundle_options" xsi:type="array">
                        <item name="option_key_0" xsi:type="array">
                            <item name="price" xsi:type="string">6</item>
                        </item>
                    </item>
                </item>
                <item name="price" xsi:type="string">112</item>
                <item name="subtotal" xsi:type="string">448</item>
            </field>
        </dataset>

        <dataset name="all_types_bundle_fixed_and_custom_options">
            <field name="options" xsi:type="array">
                <item name="bundle_options" xsi:type="array">
                    <item name="option_key_0" xsi:type="array">
                        <item name="value" xsi:type="array">
                            <item name="name" xsi:type="string">product_key_0</item>
                            <item name="qty" xsi:type="string">2</item>
                        </item>
                    </item>
                    <item name="option_key_1" xsi:type="array">
                        <item name="value" xsi:type="array">
                            <item name="name" xsi:type="string">product_key_0</item>
                            <item name="qty" xsi:type="string">2</item>
                        </item>
                    </item>
                    <item name="option_key_2" xsi:type="array">
                        <item name="value" xsi:type="array">
                            <item name="name" xsi:type="string">product_key_0</item>
                        </item>
                    </item>
                    <item name="option_key_3" xsi:type="array">
                        <item name="value" xsi:type="array">
                            <item name="name" xsi:type="string">product_key_0</item>
                        </item>
                    </item>
                </item>
                <item name="custom_options" xsi:type="array">
                    <item name="0" xsi:type="array">
                        <item name="title" xsi:type="string">attribute_key_0</item>
                        <item name="value" xsi:type="string">Field</item>
                    </item>
                    <item name="1" xsi:type="array">
                        <item name="title" xsi:type="string">attribute_key_1</item>
                        <item name="value" xsi:type="string">Area</item>
                    </item>
                    <item name="2" xsi:type="array">
                        <item name="title" xsi:type="string">attribute_key_3</item>
                        <item name="value" xsi:type="string">option_key_0</item>
                    </item>
                    <item name="3" xsi:type="array">
                        <item name="title" xsi:type="string">attribute_key_4</item>
                        <item name="value" xsi:type="string">option_key_0</item>
                    </item>
                    <item name="4" xsi:type="array">
                        <item name="title" xsi:type="string">attribute_key_5</item>
                        <item name="value" xsi:type="string">option_key_0</item>
                    </item>
                    <item name="5" xsi:type="array">
                        <item name="title" xsi:type="string">attribute_key_6</item>
                        <item name="value" xsi:type="string">option_key_0</item>
                    </item>
                    <item name="6" xsi:type="array">
                        <item name="title" xsi:type="string">attribute_key_7</item>
                        <item name="value" xsi:type="string">12/12/2014</item>
                    </item>
                    <item name="7" xsi:type="array">
                        <item name="title" xsi:type="string">attribute_key_8</item>
                        <item name="value" xsi:type="string">12/12/2014/12/30/AM</item>
                    </item>
                    <item name="8" xsi:type="array">
                        <item name="title" xsi:type="string">attribute_key_9</item>
                        <item name="value" xsi:type="string">12/12/AM</item>
                    </item>
                </item>
            </field>
            <field name="qty" xsi:type="string">4</field>
            <field name="cartItem" xsi:type="array">
                <item name="options" xsi:type="array">
                    <item name="bundle_options" xsi:type="array">
                        <item name="option_key_0" xsi:type="array">
                            <item name="price" xsi:type="string">5</item>
                        </item>
                        <item name="option_key_1" xsi:type="array">
                            <item name="price" xsi:type="string">5</item>
                        </item>
                        <item name="option_key_2" xsi:type="array">
                            <item name="price" xsi:type="string">5</item>
                        </item>
                        <item name="option_key_3" xsi:type="array">
                            <item name="price" xsi:type="string">5</item>
                        </item>
                    </item>
                </item>
                <item name="price" xsi:type="string">290</item>
                <item name="subtotal" xsi:type="string">1,160.00</item>
            </field>
        </dataset>
    </repository>
</config>