<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<tabs>
    <general>
        <class>\Mage\Adminhtml\Test\Block\Widget\Tab</class>
        <selector>//ul[@id="product_info_tabs"]/li[1]/a</selector>
        <strategy>xpath</strategy>
        <wrapper>product</wrapper>
        <fields>
            <sku_type>
                <input>select</input>
            </sku_type>
            <weight_type>
                <input>select</input>
            </weight_type>
        </fields>
    </general>
    <bundle-items>
        <class>\Mage\Bundle\Test\Block\Adminhtml\Catalog\Product\Edit\Tab\Bundle</class>
        <selector>#product_info_tabs_bundle_items</selector>
        <strategy>css selector</strategy>
        <wrapper>product</wrapper>
        <fields>
            <shipment_type>
                <input>select</input>
            </shipment_type>
        </fields>
    </bundle-items>
    <prices>
        <class>\Mage\Adminhtml\Test\Block\Catalog\Product\Edit\Tab\Prices</class>
        <selector>//ul[@id="product_info_tabs"]/li[2]/a</selector>
        <strategy>xpath</strategy>
        <wrapper>product</wrapper>
        <fields>
            <price_type>
                <input>select</input>
            </price_type>
            <price_view>
                <input>select</input>
            </price_view>
        </fields>
    </prices>
</tabs>
