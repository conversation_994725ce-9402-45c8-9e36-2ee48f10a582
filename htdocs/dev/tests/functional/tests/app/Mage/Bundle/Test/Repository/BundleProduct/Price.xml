<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../../vendor/magento/mtf/Magento/Mtf/Repository/etc/repository.xsd">
    <repository class="Mage\Bundle\Test\Repository\BundleProduct\Price">
        <dataset name="default_dynamic">
            <field name="price_from" xsi:type="string">150.00</field>
            <field name="price_to" xsi:type="string">200.00</field>
        </dataset>

        <dataset name="default_fixed">
            <field name="price_from" xsi:type="string">760.00</field>
            <field name="price_to" xsi:type="string">768.00</field>
        </dataset>

        <dataset name="default_bundle_fixed">
            <field name="price_from" xsi:type="string">760.00</field>
            <field name="price_to" xsi:type="string">768.00</field>
        </dataset>

        <dataset name="dynamic_with_tier_price">
            <field name="price_from" xsi:type="string">150.00</field>
            <field name="price_to" xsi:type="string">200.00</field>
        </dataset>

        <dataset name="dynamic_with_group_price">
            <field name="price_from" xsi:type="string">15.00</field>
            <field name="price_to" xsi:type="string">20.00</field>
        </dataset>

        <dataset name="fixed_with_special_price">
            <field name="price_from" xsi:type="string">11.00</field>
            <field name="price_to" xsi:type="string">11.80</field>
        </dataset>

        <dataset name="dynamic_as_low_as_price">
            <field name="price_from" xsi:type="string">150.00</field>
        </dataset>

        <dataset name="fixed_as_low_as_price">
            <field name="price_from" xsi:type="string">110.00</field>
        </dataset>

        <dataset name="all_types_bundle_fixed_and_custom_options">
            <field name="price_from" xsi:type="string">290.00</field>
            <field name="price_to" xsi:type="string">372.00</field>
        </dataset>
    </repository>
</config>