<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="../../../../../../../vendor/magento/mtf/Magento/Mtf/Repository/etc/repository.xsd">
    <repository class="Mage\Bundle\Test\Repository\BundleProduct\BundleSelections">
        <dataset name="default_dynamic">
            <field name="bundle_options" xsi:type="array">
                <item name="0" xsi:type="array">
                    <item name="title" xsi:type="string">Drop-down Option</item>
                    <item name="type" xsi:type="string">Drop-down</item>
                    <item name="required" xsi:type="string">Yes</item>
                    <item name="position" xsi:type="string">1</item>
                    <item name="assigned_products" xsi:type="array">
                        <item name="0" xsi:type="array">
                            <item name="sku" xsi:type="string">%product_sku%</item>
                            <item name="selection_qty" xsi:type="string">2</item>
                            <item name="selection_can_change_qty" xsi:type="string">Yes</item>
                        </item>
                        <item name="1" xsi:type="array">
                            <item name="sku" xsi:type="string">%product_sku%</item>
                            <item name="selection_qty" xsi:type="string">3</item>
                            <item name="selection_can_change_qty" xsi:type="string">Yes</item>
                        </item>
                    </item>
                </item>
            </field>
        <field name="products" xsi:type="array">
            <item name="0" xsi:type="array">
                <item name="0" xsi:type="string">catalogProductSimple::default</item>
                <item name="1" xsi:type="string">catalogProductSimple::50_dollar_product</item>
            </item>
        </field>
        </dataset>

        <dataset name="default_fixed">
            <field name="bundle_options" xsi:type="array">
                <item name="0" xsi:type="array">
                    <item name="title" xsi:type="string">Drop-down Option</item>
                    <item name="type" xsi:type="string">Drop-down</item>
                    <item name="required" xsi:type="string">Yes</item>
                    <item name="position" xsi:type="string">0</item>
                    <item name="assigned_products" xsi:type="array">
                        <item name="0" xsi:type="array">
                            <item name="sku" xsi:type="string">%product_sku%</item>
                            <item name="selection_price_value" xsi:type="string">5</item>
                            <item name="selection_price_type" xsi:type="string">Fixed</item>
                            <item name="selection_qty" xsi:type="string">2</item>
                            <item name="selection_can_change_qty" xsi:type="string">Yes</item>
                        </item>
                        <item name="1" xsi:type="array">
                            <item name="sku" xsi:type="string">%product_sku%</item>
                            <item name="selection_price_value" xsi:type="string">6</item>
                            <item name="selection_price_type" xsi:type="string">Fixed</item>
                            <item name="selection_qty" xsi:type="string">3</item>
                            <item name="selection_can_change_qty" xsi:type="string">Yes</item>
                        </item>
                    </item>
                </item>
            </field>
            <field name="products" xsi:type="array">
                <item name="0" xsi:type="array">
                    <item name="0" xsi:type="string">catalogProductSimple::default</item>
                    <item name="1" xsi:type="string">catalogProductSimple::50_dollar_product</item>
                </item>
            </field>
        </dataset>

        <dataset name="all_types_fixed">
            <field name="bundle_options" xsi:type="array">
                <item name="0" xsi:type="array">
                    <item name="title" xsi:type="string">Drop-down Option</item>
                    <item name="type" xsi:type="string">Drop-down</item>
                    <item name="required" xsi:type="string">Yes</item>
                    <item name="position" xsi:type="string">1</item>
                    <item name="assigned_products" xsi:type="array">
                        <item name="0" xsi:type="array">
                            <item name="sku" xsi:type="string">%product_sku%</item>
                            <item name="selection_price_value" xsi:type="string">5</item>
                            <item name="selection_price_type" xsi:type="string">Fixed</item>
                            <item name="selection_qty" xsi:type="string">2</item>
                            <item name="selection_can_change_qty" xsi:type="string">Yes</item>
                        </item>
                        <item name="1" xsi:type="array">
                            <item name="sku" xsi:type="string">%product_sku%</item>
                            <item name="selection_price_value" xsi:type="string">6</item>
                            <item name="selection_price_type" xsi:type="string">Fixed</item>
                            <item name="selection_qty" xsi:type="string">3</item>
                            <item name="selection_can_change_qty" xsi:type="string">Yes</item>
                        </item>
                    </item>
                </item>
                <item name="1" xsi:type="array">
                    <item name="title" xsi:type="string">Radio Button Option</item>
                    <item name="type" xsi:type="string">Radio Buttons</item>
                    <item name="required" xsi:type="string">Yes</item>
                    <item name="position" xsi:type="string">2</item>
                    <item name="assigned_products" xsi:type="array">
                        <item name="0" xsi:type="array">
                            <item name="sku" xsi:type="string">%product_sku%</item>
                            <item name="selection_price_value" xsi:type="string">5</item>
                            <item name="selection_price_type" xsi:type="string">Fixed</item>
                            <item name="selection_qty" xsi:type="string">2</item>
                            <item name="selection_can_change_qty" xsi:type="string">Yes</item>
                        </item>
                        <item name="1" xsi:type="array">
                            <item name="sku" xsi:type="string">%product_sku%</item>
                            <item name="selection_price_value" xsi:type="string">6</item>
                            <item name="selection_price_type" xsi:type="string">Fixed</item>
                            <item name="selection_qty" xsi:type="string">3</item>
                        <item name="selection_can_change_qty" xsi:type="string">Yes</item>
                        </item>
                    </item>
                </item>
                <item name="2" xsi:type="array">
                    <item name="title" xsi:type="string">Checkbox Option</item>
                    <item name="type" xsi:type="string">Checkbox</item>
                    <item name="required" xsi:type="string">Yes</item>
                    <item name="position" xsi:type="string">3</item>
                    <item name="assigned_products" xsi:type="array">
                        <item name="0" xsi:type="array">
                            <item name="sku" xsi:type="string">%product_sku%</item>
                            <item name="selection_price_value" xsi:type="string">5</item>
                            <item name="selection_price_type" xsi:type="string">Fixed</item>
                            <item name="selection_qty" xsi:type="string">2</item>
                        </item>
                        <item name="1" xsi:type="array">
                            <item name="sku" xsi:type="string">%product_sku%</item>
                            <item name="selection_price_value" xsi:type="string">6</item>
                            <item name="selection_price_type" xsi:type="string">Fixed</item>
                            <item name="selection_qty" xsi:type="string">3</item>
                        </item>
                    </item>
                </item>
                <item name="3" xsi:type="array">
                    <item name="title" xsi:type="string">Multiple Select Option</item>
                    <item name="type" xsi:type="string">Multiple Select</item>
                    <item name="required" xsi:type="string">Yes</item>
                    <item name="position" xsi:type="string">4</item>
                <item name="assigned_products" xsi:type="array">
                    <item name="0" xsi:type="array">
                        <item name="sku" xsi:type="string">%product_sku%</item>
                        <item name="selection_price_value" xsi:type="string">5</item>
                        <item name="selection_price_type" xsi:type="string">Fixed</item>
                        <item name="selection_qty" xsi:type="string">2</item>
                    </item>
                    <item name="1" xsi:type="array">
                        <item name="sku" xsi:type="string">%product_sku%</item>
                        <item name="selection_price_value" xsi:type="string">6</item>
                        <item name="selection_price_type" xsi:type="string">Fixed</item>
                        <item name="selection_qty" xsi:type="string">3</item>
                    </item>
                </item>
                </item>
            </field>
            <field name="products" xsi:type="array">
                <item name="0" xsi:type="array">
                    <item name="0" xsi:type="string">catalogProductSimple::default</item>
                    <item name="1" xsi:type="string">catalogProductSimple::50_dollar_product</item>
                </item>
                <item name="1" xsi:type="array">
                    <item name="0" xsi:type="string">catalogProductSimple::default</item>
                    <item name="1" xsi:type="string">catalogProductSimple::50_dollar_product</item>
                </item>
                <item name="2" xsi:type="array">
                    <item name="0" xsi:type="string">catalogProductSimple::default</item>
                    <item name="1" xsi:type="string">catalogProductSimple::50_dollar_product</item>
                </item>
                <item name="3" xsi:type="array">
                    <item name="0" xsi:type="string">catalogProductSimple::default</item>
                    <item name="1" xsi:type="string">catalogProductSimple::50_dollar_product</item>
                </item>
            </field>
        </dataset>

        <dataset name="all_types_dynamic">
            <field name="bundle_options" xsi:type="array">
                <item name="0" xsi:type="array">
                    <item name="title" xsi:type="string">Drop-down Option</item>
                    <item name="type" xsi:type="string">Drop-down</item>
                    <item name="required" xsi:type="string">Yes</item>
                    <item name="position" xsi:type="string">1</item>
                    <item name="assigned_products" xsi:type="array">
                        <item name="0" xsi:type="array">
                            <item name="sku" xsi:type="string">%product_sku%</item>
                            <item name="selection_qty" xsi:type="string">2</item>
                            <item name="selection_can_change_qty" xsi:type="string">Yes</item>
                        </item>
                        <item name="1" xsi:type="array">
                            <item name="sku" xsi:type="string">%product_sku%</item>
                            <item name="selection_qty" xsi:type="string">3</item>
                            <item name="selection_can_change_qty" xsi:type="string">Yes</item>
                        </item>
                    </item>
                </item>
                <item name="1" xsi:type="array">
                    <item name="title" xsi:type="string">Radio Button Option</item>
                    <item name="type" xsi:type="string">Radio Buttons</item>
                    <item name="required" xsi:type="string">Yes</item>
                    <item name="position" xsi:type="string">2</item>
                    <item name="assigned_products" xsi:type="array">
                        <item name="0" xsi:type="array">
                            <item name="sku" xsi:type="string">%product_sku%</item>
                            <item name="selection_qty" xsi:type="string">2</item>
                            <item name="selection_can_change_qty" xsi:type="string">Yes</item>
                        </item>
                        <item name="1" xsi:type="array">
                            <item name="sku" xsi:type="string">%product_sku%</item>
                            <item name="selection_qty" xsi:type="string">3</item>
                            <item name="selection_can_change_qty" xsi:type="string">Yes</item>
                        </item>
                    </item>
                </item>
                <item name="2" xsi:type="array">
                    <item name="title" xsi:type="string">Checkbox Option</item>
                    <item name="type" xsi:type="string">Checkbox</item>
                    <item name="required" xsi:type="string">Yes</item>
                    <item name="position" xsi:type="string">3</item>
                    <item name="assigned_products" xsi:type="array">
                        <item name="0" xsi:type="array">
                            <item name="sku" xsi:type="string">%product_sku%</item>
                            <item name="selection_qty" xsi:type="string">2</item>
                        </item>
                        <item name="1" xsi:type="array">
                            <item name="sku" xsi:type="string">%product_sku%</item>
                            <item name="selection_qty" xsi:type="string">3</item>
                        </item>
                    </item>
                </item>
                <item name="3" xsi:type="array">
                    <item name="title" xsi:type="string">Multiple Select Option</item>
                    <item name="type" xsi:type="string">Multiple Select</item>
                    <item name="required" xsi:type="string">Yes</item>
                    <item name="position" xsi:type="string">4</item>
                    <item name="assigned_products" xsi:type="array">
                        <item name="0" xsi:type="array">
                            <item name="sku" xsi:type="string">%product_sku%</item>
                            <item name="selection_qty" xsi:type="string">2</item>
                        </item>
                        <item name="1" xsi:type="array">
                            <item name="sku" xsi:type="string">%product_sku%</item>
                            <item name="selection_qty" xsi:type="string">3</item>
                        </item>
                    </item>
                </item>
            </field>
            <field name="products" xsi:type="array">
                <item name="0" xsi:type="array">
                    <item name="0" xsi:type="string">catalogProductSimple::default</item>
                    <item name="1" xsi:type="string">catalogProductSimple::50_dollar_product</item>
                </item>
                <item name="1" xsi:type="array">
                    <item name="0" xsi:type="string">catalogProductSimple::default</item>
                    <item name="1" xsi:type="string">catalogProductSimple::50_dollar_product</item>
                </item>
                <item name="2" xsi:type="array">
                    <item name="0" xsi:type="string">catalogProductSimple::default</item>
                    <item name="1" xsi:type="string">catalogProductSimple::50_dollar_product</item>
                </item>
                <item name="3" xsi:type="array">
                    <item name="0" xsi:type="string">catalogProductSimple::default</item>
                    <item name="1" xsi:type="string">catalogProductSimple::50_dollar_product</item>
                </item>
            </field>
        </dataset>

        <dataset name="with_not_required_options">
            <field name="bundle_options" xsi:type="array">
                <item name="0" xsi:type="array">
                    <item name="title" xsi:type="string">Drop-down Option</item>
                    <item name="type" xsi:type="string">Drop-down</item>
                    <item name="required" xsi:type="string">No</item>
                    <item name="position" xsi:type="string">0</item>
                    <item name="assigned_products" xsi:type="array">
                        <item name="0" xsi:type="array">
                            <item name="sku" xsi:type="string">%product_sku%</item>
                            <item name="selection_qty" xsi:type="string">2</item>
                            <item name="selection_price_value" xsi:type="string">45</item>
                            <item name="selection_price_type" xsi:type="string">Fixed</item>
                            <item name="selection_can_change_qty" xsi:type="string">Yes</item>
                        </item>
                        <item name="1" xsi:type="array">
                            <item name="sku" xsi:type="string">%product_sku%</item>
                            <item name="selection_qty" xsi:type="string">3</item>
                            <item name="selection_price_value" xsi:type="string">43</item>
                            <item name="selection_price_type" xsi:type="string">Fixed</item>
                            <item name="selection_can_change_qty" xsi:type="string">Yes</item>
                        </item>
                    </item>
                </item>
                <item name="1" xsi:type="array">
                    <item name="title" xsi:type="string">Radio Button Option</item>
                    <item name="type" xsi:type="string">Radio Buttons</item>
                    <item name="required" xsi:type="string">No</item>
                    <item name="position" xsi:type="string">0</item>
                    <item name="assigned_products" xsi:type="array">
                        <item name="0" xsi:type="array">
                            <item name="sku" xsi:type="string">%product_sku%</item>
                            <item name="selection_qty" xsi:type="string">2</item>
                            <item name="selection_price_value" xsi:type="string">45</item>
                            <item name="selection_price_type" xsi:type="string">Fixed</item>
                            <item name="selection_can_change_qty" xsi:type="string">Yes</item>
                        </item>
                        <item name="1" xsi:type="array">
                            <item name="sku" xsi:type="string">%product_sku%</item>
                            <item name="selection_qty" xsi:type="string">3</item>
                            <item name="selection_price_value" xsi:type="string">43</item>
                            <item name="selection_price_type" xsi:type="string">Fixed</item>
                            <item name="selection_can_change_qty" xsi:type="string">Yes</item>
                        </item>
                    </item>
                </item>
            </field>
            <field name="products" xsi:type="array">
                <item name="0" xsi:type="array">
                    <item name="0" xsi:type="string">catalogProductSimple::default</item>
                    <item name="1" xsi:type="string">catalogProductSimple::50_dollar_product</item>
                </item>
                <item name="1" xsi:type="array">
                    <item name="0" xsi:type="string">catalogProductSimple::default</item>
                    <item name="1" xsi:type="string">catalogProductSimple::50_dollar_product</item>
                </item>
            </field>
        </dataset>
    </repository>
</config>