<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<mapping strict="0">
    <fields>
        <is_default>
            <selector>[name$="[is_default]"]</selector>
            <input>checkbox</input>
        </is_default>
        <selection_price_value>
            <selector>[name$='[selection_price_value]']</selector>
        </selection_price_value>
        <selection_price_type>
            <selector>[name$='[selection_price_type]']</selector>
            <input>select</input>
        </selection_price_type>
        <selection_qty>
            <selector>[name$='[selection_qty]']</selector>
        </selection_qty>
        <selection_can_change_qty>
            <selector>[name$='[selection_can_change_qty]']</selector>
            <input>select</input>
        </selection_can_change_qty>
        <get_is_default>
            <selector>[name$="[is_default]"]</selector>
            <input>checkbox</input>
        </get_is_default>
        <getProductSku>
            <selector>./td[1]/div[@class="nobr"]</selector>
            <strategy>xpath</strategy>
        </getProductSku>
        <getProductName>
            <selector>./td[1][div[@class="nobr"]]</selector>
            <strategy>xpath</strategy>
        </getProductName>
    </fields>
</mapping>
