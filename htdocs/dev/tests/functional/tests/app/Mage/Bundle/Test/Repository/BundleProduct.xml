<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/Magento/Mtf/Repository/etc/repository.xsd">
    <repository class="Mage\Bundle\Test\Repository\BundleProduct">
        <dataset name="default">
            <field name="name" xsi:type="string">BundleProduct %isolation%</field>
            <field name="sku_type" xsi:type="string">Dynamic</field>
            <field name="price_type" xsi:type="string">Dynamic</field>
            <field name="weight_type" xsi:type="string">Dynamic</field>
            <field name="description" xsi:type="string">Bundle product %isolation%</field>
            <field name="short_description" xsi:type="string">Short description bundle product %isolation%</field>
            <field name="status" xsi:type="string">Enabled</field>
            <field name="visibility" xsi:type="string">Catalog, Search</field>
            <field name="url_key" xsi:type="string">bundleproduct-%isolation%</field>
        </dataset>

        <dataset name="bundle_dynamic_product">
            <field name="name" xsi:type="string">Bundle dynamic product %isolation%</field>
            <field name="sku" xsi:type="string">sku_bundle_dynamic_product_%isolation%</field>
            <field name="sku_type" xsi:type="string">Dynamic</field>
            <field name="price_type" xsi:type="string">Dynamic</field>
            <field name="price" xsi:type="array">
                <item name="value" xsi:type="string">-</item>
                <item name="dataset" xsi:type="string">default_dynamic</item>
            </field>
            <field name="weight_type" xsi:type="string">Dynamic</field>
            <field name="shipment_type" xsi:type="string">Separately</field>
            <field name="tax_class_id" xsi:type="array">
                <item name="dataset" xsi:type="string">Taxable Goods</item>
            </field>
            <field name="website_ids" xsi:type="array">
                <item name="dataset" xsi:type="array">
                    <item name="0" xsi:type="string">main_website</item>
                </item>
            </field>
            <field name="stock_data" xsi:type="array">
                <item name="manage_stock" xsi:type="string">Yes</item>
                <item name="use_config_enable_qty_increments" xsi:type="string">Yes</item>
                <item name="use_config_qty_increments" xsi:type="string">Yes</item>
                <item name="is_in_stock" xsi:type="string">In Stock</item>
            </field>
            <field name="url_key" xsi:type="string">bundle-dynamic-product-%isolation%</field>
            <field name="visibility" xsi:type="string">Catalog, Search</field>
            <field name="bundle_selections" xsi:type="array">
                <item name="dataset" xsi:type="string">default_dynamic</item>
            </field>
            <field name="attribute_set_id" xsi:type="string">Default</field>
            <field name="checkout_data" xsi:type="array">
                <item name="dataset" xsi:type="string">default_dynamic</item>
            </field>
            <field name="description" xsi:type="string">Description for bundle dynamic product</field>
            <field name="short_description" xsi:type="string">Short description for bundle dynamic product</field>
            <field name="status" xsi:type="string">Enabled</field>
        </dataset>
        <dataset name="bundle_fixed_product">
            <field name="name" xsi:type="string">Bundle fixed product %isolation%</field>
            <field name="sku" xsi:type="string">sku_bundle_fixed_product_%isolation%</field>
            <field name="sku_type" xsi:type="string">Fixed</field>
            <field name="price_type" xsi:type="string">Fixed</field>
            <field name="price" xsi:type="array">
                <item name="value" xsi:type="string">750.00</item>
                <item name="dataset" xsi:type="string">default_bundle_fixed</item>
            </field>
            <field name="tax_class_id" xsi:type="array">
                <item name="dataset" xsi:type="string">Taxable Goods</item>
            </field>
            <field name="weight" xsi:type="string">1.0000</field>
            <field name="weight_type" xsi:type="string">Fixed</field>
            <field name="shipment_type" xsi:type="string">Together</field>
            <field name="website_ids" xsi:type="array">
                <item name="dataset" xsi:type="array">
                    <item name="0" xsi:type="string">main_website</item>
                </item>
            </field>
            <field name="stock_data" xsi:type="array">
                <item name="manage_stock" xsi:type="string">Yes</item>
                <item name="use_config_enable_qty_increments" xsi:type="string">Yes</item>
                <item name="use_config_qty_increments" xsi:type="string">Yes</item>
                <item name="is_in_stock" xsi:type="string">In Stock</item>
            </field>
            <field name="url_key" xsi:type="string">bundle-fixed-product-%isolation%</field>
            <field name="visibility" xsi:type="string">Catalog, Search</field>
            <field name="bundle_selections" xsi:type="array">
                <item name="dataset" xsi:type="string">default_fixed</item>
            </field>
            <field name="attribute_set_id" xsi:type="string">Default</field>
            <field name="checkout_data" xsi:type="array">
                <item name="dataset" xsi:type="string">default_fixed</item>
            </field>
            <field name="description" xsi:type="string">Description for bundle dynamic product</field>
            <field name="short_description" xsi:type="string">Short description for bundle dynamic product</field>
            <field name="status" xsi:type="string">Enabled</field>
        </dataset>
    </repository>
</config>
