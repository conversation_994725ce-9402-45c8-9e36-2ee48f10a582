<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Mage\Paypal\Test\TestCase\CreateInvoiceForPaypalExpressCheckoutTest" summary="CreateInvoiceForPaypalExpressCheckoutTest">
        <variation name="CreateInvoiceForPaypalExpressCheckoutTestVariation1" method="test">
            <data name="description" xsi:type="string">Create full online invoice with shipment for paypal express checkout with line items mark require billing address</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="configData" xsi:type="string">paypal_express_order_line_items_mark_require_billing_address</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="data/invoice/0/form_data/comment_text" xsi:type="string">comments for invoice</data>
            <data name="data/invoice/0/form_data/do_shipment" xsi:type="string">Yes</data>
            <data name="verifyData/grandTotal/invoices/0/from" xsi:type="string">315.00</data>
            <data name="verifyData/grandTotal/invoices/0/to" xsi:type="string">315.00</data>
            <data name="verifyData/items_data/0/item_status" xsi:type="string">Shipped</data>
            <data name="verifyData/items_data/0/item_price" xsi:type="string">100.00</data>
            <data name="verifyData/items_data/0/item_subtotal" xsi:type="string">300.00</data>
            <data name="verifyData/items_data/0/item_row_total" xsi:type="string">300.00</data>
            <data name="status" xsi:type="string">Complete</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceWithShipmentSuccessMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesTab" />
            <constraint name="Mage\Sales\Test\Constraint\AssertNoInvoiceButton" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItems" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
        <variation name="CreateInvoiceForPaypalExpressCheckoutTestVariation2" method="test">
            <data name="description" xsi:type="string">Create partial online invoice for payflow express authorization line items</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="configData" xsi:type="string">payflow_express_authorization_line_items</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="data/invoice/0/items_data/0/qty" xsi:type="string">1</data>
            <data name="data/invoice/0/form_data/comment_text" xsi:type="string">comments for invoice</data>
            <data name="verifyData/grandTotal/invoices/0/from" xsi:type="string">115.00</data>
            <data name="verifyData/grandTotal/invoices/0/to" xsi:type="string">115.00</data>
            <data name="verifyData/items_data/0/qty" xsi:type="string">1</data>
            <data name="verifyData/items_data/0/item_price" xsi:type="string">100.00</data>
            <data name="verifyData/items_data/0/item_subtotal" xsi:type="string">100.00</data>
            <data name="verifyData/items_data/0/item_row_total" xsi:type="string">100.00</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceSuccessCreateMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesTab" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItems" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
        <variation name="CreateInvoiceForPaypalExpressCheckoutTestVariation3" method="test">
            <data name="description" xsi:type="string">Create full online invoice for payflow express authorization specific country us</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="configData" xsi:type="string">payflow_express_authorization_specificcountry_us</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="data/invoice/0/form_data/comment_text" xsi:type="string">comments for invoice</data>
            <data name="verifyData/grandTotal/invoices/0/from" xsi:type="string">315.00</data>
            <data name="verifyData/grandTotal/invoices/0/to" xsi:type="string">315.00</data>
            <data name="verifyData/items_data/0/item_price" xsi:type="string">100.00</data>
            <data name="verifyData/items_data/0/item_subtotal" xsi:type="string">300.00</data>
            <data name="verifyData/items_data/0/item_row_total" xsi:type="string">300.00</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceSuccessCreateMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesTab" />
            <constraint name="Mage\Sales\Test\Constraint\AssertNoInvoiceButton" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItems" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
        <variation name="CreateInvoiceForPaypalExpressCheckoutTestVariation5" method="test">
            <data name="description" xsi:type="string">Create full online invoice for payflow express sale specific country gb line items</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="configData" xsi:type="string">payflow_express_sale_specificcountry_gb_line_items</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="data/invoice/0/form_data/comment_text" xsi:type="string">comments for invoice</data>
            <data name="verifyData/grandTotal/invoices/0/from" xsi:type="string">315.00</data>
            <data name="verifyData/grandTotal/invoices/0/to" xsi:type="string">315.00</data>
            <data name="verifyData/items_data/0/item_price" xsi:type="string">100.00</data>
            <data name="verifyData/items_data/0/item_subtotal" xsi:type="string">300.00</data>
            <data name="verifyData/items_data/0/item_row_total" xsi:type="string">300.00</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceSuccessCreateMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesTab" />
            <constraint name="Mage\Sales\Test\Constraint\AssertNoInvoiceButton" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItems" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
        <variation name="CreateInvoiceForPaypalExpressCheckoutTestVariation6" method="test">
            <data name="description" xsi:type="string">Create online full invoice for paypal express sale specific country us shipping options mark</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="configData" xsi:type="string">paypal_express_sale_specificcountry_us_shipping_options_mark</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="data/invoice/0/form_data/comment_text" xsi:type="string">comments for invoice</data>
            <data name="verifyData/grandTotal/invoices/0/from" xsi:type="string">315.00</data>
            <data name="verifyData/grandTotal/invoices/0/to" xsi:type="string">315.00</data>
            <data name="verifyData/items_data/0/item_price" xsi:type="string">100.00</data>
            <data name="verifyData/items_data/0/item_subtotal" xsi:type="string">300.00</data>
            <data name="verifyData/items_data/0/item_row_total" xsi:type="string">300.00</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceSuccessCreateMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesTab" />
            <constraint name="Mage\Sales\Test\Constraint\AssertNoInvoiceButton" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItems" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
        <variation name="CreateInvoiceForPaypalExpressCheckoutTestVariation7" method="test">
            <data name="description" xsi:type="string">Create full online invoice for paypal express sale specific country us shipping options mark</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="configData" xsi:type="string">paypal_express_sale_specificcountry_us_shipping_options_mark</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="data/invoice/0/form_data/comment_text" xsi:type="string">comments for invoice</data>
            <data name="verifyData/grandTotal/invoices/0/from" xsi:type="string">315.00</data>
            <data name="verifyData/grandTotal/invoices/0/to" xsi:type="string">315.00</data>
            <data name="verifyData/items_data/0/item_price" xsi:type="string">100.00</data>
            <data name="verifyData/items_data/0/item_subtotal" xsi:type="string">300.00</data>
            <data name="verifyData/items_data/0/item_row_total" xsi:type="string">300.00</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceSuccessCreateMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesTab" />
            <constraint name="Mage\Sales\Test\Constraint\AssertNoInvoiceButton" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItems" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
        <variation name="CreateInvoiceForPaypalExpressCheckoutTestVariation80" method="test">
            <data name="description" xsi:type="string">Create full online invoice with 2 products and taxes for payflow express authorization line items</data>
            <data name="products" xsi:type="string">catalogProductSimple::default,catalogProductSimple::default</data>
            <data name="taxRule" xsi:type="string">default</data>
            <data name="configData" xsi:type="string">payflow_express_authorization_line_items, tax_calculation_base_on_shipping_origin</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="data/invoice/0/form_data/comment_text" xsi:type="string">comments for invoice</data>
            <data name="verifyData/grandTotal/invoices/0/from" xsi:type="string">679.50</data>
            <data name="verifyData/grandTotal/invoices/0/to" xsi:type="string">679.50</data>
            <data name="verifyData/items_data/0/item_price" xsi:type="string">100.00</data>
            <data name="verifyData/items_data/0/item_subtotal" xsi:type="string">300.00</data>
            <data name="verifyData/items_data/0/item_tax" xsi:type="string">24.75</data>
            <data name="verifyData/items_data/0/item_row_total" xsi:type="string">324.75</data>
            <data name="verifyData/items_data/1/item_price" xsi:type="string">100.00</data>
            <data name="verifyData/items_data/1/item_subtotal" xsi:type="string">300.00</data>
            <data name="verifyData/items_data/1/item_tax" xsi:type="string">24.75</data>
            <data name="verifyData/items_data/1/item_row_total" xsi:type="string">324.75</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceSuccessCreateMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesTab" />
            <constraint name="Mage\Sales\Test\Constraint\AssertNoInvoiceButton" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItems" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
        <variation name="CreateInvoiceForPaypalExpressCheckoutTestVariation81" method="test">
            <data name="description" xsi:type="string">Create full online invoice with configurable product for payflow express authorization line items</data>
            <data name="products" xsi:type="string">configurableProduct::default</data>
            <data name="configData" xsi:type="string">payflow_express_authorization_line_items</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="data/invoice/0/form_data/comment_text" xsi:type="string">comments for invoice</data>
            <data name="verifyData/grandTotal/invoices/0/from" xsi:type="string">330.00</data>
            <data name="verifyData/grandTotal/invoices/0/to" xsi:type="string">330.00</data>
            <data name="verifyData/items_data/0/item_price" xsi:type="string">160.00</data>
            <data name="verifyData/items_data/0/item_subtotal" xsi:type="string">320.00</data>
            <data name="verifyData/items_data/0/item_row_total" xsi:type="string">320.00</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceSuccessCreateMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesTab" />
            <constraint name="Mage\Sales\Test\Constraint\AssertNoInvoiceButton" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceConfigurableItems" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceConfigurableItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
        <variation name="CreateInvoiceForPaypalExpressCheckoutTestVariation8" method="test">
            <data name="description" xsi:type="string">Create full offline invoice with shipment for paypal express checkout with line items mark require billing address</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="configData" xsi:type="string">paypal_express_order_line_items_mark_require_billing_address</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="data/invoice/0/form_data/comment_text" xsi:type="string">comments for invoice</data>
            <data name="data/invoice/0/form_data/do_shipment" xsi:type="string">Yes</data>
            <data name="data/invoice/0/form_data/capture_case" xsi:type="string">Capture Offline</data>
            <data name="verifyData/grandTotal/invoices/0/from" xsi:type="string">315.00</data>
            <data name="verifyData/grandTotal/invoices/0/to" xsi:type="string">315.00</data>
            <data name="verifyData/item_status" xsi:type="string">Shipped</data>
            <data name="verifyData/item_price" xsi:type="string">100.00</data>
            <data name="verifyData/item_subtotal" xsi:type="string">300.00</data>
            <data name="verifyData/item_row_total" xsi:type="string">300.00</data>
            <data name="status" xsi:type="string">Complete</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceWithShipmentSuccessMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesTab" />
            <constraint name="Mage\Sales\Test\Constraint\AssertNoInvoiceButton" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItems" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
        <variation name="CreateShipmentForPaypalExpressCheckoutTestVariation9" method="test">
            <data name="description" xsi:type="string">Create partial offline invoice for payflow express authorization line items</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="configData" xsi:type="string">payflow_express_authorization_line_items</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="data/invoice/0/items_data/0/qty" xsi:type="string">1</data>
            <data name="data/invoice/0/form_data/comment_text" xsi:type="string">comments for invoice</data>
            <data name="data/invoice/0/form_data/capture_case" xsi:type="string">Capture Offline</data>
            <data name="verifyData/grandTotal/invoices/0/from" xsi:type="string">115.00</data>
            <data name="verifyData/grandTotal/invoices/0/to" xsi:type="string">115.00</data>
            <data name="verifyData/items_data/0/qty" xsi:type="string">1</data>
            <data name="verifyData/item_price" xsi:type="string">100.00</data>
            <data name="verifyData/item_subtotal" xsi:type="string">100.00</data>
            <data name="verifyData/item_row_total" xsi:type="string">100.00</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceSuccessCreateMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesTab" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItems" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
        <variation name="CreateShipmentForPaypalExpressCheckoutTestVariation10" method="test">
            <data name="description" xsi:type="string">Create full offline invoice for payflow express authorization specific country us</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="configData" xsi:type="string">payflow_express_authorization_specificcountry_us</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="data/invoice/0/form_data/comment_text" xsi:type="string">comments for invoice</data>
            <data name="data/invoice/0/form_data/capture_case" xsi:type="string">Capture Offline</data>
            <data name="verifyData/grandTotal/invoices/0/from" xsi:type="string">315.00</data>
            <data name="verifyData/grandTotal/invoices/0/to" xsi:type="string">315.00</data>
            <data name="verifyData/item_price" xsi:type="string">100.00</data>
            <data name="verifyData/item_subtotal" xsi:type="string">300.00</data>
            <data name="verifyData/item_row_total" xsi:type="string">300.00</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceSuccessCreateMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesTab" />
            <constraint name="Mage\Sales\Test\Constraint\AssertNoInvoiceButton" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItems" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
        <variation name="CreateShipmentForPaypalExpressCheckoutTestVariation11" method="test">
            <data name="description" xsi:type="string">Create full offline invoice for payflow express sale specific country gb line items</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="configData" xsi:type="string">payflow_express_sale_specificcountry_gb_line_items</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="data/invoice/0/form_data/comment_text" xsi:type="string">comments for invoice</data>
            <data name="data/invoice/0/form_data/capture_case" xsi:type="string">Capture Offline</data>
            <data name="verifyData/grandTotal/invoices/0/from" xsi:type="string">315.00</data>
            <data name="verifyData/grandTotal/invoices/0/to" xsi:type="string">315.00</data>
            <data name="verifyData/item_price" xsi:type="string">100.00</data>
            <data name="verifyData/item_subtotal" xsi:type="string">300.00</data>
            <data name="verifyData/item_row_total" xsi:type="string">300.00</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceSuccessCreateMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesTab" />
            <constraint name="Mage\Sales\Test\Constraint\AssertNoInvoiceButton" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItems" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
        <variation name="CreateShipmentForPaypalExpressCheckoutTestVariation12" method="test">
            <data name="description" xsi:type="string">Create full offline invoice for paypal express sale specific country us shipping options mark</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="configData" xsi:type="string">paypal_express_sale_specificcountry_us_shipping_options_mark</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="data/invoice/0/form_data/comment_text" xsi:type="string">comments for invoice</data>
            <data name="data/invoice/0/form_data/capture_case" xsi:type="string">Capture Offline</data>
            <data name="verifyData/grandTotal/invoices/0/from" xsi:type="string">315.00</data>
            <data name="verifyData/grandTotal/invoices/0/to" xsi:type="string">315.00</data>
            <data name="verifyData/item_price" xsi:type="string">100.00</data>
            <data name="verifyData/item_subtotal" xsi:type="string">300.00</data>
            <data name="verifyData/item_row_total" xsi:type="string">300.00</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceSuccessCreateMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesTab" />
            <constraint name="Mage\Sales\Test\Constraint\AssertNoInvoiceButton" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItems" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
        <variation name="CreateShipmentForPaypalExpressCheckoutTestVariation13" method="test">
            <data name="description" xsi:type="string">Create full offline invoice for paypal express sale specific country us shipping options mark</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="configData" xsi:type="string">paypal_express_sale_specificcountry_us_shipping_options_mark</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="data/invoice/0/form_data/comment_text" xsi:type="string">comments for invoice</data>
            <data name="data/invoice/0/form_data/capture_case" xsi:type="string">Capture Offline</data>
            <data name="verifyData/grandTotal/invoices/0/from" xsi:type="string">315.00</data>
            <data name="verifyData/grandTotal/invoices/0/to" xsi:type="string">315.00</data>
            <data name="verifyData/item_price" xsi:type="string">100.00</data>
            <data name="verifyData/item_subtotal" xsi:type="string">300.00</data>
            <data name="verifyData/item_row_total" xsi:type="string">300.00</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceSuccessCreateMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesTab" />
            <constraint name="Mage\Sales\Test\Constraint\AssertNoInvoiceButton" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItems" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
    </testCase>
</config>
