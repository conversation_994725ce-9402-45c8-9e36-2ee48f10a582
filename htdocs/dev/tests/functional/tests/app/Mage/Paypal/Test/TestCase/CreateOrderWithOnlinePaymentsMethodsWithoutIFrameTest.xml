<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Mage\Paypal\Test\TestCase\CreateOrderWithOnlinePaymentsMethodsWithoutIFrameTest" summary="CreateOrderWithOnlinePaymentsMethodsWithoutIFrameTest">
        <variation name="CreateOrderWithOnlinePaymentsMethodsWithoutIFrameTestVariation1" firstConstraint="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" method="test">
            <data name="configData" xsi:type="string">paypal_payments_pro</data>
            <data name="products" xsi:type="string">catalogProductSimple::order_default</data>
            <data name="customer/dataset" xsi:type="string">default_frontend</data>
            <data name="checkoutMethod" xsi:type="string">guest</data>
            <data name="customerPersist" xsi:type="string">no</data>
            <data name="billingAddress/dataset" xsi:type="string">customer_US</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="payment/method" xsi:type="string">paypal_direct</data>
            <data name="payment/cc" xsi:type="string">payments_pro</data>
            <data name="transactionType" xsi:type="string">Authorization</data>
            <data name="paymentAction" xsi:type="string">Authorized</data>
            <data name="grandTotal" xsi:type="string">105</data>
            <data name="tag" xsi:type="string">payment_method:paypal, paypal:payments_pro</data>
            <constraint name="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" next="Mage\Sales\Test\Constraint\AssertOrderInOrdersGrid"/>
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderInOrdersGrid" next="Mage\Paypal\Test\Constraint\AssertTransaction" prev="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage"/>
            <constraint name="Mage\Paypal\Test\Constraint\AssertTransaction" next="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" prev="Mage\Sales\Test\Constraint\AssertOrderInOrdersGrid"/>
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" prev="Mage\Paypal\Test\Constraint\AssertTransaction"/>
        </variation>
        <variation name="CreateOrderWithOnlinePaymentsMethodsWithoutIFrameTestVariation2" firstConstraint="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" method="test">
            <data name="configData" xsi:type="string">paypal_payments_pro</data>
            <data name="products" xsi:type="string">catalogProductSimple::order_default</data>
            <data name="customer/dataset" xsi:type="string">default_frontend_new</data>
            <data name="checkoutMethod" xsi:type="string">login</data>
            <data name="billingAddress/dataset" xsi:type="string">customer_US_login</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="payment/method" xsi:type="string">paypal_direct</data>
            <data name="payment/cc" xsi:type="string">payments_pro</data>
            <data name="transactionType" xsi:type="string">Authorization</data>
            <data name="paymentAction" xsi:type="string">Authorized</data>
            <data name="grandTotal" xsi:type="string">105</data>
            <data name="tag" xsi:type="string">payment_method:paypal, paypal:payments_pro</data>
            <constraint name="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" next="Mage\Sales\Test\Constraint\AssertOrderInOrdersGrid"/>
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderInOrdersGrid" next="Mage\Paypal\Test\Constraint\AssertTransaction" prev="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage"/>
            <constraint name="Mage\Paypal\Test\Constraint\AssertTransaction" next="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" prev="Mage\Sales\Test\Constraint\AssertOrderInOrdersGrid"/>
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" prev="Mage\Paypal\Test\Constraint\AssertTransaction"/>
        </variation>
        <variation name="CreateOrderWithOnlinePaymentsMethodsWithoutIFrameTestVariation3" firstConstraint="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" method="test">
            <data name="configData" xsi:type="string">paypal_payments_pro_action_sale</data>
            <data name="products" xsi:type="string">catalogProductSimple::order_default</data>
            <data name="customer/dataset" xsi:type="string">default_frontend_new</data>
            <data name="checkoutMethod" xsi:type="string">register</data>
            <data name="customerPersist" xsi:type="string">no</data>
            <data name="billingAddress/dataset" xsi:type="string">customer_US</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="payment/method" xsi:type="string">paypal_direct</data>
            <data name="payment/cc" xsi:type="string">payments_pro</data>
            <data name="transactionType" xsi:type="string">Capture</data>
            <data name="paymentAction" xsi:type="string">Captured</data>
            <data name="grandTotal" xsi:type="string">105</data>
            <data name="tag" xsi:type="string">payment_method:paypal, paypal:payments_pro</data>
            <constraint name="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" next="Mage\Sales\Test\Constraint\AssertOrderInOrdersGrid"/>
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderInOrdersGrid" next="Mage\Paypal\Test\Constraint\AssertTransaction" prev="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage"/>
            <constraint name="Mage\Paypal\Test\Constraint\AssertTransaction" next="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" prev="Mage\Sales\Test\Constraint\AssertOrderInOrdersGrid"/>
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" prev="Mage\Paypal\Test\Constraint\AssertTransaction"/>
        </variation>
        <variation name="CreateOrderWithOnlinePaymentsMethodsWithoutIFrameTestVariation4" firstConstraint="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" method="test">
            <data name="configData" xsi:type="string">paypal_payflow_pro</data>
            <data name="products" xsi:type="string">catalogProductSimple::order_default</data>
            <data name="customer/dataset" xsi:type="string">default_frontend</data>
            <data name="checkoutMethod" xsi:type="string">guest</data>
            <data name="customerPersist" xsi:type="string">no</data>
            <data name="billingAddress/dataset" xsi:type="string">customer_US</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="payment/method" xsi:type="string">verisign</data>
            <data name="payment/cc" xsi:type="string">default</data>
            <data name="transactionType" xsi:type="string">Authorization</data>
            <data name="paymentAction" xsi:type="string">Authoriz</data>
            <data name="grandTotal" xsi:type="string">105</data>
            <data name="tag" xsi:type="string">payment_method:paypal, paypal:payflow_pro</data>
            <constraint name="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" next="Mage\Sales\Test\Constraint\AssertOrderInOrdersGrid"/>
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderInOrdersGrid" next="Mage\Paypal\Test\Constraint\AssertTransaction" prev="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage"/>
            <constraint name="Mage\Paypal\Test\Constraint\AssertTransaction" next="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" prev="Mage\Sales\Test\Constraint\AssertOrderInOrdersGrid"/>
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" prev="Mage\Paypal\Test\Constraint\AssertTransaction"/>
        </variation>
        <variation name="CreateOrderWithOnlinePaymentsMethodsWithoutIFrameTestVariation5" firstConstraint="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" method="test">
            <data name="configData" xsi:type="string">paypal_payflow_pro</data>
            <data name="products" xsi:type="string">catalogProductSimple::order_default</data>
            <data name="customer/dataset" xsi:type="string">default_frontend_new</data>
            <data name="checkoutMethod" xsi:type="string">login</data>
            <data name="billingAddress/dataset" xsi:type="string">customer_US_login</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="payment/method" xsi:type="string">verisign</data>
            <data name="payment/cc" xsi:type="string">default</data>
            <data name="transactionType" xsi:type="string">Authorization</data>
            <data name="paymentAction" xsi:type="string">Authoriz</data>
            <data name="grandTotal" xsi:type="string">105</data>
            <data name="tag" xsi:type="string">payment_method:paypal, paypal:payflow_pro</data>
            <constraint name="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" next="Mage\Sales\Test\Constraint\AssertOrderInOrdersGrid"/>
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderInOrdersGrid" next="Mage\Paypal\Test\Constraint\AssertTransaction" prev="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage"/>
            <constraint name="Mage\Paypal\Test\Constraint\AssertTransaction" next="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" prev="Mage\Sales\Test\Constraint\AssertOrderInOrdersGrid"/>
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" prev="Mage\Paypal\Test\Constraint\AssertTransaction"/>
        </variation>
        <variation name="CreateOrderWithOnlinePaymentsMethodsWithoutIFrameTestVariation6" firstConstraint="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" method="test">
            <data name="configData" xsi:type="string">paypal_payflow_pro_action_sale</data>
            <data name="products" xsi:type="string">catalogProductSimple::order_default</data>
            <data name="customer/dataset" xsi:type="string">default_frontend_new</data>
            <data name="checkoutMethod" xsi:type="string">register</data>
            <data name="customerPersist" xsi:type="string">no</data>
            <data name="billingAddress/dataset" xsi:type="string">customer_US</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="payment/method" xsi:type="string">verisign</data>
            <data name="payment/cc" xsi:type="string">default</data>
            <data name="transactionType" xsi:type="string">Capture</data>
            <data name="paymentAction" xsi:type="string">Captur</data>
            <data name="grandTotal" xsi:type="string">105</data>
            <data name="tag" xsi:type="string">payment_method:paypal, paypal:payflow_pro</data>
            <constraint name="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" next="Mage\Sales\Test\Constraint\AssertOrderInOrdersGrid"/>
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderInOrdersGrid" next="Mage\Paypal\Test\Constraint\AssertTransaction" prev="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage"/>
            <constraint name="Mage\Paypal\Test\Constraint\AssertTransaction" next="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" prev="Mage\Sales\Test\Constraint\AssertOrderInOrdersGrid"/>
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" prev="Mage\Paypal\Test\Constraint\AssertTransaction"/>
        </variation>
    </testCase>
</config>
