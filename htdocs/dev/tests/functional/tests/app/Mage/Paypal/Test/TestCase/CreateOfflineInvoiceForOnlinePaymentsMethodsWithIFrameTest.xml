<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Mage\Paypal\Test\TestCase\CreateOfflineInvoiceForOnlinePaymentsMethodsWithIFrameTest" summary="CreateOfflineInvoiceForOnlinePaymentsMethodsWithIFrameTest">
        <variation name="CreateOfflineInvoiceForOnlinePaymentsMethodsWithIFrameTestVariation1" method="test">
            <data name="description" xsi:type="string">Create full invoice with shipment for paypal payflow link</data>
            <data name="configData" xsi:type="string">paypal_payflow_link</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="customer/dataset" xsi:type="string">default_frontend_new</data>
            <data name="checkoutMethod" xsi:type="string">login</data>
            <data name="customerPersist" xsi:type="string">yes</data>
            <data name="billingAddress/dataset" xsi:type="string">customer_US_login</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="payment/method" xsi:type="string">payflow_link</data>
            <data name="payment/cc" xsi:type="string">for_iframe</data>
            <data name="payment/iframe" xsi:type="string">yes</data>
            <data name="data/invoice/0/form_data/do_shipment" xsi:type="string">Yes</data>
            <data name="data/invoice/0/form_data/comment_text" xsi:type="string">comments for invoice</data>
            <data name="data/invoice/0/form_data/capture_case" xsi:type="string">Capture Offline</data>
            <data name="verifyData/grandTotal/invoices/0/from" xsi:type="string">315.00</data>
            <data name="verifyData/grandTotal/invoices/0/to" xsi:type="string">315.00</data>
            <data name="verifyData/item_status" xsi:type="string">Shipped</data>
            <data name="verifyData/item_price" xsi:type="string">100.00</data>
            <data name="verifyData/item_subtotal" xsi:type="string">300.00</data>
            <data name="verifyData/item_row_total" xsi:type="string">300.00</data>
            <data name="status" xsi:type="string">Complete</data>
            <data name="tag" xsi:type="string">payment_method:paypal, paypal:payflow_link</data>
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceWithShipmentSuccessMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesTab" />
            <constraint name="Mage\Sales\Test\Constraint\AssertNoInvoiceButton" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItems" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
        <variation name="CreateOfflineInvoiceForOnlinePaymentsMethodsWithIFrameTestVariation2" method="test">
            <data name="description" xsi:type="string">Create partial invoice for paypal advanced</data>
            <data name="configData" xsi:type="string">paypal_advanced</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="customer/dataset" xsi:type="string">default_frontend_new</data>
            <data name="checkoutMethod" xsi:type="string">login</data>
            <data name="customerPersist" xsi:type="string">yes</data>
            <data name="billingAddress/dataset" xsi:type="string">customer_US_login</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="payment/method" xsi:type="string">payflow_advanced</data>
            <data name="payment/cc" xsi:type="string">for_iframe</data>
            <data name="payment/iframe" xsi:type="string">yes</data>
            <data name="data/invoice/0/form_data/comment_text" xsi:type="string">comments for invoice</data>
            <data name="data/invoice/0/items_data/0/qty" xsi:type="string">1</data>
            <data name="data/invoice/0/form_data/capture_case" xsi:type="string">Capture Offline</data>
            <data name="verifyData/grandTotal/invoices/0/from" xsi:type="string">115.00</data>
            <data name="verifyData/grandTotal/invoices/0/to" xsi:type="string">115.00</data>
            <data name="verifyData/items_data/0/qty" xsi:type="string">1</data>
            <data name="verifyData/item_price" xsi:type="string">100.00</data>
            <data name="verifyData/item_subtotal" xsi:type="string">100.00</data>
            <data name="verifyData/item_row_total" xsi:type="string">100.00</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">payment_method:paypal, paypal:advanced</data>
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceSuccessCreateMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesTab" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItems" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
        <variation name="CreateOfflineInvoiceForOnlinePaymentsMethodsWithIFrameTestVariation3" method="test">
            <data name="description" xsi:type="string">Create full invoice for paypal advanced action sale</data>
            <data name="configData" xsi:type="string">paypal_advanced_action_sale</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="customer/dataset" xsi:type="string">default_frontend_new</data>
            <data name="checkoutMethod" xsi:type="string">login</data>
            <data name="customerPersist" xsi:type="string">yes</data>
            <data name="billingAddress/dataset" xsi:type="string">customer_US_login</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="payment/method" xsi:type="string">payflow_advanced</data>
            <data name="payment/cc" xsi:type="string">for_iframe</data>
            <data name="payment/iframe" xsi:type="string">yes</data>
            <data name="action/invoice/0" xsi:type="boolean">false</data>
            <data name="data/invoice/0/form_data/comment_text" xsi:type="string">comments for invoice</data>
            <data name="data/invoice/0/form_data/capture_case" xsi:type="string">Capture Offline</data>
            <data name="verifyData/grandTotal/invoices/0/from" xsi:type="string">315.00</data>
            <data name="verifyData/grandTotal/invoices/0/to" xsi:type="string">315.00</data>
            <data name="verifyData/item_price" xsi:type="string">100.00</data>
            <data name="verifyData/item_subtotal" xsi:type="string">300.00</data>
            <data name="verifyData/item_row_total" xsi:type="string">300.00</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">payment_method:paypal, paypal:advanced</data>
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesTab" />
            <constraint name="Mage\Sales\Test\Constraint\AssertNoInvoiceButton" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItems" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
        <variation name="CreateOfflineInvoiceForOnlinePaymentsMethodsWithIFrameTestVariation4" method="test">
            <data name="description" xsi:type="string">Create full invoice for paypal hosted solution</data>
            <data name="configData" xsi:type="string">paypal_hosted_solution</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="customer/dataset" xsi:type="string">default_frontend_new</data>
            <data name="checkoutMethod" xsi:type="string">login</data>
            <data name="customerPersist" xsi:type="string">yes</data>
            <data name="billingAddress/dataset" xsi:type="string">customer_US_login</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="payment/method" xsi:type="string">hosted_pro</data>
            <data name="payment/cc" xsi:type="string">hosted_pro</data>
            <data name="payment/iframe" xsi:type="string">yes</data>
            <data name="data/invoice/0/form_data/comment_text" xsi:type="string">comments for invoice</data>
            <data name="data/invoice/0/form_data/do_shipment" xsi:type="string">Yes</data>
            <data name="data/invoice/0/form_data/capture_case" xsi:type="string">Capture Offline</data>
            <data name="verifyData/grandTotal/invoices/0/from" xsi:type="string">315.00</data>
            <data name="verifyData/grandTotal/invoices/0/to" xsi:type="string">315.00</data>
            <data name="verifyData/item_status" xsi:type="string">Shipped</data>
            <data name="verifyData/item_price" xsi:type="string">100.00</data>
            <data name="verifyData/item_subtotal" xsi:type="string">300.00</data>
            <data name="verifyData/item_row_total" xsi:type="string">300.00</data>
            <data name="status" xsi:type="string">Complete</data>
            <data name="tag" xsi:type="string">payment_method:paypal, paypal:hosted_solution</data>
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceWithShipmentSuccessMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesTab" />
            <constraint name="Mage\Sales\Test\Constraint\AssertNoInvoiceButton" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItems" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
        <variation name="CreateOfflineInvoiceForOnlinePaymentsMethodsWithIFrameTestVariation5" method="test">
            <data name="description" xsi:type="string">Create full invoice for paypal hosted solution action sale</data>
            <data name="configData" xsi:type="string">paypal_hosted_solution_action_sale</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="customer/dataset" xsi:type="string">default_frontend_new</data>
            <data name="checkoutMethod" xsi:type="string">login</data>
            <data name="customerPersist" xsi:type="string">yes</data>
            <data name="billingAddress/dataset" xsi:type="string">customer_US_login</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="payment/method" xsi:type="string">hosted_pro</data>
            <data name="payment/cc" xsi:type="string">hosted_pro</data>
            <data name="payment/iframe" xsi:type="string">yes</data>
            <data name="action/invoice/0" xsi:type="boolean">false</data>
            <data name="data/invoice/0/form_data/comment_text" xsi:type="string">comments for invoice</data>
            <data name="data/invoice/0/form_data/capture_case" xsi:type="string">Capture Offline</data>
            <data name="verifyData/grandTotal/invoices/0/from" xsi:type="string">315.00</data>
            <data name="verifyData/grandTotal/invoices/0/to" xsi:type="string">315.00</data>
            <data name="verifyData/item_price" xsi:type="string">100.00</data>
            <data name="verifyData/item_subtotal" xsi:type="string">300.00</data>
            <data name="verifyData/item_row_total" xsi:type="string">300.00</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">payment_method:paypal, paypal:hosted_solution</data>
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesTab" />
            <constraint name="Mage\Sales\Test\Constraint\AssertNoInvoiceButton" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItems" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
    </testCase>
</config>
