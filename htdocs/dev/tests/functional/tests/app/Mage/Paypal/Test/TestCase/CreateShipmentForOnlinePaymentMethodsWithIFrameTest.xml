<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Mage\Paypal\Test\TestCase\CreateShipmentForOnlinePaymentMethodsWithIFrameTest" summary="CreateShipmentForOnlinePaymentMethodsWithIFrameTest">
        <variation name="CreateShipmentForOnlinePaymentMethodsWithIFrameTestVariation5" method="test">
            <data name="description" xsi:type="string">Create full shipment for paypal payflow link</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="configData" xsi:type="string">paypal_payflow_link</data>
            <data name="customer/dataset" xsi:type="string">default_frontend_new</data>
            <data name="checkoutMethod" xsi:type="string">login</data>
            <data name="customerPersist" xsi:type="string">yes</data>
            <data name="billingAddress/dataset" xsi:type="string">customer_US_login</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="payment/method" xsi:type="string">payflow_link</data>
            <data name="payment/cc" xsi:type="string">for_iframe</data>
            <data name="payment/iframe" xsi:type="string">yes</data>
            <data name="data/shipment/0/form_data/comment_text" xsi:type="string">comments for shipment</data>
            <data name="verifyData/totalQtyOrdered/shipments/0/total_qty_from" xsi:type="string">3</data>
            <data name="verifyData/totalQtyOrdered/shipments/0/total_qty_to" xsi:type="string">3</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">payment_method:paypal_direct, paypal:payflow_link</data>
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentSuccessCreateMessage" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentInShipmentsTab" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentInShipmentsGrid" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentItems" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertNoShipButton" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
        <variation name="CreateShipmentForOnlinePaymentMethodsWithIFrameTestVariation6" method="test">
            <data name="description" xsi:type="string">Create full shipment for paypal advanced</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="configData" xsi:type="string">paypal_advanced</data>
            <data name="customer/dataset" xsi:type="string">default_frontend_new</data>
            <data name="checkoutMethod" xsi:type="string">login</data>
            <data name="customerPersist" xsi:type="string">yes</data>
            <data name="billingAddress/dataset" xsi:type="string">customer_US_login</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="payment/method" xsi:type="string">payflow_advanced</data>
            <data name="payment/cc" xsi:type="string">for_iframe</data>
            <data name="payment/iframe" xsi:type="string">yes</data>
            <data name="data/shipment/0/form_data/comment_text" xsi:type="string">comments for shipment</data>
            <data name="verifyData/totalQtyOrdered/shipments/0/total_qty_from" xsi:type="string">3</data>
            <data name="verifyData/totalQtyOrdered/shipments/0/total_qty_to" xsi:type="string">3</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">payment_method:paypal_direct, paypal:advanced</data>
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentSuccessCreateMessage" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentInShipmentsTab" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentInShipmentsGrid" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentItems" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertNoShipButton" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
    </testCase>
</config>
