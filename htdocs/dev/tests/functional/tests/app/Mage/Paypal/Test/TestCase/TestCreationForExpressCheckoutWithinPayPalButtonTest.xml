<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Mage\Paypal\Test\TestCase\TestCreationForExpressCheckoutWithinPayPalButtonTest" summary="TestCreationForExpressCheckoutWithinPayPalButtonTest">
        <variation name="TestCreationForExpressCheckoutWithinPayPalButtonTestVariation1" method="test">
            <data name="products" xsi:type="string">catalogProductVirtual::order_default,catalogProductSimple::default</data>
            <data name="salesRule" xsi:type="string">active_sales_rule_for_all_groups</data>
            <data name="configData" xsi:type="string">paypal_express_order_line_items_mark_require_billing_address</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="grandTotal/base" xsi:type="string">215</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="transactionType" xsi:type="string">Authorization</data>
            <data name="ipn" xsi:type="string">-</data>
            <data name="orderButtonsAvailable" xsi:type="string">Back, Edit, Cancel, Send Email, Void, Hold, Invoice, Ship</data>
            <data name="paymentAction" xsi:type="string">Authorized</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderButtonsAvailable" />
            <constraint name="Mage\Paypal\Test\Constraint\AssertTransaction" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" />
            <constraint name="Mage\Checkout\Test\Constraint\AssertOrderShippingAddressSameAsPaypalBilling" />
        </variation>
        <variation name="TestCreationForExpressCheckoutWithinPayPalButtonTestVariation2" method="test">
            <data name="products" xsi:type="string">catalogProductVirtual::order_default,catalogProductSimple::order_default</data>
            <data name="salesRule" xsi:type="string">active_sales_rule_for_all_groups</data>
            <data name="configData" xsi:type="string">payflow_express_authorization_line_items</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="grandTotal" xsi:type="string">105</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="transactionType" xsi:type="string">Authorization</data>
            <data name="ipn" xsi:type="string">-</data>
            <data name="orderButtonsAvailable" xsi:type="string">Back, Edit, Cancel, Send Email, Void, Hold, Invoice, Ship</data>
            <data name="paymentAction" xsi:type="string">Authorized</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderButtonsAvailable" />
            <constraint name="Mage\Paypal\Test\Constraint\AssertTransaction" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" />
            <constraint name="Mage\Checkout\Test\Constraint\AssertOrderShippingAddressSameAsPaypalBilling" />
        </variation>
        <variation name="TestCreationForExpressCheckoutWithinPayPalButtonTestVariation3" method="test">
            <data name="products" xsi:type="string">catalogProductVirtual::order_default_expensive</data>
            <data name="salesRule" xsi:type="string">active_sales_rule_for_all_groups</data>
            <data name="configData" xsi:type="string">payflow_express_authorization_specificcountry_us</data>
            <data name="shippingMethod" xsi:type="string">-</data>
            <data name="grandTotal/base" xsi:type="string">500</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="transactionType" xsi:type="string">Authorization</data>
            <data name="ipn" xsi:type="string">-</data>
            <data name="orderButtonsAvailable" xsi:type="string">Back, Edit, Cancel, Send Email, Void, Hold, Invoice</data>
            <data name="paymentAction" xsi:type="string">Authorized</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderButtonsAvailable" />
            <constraint name="Mage\Paypal\Test\Constraint\AssertTransaction" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" />
        </variation>
        <variation name="TestCreationForExpressCheckoutWithinPayPalButtonTestVariation4" method="test">
            <data name="products" xsi:type="string">catalogProductSimple::order_default</data>
            <data name="salesRule" xsi:type="string">-</data>
            <data name="configData" xsi:type="string">payflow_express_sale</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="grandTotal" xsi:type="string">105</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="transactionType" xsi:type="string">Capture</data>
            <data name="ipn" xsi:type="string">Completed</data>
            <data name="orderButtonsAvailable" xsi:type="string">Back, Edit, Send Email, Hold, Ship</data>
            <data name="paymentAction" xsi:type="string">Captured</data>
            <data name="verifyData/grandTotal/invoices/0/from" xsi:type="string">105</data>
            <data name="verifyData/grandTotal/invoices/0/to" xsi:type="string">105</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderButtonsAvailable" />
            <constraint name="Mage\Paypal\Test\Constraint\AssertTransaction" />
            <constraint name="Mage\Sales\Test\Constraint\AssertInvoiceInInvoicesTab" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" />
            <constraint name="Mage\Checkout\Test\Constraint\AssertOrderBillingAddressSameAsPaypalShipping" />
        </variation>
        <variation name="TestCreationForExpressCheckoutWithinPayPalButtonTestVariation5" method="test">
            <data name="products" xsi:type="string">catalogProductVirtual::order_default,catalogProductSimple::order_default</data>
            <data name="salesRule" xsi:type="string">-</data>
            <data name="configData" xsi:type="string">payflow_express_sale_specificcountry_gb_line_items</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="grandTotal/base" xsi:type="string">205</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="transactionType" xsi:type="string">Authorization</data>
            <data name="ipn" xsi:type="string">Completed</data>
            <data name="orderButtonsAvailable" xsi:type="string">Back, Edit, Cancel, Send Email, Void, Hold, Invoice, Ship</data>
            <data name="paymentAction" xsi:type="string">Authorized</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderButtonsAvailable" />
            <constraint name="Mage\Paypal\Test\Constraint\AssertTransaction" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" />
            <constraint name="Mage\Checkout\Test\Constraint\AssertOrderBillingAddressSameAsPaypalShipping" />
        </variation>
        <variation name="TestCreationForExpressCheckoutWithinPayPalButtonTestVariation6" method="test">
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="salesRule" xsi:type="string">-</data>
            <data name="configData" xsi:type="string">paypal_express_sale_specificcountry_us_shipping_options_mark</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="grandTotal/base" xsi:type="string">315</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="transactionType" xsi:type="string">Authorization</data>
            <data name="ipn" xsi:type="string">Completed</data>
            <data name="orderButtonsAvailable" xsi:type="string">Back, Edit, Cancel, Send Email, Void, Hold, Invoice, Ship</data>
            <data name="paymentAction" xsi:type="string">Authorized</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderButtonsAvailable" />
            <constraint name="Mage\Paypal\Test\Constraint\AssertTransaction" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" />
            <constraint name="Mage\Checkout\Test\Constraint\AssertOrderBillingAddressSameAsPaypalShipping" />
        </variation>
        <variation name="TestCreationForExpressCheckoutWithinPayPalButtonTestVariation7" method="test">
            <data name="products" xsi:type="string">catalogProductVirtual::order_default_expensive</data>
            <data name="salesRule" xsi:type="string">active_sales_rule_for_all_groups</data>
            <data name="configData" xsi:type="string">paypal_express_sale_specificcountry_us_shipping_options_mark</data>
            <data name="shippingMethod" xsi:type="string">-</data>
            <data name="grandTotal/base" xsi:type="string">500</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="transactionType" xsi:type="string">Authorization</data>
            <data name="ipn" xsi:type="string">Completed</data>
            <data name="orderButtonsAvailable" xsi:type="string">Back, Edit, Cancel, Send Email, Void, Hold, Invoice</data>
            <data name="paymentAction" xsi:type="string">Authorized</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderButtonsAvailable" />
            <constraint name="Mage\Paypal\Test\Constraint\AssertTransaction" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" />
        </variation>
        <variation name="TestCreationForExpressCheckoutWithinPayPalButtonTestVariation8" method="test">
            <data name="description" xsi:type="string">With two product and taxes</data>
            <data name="products" xsi:type="string">catalogProductSimple::default,configurableProduct::default</data>
            <data name="taxRule" xsi:type="string">for_all_states</data>
            <data name="configData" xsi:type="string">paypal_express_sale_specificcountry_us_shipping_options_mark, tax_calculation_base_on_shipping_origin</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="grandTotal" xsi:type="string">696.15</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="transactionType" xsi:type="string">Authorization</data>
            <data name="ipn" xsi:type="string">Completed</data>
            <data name="orderButtonsAvailable" xsi:type="string">Back, Edit, Cancel, Send Email, Void, Hold, Invoice, Ship</data>
            <data name="paymentAction" xsi:type="string">Authorized</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderButtonsAvailable" />
            <constraint name="Mage\Paypal\Test\Constraint\AssertTransaction" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" />
            <constraint name="Mage\Checkout\Test\Constraint\AssertOrderBillingAddressSameAsPaypalShipping" />
        </variation>
    </testCase>
</config>
