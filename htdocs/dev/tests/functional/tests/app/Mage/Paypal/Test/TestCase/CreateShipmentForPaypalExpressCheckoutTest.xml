<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Mage\Paypal\Test\TestCase\CreateShipmentForPaypalExpressCheckoutTest" summary="CreateShipmentForPaypalExpressCheckoutTest">
        <variation name="CreateShipmentForPaypalExpressCheckoutTestVariation1" method="test">
            <data name="description" xsi:type="string">Create full shipment for paypal express checkout with line items mark require billing address</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="configData" xsi:type="string">paypal_express_order_line_items_mark_require_billing_address</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="data/shipment/0/form_data/comment_text" xsi:type="string">comments for shipment</data>
            <data name="data/shipment/0/form_data/tracking/0/carrier_code" xsi:type="string">Custom Value</data>
            <data name="data/shipment/0/form_data/tracking/0/title" xsi:type="string">title</data>
            <data name="data/shipment/0/form_data/tracking/0/number" xsi:type="string">199</data>
            <data name="verifyData/totalQtyOrdered/shipments/0/total_qty_from" xsi:type="string">3</data>
            <data name="verifyData/totalQtyOrdered/shipments/0/total_qty_to" xsi:type="string">3</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentSuccessCreateMessage" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentInShipmentsTab" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentInShipmentsGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentItems" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertNoShipButton" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentItemsOnFrontend" />
        </variation>
        <variation name="CreateShipmentForPaypalExpressCheckoutTestVariation2" method="test">
            <data name="description" xsi:type="string">Create partial shipment for payflow express authorization line items</data>
            <data name="products" xsi:type="string">catalogProductSimple::order_default</data>
            <data name="configData" xsi:type="string">payflow_express_authorization_line_items</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="data/shipment/0/items_data/0/qty" xsi:type="string">1</data>
            <data name="data/shipment/0/form_data/comment_text" xsi:type="string">comments for shipment</data>
            <data name="verifyData/items_data/0/qty" xsi:type="string">1</data>
            <data name="verifyData/totalQtyOrdered/shipments/0/total_qty_from" xsi:type="string">1</data>
            <data name="verifyData/totalQtyOrdered/shipments/0/total_qty_to" xsi:type="string">1</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentSuccessCreateMessage" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentInShipmentsTab" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentInShipmentsGrid" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentItems" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
        <variation name="CreateShipmentForPaypalExpressCheckoutTestVariation3" method="test">
            <data name="description" xsi:type="string">Create full shipment for payflow express authorization specific country us</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="configData" xsi:type="string">payflow_express_authorization_specificcountry_us</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="data/shipment/0/form_data/comment_text" xsi:type="string">comments for shipment</data>
            <data name="verifyData/totalQtyOrdered/shipments/0/total_qty_from" xsi:type="string">3</data>
            <data name="verifyData/totalQtyOrdered/shipments/0/total_qty_to" xsi:type="string">3</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentSuccessCreateMessage" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentInShipmentsTab" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentInShipmentsGrid" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentItems" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertNoShipButton" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
        <variation name="CreateShipmentForPaypalExpressCheckoutTestVariation4" method="test">
            <data name="description" xsi:type="string">Create full shipment for payflow express sale</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="configData" xsi:type="string">payflow_express_sale</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="data/shipment/0/form_data/comment_text" xsi:type="string">comments for shipment</data>
            <data name="verifyData/totalQtyOrdered/shipments/0/total_qty_from" xsi:type="string">3</data>
            <data name="verifyData/totalQtyOrdered/shipments/0/total_qty_to" xsi:type="string">3</data>
            <data name="status" xsi:type="string">Complete</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentSuccessCreateMessage" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentInShipmentsTab" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentInShipmentsGrid" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentItems" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertNoShipButton" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
        <variation name="CreateShipmentForPaypalExpressCheckoutTestVariation5" method="test">
            <data name="description" xsi:type="string">Create full shipment for payflow express sale specific country gb line items</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="configData" xsi:type="string">payflow_express_sale_specificcountry_gb_line_items</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="data/shipment/0/form_data/comment_text" xsi:type="string">comments for shipment</data>
            <data name="verifyData/totalQtyOrdered/shipments/0/total_qty_from" xsi:type="string">3</data>
            <data name="verifyData/totalQtyOrdered/shipments/0/total_qty_to" xsi:type="string">3</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentSuccessCreateMessage" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentInShipmentsTab" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentInShipmentsGrid" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentItems" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertNoShipButton" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
        <variation name="CreateShipmentForPaypalExpressCheckoutTestVariation6" method="test">
            <data name="description" xsi:type="string">Create full shipment for paypal express sale specific country us shipping options mark</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="configData" xsi:type="string">paypal_express_sale_specificcountry_us_shipping_options_mark</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="data/shipment/0/form_data/comment_text" xsi:type="string">comments for shipment</data>
            <data name="verifyData/totalQtyOrdered/shipments/0/total_qty_from" xsi:type="string">3</data>
            <data name="verifyData/totalQtyOrdered/shipments/0/total_qty_to" xsi:type="string">3</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentSuccessCreateMessage" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentInShipmentsTab" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentInShipmentsGrid" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentItems" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertNoShipButton" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
        <variation name="CreateShipmentForPaypalExpressCheckoutTestVariation7" method="test">
            <data name="description" xsi:type="string">Create full shipment for paypal express sale specific country us shipping options mark</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="configData" xsi:type="string">paypal_express_sale_specificcountry_us_shipping_options_mark</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="data/shipment/0/form_data/comment_text" xsi:type="string">comments for shipment</data>
            <data name="verifyData/totalQtyOrdered/shipments/0/total_qty_from" xsi:type="string">3</data>
            <data name="verifyData/totalQtyOrdered/shipments/0/total_qty_to" xsi:type="string">3</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentSuccessCreateMessage" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentInShipmentsTab" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentInShipmentsGrid" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentItems" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertNoShipButton" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
        <variation name="CreateShipmentForPaypalExpressCheckoutTestVariation80" method="test">
            <data name="description" xsi:type="string">Create full shipment for paypal express sale specific country us shipping options mark with taxes and two products</data>
            <data name="products" xsi:type="string">catalogProductSimple::default,catalogProductSimple::default</data>
            <data name="taxRule" xsi:type="string">default</data>
            <data name="configData" xsi:type="string">paypal_express_sale_specificcountry_us_shipping_options_mark, tax_calculation_base_on_shipping_origin</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="data/shipment/0/form_data/comment_text" xsi:type="string">comments for shipment</data>
            <data name="verifyData/totalQtyOrdered/shipments/0/total_qty_from" xsi:type="string">6</data>
            <data name="verifyData/totalQtyOrdered/shipments/0/total_qty_to" xsi:type="string">6</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentSuccessCreateMessage" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentInShipmentsTab" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentInShipmentsGrid" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentItems" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertNoShipButton" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
        <variation name="CreateShipmentForPaypalExpressCheckoutTestVariation81" method="test">
            <data name="description" xsi:type="string">Create full shipment for paypal express sale specific country us shipping options mark with configurable product</data>
            <data name="products" xsi:type="string">configurableProduct::default</data>
            <data name="configData" xsi:type="string">paypal_express_sale_specificcountry_us_shipping_options_mark</data>
            <data name="shippingMethod" xsi:type="string">Flat Rate/Fixed</data>
            <data name="data/shipment/0/form_data/comment_text" xsi:type="string">comments for shipment</data>
            <data name="verifyData/totalQtyOrdered/shipments/0/total_qty_from" xsi:type="string">2</data>
            <data name="verifyData/totalQtyOrdered/shipments/0/total_qty_to" xsi:type="string">2</data>
            <data name="status" xsi:type="string">Processing</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentSuccessCreateMessage" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentInShipmentsTab" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentInShipmentsGrid" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentConfigurableItems" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertNoShipButton" />
            <constraint name="Mage\Shipping\Test\Constraint\AssertShipmentConfigurableItemsOnFrontend" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
        </variation>
    </testCase>
</config>
