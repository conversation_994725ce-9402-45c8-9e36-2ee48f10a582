<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Mage\Paypal\Test\TestCase\CreateOrderWithPayPalStandardTest" summary="CreateOrderWithPayPalStandardTest">
        <variation name="CreateOrderWithPayPalStandardTestVariation1" firstConstraint="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" method="test">
            <data name="description" xsi:type="string">Simple product one page checkout within Pay Pal standard payment method</data>
            <data name="products" xsi:type="string">catalogProductSimple::order_default</data>
            <data name="configData" xsi:type="string">paypal_standard</data>
            <data name="checkoutMethod" xsi:type="string">guest</data>
            <data name="billingAddress/dataset" xsi:type="string">customer_US</data>
            <data name="customer/dataset" xsi:type="string">default_frontend</data>
            <data name="customerPersist" xsi:type="string">no</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="payment/method" xsi:type="string">paypal_express</data>
            <data name="grandTotal" xsi:type="string">113.25</data>
            <data name="tag" xsi:type="string">payment_method:paypal</data>
            <data name="tag" xsi:type="string">paypal:standard</data>
            <constraint name="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" next="Mage\Sales\Test\Constraint\AssertOrderGrandTotal"/>
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" prev="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage"/>
        </variation>
        <variation name="CreateOrderWithPayPalStandardTestVariation2" firstConstraint="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" method="test">
            <data name="description" xsi:type="string">Bundle product one page checkout within Pay Pal standard payment method</data>
            <data name="products" xsi:type="string">bundleProduct::bundle_fixed_product</data>
            <data name="configData" xsi:type="string">paypal_standard</data>
            <data name="checkoutMethod" xsi:type="string">guest</data>
            <data name="billingAddress/dataset" xsi:type="string">customer_US</data>
            <data name="customer/dataset" xsi:type="string">default_frontend</data>
            <data name="customerPersist" xsi:type="string">no</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="payment/method" xsi:type="string">paypal_express</data>
            <data name="grandTotal" xsi:type="string">836.36</data>
            <data name="tag" xsi:type="string">payment_method:paypal, paypal:standard</data>
            <constraint name="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" next="Mage\Sales\Test\Constraint\AssertOrderGrandTotal"/>
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" prev="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage"/>
        </variation>
        <variation name="CreateOrderWithPayPalStandardTestVariation3" firstConstraint="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" method="test">
            <data name="description" xsi:type="string">Grouped product one page checkout within Pay Pal standard payment method</data>
            <data name="products" xsi:type="string">groupedProduct::three_simple_products_without_category</data>
            <data name="configData" xsi:type="string">paypal_standard</data>
            <data name="checkoutMethod" xsi:type="string">guest</data>
            <data name="billingAddress/dataset" xsi:type="string">customer_US</data>
            <data name="customer/dataset" xsi:type="string">default_frontend</data>
            <data name="customerPersist" xsi:type="string">no</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="payment/method" xsi:type="string">paypal_express</data>
            <data name="grandTotal" xsi:type="string">663.00</data>
            <data name="tag" xsi:type="string">payment_method:paypal, paypal:standard</data>
            <constraint name="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" next="Mage\Sales\Test\Constraint\AssertOrderGrandTotal"/>
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" prev="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage"/>
        </variation>
    </testCase>
</config>
