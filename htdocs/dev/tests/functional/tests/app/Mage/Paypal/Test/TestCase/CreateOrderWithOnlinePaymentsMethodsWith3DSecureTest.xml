<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Mage\Paypal\Test\TestCase\CreateOrderWithOnlinePaymentsMethodsWith3DSecureTest" summary="CreateOrderWithOnlinePaymentsMethodsWith3DSecureTest">
        <variation name="CreateOrderWithOnlinePaymentsMethodsWith3DSecureTestVariation3" method="test">
            <data name="configData" xsi:type="string">paypal_payflow_pro_3d_secure, enable_3d_secure</data>
            <data name="products" xsi:type="string">catalogProductSimple::order_default</data>
            <data name="customer/dataset" xsi:type="string">default_frontend</data>
            <data name="checkoutMethod" xsi:type="string">guest</data>
            <data name="customerPersist" xsi:type="string">no</data>
            <data name="billingAddress/dataset" xsi:type="string">customer_US</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="payment/method" xsi:type="string">verisign</data>
            <data name="payment/cc" xsi:type="string">visa_3d_secure_positive</data>
            <data name="validationPassword/dataset" xsi:type="string">visa_3d_secure_positive</data>
            <data name="grandTotal" xsi:type="string">105.00</data>
            <data name="positiveCase" xsi:type="boolean">true</data>
            <data name="tag" xsi:type="string">payment_method:paypal, paypal:payflow_pro</data>
            <constraint name="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderInOrdersGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" />
        </variation>
        <variation name="CreateOrderWithOnlinePaymentsMethodsWith3DSecureTestVariation4" method="test">
            <data name="configData" xsi:type="string">paypal_payflow_pro_3d_secure, enable_3d_secure</data>
            <data name="products" xsi:type="string">catalogProductSimple::order_default</data>
            <data name="customer/dataset" xsi:type="string">default_frontend</data>
            <data name="checkoutMethod" xsi:type="string">guest</data>
            <data name="customerPersist" xsi:type="string">no</data>
            <data name="billingAddress/dataset" xsi:type="string">customer_US</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="payment/method" xsi:type="string">verisign</data>
            <data name="payment/cc" xsi:type="string">visa_3d_secure_negative</data>
            <data name="validationPassword/dataset" xsi:type="string">visa_3d_secure_positive</data>
            <data name="grandTotal" xsi:type="string">105.00</data>
            <data name="positiveCase" xsi:type="boolean">false</data>
            <data name="tag" xsi:type="string">payment_method:paypal, paypal:payflow_pro</data>
            <constraint name="Mage\Payment\Test\Constraint\Assert3DSecureVerificationFailed" />
        </variation>
        <variation name="CreateOrderWithOnlinePaymentsMethodsWith3DSecureTestVariation5" method="test">
            <data name="configData" xsi:type="string">paypal_payments_pro_3d_secure, enable_3d_secure</data>
            <data name="products" xsi:type="string">catalogProductSimple::order_default</data>
            <data name="customer/dataset" xsi:type="string">default_frontend</data>
            <data name="checkoutMethod" xsi:type="string">guest</data>
            <data name="customerPersist" xsi:type="string">no</data>
            <data name="billingAddress/dataset" xsi:type="string">customer_US</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="payment/method" xsi:type="string">paypal_direct</data>
            <data name="payment/cc" xsi:type="string">visa_3d_secure_positive_without_pass</data>
            <data name="validationPassword/dataset" xsi:type="string">visa_3d_secure_positive</data>
            <data name="grandTotal" xsi:type="string">105.00</data>
            <data name="positiveCase" xsi:type="boolean">true</data>
            <data name="tag" xsi:type="string">payment_method:paypal, paypal:payments_pro</data>
            <constraint name="Mage\Checkout\Test\Constraint\AssertOrderSuccessPlacedMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderInOrdersGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" />
        </variation>
        <variation name="CreateOrderWithOnlinePaymentsMethodsWith3DSecureTestVariation6" method="test">
            <data name="configData" xsi:type="string">paypal_payments_pro_3d_secure, enable_3d_secure</data>
            <data name="products" xsi:type="string">catalogProductSimple::order_default</data>
            <data name="customer/dataset" xsi:type="string">default_frontend</data>
            <data name="checkoutMethod" xsi:type="string">guest</data>
            <data name="customerPersist" xsi:type="string">no</data>
            <data name="billingAddress/dataset" xsi:type="string">customer_US</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="payment/method" xsi:type="string">paypal_direct</data>
            <data name="payment/cc" xsi:type="string">visa_3d_secure_negative_without_pass</data>
            <data name="validationPassword/dataset" xsi:type="string">visa_3d_secure_positive</data>
            <data name="grandTotal" xsi:type="string">105.00</data>
            <data name="positiveCase" xsi:type="boolean">false</data>
            <data name="tag" xsi:type="string">payment_method:paypal, paypal:payments_pro</data>
            <constraint name="Mage\Payment\Test\Constraint\Assert3DSecureVerificationFailed" />
        </variation>
    </testCase>
</config>
