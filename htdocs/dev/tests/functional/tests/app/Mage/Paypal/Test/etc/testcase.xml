<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/Magento/Mtf/TestCase/etc/testcase.xsd">
    <scenario name="TestCreationForExpressCheckoutWithinPayPalButtonTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Mage_Core" next="createProducts"/>
        <step name="createProducts" module="Mage_Catalog" next="addProductsToTheCart"/>
        <step name="addProductsToTheCart" module="Mage_Checkout" next="checkoutWithPayPal"/>
        <step name="checkoutWithPayPal" module="Mage_Paypal" next="loginToPayPal"/>
        <step name="loginToPayPal" module="Mage_Paypal" next="continuePayPalCheckout"/>
        <step name="continuePayPalCheckout" module="Mage_Paypal" next="placeOrder"/>
        <step name="placeOrder" module="Mage_Paypal"/>
    </scenario>

    <scenario name="TestCreationForExpressCheckoutWithinPayPalButtonFromProductPageTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Mage_Core" next="createProducts"/>
        <step name="createProducts" module="Mage_Catalog" next="checkoutWithPayPalFromProductPage"/>
        <step name="checkoutWithPayPalFromProductPage" module="Mage_Paypal" next="loginToPayPal"/>
        <step name="loginToPayPal" module="Mage_Paypal" next="continuePayPalCheckout"/>
        <step name="continuePayPalCheckout" module="Mage_Paypal" next="placeOrder"/>
        <step name="placeOrder" module="Mage_Paypal"/>
    </scenario>

    <scenario name="CreateOrderWithPayPalStandardTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Mage_Core" next="createProducts"/>
        <step name="createProducts" module="Mage_Catalog" next="addProductsToTheCart"/>
        <step name="addProductsToTheCart" module="Mage_Checkout" next="proceedToCheckout"/>
        <step name="proceedToCheckout" module="Mage_Checkout" next="selectCheckoutMethod"/>
        <step name="selectCheckoutMethod" module="Mage_Checkout" next="fillBillingInformation"/>
        <step name="fillBillingInformation" module="Mage_Checkout" next="fillShippingMethod"/>
        <step name="fillShippingMethod" module="Mage_Checkout" next="selectPaymentMethod"/>
        <step name="selectPaymentMethod" module="Mage_Checkout" next="loginToPayPal"/>
        <step name="loginToPayPal" module="Mage_Paypal" next="continuePayPalCheckout"/>
        <step name="continuePayPalCheckout" module="Mage_Paypal"/>
    </scenario>

    <scenario name="CreateOrderWithOnlinePaymentsMethodsWithoutIFrameTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Mage_Core" next="createProducts"/>
        <step name="createProducts" module="Mage_Catalog" next="addProductsToTheCart"/>
        <step name="addProductsToTheCart" module="Mage_Checkout" next="proceedToCheckout"/>
        <step name="proceedToCheckout" module="Mage_Checkout" next="createCustomer"/>
        <step name="createCustomer" module="Mage_Customer" next="selectCheckoutMethod"/>
        <step name="selectCheckoutMethod" module="Mage_Checkout" next="fillBillingInformation"/>
        <step name="fillBillingInformation" module="Mage_Checkout" next="fillShippingMethod"/>
        <step name="fillShippingMethod" module="Mage_Checkout" next="selectPaymentMethod"/>
        <step name="selectPaymentMethod" module="Mage_Checkout" next="placeOrder"/>
        <step name="placeOrder" module="Mage_Checkout"/>
    </scenario>

    <scenario name="CreateOrderWithOnlinePaymentsMethodsWithIFrameTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Mage_Core" next="createProducts"/>
        <step name="createProducts" module="Mage_Catalog" next="addProductsToTheCart"/>
        <step name="addProductsToTheCart" module="Mage_Checkout" next="proceedToCheckout"/>
        <step name="proceedToCheckout" module="Mage_Checkout" next="createCustomer"/>
        <step name="createCustomer" module="Mage_Customer" next="selectCheckoutMethod"/>
        <step name="selectCheckoutMethod" module="Mage_Checkout" next="fillBillingInformation"/>
        <step name="fillBillingInformation" module="Mage_Checkout" next="fillShippingMethod"/>
        <step name="fillShippingMethod" module="Mage_Checkout" next="selectPaymentMethod"/>
        <step name="selectPaymentMethod" module="Mage_Checkout" next="fillCreditCardInIFrame"/>
        <step name="fillCreditCardInIFrame" module="Mage_Paypal"/>
    </scenario>

    <scenario name="CreateShipmentForPaypalExpressCheckoutTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Mage_Core" next="createProducts"/>
        <step name="createProducts" module="Mage_Catalog" next="loginCustomerOnFrontend"/>
        <step name="loginCustomerOnFrontend" module="Mage_Customer" next="addProductsToTheCart"/>
        <step name="addProductsToTheCart" module="Mage_Checkout" next="checkoutWithPayPal"/>
        <step name="checkoutWithPayPal" module="Mage_Paypal" next="loginToPayPal"/>
        <step name="loginToPayPal" module="Mage_Paypal" next="continuePayPalCheckout"/>
        <step name="continuePayPalCheckout" module="Mage_Paypal" next="placeOrder"/>
        <step name="placeOrder" module="Mage_Paypal" next="createShipment"/>
        <step name="createShipment" module="Mage_Shipping"/>
    </scenario>

    <scenario name="CreateOrderWithOnlinePaymentsMethodsWith3DSecureTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Mage_Core" next="createProducts"/>
        <step name="createProducts" module="Mage_Catalog" next="addProductsToTheCart"/>
        <step name="addProductsToTheCart" module="Mage_Checkout" next="proceedToCheckout"/>
        <step name="proceedToCheckout" module="Mage_Checkout" next="createCustomer"/>
        <step name="createCustomer" module="Mage_Customer" next="selectCheckoutMethod"/>
        <step name="selectCheckoutMethod" module="Mage_Checkout" next="fillBillingInformation"/>
        <step name="fillBillingInformation" module="Mage_Checkout" next="fillShippingMethod"/>
        <step name="fillShippingMethod" module="Mage_Checkout" next="selectPaymentMethod"/>
        <step name="selectPaymentMethod" module="Mage_Checkout" next="fill3DSecureCreditCardValidation"/>
        <step name="fill3DSecureCreditCardValidation" module="Mage_Payment" next="placeOrder"/>
        <step name="placeOrder" module="Mage_Checkout"/>
    </scenario>

    <scenario name="CreateShipmentForOnlinePaymentMethodsWithoutIFrameTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Mage_Core" next="createProducts"/>
        <step name="createProducts" module="Mage_Catalog" next="addProductsToTheCart"/>
        <step name="addProductsToTheCart" module="Mage_Checkout" next="proceedToCheckout"/>
        <step name="proceedToCheckout" module="Mage_Checkout" next="createCustomer"/>
        <step name="createCustomer" module="Mage_Customer" next="selectCheckoutMethod"/>
        <step name="selectCheckoutMethod" module="Mage_Checkout" next="fillBillingInformation"/>
        <step name="fillBillingInformation" module="Mage_Checkout" next="fillShippingMethod"/>
        <step name="fillShippingMethod" module="Mage_Checkout" next="selectPaymentMethod"/>
        <step name="selectPaymentMethod" module="Mage_Checkout" next="placeOrder"/>
        <step name="placeOrder" module="Mage_Checkout" next="createShipment"/>
        <step name="createShipment" module="Mage_Shipping"/>
    </scenario>

    <scenario name="CreateShipmentForOnlinePaymentMethodsWithIFrameTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Mage_Core" next="createProducts"/>
        <step name="createProducts" module="Mage_Catalog" next="addProductsToTheCart"/>
        <step name="addProductsToTheCart" module="Mage_Checkout" next="proceedToCheckout"/>
        <step name="proceedToCheckout" module="Mage_Checkout" next="createCustomer"/>
        <step name="createCustomer" module="Mage_Customer" next="selectCheckoutMethod"/>
        <step name="selectCheckoutMethod" module="Mage_Checkout" next="fillBillingInformation"/>
        <step name="fillBillingInformation" module="Mage_Checkout" next="fillShippingMethod"/>
        <step name="fillShippingMethod" module="Mage_Checkout" next="selectPaymentMethod"/>
        <step name="selectPaymentMethod" module="Mage_Checkout" next="fillCreditCardInIFrame"/>
        <step name="fillCreditCardInIFrame" module="Mage_Paypal" next="createShipment"/>
        <step name="createShipment" module="Mage_Shipping"/>
    </scenario>

    <scenario name="CreateInvoiceForPaypalExpressCheckoutTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Mage_Core" next="createProducts"/>
        <step name="createProducts" module="Mage_Catalog" next="loginCustomerOnFrontend"/>
        <step name="loginCustomerOnFrontend" module="Mage_Customer" next="addProductsToTheCart"/>
        <step name="addProductsToTheCart" module="Mage_Checkout" next="checkoutWithPayPal"/>
        <step name="checkoutWithPayPal" module="Mage_Paypal" next="loginToPayPal"/>
        <step name="loginToPayPal" module="Mage_Paypal" next="continuePayPalCheckout"/>
        <step name="continuePayPalCheckout" module="Mage_Paypal" next="placeOrder"/>
        <step name="placeOrder" module="Mage_Paypal" next="createInvoice"/>
        <step name="createInvoice" module="Mage_Sales"/>
    </scenario>

    <scenario name="CreateOnlineInvoiceForOnlinePaymentsMethodsWithoutIFrameTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Mage_Core" next="createProducts"/>
        <step name="createProducts" module="Mage_Catalog" next="addProductsToTheCart"/>
        <step name="addProductsToTheCart" module="Mage_Checkout" next="proceedToCheckout"/>
        <step name="proceedToCheckout" module="Mage_Checkout" next="createCustomer"/>
        <step name="createCustomer" module="Mage_Customer" next="selectCheckoutMethod"/>
        <step name="selectCheckoutMethod" module="Mage_Checkout" next="fillBillingInformation"/>
        <step name="fillBillingInformation" module="Mage_Checkout" next="fillShippingMethod"/>
        <step name="fillShippingMethod" module="Mage_Checkout" next="selectPaymentMethod"/>
        <step name="selectPaymentMethod" module="Mage_Checkout" next="placeOrder"/>
        <step name="placeOrder" module="Mage_Checkout" next="createInvoice"/>
        <step name="createInvoice" module="Mage_Sales"/>
    </scenario>

    <scenario name="CreateOnlineRefundForPaypalExpressCheckoutTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Mage_Core" next="createProducts"/>
        <step name="createProducts" module="Mage_Catalog" next="loginCustomerOnFrontend"/>
        <step name="loginCustomerOnFrontend" module="Mage_Customer" next="addProductsToTheCart"/>
        <step name="addProductsToTheCart" module="Mage_Checkout" next="checkoutWithPayPal"/>
        <step name="checkoutWithPayPal" module="Mage_Paypal" next="loginToPayPal"/>
        <step name="loginToPayPal" module="Mage_Paypal" next="continuePayPalCheckout"/>
        <step name="continuePayPalCheckout" module="Mage_Paypal" next="placeOrder"/>
        <step name="placeOrder" module="Mage_Paypal" next="createShipment"/>
        <step name="createShipment" module="Mage_Shipping" next="createInvoice"/>
        <step name="createInvoice" module="Mage_Sales" next="createOnlineRefund"/>
        <step name="createOnlineRefund" module="Mage_Sales"/>
    </scenario>

    <scenario name="CreateOnlineRefundForOnlinePaymentsMethodsWithoutIFrameTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Mage_Core" next="createProducts"/>
        <step name="createProducts" module="Mage_Catalog" next="addProductsToTheCart"/>
        <step name="addProductsToTheCart" module="Mage_Checkout" next="proceedToCheckout"/>
        <step name="proceedToCheckout" module="Mage_Checkout" next="createCustomer"/>
        <step name="createCustomer" module="Mage_Customer" next="selectCheckoutMethod"/>
        <step name="selectCheckoutMethod" module="Mage_Checkout" next="fillBillingInformation"/>
        <step name="fillBillingInformation" module="Mage_Checkout" next="fillShippingMethod"/>
        <step name="fillShippingMethod" module="Mage_Checkout" next="selectPaymentMethod"/>
        <step name="selectPaymentMethod" module="Mage_Checkout" next="placeOrder"/>
        <step name="placeOrder" module="Mage_Checkout" next="createShipment"/>
        <step name="createShipment" module="Mage_Shipping" next="createInvoice"/>
        <step name="createInvoice" module="Mage_Sales" next="createOnlineRefund"/>
        <step name="createOnlineRefund" module="Mage_Sales"/>
    </scenario>

    <scenario name="CreateOnlineInvoiceForOnlinePaymentsMethodsWithIFrameTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Mage_Core" next="createProducts"/>
        <step name="createProducts" module="Mage_Catalog" next="addProductsToTheCart"/>
        <step name="addProductsToTheCart" module="Mage_Checkout" next="proceedToCheckout"/>
        <step name="proceedToCheckout" module="Mage_Checkout" next="createCustomer"/>
        <step name="createCustomer" module="Mage_Customer" next="selectCheckoutMethod"/>
        <step name="selectCheckoutMethod" module="Mage_Checkout" next="fillBillingInformation"/>
        <step name="fillBillingInformation" module="Mage_Checkout" next="fillShippingMethod"/>
        <step name="fillShippingMethod" module="Mage_Checkout" next="selectPaymentMethod"/>
        <step name="selectPaymentMethod" module="Mage_Checkout" next="fillCreditCardInIFrame"/>
        <step name="fillCreditCardInIFrame" module="Mage_Paypal" next="createInvoice"/>
        <step name="createInvoice" module="Mage_Sales"/>
    </scenario>

    <scenario name="CreateOfflineInvoiceForOnlinePaymentsMethodsWithIFrameTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Mage_Core" next="createProducts"/>
        <step name="createProducts" module="Mage_Catalog" next="addProductsToTheCart"/>
        <step name="addProductsToTheCart" module="Mage_Checkout" next="proceedToCheckout"/>
        <step name="proceedToCheckout" module="Mage_Checkout" next="createCustomer"/>
        <step name="createCustomer" module="Mage_Customer" next="selectCheckoutMethod"/>
        <step name="selectCheckoutMethod" module="Mage_Checkout" next="fillBillingInformation"/>
        <step name="fillBillingInformation" module="Mage_Checkout" next="fillShippingMethod"/>
        <step name="fillShippingMethod" module="Mage_Checkout" next="selectPaymentMethod"/>
        <step name="selectPaymentMethod" module="Mage_Checkout" next="fillCreditCardInIFrame"/>
        <step name="fillCreditCardInIFrame" module="Mage_Paypal" next="createInvoice"/>
        <step name="createInvoice" module="Mage_Sales"/>
    </scenario>

    <scenario name="CreateOfflineInvoiceForOnlinePaymentsMethodsWithoutIFrameTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Mage_Core" next="createProducts"/>
        <step name="createProducts" module="Mage_Catalog" next="addProductsToTheCart"/>
        <step name="addProductsToTheCart" module="Mage_Checkout" next="proceedToCheckout"/>
        <step name="proceedToCheckout" module="Mage_Checkout" next="createCustomer"/>
        <step name="createCustomer" module="Mage_Customer" next="selectCheckoutMethod"/>
        <step name="selectCheckoutMethod" module="Mage_Checkout" next="fillBillingInformation"/>
        <step name="fillBillingInformation" module="Mage_Checkout" next="fillShippingMethod"/>
        <step name="fillShippingMethod" module="Mage_Checkout" next="selectPaymentMethod"/>
        <step name="selectPaymentMethod" module="Mage_Checkout" next="placeOrder"/>
        <step name="placeOrder" module="Mage_Checkout" next="createInvoice"/>
        <step name="createInvoice" module="Mage_Sales"/>
    </scenario>

    <scenario name="CreateOnlineRefundForOnlinePaymentsMethodsWithIFrameTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Mage_Core" next="createProducts"/>
        <step name="createProducts" module="Mage_Catalog" next="addProductsToTheCart"/>
        <step name="addProductsToTheCart" module="Mage_Checkout" next="proceedToCheckout"/>
        <step name="proceedToCheckout" module="Mage_Checkout" next="createCustomer"/>
        <step name="createCustomer" module="Mage_Customer" next="selectCheckoutMethod"/>
        <step name="selectCheckoutMethod" module="Mage_Checkout" next="fillBillingInformation"/>
        <step name="fillBillingInformation" module="Mage_Checkout" next="fillShippingMethod"/>
        <step name="fillShippingMethod" module="Mage_Checkout" next="selectPaymentMethod"/>
        <step name="selectPaymentMethod" module="Mage_Checkout" next="fillCreditCardInIFrame"/>
        <step name="fillCreditCardInIFrame" module="Mage_Paypal" next="createShipment"/>
        <step name="createShipment" module="Mage_Shipping" next="createInvoice"/>
        <step name="createInvoice" module="Mage_Sales" next="createOnlineRefund"/>
        <step name="createOnlineRefund" module="Mage_Sales"/>
    </scenario>
</config>
