<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/fixture.xsd">
    <fixture name="rating" module="Mage_Rating" type="flat" entity_type="rating" collection="Mage\Rating\Model\Resource\Rating\Collection" identifier="rating_code" repository_class="Mage\Rating\Test\Repository\Rating" handler_interface="Mage\Rating\Test\Handler\RatingInterface" class="Mage\Rating\Test\Fixture\Rating">
        <field name="rating_id" is_required="1"/>
        <field name="entity_id" is_required="0"/>
        <field name="rating_code" is_required="0" group="rating_information"/>
        <field name="position" is_required="0" group="rating_information"/>
        <field name="stores" group="rating_information" source="Mage\Rating\Test\Fixture\Rating\Stores" />
        <field name="options" group="rating_information"/>
    </fixture>
</config>
