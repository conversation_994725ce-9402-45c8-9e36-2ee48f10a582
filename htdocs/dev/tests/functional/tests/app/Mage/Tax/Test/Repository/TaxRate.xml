<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/Magento/Mtf/Repository/etc/repository.xsd">
    <repository class="Mage\Tax\Test\Repository\TaxRate">
        <dataset name="default">
            <field name="code" xsi:type="string">Tax Rate %isolation%</field>
            <field name="rate" xsi:type="string">10</field>
            <field name="tax_country_id" xsi:type="string">United States</field>
            <field name="tax_postcode" xsi:type="string">*</field>
            <field name="tax_region_id" xsi:type="string">California</field>
        </dataset>

        <dataset name="australia">
            <field name="code" xsi:type="string">TaxIdentifier%isolation%</field>
            <field name="tax_postcode" xsi:type="string">*</field>
            <field name="tax_country_id" xsi:type="string">Australia</field>
            <field name="rate" xsi:type="string">20</field>
        </dataset>

        <dataset name="for_all_states">
            <field name="code" xsi:type="string">TaxIdentifier%isolation%</field>
            <field name="tax_country_id" xsi:type="string">United States</field>
            <field name="tax_postcode" xsi:type="string">*</field>
            <field name="tax_region_id" xsi:type="string">*</field>
            <field name="rate" xsi:type="string">8.2500</field>
        </dataset>

        <dataset name="US-CA-*-Rate 1">
            <field name="tax_calculation_rate_id" xsi:type="string">1</field>
            <field name="tax_country_id" xsi:type="string">United States</field>
            <field name="tax_region_id" xsi:type="string">12</field>
            <field name="tax_postcode" xsi:type="string">*</field>
            <field name="code" xsi:type="string">US-CA-*-Rate 1</field>
            <field name="rate" xsi:type="string">8.2500</field>
            <field name="id" xsi:type="string">1</field>
            <field name="mtf_dataset_name" xsi:type="string">US-CA-*-Rate 1</field>
        </dataset>

        <dataset name="US-NY-*-Rate 1">
            <field name="tax_calculation_rate_id" xsi:type="string">2</field>
            <field name="tax_country_id" xsi:type="string">United States</field>
            <field name="tax_region_id" xsi:type="string">43</field>
            <field name="tax_postcode" xsi:type="string">*</field>
            <field name="code" xsi:type="string">US-NY-*-Rate 1</field>
            <field name="rate" xsi:type="string">8.3750</field>
            <field name="id" xsi:type="string">2</field>
            <field name="mtf_dataset_name" xsi:type="string">US-NY-*-Rate 1</field>
        </dataset>

        <dataset name="withZipRange">
            <field name="code" xsi:type="string">TaxIdentifier%isolation%</field>
            <field name="zip_is_range" xsi:type="string">Yes</field>
            <field name="zip_from" xsi:type="string">90001</field>
            <field name="zip_to" xsi:type="string">96162</field>
            <field name="tax_country_id" xsi:type="string">United States</field>
            <field name="tax_region_id" xsi:type="string">California</field>
            <field name="rate" xsi:type="string">15.5</field>
        </dataset>

        <dataset name="tx_rate_10">
            <field name="code" xsi:type="string">TaxIdentifierTexas%isolation%</field>
            <field name="tax_postcode" xsi:type="string">*</field>
            <field name="tax_country_id" xsi:type="string">United States</field>
            <field name="tax_region_id" xsi:type="string">Texas</field>
            <field name="rate" xsi:type="string">10</field>
        </dataset>

        <dataset name="ny_rate_20">
            <field name="code" xsi:type="string">TaxIdentifierNewYork%isolation%</field>
            <field name="tax_postcode" xsi:type="string">*</field>
            <field name="tax_country_id" xsi:type="string">United States</field>
            <field name="tax_region_id" xsi:type="string">New York</field>
            <field name="rate" xsi:type="string">20</field>
        </dataset>

        <dataset name="ca_rate_30">
            <field name="code" xsi:type="string">TaxIdentifierCalifornia%isolation%</field>
            <field name="tax_postcode" xsi:type="string">*</field>
            <field name="tax_country_id" xsi:type="string">United States</field>
            <field name="tax_region_id" xsi:type="string">California</field>
            <field name="rate" xsi:type="string">30</field>
        </dataset>

        <dataset name="us_ca_rate_8_25">
            <field name="code" xsi:type="string">Tax Rate %isolation%</field>
            <field name="rate" xsi:type="string">8.25</field>
            <field name="tax_country_id" xsi:type="string">United States</field>
            <field name="tax_postcode" xsi:type="string">90230</field>
            <field name="tax_region_id" xsi:type="string">California</field>
        </dataset>

        <dataset name="us_ca_rate_8_25_no_zip">
            <field name="code" xsi:type="string">Tax Rate %isolation%</field>
            <field name="rate" xsi:type="string">8.25</field>
            <field name="tax_country_id" xsi:type="string">United States</field>
            <field name="tax_postcode" xsi:type="string">*</field>
            <field name="tax_region_id" xsi:type="string">California</field>
        </dataset>

        <dataset name="us_ca_rate_8_375">
            <field name="code" xsi:type="string">Tax Rate %isolation%</field>
            <field name="rate" xsi:type="string">8.375</field>
            <field name="tax_country_id" xsi:type="string">United States</field>
            <field name="tax_postcode" xsi:type="string">*</field>
            <field name="tax_region_id" xsi:type="string">California</field>
        </dataset>

        <dataset name="us_ny_rate_8_375">
            <field name="code" xsi:type="string">Tax Rate %isolation%</field>
            <field name="rate" xsi:type="string">8.375</field>
            <field name="tax_country_id" xsi:type="string">United States</field>
            <field name="tax_region_id" xsi:type="string">New York</field>
            <field name="tax_postcode" xsi:type="string">*</field>
        </dataset>

        <dataset name="us_ny_rate_8_25">
            <field name="code" xsi:type="string">Tax Rate %isolation%</field>
            <field name="rate" xsi:type="string">8.25</field>
            <field name="tax_country_id" xsi:type="string">United States</field>
            <field name="tax_region_id" xsi:type="string">New York</field>
            <field name="tax_postcode" xsi:type="string">*</field>
        </dataset>

        <dataset name="US-CA-Rate_1">
            <field name="tax_calculation_rate_id" xsi:type="string">1</field>
            <field name="tax_country_id" xsi:type="string">United States</field>
            <field name="tax_region_id" xsi:type="string">California</field>
            <field name="tax_postcode" xsi:type="string">*</field>
            <field name="code" xsi:type="string">US-CA-*-Rate 1</field>
            <field name="rate" xsi:type="string">8.2500</field>
            <field name="mtf_dataset_name" xsi:type="string">US-CA-*-Rate 1</field>
            <field name="id" xsi:type="string">1</field>
        </dataset>

        <dataset name="uk-full-tax">
            <field name="code" xsi:type="string">UK full tax %isolation%</field>
            <field name="rate" xsi:type="string">20</field>
            <field name="tax_country_id" xsi:type="string">United Kingdom</field>
            <field name="tax_postcode" xsi:type="string">*</field>
        </dataset>
    </repository>
</config>
