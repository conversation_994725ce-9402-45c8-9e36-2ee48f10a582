<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/Magento/Mtf/Repository/etc/repository.xsd">
    <repository class="Mage\Tax\Test\Repository\TaxRule">
        <dataset name="default">
            <field name="code" xsi:type="string">DefaultTaxRule%isolation%</field>
            <field name="tax_rate" xsi:type="array">
                <item name="dataset" xsi:type="array">
                    <item name="0" xsi:type="string">US-CA-*-Rate 1</item>
                    <item name="1" xsi:type="string">US-NY-*-Rate 1</item>
                </item>
            </field>
        </dataset>

        <dataset name="for_all_states">
            <field name="code" xsi:type="string">TaxIdentifier%isolation%</field>
            <field name="tax_rate" xsi:type="array">
                <item name="dataset" xsi:type="array">
                    <item name="0" xsi:type="string">for_all_states</item>
                </item>
            </field>
            <field name="priority" xsi:type="string">0</field>
        </dataset>

        <dataset name="cross_border_tax_rule">
            <field name="code" xsi:type="string">TaxIdentifier%isolation%</field>
            <field name="tax_rate" xsi:type="array">
                <item name="dataset" xsi:type="array">
                    <item name="0" xsi:type="string">tx_rate_10</item>
                    <item name="1" xsi:type="string">ny_rate_20</item>
                    <item name="2" xsi:type="string">ca_rate_30</item>
                </item>
            </field>
            <field name="priority" xsi:type="string">0</field>
            <field name="position" xsi:type="string">0</field>
        </dataset>

        <dataset name="customer_equals_store_rate">
            <field name="code" xsi:type="string">TaxIdentifier%isolation%</field>
            <field name="tax_rate" xsi:type="array">
                <item name="dataset" xsi:type="array">
                    <item name="0" xsi:type="string">us_ca_rate_8_25_no_zip</item>
                    <item name="1" xsi:type="string">us_ny_rate_8_25</item>
                </item>
            </field>
            <field name="priority" xsi:type="string">0</field>
            <field name="position" xsi:type="string">0</field>
        </dataset>

        <dataset name="customer_greater_store_rate">
            <field name="code" xsi:type="string">TaxIdentifier%isolation%</field>
            <field name="tax_rate" xsi:type="array">
                <item name="dataset" xsi:type="array">
                    <item name="0" xsi:type="string">us_ca_rate_8_25_no_zip</item>
                    <item name="1" xsi:type="string">us_ny_rate_8_375</item>
                </item>
            </field>
            <field name="priority" xsi:type="string">0</field>
            <field name="position" xsi:type="string">0</field>
        </dataset>

        <dataset name="customer_less_store_rate">
            <field name="code" xsi:type="string">TaxIdentifier%isolation%</field>
            <field name="tax_rate" xsi:type="array">
                <item name="dataset" xsi:type="array">
                    <item name="0" xsi:type="string">us_ca_rate_8_375</item>
                    <item name="1" xsi:type="string">us_ny_rate_8_25</item>
                </item>
            </field>
            <field name="priority" xsi:type="string">0</field>
            <field name="position" xsi:type="string">0</field>
        </dataset>

        <dataset name="us_tax_rule">
            <field name="code" xsi:type="string">TaxIdentifier%isolation%</field>
            <field name="tax_rate" xsi:type="array">
                <item name="dataset" xsi:type="array">
                    <item name="0" xsi:type="string">us_ca_rate_8_25</item>
                    <item name="1" xsi:type="string">us_ny_rate_8_375</item>
                </item>
            </field>
            <field name="priority" xsi:type="string">0</field>
            <field name="position" xsi:type="string">0</field>
        </dataset>

        <dataset name="tax_rule_default">
            <field name="code" xsi:type="string">TaxIdentifier%isolation%</field>
            <field name="tax_rate" xsi:type="array">
                <item name="dataset" xsi:type="array">
                    <item name="0" xsi:type="string">US-CA-Rate_1</item>
                </item>
            </field>
            <field name="tax_customer_class" xsi:type="array">
                <item name="dataset" xsi:type="array">
                    <item name="0" xsi:type="string">Retail Customer</item>
                </item>
            </field>
            <field name="tax_product_class" xsi:type="array">
                <item name="dataset" xsi:type="array">
                    <item name="0" xsi:type="string">Taxable Goods</item>
                </item>
            </field>
            <field name="priority" xsi:type="string">1</field>
            <field name="position" xsi:type="string">1</field>
        </dataset>

        <dataset name="uk_tax_rule">
            <field name="code" xsi:type="string">TaxIdentifier%isolation%</field>
            <field name="tax_rate" xsi:type="array">
                <item name="dataset" xsi:type="array">
                    <item name="0" xsi:type="string">uk-full-tax</item>
                </item>
            </field>
            <field name="priority" xsi:type="string">0</field>
            <field name="position" xsi:type="string">0</field>
        </dataset>
    </repository>
</config>
