<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Mage\Tax\Test\TestCase\CreateTaxRuleEntityTest" summary="CreateTaxRuleEntityTest">
        <variation name="CreateTaxRuleEntityTestVariation1" firstConstraint="Mage\Tax\Test\Constraint\AssertTaxRuleSuccessSaveMessage" method="test">
            <data name="taxRule/data/code" xsi:type="string">TaxIdentifier%isolation%</data>
            <data name="taxRule/data/tax_rate/dataset/rate_0" xsi:type="string">US-CA-*-Rate 1</data>
            <data name="taxRule/data/tax_rate/dataset/rate_1" xsi:type="string">US-NY-*-Rate 1</data>
            <data name="taxRule/data/tax_rate/dataset/rate_2" xsi:type="string">-</data>
            <data name="taxRule/data/tax_customer_class/dataset/class_0" xsi:type="string">customer_tax_class</data>
            <data name="taxRule/data/tax_customer_class/dataset/class_1" xsi:type="string">-</data>
            <data name="taxRule/data/tax_product_class/dataset/class_0" xsi:type="string">product_tax_class</data>
            <data name="taxRule/data/tax_product_class/dataset/class_1" xsi:type="string">-</data>
            <data name="taxRule/data/priority" xsi:type="string">1</data>
            <data name="taxRule/data/position" xsi:type="string">1</data>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRuleSuccessSaveMessage" next="Mage\Tax\Test\Constraint\AssertTaxRuleInGrid"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRuleInGrid" next="Mage\Tax\Test\Constraint\AssertTaxRuleForm" prev="Mage\Tax\Test\Constraint\AssertTaxRuleSuccessSaveMessage"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRuleForm" prev="Mage\Tax\Test\Constraint\AssertTaxRuleInGrid"/>
        </variation>
        <variation name="CreateTaxRuleEntityTestVariation2" firstConstraint="Mage\Tax\Test\Constraint\AssertTaxRuleSuccessSaveMessage" method="test">
            <data name="taxRule/data/code" xsi:type="string">TaxIdentifier%isolation%</data>
            <data name="taxRule/data/tax_rate/dataset/rate_0" xsi:type="string">default</data>
            <data name="taxRule/data/tax_rate/dataset/rate_1" xsi:type="string">-</data>
            <data name="taxRule/data/tax_rate/dataset/rate_2" xsi:type="string">-</data>
            <data name="taxRule/data/tax_customer_class/dataset/class_0" xsi:type="string">Retail Customer</data>
            <data name="taxRule/data/tax_customer_class/dataset/class_1" xsi:type="string">customer_tax_class</data>
            <data name="taxRule/data/tax_product_class/dataset/class_0" xsi:type="string">Taxable Goods</data>
            <data name="taxRule/data/tax_product_class/dataset/class_1" xsi:type="string">-</data>
            <data name="taxRule/data/priority" xsi:type="string">-</data>
            <data name="taxRule/data/position" xsi:type="string">1</data>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRuleSuccessSaveMessage" next="Mage\Tax\Test\Constraint\AssertTaxRuleInGrid"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRuleInGrid" next="Mage\Tax\Test\Constraint\AssertTaxRuleForm" prev="Mage\Tax\Test\Constraint\AssertTaxRuleSuccessSaveMessage"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRuleForm" prev="Mage\Tax\Test\Constraint\AssertTaxRuleInGrid"/>
        </variation>
        <variation name="CreateTaxRuleEntityTestVariation3" firstConstraint="Mage\Tax\Test\Constraint\AssertTaxRuleSuccessSaveMessage" method="test">
            <data name="taxRule/data/code" xsi:type="string">TaxIdentifier%isolation%</data>
            <data name="taxRule/data/tax_rate/dataset/rate_0" xsi:type="string">withZipRange</data>
            <data name="taxRule/data/tax_rate/dataset/rate_1" xsi:type="string">default</data>
            <data name="taxRule/data/tax_rate/dataset/rate_2" xsi:type="string">-</data>
            <data name="taxRule/data/tax_customer_class/dataset/class_0" xsi:type="string">Retail Customer</data>
            <data name="taxRule/data/tax_customer_class/dataset/class_1" xsi:type="string">customer_tax_class</data>
            <data name="taxRule/data/tax_product_class/dataset/class_0" xsi:type="string">Taxable Goods</data>
            <data name="taxRule/data/tax_product_class/dataset/class_1" xsi:type="string">product_tax_class</data>
            <data name="taxRule/data/priority" xsi:type="string">1</data>
            <data name="taxRule/data/position" xsi:type="string">-</data>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRuleSuccessSaveMessage" next="Mage\Tax\Test\Constraint\AssertTaxRuleInGrid"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRuleInGrid" next="Mage\Tax\Test\Constraint\AssertTaxRuleForm" prev="Mage\Tax\Test\Constraint\AssertTaxRuleSuccessSaveMessage"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRuleForm" prev="Mage\Tax\Test\Constraint\AssertTaxRuleInGrid"/>
        </variation>
    </testCase>
</config>
