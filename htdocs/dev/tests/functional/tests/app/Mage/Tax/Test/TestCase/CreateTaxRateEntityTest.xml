<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Mage\Tax\Test\TestCase\CreateTaxRateEntityTest" summary="CreateTaxRateEntityTest">
        <variation name="CreateTaxRateEntityTestVariation1" firstConstraint="Mage\Tax\Test\Constraint\AssertTaxRateSuccessSaveMessage" method="test">
            <data name="taxRate/data/code" xsi:type="string">TaxIdentifier%isolation%</data>
            <data name="taxRate/data/zip_is_range" xsi:type="string">No</data>
            <data name="taxRate/data/zip_from" xsi:type="string">-</data>
            <data name="taxRate/data/zip_to" xsi:type="string">-</data>
            <data name="taxRate/data/tax_postcode" xsi:type="string">*</data>
            <data name="taxRate/data/tax_country_id" xsi:type="string">Australia</data>
            <data name="taxRate/data/rate" xsi:type="string">20</data>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRateSuccessSaveMessage" next="Mage\Tax\Test\Constraint\AssertTaxRateInGrid"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRateInGrid" next="Mage\Tax\Test\Constraint\AssertTaxRateForm" prev="Mage\Tax\Test\Constraint\AssertTaxRateSuccessSaveMessage"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRateForm" next="Mage\Tax\Test\Constraint\AssertTaxRateInTaxRule" prev="Mage\Tax\Test\Constraint\AssertTaxRateInGrid"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRateInTaxRule" prev="Mage\Tax\Test\Constraint\AssertTaxRateForm"/>
        </variation>
        <variation name="CreateTaxRateEntityTestVariation2" firstConstraint="Mage\Tax\Test\Constraint\AssertTaxRateSuccessSaveMessage" method="test">
            <data name="taxRate/data/code" xsi:type="string">TaxIdentifier%isolation%</data>
            <data name="taxRate/data/zip_is_range" xsi:type="string">Yes</data>
            <data name="taxRate/data/zip_from" xsi:type="string">90001</data>
            <data name="taxRate/data/zip_to" xsi:type="string">96162</data>
            <data name="taxRate/data/tax_postcode" xsi:type="string">-</data>
            <data name="taxRate/data/tax_country_id" xsi:type="string">United States</data>
            <data name="taxRate/data/tax_region_id" xsi:type="string">California</data>
            <data name="taxRate/data/rate" xsi:type="string">15.5</data>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRateSuccessSaveMessage" next="Mage\Tax\Test\Constraint\AssertTaxRateInGrid"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRateInGrid" next="Mage\Tax\Test\Constraint\AssertTaxRateForm" prev="Mage\Tax\Test\Constraint\AssertTaxRateSuccessSaveMessage"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRateForm" next="Mage\Tax\Test\Constraint\AssertTaxRateInTaxRule" prev="Mage\Tax\Test\Constraint\AssertTaxRateInGrid"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRateInTaxRule" prev="Mage\Tax\Test\Constraint\AssertTaxRateForm"/>
        </variation>
        <variation name="CreateTaxRateEntityTestVariation3" firstConstraint="Mage\Tax\Test\Constraint\AssertTaxRateSuccessSaveMessage" method="test">
            <data name="taxRate/data/code" xsi:type="string">TaxIdentifier%isolation%</data>
            <data name="taxRate/data/zip_is_range" xsi:type="string">No</data>
            <data name="taxRate/data/zip_from" xsi:type="string">-</data>
            <data name="taxRate/data/zip_to" xsi:type="string">-</data>
            <data name="taxRate/data/tax_postcode" xsi:type="string">180</data>
            <data name="taxRate/data/tax_country_id" xsi:type="string">Canada</data>
            <data name="taxRate/data/tax_region_id" xsi:type="string">*</data>
            <data name="taxRate/data/rate" xsi:type="string">25</data>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRateSuccessSaveMessage" next="Mage\Tax\Test\Constraint\AssertTaxRateInGrid"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRateInGrid" next="Mage\Tax\Test\Constraint\AssertTaxRateForm" prev="Mage\Tax\Test\Constraint\AssertTaxRateSuccessSaveMessage"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRateForm" next="Mage\Tax\Test\Constraint\AssertTaxRateInTaxRule" prev="Mage\Tax\Test\Constraint\AssertTaxRateInGrid"/>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRateInTaxRule" prev="Mage\Tax\Test\Constraint\AssertTaxRateForm"/>
        </variation>
        <variation name="CreateTaxRateEntityTestVariation4" firstConstraint="Mage\Tax\Test\Constraint\AssertTaxRateIsInCorrectRange" method="test">
            <data name="taxRate/data/code" xsi:type="string">TaxIdentifier%isolation%</data>
            <data name="taxRate/data/zip_is_range" xsi:type="string">Yes</data>
            <data name="taxRate/data/zip_from" xsi:type="string">0</data>
            <data name="taxRate/data/zip_to" xsi:type="string">7800935</data>
            <data name="taxRate/data/tax_postcode" xsi:type="string">-</data>
            <data name="taxRate/data/tax_country_id" xsi:type="string">United Kingdom</data>
            <data name="taxRate/data/rate" xsi:type="string">7.75</data>
            <constraint name="Mage\Tax\Test\Constraint\AssertTaxRateIsInCorrectRange"/>
        </variation>
    </testCase>
</config>
