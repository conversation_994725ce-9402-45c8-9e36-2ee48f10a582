<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/Magento/Mtf/Repository/etc/repository.xsd">
    <repository class="Mage\Tax\Test\Repository\TaxClass">
        <dataset name="Taxable Goods">
            <field name="class_id" xsi:type="string">2</field>
            <field name="class_name" xsi:type="string">Taxable Goods</field>
            <field name="class_type" xsi:type="string">PRODUCT</field>
            <field name="id" xsi:type="string">2</field>
            <field name="mtf_dataset_name" xsi:type="string">Taxable Goods</field>
        </dataset>

        <dataset name="None">
            <field name="class_name" xsi:type="string">None</field>
            <field name="class_type" xsi:type="string">PRODUCT</field>
            <field name="id" xsi:type="string">0</field>
        </dataset>

        <dataset name="Retail Customer">
            <field name="class_id" xsi:type="string">3</field>
            <field name="class_name" xsi:type="string">Retail Customer</field>
            <field name="class_type" xsi:type="string">CUSTOMER</field>
            <field name="id" xsi:type="string">3</field>
            <field name="mtf_dataset_name" xsi:type="string">Retail Customer</field>
        </dataset>

        <dataset name="customer_tax_class">
            <field name="class_name" xsi:type="string">Customer Tax Class %isolation%</field>
            <field name="class_type" xsi:type="string">CUSTOMER</field>
        </dataset>

        <dataset name="product_tax_class">
            <field name="class_name" xsi:type="string">Product Tax Class %isolation%</field>
            <field name="class_type" xsi:type="string">PRODUCT</field>
        </dataset>
    </repository>
</config>
