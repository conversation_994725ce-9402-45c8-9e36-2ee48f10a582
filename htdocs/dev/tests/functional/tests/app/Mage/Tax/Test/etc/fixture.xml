<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<fixture>
    <taxClass module="Mage_Tax">
        <type>flat</type>
        <entity_type>tax_class</entity_type>
        <collection>Mage\Tax\Model\Resource\Class\Collection</collection>
        <fields>
            <id>
                <attribute_code>id</attribute_code>
                <backend_type>virtual</backend_type>
            </id>
        </fields>
    </taxClass>
    <taxRate module="Mage_Tax">
        <type>flat</type>
        <entity_type>tax_calculation_rate</entity_type>
        <collection>Mage\Tax\Model\Resource\Calculation\Rate\Collection</collection>
        <identifier>code</identifier>
        <fields>
            <id>
                <attribute_code>id</attribute_code>
                <backend_type>virtual</backend_type>
            </id>
        </fields>
    </taxRate>
    <taxRule module="Mage_Tax">
        <type>flat</type>
        <entity_type>tax_calculation_rule</entity_type>
        <collection>Mage\Tax\Model\Resource\Calculation\Rule\Collection</collection>
        <identifier>code</identifier>
        <fields>
            <tax_rate>
                <attribute_code>tax_rate</attribute_code>
                <backend_type>virtual</backend_type>
                <source>Mage\Tax\Test\Fixture\TaxRule\TaxRate</source>
            </tax_rate>
            <tax_customer_class>
                <attribute_code>tax_customer_class</attribute_code>
                <backend_type>virtual</backend_type>
                <source>Mage\Tax\Test\Fixture\TaxRule\TaxClass</source>
            </tax_customer_class>
            <tax_product_class>
                <attribute_code>tax_product_class</attribute_code>
                <backend_type>virtual</backend_type>
                <source>Mage\Tax\Test\Fixture\TaxRule\TaxClass</source>
            </tax_product_class>
        </fields>
    </taxRule>
</fixture>
