<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/fixture.xsd">
    <fixture name="order" module="Mage_Sales" type="flat" entity_type="sales_order"
             repository_class="Mage\Sales\Test\Repository\Order"
             handler_interface="Mage\Sales\Test\Handler\Order\OrderInterface" class="Mage\Sales\Test\Fixture\Order">
        <field name="entity_id" is_required="1" source="Mage\Sales\Test\Fixture\Order\EntityId" group="null"/>
        <field name="state" is_required="0"/>
        <field name="status" is_required="0"/>
        <field name="coupon_code" is_required="0" group="null"/>
        <field name="protect_code" is_required="0"/>
        <field name="shipping_description" is_required="0"/>
        <field name="is_virtual" is_required="0"/>
        <field name="store_id" is_required="0" source="Mage\Sales\Test\Fixture\Order\StoreId" group="null"/>
        <field name="customer_id" is_required="0" source="Mage\Sales\Test\Fixture\Order\CustomerId" group="null"/>
        <field name="base_discount_amount" is_required="0"/>
        <field name="base_discount_canceled" is_required="0"/>
        <field name="base_discount_invoiced" is_required="0"/>
        <field name="base_discount_refunded" is_required="0"/>
        <field name="base_grand_total" is_required="0"/>
        <field name="base_shipping_amount" is_required="0"/>
        <field name="base_shipping_canceled" is_required="0"/>
        <field name="base_shipping_invoiced" is_required="0"/>
        <field name="base_shipping_refunded" is_required="0"/>
        <field name="base_shipping_tax_amount" is_required="0"/>
        <field name="base_shipping_tax_refunded" is_required="0"/>
        <field name="base_subtotal" is_required="0"/>
        <field name="base_subtotal_canceled" is_required="0"/>
        <field name="base_subtotal_invoiced" is_required="0"/>
        <field name="base_subtotal_refunded" is_required="0"/>
        <field name="base_tax_amount" is_required="0"/>
        <field name="base_tax_canceled" is_required="0"/>
        <field name="base_tax_invoiced" is_required="0"/>
        <field name="base_tax_refunded" is_required="0"/>
        <field name="base_to_global_rate" is_required="0"/>
        <field name="base_to_order_rate" is_required="0"/>
        <field name="base_total_canceled" is_required="0"/>
        <field name="base_total_invoiced" is_required="0"/>
        <field name="base_total_invoiced_cost" is_required="0"/>
        <field name="base_total_offline_refunded" is_required="0"/>
        <field name="base_total_online_refunded" is_required="0"/>
        <field name="base_total_paid" is_required="0"/>
        <field name="base_total_qty_ordered" is_required="0"/>
        <field name="base_total_refunded" is_required="0"/>
        <field name="discount_amount" is_required="0"/>
        <field name="discount_canceled" is_required="0"/>
        <field name="discount_invoiced" is_required="0"/>
        <field name="discount_refunded" is_required="0"/>
        <field name="grand_total" is_required="0"/>
        <field name="shipping_amount" is_required="0"/>
        <field name="shipping_canceled" is_required="0"/>
        <field name="shipping_invoiced" is_required="0"/>
        <field name="shipping_refunded" is_required="0"/>
        <field name="shipping_tax_amount" is_required="0"/>
        <field name="shipping_tax_refunded" is_required="0"/>
        <field name="store_to_base_rate" is_required="0"/>
        <field name="store_to_order_rate" is_required="0"/>
        <field name="subtotal" is_required="0"/>
        <field name="subtotal_canceled" is_required="0"/>
        <field name="subtotal_invoiced" is_required="0"/>
        <field name="subtotal_refunded" is_required="0"/>
        <field name="tax_amount" is_required="0"/>
        <field name="tax_canceled" is_required="0"/>
        <field name="tax_invoiced" is_required="0"/>
        <field name="tax_refunded" is_required="0"/>
        <field name="total_canceled" is_required="0"/>
        <field name="total_invoiced" is_required="0"/>
        <field name="total_offline_refunded" is_required="0"/>
        <field name="total_online_refunded" is_required="0"/>
        <field name="total_paid" is_required="0"/>
        <field name="total_qty_ordered" is_required="0"/>
        <field name="total_refunded" is_required="0"/>
        <field name="can_ship_partially" is_required="0"/>
        <field name="can_ship_partially_item" is_required="0"/>
        <field name="customer_is_guest" is_required="0"/>
        <field name="customer_note_notify" is_required="0"/>
        <field name="billing_address_id" is_required="0" source="Mage\Sales\Test\Fixture\Order\BillingAddressId"/>
        <field name="customer_group_id" is_required="0"/>
        <field name="edit_increment" is_required="0"/>
        <field name="email_sent" is_required="0"/>
        <field name="forced_shipment_with_invoice" is_required="0"/>
        <field name="payment_auth_expiration" is_required="0"/>
        <field name="quote_address_id" is_required="0"/>
        <field name="quote_id" is_required="0"/>
        <field name="shipping_address_id" is_required="0"/>
        <field name="adjustment_negative" is_required="0"/>
        <field name="adjustment_positive" is_required="0"/>
        <field name="base_adjustment_negative" is_required="0"/>
        <field name="base_adjustment_positive" is_required="0"/>
        <field name="base_shipping_discount_amount" is_required="0"/>
        <field name="base_subtotal_incl_tax" is_required="0"/>
        <field name="base_total_due" is_required="0"/>
        <field name="payment_authorization_amount" is_required="0"/>
        <field name="shipping_discount_amount" is_required="0"/>
        <field name="subtotal_incl_tax" is_required="0"/>
        <field name="total_due" is_required="0"/>
        <field name="weight" is_required="0"/>
        <field name="customer_dob" is_required="0"/>
        <field name="increment_id" is_required="0"/>
        <field name="applied_rule_ids" is_required="0"/>
        <field name="base_currency_code" is_required="0"/>
        <field name="customer_email" is_required="0"/>
        <field name="customer_firstname" is_required="0"/>
        <field name="customer_lastname" is_required="0"/>
        <field name="customer_middlename" is_required="0"/>
        <field name="customer_prefix" is_required="0"/>
        <field name="customer_suffix" is_required="0"/>
        <field name="customer_taxvat" is_required="0"/>
        <field name="discount_description" is_required="0"/>
        <field name="ext_customer_id" is_required="0"/>
        <field name="ext_order_id" is_required="0"/>
        <field name="global_currency_code" is_required="0"/>
        <field name="hold_before_state" is_required="0"/>
        <field name="hold_before_status" is_required="0"/>
        <field name="order_currency_code" is_required="0"/>
        <field name="original_increment_id" is_required="0"/>
        <field name="relation_child_id" is_required="0"/>
        <field name="relation_child_real_id" is_required="0"/>
        <field name="relation_parent_id" is_required="0"/>
        <field name="relation_parent_real_id" is_required="0"/>
        <field name="remote_ip" is_required="0"/>
        <field name="shipping_method" is_required="0"/>
        <field name="store_currency_code" is_required="0"/>
        <field name="store_name" is_required="0"/>
        <field name="x_forwarded_for" is_required="0"/>
        <field name="customer_note" is_required="0"/>
        <field name="created_at" is_required="0"/>
        <field name="updated_at" is_required="0"/>
        <field name="total_item_count" is_required="0"/>
        <field name="customer_gender" is_required="0"/>
        <field name="hidden_tax_amount" is_required="0"/>
        <field name="base_hidden_tax_amount" is_required="0"/>
        <field name="shipping_hidden_tax_amount" is_required="0"/>
        <field name="base_shipping_hidden_tax_amnt" is_required="0"/>
        <field name="hidden_tax_invoiced" is_required="0"/>
        <field name="base_hidden_tax_invoiced" is_required="0"/>
        <field name="hidden_tax_refunded" is_required="0"/>
        <field name="base_hidden_tax_refunded" is_required="0"/>
        <field name="shipping_incl_tax" is_required="0"/>
        <field name="base_shipping_incl_tax" is_required="0"/>
        <field name="coupon_rule_name" is_required="0"/>
        <field name="base_customer_balance_amount" is_required="0"/>
        <field name="customer_balance_amount" is_required="0"/>
        <field name="base_customer_balance_invoiced" is_required="0"/>
        <field name="customer_balance_invoiced" is_required="0"/>
        <field name="base_customer_balance_refunded" is_required="0"/>
        <field name="customer_balance_refunded" is_required="0"/>
        <field name="bs_customer_bal_total_refunded" is_required="0"/>
        <field name="customer_bal_total_refunded" is_required="0"/>
        <field name="gift_cards" is_required="0"/>
        <field name="base_gift_cards_amount" is_required="0"/>
        <field name="gift_cards_amount" is_required="0"/>
        <field name="base_gift_cards_invoiced" is_required="0"/>
        <field name="gift_cards_invoiced" is_required="0"/>
        <field name="base_gift_cards_refunded" is_required="0"/>
        <field name="gift_cards_refunded" is_required="0"/>
        <field name="gift_message_id" is_required="0"/>
        <field name="gw_id" is_required="0"/>
        <field name="gw_allow_gift_receipt" is_required="0"/>
        <field name="gw_add_card" is_required="0"/>
        <field name="gw_base_price" is_required="0"/>
        <field name="gw_price" is_required="0"/>
        <field name="gw_items_base_price" is_required="0"/>
        <field name="gw_items_price" is_required="0"/>
        <field name="gw_card_base_price" is_required="0"/>
        <field name="gw_card_price" is_required="0"/>
        <field name="gw_base_tax_amount" is_required="0"/>
        <field name="gw_tax_amount" is_required="0"/>
        <field name="gw_items_base_tax_amount" is_required="0"/>
        <field name="gw_items_tax_amount" is_required="0"/>
        <field name="gw_card_base_tax_amount" is_required="0"/>
        <field name="gw_card_tax_amount" is_required="0"/>
        <field name="gw_base_price_invoiced" is_required="0"/>
        <field name="gw_price_invoiced" is_required="0"/>
        <field name="gw_items_base_price_invoiced" is_required="0"/>
        <field name="gw_items_price_invoiced" is_required="0"/>
        <field name="gw_card_base_price_invoiced" is_required="0"/>
        <field name="gw_card_price_invoiced" is_required="0"/>
        <field name="gw_base_tax_amount_invoiced" is_required="0"/>
        <field name="gw_tax_amount_invoiced" is_required="0"/>
        <field name="gw_items_base_tax_invoiced" is_required="0"/>
        <field name="gw_items_tax_invoiced" is_required="0"/>
        <field name="gw_card_base_tax_invoiced" is_required="0"/>
        <field name="gw_card_tax_invoiced" is_required="0"/>
        <field name="gw_base_price_refunded" is_required="0"/>
        <field name="gw_price_refunded" is_required="0"/>
        <field name="gw_items_base_price_refunded" is_required="0"/>
        <field name="gw_items_price_refunded" is_required="0"/>
        <field name="gw_card_base_price_refunded" is_required="0"/>
        <field name="gw_card_price_refunded" is_required="0"/>
        <field name="gw_base_tax_amount_refunded" is_required="0"/>
        <field name="gw_tax_amount_refunded" is_required="0"/>
        <field name="gw_items_base_tax_refunded" is_required="0"/>
        <field name="gw_items_tax_refunded" is_required="0"/>
        <field name="gw_card_base_tax_refunded" is_required="0"/>
        <field name="gw_card_tax_refunded" is_required="0"/>
        <field name="paypal_ipn_customer_notified" is_required="0"/>
        <field name="reward_points_balance" is_required="0"/>
        <field name="base_reward_currency_amount" is_required="0"/>
        <field name="reward_currency_amount" is_required="0"/>
        <field name="base_rwrd_crrncy_amt_invoiced" is_required="0"/>
        <field name="rwrd_currency_amount_invoiced" is_required="0"/>
        <field name="base_rwrd_crrncy_amnt_refnded" is_required="0"/>
        <field name="rwrd_crrncy_amnt_refunded" is_required="0"/>
        <field name="reward_points_balance_refund" is_required="0"/>
        <field name="reward_points_balance_refunded" is_required="0"/>
        <field name="reward_salesrule_points" is_required="0"/>
        <field name="id"/>
        <field name="price" is_required="1" group="null"/>
    </fixture>
</config>
