<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Mage\Sales\Test\TestCase\CreateOrderFromBackendWithinOfflinePaymentMethodsTest" summary="CreateOrderFromBackendWithinOfflinePaymentMethodsTest">
        <variation name="CreateOrderFromBackendWithinOfflinePaymentMethodsTestVariation1" method="test">
            <data name="description" xsi:type="string">Create backend order with simple product for existing customer.</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="customer/dataset" xsi:type="string">default</data>
            <data name="billingAddress/dataset" xsi:type="string">US_address_without_email</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="grandTotal" xsi:type="string">315.00</data>
            <data name="payment/method" xsi:type="string">checkmo</data>
            <data name="status" xsi:type="string">Pending</data>
            <data name="configData" xsi:type="string">-</data>
            <data name="customer/dataset" xsi:type="string">johndoe_unique</data>
            <data name="salesRule" xsi:type="string">-</data>
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderCreateSuccessMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderInOrdersGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderInOrdersGridOnFrontend" />
        </variation>
        <variation name="CreateOrderFromBackendWithinOfflinePaymentMethodsTestVariation2" method="test">
            <data name="description" xsi:type="string">Create backend order with simple product for existing customer.</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="customer/dataset" xsi:type="string">default</data>
            <data name="billingAddress/dataset" xsi:type="string">US_address_without_email</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="grandTotal/base" xsi:type="string">315.00</data>
            <data name="payment/method" xsi:type="string">checkmo</data>
            <data name="status" xsi:type="string">Pending</data>
            <data name="configData" xsi:type="string">-</data>
            <data name="customer/dataset" xsi:type="string">johndoe_unique</data>
            <data name="salesRule" xsi:type="string">-</data>
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderCreateSuccessMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderInOrdersGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderInOrdersGridOnFrontend" />
        </variation>
        <variation name="CreateOrderFromBackendWithinOfflinePaymentMethodsTestVariation3" method="test">
            <data name="description" xsi:type="string">Create backend order with simple product for existing customer and discount coupon.</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="customer/dataset" xsi:type="string">default</data>
            <data name="billingAddress/dataset" xsi:type="string">US_address_without_email</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="grandTotal/base" xsi:type="string">165.00</data>
            <data name="payment/method" xsi:type="string">checkmo</data>
            <data name="status" xsi:type="string">Pending</data>
            <data name="configData" xsi:type="string">-</data>
            <data name="customer/dataset" xsi:type="string">johndoe_unique</data>
            <data name="salesRule" xsi:type="string">active_sales_rule_for_all_groups</data>
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderCreateSuccessMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderInOrdersGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderInOrdersGridOnFrontend" />
        </variation>
        <variation name="CreateOrderFromBackendWithinOfflinePaymentMethodsTestVariation4" method="test">
            <data name="description" xsi:type="string">Create backend order with simple product for existing customer and apply discount coupon.</data>
            <data name="products" xsi:type="string">catalogProductSimple::default</data>
            <data name="customer/dataset" xsi:type="string">default</data>
            <data name="billingAddress/dataset" xsi:type="string">US_address_without_email</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="grandTotal/base" xsi:type="string">165.00</data>
            <data name="payment/method" xsi:type="string">checkmo</data>
            <data name="status" xsi:type="string">Pending</data>
            <data name="configData" xsi:type="string">-</data>
            <data name="customer/dataset" xsi:type="string">johndoe_unique</data>
            <data name="salesRule" xsi:type="string">active_sales_rule_for_all_groups</data>
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderCreateSuccessMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderInOrdersGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderInOrdersGridOnFrontend" />
        </variation>
    </testCase>
</config>
