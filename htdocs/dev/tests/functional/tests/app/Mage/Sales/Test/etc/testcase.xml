<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/Magento/Mtf/TestCase/etc/testcase.xsd">
    <scenario name="CreateOrderFromBackendWithinOfflinePaymentMethodsTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Mage_Core" next="createProducts" />
        <step name="createProducts" module="Mage_Catalog" next="createCustomer" />
        <step name="createCustomer" module="Mage_Customer" next="openSalesOrders" />
        <step name="openSalesOrders" module="Mage_Sales" next="createNewOrder" />
        <step name="createNewOrder" module="Mage_Sales" next="selectCustomerOrder" />
        <step name="selectCustomerOrder" module="Mage_Sales" next="selectStoreOnCreateOrder" />
        <step name="selectStoreOnCreateOrder" module="Mage_Sales" next="addProducts" />
        <step name="addProducts" module="Mage_Sales" next="updateProductsData" />
        <step name="updateProductsData" module="Mage_Sales" next="fillBillingAddress" />
        <step name="fillBillingAddress" module="Mage_Sales" next="selectPaymentMethodForOrder" />
        <step name="selectPaymentMethodForOrder" module="Mage_Sales" next="selectShippingMethodForOrder" />
        <step name="selectShippingMethodForOrder" module="Mage_Sales" next="submitOrder" />
        <step name="submitOrder" module="Mage_Sales" />
    </scenario>

    <scenario name="CreateOrderFromBackendCustomerPageTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Mage_Core" next="createProducts" />
        <step name="createProducts" module="Mage_Catalog" next="createCustomer" />
        <step name="createCustomer" module="Mage_Customer" next="openCustomerOnBackend" />
        <step name="openCustomerOnBackend" module="Mage_Customer" next="createOrderFromCustomerAccount" />
        <step name="createOrderFromCustomerAccount" module="Mage_Customer" next="selectStoreOnCreateOrder" />
        <step name="selectStoreOnCreateOrder" module="Mage_Sales" next="addProducts" />
        <step name="addProducts" module="Mage_Sales" next="updateProductsData" />
        <step name="updateProductsData" module="Mage_Sales" next="fillBillingAddress" />
        <step name="fillBillingAddress" module="Mage_Sales" next="selectPaymentMethodForOrder" />
        <step name="selectPaymentMethodForOrder" module="Mage_Sales" next="selectShippingMethodForOrder" />
        <step name="selectShippingMethodForOrder" module="Mage_Sales" next="submitOrder" />
        <step name="submitOrder" module="Mage_Sales" />
    </scenario>

    <scenario name="MoveShoppingCartProductsOnOrderPageTest" firstStep="createCustomer">
        <step name="createCustomer" module="Mage_Customer" next="createProducts" />
        <step name="createProducts" module="Mage_Catalog" next="loginCustomerOnFrontend" />
        <step name="loginCustomerOnFrontend" module="Mage_Customer" next="addProductsToTheCart" />
        <step name="addProductsToTheCart" module="Mage_Checkout" next="openCustomerOnBackend" />
        <step name="openCustomerOnBackend" module="Mage_Customer" next="createOrderFromCustomerAccount" />
        <step name="createOrderFromCustomerAccount" module="Mage_Customer" next="selectStoreOnCreateOrder" />
        <step name="selectStoreOnCreateOrder" module="Mage_Sales" next="moveProductsFromShoppingCartSidebar" />
        <step name="moveProductsFromShoppingCartSidebar" module="Mage_Sales" />
    </scenario>

    <scenario name="ReorderOrderEntityTest" firstStep="setupConfiguration">
        <step name="setupConfiguration" module="Mage_Core" next="createNewOrderViaCurl" />
        <step name="createNewOrderViaCurl" module="Mage_Sales" next="openOrder" />
        <step name="openOrder" module="Mage_Sales" next="reorderOrder" />
        <step name="reorderOrder" module="Mage_Sales" next="fillBillingAddress" />
        <step name="fillBillingAddress" module="Mage_Sales" next="selectPaymentMethodForOrder" />
        <step name="selectPaymentMethodForOrder" module="Mage_Sales" next="selectShippingMethodForOrder" />
        <step name="selectShippingMethodForOrder" module="Mage_Sales" next="submitOrder" />
        <step name="submitOrder" module="Mage_Sales" />
    </scenario>
</config>
