<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/etc/variations.xsd">
    <testCase name="Mage\Sales\Test\TestCase\ReorderOrderEntityTest" summary="ReorderOrderEntityTest">
        <variation name="ReorderOrderEntityTestVariation1">
            <data name="description" xsi:type="string">Reorder placed order (update products, billing address).</data>
            <data name="configData" xsi:type="string">default_tax_configuration</data>
            <data name="orderdataset" xsi:type="string">default</data>
            <data name="customer/dataset" xsi:type="string">customer_US</data>
            <data name="billingAddress/dataset" xsi:type="string">customer_US_login</data>
            <data name="shipping/shipping_service" xsi:type="string">Flat Rate</data>
            <data name="shipping/shipping_method" xsi:type="string">Fixed</data>
            <data name="grandTotal/base" xsi:type="string">315.00</data>
            <data name="payment/method" xsi:type="string">checkmo</data>
            <data name="previousOrderStatus" xsi:type="string">Pending</data>
            <data name="status" xsi:type="string">Pending</data>
            <data name="orderButtonsAvailable" xsi:type="string">Back, Reorder, Cancel, Hold, Invoice, Ship, Edit</data>
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderCreateSuccessMessage" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderInOrdersGrid" />
            <constraint name="Mage\Sales\Test\Constraint\AssertReorderedOrderStatusIsCorrect" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderStatusIsCorrect" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderButtonsAvailable" />
            <constraint name="Mage\Sales\Test\Constraint\AssertOrderGrandTotal" />
        </variation>
    </testCase>
</config>
