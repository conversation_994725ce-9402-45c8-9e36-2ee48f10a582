<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Tests
 * @package     Tests_Functional
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../vendor/magento/mtf/Magento/Mtf/Repository/etc/repository.xsd">
    <repository class="Mage\Core\Test\Repository\ConfigData">
        <dataset name="enable_map_on_gesture">
            <field name="sales/msrp/enabled" xsi:type="array">
                <item name="scope" xsi:type="string">sales</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="sales/msrp/apply_for_all" xsi:type="array">
                <item name="scope" xsi:type="string">sales</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="sales/msrp/display_price_type" xsi:type="array">
                <item name="scope" xsi:type="string">sales</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">On Gesture</item>
                <item name="value" xsi:type="string">1</item>
            </field>
        </dataset>
        <dataset name="enable_map_on_gesture_rollback">
            <field name="sales/msrp/enabled" xsi:type="array">
                <item name="scope" xsi:type="string">sales</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="sales/msrp/apply_for_all" xsi:type="array">
                <item name="scope" xsi:type="string">sales</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
        </dataset>
        <dataset name="enable_map_in_cart">
            <field name="sales/msrp/enabled" xsi:type="array">
                <item name="scope" xsi:type="string">sales</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="sales/msrp/apply_for_all" xsi:type="array">
                <item name="scope" xsi:type="string">sales</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="sales/msrp/display_price_type" xsi:type="array">
                <item name="scope" xsi:type="string">sales</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">In Cart</item>
                <item name="value" xsi:type="string">2</item>
            </field>
        </dataset>
        <dataset name="enable_map_in_cart_rollback">
            <field name="sales/msrp/enabled" xsi:type="array">
                <item name="scope" xsi:type="string">sales</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="sales/msrp/apply_for_all" xsi:type="array">
                <item name="scope" xsi:type="string">sales</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
        </dataset>
        <dataset name="enable_map_before_order">
            <field name="sales/msrp/enabled" xsi:type="array">
                <item name="scope" xsi:type="string">sales</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="sales/msrp/apply_for_all" xsi:type="array">
                <item name="scope" xsi:type="string">sales</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="sales/msrp/display_price_type" xsi:type="array">
                <item name="scope" xsi:type="string">sales</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Before Order Confirmation</item>
                <item name="value" xsi:type="string">3</item>
            </field>
        </dataset>
        <dataset name="enable_map_before_order_rollback">
            <field name="sales/msrp/enabled" xsi:type="array">
                <item name="scope" xsi:type="string">sales</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="sales/msrp/apply_for_all" xsi:type="array">
                <item name="scope" xsi:type="string">sales</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
        </dataset>
        <dataset name="enableGiftMessages">
            <field name="sales/gift_options/allow_order" xsi:type="array">
                <item name="scope" xsi:type="string">sales</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
            <field name="sales/gift_options/allow_items" xsi:type="array">
                <item name="scope" xsi:type="string">sales</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">Yes</item>
                <item name="value" xsi:type="string">1</item>
            </field>
        </dataset>
        <dataset name="enableGiftMessages_rollback">
            <field name="sales/gift_options/allow_order" xsi:type="array">
                <item name="scope" xsi:type="string">sales</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
            <field name="sales/gift_options/allow_items" xsi:type="array">
                <item name="scope" xsi:type="string">sales</item>
                <item name="scope_id" xsi:type="number">1</item>
                <item name="label" xsi:type="string">No</item>
                <item name="value" xsi:type="string" />
            </field>
        </dataset>
    </repository>
</config>
