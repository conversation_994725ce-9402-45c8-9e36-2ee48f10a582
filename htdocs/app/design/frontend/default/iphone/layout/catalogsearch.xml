<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

-->
<layout version="0.1.0">

    <default>
        <reference name="header">
            <block type="core/template" name="top.search" as="topSearch" template="catalogsearch/form.mini.phtml"/>
        </reference>
    </default>

    <catalogsearch_result_index translate="label">
        <label>Quick Search Form</label>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="left">
        </reference>
        <reference name="content">
            <block type="catalogsearch/result" name="search.result" template="catalogsearch/result.phtml">
                <block type="catalog/product_list" name="search_result_list" template="catalog/product/list.phtml">
                    <block type="catalog/product_list_toolbar" name="product_list_toolbar" template="catalog/product/list/toolbar.phtml">
                        <block type="page/html_pager" name="product_list_toolbar_pager"/>
                    </block>
                    <action method="setToolbarBlockName"><name>product_list_toolbar</name></action>
                </block>
                <action method="setListOrders"/>
                <action method="setListModes"/>
                <action method="setListCollection"/>
            </block>
            <block type="catalogsearch/layer" name="catalogsearch" after="-" template="catalog/layer/view.phtml"/>
        </reference>
    </catalogsearch_result_index>

    <catalogsearch_advanced_index translate="label">
        <label>Advanced Search Form</label>
        <!-- Mage_Catalogsearch -->
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="head">
            <action method="setTitle" translate="title" module="catalogsearch"><title>Advanced Search</title></action>
            <action method="addItem"><type>js_css</type><name>calendar/calendar-win2k-1.css</name><params/><!--<if/><condition>can_load_calendar_js</condition>--></action>
            <action method="addItem"><type>js</type><name>calendar/calendar.js</name><!--<params/><if/><condition>can_load_calendar_js</condition>--></action>
            <action method="addItem"><type>js</type><name>calendar/calendar-setup.js</name><!--<params/><if/><condition>can_load_calendar_js</condition>--></action>
        </reference>
        <reference name="content">
            <block type="catalogsearch/advanced_form" name="catalogsearch_advanced_form" template="catalogsearch/advanced/form.phtml"/>
            <block type="core/html_calendar" name="html_calendar" as="html_calendar" template="page/js/calendar.phtml"/>
        </reference>
    </catalogsearch_advanced_index>

<!--
Advanced search results
-->

    <catalogsearch_advanced_result translate="label">
        <label>Advanced Search Result</label>
        <update handle="page_two_columns_right" />
        <!-- Mage_Catalogsearch -->
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="content">
            <block type="catalogsearch/advanced_result" name="catalogsearch_advanced_result" template="catalogsearch/advanced/result.phtml">
                <block type="catalog/product_list" name="search_result_list" template="catalog/product/list.phtml">
                    <block type="catalog/product_list_toolbar" name="product_list_toolbar" template="catalog/product/list/toolbar.phtml">
                        <block type="page/html_pager" name="product_list_toolbar_pager"/>
                    </block>
                    <action method="setToolbarBlockName"><name>product_list_toolbar</name></action>
                </block>
                <action method="setListOrders"/>
                <action method="setListModes"/>
                <action method="setListCollection"/>
            </block>
        </reference>
    </catalogsearch_advanced_result>

    <catalogsearch_term_popular translate="label">
        <label>Popular Search Terms</label>
        <remove name="right"/>
        <remove name="left"/>

        <reference name="head">
            <action method="setTitle" translate="title" module="catalogsearch"><title>Search Terms</title></action>
        </reference>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="content">
            <block type="catalogsearch/term" name="seo.searchterm" template="catalogsearch/term.phtml"/>
        </reference>
    </catalogsearch_term_popular>

</layout>
