<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

-->
<layout version="0.1.0">

<!--
Default layout, loads most of the pages
-->

    <default>

        <!-- Mage_Checkout -->
        <reference name="top.links">
            <block type="checkout/links" name="checkout_cart_link">
                <action method="addCheckoutLink"></action>
            </block>
        </reference>
    </default>

    <checkout_cart_index translate="label">
        <label>Shopping Cart</label>
        <remove name="right"/>
        <remove name="left"/>
        <!-- Mage_Checkout -->
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="content">
            <block type="checkout/cart" name="checkout.cart">
                <action method="setCartTemplate"><value>checkout/cart.phtml</value></action>
                <action method="setEmptyTemplate"><value>checkout/cart/noItems.phtml</value></action>
                <action method="chooseTemplate"/>
                <action method="addItemRender"><type>simple</type><block>checkout/cart_item_renderer</block><template>checkout/cart/item/default.phtml</template></action>
                <action method="addItemRender"><type>grouped</type><block>checkout/cart_item_renderer_grouped</block><template>checkout/cart/item/default.phtml</template></action>
                <action method="addItemRender"><type>configurable</type><block>checkout/cart_item_renderer_configurable</block><template>checkout/cart/item/default.phtml</template></action>

                <block type="core/text_list" name="checkout.cart.top_methods" as="top_methods" translate="label">
                    <label>Payment Methods Before Checkout Button</label>
                    <block type="checkout/onepage_link" name="checkout.cart.methods.onepage" template="checkout/onepage/link.phtml"/>
                </block>

                <block type="page/html_wrapper" name="checkout.cart.form.before" as="form_before" translate="label">
                    <label>Shopping Cart Form Before</label>
                </block>

                <block type="core/text_list" name="checkout.cart.methods" as="methods" translate="label">
                    <label>Payment Methods After Checkout Button</label>
                    <block type="checkout/onepage_link" name="checkout.cart.methods.onepage" template="checkout/onepage/link.phtml"/>
                </block>

                <block type="checkout/cart_coupon" name="checkout.cart.coupon" as="coupon" template="checkout/cart/coupon.phtml"/>

                <block type="checkout/cart_totals" name="checkout.cart.totals" as="totals" template="checkout/cart/totals.phtml"/>
            </block>
        </reference>
        <block type="core/text_list" name="additional.product.info" translate="label">
            <label>Additional Product Info</label>
        </block>
    </checkout_cart_index>

    <checkout_cart_configure translate="label">
        <label>Configure Cart Item</label>
        <update handle="catalog_product_view"/>
        <reference name="product.info">
            <block type="checkout/cart_item_configure" name="checkout.cart.item.configure.block"></block>
        </reference>
    </checkout_cart_configure>

<!--
Multi address shipping checkout main layout,
will be rendered on all checkout pages
-->

    <checkout_multishipping translate="label">
        <label>Multishipping Checkout</label>
        <!-- Mage_Checkout -->
        <remove name="right"/>
        <remove name="left"/>

        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="content">
            <block type="checkout/multishipping_state" name="checkout_state" template="checkout/multishipping/state.phtml"/>
        </reference>
    </checkout_multishipping>

    <checkout_multishipping_login>
        <update handle="customer_account_login"/>
    </checkout_multishipping_login>

    <checkout_multishipping_register>
        <update handle="customer_account_create"/>
    </checkout_multishipping_register>

<!--
Multi address shipping checkout selection of address per item page
-->

    <checkout_multishipping_address_select translate="label">
        <label>Multishipping Checkout Shipping Address Selection</label>
        <update handle="checkout_multishipping"/>
        <!-- Mage_Checkout -->
        <reference name="content">
            <block type="checkout/multishipping_address_select" name="checkout_address_select" template="checkout/multishipping/address/select.phtml"/>
        </reference>
    </checkout_multishipping_address_select>

    <checkout_multishipping_address_selectbilling translate="label">
        <label>Multishipping Checkout Billing Address Selection</label>
        <update handle="checkout_multishipping"/>
        <!-- Mage_Checkout -->
        <reference name="content">
            <block type="checkout/multishipping_address_select" name="checkout_address_select" template="checkout/multishipping/address/select.phtml"/>
        </reference>
    </checkout_multishipping_address_selectbilling>


    <checkout_multishipping_address_newshipping translate="label">
        <label>Multishipping Checkout Shipping Address Creation</label>
        <update handle="checkout_multishipping"/>
        <update handle="checkout_multishipping_customer_address"/>
    </checkout_multishipping_address_newshipping>

    <checkout_multishipping_address_newbilling translate="label">
        <label>Multishipping Checkout Billing Address Creation</label>
        <update handle="checkout_multishipping"/>
        <update handle="checkout_multishipping_customer_address"/>
    </checkout_multishipping_address_newbilling>

    <checkout_multishipping_address_editshipping translate="label">
        <label>Multishipping Checkout Shipping Address Edit Form</label>
        <update handle="checkout_multishipping"/>
        <update handle="checkout_multishipping_customer_address"/>
    </checkout_multishipping_address_editshipping>

    <checkout_multishipping_address_editaddress>
        <update handle="checkout_multishipping"/>
        <update handle="checkout_multishipping_customer_address"/>
    </checkout_multishipping_address_editaddress>

    <checkout_multishipping_address_editbilling translate="label">
        <label>Multishipping Checkout Billing Address Edit Form</label>
        <update handle="checkout_multishipping"/>
        <update handle="checkout_multishipping_customer_address"/>
    </checkout_multishipping_address_editbilling>

    <checkout_multishipping_customer_address translate="label">
        <label>Multishipping Checkout Customer Address Edit Form</label>
        <reference name="content">
            <block type="customer/address_edit" name="customer_address_edit" template="customer/address/edit.phtml"/>
        </reference>
    </checkout_multishipping_customer_address>

<!--
Multi address shipping checkout address page
-->

    <checkout_multishipping_addresses translate="label">
        <label>Multishipping Checkout Address (Any) Form</label>
        <update handle="checkout_multishipping"/>
        <!-- Mage_Checkout -->
        <reference name="content">
            <block type="checkout/multishipping_addresses" name="checkout_addresses" template="checkout/multishipping/addresses.phtml">
                <action method="addItemRender"><type>default</type><block>checkout/cart_item_renderer</block><template>checkout/multishipping/item/default.phtml</template></action>
                <action method="addItemRender"><type>grouped</type><block>checkout/cart_item_renderer_grouped</block><template>checkout/multishipping/item/default.phtml</template></action>
                <action method="addItemRender"><type>configurable</type><block>checkout/cart_item_renderer_configurable</block><template>checkout/multishipping/item/default.phtml</template></action>
            </block>
        </reference>
    </checkout_multishipping_addresses>

<!--
Multi address shipping checkout shipping information
-->

    <checkout_multishipping_shipping translate="label">
        <label>Multishipping Checkout Shipping Information Step</label>
        <update handle="checkout_multishipping"/>
        <!-- Mage_Checkout -->
        <reference name="content">
            <block type="checkout/multishipping_shipping" name="checkout_shipping" template="checkout/multishipping/shipping.phtml">
                <action method="addItemRender"><type>default</type><block>checkout/cart_item_renderer</block><template>checkout/multishipping/item/default.phtml</template></action>
                <action method="addItemRender"><type>grouped</type><block>checkout/cart_item_renderer_grouped</block><template>checkout/multishipping/item/default.phtml</template></action>
                <action method="addItemRender"><type>configurable</type><block>checkout/cart_item_renderer_configurable</block><template>checkout/multishipping/item/default.phtml</template></action>

                <block type="checkout/multishipping_billing_items" name="checkout_billing_items" template="checkout/multishipping/billing/items.phtml">
                    <action method="addItemRender"><type>default</type><block>checkout/cart_item_renderer</block><template>checkout/multishipping/item/default.phtml</template></action>
                    <action method="addItemRender"><type>grouped</type><block>checkout/cart_item_renderer_grouped</block><template>checkout/multishipping/item/default.phtml</template></action>
                    <action method="addItemRender"><type>configurable</type><block>checkout/cart_item_renderer_configurable</block><template>checkout/multishipping/item/default.phtml</template></action>
                </block>
            </block>
        </reference>
    </checkout_multishipping_shipping>

<!--
Multi address shipping checkout billing information
-->

    <checkout_multishipping_billing translate="label">
        <label>Multishipping Checkout Billing Information Step</label>
        <update handle="checkout_multishipping"/>
        <!-- Mage_Checkout -->
        <reference name="content">
            <block type="checkout/multishipping_billing" name="checkout_billing" template="checkout/multishipping/billing.phtml">
                <action method="setMethodFormTemplate"><method>purchaseorder</method><template>payment/form/purchaseorder.phtml</template></action>

                <!--<block type="checkout/multishipping_billing_items" name="checkout_billing_items" template="checkout/multishipping/billing/items.phtml">
                    <action method="addItemRender"><type>default</type><block>checkout/cart_item_renderer</block><template>checkout/multishipping/item/default.phtml</template></action>
                    <action method="addItemRender"><type>grouped</type><block>checkout/cart_item_renderer_grouped</block><template>checkout/multishipping/item/default.phtml</template></action>
                    <action method="addItemRender"><type>configurable</type><block>checkout/cart_item_renderer_configurable</block><template>checkout/multishipping/item/default.phtml</template></action>
                </block>-->

            </block>
        </reference>
    </checkout_multishipping_billing>

<!--
Multi address shipping checkout overview
-->

    <checkout_multishipping_overview translate="label">
        <label>Multishipping Checkout Overview</label>
        <update handle="checkout_multishipping"/>
        <!-- Mage_Checkout -->
        <reference name="content">
            <block type="checkout/multishipping_overview" name="checkout_overview" template="checkout/multishipping/overview.phtml">
                <action method="addItemRender"><type>default</type><block>checkout/cart_item_renderer</block><template>checkout/multishipping/item/default.phtml</template></action>
                <action method="addItemRender"><type>grouped</type><block>checkout/cart_item_renderer_grouped</block><template>checkout/multishipping/item/default.phtml</template></action>
                <action method="addItemRender"><type>configurable</type><block>checkout/cart_item_renderer_configurable</block><template>checkout/multishipping/item/default.phtml</template></action>
                <action method="addRowItemRender"><type>grouped</type><block>checkout/cart_item_renderer_grouped</block><template>checkout/multishipping/overview/item.phtml</template></action>
                <action method="addRowItemRender"><type>configurable</type><block>checkout/cart_item_renderer_configurable</block><template>checkout/multishipping/overview/item.phtml</template></action>
                <block type="checkout/multishipping_payment_info" name="payment_info">
                    <action method="setInfoTemplate"><method></method><template></template></action>
                </block>
                <block type="checkout/agreements" name="checkout.multishipping.agreements" as="agreements" template="checkout/multishipping/agreements.phtml"/>
                <block type="checkout/cart_totals" name="totals" />
                <block type="core/text_list" name="checkout.multishipping.overview.items.after" as="items_after" translate="label">
                    <label>Overview Items After</label>
                </block>
            </block>
        </reference>
    </checkout_multishipping_overview>

<!--
Multi address shipping checkout success
-->

    <checkout_multishipping_success translate="label">
        <label>Multishipping Checkout Success</label>
        <update handle="checkout_multishipping"/>
        <!-- Mage_Checkout -->
        <reference name="content">
            <block type="checkout/multishipping_success" name="checkout_success" template="checkout/multishipping/success.phtml"/>
        </reference>
    </checkout_multishipping_success>

<!--
One page checkout main layout
-->

    <checkout_onepage_index translate="label">
        <label>One Page Checkout</label>
        <!-- Mage_Checkout -->
        <remove name="left"/>

        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="content">
            <block type="checkout/onepage" name="checkout.onepage" template="checkout/onepage.phtml">
                <block type="checkout/onepage_login" name="checkout.onepage.login" as="login" template="checkout/onepage/login.phtml">
                    <block type="page/html_wrapper" name="checkout.onepage.login.before" as="login_before" translate="label">
                        <label>Login/Registration Before</label>
                        <action method="setMayBeInvisible"><value>1</value></action>
                    </block>
                </block>
                <block type="checkout/onepage_billing" name="checkout.onepage.billing" as="billing" template="checkout/onepage/billing.phtml"/>
                <block type="checkout/onepage_shipping" name="checkout.onepage.shipping" as="shipping" template="checkout/onepage/shipping.phtml"/>
                <block type="checkout/onepage_shipping_method" name="checkout.onepage.shipping_method" as="shipping_method" template="checkout/onepage/shipping_method.phtml">
                    <block type="checkout/onepage_shipping_method_available" name="checkout.onepage.shipping_method.available" as="available" template="checkout/onepage/shipping_method/available.phtml"/>
                    <block type="checkout/onepage_shipping_method_additional" name="checkout.onepage.shipping_method.additional" as="additional" template="checkout/onepage/shipping_method/additional.phtml"/>
                </block>
                <block type="checkout/onepage_payment" name="checkout.onepage.payment" as="payment" template="checkout/onepage/payment.phtml">
                    <block type="checkout/onepage_payment_methods" name="checkout.payment.methods" as="methods" template="checkout/onepage/payment/info.phtml">
                        <action method="setMethodFormTemplate"><method>purchaseorder</method><template>payment/form/purchaseorder.phtml</template></action>
                    </block>
                </block>
                <block type="checkout/onepage_review" name="checkout.onepage.review" as="review" template="checkout/onepage/review.phtml"/>
            </block>
        </reference>
    </checkout_onepage_index>

<!--
One page checkout progress block
-->

    <checkout_onepage_progress>
        <!-- Mage_Checkout -->
        <remove name="right"/>
        <remove name="left"/>
        
    </checkout_onepage_progress>

<!--
One page checkout payment methods block
-->
    <checkout_onepage_paymentmethod>
        <remove name="right"/>
        <remove name="left"/>

        <block type="checkout/onepage_payment_methods" name="root" output="toHtml" template="checkout/onepage/payment/methods.phtml">
            <action method="setMethodFormTemplate"><method>purchaseorder</method><template>payment/form/purchaseorder.phtml</template></action>
        </block>
    </checkout_onepage_paymentmethod>


<!--
One page checkout shipping methods block
-->

    <checkout_onepage_shippingmethod>
        <!-- Mage_Checkout -->
        <remove name="right"/>
        <remove name="left"/>

        <block type="checkout/onepage_shipping_method_available" name="root" output="toHtml" template="checkout/onepage/shipping_method/available.phtml"/>
    </checkout_onepage_shippingmethod>

    <checkout_onepage_additional>
        <!-- Mage_Checkout -->
        <remove name="right"/>
        <remove name="left"/>

        <block type="checkout/onepage_shipping_method_additional" name="root" output="toHtml" template="checkout/onepage/shipping_method/additional.phtml">
            <action method="setDontDisplayContainer"><param>1</param></action>
        </block>
    </checkout_onepage_additional>

<!--
One page checkout order review block
-->

    <checkout_onepage_review translate="label">
        <label>One Page Checkout Overview</label>
        <!-- Mage_Checkout -->
        <remove name="right"/>
        <remove name="left"/>

        <block type="checkout/onepage_review_info" name="root" output="toHtml" template="checkout/onepage/review/info.phtml">
            <action method="addItemRender"><type>default</type><block>checkout/cart_item_renderer</block><template>checkout/onepage/review/item.phtml</template></action>
            <action method="addItemRender"><type>grouped</type><block>checkout/cart_item_renderer_grouped</block><template>checkout/onepage/review/item.phtml</template></action>
            <action method="addItemRender"><type>configurable</type><block>checkout/cart_item_renderer_configurable</block><template>checkout/onepage/review/item.phtml</template></action>
            <block type="checkout/cart_totals" name="checkout.onepage.review.info.totals" as="totals" template="checkout/onepage/review/totals.phtml"/>
            <block type="core/text_list" name="checkout.onepage.review.info.items.before" as="items_before" translate="label">
                <label>Items Before</label>
            </block>
            <block type="core/text_list" name="checkout.onepage.review.info.items.after" as="items_after" translate="label">
                <label>Items After</label>
            </block>
            <block type="checkout/agreements" name="checkout.onepage.agreements" as="agreements" template="checkout/onepage/agreements.phtml"/>
            <block type="core/template" name="checkout.onepage.review.button" as="button" template="checkout/onepage/review/button.phtml"/>
        </block>
    </checkout_onepage_review>

    <checkout_onepage_success translate="label">
        <label>One Page Checkout Success</label>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="content">
            <block type="checkout/onepage_success" name="checkout.success" template="checkout/success.phtml"/>
        </reference>
    </checkout_onepage_success>
    <checkout_onepage_failure translate="label">
        <label>One Page Checkout Failure</label>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="content">
            <block type="checkout/onepage_failure" name="checkout.failure" template="checkout/onepage/failure.phtml"/>
        </reference>
    </checkout_onepage_failure>
    <block type="core/list" name="additional.product.info"/>
</layout>
