<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

-->
<layout version="0.1.0">
    <customer_account>
        <reference name="customer_account_navigation" >
            <action method="addLink" translate="label"><name>recurring_profiles</name><path>sales/recurring_profile/</path><label>Recurring Profiles</label></action>
        </reference>
    </customer_account>

    <sales_recurring_profile_index>
        <update handle="customer_account"/>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="my.account.wrapper">
            <block type="sales/recurring_profiles" name="sales.recurring.profiles" template="sales/recurring/profiles.phtml">
                <block type="sales/recurring_profiles" name="sales.recurring.profiles.grid" as="grid" template="sales/recurring/grid.phtml">
                    <action method="prepareProfilesGrid"/>
                    <action method="setEmptyGridMessage" translate="value"><value>There are no recurring profiles yet.</value></action>
                    <action method="setGridHtmlId"><value>recurring_profile_list_view</value></action>
                </block>
            </block>
        </reference>
    </sales_recurring_profile_index>

    <sales_recurring_profile_view__tabs>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="my.account.wrapper">
            <block type="sales/recurring_profile_view" name="sales.recurring.profile.view" template="sales/recurring/profile/view.phtml">
                <action method="prepareViewData"/>
                <action method="setShouldPrepareInfoTabs"><value>1</value></action>
                <block type="core/text" as="profile_info" name="sales.recurring.profile.view.tab.profile">
                    <action method="addToParentGroup"><value>info_tabs</value></action>
                    <action method="setViewLabel" translate="value"><value>Profile Information</value></action>
                    <action method="setViewAction"><value>view</value></action>
                </block>
<!-- not implemented
                <block type="core/text" as="history" name="sales.recurring.profile.view.tab.history">
                    <action method="addToParentGroup"><value>info_tabs</value></action>
                    <action method="setViewLabel" translate="value"><value>History</value></action>
                    <action method="setViewAction"><value>history</value></action>
                </block>
-->
                <block type="core/text" as="related_orders" name="sales.recurring.profile.view.tab.orders">
                    <action method="addToParentGroup"><value>info_tabs</value></action>
                    <action method="setViewLabel" translate="value"><value>Related Orders</value></action>
                    <action method="setViewAction"><value>orders</value></action>
                </block>
<!-- not implemented
                <block type="core/text" as="vendor_info" name="sales.recurring.profile.view.tab.vendor">
                    <action method="addToParentGroup"><value>info_tabs</value></action>
                    <action method="setViewLabel" translate="value"><value>Gateway Information</value></action>
                    <action method="setViewAction"><value>vendor</value></action>
                </block>
-->
            </block>
        </reference>
    </sales_recurring_profile_view__tabs>

    <sales_recurring_profile_view>
        <update handle="customer_account"/>
        <update handle="sales_recurring_profile_view__tabs"/>
        <reference name="sales.recurring.profile.view.tab.profile">
            <action method="setIsViewCurrent"><v>1</v></action>
        </reference>
        <reference name="sales.recurring.profile.view">
            <block type="sales/recurring_profile_view" name="sales.recurring.profile.view.general" as="general" template="sales/recurring/profile/view/info.phtml">
                <action method="prepareReferenceInfo"/>
                <action method="addToParentGroup"><value>info_blocks_row_1</value></action>
                <action method="setViewColumn"><value>1</value></action>
                <action method="setViewLabel" translate="value"><value>Reference</value></action>
            </block>
            <block type="sales/recurring_profile_view" name="sales.recurring.profile.view.item" as="item" template="sales/recurring/profile/view/info.phtml">
                <action method="prepareItemInfo"/>
                <action method="addToParentGroup"><value>info_blocks_row_1</value></action>
                <action method="setViewColumn"><value>2</value></action>
                <action method="setViewLabel" translate="value"><value>Purchased Item</value></action>
            </block>
            <block type="sales/recurring_profile_view" name="sales.recurring.profile.view.schedule" as="profile" template="sales/recurring/profile/view/info.phtml">
                <action method="prepareScheduleInfo"/>
                <action method="addToParentGroup"><value>info_blocks_row_2</value></action>
                <action method="setViewColumn"><value>1</value></action>
                <action method="setViewLabel" translate="value"><value>Profile Schedule</value></action>
            </block>
            <block type="sales/recurring_profile_view" name="sales.recurring.profile.view.fees" as="fees" template="sales/recurring/profile/view/info.phtml">
                <action method="prepareFeesInfo"/>
                <action method="addToParentGroup"><value>info_blocks_row_2</value></action>
                <action method="setViewColumn"><value>2</value></action>
                <action method="setViewLabel" translate="value"><value>Profile Payments</value></action>
            </block>
            <block type="sales/recurring_profile_view" name="sales.recurring.profile.view.billing" as="billing_address" template="sales/recurring/profile/view/info.phtml">
                <action method="prepareAddressInfo"/>
                <action method="addToParentGroup"><value>info_blocks_row_3</value></action>
                <action method="setViewColumn"><value>1</value></action>
                <action method="setViewLabel" translate="value"><value>Billing Address</value></action>
            </block>
            <block type="sales/recurring_profile_view" name="sales.recurring.profile.view.shipping" as="shipping_address" template="sales/recurring/profile/view/info.phtml">
                <action method="setAddressType"><value>shipping</value></action>
                <action method="prepareAddressInfo"/>
                <action method="addToParentGroup"><value>info_blocks_row_3</value></action>
                <action method="setViewColumn"><value>2</value></action>
                <action method="setViewLabel" translate="value"><value>Shipping Address</value></action>
            </block>
        </reference>
    </sales_recurring_profile_view>

<!-- not implemented
    <sales_recurring_profile_history>
        <update handle="customer_account"/>
        <update handle="sales_recurring_profile_view__tabs"/>
        <reference name="sales.recurring.profile.view.tab.history">
            <action method="setIsViewCurrent"><v>1</v></action>
        </reference>
    </sales_recurring_profile_history>
-->

    <sales_recurring_profile_orders>
        <update handle="customer_account"/>
        <update handle="sales_recurring_profile_view__tabs"/>
        <reference name="sales.recurring.profile.view.tab.orders">
            <action method="setIsViewCurrent"><v>1</v></action>
        </reference>
        <reference name="sales.recurring.profile.view">
            <block type="sales/recurring_profile_view" name="sales.recurring.profile.view.orders" as="table" template="sales/recurring/grid.phtml">
                <action method="prepareRelatedOrdersFrontendGrid"/>
                <action method="setViewLabel" translate="value"><value>Orders Based on This Profile</value></action>
                <action method="setEmptyGridMessage" translate="value"><value>There are no orders yet.</value></action>
                <action method="setGridHtmlClass"><value>info-box</value></action>
                <action method="setGridHtmlCss"><value>border:0</value></action>
            </block>
        </reference>
    </sales_recurring_profile_orders>

<!-- not implemented
    <sales_recurring_profile_vendor>
        <update handle="customer_account"/>
        <update handle="sales_recurring_profile_view__tabs"/>
        <reference name="sales.recurring.profile.view.tab.vendor">
            <action method="setIsViewCurrent"><v>1</v></action>
        </reference>
    </sales_recurring_profile_vendor>
-->
</layout>
