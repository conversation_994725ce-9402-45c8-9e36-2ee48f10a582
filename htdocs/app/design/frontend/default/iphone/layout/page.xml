<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<layout version="0.1.0">
<!--
Default layout, loads most of the pages
-->

    <default translate="label" module="page">
        <label>All Pages</label>
        <block type="page/html" name="root" output="toHtml" template="page/1column.phtml">

            <block type="page/html_head" name="head" as="head">
                <action method="addJs"><script>prototype/prototype.js</script></action>
                <action method="addJs"><script>lib/ccard.js</script></action>
                <action method="addJs"><script>prototype/validation.js</script></action>
                <action method="addJs"><script>scriptaculous/builder.js</script></action>
                <action method="addJs"><script>scriptaculous/effects.js</script></action>
                <action method="addJs"><script>scriptaculous/dragdrop.js</script></action>
                <action method="addJs"><script>scriptaculous/controls.js</script></action>
                <action method="addJs"><script>scriptaculous/slider.js</script></action>
                <action method="addJs"><script>varien/js.js</script></action>
                <action method="addJs"><script>varien/form.js</script></action>
                <action method="addJs"><script>mage/translate.js</script></action>
                <action method="addJs"><script>mage/cookies.js</script></action>

                <block type="page/js_cookie" name="js_cookies" template="page/js/cookie.phtml"/>
                
                <action method="addItem"><type>skin_js</type><name>js/modernizr.js</name></action>
                <action method="addItem"><type>skin_js</type><name>js/iphone.js</name></action>
                <action method="addItem"><type>skin_js</type><name>js/dnd.js</name></action>

                <action method="addCss"><stylesheet>css/iphone.css</stylesheet></action>
                <action method="addCss"><stylesheet>css/custom.css</stylesheet></action>
            </block>

            <block type="core/text_list" name="after_body_start" as="after_body_start" translate="label">
                <label>Page Top</label>
            </block>

            <block type="page/html_notices" name="global_notices" as="global_notices" template="page/html/notices.phtml" />

            <block type="page/html_header" name="header" as="header">
                <block type="page/template_links" name="account.links" as="accountLinks"/>
                <block type="page/template_links" name="top.links" as="topLinks"/>
                <block type="page/template_links" name="checkout.links" as="checkoutLinks"/>
                <block type="core/text_list" name="top.menu" as="topMenu" translate="label">
                    <label>Navigation Bar</label>
                    <block type="page/html_topmenu" name="catalog.topnav" template="page/html/topmenu.phtml"/>
                </block>
                <block type="page/html_wrapper" name="top.container" as="topContainer" translate="label">
                    <label>Page Header</label>
                    <action method="setElementClass"><value>top-container</value></action>
                </block>
                <block type="page/html_welcome" name="welcome" as="welcome"/>
            </block>

            <block type="page/html_breadcrumbs" name="breadcrumbs" as="breadcrumbs"/>

            <block type="core/text_list" name="left" as="left" translate="label">
                <label>Left Column</label>
            </block>

            <block type="core/messages" name="global_messages" as="global_messages"/>
            <block type="core/messages" name="messages" as="messages"/>

            <block type="core/text_list" name="content" as="content" translate="label">
                <label>Main Content Area</label>
            </block>

            <block type="core/text_list" name="right" as="right" translate="label">
                <label>Right Column</label>
            </block>

            <block type="page/html_footer" name="footer" as="footer" template="page/html/footer.phtml">
                <block type="page/html_wrapper" name="bottom.container" as="bottomContainer" translate="label">
                    <label>Page Footer</label>
                    <action method="setElementClass"><value>bottom-container</value></action>
                </block>

                <block type="page/template_links" name="footer_links" as="footer_links" template="page/template/links.phtml"/>
            </block>

            <block type="core/text_list" name="before_body_end" as="before_body_end" translate="label">
                <label>Page Bottom</label>
            </block>
        </block>

        <block type="core/profiler" output="toHtml" name="core_profiler"/>
    </default>

    <print translate="label" module="page">
        <label>All Pages (Print Version)</label>
        <!-- Mage_Page -->
        <block type="page/html" name="root" output="toHtml" template="page/print.phtml">

            <block type="page/html_head" name="head" as="head">
                <action method="addJs"><script>prototype/prototype.js</script></action>
                <action method="addJs"><script>mage/translate.js</script></action>
                <action method="addJs"><script>lib/ccard.js</script></action>
                <action method="addJs"><script>prototype/validation.js</script></action>
                <action method="addJs"><script>varien/js.js</script></action>

                <action method="addCss"><stylesheet>css/styles.css</stylesheet></action>
                <action method="addItem"><type>skin_css</type><name>css/styles-ie.css</name><params/><if>lt IE 8</if></action>
                <action method="addCss"><stylesheet>css/print.css</stylesheet><params>media="print"</params></action>
            </block>

            <block type="core/text_list" name="content" as="content" translate="label">
                <label>Main Content Area (Print Version)</label>
            </block>

        </block>
    </print>

     <!-- Custom page layout handles -->
    <page_empty translate="label">
        <label>All Empty Layout Pages</label>
        <reference name="root">
            <action method="setTemplate"><template>page/empty.phtml</template></action>
            <!-- Mark root page block that template is applied -->
            <action method="setIsHandle"><applied>1</applied></action>
            <action method="setLayoutCode"><name>empty</name></action>
        </reference>
    </page_empty>

    <page_one_column translate="label">
        <label>All One-Column Layout Pages</label>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
            <!-- Mark root page block that template is applied -->
            <action method="setIsHandle"><applied>1</applied></action>
            <action method="setLayoutCode"><name>one_column</name></action>
        </reference>
    </page_one_column>

    <page_two_columns_left translate="label">
        <label>All Two-Column Layout Pages (Left Column)</label>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
            <!-- Mark root page block that template is applied -->
            <action method="setIsHandle"><applied>1</applied></action>
            <action method="setLayoutCode"><name>two_columns_left</name></action>
        </reference>
    </page_two_columns_left>

    <page_two_columns_right translate="label">
        <label>All Two-Column Layout Pages (Right Column)</label>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
            <!-- Mark root page block that template is applied -->
            <action method="setIsHandle"><applied>1</applied></action>
            <action method="setLayoutCode"><name>two_columns_right</name></action>
        </reference>
    </page_two_columns_right>

    <page_three_columns translate="label">
        <label>All Three-Column Layout Pages</label>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
            <!-- Mark root page block that template is applied -->
            <action method="setIsHandle"><applied>1</applied></action>
            <action method="setLayoutCode"><name>three_columns</name></action>
        </reference>
    </page_three_columns>
</layout>
