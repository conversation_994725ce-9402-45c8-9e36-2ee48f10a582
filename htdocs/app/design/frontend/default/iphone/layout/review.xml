<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

-->
<layout version="0.1.0">

    <customer_account>
        <!-- Mage_Review -->
        <reference name="customer_account_navigation">
            <action method="addLink" translate="label" module="review"><name>reviews</name><path>review/customer</path><label>My Product Reviews</label></action>
        </reference>

    </customer_account>

<!--
Customer account home dashboard layout
-->

    <customer_account_index>

        <!-- Mage_Review -->
        <reference name="customer_account_dashboard">
            <block type="review/customer_recent" name="customer_account_dashboard_info1" as="info1" template="review/customer/recent.phtml"/>
        </reference>

    </customer_account_index>


<!--
Product reviews page (?)
-->

    <reviews>
        <!-- Mage_Review -->
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
    </reviews>

<!--
Product reviews page
-->

    <review_product_list translate="label">
        <label>Catalog Product Reviews List</label>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
            <action method="setMyClass"><value>review-product-page</value></action>
        </reference>
        <reference name="head">
            <action method="addJs"><script>varien/product.js</script></action>
            <action method="addJs"><script>varien/product_options.js</script></action>
            <action method="addJs"><script>varien/configurable.js</script></action>
        </reference>
        <reference name="content">
            <block type="review/product_view" name="product.info" template="catalog/product/view.phtml">
                <block type="page/html_pager" name="product_review_list.toolbar"/>
                <block type="core/template" name="product_review_list.count" template="review/product/view/count.phtml"/>
                <block type="review/product_view_list" name="product.info.product_additional_data" as="product_additional_data" template="review/product/view/list.phtml">
                    <block type="review/form" name="product.review.form" as="review_form">
                        <block type="page/html_wrapper" name="product.review.form.fields.before" as="form_fields_before" translate="label">
                            <label>Review Form Fields Before</label>
                            <action method="setMayBeInvisible"><value>1</value></action>
                        </block>
                    </block>
                </block>
            </block>
        </reference>
    </review_product_list>

    <review_product_view translate="label">
        <label>Catalog Product Review View</label>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="content">
            <block type="review/view" name="review_view"/>
        </reference>
    </review_product_view>

    <review_customer_index translate="label">
        <label>Customer My Account Product Reviews</label>
        <update handle="customer_account"/>
        <reference name="my.account.wrapper">
            <block type="review/customer_list" name="review_customer_list" template="review/customer/list.phtml"/>
        </reference>
    </review_customer_index>

    <review_customer_view translate="label">
        <label>Customer My Account Review Details</label>
        <update handle="customer_account"/>
        <reference name="my.account.wrapper">
            <block type="review/customer_view" name="customers_review"/>
        </reference>
    </review_customer_view>

</layout>
