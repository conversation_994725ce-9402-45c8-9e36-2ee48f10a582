<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

-->
<layout version="0.1.0">

<!--
Adding custom product price block
-->

    <catalog_category_default>
        <reference name="product_list">
            <action method="addPriceBlockType"><type>bundle</type><block>bundle/catalog_product_price</block><template>bundle/catalog/product/price.phtml</template></action>
        </reference>
    </catalog_category_default>

    <catalog_category_view>
        <reference name="product_list">
            <action method="addPriceBlockType"><type>bundle</type><block>bundle/catalog_product_price</block><template>bundle/catalog/product/price.phtml</template></action>
        </reference>
    </catalog_category_view>

    <catalog_category_layered>
        <reference name="product_list">
            <action method="addPriceBlockType"><type>bundle</type><block>bundle/catalog_product_price</block><template>bundle/catalog/product/price.phtml</template></action>
        </reference>
    </catalog_category_layered>

    <catalog_product_compare_index>
        <reference name="catalog.compare.list">
            <action method="addPriceBlockType"><type>bundle</type><block>bundle/catalog_product_price</block><template>bundle/catalog/product/price.phtml</template></action>
        </reference>
    </catalog_product_compare_index>

    <catalogsearch_result_index>
        <reference name="search_result_list">
            <action method="addPriceBlockType"><type>bundle</type><block>bundle/catalog_product_price</block><template>bundle/catalog/product/price.phtml</template></action>
        </reference>
    </catalogsearch_result_index>

    <catalogsearch_advanced_result>
        <reference name="search_result_list">
            <action method="addPriceBlockType"><type>bundle</type><block>bundle/catalog_product_price</block><template>bundle/catalog/product/price.phtml</template></action>
        </reference>
    </catalogsearch_advanced_result>

    <tag_product_list>
        <reference name="search_result_list">
            <action method="addPriceBlockType"><type>bundle</type><block>bundle/catalog_product_price</block><template>bundle/catalog/product/price.phtml</template></action>
        </reference>
    </tag_product_list>

    <tag_customer_view>
        <reference name="customer_view">
            <action method="addPriceBlockType"><type>bundle</type><block>bundle/catalog_product_price</block><template>bundle/catalog/product/price.phtml</template></action>
        </reference>
    </tag_customer_view>

    <default>
        <reference name="cart_sidebar">
            <action method="addItemRender"><type>bundle</type><block>bundle/checkout_cart_item_renderer</block><template>checkout/cart/sidebar/default.phtml</template></action>
        </reference>
        <reference name="wishlist_sidebar">
            <action method="addPriceBlockType"><type>bundle</type><block>bundle/catalog_product_price</block><template>bundle/catalog/product/price.phtml</template></action>
        </reference>
        <reference name="catalog_product_price_template">
            <action method="addPriceBlockType"><type>bundle</type><block>bundle/catalog_product_price</block><template>bundle/catalog/product/price.phtml</template></action>
        </reference>
    </default>

    <catalog_product_view>
        <reference name="catalog.product.related">
            <action method="addPriceBlockType"><type>bundle</type><block>bundle/catalog_product_price</block><template>bundle/catalog/product/price.phtml</template></action>
        </reference>
    </catalog_product_view>

<!--
Partof block for simple products
-->

   <PRODUCT_TYPE_simple>
        <!--
        <reference name="product.info.additional">

            <block type="bundle/catalog_product_list_partof" before="-" name="product.info.partof" as="partof_products" template="bundle/catalog/product/list/partof.phtml">
                <action method="addPriceBlockType"><type>bundle</type><block>bundle/catalog_product_price</block><template>bundle/catalog/product/price.phtml</template></action>
            </block>
        -->
        <reference name="product.info.upsell">
            <action method="addPriceBlockType"><type>bundle</type><block>bundle/catalog_product_price</block><template>bundle/catalog/product/price.phtml</template></action>
            <action method="setItemLimit"><type>bundle</type><limit>4</limit></action>
        </reference>
    </PRODUCT_TYPE_simple>

<!--
Shopping cart item renderer (sidebar)
-->

    <customer_account>
        <reference name="cart_sidebar">
            <action method="addItemRender"><type>bundle</type><block>bundle/checkout_cart_item_renderer</block><template>checkout/cart/sidebar/default.phtml</template></action>
        </reference>
    </customer_account>

<!--
Shopping cart item renderer
-->

    <checkout_cart_index>
        <reference name="checkout.cart">
            <action method="addItemRender"><type>bundle</type><block>bundle/checkout_cart_item_renderer</block><template>checkout/cart/item/default.phtml</template></action>
        </reference>
        <reference name="checkout.cart.crosssell">
            <action method="addPriceBlockType"><type>bundle</type><block>bundle/catalog_product_price</block><template>bundle/catalog/product/price.phtml</template></action>
        </reference>
    </checkout_cart_index>

<!--
Onepage Checkout Review Page
-->

    <checkout_onepage_review>
        <reference name="root">
            <action method="addItemRender"><type>bundle</type><block>bundle/checkout_cart_item_renderer</block><template>checkout/onepage/review/item.phtml</template></action>
        </reference>
    </checkout_onepage_review>

    <checkout_multishipping_addresses>
        <reference name="checkout_addresses">
            <action method="addItemRender"><type>bundle</type><block>bundle/checkout_cart_item_renderer</block><template>checkout/multishipping/item/default.phtml</template></action>
        </reference>
    </checkout_multishipping_addresses>

    <checkout_multishipping_shipping>
        <reference name="checkout_shipping">
            <action method="addItemRender"><type>bundle</type><block>bundle/checkout_cart_item_renderer</block><template>checkout/multishipping/item/default.phtml</template></action>
        </reference>
        <reference name="checkout_billing_items">
            <action method="addItemRender"><type>bundle</type><block>bundle/checkout_cart_item_renderer</block><template>checkout/multishipping/item/default.phtml</template></action>
        </reference>
    </checkout_multishipping_shipping>

    <checkout_multishipping_overview>
        <reference name="checkout_overview">
            <action method="addItemRender"><type>bundle</type><block>bundle/checkout_cart_item_renderer</block><template>checkout/multishipping/item/default.phtml</template></action>
        </reference>
    </checkout_multishipping_overview>

    <paypal_express_review>
        <reference name="paypal.express.review.details">
            <action method="addItemRender"><type>bundle</type><block>bundle/checkout_cart_item_renderer</block><template>checkout/onepage/review/item.phtml</template></action>
        </reference>
    </paypal_express_review>
    <paypal_express_review_details>
        <reference name="root">
            <action method="addItemRender"><type>bundle</type><block>bundle/checkout_cart_item_renderer</block><template>checkout/onepage/review/item.phtml</template></action>
        </reference>
    </paypal_express_review_details>

    <paypaluk_express_review>
        <reference name="paypal.express.review.details">
            <action method="addItemRender"><type>bundle</type><block>bundle/checkout_cart_item_renderer</block><template>checkout/onepage/review/item.phtml</template></action>
        </reference>
    </paypaluk_express_review>
    <paypaluk_express_review_details>
        <reference name="root">
            <action method="addItemRender"><type>bundle</type><block>bundle/checkout_cart_item_renderer</block><template>checkout/onepage/review/item.phtml</template></action>
        </reference>
    </paypaluk_express_review_details>

<!--
Additional block for bundle product type
-->

    <PRODUCT_TYPE_bundle translate="label" module="bundle">
        <label>Catalog Product View (Bundle)</label>
        <reference name="head">
            <action method="addItem"><type>skin_js</type><name>js/bundle.js</name></action>
        </reference>
        <reference name="product.info">
            <block type="bundle/catalog_product_view_type_bundle" name="product.info.bundle" as="product_type_data" template="bundle/catalog/product/view/type/bundle.phtml">
                <action method="addPriceBlockType"><type>bundle</type><block>bundle/catalog_product_price</block><template>bundle/catalog/product/price.phtml</template></action>
                <block type="bundle/catalog_product_price" name="bundle.prices" as="bundle_prices" template="bundle/catalog/product/view/price.phtml">
                    <action method="setMAPTemplate"><tmpl>catalog/product/price_msrp_item.phtml</tmpl></action>
                </block>
            </block>
        </reference>
        <reference name="product.info.options.wrapper">
            <block type="bundle/catalog_product_view_type_bundle" name="product.info.bundle.options" as="type_bundle_options" template="bundle/catalog/product/view/type/bundle/options.phtml">
                <action method="addRenderer"><type>select</type><block>bundle/catalog_product_view_type_bundle_option_select</block></action>
                <action method="addRenderer"><type>multi</type><block>bundle/catalog_product_view_type_bundle_option_multi</block></action>
                <action method="addRenderer"><type>radio</type><block>bundle/catalog_product_view_type_bundle_option_radio</block></action>
                <action method="addRenderer"><type>checkbox</type><block>bundle/catalog_product_view_type_bundle_option_checkbox</block></action>
            </block>
            <action method="insert"><block>product.info.bundle.options</block></action>
        </reference>
        <reference name="product.info.options.wrapper.bottom">
            <remove name="product.tierprices" />
            <block type="bundle/catalog_product_view" name="bundle.tierprices" as="tierprices" before="-" template="bundle/catalog/product/view/tierprices.phtml"/>
            <block type="cataloginventory/qtyincrements" name="product.info.qtyincrements" before="-" template="cataloginventory/qtyincrements.phtml"/>
        </reference>
        <reference name="product.clone_prices">
            <action method="addPriceBlockType"><type>bundle</type><block>bundle/catalog_product_price</block><template>bundle/catalog/product/view/price.phtml</template></action>
        </reference>
    </PRODUCT_TYPE_bundle>

    <sales_order_view>
        <reference name="order_items">
            <action method="addItemRender"><type>bundle</type><block>bundle/sales_order_items_renderer</block><template>bundle/sales/order/items/renderer.phtml</template></action>
        </reference>
    </sales_order_view>

    <sales_order_invoice>
        <reference name="invoice_items">
            <action method="addItemRender"><type>bundle</type><block>bundle/sales_order_items_renderer</block><template>bundle/sales/order/invoice/items/renderer.phtml</template></action>
        </reference>
    </sales_order_invoice>

    <sales_order_shipment>
        <reference name="shipment_items">
            <action method="addItemRender"><type>bundle</type><block>bundle/sales_order_items_renderer</block><template>bundle/sales/order/shipment/items/renderer.phtml</template></action>
        </reference>
    </sales_order_shipment>

    <sales_order_creditmemo>
        <reference name="creditmemo_items">
            <action method="addItemRender"><type>bundle</type><block>bundle/sales_order_items_renderer</block><template>bundle/sales/order/creditmemo/items/renderer.phtml</template></action>
        </reference>
    </sales_order_creditmemo>

<!--
Print pages
-->

    <sales_order_print>
        <reference name="sales.order.print">
            <action method="addItemRender"><type>bundle</type><block>bundle/sales_order_items_renderer</block><template>bundle/sales/order/items/renderer.phtml</template></action>
        </reference>
    </sales_order_print>

    <sales_order_printinvoice>
        <reference name="sales.order.print.invoice">
            <action method="addItemRender"><type>bundle</type><block>bundle/sales_order_items_renderer</block><template>bundle/sales/order/invoice/items/renderer.phtml</template></action>
        </reference>
    </sales_order_printinvoice>

    <sales_order_printshipment>
        <reference name="sales.order.print.shipment">
            <action method="addItemRender"><type>bundle</type><block>bundle/sales_order_items_renderer</block><template>bundle/sales/order/shipment/items/renderer.phtml</template></action>
        </reference>
    </sales_order_printshipment>

    <sales_order_printcreditmemo>
        <reference name="sales.order.print.creditmemo">
            <action method="addItemRender"><type>bundle</type><block>bundle/sales_order_items_renderer</block><template>bundle/sales/order/creditmemo/items/renderer.phtml</template></action>
        </reference>
    </sales_order_printcreditmemo>

<!--
For guests
-->
    <sales_guest_view>
        <reference name="order_items">
            <action method="addItemRender"><type>bundle</type><block>bundle/sales_order_items_renderer</block><template>bundle/sales/order/items/renderer.phtml</template></action>
        </reference>
    </sales_guest_view>

    <sales_guest_invoice>
        <reference name="invoice_items">
            <action method="addItemRender"><type>bundle</type><block>bundle/sales_order_items_renderer</block><template>bundle/sales/order/invoice/items/renderer.phtml</template></action>
        </reference>
    </sales_guest_invoice>

    <sales_guest_shipment>
        <reference name="shipment_items">
            <action method="addItemRender"><type>bundle</type><block>bundle/sales_order_items_renderer</block><template>bundle/sales/order/shipment/items/renderer.phtml</template></action>
        </reference>
    </sales_guest_shipment>

    <sales_guest_creditmemo>
        <reference name="creditmemo_items">
            <action method="addItemRender"><type>bundle</type><block>bundle/sales_order_items_renderer</block><template>bundle/sales/order/creditmemo/items/renderer.phtml</template></action>
        </reference>
    </sales_guest_creditmemo>

    <sales_guest_print>
        <reference name="sales.order.print">
            <action method="addItemRender"><type>bundle</type><block>bundle/sales_order_items_renderer</block><template>bundle/sales/order/items/renderer.phtml</template></action>
        </reference>
    </sales_guest_print>

    <sales_guest_printinvoice>
        <reference name="sales.order.print.invoice">
            <action method="addItemRender"><type>bundle</type><block>bundle/sales_order_items_renderer</block><template>bundle/sales/order/invoice/items/renderer.phtml</template></action>
        </reference>
    </sales_guest_printinvoice>

    <sales_guest_printshipment>
        <reference name="sales.order.print.shipment">
            <action method="addItemRender"><type>bundle</type><block>bundle/sales_order_items_renderer</block><template>bundle/sales/order/shipment/items/renderer.phtml</template></action>
        </reference>
    </sales_guest_printshipment>

    <sales_guest_printcreditmemo>
        <reference name="sales.order.print.creditmemo">
            <action method="addItemRender"><type>bundle</type><block>bundle/sales_order_items_renderer</block><template>bundle/sales/order/creditmemo/items/renderer.phtml</template></action>
        </reference>
    </sales_guest_printcreditmemo>

<!--
Emails
-->
    <sales_email_order_items>
        <reference name="items">
            <action method="addItemRender"><type>bundle</type><block>bundle/sales_order_items_renderer</block><template>bundle/email/order/items/order/default.phtml</template></action>
        </reference>
    </sales_email_order_items>

    <sales_email_order_invoice_items>
        <reference name="items">
            <action method="addItemRender"><type>bundle</type><block>bundle/sales_order_items_renderer</block><template>bundle/email/order/items/invoice/default.phtml</template></action>
        </reference>
    </sales_email_order_invoice_items>


    <sales_email_order_shipment_items>
        <reference name="items">
            <action method="addItemRender"><type>bundle</type><block>bundle/sales_order_items_renderer</block><template>bundle/email/order/items/shipment/default.phtml</template></action>
        </reference>
    </sales_email_order_shipment_items>

    <sales_email_order_creditmemo_items>
        <reference name="items">
            <action method="addItemRender"><type>bundle</type><block>bundle/sales_order_items_renderer</block><template>bundle/email/order/items/creditmemo/default.phtml</template></action>
        </reference>
    </sales_email_order_creditmemo_items>

<!--
RSS
-->
    <rss_catalog_category>
        <reference name="rss.catalog.category">
            <action method="addPriceBlockType"><type>bundle</type><block>bundle/catalog_product_price</block><template>bundle/rss/catalog/product/price.phtml</template></action>
        </reference>
    </rss_catalog_category>
    <rss_catalog_new>
        <reference name="rss.catalog.new">
            <action method="addPriceBlockType"><type>bundle</type><block>bundle/catalog_product_price</block><template>bundle/rss/catalog/product/price.phtml</template></action>
        </reference>
    </rss_catalog_new>
    <rss_catalog_tag>
        <reference name="rss.catalog.tag">
            <action method="addPriceBlockType"><type>bundle</type><block>bundle/catalog_product_price</block><template>bundle/rss/catalog/product/price.phtml</template></action>
        </reference>
    </rss_catalog_tag>
</layout>
