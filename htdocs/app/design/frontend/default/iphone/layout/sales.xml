<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

-->
<layout version="0.1.0">

<!--
Customer account pages, rendered for all tabs in dashboard
-->


    <customer_logged_in>
        <reference name="right">
            <block type="sales/reorder_sidebar" name="sale.reorder.sidebar" as="reorder" template="sales/reorder/sidebar.phtml"/>
        </reference>
    </customer_logged_in>
    <checkout_onepage_index>
        <remove name="sale.reorder.sidebar"/>
    </checkout_onepage_index>
    <checkout_onepage_reorder>
        <reference name="right">
            <action method="unsetChild"><name>reorder</name></action>
        </reference>
    </checkout_onepage_reorder>

    <customer_account>
        <!-- Mage_Sales -->
        <reference name="customer_account_navigation">
            <action method="addLink" translate="label" module="sales"><name>orders</name><path>sales/order/history/</path><label>My Orders</label></action>
        </reference>
        <reference name="left">
            <block type="sales/reorder_sidebar" name="sale.reorder.sidebar" as="reorder" template="sales/reorder/sidebar.phtml"/>
        </reference>

    </customer_account>

<!--
Customer account home dashboard layout
-->

    <customer_account_index>
        <!-- Mage_Sales -->
        <!--remove name="customer_account_dashboard_top"/-->
        <reference name="customer_account_dashboard">
            <block type="sales/order_recent" name="customer_account_dashboard_top" as="top" template="sales/order/recent.phtml"/>
        </reference>

    </customer_account_index>

    <sales_order_history translate="label">
        <label>Customer My Account Order History</label>
        <update handle="customer_account"/>
        <reference name="my.account.wrapper">
            <block type="sales/order_history" name="sales.order.history">
                <block type="core/text_list" name="sales.order.history.info" as="info" translate="label">
                    <label>Order History Info</label>
                </block>
            </block>
            <block type="customer/account_dashboard" name="customer.account.link.back" template="customer/account/link/back.phtml"/>
        </reference>
    </sales_order_history>


    <sales_order_view translate="label">
        <label>Customer My Account Order View</label>
        <update handle="customer_account"/>
        <reference name="my.account.wrapper">
            <block type="sales/order_info" as="info" name="sales.order.info">
                <block type="sales/order_info_buttons" as="buttons" name="sales.order.info.buttons" />
            </block>
            <block type="sales/order_view" name="sales.order.view">
                <block type="sales/order_items" name="order_items" template="sales/order/items.phtml">
                    <action method="addItemRender"><type>default</type><block>sales/order_item_renderer_default</block><template>sales/order/items/renderer/default.phtml</template></action>
                    <action method="addItemRender"><type>grouped</type><block>sales/order_item_renderer_grouped</block><template>sales/order/items/renderer/default.phtml</template></action>
                    <block type="sales/order_totals" name="order_totals" template="sales/order/totals.phtml">
                        <action method="setLabelProperties"><value>colspan="4" class="a-right"</value></action>
                        <action method="setValueProperties"><value>class="last a-right"</value></action>
                        <block type="tax/sales_order_tax" name="tax" template="tax/order/tax.phtml" />
                    </block>
                </block>
            </block>
        </reference>
        <reference name="sales.order.info">
            <action method="addLink" translate="label" module="sales"><name>view</name><path></path><label>Order Information</label></action>
            <action method="addLink" translate="label" module="sales"><name>invoice</name><path>*/*/invoice</path><label>Invoices</label></action>
            <action method="addLink" translate="label" module="sales"><name>shipment</name><path>*/*/shipment</path><label>Shipments</label></action>
            <action method="addLink" translate="label" module="sales"><name>creditmemo</name><path>*/*/creditmemo</path><label>Refunds</label></action>
        </reference>
        <block type="core/text_list" name="additional.product.info" translate="label">
            <label>Additional Product Info</label>
        </block>
    </sales_order_view>

    <sales_order_invoice translate="label">
        <label>Customer My Account Order Invoice View</label>
        <update handle="customer_account"/>
        <reference name="my.account.wrapper">
            <block type="sales/order_info" as="info" name="sales.order.info">
                <block type="sales/order_info_buttons" as="buttons" name="sales.order.info.buttons" />
            </block>
            <block type="sales/order_invoice" name="sales.order.invoice">
                <block type="sales/order_invoice_items" name="invoice_items" template="sales/order/invoice/items.phtml">
                    <action method="addItemRender"><type>default</type><block>sales/order_item_renderer_default</block><template>sales/order/invoice/items/renderer/default.phtml</template></action>
                    <action method="addItemRender"><type>grouped</type><block>sales/order_item_renderer_grouped</block><template>sales/order/invoice/items/renderer/default.phtml</template></action>
                    <block type="sales/order_invoice_totals" name="invoice_totals" template="sales/order/totals.phtml">
                        <action method="setLabelProperties"><value>colspan="4" class="a-right"</value></action>
                        <action method="setValueProperties"><value>class="last a-right"</value></action>
                        <block type="tax/sales_order_tax" name="tax" template="tax/order/tax.phtml" />
                    </block>
                    <block type="sales/order_comments" name="invoice_comments" template="sales/order/comments.phtml" />
                </block>
            </block>
        </reference>
        <reference name="sales.order.info">
            <action method="addLink" translate="label" module="sales"><name>view</name><path>*/*/view</path><label>Order Information</label></action>
            <action method="addLink" translate="label" module="sales"><name>invoice</name><path></path><label>Invoices</label></action>
            <action method="addLink" translate="label" module="sales"><name>shipment</name><path>*/*/shipment</path><label>Shipments</label></action>
            <action method="addLink" translate="label" module="sales"><name>creditmemo</name><path>*/*/creditmemo</path><label>Refunds</label></action>
        </reference>
        <block type="core/text_list" name="additional.product.info" />
    </sales_order_invoice>

    <sales_order_shipment translate="label">
        <label>Customer My Account Order Shipment View</label>
        <update handle="customer_account"/>
        <reference name="my.account.wrapper">
            <block type="sales/order_info" as="info" name="sales.order.info">
                <block type="sales/order_info_buttons" as="buttons" name="sales.order.info.buttons" />
            </block>
            <block type="sales/order_shipment" name="sales.order.shipment">
                <block type="sales/order_shipment_items" name="shipment_items" template="sales/order/shipment/items.phtml">
                    <action method="addItemRender"><type>default</type><block>sales/order_item_renderer_default</block><template>sales/order/shipment/items/renderer/default.phtml</template></action>
                    <block type="sales/order_comments" name="shipment_comments" template="sales/order/comments.phtml" />
                </block>
            </block>
        </reference>
        <reference name="sales.order.info">
            <action method="addLink" translate="label" module="sales"><name>view</name><path>*/*/view</path><label>Order Information</label></action>
            <action method="addLink" translate="label" module="sales"><name>invoice</name><path>*/*/invoice</path><label>Invoices</label></action>
            <action method="addLink" translate="label" module="sales"><name>shipment</name><path></path><label>Shipments</label></action>
            <action method="addLink" translate="label" module="sales"><name>creditmemo</name><path>*/*/creditmemo</path><label>Refunds</label></action>
        </reference>
        <block type="core/text_list" name="additional.product.info" />
    </sales_order_shipment>

    <sales_order_creditmemo translate="label">
        <label>Customer My Account Order Creditmemo View</label>
        <update handle="customer_account"/>
        <reference name="my.account.wrapper">
            <block type="sales/order_info" as="info" name="sales.order.info">
                <block type="sales/order_info_buttons" as="buttons" name="sales.order.info.buttons" />
            </block>
            <block type="sales/order_creditmemo" name="sales.order.creditmemo">
                <block type="sales/order_creditmemo_items" name="creditmemo_items" template="sales/order/creditmemo/items.phtml">
                    <action method="addItemRender"><type>default</type><block>sales/order_item_renderer_default</block><template>sales/order/creditmemo/items/renderer/default.phtml</template></action>
                    <action method="addItemRender"><type>grouped</type><block>sales/order_item_renderer_grouped</block><template>sales/order/creditmemo/items/renderer/default.phtml</template></action>
                    <block type="sales/order_creditmemo_totals" name="creditmemo_totals" template="sales/order/totals.phtml">
                        <action method="setLabelProperties"><value>colspan="6" class="a-right"</value></action>
                        <action method="setValueProperties"><value>class="a-right"</value></action>
                        <block type="tax/sales_order_tax" name="tax" template="tax/order/tax.phtml"/>
                    </block>
                    <block type="sales/order_comments" name="creditmemo_comments" template="sales/order/comments.phtml" />
                </block>
            </block>
        </reference>
        <reference name="sales.order.info">
            <action method="addLink" translate="label" module="sales"><name>view</name><path>*/*/view</path><label>Order Information</label></action>
            <action method="addLink" translate="label" module="sales"><name>invoice</name><path>*/*/invoice</path><label>Invoices</label></action>
            <action method="addLink" translate="label" module="sales"><name>shipment</name><path>*/*/shipment</path><label>Shipments</label></action>
            <action method="addLink" translate="label" module="sales"><name>creditmemo</name><path></path><label>Refunds</label></action>
        </reference>
        <block type="core/text_list" name="additional.product.info" />
    </sales_order_creditmemo>

    <sales_order_reorder>
        <update handle="customer_account"/>
        <reference name="content">
            <block type="sales/order_view" name="sales.order.view"/>
        </reference>
    </sales_order_reorder>

    <sales_order_print translate="label">
        <label>Sales Order Print View</label>
        <reference name="content">
            <block type="sales/order_print" name="sales.order.print" template="sales/order/print.phtml">
                <action method="addItemRender"><type>default</type><block>sales/order_item_renderer_default</block><template>sales/order/items/renderer/default.phtml</template></action>
                <action method="addItemRender"><type>grouped</type><block>sales/order_item_renderer_grouped</block><template>sales/order/items/renderer/default.phtml</template></action>
                <block type="sales/order_totals" name="order_totals" template="sales/order/totals.phtml">
                    <action method="setLabelProperties"><value>colspan="4" class="a-right"</value></action>
                    <action method="setValueProperties"><value>class="last a-right"</value></action>
                    <block type="tax/sales_order_tax" name="tax" template="tax/order/tax.phtml">
                        <action method="setIsPlaneMode"><value>1</value></action>
                    </block>
                </block>
            </block>
        </reference>
        <block type="core/text_list" name="additional.product.info" />
    </sales_order_print>

    <sales_order_printinvoice translate="label">
        <label>Sales Invoice Print View</label>
        <reference name="content">
            <block type="sales/order_print_invoice" name="sales.order.print.invoice" template="sales/order/print/invoice.phtml">
                <action method="addItemRender"><type>default</type><block>sales/order_item_renderer_default</block><template>sales/order/invoice/items/renderer/default.phtml</template></action>
                <action method="addItemRender"><type>grouped</type><block>sales/order_item_renderer_grouped</block><template>sales/order/invoice/items/renderer/default.phtml</template></action>
                <block type="sales/order_invoice_totals" name="invoice_totals" template="sales/order/totals.phtml">
                    <action method="setLabelProperties"><value>colspan="4" class="a-right"</value></action>
                    <action method="setValueProperties"><value>class="last a-right"</value></action>
                    <block type="tax/sales_order_tax" name="tax" template="tax/order/tax.phtml">
                        <action method="setIsPlaneMode"><value>1</value></action>
                    </block>
                </block>
            </block>
        </reference>
        <block type="core/text_list" name="additional.product.info" />
    </sales_order_printinvoice>

    <sales_order_printshipment translate="label">
        <label>Sales Shipment Print View</label>
        <reference name="content">
            <block type="sales/order_print_shipment" name="sales.order.print.shipment" template="sales/order/print/shipment.phtml">
                <action method="addItemRender"><type>default</type><block>sales/order_item_renderer_default</block><template>sales/order/shipment/items/renderer/default.phtml</template></action>
            </block>
        </reference>
        <block type="core/text_list" name="additional.product.info" />
    </sales_order_printshipment>

    <sales_order_printcreditmemo>
        <reference name="content">
            <block type="sales/order_print_creditmemo" name="sales.order.print.creditmemo" template="sales/order/print/creditmemo.phtml">
                <action method="addItemRender"><type>default</type><block>sales/order_item_renderer_default</block><template>sales/order/creditmemo/items/renderer/default.phtml</template></action>
                <action method="addItemRender"><type>grouped</type><block>sales/order_item_renderer_grouped</block><template>sales/order/creditmemo/items/renderer/default.phtml</template></action>
                <block type="sales/order_creditmemo_totals" name="creditmemo_totals" template="sales/order/totals.phtml">
                    <action method="setLabelProperties"><value>colspan="6" class="a-right"</value></action>
                    <action method="setValueProperties"><value>class="a-right"</value></action>
                    <block type="tax/sales_order_tax" name="tax" template="tax/order/tax.phtml">
                        <action method="setIsPlaneMode"><value>1</value></action>
                    </block>
                </block>
            </block>
        </reference>
        <block type="core/text_list" name="additional.product.info" />
    </sales_order_printcreditmemo>

<!--
Email layouts section
-->
    <sales_email_order_items>
        <block type="sales/order_email_items" name="items" template="email/order/items.phtml">
            <action method="addItemRender"><type>default</type><block>sales/order_email_items_order_default</block><template>email/order/items/order/default.phtml</template></action>
            <action method="addItemRender"><type>grouped</type><block>sales/order_email_items_order_grouped</block><template>email/order/items/order/default.phtml</template></action>
            <block type="sales/order_totals" name="order_totals" template="sales/order/totals.phtml">
                <action method="setLabelProperties"><value>colspan="3" align="right" style="padding:3px 9px"</value></action>
                <action method="setValueProperties"><value>align="right" style="padding:3px 9px"</value></action>
                <block type="tax/sales_order_tax" name="tax" template="tax/order/tax.phtml">
                    <action method="setIsPlaneMode"><value>1</value></action>
                </block>
            </block>
        </block>
        <block type="core/text_list" name="additional.product.info" />
    </sales_email_order_items>

    <sales_email_order_invoice_items>
        <block type="sales/order_email_invoice_items" name="items" template="email/order/invoice/items.phtml">
            <action method="addItemRender"><type>default</type><block>sales/order_email_items_default</block><template>email/order/items/invoice/default.phtml</template></action>
            <action method="addItemRender"><type>grouped</type><block>sales/order_email_items_order_grouped</block><template>email/order/items/invoice/default.phtml</template></action>
            <block type="sales/order_invoice_totals" name="invoice_totals" template="sales/order/totals.phtml">
                <action method="setLabelProperties"><value>colspan="3" align="right" style="padding:3px 9px"</value></action>
                <action method="setValueProperties"><value>align="right" style="padding:3px 9px"</value></action>
                <block type="tax/sales_order_tax" name="tax" template="tax/order/tax.phtml">
                    <action method="setIsPlaneMode"><value>1</value></action>
                </block>
            </block>
        </block>
        <block type="core/text_list" name="additional.product.info" />
    </sales_email_order_invoice_items>

    <sales_email_order_shipment_items>
        <block type="sales/order_email_shipment_items" name="items" template="email/order/shipment/items.phtml">
            <action method="addItemRender"><type>default</type><block>sales/order_email_items_default</block><template>email/order/items/shipment/default.phtml</template></action>
        </block>
        <block type="core/text_list" name="additional.product.info" />
    </sales_email_order_shipment_items>

    <sales_email_order_creditmemo_items>
        <block type="sales/order_email_creditmemo_items" name="items" template="email/order/creditmemo/items.phtml">
            <action method="addItemRender"><type>default</type><block>sales/order_email_items_default</block><template>email/order/items/creditmemo/default.phtml</template></action>
            <action method="addItemRender"><type>grouped</type><block>sales/order_email_items_order_grouped</block><template>email/order/items/creditmemo/default.phtml</template></action>
            <block type="sales/order_creditmemo_totals" name="creditmemo_totals" template="sales/order/totals.phtml">
                <action method="setLabelProperties"><value>colspan="3" align="right" style="padding:3px 9px"</value></action>
                <action method="setValueProperties"><value>align="right" style="padding:3px 9px"</value></action>
                <block type="tax/sales_order_tax" name="tax" template="tax/order/tax.phtml">
                    <action method="setIsPlaneMode"><value>1</value></action>
                </block>
            </block>
        </block>
        <block type="core/text_list" name="additional.product.info" />
    </sales_email_order_creditmemo_items>

<!--
Guest
-->

    <sales_guest_form translate="label">
        <label>Returns</label>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="content">
            <block type="sales/widget_guest_form" name="guest.form" template="sales/guest/form.phtml"/>
        </reference>
    </sales_guest_form>


    <sales_guest_view translate="label">
        <label>Customer My Account Order View</label>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="content">
            <block type="sales/order_info" as="info" name="sales.order.info">
                <block type="sales/order_info_buttons" as="buttons" name="sales.order.info.buttons" />
            </block>
            <block type="sales/order_view" name="sales.order.view">
                <block type="sales/order_items" name="order_items" template="sales/order/items.phtml">
                    <action method="addItemRender"><type>default</type><block>sales/order_item_renderer_default</block><template>sales/order/items/renderer/default.phtml</template></action>
                    <action method="addItemRender"><type>grouped</type><block>sales/order_item_renderer_grouped</block><template>sales/order/items/renderer/default.phtml</template></action>
                    <block type="sales/order_totals" name="order_totals" template="sales/order/totals.phtml">
                        <action method="setLabelProperties"><value>colspan="4" class="a-right"</value></action>
                        <action method="setValueProperties"><value>class="last a-right"</value></action>
                        <block type="tax/sales_order_tax" name="tax" template="tax/order/tax.phtml" />
                    </block>
                </block>
            </block>
        </reference>
        <reference name="sales.order.info">
            <action method="addLink" translate="label" module="sales"><name>view</name><path></path><label>Order Information</label></action>
            <action method="addLink" translate="label" module="sales"><name>invoice</name><path>*/*/invoice</path><label>Invoices</label></action>
            <action method="addLink" translate="label" module="sales"><name>shipment</name><path>*/*/shipment</path><label>Shipments</label></action>
            <action method="addLink" translate="label" module="sales"><name>creditmemo</name><path>*/*/creditmemo</path><label>Refunds</label></action>
        </reference>
    </sales_guest_view>

    <sales_guest_invoice translate="label">
        <label>Customer My Account Order Invoice View</label>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="content">
            <block type="sales/order_info" as="info" name="sales.order.info">
                <block type="sales/order_info_buttons" as="buttons" name="sales.order.info.buttons" />
            </block>
            <block type="sales/order_invoice" name="sales.order.invoice">
                <block type="sales/order_invoice_items" name="invoice_items" template="sales/order/invoice/items.phtml">
                    <action method="addItemRender"><type>default</type><block>sales/order_item_renderer_default</block><template>sales/order/invoice/items/renderer/default.phtml</template></action>
                    <action method="addItemRender"><type>grouped</type><block>sales/order_item_renderer_grouped</block><template>sales/order/invoice/items/renderer/default.phtml</template></action>
                    <block type="sales/order_invoice_totals" name="invoice_totals" template="sales/order/totals.phtml">
                        <action method="setLabelProperties"><value>colspan="4" class="a-right"</value></action>
                        <action method="setValueProperties"><value>class="last a-right"</value></action>
                        <block type="tax/sales_order_tax" name="tax" template="tax/order/tax.phtml" />
                    </block>
                    <block type="sales/order_comments" name="invoice_comments" template="sales/order/comments.phtml" />
                </block>
            </block>
        </reference>
        <reference name="sales.order.info">
            <action method="addLink" translate="label" module="sales"><name>view</name><path>*/*/view</path><label>Order Information</label></action>
            <action method="addLink" translate="label" module="sales"><name>invoice</name><path></path><label>Invoices</label></action>
            <action method="addLink" translate="label" module="sales"><name>shipment</name><path>*/*/shipment</path><label>Shipments</label></action>
            <action method="addLink" translate="label" module="sales"><name>creditmemo</name><path>*/*/creditmemo</path><label>Refunds</label></action>
        </reference>
    </sales_guest_invoice>

    <sales_guest_shipment translate="label">
        <label>Customer My Account Order Shipment View</label>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="content">
            <block type="sales/order_info" as="info" name="sales.order.info">
                <block type="sales/order_info_buttons" as="buttons" name="sales.order.info.buttons" />
            </block>
            <block type="sales/order_shipment" name="sales.order.shipment">
                <block type="sales/order_shipment_items" name="shipment_items" template="sales/order/shipment/items.phtml">
                    <action method="addItemRender"><type>default</type><block>sales/order_item_renderer_default</block><template>sales/order/shipment/items/renderer/default.phtml</template></action>
                    <block type="sales/order_comments" name="shipment_comments" template="sales/order/comments.phtml" />
                </block>
            </block>
        </reference>
        <reference name="sales.order.info">
            <action method="addLink" translate="label" module="sales"><name>view</name><path>*/*/view</path><label>Order Information</label></action>
            <action method="addLink" translate="label" module="sales"><name>invoice</name><path>*/*/invoice</path><label>Invoices</label></action>
            <action method="addLink" translate="label" module="sales"><name>shipment</name><path></path><label>Shipments</label></action>
            <action method="addLink" translate="label" module="sales"><name>creditmemo</name><path>*/*/creditmemo</path><label>Refunds</label></action>
        </reference>
    </sales_guest_shipment>

    <sales_guest_creditmemo translate="label">
        <label>Customer My Account Order Creditmemo View</label>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="content">
            <block type="sales/order_info" as="info" name="sales.order.info">
                <block type="sales/order_info_buttons" as="buttons" name="sales.order.info.buttons" />
            </block>
            <block type="sales/order_creditmemo" name="sales.order.creditmemo">
                <block type="sales/order_creditmemo_items" name="creditmemo_items" template="sales/order/creditmemo/items.phtml">
                    <action method="addItemRender"><type>default</type><block>sales/order_item_renderer_default</block><template>sales/order/creditmemo/items/renderer/default.phtml</template></action>
                    <action method="addItemRender"><type>grouped</type><block>sales/order_item_renderer_grouped</block><template>sales/order/creditmemo/items/renderer/default.phtml</template></action>
                    <block type="sales/order_creditmemo_totals" name="creditmemo_totals" template="sales/order/totals.phtml">
                        <action method="setLabelProperties"><value>colspan="6" class="a-right"</value></action>
                        <action method="setValueProperties"><value>class="a-right"</value></action>
                        <block type="tax/sales_order_tax" name="tax" template="tax/order/tax.phtml"/>
                    </block>
                    <block type="sales/order_comments" name="creditmemo_comments" template="sales/order/comments.phtml" />
                </block>
            </block>
        </reference>
        <reference name="sales.order.info">
            <action method="addLink" translate="label" module="sales"><name>view</name><path>*/*/view</path><label>Order Information</label></action>
            <action method="addLink" translate="label" module="sales"><name>invoice</name><path>*/*/invoice</path><label>Invoices</label></action>
            <action method="addLink" translate="label" module="sales"><name>shipment</name><path>*/*/shipment</path><label>Shipments</label></action>
            <action method="addLink" translate="label" module="sales"><name>creditmemo</name><path></path><label>Refunds</label></action>
        </reference>
    </sales_guest_creditmemo>

    <sales_guest_reorder>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="content">
            <block type="sales/order_view" name="sales.order.view"/>
        </reference>
    </sales_guest_reorder>

    <sales_guest_print translate="label">
        <label>Sales Order Print View</label>
        <reference name="content">
            <block type="sales/order_print" name="sales.order.print" template="sales/order/print.phtml">
                <action method="addItemRender"><type>default</type><block>sales/order_item_renderer_default</block><template>sales/order/items/renderer/default.phtml</template></action>
                <action method="addItemRender"><type>grouped</type><block>sales/order_item_renderer_grouped</block><template>sales/order/items/renderer/default.phtml</template></action>
                <block type="sales/order_totals" name="order_totals" template="sales/order/totals.phtml">
                    <action method="setLabelProperties"><value>colspan="4" class="a-right"</value></action>
                    <action method="setValueProperties"><value>class="last a-right"</value></action>
                    <block type="tax/sales_order_tax" name="tax" template="tax/order/tax.phtml">
                        <action method="setIsPlaneMode"><value>1</value></action>
                    </block>
                </block>
            </block>
        </reference>
    </sales_guest_print>

    <sales_guest_printinvoice translate="label">
        <label>Sales Invoice Print View</label>
        <reference name="content">
            <block type="sales/order_print_invoice" name="sales.order.print.invoice" template="sales/order/print/invoice.phtml">
                <action method="addItemRender"><type>default</type><block>sales/order_item_renderer_default</block><template>sales/order/invoice/items/renderer/default.phtml</template></action>
                <action method="addItemRender"><type>grouped</type><block>sales/order_item_renderer_grouped</block><template>sales/order/invoice/items/renderer/default.phtml</template></action>
                <block type="sales/order_invoice_totals" name="invoice_totals" template="sales/order/totals.phtml">
                    <action method="setLabelProperties"><value>colspan="4" class="a-right"</value></action>
                    <action method="setValueProperties"><value>class="last a-right"</value></action>
                    <block type="tax/sales_order_tax" name="tax" template="tax/order/tax.phtml">
                        <action method="setIsPlaneMode"><value>1</value></action>
                    </block>
                </block>
            </block>
        </reference>
    </sales_guest_printinvoice>

    <sales_guest_printshipment translate="label">
        <label>Sales Shipment Print View</label>
        <reference name="content">
            <block type="sales/order_print_shipment" name="sales.order.print.shipment" template="sales/order/print/shipment.phtml">
                <action method="addItemRender"><type>default</type><block>sales/order_item_renderer_default</block><template>sales/order/shipment/items/renderer/default.phtml</template></action>
            </block>
        </reference>
    </sales_guest_printshipment>

    <sales_guest_printcreditmemo>
        <reference name="content">
            <block type="sales/order_print_creditmemo" name="sales.order.print.creditmemo" template="sales/order/print/creditmemo.phtml">
                <action method="addItemRender"><type>default</type><block>sales/order_item_renderer_default</block><template>sales/order/creditmemo/items/renderer/default.phtml</template></action>
                <action method="addItemRender"><type>grouped</type><block>sales/order_item_renderer_grouped</block><template>sales/order/creditmemo/items/renderer/default.phtml</template></action>
                <block type="sales/order_creditmemo_totals" name="creditmemo_totals" template="sales/order/totals.phtml">
                    <action method="setLabelProperties"><value>colspan="6" class="a-right"</value></action>
                    <action method="setValueProperties"><value>class="a-right"</value></action>
                    <block type="tax/sales_order_tax" name="tax" template="tax/order/tax.phtml">
                        <action method="setIsPlaneMode"><value>1</value></action>
                    </block>
                </block>
            </block>
        </reference>
    </sales_guest_printcreditmemo>
</layout>
