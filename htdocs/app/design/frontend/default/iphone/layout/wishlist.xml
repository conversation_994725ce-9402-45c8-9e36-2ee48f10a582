<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

-->
<layout version="0.1.0">

<!--
Default layout, loads most of the pages
-->

    <default>
        <reference name="top.links">
            <block type="wishlist/links" name="wishlist_link" />
            <action method="addLinkBlock"><blockName>wishlist_link</blockName></action>
        </reference>

        <!-- Mage_Wishlist -->
        <reference name="right">
            <block type="wishlist/customer_sidebar" name="wishlist_sidebar" as="wishlist" after="cart_sidebar" template="wishlist/sidebar.phtml" />
        </reference>
    </default>

<!--
Customer account pages, rendered for all tabs in dashboard
-->

    <customer_account>
        <!-- Mage_Wishlist -->
        <reference name="customer_account_navigation">
            <action method="addLink" translate="label" module="wishlist" ifconfig="wishlist/general/active"><name>wishlist</name><path>wishlist/</path><label>My Wishlist</label></action>
        </reference>
    </customer_account>

<!--
Customer account home dashboard layout
-->

    <customer_account_index>

        <reference name="right">
            <action method="unsetChild"><name>wishlist</name></action>
        </reference>
    </customer_account_index>

<!--
Wishlist pages
-->

    <wishlist_index_index translate="label">
        <label>Customer My Account My Wishlist</label>
        <!-- Mage_Wishlist -->
        <update handle="customer_account"/>
        <reference name="my.account.wrapper">
            <block type="wishlist/customer_wishlist" name="customer.wishlist" template="wishlist/view.phtml">
                <action method="setTitle" translate="title">
                    <title>My Wishlist</title>
                </action>
                <block type="wishlist/customer_wishlist_items" name="customer.wishlist.items" as="items" template="wishlist/item/list.phtml">
                    <block type="wishlist/customer_wishlist_item_column_remove" name="customer.wishlist.item.remove" template="wishlist/item/column/remove.phtml"></block>
                    <block type="wishlist/customer_wishlist_item_column_image" name="customer.wishlist.item.image" template="wishlist/item/column/image.phtml"></block>
                    <block type="wishlist/customer_wishlist_item_column_cart" name="customer.wishlist.item.cart" template="wishlist/item/column/cart.phtml">
                        <action method="setTitle" translate="title">
                            <title>Add to Cart</title>
                        </action>
                        <block type="wishlist/customer_wishlist_item_options" name="customer.wishlist.item.options"/>
                    </block>
                    <block type="wishlist/customer_wishlist_item_column_comment" name="customer.wishlist.item.info" template="wishlist/item/column/info.phtml">
                        <action method="setTitle" translate="title">
                            <title>Product Details and Comment</title>
                        </action>
                    </block>
                </block>
                <block type="core/text_list" name="customer.wishlist.buttons" as="control_buttons">
                    <block type="wishlist/customer_wishlist_button" name="customer.wishlist.button.share" template="wishlist/button/share.phtml" />
                    <block type="wishlist/customer_wishlist_button" name="customer.wishlist.button.toCart" template="wishlist/button/tocart.phtml" />
                    <block type="wishlist/customer_wishlist_button" name="customer.wishlist.button.update" template="wishlist/button/update.phtml" />
                </block>
            </block>
        </reference>
        <reference name="right">
            <action method="unsetChild"><name>wishlist_customer_sidebar</name></action>
        </reference>
    </wishlist_index_index>

    <wishlist_index_share translate="label">
        <label>Customer My Account Wishlist Sharing Form</label>
        <!-- Mage_Wishlist -->
        <update handle="customer_account" />
        <reference name="my.account.wrapper">
            <block type="wishlist/customer_sharing" name="wishlist.sharing" template="wishlist/sharing.phtml" />
        </reference>
        <reference name="right">
            <action method="unsetChild"><name>wishlist_customer_sidebar</name></action>
        </reference>
    </wishlist_index_share>

    <wishlist_index_configure translate="label">
        <label>Configure Wishlist Item</label>
        <update handle="catalog_product_view" />
        <reference name="product.info">
            <block type="wishlist/item_configure" name="product.info.addto" as="addto" template="wishlist/item/configure/addto.phtml" />
        </reference>
        <reference name="product.info.options.wrapper.bottom">
            <action method="unsetChild"><name>product.info.addto</name></action>
            <action method="append"><block>product.info.addto</block></action>
        </reference>
    </wishlist_index_configure>

    <wishlist_shared_index translate="label">
        <label>Customer Shared Wishlist View</label>
        <!-- Mage_Wishlist -->
        <reference name="content">
            <block type="wishlist/share_wishlist" name="customer.wishlist" template="wishlist/shared.phtml" />
        </reference>
    </wishlist_shared_index>
</layout>
