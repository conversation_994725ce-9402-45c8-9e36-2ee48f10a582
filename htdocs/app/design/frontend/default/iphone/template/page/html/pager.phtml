<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * Pager template
 *
 * @see Mage_Page_Block_Html_Pager
 */
?>
<?php if($this->getCollection()->getSize()): ?>

    <?php if($this->getUseContainer()): ?>
    <div class="pager">
    <?php endif ?>

    <?php if($this->getShowAmounts()): ?>
    <p class="amount">

        <?php if($this->getLastPageNum()>1): ?>
            <?php echo $this->__('Items %s to %s of %s total', $this->getFirstNum(), $this->getLastNum(), $this->getTotalNum()) ?>
        <?php else: ?>
            <strong><?php echo $this->__('%s Item(s)', $this->getTotalNum()) ?></strong>
        <?php endif; ?>
    </p>
    <?php endif ?>

    <?php if($this->getShowPerPage()): ?>
    <div class="limiter">
        <label><?php echo $this->__('Show') ?></label>
        <select onchange="setLocation(this.value)">
        <?php foreach ($this->getAvailableLimit() as  $_key=>$_limit): ?>
            <option value="<?php echo $this->getLimitUrl($_key) ?>"<?php if($this->isLimitCurrent($_key)): ?> selected="selected"<?php endif ?>>
                <?php echo $_limit ?>
            </option>
        <?php endforeach; ?>
        </select> <?php echo $this->__('per page') ?>
    </div>
    <?php endif ?>

    <?php if($this->getLastPageNum()>1): ?>
    <div class="pages">
        <strong><?php echo $this->__('Page') ?></strong>
        <select onchange="setLocation(this.value)">
            <?php if ($this->canShowFirst()): ?>
                <option value="<?php echo $this->getFirstPageUrl() ?>">1</option>
            <?php endif;?>

            <?php if ($this->canShowPreviousJump()): ?>
                <option value="<?php echo $this->getPreviousJumpUrl() ?>">...</option>
            <?php endif;?>

            <?php foreach ($this->getFramePages() as $_page): ?>
                <?php if ($this->isPageCurrent($_page)): ?>
                    <option value="" class="current" selected="selected"><?php echo $_page ?></li>
                <?php else: ?>
                    <option value="<?php echo $this->getPageUrl($_page) ?>"><?php echo $_page ?></option>
                <?php endif;?>
            <?php endforeach;?>


            <?php if ($this->canShowNextJump()): ?>
                <option value="<?php echo $this->getNextJumpUrl() ?>">...</option>
            <?php endif;?>

            <?php if ($this->canShowLast()): ?>
                <option value="<?php echo $this->getLastPageUrl() ?>"><?php echo $this->getLastPageNum() ?></option>
            <?php endif;?>
        </select>

        <?php if (!$this->isFirstPage()): ?>
            <a class="prev-page" href="<?php echo $this->getPreviousPageUrl() ?>" title="<?php echo $this->__('Previous') ?>"></a>
        <?php else: ?>
            <span class="prev-page disabled">&nbsp;</span>
        <?php endif;?>

        <?php if (!$this->isLastPage()): ?>
                <a class="next-page" href="<?php echo $this->getNextPageUrl() ?>" title="<?php echo $this->__('Next') ?>">&nbsp;</a>
        <?php else: ?>
                <span class="next-page disabled">&nbsp;</span>
        <?php endif;?>

    </div>
    <?php endif; ?>

    <?php if($this->getUseContainer()): ?>
    </div>
    <?php endif ?>

<?php endif ?>
