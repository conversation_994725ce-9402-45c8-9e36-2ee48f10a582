<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/**
 * @var Mage_Page_Block_Html_Header $this
 */
?>
<?php $_cartItems = $this->helper('checkout/cart')->getSummaryCount(); ?>
<div class="header-bg">
    <a class="header-logo" href="<?php echo $this->getUrl('') ?>"></a>
</div>
<header>
    <div class="menu-wrapper">
        <dl id="menu">
            <dt class="menu dropdown"><a href="#">Menu</a></dt>
            <dd class="menu-box">
                <?php echo $this->getChildHtml('topLinks') ?>
                <?php echo $this->getChildHtml('checkoutLinks') ?>
                <?php echo $this->getChildHtml('accountLinks') ?>
            </dd>
            <dt class="cart-icon <?php echo $this->getInCart() ? 'active' : '' ?>">
                <a href="<?php echo $this->getUrl('checkout/cart'); ?>">Cart</a>
                <?php echo $_cartItems > 0 ? '<span>'.$_cartItems.'</span>' : ''; ?>
            </dt>
            <dd></dd>
        </dl>
        <div class="search"><?php echo $this->getChildHtml('topSearch') ?></div>
    </div>
</header>
<?php echo $this->getChildHtml('topCart') ?>
