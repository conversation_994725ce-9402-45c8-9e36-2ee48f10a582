<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * Shopping cart template
 *
 * @see Mage_Checkout_Block_Cart
 */
?>
<div class="cart">
    <?php echo $this->getChildHtml('form_before') ?>
    <?php echo $this->getMessagesBlock()->toHtml() ?>
    <?php if(!$this->hasError() && count($this->getItems()) > 1): ?>
        <ul class="checkout-types">
        <?php foreach ($this->getMethods('methods') as $method): ?>
            <?php $methodHtml = $this->getMethodHtml($method); ?>
            <?php if ($methodHtml): ?>
            <li><?php echo $methodHtml; ?></li>
            <?php endif; ?>
        <?php endforeach; ?>
        </ul>
    <?php endif; ?>
    <form action="<?php echo $this->getFormActionUrl() ?>" method="post">
        <?php echo $this->getBlockHtml('formkey') ?>
        <fieldset>
            <table id="shopping-cart-table" class="data-table cart-table">
                <tfoot>
                    <tr>
                        <td colspan="2" class="a-right">
                            <button type="submit" name="update_cart_action" value="update_qty" title="<?php echo $this->__('Update Shopping Cart'); ?>" class="button btn-update"><?php echo $this->__('Update Shopping Cart'); ?></button>
                            <!--<button type="submit" name="update_cart_action" value="empty_cart" title="<?php echo $this->__('Clear Shopping Cart'); ?>" class="button btn-update" id="empty_cart_button"><?php echo $this->__('Clear Shopping Cart'); ?></button>-->
                        </td>
                    </tr>
                </tfoot>
                <tbody>
                <?php foreach($this->getItems() as $_item): ?>
                    <?php echo $this->getItemHtml($_item) ?>
                <?php endforeach ?>
                </tbody>
            </table>
        </fieldset>
    </form>
    <?php echo $this->getChildHtml('shopping.cart.table.after'); ?>
    <div class="cart-collaterals<?php if($this->getChildHtml('crosssell')) echo ' cross-inside'; ?>">
        <?php echo $this->getChildHtml('crosssell') ?>
        <div class="deals">
            <?php /* Extensions placeholder */ ?>
            <?php echo $this->getChildHtml('checkout.cart.extra') ?>
            <?php echo $this->getChildHtml('checkout_cart_widget') ?>
            <?php echo $this->getChildHtml('coupon') ?>
            <?php echo $this->getChildHtml('giftcards') ?>
            <?php echo $this->getChildHtml('giftregistry.cart.link') ?>
        </div>
        <?php if (!$this->getIsVirtual()): echo $this->getChildHtml('shipping'); endif; ?>
    </div>
    <div class="totals">
        <?php echo $this->getChildHtml('totals'); ?>
    </div>
    <?php if(!$this->hasError()): ?>
        <ul class="checkout-types">
        <?php foreach ($this->getMethods('methods') as $method): ?>
            <?php $methodHtml = $this->getMethodHtml($method); ?>
            <?php if ($methodHtml): ?>
            <li><?php echo $methodHtml; ?></li>
            <?php endif; ?>
        <?php endforeach; ?>
        </ul>
    <?php endif; ?>

</div>
