<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
$_item = $this->getItem();
$isVisibleProduct = $_item->getProduct()->isVisibleInSiteVisibility();
$canApplyMsrp = Mage::helper('catalog')->canApplyMsrp($_item->getProduct(), Mage_Catalog_Model_Product_Attribute_Source_Msrp_Type::TYPE_BEFORE_ORDER_CONFIRM);
$messages = $this->getMessages();
$_options = $this->getOptionList();
?>

<?php if ($messages): ?>
<tr class="messages">
    <td colspan="2">
        <?php foreach ($messages as $message): ?>
            <p class="item-msg <?php echo $message['type'] ?>">* <?php echo $this->escapeHtml($message['text']) ?></p>
        <?php endforeach; ?>
    </td>
</tr>
<?php endif; ?>
<tr>
    <td>

        <?php if ($this->hasProductUrl()):?>
            <a href="<?php echo $this->getProductUrl() ?>" title="<?php echo $this->escapeHtml($this->getProductName()) ?>" class="product-image"><?php endif;?><img src="<?php echo $this->getProductThumbnail()->resize(114); ?>" width="57" height="57" alt="<?php echo $this->escapeHtml($this->getProductName()) ?>" /><?php if ($this->hasProductUrl()):?></a>
        <?php endif;?>

        <?php if ($isVisibleProduct && $this->getOptionList()): ?>
            <p><a class="link-edit" href="<?php echo $this->getConfigureUrl() ?>" title="<?php echo $this->__('Edit item parameters') ?>"><?php echo $this->__('Edit') ?></a></p>
        <?php endif ?>

    </td>

    <td>

        <a href="<?php echo $this->getDeleteUrl()?>" title="<?php echo $this->__('Remove item')?>" class="btn-remove btn-remove2"><?php echo $this->__('Remove item')?></a>

        <h2 class="product-name">
        <?php if ($this->hasProductUrl()):?>
            <a href="<?php echo $this->getProductUrl() ?>"><?php echo $this->escapeHtml($this->getProductName()) ?></a>
        <?php else: ?>
            <?php echo $this->escapeHtml($this->getProductName()) ?>
        <?php endif; ?>
        </h2>

        <?php $addInfoBlock = $this->getProductAdditionalInformationBlock(); ?>
        <?php if ($addInfoBlock): ?>
            <?php echo $addInfoBlock->setItem($_item)->toHtml() ?>
        <?php endif;?>

        <div class="price-box">

            <div class="qty-wrap">
                <input name="cart[<?php echo $_item->getId() ?>][qty]" value="<?php echo $this->getQty() ?>" size="4" title="<?php echo $this->__('Qty') ?>" class="input-text qty" maxlength="12" />
                <span<?php if ($this->getQty() == 1) echo ' style="display:none;"' ?>>×</span>
            </div>

            <div class="cart-price-box"<?php if ($this->getQty() == 1) echo ' style="display:none;"' ?>>

                <?php if ($canApplyMsrp): ?>
                    <span class="cart-price">
                        <span class="cart-msrp-unit"><?php echo $this->__('See price before order confirmation.'); ?></span>
                        <?php $helpLinkId = 'cart-msrp-help-' . $_item->getId(); ?>
                        <a id="<?php echo $helpLinkId ?>" href="#" class="map-help-link"><?php echo $this->__("What's this?"); ?></a>
                        <script type="text/javascript">
                            Catalog.Map.addHelpLink($('<?php echo $helpLinkId ?>'), "<?php echo $this->__("What's this?") ?>");
                        </script>
                    </span>
                <?php else: ?>

                    <?php if ($this->helper('tax')->displayCartPriceExclTax() || $this->helper('tax')->displayCartBothPrices()): ?>

                        <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(1, 4), 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                            <span class="cart-tax-total" onclick="taxToggle('eunit-item-tax-details<?php echo $_item->getId(); ?>', this, 'cart-tax-total-expanded');">
                        <?php else: ?>
                            <span class="cart-price">
                        <?php endif; ?>
                            <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                                <?php echo $this->helper('checkout')->formatPrice($_item->getCalculationPrice()+$_item->getWeeeTaxAppliedAmount()+$_item->getWeeeTaxDisposition()); ?>
                            <?php else: ?>
                                <?php echo $this->helper('checkout')->formatPrice($_item->getCalculationPrice()) ?>
                            <?php endif; ?>

                        </span>


                        <?php if (Mage::helper('weee')->getApplied($_item)): ?>

                            <div class="cart-tax-info" id="eunit-item-tax-details<?php echo $_item->getId(); ?>" style="display:none;">
                                <?php if (Mage::helper('weee')->typeOfDisplay($_item, 1, 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                                    <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                        <span class="weee"><?php echo $tax['title']; ?>: <?php echo Mage::helper('checkout')->formatPrice($tax['amount'],true,true); ?></span>
                                    <?php endforeach; ?>
                                <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                                    <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                        <span class="weee"><?php echo $tax['title']; ?>: <?php echo Mage::helper('checkout')->formatPrice($tax['amount'],true,true); ?></span>
                                    <?php endforeach; ?>
                                <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 4, 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                                    <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                        <span class="weee"><?php echo $tax['title']; ?>: <?php echo Mage::helper('checkout')->formatPrice($tax['amount'],true,true); ?></span>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>

                            <?php if (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                                <div class="cart-tax-total" onclick="taxToggle('eunit-item-tax-details<?php echo $_item->getId(); ?>', this, 'cart-tax-total-expanded');">
                                    <span class="weee"><?php echo Mage::helper('weee')->__('Total'); ?>: <?php echo $this->helper('checkout')->formatPrice($_item->getCalculationPrice()+$_item->getWeeeTaxAppliedAmount()+$_item->getWeeeTaxDisposition()); ?></span>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>

                    <?php endif; ?>
                    <?php if ($this->helper('tax')->displayCartPriceInclTax()): ?>

                        <?php $_incl = $this->helper('checkout')->getPriceInclTax($_item); ?>
                        <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(1, 4), 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                            <span class="cart-tax-total" onclick="taxToggle('unit-item-tax-details<?php echo $_item->getId(); ?>', this, 'cart-tax-total-expanded');">
                        <?php else: ?>
                            <span class="cart-price">
                        <?php endif; ?>

                            <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                                <?php echo $this->helper('checkout')->formatPrice($_incl + Mage::helper('weee')->getRowWeeeTaxInclTax($_item)); ?>
                            <?php else: ?>
                                <?php echo $this->helper('checkout')->formatPrice($_incl-$_item->getWeeeTaxDisposition()) ?>
                            <?php endif; ?>

                        </span>
                        <?php if (Mage::helper('weee')->getApplied($_item)): ?>

                            <div class="cart-tax-info" id="unit-item-tax-details<?php echo $_item->getId(); ?>" style="display:none;">
                                <?php if (Mage::helper('weee')->typeOfDisplay($_item, 1, 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                                    <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                        <span class="weee"><?php echo $tax['title']; ?>: <?php echo Mage::helper('checkout')->formatPrice($tax['amount_incl_tax'],true,true); ?></span>
                                    <?php endforeach; ?>
                                <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                                    <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                        <span class="weee"><?php echo $tax['title']; ?>: <?php echo Mage::helper('checkout')->formatPrice($tax['amount_incl_tax'],true,true); ?></span>
                                    <?php endforeach; ?>
                                <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 4, 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                                    <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                        <span class="weee"><?php echo $tax['title']; ?>: <?php echo Mage::helper('checkout')->formatPrice($tax['amount_incl_tax'],true,true); ?></span>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>

                            <?php if (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                                <div class="cart-tax-total" onclick="taxToggle('unit-item-tax-details<?php echo $_item->getId(); ?>', this, 'cart-tax-total-expanded');">
                                    <span class="weee"><?php echo Mage::helper('weee')->__('Total incl. tax'); ?>: <?php echo $this->helper('checkout')->formatPrice($_incl + Mage::helper('weee')->getWeeeTaxInclTax($_item)); ?></span>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>

                    <?php endif; ?>
                <?php endif; ?>

            </div>

            <div class="cart-price-box">

                <?php if ($this->helper('tax')->displayCartPriceExclTax() && !$_item->getNoSubtotal()): ?>

                    <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(1, 4), 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                        <span class="cart-tax-total" onclick="taxToggle('esubtotal-item-tax-details<?php echo $_item->getId(); ?>', this, 'cart-tax-total-expanded');">
                    <?php else: ?>
                        <span class="cart-price">
                    <?php endif; ?>

                        <?php if ($canApplyMsrp): ?>
                            <span class="cart-msrp-subtotal">--</span>
                        <?php else: ?>
                            <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                                <?php echo $this->helper('checkout')->formatPrice($_item->getRowTotal()+$_item->getWeeeTaxAppliedRowAmount()+$_item->getWeeeTaxRowDisposition()); ?>
                            <?php else: ?>
                                <?php echo $this->helper('checkout')->formatPrice($_item->getRowTotal()) ?>
                            <?php endif; ?>
                        <?php endif; ?>

                    </span>
                    <?php if (Mage::helper('weee')->getApplied($_item)): ?>

                        <div class="cart-tax-info" id="esubtotal-item-tax-details<?php echo $_item->getId(); ?>" style="display:none;">
                            <?php if (Mage::helper('weee')->typeOfDisplay($_item, 1, 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                    <span class="weee"><?php echo $tax['title']; ?>: <?php echo Mage::helper('checkout')->formatPrice($tax['row_amount'],true,true); ?></span>
                                <?php endforeach; ?>
                            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                    <span class="weee"><?php echo $tax['title']; ?>: <?php echo Mage::helper('checkout')->formatPrice($tax['row_amount'],true,true); ?></span>
                                <?php endforeach; ?>
                            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 4, 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                    <span class="weee"><?php echo $tax['title']; ?>: <?php echo Mage::helper('checkout')->formatPrice($tax['row_amount'],true,true); ?></span>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>

                        <?php if (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                            <div class="cart-tax-total" onclick="taxToggle('esubtotal-item-tax-details<?php echo $_item->getId(); ?>', this, 'cart-tax-total-expanded');">
                                <span class="weee"><?php echo Mage::helper('weee')->__('Total'); ?>: <?php echo $this->helper('checkout')->formatPrice($_item->getRowTotal()+$_item->getWeeeTaxAppliedRowAmount()+$_item->getWeeeTaxRowDisposition()); ?></span>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>

                <?php endif; ?>

                <?php if (($this->helper('tax')->displayCartPriceInclTax() || $this->helper('tax')->displayCartBothPrices()) && !$_item->getNoSubtotal()): ?>

                    <?php $_incl = $this->helper('checkout')->getSubtotalInclTax($_item); ?>
                    <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(1, 4), 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                        <span class="cart-tax-total" onclick="taxToggle('subtotal-item-tax-details<?php echo $_item->getId(); ?>', this, 'cart-tax-total-expanded');">
                    <?php else: ?>
                        <span class="cart-price">
                    <?php endif; ?>

                        <?php if ($canApplyMsrp): ?>
                            <span class="cart-msrp-subtotal">--</span>
                        <?php else: ?>
                            <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                                <?php echo $this->helper('checkout')->formatPrice($_incl + Mage::helper('weee')->getRowWeeeTaxInclTax($_item)); ?>
                            <?php else: ?>
                                <?php echo $this->helper('checkout')->formatPrice($_incl-$_item->getWeeeTaxRowDisposition()) ?>
                            <?php endif; ?>
                        <?php endif; ?>

                    </span>


                    <?php if (Mage::helper('weee')->getApplied($_item)): ?>

                        <div class="cart-tax-info" id="subtotal-item-tax-details<?php echo $_item->getId(); ?>" style="display:none;">
                            <?php if (Mage::helper('weee')->typeOfDisplay($_item, 1, 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                    <span class="weee"><?php echo $tax['title']; ?>: <?php echo Mage::helper('checkout')->formatPrice($tax['row_amount_incl_tax'],true,true); ?></span>
                                <?php endforeach; ?>
                            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                    <span class="weee"><?php echo $tax['title']; ?>: <?php echo Mage::helper('checkout')->formatPrice($tax['row_amount_incl_tax'],true,true); ?></span>
                                <?php endforeach; ?>
                            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 4, 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                    <span class="weee"><?php echo $tax['title']; ?>: <?php echo Mage::helper('checkout')->formatPrice($tax['row_amount_incl_tax'],true,true); ?></span>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>

                        <?php if (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                            <div class="cart-tax-total" onclick="taxToggle('subtotal-item-tax-details<?php echo $_item->getId(); ?>', this, 'cart-tax-total-expanded');">
                                <span class="weee"><?php echo Mage::helper('weee')->__('Total incl. tax'); ?>: <?php echo $this->helper('checkout')->formatPrice($_incl + Mage::helper('weee')->getRowWeeeTaxInclTax($_item)); ?></span>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>

                <?php endif; ?>

            </div>

        </div>

    </td>
</tr>
<?php if ($_options):?>
<tr class="product-options">
    <td colspan="2">
        <span class="toggle" onclick="$(this).next('.product-options').toggle(); return false;">Show Options</span>
        <div class="product-options" style="display:none;">
            <dl class="item-options">
                <?php foreach ($_options as $_option) : ?>
                    <?php $_formatedOptionValue = $this->getFormatedOptionValue($_option) ?>
                    <dt><?php echo $this->escapeHtml($_option['label']) ?></dt>
                    <?php if (isset($_formatedOptionValue['full_view'])): ?>
                        <dd><?php echo $_formatedOptionValue['full_view'] ?></dd>
                    <?php else: ?>
                        <dd><?php echo $_formatedOptionValue['value'] ?></dd>
                    <?php endif; ?>
                <?php endforeach; ?>
            </dl>
        </div>
    </td>
</tr>
<?php endif;?>
