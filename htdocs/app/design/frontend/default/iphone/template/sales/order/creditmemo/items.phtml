<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php $_order = $this->getOrder() ?>
<p class="order-links"><a href="<?php echo $this->getPrintAllCreditmemosUrl($_order) ?>" onclick="this.target='_blank'" class="link-print"><?php echo $this->__('Print All Refunds') ?></a></p>
<?php foreach ($_order->getCreditmemosCollection() as $_creditmemo): ?>
<h2 class="sub-title"><?php echo $this->__('Refund #') ?><?php echo $_creditmemo->getIncrementId(); ?> <span class="separator">|</span> <a href="<?php echo $this->getPrintCreditmemoUrl($_creditmemo) ?>" onclick="this.target='_blank'" class="link-print"><?php echo $this->__('Print Refund') ?></a></h2>
<h3 class="table-caption"><?php echo $this->__('Items Refunded') ?></h3>
<table class="data-table" id="my-refund-table-<?php echo $_creditmemo->getId(); ?>">
    <tfoot>
       <?php echo $this->getTotalsHtml($_creditmemo);?>
    </tfoot>
    <?php $_items = $_creditmemo->getAllItems(); ?>
    <?php $_count = count($_items) ?>
    <?php foreach ($_items as $_item): ?>
    <?php if ($_item->getOrderItem()->getParentItem()) continue; ?>
        <?php echo $this->getItemHtml($_item) ?>
    <?php endforeach; ?>
</table>
<?php echo $this->getCommentsHtml($_creditmemo)?>
<?php endforeach; ?>
