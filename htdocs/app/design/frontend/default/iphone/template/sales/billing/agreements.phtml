<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php /* @var $this Mage_Sales_Block_Billing_Agreements */ ?>
<div class="page-title">
    <h1><?php echo $this->__('Billing Agreements') ?></h1>
</div>

<?php echo $this->getMessagesBlock()->toHtml() ?>

<div class="billing-agreements">
    <?php $billingAgreements = $this->getBillingAgreements(); ?>
    <?php if (count($billingAgreements) > 0): ?>
        <?php echo $this->getChildHtml('pager'); ?>
        <table id="billing-agreements" class="info-table">
            <tbody>
                <?php foreach($billingAgreements as $item): ?>
                <tr>
                    <th><span class="nobr"><?php echo $this->__('Reference ID'); ?></span></th>
                </tr>
                    <td><span class="nobr"><?php echo $this->getItemValue($item, 'reference_id') ?></span></td>
                </tr>
                    <th><?php echo $this->__('Status'); ?></th>
                </tr>
                    <td><?php echo $this->getItemValue($item, 'status') ?></td>
                <tr>
                    <th><span class="nobr"><?php echo $this->__('Created At'); ?></span></th>
                </tr>
                    <td><span class="nobr"><?php echo $this->getItemValue($item, 'created_at') ?></span></td>
                </tr>
                <tr>
                    <th><span class="nobr"><?php echo $this->__('Updated At'); ?></span></th>
                </tr>
                    <td><span class="nobr"><?php echo $this->getItemValue($item, 'updated_at') ?></span></td>
                </tr>
                    <th><span class="nobr"><?php echo $this->__('Payment Method'); ?></span></th>
                </tr>
                    <td><?php echo $this->getItemValue($item, 'payment_method_label') ?></td>
                <tr>
                    <td><a href="<?php echo $this->getItemValue($item, 'edit_url') ?>"><?php echo $this->__('View') ?></a></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php else: ?>
        <p><?php echo $this->__('There are no billing agreements yet.')?></p>
    <?php endif; ?>

    <?php $paymentMethods = $this->getWizardPaymentMethodOptions() ?>
    <?php if ($paymentMethods): ?>
        <div class="info-box">
            <h2 class="box-title"><?php echo $this->__('New Billing Agreement') ?></h2>
            <form action="<?php echo $this->getCreateUrl() ?>" method="post">
                <div class="box-content">
                    <p><?php echo $this->__('You will be redirected to the payment system website.') ?></p>
                    <ul class="form-list">
                        <li>
                            <select id="payment_method" name="payment_method">
                                <option value=""><?php echo $this->__('-- Please Select --') ?></option>
                                <?php foreach ($paymentMethods as $code => $title): ?>
                                    <option value="<?php echo $code ?>"><?php echo $title ?></option>
                                <?php endforeach; ?>
                            </select>
                            <button type="submit" class="button"><span><span><?php echo $this->__('Create...') ?></span></span></button>
                        </li>
                    </ul>
                </div>
            </form>
        </div>
    <?php endif; ?>

    <div class="buttons-set">
        <p class="back-link"><a href="<?php echo $this->escapeHtml($this->getBackUrl()) ?>"><small>&laquo; </small><?php echo $this->__('Back') ?></a></p>
    </div>
</div>
