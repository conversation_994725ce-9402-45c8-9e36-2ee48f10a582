<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php $_orders = $this->getOrders(); ?>
<div class="page-title">
    <h1><?php echo $this->__('My Orders') ?></h1>
</div>
<div class="recent-orders">
    <?php echo $this->getMessagesBlock()->toHtml() ?>
    <?php echo $this->getChildHtml('info');?>
    <?php echo $this->getPagerHtml(); ?>
    <?php if($_orders->getSize()): ?>
    <table class="data-table" id="my-orders-table">
        <thead>
            <tr>
                <th><?php echo $this->__('Order #') ?></th>
                <th><?php echo $this->__('Date') ?></th>
                <th><span class="nobr"><?php echo $this->__('Total') ?></span></th>
                <th><span class="nobr"><?php echo $this->__('Status') ?></span></th>
            </tr>
        </thead>
        <tbody>
            <?php $_odd = ''; ?>
            <?php foreach ($_orders as $_order): ?>
            <tr>
                <td><a href="<?php echo $this->getViewUrl($_order) ?>"><?php echo $_order->getRealOrderId() ?></a></td>
                <td><span class="nobr"><?php echo $this->formatDate($_order->getCreatedAtStoreDate()) ?></span></td>
                <td><?php echo $_order->formatPrice($_order->getGrandTotal()) ?></td>
                <td><em><?php echo $_order->getStatusLabel() ?></em></td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
    <?php else: ?>
        <p><?php echo $this->__('You have placed no orders.'); ?></p>
    <?php endif ?>
</div>
