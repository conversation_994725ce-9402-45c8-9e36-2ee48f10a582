<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php $_item = $this->getItem() ?>
<?php $_order = $this->getItem()->getOrderItem()->getOrder() ?>
<tbody>
<tr class="border" id="order-item-row-<?php echo $_item->getId() ?>">
    <th><?php echo $this->__('Product Name') ?></th>
    <td><h4 class="product-name"><?php echo $this->escapeHtml($_item->getName()) ?></h4>
        <?php if ($_options = $this->getItemOptions()): ?>
            <dl class="item-options">
                <?php foreach ($_options as $_option) : ?>
                    <dt><?php echo $this->escapeHtml($_option['label']) ?></dt>
                    <?php if (!$this->getPrintStatus()): ?>
                        <?php $_formatedOptionValue = $this->getFormatedOptionValue($_option) ?>
                        <dd<?php if (isset($_formatedOptionValue['full_view'])): ?> class="truncated"<?php endif; ?>><?php echo $_formatedOptionValue['value'] ?>
                            <?php if (isset($_formatedOptionValue['full_view'])): ?>
                                <div class="truncated_full_value">
                                    <dl class="item-options">
                                        <dt><?php echo $this->escapeHtml($_option['label']) ?></dt>
                                        <dd><?php echo $_formatedOptionValue['full_view'] ?></dd>
                                    </dl>
                                </div>
                            <?php endif; ?>
                        </dd>
                    <?php else: ?>
                        <dd><?php echo $this->escapeHtml((isset($_option['print_value']) ? $_option['print_value'] : $_option['value'])) ?></dd>
                    <?php endif; ?>
                <?php endforeach; ?>
            </dl>
        <?php endif; ?>
        <?php $addInfoBlock = $this->getProductAdditionalInformationBlock(); ?>
        <?php if ($addInfoBlock) : ?>
            <?php echo $addInfoBlock->setItem($_item->getOrderItem())->toHtml(); ?>
        <?php endif; ?>
        <?php echo $this->escapeHtml($_item->getDescription()) ?>
        <?php if ($this->helper('giftmessage/message')->getIsMessagesAvailable('order_item', $_item->getOrderItem()) && $_item->getGiftMessageId()): ?>
            <a href="#" id="order-item-gift-message-link-<?php echo $_item->getId() ?>" class="gift-message-link"
               onclick="return giftMessageToogle('<?php echo $_item->getId() ?>')"><?php echo $this->__('Gift Message') ?></a>
        <?php endif; ?>
    </td>
</tr>
<tr>
    <th><?php echo $this->__('SKU') ?></th>
    <td><?php echo $this->escapeHtml(Mage::helper('core/string')->splitInjection($this->getSku())) ?></td>
</tr>
<th class="a-right"><?php echo $this->__('Price') ?></th>
<td class="a-right">
    <?php if ($this->helper('tax')->displaySalesBothPrices() || $this->helper('tax')->displaySalesPriceExclTax()): ?>
    <span class="price-excl-tax">
                    <?php if ($this->helper('tax')->displaySalesBothPrices()): ?>
                        <?php if (!Mage::helper('weee')->typeOfDisplay($this->getItem(), array(1, 4), 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                            <span class="cart-price">
                        <?php endif; ?>
                        <span class="label"><?php echo $this->__('Excl. Tax'); ?>:</span>
                        <?php if (!Mage::helper('weee')->typeOfDisplay($this->getItem(), array(1, 4), 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                            </span>
                        <?php endif; ?>
                    <?php endif; ?>
        <?php if (Mage::helper('weee')->typeOfDisplay($this->getItem(), array(1, 4), 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
        <span class="cart-tax-total"
              onclick="taxToggle('eunit-item-tax-details<?php echo $this->getItem()->getId(); ?>', this, 'cart-tax-total-expanded');">
                    <?php else: ?>
            <span class="cart-price">
                    <?php endif; ?>

                    <?php if (Mage::helper('weee')->typeOfDisplay($this->getItem(), array(0, 1, 4), 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                        <?php echo $this->getOrder()->formatPrice($this->getItem()->getPrice() + $this->getItem()->getWeeeTaxAppliedAmount() + $this->getItem()->getWeeeTaxDisposition()); ?>
                    <?php else: ?>
                        <?php echo $this->getOrder()->formatPrice($this->getItem()->getPrice()) ?>
                    <?php endif; ?>

                    </span>


            <?php if (Mage::helper('weee')->getApplied($this->getItem())): ?>

                <span class="cart-tax-info" id="eunit-item-tax-details<?php echo $this->getItem()->getId(); ?>"
                      style="display:none;">
                            <?php if (Mage::helper('weee')->typeOfDisplay($this->getItem(), 1, 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                                <small>
                                    <?php foreach (Mage::helper('weee')->getApplied($this->getItem()) as $tax): ?>
                                        <span class="nobr"><?php echo $tax['title']; ?>
                                            : <?php echo $this->getOrder()->formatPrice($tax['amount']); ?></span>
                                    <?php endforeach; ?>
                                </small>
                            <?php elseif (Mage::helper('weee')->typeOfDisplay($this->getItem(), 2, 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                                <?php foreach (Mage::helper('weee')->getApplied($this->getItem()) as $tax): ?>
                                    <span class="nobr"><small><?php echo $tax['title']; ?>
                                            : <?php echo $this->getOrder()->formatPrice($tax['amount_incl_tax']); ?></small></span>
                                <?php endforeach; ?>
                            <?php elseif (Mage::helper('weee')->typeOfDisplay($this->getItem(), 4, 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                                <small>
                                    <?php foreach (Mage::helper('weee')->getApplied($this->getItem()) as $tax): ?>
                                        <span class="nobr"><?php echo $tax['title']; ?>
                                            : <?php echo $this->getOrder()->formatPrice($tax['amount_incl_tax']); ?></span>
                                    <?php endforeach; ?>
                                </small>
                            <?php endif; ?>
                        </span>

                        <?php if (Mage::helper('weee')->typeOfDisplay($this->getItem(), 2, 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                    <span class="cart-tax-total"
                          onclick="taxToggle('eunit-item-tax-details<?php echo $this->getItem()->getId(); ?>', this, 'cart-tax-total-expanded');">
                                <span class="nobr"><?php echo Mage::helper('weee')->__('Total'); ?>
                                    :<br/> <?php echo $this->getOrder()->formatPrice($this->getItem()->getPrice() + $this->getItem()->getWeeeTaxAppliedAmount() + $this->getItem()->getWeeeTaxDisposition()); ?></span>
                            </span>
                <?php endif; ?>
            <?php endif; ?>
                </span>
                <br/>
        <?php endif; ?>
        <?php if ($this->helper('tax')->displaySalesBothPrices() || $this->helper('tax')->displaySalesPriceInclTax()): ?>
        <span class="price-incl-tax">
                    <?php if ($this->helper('tax')->displaySalesBothPrices()): ?>
                        <?php if (!Mage::helper('weee')->typeOfDisplay($this->getItem(), array(1, 4), 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                            <span class="cart-price">
                        <?php endif; ?>
                        <span class="label"><?php echo $this->__('Incl. Tax'); ?>:</span>
                        <?php if (!Mage::helper('weee')->typeOfDisplay($this->getItem(), array(1, 4), 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                            </span>
                        <?php endif; ?>
                    <?php endif; ?>
            <?php $_incl = $this->helper('checkout')->getPriceInclTax($this->getItem()); ?>
            <?php if (Mage::helper('weee')->typeOfDisplay($this->getItem(), array(1, 4), 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
            <span class="cart-tax-total"
                  onclick="taxToggle('unit-item-tax-details<?php echo $this->getItem()->getId(); ?>', this, 'cart-tax-total-expanded');">
                    <?php else: ?>
                <span class="cart-price">
                    <?php endif; ?>

                    <?php if (Mage::helper('weee')->typeOfDisplay($this->getItem(), array(0, 1, 4), 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                        <?php echo $this->getOrder()->formatPrice($_incl + Mage::helper('weee')->getWeeeTaxInclTax($_item)); ?>
                    <?php else: ?>
                        <?php echo $this->getOrder()->formatPrice($_incl - $this->getItem()->getWeeeTaxDisposition()) ?>
                    <?php endif; ?>

                    </span>


                <?php if (Mage::helper('weee')->getApplied($this->getItem())): ?>

                    <span class="cart-tax-info" id="unit-item-tax-details<?php echo $this->getItem()->getId(); ?>"
                          style="display:none;">
                            <?php if (Mage::helper('weee')->typeOfDisplay($this->getItem(), 1, 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                                <small>
                                    <?php foreach (Mage::helper('weee')->getApplied($this->getItem()) as $tax): ?>
                                        <span class="nobr"><?php echo $tax['title']; ?>
                                            : <?php echo $this->getOrder()->formatPrice($tax['amount']); ?></span>
                                    <?php endforeach; ?>
                                </small>
                            <?php elseif (Mage::helper('weee')->typeOfDisplay($this->getItem(), 2, 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                                <?php foreach (Mage::helper('weee')->getApplied($this->getItem()) as $tax): ?>
                                    <span class="nobr"><small><?php echo $tax['title']; ?>
                                            : <?php echo $this->getOrder()->formatPrice($tax['amount_incl_tax']); ?></small></span>
                                <?php endforeach; ?>
                            <?php elseif (Mage::helper('weee')->typeOfDisplay($this->getItem(), 4, 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                                <small>
                                    <?php foreach (Mage::helper('weee')->getApplied($this->getItem()) as $tax): ?>
                                        <span class="nobr"><?php echo $tax['title']; ?>
                                            : <?php echo $this->getOrder()->formatPrice($tax['amount_incl_tax']); ?></span>
                                    <?php endforeach; ?>
                                </small>
                            <?php endif; ?>
                        </span>

                        <?php if (Mage::helper('weee')->typeOfDisplay($this->getItem(), 2, 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                        <span class="cart-tax-total"
                              onclick="taxToggle('unit-item-tax-details<?php echo $this->getItem()->getId(); ?>', this, 'cart-tax-total-expanded');">
                                <span class="nobr"><?php echo Mage::helper('weee')->__('Total incl. tax'); ?>
                                    :<br/> <?php echo $this->getOrder()->formatPrice($_incl + Mage::helper('weee')->getWeeeTaxInclTax($_item)); ?></span>
                            </span>
                    <?php endif; ?>
                <?php endif; ?>
                </span>
            <?php endif; ?>
</td>
</tr>
<tr>
    <th class="a-center"><?php echo $this->__('Qty') ?></th>
    <td class="a-center"><?php echo $_item->getQty() * 1 ?></td>
</tr>
<tr>
    <th class="a-right"><?php echo $this->__('Subtotal') ?></th>
    <td class="a-right">
        <?php if ($this->helper('tax')->displaySalesBothPrices() || $this->helper('tax')->displaySalesPriceExclTax()): ?>
        <span class="price-excl-tax">
                    <?php if ($this->helper('tax')->displaySalesBothPrices()): ?>
                        <?php if (!Mage::helper('weee')->typeOfDisplay($this->getItem(), array(1, 4), 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                            <span class="cart-price">
                        <?php endif; ?>
                        <span class="label"><?php echo $this->__('Excl. Tax'); ?>:</span>
                        <?php if (!Mage::helper('weee')->typeOfDisplay($this->getItem(), array(1, 4), 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                            </span>
                        <?php endif; ?>
                    <?php endif; ?>
            <?php if (Mage::helper('weee')->typeOfDisplay($this->getItem(), array(1, 4), 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
            <span class="cart-tax-total"
                  onclick="taxToggle('esubtotal-item-tax-details<?php echo $this->getItem()->getId(); ?>', this, 'cart-tax-total-expanded');">
                    <?php else: ?>
                <span class="cart-price">
                    <?php endif; ?>

                    <?php if (Mage::helper('weee')->typeOfDisplay($this->getItem(), array(0, 1, 4), 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                        <?php echo $this->getOrder()->formatPrice($this->getItem()->getRowTotal() + $this->getItem()->getWeeeTaxAppliedRowAmount() + $this->getItem()->getWeeeTaxRowDisposition()); ?>
                    <?php else: ?>
                        <?php echo $this->getOrder()->formatPrice($this->getItem()->getRowTotal()) ?>
                    <?php endif; ?>

                    </span>


                <?php if (Mage::helper('weee')->getApplied($this->getItem())): ?>

                    <span class="cart-tax-info" id="esubtotal-item-tax-details<?php echo $this->getItem()->getId(); ?>"
                          style="display:none;">
                            <?php if (Mage::helper('weee')->typeOfDisplay($this->getItem(), 1, 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                                <small>
                                    <?php foreach (Mage::helper('weee')->getApplied($this->getItem()) as $tax): ?>
                                        <span class="nobr"><?php echo $tax['title']; ?>
                                            : <?php echo $this->getOrder()->formatPrice($tax['row_amount']); ?></span>
                                    <?php endforeach; ?>
                                </small>
                            <?php elseif (Mage::helper('weee')->typeOfDisplay($this->getItem(), 2, 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                                <?php foreach (Mage::helper('weee')->getApplied($this->getItem()) as $tax): ?>
                                    <span class="nobr"><small><?php echo $tax['title']; ?>
                                            : <?php echo $this->getOrder()->formatPrice($tax['row_amount']); ?></small></span>
                                <?php endforeach; ?>
                            <?php elseif (Mage::helper('weee')->typeOfDisplay($this->getItem(), 4, 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                                <small>
                                    <?php foreach (Mage::helper('weee')->getApplied($this->getItem()) as $tax): ?>
                                        <span class="nobr"><?php echo $tax['title']; ?>
                                            : <?php echo $this->getOrder()->formatPrice($tax['row_amount']); ?></span>
                                    <?php endforeach; ?>
                                </small>
                            <?php endif; ?>
                        </span>

                        <?php if (Mage::helper('weee')->typeOfDisplay($this->getItem(), 2, 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                        <span class="cart-tax-total"
                              onclick="taxToggle('esubtotal-item-tax-details<?php echo $this->getItem()->getId(); ?>', this, 'cart-tax-total-expanded');">
                                <span class="nobr"><?php echo Mage::helper('weee')->__('Total'); ?>
                                    :<br/> <?php echo $this->getOrder()->formatPrice($this->getItem()->getRowTotal() + $this->getItem()->getWeeeTaxAppliedRowAmount() + $this->getItem()->getWeeeTaxRowDisposition()); ?></span>
                            </span>
                    <?php endif; ?>
                <?php endif; ?>
                </span>
                <br/>
            <?php endif; ?>
            <?php if ($this->helper('tax')->displaySalesBothPrices() || $this->helper('tax')->displaySalesPriceInclTax()): ?>
            <span class="price-incl-tax">
                    <?php if ($this->helper('tax')->displaySalesBothPrices()): ?>
                        <?php if (!Mage::helper('weee')->typeOfDisplay($this->getItem(), array(1, 4), 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                            <span class="cart-price">
                        <?php endif; ?>
                        <span class="label"><?php echo $this->__('Incl. Tax'); ?>:</span>
                        <?php if (!Mage::helper('weee')->typeOfDisplay($this->getItem(), array(1, 4), 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                            </span>
                        <?php endif; ?>
                    <?php endif; ?>
                <?php $_incl = $this->helper('checkout')->getSubtotalInclTax($this->getItem()); ?>
                <?php if (Mage::helper('weee')->typeOfDisplay($this->getItem(), array(1, 4), 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                <span class="cart-tax-total"
                      onclick="taxToggle('subtotal-item-tax-details<?php echo $this->getItem()->getId(); ?>', this, 'cart-tax-total-expanded');">
                    <?php else: ?>
                    <span class="cart-price">
                    <?php endif; ?>
                    <?php if (Mage::helper('weee')->typeOfDisplay($this->getItem(), array(0, 1, 4), 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                        <?php echo $this->getOrder()->formatPrice($_incl + Mage::helper('weee')->getRowWeeeTaxInclTax($_item)); ?>
                    <?php else: ?>
                        <?php echo $this->getOrder()->formatPrice($_incl - $this->getItem()->getWeeeTaxRowDisposition()) ?>
                    <?php endif; ?>

                    </span>


                    <?php if (Mage::helper('weee')->getApplied($this->getItem())): ?>

                        <span class="cart-tax-info"
                              id="subtotal-item-tax-details<?php echo $this->getItem()->getId(); ?>"
                              style="display:none;">
                            <?php if (Mage::helper('weee')->typeOfDisplay($this->getItem(), 1, 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                                <small>
                                    <?php foreach (Mage::helper('weee')->getApplied($this->getItem()) as $tax): ?>
                                        <span class="nobr"><?php echo $tax['title']; ?>
                                            : <?php echo $this->getOrder()->formatPrice($tax['row_amount_incl_tax']); ?></span>

                                    <?php endforeach; ?>
                                </small>
                            <?php elseif (Mage::helper('weee')->typeOfDisplay($this->getItem(), 2, 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                                <?php foreach (Mage::helper('weee')->getApplied($this->getItem()) as $tax): ?>
                                    <span class="nobr"><small><?php echo $tax['title']; ?>
                                            : <?php echo $this->getOrder()->formatPrice($tax['row_amount_incl_tax']); ?></small></span>
                                <?php endforeach; ?>
                            <?php elseif (Mage::helper('weee')->typeOfDisplay($this->getItem(), 4, 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                                <small>
                                    <?php foreach (Mage::helper('weee')->getApplied($this->getItem()) as $tax): ?>
                                        <span class="nobr"><?php echo $tax['title']; ?>
                                            : <?php echo $this->getOrder()->formatPrice($tax['row_amount_incl_tax']); ?></span>
                                    <?php endforeach; ?>
                                </small>
                            <?php endif; ?>
                        </span>

                        <?php if (Mage::helper('weee')->typeOfDisplay($this->getItem(), 2, 'sales') && (float)$this->getItem()->getWeeeTaxAppliedAmount()): ?>
                            <span class="cart-tax-total"
                                  onclick="taxToggle('subtotal-item-tax-details<?php echo $this->getItem()->getId(); ?>', this, 'cart-tax-total-expanded');">
                                <span class="nobr"><?php echo Mage::helper('weee')->__('Total incl. tax'); ?>
                                    :<br/> <?php echo $this->getOrder()->formatPrice($_incl + Mage::helper('weee')->getRowWeeeTaxInclTax($_item)); ?></span>
                            </span>
                        <?php endif; ?>
                    <?php endif; ?>



                </span>
                <?php endif; ?>
    </td>
</tr>
<tr>
    <th class="a-center wrap"><?php echo $this->__('Discount Amount') ?></th>
    <td class="a-right"><?php echo $_order->formatPrice(-$_item->getDiscountAmount()) ?></td>
</tr>
<tr>
    <th class="a-right wrap"><?php echo $this->__('Row Total') ?></th>
    <td class="last a-right">
        <?php echo $_order->formatPrice($_item->getRowTotal() - $_item->getDiscountAmount() + $_item->getTaxAmount() + $_item->getWeeeTaxAppliedRowAmount()) ?>
    </td>
</tr>
</tbody>
