<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/**
 * @var $this Mage_Sales_Block_Order_Totals
 * @see Mage_Sales_Block_Order_Totals
 */
?>
<?php foreach ($this->getTotals() as $_code => $_total): ?>
    <?php if ($_total->getBlockName()): ?>
        <?php echo $this->getChildHtml($_total->getBlockName(), false); ?>
    <?php else:?>
    <tr class="<?php echo $_code?>">
        <td>
            <?php if ($_total->getStrong()):?>
            <strong><?php echo $this->escapeHtml($_total->getLabel());?></strong>
            <?php else:?>
            <?php echo $this->escapeHtml($_total->getLabel());?>
            <?php endif?>
        </td>
        <td>
            <?php if ($_total->getStrong()):?>
            <strong><?php echo $this->formatValue($_total) ?></strong>
            <?php else:?>
            <?php echo $this->formatValue($_total) ?>
            <?php endif?>
        </td>
    </tr>
    <?php endif?>
<?php endforeach?>
