<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php if(count($this->getItemCollection()->getItems())): ?>
<div class="box-collateral box-up-sell">
    <div class="box-title">
        <h2><?php echo $this->__('You May Also Like') ?></h2>
    </div>
    <ul class="products-grid" id="upsell-product-list"><!-
    <?php $this->resetItemsIterator() ?>
    <?php for($_i=0;$_i<$this->getRowCount();$_i++): ?>
        <?php for($_j=0;$_j<$this->getColumnCount();$_j++): ?>
            <?php if($_link=$this->getIterableItem()): ?>
            -><li class="item">
                <a href="<?php echo $_link->getProductUrl() ?>" class="product-image"><img src="<?php echo $this->helper('catalog/image')->init($_link, 'small_image')->resize(135) ?>" width="135" height="135" alt="<?php echo $this->escapeHtml($_link->getName()) ?>" title="<?php echo $this->escapeHtml($_link->getName()) ?>" /></a>
                <h3 class="product-name"><a href="<?php echo $_link->getProductUrl() ?>"><?php echo $this->escapeHtml($_link->getName()) ?></a></h3>
                <?php echo $this->getPriceHtml($_link, true, '-upsell') ?>
            </li><!-
            <?php endif; ?>
        <?php endfor; ?>
    <?php endfor; ?>
    -></ul>
</div>
<?php endif ?>
