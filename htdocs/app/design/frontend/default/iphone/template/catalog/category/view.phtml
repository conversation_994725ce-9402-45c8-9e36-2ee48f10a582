<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * Category view template
 *
 * @see Mage_Catalog_Block_Category_View
 */
?>
<?php
    $_helper    = $this->helper('catalog/output');
    $_category  = $this->getCurrentCategory();
    $_categoryTitle = '<div class="category-title page-title"><h1>'.$_helper->categoryAttribute($_category, $_category->getName(), 'name').'</h1></div>';
    $_imgUrl = $_category->getImageUrl();
    $_imgHtml   = '';
    if ($_imgUrl) {
        $_imgHtml = '<p class="category-image"><img src="'.$_imgUrl.'" alt="'.$this->escapeHtml($_category->getName()).'" title="'.$this->escapeHtml($_category->getName()).'" /></p>';
        $_imgHtml = $_helper->categoryAttribute($_category, $_imgHtml, 'image');
    }
?>
<?php echo $this->getMessagesBlock()->toHtml() ?>
<div class="category-view">
    <?php if($_imgUrl): ?>
        <?php echo $_imgHtml ?>
    <?php endif; ?>

    <?php if($_description=$this->getCurrentCategory()->getDescription()): ?>
        <div class="category-description std">
            <?php echo $_helper->categoryAttribute($_category, $_description, 'description') ?>
        </div>
    <?php endif; ?>

    <?php if($this->isContentMode()): ?>
        <?php echo $_categoryTitle ?>
        <?php echo $this->getCmsBlockHtml() ?>

    <?php elseif($this->isMixedMode()): ?>
        <?php echo $this->getCmsBlockHtml() ?>
        <?php echo $_categoryTitle ?>
        <?php echo $this->getProductListHtml() ?>

    <?php else: ?>
        <?php echo $_categoryTitle ?>
        <?php echo $this->getProductListHtml() ?>
    <?php endif; ?>
</div>
