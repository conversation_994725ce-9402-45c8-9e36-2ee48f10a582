<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * Category layered navigation
 *
 * @see Mage_Catalog_Block_Layer_View
 */
?>
<?php if($this->canShowBlock()): ?>
<div class="filters-block">
    <?php echo $this->getStateHtml() ?>
    <?php if ($this->getLayer()->getState()->getFilters()): ?>
        <div class="actions"><a href="<?php echo $this->getClearUrl() ?>"><?php echo $this->__('Clear All') ?></a></div>
    <?php endif; ?>
    <div class="block-content">
    <?php if($this->canShowOptions()): ?>
            <h3><?php echo $this->__('Shop By') ?></h3>
            <?php $_filters = $this->getFilters() ?>
            <?php foreach ($_filters as $_filter): ?>
                <?php if($_filter->getItemsCount()): ?>
                <dl>
                    <dt><?php echo $this->__($_filter->getName()) ?></dt>
                    <dd><?php echo $_filter->getHtml() ?></dd>
                </dl>
                <?php endif; ?>
            <?php endforeach; ?>
    <?php endif; ?>
    </div>
</div>
<?php endif; ?>
