<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<?php
/**
 * Template for displaying product price at catalog and sidebars
 *
 * @var $this Mage_Catalog_Block_Product_Price
 */
?>
<?php
    /** @var $_product Mage_Catalog_Model_Product */
    $_product = $this->getProduct();
    $_msrpPrice = "";
?>
    <div class="price-box map-info">
    <?php $_price = $this->helper('tax')->getPrice($_product, $_product->getMsrp()) ?>
        <?php if ($_product->getMsrp()): ?>
            <?php $_msrpPrice = $this->helper('core')->currency($_product->getMsrp(),true,true) ?>
            <span class="old-price" id="product-price-<?php echo $_product->getId() ?><?php echo $this->getIdSuffix() ?>"><?php echo $_msrpPrice ?></span>
        <?php endif; ?>
        <?php echo $this->__('Click for price') ?>
    </div>
