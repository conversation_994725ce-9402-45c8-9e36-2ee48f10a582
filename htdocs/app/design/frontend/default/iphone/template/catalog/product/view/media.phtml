<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

/**
 * Product media data template
 *
 * @see Mage_Catalog_Block_Product_View_Media
 */
?>
<?php
    $_product = $this->getProduct();
    $_helper = $this->helper('catalog/output');
?>
<div class="product-image-wrap">
    <div class="product-image">
        <ul><!--
            --><li>
                <?php
                    $_img = '<a href="'.$this->getGalleryUrl().'"><img src="'.$this->helper('catalog/image')->init($_product, 'image')->resize(290).'" width="145" alt="'.$this->escapeHtml($this->getImageLabel()).'" title="'.$this->escapeHtml($this->getImageLabel()).'" /></a>';
                    echo $_helper->productAttribute($_product, $_img, 'image');
                ?>
            </li><!--
            <?php if (count($this->getGalleryImages()) > 0): ?>
                <?php foreach ($this->getGalleryImages() as $_image): ?>
                    --><li><a href="<?php echo $this->getGalleryUrl($_image) ?>" title="<?php echo $this->escapeHtml($_image->getLabel()) ?>"><img src="<?php echo $this->helper('catalog/image')->init($this->getProduct(), 'thumbnail', $_image->getFile())->resize(290); ?>" width="145" height="145" alt="<?php echo $this->escapeHtml($_image->getLabel()) ?>" /></a></li><!--
                <?php endforeach; ?>
            <?php endif; ?>
        --></ul>
    </div>
</div>
