<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php $__helper = $this->helper('catalog/output'); ?>
<?php $_product = $this->getProduct(); ?>
<div class="product-gallery" id="product-gallery">
    <ul>
        <li>
            <img src="<?php echo $this->helper('catalog/image')->init($_product, 'image')->resize(300) ?>" height="300" width="300" alt="<?php echo $this->escapeHtml($this->getImageLabel()) ?>" title="<?php echo $this->escapeHtml($this->getImageLabel()) ?>" />
        </li>
        <?php foreach ($this->getProduct()->getMediaGalleryImages() as $_image): ?>
        <li>
            <img src="<?php echo $this->helper('catalog/image')->init($this->getProduct(), 'thumbnail', $_image->getFile())->resize(300); ?>" height="300" width="300" alt="<?php echo $this->escapeHtml($_image->getLabel()) ?>" />
        </li>
        <?php endforeach; ?>
    </ul>
</div>
<div class="buttons-set">
    <a href="<?php echo $this->helper('checkout/cart')->getAddUrl($_product) ?>">Add to Cart</a>
</div>
<script type="text/javascript">
    document.observe("dom:loaded", function() {
        var gallery = new zoomGallery($('product-gallery'));
    });
</script>
