<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/** @var $this Mage_Paypal_Block_Express_Review */

$billingBlock = $this->getChild('billing')->setFieldNamePrefix('billing')->setHideEmailAddress(true);
$shippingAddress = $this->getShippingAddress();
?>
<div class="page-title">
    <h1><?php echo $this->__('Review Order') ?></h1>
</div>
<?php echo $this->getMessagesBlock()->toHtml() ?>
<script type="text/javascript">
//<![CDATA[
    var countryRegions = <?php echo $this->helper('directory')->getRegionJson() ?>
//]]>
</script>
<div class="paypal-review-order">
    <h2 class="sub-title">
        Please confirm your addresses
    </h2>
    <form method="post" id="order_review_form" action="<?php echo $this->getPlaceOrderUrl() ?>">
    <?php if(!$billingBlock->isCustomerLoggedIn()): ?>
        <div class="info-set">
            <h2 class="legend"><?php echo $this->__('Customer Information') ?></h2>
            <ul class="form-list form-list-narrow">
                <li id="customer-info-form" class="address-form">
                    <div class="field">
                        <label for="customer:email" class="required"><em>*</em><?php echo $this->__('Email Address') ?></label>
                        <div class="input-box">
                            <input type="text" name="customer-email" id="customer:email" value="<?php echo $this->escapeHtml($billingBlock->getAddress()->getEmail()) ?>" title="<?php echo $this->quoteEscape($this->__('Email Address')) ?>" class="input-text validate-email required-entry" />
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    <?php endif ?>
    <div class="info-set">
        <div class="col-1" id="billing-address">
            <h2 class="legend"><?php echo $this->__('Billing Address') ?></h2>
            <?php if ($shippingAddress): ?>
                <?php echo $billingBlock->setShowAsShippingCheckbox(true)->toHtml(); ?>
            <?php else: ?>
                <?php echo $billingBlock->toHtml(); ?>
            <?php endif; ?>
        </div>
    <?php if ($shippingAddress): ?>
        <div class="col-2" id="shipping-address">
            <h2 class="legend"><?php echo $this->__('Shipping Address') ?></h2>
            <?php echo $this->getChild('shipping')->setFieldNamePrefix('shipping')->setHideEmailAddress(true)->toHtml(); ?>
        </div>
    </div>

    <div class="info-set">
        <div class="col-2">
            <div class="box paypal-shipping-method">
                <div class="box-title">
                    <h3><?php echo $this->__('Shipping Method') ?></h3>
                </div>
                <div class="box-content">
                    <?php echo $this->getChild('shipping_method')->toHtml(); ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
    </div>

    <div class="info-set">
        <h2 class="legend"><?php echo $this->__('Items in Your Shopping Cart') ?><span class="separator"> | </span><a href="<?php echo $this->getUrl('checkout/cart') ?>"><?php echo $this->__('Edit Shopping Cart') ?></a></h2>
        <div id="details-reload">
            <?php echo $this->getChildHtml('details') ?>
        </div>
    </div>
        <?php echo $this->getChildHtml('agreements'); ?>
        <div class="buttons-set buttons-set-order" id="review-buttons-container">
            <button type="button" id="review_button" value="<?php echo $this->__('Place Order') ?>" class="button btn-checkout"><span><span><?php echo $this->__('Place Order') ?></span></span></button>
            <button type="button" id="review_submit" value="<?php echo $this->__('Place Order') ?>" class="button btn-checkout"><span><span><?php echo $this->__('Place Order') ?></span></span></button>
            <button type="button" id="update_order" class="button btn-checkout"><span><span><?php echo $this->__('Update Order Data') ?></span></span></button>
            <span class="please-wait" id="review-please-wait" style="display:none;">
                <img src="<?php echo $this->getSkinUrl('images/opc-ajax-loader.gif') ?>" alt="<?php echo $this->quoteEscape($this->__('Submitting order information...')) ?>" title="<?php echo $this->quoteEscape($this->__('Submitting order information...')) ?>" class="v-middle" /> <?php echo $this->__('Submitting order information...') ?>
            </span>
        </div>
    </form>
</div>
<script type="text/javascript">
//<![CDATA[
// submit buttons are not needed when submitting with ajax
$('review_submit').hide();
if ($('update_shipping_method_submit')) {
    $('update_shipping_method_submit').hide();
}

<?php if ($this->getUseAjax()):?>
    OrderReviewController.prototype._submitOrder = function() {
        if (this._canSubmitOrder) {
            if (this._pleaseWait) {
                this._pleaseWait.show();
            }
            new Ajax.Request(this.form.action, {
                parameters: {isAjax: 1, method: 'POST'},
                onSuccess: function(transport) {
                    try{
                        response = eval('(' + transport.responseText + ')');
                    } catch (e) {
                        response = {};
                    }
                    if (response.redirect) {
                        setLocation(response.redirect);
                        return;
                    }
                    if (response.success) {
                        setLocation('<?php echo $this->getSuccessUrl()?>');
                        return;
                    } else {
                        var msg = response.error_messages;
                        if (typeof(msg)=='object') {
                            msg = msg.join("\n");
                        }
                        if (msg) {
                            $('review-please-wait').hide();
                            alert(msg);
                            return;
                        }
                    }
                    $('review-please-wait').hide();
                    alert('<?php echo $this->jsQuoteEscape($this->__('Unknown Error. Please try again later.')); ?>');
                    return;
                },
                onFailure: function(){
                    alert('<?php echo $this->jsQuoteEscape($this->__('Server Error. Please try again.')) ?>');
                    $('review-please-wait').hide();
                }
            });
        }
    }
<?php endif ?>
PayPalExpressAjax = new OrderReviewController($('order_review_form'), $('review_button'),
    'shipping_method', null, 'details-reload'
);
PayPalExpressAjax.addPleaseWait($('review-please-wait'));
PayPalExpressAjax.setShippingAddressContainer($('shipping-address'));
PayPalExpressAjax.setShippingMethodContainer('shipping-method-container');
PayPalExpressAjax.shippingMethodsUpdateUrl = '<?php echo $this->escapeHtml($this->getUpdateShippingMethodsUrl()) ?>';
PayPalExpressAjax.setUpdateButton($('update_order'),'<?php echo $this->escapeHtml($this->getUpdateOrderSubmitUrl()) ?>','details-reload');
if ($('billing:as_shipping')) {
    PayPalExpressAjax.setCopyElement($('billing:as_shipping'));
}
//]]>
</script>
