<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * Customer login form template
 *
 * @see Mage_Customer_Block_Form_Login
 */
?>
<div class="account-login">
    <div class="page-title">
        <h1><?php echo $this->__('<PERSON><PERSON> or Create an Account') ?></h1>
    </div>
    <?php echo $this->getMessagesBlock()->toHtml() ?>
    <?php /* Extensions placeholder */ ?>
    <?php echo $this->getChildHtml('customer.form.login.extra')?>
    <form action="<?php echo $this->getPostActionUrl() ?>" method="post" id="login-form">
        <?php echo $this->getBlockHtml('formkey') ?>
        <div class="col2-set">
            <div class="col-1 registered-users">
                <div class="content">
                    <h2><?php echo $this->__('Registered Customers') ?></h2>
                    <ul class="form-list">
                        <li>
                            <label for="email" class="required"><em>*</em><?php echo $this->__('Email Address') ?></label>
                            <div class="input-box">
                                <input type="text" name="login[username]" value="<?php echo $this->escapeHtml($this->getUsername()) ?>" id="email" class="input-text required-entry validate-email" title="<?php echo $this->__('Email Address') ?>" />
                            </div>
                        </li>
                        <li>
                            <label for="pass" class="required"><em>*</em><?php echo $this->__('Password') ?></label>
                            <div class="input-box">
                                <input type="password" name="login[password]" class="input-text required-entry validate-password" id="pass" title="<?php echo $this->__('Password') ?>" />
                            </div>
                        </li>
                        <li class="note">
                            <a href="<?php echo $this->getForgotPasswordUrl() ?>" class="f-left"><?php echo $this->__('Forgot Your Password?') ?></a>
                        </li>
                        <?php echo $this->getChildHtml('form.additional.info'); ?>
                        <li class="buttons-set">
                            <button type="submit" class="button" title="<?php echo $this->__('Login') ?>" name="send" id="send2"><?php echo $this->__('Login') ?></button>
                        </li>
                    </ul>
                    <p class="required"><?php echo $this->__('* Required Fields') ?></p>
                </div>
            </div>
            <div class="col-2 new-users">
                <div class="content">
                    <h2><?php echo $this->__('New Customers') ?></h2>
                    <div class="buttons-set">
                        <button type="button" title="<?php echo $this->__('Create an Account') ?>" class="button" onclick="window.location='<?php echo $this->getCreateAccountUrl() ?>';"><span><span><?php echo $this->__('Create an Account') ?></span></span></button>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <script type="text/javascript">
    //<![CDATA[
        var dataForm = new VarienForm('login-form', true);
    //]]>
    </script>
</div>
