<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

/* @var Mage_Wishlist_Model_Item $item */
$item = $this->getItem();
$product = $item->getProduct();
?>
<?php if($this->getSortedChildren()): ?>
    <div class="item-manage">
    <?php foreach($this->getSortedChildren() as $childName):?>
        <?php echo $this->getChildHtml($childName, false);?>
    <?php endforeach;?>
    </div>
<?php endif; ?>
<div class="description std"><div class="inner"><?php echo $this->escapeHtml($this->stripTags($product->getShortDescription()));?></div></div>
<textarea name="description[<?php echo $item->getWishlistItemId() ?>]" rows="3" cols="5" onfocus="focusComment(this)" onblur="focusComment(this)" placeholder="<?php echo Mage::helper('core')->quoteEscape($this->__('Comment')) ?>"><?php echo ($this->escapeHtml($item->getDescription())) ?></textarea>
