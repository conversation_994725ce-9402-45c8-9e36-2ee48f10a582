<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_iphone
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/* @var $this Mage_Wishlist_Block_Share_Wishlist */
?>
<div class="my-wishlist">
    <div class="page-title">
        <h1><?php echo $this->escapeHtml($this->getHeader()) ?></h1>
    </div>
    <div class="wishlist-view">
        <?php echo $this->getMessagesBlock()->toHtml() ?>
        <?php if ($this->hasWishlistItems()): ?>
        <form action="<?php echo $this->getUrl('*/*/update') ?>" method="post">
            <fieldset>
                <table class="data-table" id="wishlist-table">
                <?php foreach($this->getWishlistItems() as $item): ?>
                    <?php
                        $product = $item->getProduct();
                        $isVisibleProduct = $product->isVisibleInSiteVisibility();
                    ?>
                    <tr>
                        <td>
                            <a class="product-image" href="<?php echo $this->getProductUrl($item) ?>" title="<?php echo $this->escapeHtml($product->getName()) ?>"><img src="<?php echo $this->helper('catalog/image')->init($product, 'small_image')->resize(113, 113); ?>" width="113" height="113" alt="<?php echo $this->escapeHtml($product->getName()) ?>" /></a>
                            <h2 class="product-name"><a href="<?php echo $this->getProductUrl($item) ?>"><?php echo $this->escapeHtml($product->getName()) ?></a></h2>
                            <?php echo $this->getPriceHtml($product) ?>
                            <?php echo $this->getDetailsHtml($item) ?>

                            <?php if ($product->isSaleable()): ?>
                                <?php if ($isVisibleProduct): ?>
                                    <a href="<?php echo $this->getSharedItemAddToCartUrl($item) ?>"><?php echo $this->__('Add to Cart') ?></a>
                                <?php endif ?>
                            <?php endif; ?>
                            <p><a href="<?php echo $this->getAddToWishlistUrl($item) ?>" onclick="setLocation(this.href); return false;" class="link-wishlist"><?php echo $this->__('Add to Wishlist') ?></a></p>

                            <div class="comment"><?php echo $this->getEscapedDescription($item) ?></div>
                        </td>
                    </tr>
                <?php endforeach ?>
                </table>
            </fieldset>
            <?php if($this->isSaleable()):?>
                <div class="buttons-set">
                    <button type="button" title="<?php echo $this->__('Add All to Cart') ?>" onclick="setLocation('<?php echo $this->getUrl('*/*/allcart', array('_current'=>true)) ?>')" class="button"><?php echo $this->__('Add All to Cart') ?></button>
                </div>
            <?php endif;?>
        </form>
        <?php else: ?>
            <p><?php echo $this->__('Wishlist is empty now.') ?></p>
        <?php endif ?>
    </div>
</div>
