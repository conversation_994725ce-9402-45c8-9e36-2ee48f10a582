<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_modern
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

/**
 * Product media data template
 *
 * @see Mage_Catalog_Block_Product_View_Media
 */
?>
<?php
    $_product = $this->getProduct();
    $_helper = $this->helper('catalog/output');
?>
<?php if ($_product->getImage() != 'no_selection' && $_product->getImage()): ?>
<p class="product-image product-image-zoom">
    <?php
        $_img = '<img id="image" src="'.$this->helper('catalog/image')->init($_product, 'image').'" alt="'.$this->escapeHtml($this->getImageLabel()).'" title="'.$this->escapeHtml($this->getImageLabel()).'" />';
        echo $_helper->productAttribute($_product, $_img, 'image');
    ?>
</p>
<p class="zoom-notice" id="track_hint"><?php echo $this->__('Double click on above image to view full picture') ?></p>
<div class="zoom">
    <img id="zoom_out" src="<?php echo $this->getSkinUrl('images/slider_btn_zoom_out.gif') ?>" alt="<?php echo Mage::helper('core')->quoteEscape($this->__('Zoom Out')) ?>" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Zoom Out')) ?>" class="btn-zoom-out" />
    <div id="track">
        <div id="handle"></div>
    </div>
    <img id="zoom_in" src="<?php echo $this->getSkinUrl('images/slider_btn_zoom_in.gif') ?>" alt="<?php echo Mage::helper('core')->quoteEscape($this->__('Zoom In')) ?>" title="<?php echo $this->__('Zoom In') ?>" class="btn-zoom-in" />
</div>
<script type="text/javascript">
//<![CDATA[
    Event.observe(window, 'load', function() {
        product_zoom = new Product.Zoom('image', 'track', 'handle', 'zoom_in', 'zoom_out', 'track_hint');
    });
//]]>
</script>
<?php else: ?>
<p class="product-image">
    <?php
        $_img = '<img src="'.$this->helper('catalog/image')->init($_product, 'image')->resize(300).'" alt="'.$this->escapeHtml($this->getImageLabel()).'" title="'.$this->escapeHtml($this->getImageLabel()).'" />';
        echo $_helper->productAttribute($_product, $_img, 'image');
    ?>
</p>
<?php endif; ?>
<?php if (count($this->getGalleryImages()) > 0): ?>
<div class="more-views">
    <h2><?php echo $this->__('More Views') ?></h2>
    <ul>
    <?php foreach ($this->getGalleryImages() as $_image): ?>
        <li>
            <a href="#" onclick="popWin('<?php echo $this->getGalleryUrl($_image) ?>', 'gallery', 'width=300,height=300,left=0,top=0,location=no,status=yes,scrollbars=yes,resizable=yes'); return false;" title="<?php echo $this->escapeHtml($_image->getLabel()) ?>"><img src="<?php echo $this->helper('catalog/image')->init($this->getProduct(), 'thumbnail', $_image->getFile())->resize(66); ?>" width="66" height="66" alt="<?php echo $this->escapeHtml($_image->getLabel()) ?>" /></a>
        </li>
    <?php endforeach; ?>
    </ul>
</div>
<?php endif; ?>
