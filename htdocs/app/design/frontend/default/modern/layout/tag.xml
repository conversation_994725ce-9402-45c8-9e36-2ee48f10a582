<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_modern
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

-->
<layout version="0.1.0">

<!--
Default layout, loads most of the pages
-->

    <default>
        <!-- Mage_Tag -->
        <reference name="left">
            <block type="tag/popular" name="tags_popular" template="tag/popular.phtml"/>
        </reference>

    </default>

<!--
Customer account pages, rendered for all tabs in dashboard
-->

    <customer_account>
        <!-- Mage_Tag -->
        <reference name="customer_account_navigation">
            <action method="addLink" translate="label" module="tag"><name>tags</name><path>tag/customer/</path><label>My Tags</label></action>
        </reference>
    </customer_account>

<!--
Customer account home dashboard layout
-->

    <customer_account_index>
        <!-- Mage_Tag -->
        <reference name="customer_account_dashboard">
            <action method="unsetChild"><name>customer_account_dashboard_info2</name></action>
            <block type="tag/customer_recent" name="customer_account_dashboard_info2" as="info2" template="tag/customer/recent.phtml"/>
        </reference>
    </customer_account_index>

    <catalog_product_view translate="label">
        <label>Catalog Product View</label>
         <!-- Mage_Tag -->
         <reference name="product.info.tabs">
            <action method="addTab" translate="title" module="tag"><alias>product.tags</alias><title>Product Tags</title><block>tag/product_list</block><template>tag/list.phtml</template></action>
        </reference>
        <reference name="product.tags">
            <block type="page/html_wrapper" name="product.tag.list.list.before" as="list_before" translate="label">
                <label>Tags List Before</label>
                <action method="setMayBeInvisible"><value>1</value></action>
            </block>
        </reference>
    </catalog_product_view>

<!--
All tags page
-->

    <tag_list_index translate="label">
        <label>Tags List (All Available)</label>
        <!-- Mage_Tag -->
        <reference name="root">
            <action method="setTemplate"><template>page/2columns-right.phtml</template></action>
        </reference>
        <reference name="content">
            <block type="tag/all" name="tags_all" template="tag/cloud.phtml"/>
        </reference>
    </tag_list_index>

    <tag_product_list translate="label">
        <label>Tagged Products List</label>
        <!-- Mage_Tag -->
        <reference name="content">
            <block type="tag/product_result" name="tag_products" template="catalogsearch/result.phtml">
                <block type="catalog/product_list" name="search_result_list" template="catalog/product/list.phtml">
                    <block type="catalog/product_list_toolbar" name="product_list_toolbar" template="catalog/product/list/toolbar.phtml">
                        <block type="page/html_pager" name="product_list_toolbar_pager"/>
                    </block>
                    <action method="setToolbarBlockName"><name>product_list_toolbar</name></action>
                </block>
                <action method="setListOrders"/>
                <action method="setListModes"/>
                <action method="setListCollection"/>
            </block>
        </reference>
    </tag_product_list>

    <tag_customer_index translate="label">
        <label>Customer My Account My Tags List</label>
        <update handle="customer_account"/>
        <reference name="root">
            <action method="setHeaderTitle" translate="title" module="customer"><title>My Account</title></action>
        </reference>
        <reference name="my.account.wrapper">
            <block type="tag/customer_tags" name="customer_tags" template="tag/customer/tags.phtml"/>
        </reference>
    </tag_customer_index>

    <tag_customer_view translate="label">
        <label>Customer My Account Tag View</label>
        <update handle="customer_account"/>
        <reference name="root">
            <action method="setHeaderTitle" translate="title" module="customer"><title>My Account</title></action>
        </reference>
        <reference name="my.account.wrapper">
            <block type="tag/customer_view" name="customer_view" template="tag/customer/view.phtml"/>
        </reference>
    </tag_customer_view>

</layout>
