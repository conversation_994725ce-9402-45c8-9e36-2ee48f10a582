<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_modern
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

Supported layout update handles (action):
- customer_account_index
- customer_address_index
- customer_address_view
- customer_account_login
- customer_account_logoutsuccess
- customer_account_create
- customer_account_forgotpassword
- customer_account_confirmation
- customer_account_edit

Supported layout update handles (special):
- default

-->
<layout version="0.1.0">

<!--
Default layout, loads most of the pages
-->

    <default>
        <!-- Mage_Customer -->
        <reference name="top.links">
            <action method="addLink" translate="label title" module="customer"><label>My Account</label><url helper="customer/getAccountUrl"/><title>My Account</title><prepare/><urlParams/><position>10</position></action>
        </reference>
    </default>

<!--
Load this update on every page when customer is logged in
-->

    <customer_logged_in>
        <reference name="top.links">
            <action method="addLink" translate="label title" module="customer"><label>Log Out</label><url helper="customer/getLogoutUrl"/><title>Log Out</title><prepare/><urlParams/><position>100</position></action>
        </reference>
    </customer_logged_in>

<!--
Load this update on every page when customer is logged out
-->

    <customer_logged_out>
        <!---<reference name="right">
            <block type="customer/form_login" name="customer_form_mini_login" before="-" template="customer/form/mini.login.phtml"/>
        </reference>-->
        <reference name="top.links">
            <action method="addLink" translate="label title" module="customer"><label>Log In</label><url helper="customer/getLoginUrl"/><title>Log In</title><prepare/><urlParams/><position>100</position></action>
        </reference>
        <remove name="reorder"></remove>
    </customer_logged_out>

<!--
Layout for customer login page
-->

    <customer_account_login translate="label">
        <label>Customer Account Login Form</label>
        <!-- Mage_Customer -->
        <remove name="right"/>
        <remove name="left"/>

        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="content">
            <block type="customer/form_login" name="customer_form_login" template="customer/form/login.phtml" />
        </reference>
    </customer_account_login>

<!--
Layout for customer log out page
-->

    <customer_account_logoutsuccess translate="label">
        <label>Customer Account Logout Success</label>
        <!-- Mage_Customer -->
        <remove name="right"/>
        <remove name="left"/>

        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="content">
            <block type="core/template" name="customer_logout" template="customer/logout.phtml"/>
        </reference>
    </customer_account_logoutsuccess>

<!--
New customer registration
-->

    <customer_account_create translate="label">
        <label>Customer Account Registration Form</label>
        <!-- Mage_Customer -->
        <remove name="right"/>
        <remove name="left"/>

        <reference name="head">
            <block type="core/template" name="optional_zip_countries" as="optional_zip_countries" template="directory/js/optional_zip_countries.phtml" />
        </reference>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="content">
            <block type="customer/form_register" name="customer_form_register" template="customer/form/register.phtml">
                <block type="page/html_wrapper" name="customer.form.register.fields.before" as="form_fields_before" translate="label">
                    <label>Form Fields Before</label>
                </block>
            </block>
        </reference>
    </customer_account_create>

    <customer_account_forgotpassword translate="label">
        <label>Customer Forgot Password Form</label>
        <remove name="right"/>
        <remove name="left"/>

        <reference name="head">
            <action method="setTitle" translate="title" module="customer"><title>Forgot Your Password</title></action>
        </reference>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
            <action method="setHeaderTitle" translate="title" module="customer"><title>Password forgotten</title></action>
        </reference>
        <reference name="content">
            <!--<block type="core/template" name="forgotPassword" template="customer/form/forgotpassword.phtml"/>-->
            <block type="customer/account_forgotpassword" name="forgotPassword" template="customer/form/forgotpassword.phtml" />
        </reference>
    </customer_account_forgotpassword>

    <customer_account_changeforgotten translate="label">
        <label>Reset a Password</label>
        <remove name="right"/>
        <remove name="left"/>

        <reference name="head">
            <action method="setTitle" translate="title" module="customer">
                <title>Reset a Password</title>
            </action>
        </reference>
        <reference name="root">
            <action method="setTemplate">
                <template>page/1column.phtml</template>
            </action>
            <action method="setHeaderTitle" translate="title" module="customer">
                <title>Reset a Password</title>
            </action>
        </reference>
        <reference name="content">
            <block type="customer/account_changeforgotten" name="changeForgottenPassword" template="customer/form/resetforgottenpassword.phtml"/>
        </reference>
    </customer_account_changeforgotten>

    <customer_account_confirmation>
        <remove name="right"/>
        <remove name="left"/>

        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
            <action method="setHeaderTitle" translate="title" module="customer"><title>Send confirmation link</title></action>
        </reference>
        <reference name="content">
            <block type="core/template" name="accountConfirmation" template="customer/form/confirmation.phtml"/>
        </reference>
    </customer_account_confirmation>

    <customer_account_edit translate="label">
        <label>Customer Account Edit Form</label>
        <update handle="customer_account"/>
        <reference name="root">
            <action method="setHeaderTitle" translate="title" module="customer"><title>Edit Account Info</title></action>
        </reference>
        <reference name="my.account.wrapper">
            <block type="customer/form_edit" name="customer_edit" template="customer/form/edit.phtml"/>
        </reference>
    </customer_account_edit>

<!--
Customer account pages, rendered for all tabs in dashboard
-->

    <customer_account translate="label">
        <label>Customer My Account (All Pages)</label>
        <!-- Mage_Customer -->
        <reference name="root">
            <action method="setTemplate"><template>page/2columns-left.phtml</template></action>
        </reference>

        <reference name="content">
            <block type="page/html_wrapper" name="my.account.wrapper" translate="label">
                <label>My Account Wrapper</label>
                <action method="setElementClass"><value>my-account</value></action>
            </block>
        </reference>

        <reference name="left">
            <block type="customer/account_navigation" name="customer_account_navigation" before="-" template="customer/account/navigation.phtml">
                <action method="addLink" translate="label" module="customer"><name>account</name><path>customer/account/</path><label>Account Dashboard</label></action>
                <action method="addLink" translate="label" module="customer"><name>account_edit</name><path>customer/account/edit/</path><label>Account Information</label></action>
                <action method="addLink" translate="label" module="customer"><name>address_book</name><path>customer/address/</path><label>Address Book</label></action>
            </block>
            <remove name="tags_popular"/>

        </reference>
    </customer_account>

<!--
Customer account home dashboard layout
-->

    <customer_account_index translate="label">
        <label>Customer My Account Dashboard</label>
        <update handle="customer_account"/>
        <!-- Mage_Customer -->
        <reference name="root">
            <action method="setTemplate"><template>page/2columns-left.phtml</template></action>
        </reference>
        <reference name="my.account.wrapper">
            <block type="customer/account_dashboard" name="customer_account_dashboard" template="customer/account/dashboard.phtml">
                <block type="customer/account_dashboard_hello" name="customer_account_dashboard_hello" as="hello" template="customer/account/dashboard/hello.phtml"/>
                <block type="core/template" name="customer_account_dashboard_top" as="top" />
                <block type="customer/account_dashboard_info" name="customer_account_dashboard_info" as="info" template="customer/account/dashboard/info.phtml"/>
                <block type="customer/account_dashboard_newsletter" name="customer_account_dashboard_newsletter" as="newsletter" template="customer/account/dashboard/newsletter.phtml"/>
                <block type="customer/account_dashboard_address" name="customer_account_dashboard_address" as="address" template="customer/account/dashboard/address.phtml"/>
                <block type="core/template" name="customer_account_dashboard_info1" as="info1" />
                <block type="core/template" name="customer_account_dashboard_info2" as="info2" />
            </block>
        </reference>

    </customer_account_index>

<!--
Customer account address book
-->

    <customer_address_index translate="label">
        <label>Customer My Account Address Book</label>
        <!-- Mage_Customer -->
        <update handle="customer_account"/>
        <reference name="my.account.wrapper">
            <block type="customer/address_book" name="address_book" before="-" template="customer/address/book.phtml"/>
        </reference>
    </customer_address_index>

<!--
Customer account address edit page
-->

    <customer_address_form translate="label">
        <label>Customer My Account Address Edit Form</label>
        <!-- Mage_Customer -->
        <update handle="customer_account"/>
        <reference name="head">
            <block type="core/template" name="optional_zip_countries" as="optional_zip_countries" template="directory/js/optional_zip_countries.phtml" />
        </reference>
        <reference name="my.account.wrapper">
            <block type="customer/address_edit" name="customer_address_edit" template="customer/address/edit.phtml"/>
        </reference>
    </customer_address_form>

</layout>
