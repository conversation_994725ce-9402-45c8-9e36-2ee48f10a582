<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category   design_default
 * @package    Mage
 * @copyright  Copyright (c) 2004-2007 Irubin Consulting Inc. DBA Varien (http://www.varien.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<layout version="0.1.0">
    <default>
        <reference name="head">
            <action method="addItem"><type>skin_css</type><name>css/clnews/style.css</name></action>
            <action method="addJs"><script>jquery/jquery-1.7.1-min.js</script></action>
            <action method="addJs"><script>commercelab/noconflict.js</script></action>
            <action method="addJs"><script>commercelab/treeview/jquery.treeview.pack.js</script></action>
            <action method="addJs"><script>commercelab/category_tree.js</script></action>

            <action method="addCss"><stylesheet>css/commercelab/treeview/jquery.treeview.css</stylesheet></action>
        </reference>
        <reference name="right">
            <block type="clnews/settings" name="right.clnews.menu" before="-">
                <action method="setTemplate" ifconfig="clnews/news/showrightblock">
                    <template>clnews/menu.phtml</template>
                </action>
            </block>
            <block type="clnews/news" name="right.clnews.latest" after="right.clnews.menu">
                <action method="setTemplate" ifconfig="clnews/news/showlatestnews">
                    <template>clnews/latest.phtml</template>
                </action>
            </block>
        </reference>
        <reference name="left">
            <block type="clnews/settings" name="left.clnews.menu"  before="-">
                <action method="setTemplate" ifconfig="clnews/news/showleftblock">
                    <template>clnews/menu.phtml</template>
                </action>
            </block>
        </reference>
        <reference name="top.links">
            <block type="clnews/settings" name="top.clnews.link">
                <action method="getTopLink"></action>
            </block>
        </reference>
    </default>

    <clnews_index_index>
        <reference name="root">
            <action method="setTemplate"><template>page/2columns-right.phtml</template></action>
        </reference>
        <reference name="head">
            <action method="addJs"><script>commercelab/clnews/news.js</script></action>
        </reference>
        <reference name="content">
            <block type="clnews/news" name="news" template="clnews/list.phtml"/>
        </reference>
    </clnews_index_index>

    <clnews_newsitem_view>
        <reference name="root">
            <action method="setTemplate"><template>page/2columns-right.phtml</template></action>
        </reference>
        <reference name="head">
            <action method="addJs"><script>commercelab/clnews/news.js</script></action>
        </reference>
        <reference name="content">
            <!-- <block type="core/template" name="contactForm" template="contacts/form.phtml"/> -->
            <block type="clnews/newsitem" name="newsitem" template="clnews/newsitem.phtml"/>
        </reference>
    </clnews_newsitem_view>

    <clnews_rss_index>
        <block type="clnews/rss" output="toHtml" name="news.rss"/>
    </clnews_rss_index>
</layout>
