<div class="news-item-content">
    <?php $item = $this->getNewsitem(); ?>
    <h4><?php echo $item->getTitle() ?></h4>
    <?php if (Mage::helper('clnews')->showDate()): ?>
        <span class="date"><?php echo Mage::helper('clnews')->formatDate($item->getNewsTime()) ?></span>
    <?php endif; ?>
    <?php if (Mage::helper('clnews')->showCategory()): ?>
        <?php if ($this->getCategoryByNews($item->getID())->getData('title') != ''): ?>
            <span class="date">&nbsp;|&nbsp; <?php echo $this->getCategoryByNews($item->getID())->getData('title'); ?></span>
        <?php endif; ?>
    <?php endif; ?>
    
    <div class="social">
        <?php if (Mage::helper('clnews')->getGoogleAccess()): ?>
            <script type="text/javascript">
                (function() {
                    var po = document.createElement('script'); po.type = 'text/javascript'; po.async = true;
                    po.src = 'https://apis.google.com/js/plusone.js';
                    var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(po, s);
                    <?php if (isset($comments) && count($comments)): ?>
                        AjaxSend(1, <?php echo $item->getID() ?>, false);
                    <?php endif ?>
                })();
            </script>
            <div class="google_button"><g:plusone></g:plusone></div>
        <?php endif; ?>
        <?php if (Mage::helper('clnews')->getFaceBookAccess()): ?>
            <div id="fb-root"></div>
            <script>(function(d, s, id) {
              var js, fjs = d.getElementsByTagName(s)[0];
              if (d.getElementById(id)) return;
              js = d.createElement(s); js.id = id;
              js.src = "//connect.facebook.net/<?php echo Mage::app()->getLocale()->getDefaultLocale() ?>/all.js#xfbml=1";
              fjs.parentNode.insertBefore(js, fjs);
            }(document, 'script', 'facebook-jssdk'));</script>
            <div class="fb-like" data-send="false" data-layout="button_count" data-width="110" data-show-faces="false"></div>
        <?php endif; ?>
        <?php if (Mage::helper('clnews')->getLinkedInAccess()): ?>
            <script src="http://platform.linkedin.com/in.js" type="text/javascript"></script>
            <script type="IN/Share" data-counter="right"></script>
        <?php endif; ?>
        <?php if (Mage::helper('clnews')->getTwitterAccess()): ?>
            <script>!function(d,s,id){var js,fjs=d.getElementsByTagName(s)[0];if(!d.getElementById(id)){js=d.createElement(s);js.id=id;js.src="//platform.twitter.com/widgets.js";fjs.parentNode.insertBefore(js,fjs);}}(document,"script","twitter-wjs");</script>
            <a href="https://twitter.com/share" class="twitter-share-button">Tweet</a>
        <?php endif; ?>
        <a href="<?php echo $item->getUrl() ?>?mode=print" class="print-btn"><?php echo $this->__('Print Version'); ?></a>
    </div>
    
    <?php if ($item->getImageFullContentShow() && $item->getImageFullContent()): ?>
        <div class="news_image">
            <?php $imageSize = $this->getFullImageSize($item) ?>
            <img src="<?php echo Mage::helper('clnews')->resizeImage(str_replace('clnews/', '', $item->getImageFullContent()), $imageSize['width'], $imageSize['height'], 'clnews'); ?>" />
        </div>
    <?php endif; ?>
    
    <div class="news">
        <?php echo Mage::helper('clnews')->contentFilter($item->getFullContent()) ?>
        <?php if ($item->getDocument()!=''): ?>
            <p class="loadfile"><a href="<?php echo Mage::Helper('clnews')->getFileUrl($item) ?>"><?php if ($item->getLink()): echo $item->getLink(); else: echo $this->__('Download attached document'); endif; ?></a></p>
        <?php endif ?>
        <?php if (Mage::helper('clnews')->showAuthor()): ?>
            <?php if ($item->getAuthor()): ?>
                <span><?php echo $this->__('Author: ').$item->getAuthor(); ?></span>
            <?php endif; ?>
        <?php endif; ?>
    </div>
    
    <p>
    <?php if (Mage::helper('clnews')->getTagsAccess()): ?>
        <?php if ($item->getTags()): ?>
        <?php $arr = preg_split('/,/', $item->getTags()); ?>
        <?php $tags = ''; ?>
        <?php $count = count($arr); ?>
        <?php $k = 1; ?>
        <?php foreach ($arr as $val): ?>
        <?php if ($k == $count): ?>
        <?php $tags .= '<a href="' . $this->getUrl(Mage::helper('clnews')->getRoute() . '/index/index/q/' . urlencode(trim($val)) . '/') . '" >' . trim($val) . '</a> '; ?>
        <?php else: ?>
        <?php $tags .= '<a href="' . $this->getUrl(Mage::helper('clnews')->getRoute() . '/index/index/q/' . urlencode(trim($val)) . '/') . '" >' . trim($val) . '</a>, '; ?>
        <?php endif; ?>
        <?php $k++; ?>
        <?php endforeach; ?>
        <img src="<?php echo $this->getSkinUrl('css/clnews/images/i-tags.gif')?>"/>&nbsp;<?php echo $tags; ?>
        <?php endif; ?>
    <?php endif; ?>
    </p>
    
    <?php echo $this->getMessagesBlock()->getGroupedHtml(); ?>
    
    <div class="news-item-comment">
    <?php if($item->getCommentsEnabled()): ?>
        <?php $comments = $this->getComments(); ?>
        <div>
            <a name="commentBox" ></a>
            <h2><?php echo $this->getCommentTotalString($comments);?></h2>
        </div>
        <div id="clloader" style="display:none;"><img src="<?php echo $this->getSkinUrl('images/clnews/ajax-loader.gif')?>"/></div>
        <div id="comment_block">
        <?php $k = 0;?>
        <?php foreach ($comments as $comment):  ?>
            <div id="comment_item_<?php echo $k;?>" class="comment-item">
                <h4 class="username"><?php echo $comment->getUser();?></h4>
                <?php echo $this->__("posted on")?> <?php echo Mage::helper('clnews')->formatDate($comment->getCreatedTime());?>
                <div><?php echo $comment->getComment();?></div>
            </div>
            <?php $k++;?>
        <?php endforeach; ?>
        </div>
        <?php if($this->getLastPageNum()>1): ?>
            <div class="pager">
                <p class="page">
                <span id="prev"><span class="prev" style="display:none;"><?php echo $this->__('Previous') ?></span></span>
                <?php foreach ($this->getPages() as $_page): ?>
                    <?php if ($this->isPageCurrent($_page)): ?>
                        <span id="page_<?php echo $_page ?>" class="page current" onclick="AjaxSend(<?php echo $_page ?>, <?php echo $item->getId() ?>, true);"><?php echo $_page ?></span>
                    <?php else: ?>
                        <span id="page_<?php echo $_page ?>" class="page" onclick="AjaxSend(<?php echo $_page ?>, <?php echo $item->getId() ?>, true);"><?php echo $_page ?></span>
                    <?php endif ?>
                <?php endforeach; ?>
                    <span id="next"><span class="next"><?php echo $this->__('Next') ?></span></span>
                </p>
            </div>
        <?php endif;?>
    
        <?php if (!$this->getRequireLogin() || ($this->getRequireLogin()&&$this->helper('customer')->isLoggedIn())): ?>
            <form id="postComment" method="post" action="">
                <h4><?php echo Mage::helper('clnews')->__('Submit Comment') ?></h4>
                <ul>
                    <li>
                        <?php if (!$this->helper('customer')->isLoggedIn()): ?>
                            <div class="input-box">
                                <label for="name"><?php echo Mage::helper('clnews')->__('Name') ?> <span class="required">*</span></label>
                                <input name="user" id="user" value="<?php echo $this->getCommentName(); ?>" title="<?php echo $this->__('Name') ?>" value="<?php echo $this->htmlEscape($this->helper('clnews')->getUserName()) ?>" class="required-entry input-text" type="text" />
                            </div>
                            <div class="clear"></div>
        
                            <div class="input-box">
                                <label for="email"><?php echo Mage::helper('clnews')->__('Email') ?> <span class="required">*</span></label>
                                <input name="email" id="email" value="<?php echo $this->getCommentEmail(); ?>" title="<?php echo $this->__('Email') ?>" value="<?php echo $this->htmlEscape($this->helper('clnews')->getUserEmail()) ?>" class="required-entry input-text validate-email" type="text" />
                                <small><?php echo $this->__('(will not be displayed)') ?></small>
                            </div>
                        <?php else: ?>
                            <input name="post_id" type="hidden" value="<?php echo $item->getPostId();?>" />
                            <input name="email" type="hidden" value="<?php echo $this->htmlEscape($this->helper('clnews')->getUserEmail()) ?>"/><br/>
                            <input name="user" type="hidden" value="<?php echo $this->htmlEscape($this->helper('clnews')->getUserName()) ?>"/><br/>
                        <?php endif ?>
                        <div class="clear"></div>
                        <div class="input-box">
                            <label for="comment"><?php echo Mage::helper('clnews')->__('Comment') ?> <span class="required">*</span></label>
                            <textarea name="comment" id="comment" title="<?php echo Mage::helper('clnews')->__('Comment') ?>" class="required-entry input-text" style="height:150px;width:400px;" cols="50" rows="5"><?php echo $this->getCommentText(); ?></textarea>
                        </div>
                    </li>
                    <?php if (Mage::getStoreConfig('clnews/captcha/enabled')): ?>
                        <!-- - -->
                    <?php endif ?>
                    </li>
                </ul>
                <div class="button-set">
                    <input name="news_id" type="hidden" value="<?php echo $item->getId() ?>" />
                    <p class="required"><?php echo $this->__('* Required Fields') ?></p>
                    <button class="button" type="submit"><span><span><?php echo Mage::helper('clnews')->__('Submit Comment') ?></span></span></button>
                </div>
            </form>
        
            <script type="text/javascript">
            var contactForm = new VarienForm('postComment', false);
            </script>
        <?php else: ?>
            <p><?php echo Mage::helper('clnews')->__('You must be logged in to post a comment.');?></p>
            <p><a href="<?php echo Mage::helper('customer')->getLoginUrl(); ?>"><?php echo Mage::helper('clnews')->__('click here');?></a> <?php echo Mage::helper('clnews')->__('to log in');?></p>
        <?php endif; ?>
    <?php endif; ?>
    </div>
    <a href="<?php echo $this->getBackUrl() ?>" class="back">&laquo; <?php echo $this->__('Back') ?></a>
</div>
<script type="text/javascript">
    function AjaxSend(page, id, showLoader)
    {
        var url = "<?php echo Mage::getBaseUrl()?>clnews/newsitem/ajax/id/" + id + "/page/" + page + "/";
        if (showLoader) {
            jQuery("#clloader").css("display","block");
        }
        jQuery.ajax({
            url: url,
            dataType: 'json',
            success: function(data) {
                var content = '';
                jQuery(".comment-item").remove();
                for(var i = 0; i < data['dat'].cnt; i++) {
                    content = '<div id="comment_item_'+ i +'" class="comment-item"><h4 class="username">'+ data['collection'][i].user +'</h4> <?php echo $this->__("posted on")?> ' + data['collection'][i].created_time + '<div>' + data['collection'][i].comment + '</div></div>';
                    jQuery('#comment_block').append(content);
                }
                jQuery(".prev").remove();
                if (typeof(data['dat'].back_url) != 'undefined') {
                    jQuery("#prev").append('<span class="prev" ' + data['dat'].back_url + ' ><?php echo $this->__('Previous') ?></span>');
                }
                jQuery(".next").remove();
                if (typeof(data['dat'].fovard_url) != 'undefined') {
                    jQuery("#next").append('<span class="next" ' + data['dat'].fovard_url + ' ><?php echo $this->__('Next') ?></span>');
                }
                jQuery("#clloader").css("display","none");
            }
        });
        jQuery(".page").removeClass("current");
        var cl = 'page_' + page;
        jQuery("#" + cl).addClass("current");
    }
</script>