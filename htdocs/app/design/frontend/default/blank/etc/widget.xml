<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_blank
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<widgets>
    <new_products>
        <parameters>
            <template>
                <values>
                    <list_default translate="label">
                        <value>catalog/product/widget/new/column/new_default_list.phtml</value>
                        <label>New Products Images and Names Template</label>
                    </list_default>
                    <list_names translate="label">
                        <value>catalog/product/widget/new/column/new_names_list.phtml</value>
                        <label>New Products Names Only Template</label>
                    </list_names>
                    <list_images translate="label">
                        <value>catalog/product/widget/new/column/new_images_list.phtml</value>
                        <label>New Products Images Only Template</label>
                    </list_images>
                </values>
            </template>
        </parameters>
        <supported_blocks>
            <left_column>
                <block_name>left</block_name>
                <template>
                    <default>list_default</default>
                    <names_only>list_names</names_only>
                    <images_only>list_images</images_only>
                </template>
            </left_column>
            <main_content>
                <block_name>content</block_name>
                <template>
                    <grid>default</grid>
                    <list>list</list>
                </template>
            </main_content>
            <right_column>
                <block_name>right</block_name>
                <template>
                    <default>list_default</default>
                    <names_only>list_names</names_only>
                    <images_only>list_images</images_only>
                </template>
            </right_column>
        </supported_blocks>
    </new_products>

    <recently_viewed>
        <parameters>
            <template>
                <values>
                    <list_default translate="label">
                        <value>reports/widget/viewed/column/viewed_default_list.phtml</value>
                        <label>Viewed Products Images and Names Template</label>
                    </list_default>
                    <list_names translate="label">
                        <value>reports/widget/viewed/column/viewed_names_list.phtml</value>
                        <label>Viewed Products Names Only Template</label>
                    </list_names>
                    <list_images translate="label">
                        <value>reports/widget/viewed/column/viewed_images_list.phtml</value>
                        <label>Viewed Products Images Only Template</label>
                    </list_images>
                </values>
            </template>
        </parameters>
        <supported_blocks>
            <left_column>
                <block_name>left</block_name>
                <template>
                    <default>list_default</default>
                    <names_only>list_names</names_only>
                    <images_only>list_images</images_only>
                </template>
            </left_column>
            <main_content>
                <block_name>content</block_name>
                <template>
                    <grid>default</grid>
                    <list>list</list>
                </template>
            </main_content>
            <right_column>
                <block_name>right</block_name>
                <template>
                    <default>list_default</default>
                    <names_only>list_names</names_only>
                    <images_only>list_images</images_only>
                </template>
            </right_column>
        </supported_blocks>
    </recently_viewed>

    <recently_compared>
        <parameters>
            <template>
                <values>
                    <list_default translate="label">
                        <value>reports/widget/compared/column/compared_default_list.phtml</value>
                        <label>Compared Products Images and Names Template</label>
                    </list_default>
                    <list_names translate="label">
                        <value>reports/widget/compared/column/compared_names_list.phtml</value>
                        <label>Compared Product Names Only Template</label>
                    </list_names>
                    <list_images translate="label">
                        <value>reports/widget/compared/column/compared_images_list.phtml</value>
                        <label>Compared Product Images Only Template</label>
                    </list_images>
                </values>
            </template>
        </parameters>
        <supported_blocks>
            <left_column>
                <block_name>left</block_name>
                <template>
                    <default>list_default</default>
                    <names_only>list_names</names_only>
                    <images_only>list_images</images_only>
                </template>
            </left_column>
            <main_content>
                <block_name>content</block_name>
                <template>
                    <grid>default</grid>
                    <list>list</list>
                </template>
            </main_content>
            <right_column>
                <block_name>right</block_name>
                <template>
                    <default>list_default</default>
                    <names_only>list_names</names_only>
                    <images_only>list_images</images_only>
                </template>
            </right_column>
        </supported_blocks>
    </recently_compared>
</widgets>
