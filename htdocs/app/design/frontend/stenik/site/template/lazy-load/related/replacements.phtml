<?php
/** @var PFG_Lazyblocks_Block_Html_Dynamic $this */
$abstractBlock = $this->getLayout()->createBlock('catalog/product_list_related');
$_productCollection = $this->getData('products');
$_helper = $this->helper('catalog/output');
$productImageSufix = $this->helper('stenik_sitesettings')->getproductImagesAltSufix();

?>
<?php $productImageSufix = $this->helper('stenik_sitesettings')->getproductImagesAltSufix(); ?>

<div class="wide-area upsell-products replacement-products">
  <div class="">
    <div class="row">
      <div class="col-xs-12">
        <div class="row-header marginB30 clearfix">
          <span class="row-title"><?php echo $this->htmlEscape($this->getData('name')) ?></span>
        </div>
      </div>
    </div>
    <div class="row products-list owl-carousel product-slider-replacements">
        <?php foreach ($_productCollection as $_link): ?>
            <?php /** @var $_link Mage_Catalog_Model_Product */ ?>

            <?php $secondImg = ($this->helper('stenik_sitesettings')->getProductListingSecondImg() ? Mage::helper('stenik_base/catalog_product')->getSecondImageFile($_link) : null) ?>

          <div class="product-box clever-link">
                        <span class="image-wrapper<?php if ($secondImg): ?> has-second-img<?php endif; ?>">
                        <a href="<?php echo $_link->getProductUrl() ?>">
                            <img
                                 class="first lazyload"
                                 data-src="<?php echo $this->helper('catalog/image')->init($_link, 'small_image')->resize(285, 285); ?>"
                                 data-srcset="<?php echo $this->helper('catalog/image')->init($_link, 'small_image')->resize(570, 570); ?> 2x"
                                 alt="<?php echo $this->stripTags($this->getImageLabel($_link, 'small_image'), null, true) ?>"
                                 title="<?php echo $this->stripTags($this->getImageLabel($_link, 'small_image'), null, true) . ' - ' . $productImageSufix ?>"
                            >
                            <?php if ($secondImg): ?>
                              <img
                                   class="second lazyload"
                                   data-src="<?php echo $this->helper('catalog/image')->init($_link, 'small_image', $secondImg)->resize(285, 285); ?>"
                                   data-srcset="<?php echo $this->helper('catalog/image')->init($_link, 'small_image', $secondImg)->resize(570, 570); ?> 2x"
                                   alt="<?php echo $this->stripTags($this->getImageLabel($_link, 'small_image'), null, true) ?> - 2"
                                   title="<?php echo $this->stripTags($this->getImageLabel($_link, 'small_image'), null, true) . ' - 2 - ' . $productImageSufix ?>"
                              >
                            <?php endif; ?>
                        </a>
                            <?php
                            echo $this->getlayout()->createBlock('core/template')->setTemplate('catalog/product/labels.phtml')->setProduct($_link)->toHtml();
                            ?>
                        </span>
            <span class="product-box-info">
                            <span class="product-title-sku">
                                <a href="<?php echo $_link->getProductUrl() ?>"
                                   class="title stretched-link"><?php echo $_helper->productAttribute($_link, $_link->getName(), 'name') ?></a>
                                <?php $skuLabel = $_link->getResource()->getAttribute('sku')->getStoreLabel(); ?>
                                <p class="sku"><?php echo $skuLabel ?>: <?php echo $this->escapeHtml($_link->getSku()) ?></p>
                            </span>
                            <?php echo $abstractBlock->getPriceHtml($_link, true, '-related') ?>
                        </span>
          </div>
        <?php endforeach ?>
    </div>
  </div>
</div>

<script>
  jQuery(function ($) {
    $(".product-slider-replacements").owlCarousel({
      loop: false,
      items: 2,
      slideBy: 1,
      nav: true,
      navText: false,
      slideSpeed: 500,
      autoPlay: false,
      autoplayTimeout: 5000,
      autoplayHoverPause: true,
      addClassActive: true,
      dots: false,
      stagePadding: 1,
      responsive: {
        0: {items: 2},
        480: {items: 2},
        767: {items: 2}
      }
    });
  });
</script>
