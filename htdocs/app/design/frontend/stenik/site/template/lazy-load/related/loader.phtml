<?php
/** @var Carco_Catalog_Block_Related_Abstract $this */
$start = microtime(true);
$productIds = $this->getRelatedProducts();
if (empty($productIds)) {
    return;
}

$template = $this->getBlockTemplate();
$data = [
    'template' => $template,
    'products' => json_encode($productIds),
];
?>
<div class="lazyload <?= $template; ?>" style="display: none"
     data-include="<?= Mage::getBaseUrl() . 'lazyblock/index/dynamic?' . http_build_query($data) ?>"></div>
