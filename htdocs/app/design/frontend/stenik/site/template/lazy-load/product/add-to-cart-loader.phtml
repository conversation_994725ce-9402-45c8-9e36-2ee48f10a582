<?php
/** @var PFG_Lazyblocks_Block_Product_AddToCartLoader $this */

$brandsUniqueId = uniqid('add_to_cart_loader', true);
$addToCartSelectors = array_unique(
    array_merge(
        Mage::helper('stenik_ajaxaddtocart')->getAddToCartTriggerSelectors(),
        Mage::helper('stenik_ajaxaddtocart')->getAddToCartFormTriggerSelectors()
    )
);
$addToCartFormButtonsSelectors = Mage::helper('stenik_ajaxaddtocart')->getAddToCartFormTriggerSelectors();
?>
<div class="lazyload addtocart-lazy" id="<?= uniqid('add_to_cart_loader', true); ?>"
     data-include="<?= $this->getLazyloadUrl() ?>"
     data-expand="20"></div>

<div id="stenik-ajaxaddtocart-result" style="display: none"></div>
<div id="stenik-ajaxaddtocart-overlay" style="display: none"></div>

<script type="text/javascript">
  function removeDomClasses(classToRemove, classToReplace) {
    const elements = document.querySelectorAll('.'+classToRemove);
    for (let i = 0; i < elements.length; i++) {
      const el = elements[i];
      el.classList.remove(classToRemove);
      if(classToReplace) {
        el.classList.add(classToReplace);
      }
    }
  }

  function showLazyblock(classToRemove) {
    const elements = document.querySelectorAll('.lazyload.'+classToRemove);
    for (let i = 0; i < elements.length; i++) {
      const el = elements[i];
      el.style.display = '';
    }
  }

  jQuery(document).on('lazyloaded', function (e) {
    if (!jQuery(e.target).first().hasClass('addtocart-lazy')) {
      return;
    }

    (function ($) {
      const stockItemWrapper = document.getElementById('stock-item-wrapper')
      if (stockItemWrapper) {
        removeDomClasses('hidden-saleable', 'shown-saleable')
        showLazyblock('upsell')
      } else {
        removeDomClasses('hidden-not-saleable')
        showLazyblock('replacements')

        document.getElementById("cntdown_source").innerHTML = document.getElementById('dynamic_cntdown_source').value;
        document.getElementById("cntdown_ctime").innerHTML = document.getElementById('dynamic_cntdown_ctime').value;
      }

      $('#fast-order-opener').colorbox({inline: true});

      const areFieldsValid = function ($fields) {
        $fields = $($fields);

        let areAllFieldsValid = true;

        $fields.each(function () {
          var fieldClasses = this.className.split(' ');
          for (var i = 0; i < fieldClasses.length; i++) {
            try {
              var validator = Validation.get(fieldClasses[i]);

              if (Validation.isVisible(this) && !validator.test($F(this), this)) {
                areAllFieldsValid = false;
                return false;
              }
            } catch (e) {
              areAllFieldsValid = false;
              return false;
            }
          }
        });

        return areAllFieldsValid;
      };

      const addToCartTrigger = function (event) {
        const $element = $(this);
        const $form = $element.closest('form');

        let data = {};
        if ($form.length) {
          if (!areFieldsValid($form.find('input,select,textarea'))) {
            return true;
          }

          const dataArray = $form.serializeArray();

          for (let i = 0; i < dataArray.length; i++) {
            if (typeof dataArray[i].name === 'undefined') {
              continue;
            }

            if (typeof dataArray[i].value === 'undefined') {
              dataArray[i].value = '';
            }

            if (dataArray[i].name.match(/\[\]$/)) {
              // Array name

              if (typeof data[dataArray[i].name] == 'undefined') {
                data[dataArray[i].name] = [];
              }

              data[dataArray[i].name].push(dataArray[i].value);
            } else {
              data[dataArray[i].name] = dataArray[i].value;
            }
          }
        }

        if (typeof data.product === 'undefined') {
          data.product = false;

          let destination = $element.data('origOnclickAttr');
          if (!destination) {
            destination = $element.attr('href');
          }

          if (destination) {
            const matches = destination.match(/product\/(\d+)/)
            if (matches) {
              data.product = matches[1];
            }
          }
        }

        if (!data.product) {
          return true;
        }

        data.form_key = getUserFormKey()
        $('#stenik-ajaxaddtocart-overlay').show();

        var hideOverlay = true;
        $.ajax({
          url: <?= json_encode($this->getUrl('stenik_ajaxaddtocart/index/add')) ?>,
          method: 'post',
          dataType: 'json',
          data: data
        }).done(function (response) {
          if ((typeof response.update !== 'undefined') && response.update) {
            for (var i = 0; i < response.update.length; i++) {
              $(response.update[i].selector).replaceWith(response.update[i].html);
            }
          }

          if ((typeof response.html !== 'undefined') && response.html) {
            hideOverlay = false;
            const $resultWrapper = $('#stenik-ajaxaddtocart-result');
            $resultWrapper.html(response.html);
            $resultWrapper.find('.closeAddToCartResult').click(function () {
              $resultWrapper.hide();
              $('#stenik-ajaxaddtocart-overlay').hide();
            });
            $(document).keyup(function (e) {
              if (e.keyCode == 27) {
                $resultWrapper.hide();
                $('#stenik-ajaxaddtocart-overlay').hide();
              }
            });

            $resultWrapper.show();
          }
        }).fail(function () {
          alert(<?= json_encode($this->__('Something went wrong. Please try again.')) ?>);
        }).always(function () {
          if (hideOverlay) {
            $('#stenik-ajaxaddtocart-overlay').hide();
          }
        });

        if (typeof event != 'undefined') {
          Event.stop(event);
          event.preventDefault();
          event.stopPropagation();
          event.stopImmediatePropagation();
        }

        return false;
      };

      $(<?= json_encode(implode(',', $addToCartSelectors)) ?>).each(function () {
        var $element = $(this);
        $element.click(addToCartTrigger.bind(this));

        var onclickAttrFunc = $element.prop('onclick');
        if (onclickAttrFunc) {
          $element.data('origOnclickAttr', $element.attr('onclick'));
          $element.removeProp('onclick');
          $element.removeAttr('onclick');
          $element.click(onclickAttrFunc);
        }
      });

        <?php if (count($addToCartFormButtonsSelectors)): ?>
      $(<?= json_encode(implode(',', $addToCartFormButtonsSelectors)) ?>).each(function () {
        $(this).closest('form').submit(addToCartTrigger.bind(this));
      });
        <?php endif; ?>
      $('body').on('click', <?= json_encode(implode(',', $addToCartSelectors)) ?>, addToCartTrigger);

      if (!stockItemWrapper) {
        refreshCartReservationTimers();
      }
    })(jQuery);
  });
</script>
