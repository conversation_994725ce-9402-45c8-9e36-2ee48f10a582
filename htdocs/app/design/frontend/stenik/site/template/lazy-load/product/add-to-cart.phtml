<?php
/** @var PFG_Lazyblocks_Block_Product_AddToCart $this */
$_product = $this->getProduct();
?>
<?php if ($_product->isSaleable()): ?>
    <div id="stock-item-wrapper">
        <?= $this->getPriceHtml($_product); ?>

        <input type="text" name="qty" id="qty" value="1" title="<?php echo $this->__('Qty') ?>" class="qty hidden-qty"
               readonly/>

        <a class="button checkout-color add-to-cart" href="javascript:void(0);"
           onclick="productAddToCartForm.submit(this)">
            <svg aria-hidden="true" class="icon-svg shopping-cart">
                <use href="<?php echo $this->getBaseUrl() . 'svg/svg-sprite.svg' . '#shopping-cart' ?>"></use>
            </svg>
            <?php echo $this->__('Add to Cart'); ?>
        </a>

        <a class="button checkout-color" id="fast-order-opener" href="#fast-order-modal">
            <?php echo $this->__('Fast Order') ?>
        </a>

        <input type="hidden" name="dynamic_form_key" id="dynamic_form_key" value="{dynamic_form_key}" readonly/>
    </div>
<?php else: ?>
    <div class="clearfix">
        <div class="d-block">
            <?= sprintf($this->__('This product is not available. Please call %s for more information.'), Mage::getStoreConfig('general/store_information/phone')); ?>
        </div>

        <input type="hidden" name="dynamic_cntdown_source" id="dynamic_cntdown_source" value="<?= $this->getTime(); ?>" readonly/>
        <input type="hidden" name="dynamic_cntdown_ctime" id="dynamic_cntdown_ctime" value="<?= time(); ?>" readonly/>
    </div>
<?php endif; ?>


