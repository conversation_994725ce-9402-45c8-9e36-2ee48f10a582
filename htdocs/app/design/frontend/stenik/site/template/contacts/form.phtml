<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2015 X.commerce, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<div id="messages_product_view"><?php echo $this->getMessagesBlock()->toHtml() ?></div>

<h1><?php echo Mage::helper('contacts')->__('Contact Us') ?></h1>

<div class="clearH"></div>

<div class="col-xs-6 contacts-info">
    <?php if ($cmsBlockContactsInfoText = $this->getChildHtml('cms_block.contacts_info_text')): ?>

        <?php echo $cmsBlockContactsInfoText; ?>

        <div class="clearH2"></div>

        <div style="display:none;" itemscope itemtype="https://schema.org/ContactPage">
            <div itemscope itemtype="http://schema.org/LocalBusiness">
                <span itemprop="name"><?php echo Mage::getStoreConfig('general/store_information/name'); ?></span>
                <div itemprop="address" itemscope itemtype="http://schema.org/PostalAddress">
                    <span itemprop="streetAddress">ул. „Гюешево” 83</span>
                    <span itemprop="addressLocality">кв. Сердика</span>,
                    <span itemprop="addressRegion">София</span>
                </div>
                <span itemprop="telephone"><?php echo Mage::getStoreConfig('general/store_information/phone'); ?></span>
                <span itemprop="priceRange" content="5-5000 BGN">5-5000 BGN</span>
                <img itemprop="image" src="<?php echo $this->getBaseUrl().'svg/logo.svg' ?>" alt="img" width="100" height="100">
            </div>
        </div>
    <?php endif ?>
    <div class="gmap-content">
        <div id="map_container"></div>
    </div>
</div>
<div class="col-xs-6 contacts-form">
    <h4><?php echo $this->__('Contact form');?></h4>
    <p><?php echo $this->__('For any questions and comments, please fill out the form below.');?></p>
    <form action="<?php echo $this->getFormAction(); ?>" id="contactForm" method="post">
        <div class="row">
            <div class="col-xs-12">
                <label for="name"><?php echo Mage::helper('contacts')->__('Name and Last name') ?> <em>*</em></label>
                <input name="name" id="name" value="<?php echo $this->escapeHtml($this->helper('contacts')->getUserName()) ?>" class="input-text required-entry" type="text" />
            </div>
            <div class="col-xs-12 col-sm-6">
                <label for="email"><?php echo Mage::helper('contacts')->__('E-mail') ?> <em>*</em></label>
                <input name="email" id="email" value="<?php echo $this->escapeHtml($this->helper('contacts')->getUserEmail()) ?>" class="input-text required-entry validate-email" type="email" />
            </div>
            <div class="col-xs-12 col-sm-6">
                <label for="telephone"><?php echo Mage::helper('contacts')->__('Phone') ?> <em>*</em></label>
                <input name="telephone" id="telephone" title="<?php echo Mage::helper('core')->quoteEscape(Mage::helper('contacts')->__('Phone')) ?>" value="" class="input-text required-entry" type="tel" />
            </div>
            <div class="col-xs-12">
                <label for="about"><?php echo Mage::helper('contacts')->__('About') ?> <em>*</em></label>
                <input name="about" id="about" value="" class="input-text required-entry" type="text" />
            </div>
            <div class="col-xs-12">
                <label for="comment"><?php echo Mage::helper('core')->quoteEscape(Mage::helper('contacts')->__('Message')) ?> <em>*</em></label>
                <textarea name="comment" id="comment" class="required-entry input-text"></textarea>
            </div>
        </div>
        <div class="clearH"></div>

        <?php if (Mage::helper('core')->isModuleEnabled('Stenik_GdprCompliance')): ?>
        <?php echo $this->getLayout()->createBlock('stenik_gdprcompliance/widget_contacts')->toHtml(); ?>
        <div class="clearH2"></div>
        <?php endif; ?>

        <div class="center-form-action">
            <div class="google-captcha-box">
                <?php echo $this->getChildHtml('studioforty9.recaptcha.explicit'); ?>
            </div>
            <div class="clearH2"></div>
            <input type="text" name="hideit" id="hideit" value="" style="display:none !important;" />
            <button type="submit" class="button" name="send">
                <svg aria-hidden="true" class="icon-svg mail"><use href="<?php echo $this->getBaseUrl().'svg/svg-sprite.svg' . '#mail' ?>"></use></svg>
                <?php echo Mage::helper('core')->quoteEscape(Mage::helper('contacts')->__('Send')) ?>
            </button>
        </div>
    </form>
</div>
<div class="col-xs-12 marginT10 marginB10">
	<?= $this->getLayout()->createBlock('cms/block')->setBlockId('contacts_under')->toHtml(); ?>
</div>

<script type="text/javascript" src="https://maps.googleapis.com/maps/api/js?v=3&amp;key=<?php echo $this->helper('stenik_sitesettings')->getGoogleMapsKey() ?>"></script>

<script>

    var contactForm = new VarienForm('contactForm', true);

    var map;
    function initialize() {

        var styles = [];

        var markerPosition = new google.maps.LatLng(<?php echo $this->helper('stenik_sitesettings')->getLatitude() ?>, <?php echo $this->helper('stenik_sitesettings')->getLongitude() ?>);

        var mapOptions = {
            zoom: 16,
            scrollwheel: false,
            center: markerPosition,
            mapTypeControlOptions: {
                mapTypeIds: [google.maps.MapTypeId.ROADMAP, 'map_style']
            }
        };
        var styledMap = new google.maps.StyledMapType(styles, { name: "Styled Map" });
        map = new google.maps.Map(document.getElementById('map_container'), mapOptions);

        var iconUrl = "<?php echo $this->getBaseUrl().'svg/map-pin.png' ?>",
            iconSize = new google.maps.Size(26,40);

        var marker = new google.maps.Marker({
            position: markerPosition,
            map: map,
            animation: google.maps.Animation.DROP,
            icon: {
                url: iconUrl,
                scaledSize: iconSize,
                anchor: new google.maps.Point(26,40)
            }
        });
        map.mapTypes.set('map_style', styledMap);
        map.setMapTypeId('map_style');
    }
    google.maps.event.addDomListener(window, 'load', initialize);
</script>

