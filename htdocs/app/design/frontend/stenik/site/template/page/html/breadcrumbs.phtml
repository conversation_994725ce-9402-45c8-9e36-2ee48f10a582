<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2014 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<?php $archiveCategoryId = Mage::getStoreConfig('archive_id/settings/archive_cat_id'); ?>
<?php if($crumbs && is_array($crumbs)): ?>
    <div class="row">
        <div class="col-xs-12 d-flex">
            <nav class="breadcrumbs">
                <ul itemscope itemtype="http://schema.org/BreadcrumbList">
                    <?php
                        $i = 0;
                        $lastLinkCrumbName = null;
                        foreach ($crumbs as $_crumbName=>$_crumbInfo) {
                            if (isset($_crumbInfo['link']) && $_crumbInfo['link']) {
                                $lastLinkCrumbName = $_crumbName;
                            }
                        }
                    ?>
                    <?php foreach($crumbs as $_crumbName=>$_crumbInfo): ?>
                        <?php
                            if (mb_stripos($_crumbName, 'category' . $archiveCategoryId) !== false) {
                                continue;
                            }
                            $i++;
                            if ($lastLinkCrumbName == $_crumbName && $this->getData('last_link_suffix')) {
                                $_crumbInfo['label'] = $_crumbInfo['label'] . $this->getData('last_link_suffix');
                            }
                        ?>
                        <?php if ($_crumbName == 'home' || $_crumbName == 'Home'): ?>
                            <li class="home" itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
                                <a href="<?php echo $this->getUrl('') ?>" itemprop="item" typeof="WebPage">
                                    <img alt="<?php echo $this->__('Autoparts') ?>"
                                         class="breadcrumbs-home"
                                         height="20px"
                                         width="20px"
                                         src="<?php echo $this->getSkinUrl('images/homeC1x.png'); ?>"
                                         srcset="<?php echo $this->getSkinUrl('images/homeC1x.png'); ?> 1x,
                                             <?php echo $this->getSkinUrl('images/homeC2x.png'); ?> 2x"
                                         title="<?php echo $this->__('Parts second hand Carco.bg') ?>">
                                    <i itemprop="name" content="<?php echo $this->__('Home') ?>"></i>
                                    <meta itemprop="position" content="<?php echo $i; ?>"/>
                                </a>
                            </li>
                        <?php elseif (mb_strtolower($_crumbName) == 'category3'): ?>
                            <?php continue; ?>
                        <?php else: ?>
                            <li class="<?php echo $_crumbName ?>"
                                <?php if (!$_crumbInfo['last']): ?>
                                    itemprop="itemListElement"
                                    itemtype="http://schema.org/ListItem"
                                    itemscope
                                <?php endif; ?>
                            >
                                <?php if($_crumbInfo['link']): ?>
                                    <a
                                        href="<?php echo $_crumbInfo['link'] ?>"
                                        title="<?php echo $this->htmlEscape($_crumbInfo['title']) ?>"
                                        itemprop="item"
                                        typeof="WebPage"
                                    >
                                        <span itemprop="name"><?php echo $this->htmlEscape($_crumbInfo['label']) ?></span>
                                        <meta itemprop="position" content="<?php echo $i; ?>" />
                                    </a>
                                <?php elseif($_crumbInfo['last']): ?>
                                    <span class="current">
                                        <span><?php echo $this->htmlEscape($_crumbInfo['label']) ?></span>
                                    </span>
                                <?php else: ?>
                                    <span><?php echo $this->htmlEscape($_crumbInfo['label']) ?></span>
                                <?php endif; ?>
                            </li>
                        <?php endif ?>
                    <?php endforeach; ?>
                </ul>
            </nav>
            <div class="header-text-block-wrapper desktop">
                <?= $this->getChildHtml('header_text_block'); ?>
            </div>
        </div>
    </div>
<?php endif; ?>
