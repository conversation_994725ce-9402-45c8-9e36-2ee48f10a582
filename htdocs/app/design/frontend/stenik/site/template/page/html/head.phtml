<?php
/** @var PFG_LayoutUtils_Block_Html_Head $this */
?>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="format-detection" content="telephone=no"/>
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5">

<title><?= $this->getTitle() ?></title>
<meta name="description" content="<?= htmlspecialchars($this->getDescription()) ?>" />
<meta name="keywords" content="<?= htmlspecialchars($this->getKeywords()) ?>" />
<meta name="robots" content="<?= htmlspecialchars($this->getRobots()) ?>" />

<?php if(true): ?>
  <link rel="preload" type="font/woff2" crossorigin as="font" href="<?= $this->getSkinUrl('fonts/Montserrat700c.woff2'); ?>">
  <link rel="preload" type="font/woff2" crossorigin as="font" href="<?= $this->getSkinUrl('fonts/Montserrat600c.woff2'); ?>">
  <link rel="preload" type="font/woff2" crossorigin as="font" href="<?= $this->getSkinUrl('fonts/Montserrat400c.woff2'); ?>">
  <link rel="preload" type="font/woff2" crossorigin as="font" href="<?= $this->getSkinUrl('fonts/Montserrat300c.woff2'); ?>">
  <link rel="preload" type="font/woff2" crossorigin as="font" href="<?= $this->getSkinUrl('fonts/Montserrat500c.woff2'); ?>">

  <link rel="preload" type="font/woff2" crossorigin as="font" href="<?= $this->getSkinUrl('fonts/Montserrat700.woff2'); ?>">
  <link rel="preload" type="font/woff2" crossorigin as="font" href="<?= $this->getSkinUrl('fonts/Montserrat600.woff2'); ?>">
  <link rel="preload" type="font/woff2" crossorigin as="font" href="<?= $this->getSkinUrl('fonts/Montserrat500.woff2'); ?>">
  <link rel="preload" type="font/woff2" crossorigin as="font" href="<?= $this->getSkinUrl('fonts/Montserrat400.woff2'); ?>">
  <link rel="preload" type="font/woff2" crossorigin as="font" href="<?= $this->getSkinUrl('fonts/Montserrat300.woff2'); ?>">

  <link rel="preload" type="font/woff2" crossorigin as="font" href="<?= $this->getSkinUrl('fonts/Montserrat800.woff2'); ?>">
  <link rel="preload" type="font/woff2" crossorigin as="font" href="<?= $this->getSkinUrl('fonts/Montserrat800c.woff2'); ?>">
  <link rel="preload" type="font/woff2" crossorigin as="font" href="<?= $this->getSkinUrl('fonts/Montserrat400i.woff2'); ?>">
  <link rel="preload" type="font/woff2" crossorigin as="font" href="<?= $this->getSkinUrl('fonts/Montserrat400ic.woff2'); ?>">
<?php endif; ?>

<?php if(Mage::getBlockSingleton('page/html_header')->getIsHomePage()): ?>
    <link rel="preload" href="<?= $this->getLayout()->createBlock('cms/block')->setBlockId('homepage_top_image')->toHtml(); ?>" as="image">
<?php elseif(Mage::registry('current_product')): ?>
	<?php $prod_image = (string)$this->helper('catalog/image')->init(Mage::registry('current_product'), 'small_image')->setQuality(80)->resize(570,570); ?>
	<?php $prod_retina_image = (string)$this->helper('catalog/image')->init(Mage::registry('current_product'), 'image')->setQuality(80)->resize(1140,1140) ?>
    <link rel="preload" as="image" href="<?= $prod_image ?>" imagesrcset="<?= $prod_image ?> 1x, <?= $prod_retina_image ? $prod_retina_image.' 2x' : '' ?>">
<?php elseif(Mage::registry('current_category')): ?>
    <?php $category_image = Mage::registry('current_category')->getImageUrl() ?>
    <?= $category_image ? '<link rel="preload" as="image" href="'.$category_image.'">' : '' ?>
<?php endif; ?>

<link rel="preload" as="image" href="<?= $this->getSkinUrl(Mage::getStoreConfig('design/header/logo_src')) ?>">

<link rel="icon" href="<?= $this->getFaviconFile(); ?>" type="image/x-icon" />
<link rel="shortcut icon" href="<?= $this->getFaviconFile(); ?>" type="image/x-icon" />

<meta name="user-form-key" content="" />

<script>
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.register('<?= $this->getBaseUrl() ?>sw.js').then(reg => {
      // sometime later…
      reg.update();
    });
  }
</script>

<!-- <link rel="preload" href="{catProd1}" as="image"> -->
<!-- <link rel="preload" href="{catProd2}" as="image"> -->

<?= $this->getCssJsHtml() ?>
<?= $this->getChildHtml() ?>
<?= $this->helper('core/js')->getTranslatorScript() ?>
<?= $this->getIncludes() ?>
