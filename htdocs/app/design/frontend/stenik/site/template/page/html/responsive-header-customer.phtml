<?php
    $_helper = $this->helper('catalog/product_compare');
    $_items = $_helper->getItemCount() > 0 ? $_helper->getItemCollection() : null;
?>

<?php if ($this->helper('customer')->isLoggedIn()): ?>
    <span class="icon-link clever-link" data-link="<?php echo $this->getUrl('customer/account') ?>">
        <svg aria-hidden="true" class="icon-svg user"><use href="<?php echo $this->getBaseUrl().'svg/svg-sprite.svg' . '#user' ?>"></use></svg>
        <?php echo $this->__('Profile');?>
    </span>
    <span class="icon-link clever-link" data-link="<?php echo $this->getUrl('customer/account/logout') ?>">
        <svg aria-hidden="true" class="icon-svg logout"><use href="<?php echo $this->getBaseUrl().'svg/svg-sprite.svg' . '#logout' ?>"></use></svg>
        <?php echo $this->__('Logout');?>
    </span>
<?php else: ?>
    <span class="icon-link clever-link" data-link="<?php echo $this->getUrl('customer/account/login') ?>">
        <svg aria-hidden="true" class="icon-svg user"><use href="<?php echo $this->getBaseUrl().'svg/svg-sprite.svg' . '#user' ?>"></use></svg>
        <?php echo $this->__('Login');?>
    </span>
    <span class="icon-link clever-link" data-link="<?php echo $this->getUrl('customer/account/create') ?>">
        <svg aria-hidden="true" class="icon-svg register"><use href="<?php echo $this->getBaseUrl().'svg/svg-sprite.svg' . '#register' ?>"></use></svg>
        <?php echo $this->__('Registration');?>
    </span>
<?php endif ?>

<span data-link="<?php if ($_items): ?><?php echo $_helper->getListUrl() ?><?php else: ?>javascript:;<?php endif; ?>" class="clever-link icon-link<?php if ($_helper->getItemCount()): ?> has-items<?php endif ?>">
    <svg aria-hidden="true" class="icon-svg compare"><use href="<?php echo $this->getBaseUrl().'svg/svg-sprite.svg' . '#compare' ?>"></use></svg>
    <?php echo $this->__('Compare Products');?>
    <span class="notification"><?php echo $_helper->getItemCount(); ?></span>
</span>
