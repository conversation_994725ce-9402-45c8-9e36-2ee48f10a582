<?php
/** @var Mage_Catalog_Helper_Product_Compare $_helper */
  $_helper = $this->helper('catalog/product_compare');
  $comparedProducts = $this->getData('compared_product');
  $comparedProductsCount = count($comparedProducts);
?>

<span
     data-link="<?php if ($comparedProductsCount > 0): ?><?= $_helper->getListUrl() ?><?php else: ?>javascript:;<?php endif; ?>"
     class="clever-link icon-link<?php if ($comparedProductsCount > 0): ?> has-items<?php endif ?>"
     data-tooltip="<?= $this->__('Compare Products');?>"
     id="compare-wrapper"
>
    <svg aria-hidden="true" class="icon-svg compare">
        <use href="<?= $this->getBaseUrl().'svg/svg-sprite.svg' . '#compare' ?>"></use>
    </svg>
    <span id='compare-notification' class="notification"><?= $comparedProductsCount; ?></span>
</span>
<div class="header-links login drop-down drop-down-js">
    <span data-link="javascript:;" class="user-button open-item clever-link">
        <svg aria-hidden="true" class="icon-svg user">
            <use href="<?= $this->getBaseUrl().'svg/svg-sprite.svg' . '#user' ?>"></use>
        </svg>
    </span>
    <?php if ($this->helper('customer')->isLoggedIn()): ?>
        <ul class="dropdown-links sub-options">
            <li><span class="clever-link" data-link="<?= $this->getUrl('customer/account') ?>"><?= $this->__('Profile');?></span></li>
            <li><span class="clever-link" data-link="<?= $this->getUrl('customer/account/logout') ?>"><?= $this->__('Logout');?></span></li>
        </ul>
    <?php else: ?>
        <ul class="dropdown-links sub-options">
            <li><span class="clever-link" data-link="<?= $this->getUrl('customer/account/login') ?>"><?= $this->__('Login');?></span></li>
            <li><span class="clever-link" data-link="<?= $this->getUrl('customer/account/create') ?>"><?= $this->__('Registration');?></span></li>
        </ul>
    <?php endif ?>
</div>
