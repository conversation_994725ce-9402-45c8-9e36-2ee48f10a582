<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2014 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/**
 * @var Mage_Page_Block_Html_Header $this
 */
?>

<?php
    $_helper = $this->helper('catalog/product_compare');
    $_items = $_helper->getItemCount() > 0 ? $_helper->getItemCollection() : null;
?>

<!-- Load Facebook SDK for JavaScript -->
<div id="fb-root">
    <img id="fb-carco" src="<?php echo $this->getSkinUrl('images/fb_carco.png'); ?>" alt="Facebook Chat">
</div>
<script>
	docReady(function () {
    const e = document.getElementById('fb-carco');
    e.onclick = share_on_fb;

    function share_on_fb() {
			(function(d, s, id){
				var js, fjs = d.getElementsByTagName(s)[0];
				if (d.getElementById(id)) {return;}
				js = d.createElement(s); js.id = id;
				js.src = "https://connect.facebook.net/en_US/sdk/xfbml.customerchat.js";
				fjs.parentNode.insertBefore(js, fjs);
			}(document, 'script', 'facebook-jssdk'));

      window.fbAsyncInit = function() {
        FB.init({
          xfbml : true,
          version : 'v8.0'
        });

        FB.getLoginStatus(function(response){
          FB.CustomerChat.showDialog();
          document.getElementById('fb-carco').style.display = 'none';
        });
      };

      return false;
    }
  })
</script>

<!-- Your customer chat code -->
<div class="fb-customerchat"
    page_id="425737194508722"
    theme_color="#b81123"
    greeting_dialog_display="fade">
</div>

<header>
    <div class="wide-area top-line">
        <div class="container">
            <div class="row">
                <div class="logo-wrapper" itemscope itemtype="http://schema.org/Organization">
                    <a href="<?php echo $this->getUrl('') ?>" class="logo" itemprop="url" rel="home">
                        <img itemprop="logo" src="<?php echo $this->getLogoSrc() ?>" alt="<?php echo $this->getLogoAlt() ?>">
                    </a>
                </div>
                <div class="menu-wrapper" id="megamenu-initiator" data-ajaxurl="<?php echo $this->getUrl('megamenu/index/index') ?>">
                    <nav class="navbar">
                        <ul class="navbar-nav">
                            <li class="has-sub">
                                <span class="link">
	                                <?php echo $this->__('Parts'); ?>
                                </span>
                            </li>
                            <li class="has-sub">
                                <?php $groupUrlCollection = Mage::getResourceModel('attributeSplash/group_collection')
                                    ->addStoreFilter(Mage::app()->getStore()->getId())
                                    ->addFieldToFilter('group_id', 1)
                                    ->getFirstItem();
                                $groupUrlKey = $groupUrlCollection->getData('url_key');
                                ?>
                                <a href="<?php echo Mage::getBaseUrl() . $groupUrlKey ?>">
                                    <?php echo $this->__('Brand'); ?>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
                <?php if ($topMenu = $this->getChildHtml('cms_block.header_top_menu')): ?>
                    <nav class="topMenu">
                        <?php echo $topMenu ?>
                    </nav>
                <?php endif ?>
                <div class="right">
                    <?php echo $this->getChildHtml('header_cart'); ?>
                    <?php echo $this->getChildHtml('header_customer'); ?>
                    <?php echo $this->getChildHtml('topSearch') ?>
                </div>
            </div>
        </div>
    </div>
</header>


