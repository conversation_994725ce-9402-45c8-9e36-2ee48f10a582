<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2014 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/**
 * @var Mage_Page_Block_Html_Header $this
 */
?>

<?php
    $_helper = $this->helper('catalog/product_compare');
    $_items = $_helper->getItemCount() > 0 ? $_helper->getItemCollection() : null;
?>

<aside class="responsive-header">
    <div class="responsive-menu">
        <a href="javascript:;" class="open-responsive-menu" aria-label="<?php echo $this->__('Open menu');?>" data-ajaxurl="<?php echo $this->getUrl('megamenu/index/responsive') ?>"></a>
        <div class="responsive-menu-sub">
            <div class="tabs-links-content">
                <a href="javascript:;" class="responsive-tab-link tab1 opened"><?php echo $this->__('Menu');?></a>
                <a href="javascript:;" class="responsive-tab-link tab2"><?php echo $this->__('Profile');?></a>
            </div>
            <div class="responsive-menu-tab tab1 opened">
            </div>
            <div class="responsive-menu-tab tab2">
                <?php echo $this->getChildHtml('header_customer_responsive'); ?>
            </div>
        </div>
        <div class="responsive-menu-fade"></div>
    </div>
    <?php echo $this->getChildHtml('store_language') ?>
    <a class="header-phone" aria-label="<?php echo $this->__('Call us');?>" href="tel:<?php echo Mage::getStoreConfig('general/store_information/phone'); ?>">
        <svg aria-hidden="true" class="icon-svg phone">
            <use href="<?php echo $this->getBaseUrl().'svg/svg-sprite.svg' . '#phone' ?>"></use>
        </svg>
    </a>
    <a href="/" class="responsive-logo">
        <img itemprop="logo" src="<?php echo $this->getLogoSrc() ?>" alt="<?php echo $this->getLogoAlt() ?>">
    </a>

    <div class="responsive-search-wrapper">
        <a href="javascript:;" class="open-responsive-search" aria-label="<?php echo $this->__('Mobile search');?>">
            <svg aria-hidden="true" class="icon-svg search">
                <use href="<?php echo $this->getBaseUrl().'svg/svg-sprite.svg' . '#search' ?>"></use>
            </svg>
        </a>
        <div class="search-form-wrapper">
            <?php echo $this->getChildHtml('top.search.responsive') ?>
        </div>
    </div>

    <?php echo $this->getChildHtml('responsive_header_cart'); ?>
    <div class="header-text-block-wrapper mobile">
		<?= $this->getChildHtml('header_text_block'); ?>
    </div>
</aside>

<a class="back-to-top" href="javascript:;" style="display:none;">
    <svg aria-hidden="true" class="icon-svg arrow-up">
        <use href="<?php echo $this->getBaseUrl().'svg/svg-sprite.svg' . '#arrow-up' ?>"></use>
    </svg>
</a>


