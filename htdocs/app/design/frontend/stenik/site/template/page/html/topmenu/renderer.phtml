<?php
/**
 * Top menu for store
 *
 * @see Mage_Page_Block_Html_Topmenu::getHtml
 */

$currentCategory = Mage::registry('current_category') ? : Mage::getModel('catalog/category');
?>

<?php if ($menuTree = $this->getMenuTree()): ?>

    <?php foreach ($menuTree->getChildren() as $menuNode): ?>

        <?php $categoryId = str_replace('category-node-', '', $menuNode->getId()); ?>

        <?php if ($menuNode->hasChildren()): ?>
            <li class="has-sub">
                    <?php if($this->escapeHtml($menuNode->getId()) == 'splash-group1'): ?>
                        <?php $groupUrlCollection = Mage::getResourceModel('attributeSplash/group_collection')
                            ->addStoreFilter(Mage::app()->getStore()->getId())
                            ->addFieldToFilter('group_id', 1)
                            ->getFirstItem();
                        $groupUrlKey = $groupUrlCollection->getData('url_key');
                        ?>
                        <a href="<?php echo Mage::getBaseUrl() . $groupUrlKey ?>">
                            <?php echo $this->escapeHtml($menuNode->getName()) ?>
                        </a>
                    <?php else: ?>
                        <span  class="link">
                            <?php echo $this->escapeHtml($menuNode->getName()) ?>
                        </span>
                    <?php endif; ?>
                <div class="sub-nav">
                    <div class="container">

                        <?php $splitMenuItem = ceil($menuNode->getChildren()->count() / 4) ?>
                        <?php $i = 0; ?>

                        <div class="sub-nav-col">
                            <?php foreach ($menuNode->getChildren() as $menuSubNode): ?>
                                <?php $i++; ?>
                                <ul>
                                    <li>
                                        <a href="<?php echo $menuSubNode->getUrl() ?>" class="sub-cat-name">
                                            <?php echo $this->escapeHtml($menuSubNode->getName()) ?>
                                        </a>
                                        <?php if ($menuSubNode->hasChildren()): ?>
                                            <ul class="sub-sub-list">
                                                <?php foreach ($menuSubNode->getChildren() as $menuSubSubNode): ?>
                                                    <li>
                                                        <a href="<?php echo $menuSubSubNode->getUrl() ?>">
                                                            <?php echo $this->escapeHtml($menuSubSubNode->getName()) ?>
                                                        </a>
                                                    </li>
                                                <?php endforeach ?>
                                            </ul>
                                        <?php endif ?>
                                    </li>
                                </ul>
                                <?php if ($i != $menuNode->getChildren()->count() && $i%$splitMenuItem   == 0): ?>
                                    </div>
                                    <div class="sub-nav-col">
                                <?php endif ?>
                            <?php endforeach ?>
                        </div>

                        <div class="sub-nav-col banner">
                            <?php echo $this->getLayout()->createBlock('cms/block')->setBlockId('mainmenu_' . $categoryId)->toHtml(); ?>
                        </div>
                    </div>
                </div>
            </li>
        <?php else: ?>
            <li>
                <a href="<?php echo $menuNode->getUrl() ?>">
                    <?php echo $this->escapeHtml($menuNode->getName()) ?>
                </a>
            </li>
        <?php endif ?>
    <?php endforeach ?>

<?php endif ?>