<?php
/**
 * @var $this Bubble_Layer_Block_Catalog_Layer_Filter_Price
 * @var $this Bubble_Layer_Block_Catalog_Layer_Filter_Decimal
 */
$_currency = $this->getCurrentCurrency();
$_min = $this->getMin();
$_max = $this->getMax();
$_values = $this->getValues();
$_priceFormat = $this->getJsPriceFormat();
$_priceInputsEnabled = $this->helper('bubble_layer')->isPriceInputsEnabled();
?>

<div class="sub-options">
    <div class="layer-slider" id="layer-price-filter">
        <div class="price-slider">
            <div class="bg"></div>
            <div class="handle min"></div>
            <div class="span"></div>
            <div class="handle max"></div>
        </div>

        <div class="price-range a-center">
            <div class="left"><?php echo $_currency->formatPrecision($_values[0], 0) ?></div>
            <div class="right"><?php echo $_currency->formatPrecision($_values[1], 0) ?></div>
            <?php if ($_priceInputsEnabled): ?>
                <div class="price-slider-inputs">
                    <input type="text" class="input-text price-input-filter left" placeholder="<?php echo $this->__('Min price') ?>">
                    <input type="text" class="input-text price-input-filter right" placeholder="<?php echo $this->__('Max price') ?>">
                </div>
                <a href="javascript:;" class="button filter-button"></a>
            <?php endif; ?>
        </div>

        <div class="price-limit">
            <input class="request-var" type="hidden" value="<?php echo $this->getRequestVar() ?>">
            <input class="price-min" type="hidden" value="<?php echo $_min ?>">
            <input class="price-max" type="hidden" value="<?php echo $_max ?>">
            <input class="price-value-min" type="hidden" value="<?php echo $_values[0] ?>">
            <input class="price-value-max" type="hidden" value="<?php echo $_values[1] ?>">
            <div class="max"><?php echo $_currency->formatPrecision($_max, 0) ?></div>
            <div class="min"><?php echo $_currency->formatPrecision($_min, 0) ?></div>
        </div>
    </div>
</div>

<?php if ($_priceInputsEnabled): ?>
    <script>
      docReady(function () {
        const $layerSlider = jQuery('#layer-price-filter');
        const onFilter = function() {
          if (typeof BubbleLayer !== 'undefined') {

            let valFrom = parseFloat($layerSlider.find('.price-slider-inputs input.left').val());
            let valTo = parseFloat($layerSlider.find('.price-slider-inputs input.right').val());

            let valMin = parseFloat($layerSlider.find('.price-min').val());
            let valMax = parseFloat($layerSlider.find('.price-max').val());

            if (!valFrom) {
              valFrom = '';
            }

            if (!valTo) {
              valTo = '';
            }

            if (valTo && valFrom && valTo < valFrom) {
              const tmp = valTo;
              valTo = valFrom;
              valFrom = tmp;
            }

            if (valFrom && valFrom < valMin) {
              valFrom = valMin;
            }

            if (valFrom && valFrom > valMax) {
              valFrom = valMax - 1;
            }

            if (valTo && valTo > valMax) {
              valTo = valMax;
            }

            if (valTo && valTo < valMin) {
              valTo = valMin + 1;
            }

            let url = BubbleLayer.buildPriceUrl($layerSlider.find('.request-var').val(), valFrom, valTo);
            url = jQuery('<div>').html(url).text();
            if (BubbleLayer.options.enableAjax) {
              BubbleLayer.handleLayer(url);
            } else {
              window.location = url;
            }
            return false;
          }
        };

        $layerSlider.find('.price-input-filter').each(function() {
          this.stopObserving('keyup');
          jQuery(this).keyup(function(e) {
            if (e.keyCode == Event.KEY_RETURN) {
              onFilter();
            }
          });
        });

        $layerSlider.find('.filter-button').click(onFilter);
      });
    </script>
<?php endif ?>
