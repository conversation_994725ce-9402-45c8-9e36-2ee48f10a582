<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2014 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

/**
 * Product view template
 *
 * @see Mage_Catalog_Block_Product_View
 * @see Mage_Review_Block_Product_View
 */
?>
<?php $_helper = $this->helper('catalog/output'); ?>
<?php $_baseHelper = $this->helper('stenik_base/catalog_product'); ?>
<?php $_product = $this->getProduct(); ?>
<?php $_wishlistSubmitUrl = $this->helper('wishlist')->getAddUrl($_product); ?>
<?php $_compareUrl = $this->helper('catalog/product_compare')->getAddUrl($_product); ?>
<?php $currentUrl = Mage::helper('core/url')->getCurrentUrl(); ?>
<?php
    $splashPage = null;
    if (Mage::helper('core')->isModuleEnabled('Stenik_FishpigAttributeSplash')) {
        $splashPage = Mage::helper('stenik_fishpigattributesplash')->getSplashPageByOptionId($_product->getData('manufacturer'));
    }
?>
<?php
    $isLeasingAvailable =
    (Mage::helper('core')->isModuleEnabled('Stenik_LeasingUniCredit') && Mage::helper('stenik_leasingunicredit/product')->isLeasingAvailable($_product, true)) ||
    (Mage::helper('core')->isModuleEnabled('Stenik_LeasingTbi') && Mage::helper('stenik_leasingtbi/product')->isLeasingAvailable($_product, true)) ||
    (Mage::helper('core')->isModuleEnabled('Stenik_LeasingJetCredit') && Mage::helper('stenik_leasingjetcredit/product')->isLeasingAvailable($_product, 1, true));
?>

<div class="product-view-wrapper">

    <div class="wide-area product-view-main">
        <div class="container">
            <div class="row">
                <div class="col-xs-5">
                    <div class="gallery-box-wrapper">
                        <div class="gallery-box">
							<?= $this->getChildHtml('media'); ?>
							<?= Mage::app()->getLayout()->getBlock('product.labels')->setProduct($_product)->toHtml(); ?>
                        </div>
                    </div>
                </div>
                <div class="col-xs-7">
                    <form action="<?= $this->getSubmitUrl($_product) ?>" method="post" id="product_addtocart_form"<?php if($_product->getOptions()): ?> enctype="multipart/form-data"<?php endif; ?> >
                    <form action="<?= $this->getSubmitUrlCustom($_product, array('_secure' => $this->_isSecure()), false) ?>" method="post" id="product_addtocart_form" <?php if ($_product->getOptions()): ?> enctype="multipart/form-data" <?php endif; ?>>
                        <?= $this->getBlockHtml('formkey') ?>
                        <h1><?= $_helper->productAttribute($_product, $_product->getName(), 'name') ?></h1>
                        <div class="status-row">
                            <?php $skuLabel = $_product->getResource()->getAttribute('sku')->getStoreLabel(Mage::app()->getStore()->getName()); ?>
                            <p class="sku"><?= $skuLabel ?>: <span><?= $this->escapeHtml($_product->getSku()) ?></span></p>


                            <div class="availability-wrapper">
                                <p class="availability in-stock hidden-saleable">
                                    <svg aria-hidden="true" class="icon-svg in-stock-icon"><use href="<?= $this->getBaseUrl().'svg/svg-sprite.svg' . '#in-stock-icon' ?>"></use></svg>
									<?= $this->__('In stock');?>
                                </p>
                                <p class="availability out-of-stock hidden-not-saleable">
                                    <svg aria-hidden="true" class="icon-svg close"><use href="<?= $this->getBaseUrl().'svg/svg-sprite.svg' . '#close' ?>"></use></svg>
									<?= $this->__('Out of stock');?>
                                </p>
                            </div>

                            <?= $this->getChildHtml('compare_button') ?>

                            <a href="#errorsignal-form" class="error-signal"><?= $this->__('Error signal') ?></a>
                            <a href="tel:<?= Mage::getStoreConfig('general/store_information/phone'); ?>" class="product-contacts-button"></a>
                        </div>

                        <p class="cr-product-timer">
                            <span class="cntdown-product" data-noempty="1"></span>
                            <span class="cntdown-source" id='cntdown_source' style="display: none;"></span>
                            <span class="cntdown-ctime" id='cntdown_ctime' style="display: none;"></span>
                        </p>

                        <div class="col-xs-12 product-options">
                            <div class="no-display">
                                <input type="hidden" name="product" value="<?= $_product->getId() ?>" />
                                <input type="hidden" name="related_product" id="related-products-field" value="" />
                            </div>

                            <?= $this->getChildHtml('alert_urls') ?>
                            <?= $this->getChildHtml('extrahint') ?>

                            <?= $this->getChildHtml('lazyblock.product.add') ?>
                            <?= $this->getChildHtml('carco.replacements.native'); ?>

                            <?= $this->getChildHtml('extra_buttons') ?>
                            <?= $this->getChildHtml('other');?>
                        </div>

                        <div class="description hidden-saleable">
                            <?= $_helper->productAttribute($_product, nl2br($_product->getDescription()), 'description') ?>
                        </div>
                        <div class="clearH2"></div>
                        <div class="hidden-saleable attributes-container">
                            <?= $this->getChildHtml('attributes') ?>
                        </div>
                        <div class="col-xs-12">
                            <?php if ($_product->isSaleable() && $this->hasOptions()):?>
                                <?= $this->getChildChildHtml('container2', '', true, true) ?>
                            <?php endif;?>
                        </div>
                    </form>
                </div>
                <?= $this->getChildHtml('pfg_fast_order_form') ?>
            </div>
        </div>
    </div>

    <?php
        $cmsBlockProductViewDelivery = $this->getChildHtml('cms_block.product_view_delivery');
        $cmsBlockProductViewHelp = $this->getChildHtml('cms_block.product_view_help');
    ?>
    <?php if ($cmsBlockProductViewDelivery && $cmsBlockProductViewHelp): ?>
        <div class="wide-area product-tabs">
            <div class="container">
                <div class="row">
                    <div class="col-xs-12">
                        <div class="tabs regular-tabs">
                            <div class="tabs-nav">
                                <ul>
                                    <?php if ($cmsBlockProductViewDelivery): ?>
                                        <li class="tab-nav-item-wrapper">
                                            <a class="tab-nav-item" href="javascript:;" data-tab-target="#delivery"><?= $this->__('Delivery and payment');?></a>
                                        </li>
                                    <?php endif ?>
                                    <?php if ($cmsBlockProductViewHelp): ?>
                                        <li class="tab-nav-item-wrapper">
                                            <a class="tab-nav-item" href="javascript:;" data-tab-target="#stores-availability"><?= $this->__('To contact us');?></a>
                                        </li>
                                    <?php endif ?>
                                </ul>
                            </div>
                            <div class="tabs-content">
                                <?php if ($cmsBlockProductViewDelivery): ?>
                                    <a class="tab-nav-item responsive" href="javascript:;" data-tab-target="#delivery"><?= $this->__('Delivery and payment');?></a>
                                    <div class="tab" id="delivery">
                                        <div class="row">
                                            <div class="col-xs-12">
                                                <div class="text-page">
                                                    <?= $cmsBlockProductViewDelivery; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif ?>
                                <?php if ($cmsBlockProductViewHelp): ?>
                                <a class="tab-nav-item responsive" href="javascript:;" data-tab-target="#stores-availability"><?= $this->__('To contact us');?></a>
                                    <div class="tab" id="stores-availability">
                                        <div class="row">
                                            <div class="col-xs-12">
                                                <div class="text-page">
                                                    <?= $cmsBlockProductViewHelp; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif ?>

    <?= $this->getChildHtml('carco.related.native') ?>
    <?= $this->getLayout()->createBlock('reports/product_viewed')
        ->setTemplate('reports/product_viewed.phtml')
        ->toHtml();
    ?>
</div>

<div style="display: none;">
    <div id="errorsignal-form" class="errorsignal-popup-form">
        <span class="form-title"><?= $this->__('Error signal'); ?></span>
        <div id="error-form-content"></div>
    </div>
</div>

<script>
    function resizeColorboxOnVarienFormValidation(varienForm) {
        if (varienForm.validator) {
            var oldValidate = varienForm.validator.validate;
            varienForm.validator.validate = function(){
                var result = oldValidate.call(this);
                var resizeColorboxTimeoutId = false;
                var requestColorboxResize = function() {
                    if (resizeColorboxTimeoutId) {
                        clearTimeout(resizeColorboxTimeoutId);
                        resizeColorboxTimeoutId = false;
                    }
                    resizeColorboxTimeoutId = setTimeout(jQuery.colorbox.resize.bind(jQuery.colorbox), 20);
                }
                if (this.form) {
                    this.form.select('.validation-advice').each(function(adviceElm){
                        if (!adviceElm.colorboxResizeInited) {
                            oldShow = adviceElm.show;
                            oldHide = adviceElm.hide;
                            oldForceRendering = adviceElm.forceRerendering;
                            adviceElm.show = function() {
                                oldShow.call(adviceElm);
                                requestColorboxResize();
                            }
                            adviceElm.hide = function() {
                                oldHide.call(adviceElm);
                                requestColorboxResize();
                            }
                            adviceElm.forceRerendering = function() {
                                requestColorboxResize();
                                oldForceRendering.call(adviceElm);
                            }
                            adviceElm.colorboxResizeInited = true;
                        }
                    });
                }

                return result;
            }
        }
    }

    var productAddToCartForm   = new VarienForm('product_addtocart_form');
    var productAddToQuickOrder = new VarienForm('quickorder_dummy_form');

    resizeColorboxOnVarienFormValidation(productAddToQuickOrder);

    jQuery(function($){

        $('.regular-tabs').each(function() {
            var tabs = new Stenik.Tabs({
                $tabsWrapper: $(this),
                $tabContents: $(this).find('.tab'),
                $tabNavItems: $(this).find('.tab-nav-item'),
                navItemInitial: 0,
                onTabActivateAfter: function() {
                    $('.leasing-tabs').each(function() {
                        var leasingTabs = new Stenik.Tabs({
                            $tabsWrapper: $(this),
                            $tabContents: $(this).find('.leasing-tab'),
                            $tabNavItems: $(this).find('.leasing-tabs-nav'),
                            navItemInitial: 0,
                            onTabActivateAfter: function() {
                                $('.jetCredit-tabs').each(function() {
                                    var leasingTabs2 = new Stenik.Tabs({
                                        $tabsWrapper: $(this),
                                        $tabContents: $(this).find('.tab'),
                                        $tabNavItems: $(this).find('.tab-nav-item'),
                                        navItemInitial: 0
                                    });
                                });
                            }
                        });
                    });
                }
            });
        });

        $(".tab-nav-item.responsive").click(function() {
            $('html, body').animate({
                scrollTop: $(this).offset().top - 60
            }, 450);
        });

        $(".to-full-description").click(function() {
            $('html, body').animate({
                scrollTop: $(".regular-tabs").offset().top - 40
            }, 450);
            setTimeout(function(){
                $(".tabs-nav .tab-nav-item[data-tab-target='#full-description']").click();
            }, 480);
        });

        $(".view-stores-availability").click(function() {
            $('html, body').animate({
                scrollTop: $(".regular-tabs").offset().top - 40
            }, 450);
            setTimeout(function(){
                $(".tabs-nav .tab-nav-item[data-tab-target='#stores-availability']").click();
            }, 480);
        });

        $(".link.view-rating").click(function() {
            $('html, body').animate({
                scrollTop: $(".regular-tabs").offset().top - 40
            }, 450);
            setTimeout(function(){
                $(".tabs-nav .tab-nav-item[data-tab-target='#rating-and-reviews']").click();
            }, 480);
        });

        $(".button.leasing").click(function() {
            $('html, body').animate({
                scrollTop: $(".regular-tabs").offset().top - 40
            }, 450);
            setTimeout(function(){
                $(".tabs-nav .tab-nav-item[data-tab-target='#leasing-info']").click();
            }, 480);
        });


        <?php if ($_product->isSaleable() && $this->hasOptions()):?>
            $('.leasing-add-to-cart').click(function() {
                $('html,body').animate({
                    scrollTop: $('.product-view-main').offset().top -80
                }, 'slow');
                $('.product-view-main .add-to-cart').click();
            });
        <?php else: ?>
            $('.leasing-add-to-cart').click(function() {
                $('.product-view-main .add-to-cart').click();
            });
        <?php endif;?>

        $('.error-signal').colorbox({
          inline:true,
          width:'700px',
          maxWidth: '90%',
          height: '600px',
          maxHeight: '90%',
          onOpen: function () {
            $.get("<?= Mage::getBaseUrl() . 'lazyblock/product/error' ?>", function(data) {
              $("#error-form-content").html(data);
            });
        }});
    });

    productAddToCartForm.submit = function(button, url) {
        if (this.validator.validate()) {
            var form = this.form;
            var oldUrl = form.action;

            if (url) {
               form.action = url;
            }
            var e = null;
            try {
                this.form.submit();
            } catch (e) {
            }
            this.form.action = oldUrl;
            if (e) {
                throw e;
            }

            if (button && button != 'undefined') {
                button.disabled = true;
            }
        }
    }.bind(productAddToCartForm);

    productAddToCartForm.submitLight = function(button, url){
        if(this.validator) {
            var nv = Validation.methods;
            delete Validation.methods['required-entry'];
            delete Validation.methods['validate-one-required'];
            delete Validation.methods['validate-one-required-by-name'];
            // Remove custom datetime validators
            for (var methodName in Validation.methods) {
                if (methodName.match(/^validate-datetime-.*/i)) {
                    delete Validation.methods[methodName];
                }
            }

            if (this.validator.validate()) {
                if (url) {
                    this.form.action = url;
                }
                this.form.submit();
            }
            Object.extend(Validation.methods, nv);
        }
    }.bind(productAddToCartForm);

    function myPopup(url) {
        window.open( url, "myWindow", "status = 1, height = 500, width = 580, resizable = 0" );
    }
</script>
