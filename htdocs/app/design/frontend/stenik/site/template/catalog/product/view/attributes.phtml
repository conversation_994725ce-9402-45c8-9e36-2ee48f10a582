<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2010 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

/**
 * Product additional attributes template
 *
 * @see Carco_Catalog_Block_Product_View_Attribute
 */
?>
<?php
/** @var Mage_Catalog_Helper_Output $_helper */
$_helper = $this->helper('catalog/output');
$_product = $this->getProduct();
?>
<?php if ($_additional = $this->getAdditionalData()): ?>
    <table class="stylized attributes">
        <?php foreach ($_additional as $_data): ?>
            <?php $valuesHtml = []; ?>
            <?php if ($_data['value'] != '' && $_data['value'] != 'No' && $_data['value'] != 'N/A' && $_data['value'] != 'Не'): ?>
                <?php
                if ($_data['code'] == 'part_vehicle_maker') {
                    $attributeValue = $_product->getData('part_vehicle_maker');
                    if (!$attributeValue) {
                        continue;
                    }

                    $valuesHtml[] = $this->getPartsHtmlValue($_product);
                } elseif ($_data['code'] == 'zeron_brand_code') {
                    $valuesHtml[] = $this->getTireBrandHtmlValue($_data['value'], $_product);
                }

                $value = $_helper->productAttribute($_product, $_data['value'], $_data['code']);
                if (empty($valuesHtml)) {
                    $valuesHtml[] = $value;
                }
                ?>
                <tr data-code='<?= $_data['code'] ?? ''; ?>'>
                    <td><?= $this->escapeHtml($this->__($_data['label'])) ?></td>
                    <?php if ($_data['code'] == 'weight'): ?>
                        <td><?= round($value, 0) ?><?= $this->__('kg') ?></td>
                    <?php elseif ($_data['code'] == 'stenik_zeron_power'): ?>
                        <td><?= $value ?><?= $this->__('hp') ?></td>
                    <?php else: ?>
                        <td>
                            <?= implode(', ', $valuesHtml); ?>
                        </td>
                    <?php endif ?>
                </tr>
            <?php endif; ?>
        <?php endforeach; ?>
    </table>
<?php endif; ?>
