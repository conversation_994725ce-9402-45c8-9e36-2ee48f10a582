<script>
  if (window.location.search && window.location.search !== '?') {
    docReady(function () {
      const searchParamsElements = window.location.search.substring(1).split('&');

      searchParamsElements.each(function(value, index) {
        if (value.match('^p=')) {
          searchParamsElements.splice(index, 1)
        }
      })

      const searchParams = '?' + searchParamsElements.join('&');

      jQuery('.navbar .sub-nav a, .drop-down.categories .sub-options a').each(function() {
        const $this = jQuery(this),
          elHref = $this.attr('href');

        if (!elHref.indexOf('?') > -1) {
          if (searchParams === '?') {
            return;
          }

          let url = new URL(elHref + searchParams);
          let settingParams = "<?php echo Mage::getStoreConfig('bubble_layer/general/url_param'); ?>";
          let paramsToRemove = settingParams.split(',');
          jQuery.each(paramsToRemove, function(key, value) {
            url.searchParams.delete(value);
          });
          $this.attr('href', url);
        }
      });
    })
  }
</script>
