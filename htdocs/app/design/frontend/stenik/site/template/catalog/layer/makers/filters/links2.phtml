<?php
if (!Mage::helper('stenik_site')->canShowMakerActiveFiltersLinks()) {
    return;
}

$attributeMakerLabel = '';

$leftnavBlock = $this->getLayout()->getBlock('catalog.leftnav');
if ($leftnavBlock) {
    /** @var Mage_Catalog_Block_Layer_State $stateBlock */
    $stateBlock = $leftnavBlock->getChild('layer_state');

    foreach ($stateBlock->getActiveFilters() as $activeFilter) {
        if ($attributeModel = $activeFilter->getFilter()->getAttributeModel()) {
            if ($attributeModel->getAttributeCode() == 'part_vehicle_maker') {
                $attributeMakerLabel = $activeFilter->getLabel();
            }
        }
    }
}
?>
<?php if ($this->canShowBlock()): ?>
    <?php if ($this->canShowOptions()): ?>
        <?php
            $_filters = $this->getFilters();
            $currentCategory = Mage::registry('current_category');
        ?>
        <span class="category-parts-links-title"><?php echo Mage::helper('stenik_site')->__('See %s for the following models as well', $currentCategory->getName()); ?>:</span>
        <div class="parts-links">
            <?php foreach ($_filters as $_filter): ?>
                <?php
                    if (!$this->getShowCategoryFilter() &&
                        in_array($_filter->getType(), array('catalog/layer_filter_category', 'Mage_Catalog_Block_Layer_Filter_Category'))
                    ) {
                        continue;
                    }
                ?>
                <?php if ($_filter->getItemsCount()): ?>
                    <?php
                        $attributeCode = null;
                        if ($_filter->getAttributeModel()) {
                            $attributeCode = $_filter->getAttributeModel()->getAttributeCode();
                        }
                        if ($attributeCode != 'part_vehicle_model_short') {
                            continue;
                        }
                    ?>
                    <?php foreach ($_filter->getItems() as $item): ?>
                        <?php
                            $label = sprintf(
                                        "%s %s %s %s",
                                        $currentCategory->getName(),
                                        $this->__('for'),
                                        $attributeMakerLabel,
                                        $item->getLabel()
                                    );
                        ?>
                        <a href="<?php echo $item->getUrl(); ?>" class="parts-links-item"><?php echo $label; ?></a>
                    <?php endforeach; ?>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
<?php endif; ?>