<?php
/**
 * Product list toolbar
 *
 * @see Mage_Catalog_Block_Product_List_Toolbar
 */
?>
<?php if($this->getCollection()->getSize()): ?>
    <div class="col-xs-12">
        <div class="toolbar">
            <span class="sort-block first">
                <span class="sort-title"><?php echo $this->__('Sort by'); ?>:</span>
                <div class="drop-down drop-down-js openonclick">
                    <?php $orderOptions = array('best_sellers', 'most_reviewed', 'name'); ?>
                    <?php foreach($this->getAvailableOrders() as $_key=>$_order): ?>
                        <?php if($this->isOrderCurrent($_key)): ?>
                            <span href="javascript:;" class="open-item">
                                <span><?php echo $this->__($_order) ?></span>
                            </span>
                        <?php endif; ?>
                    <?php endforeach; ?>
                    <ul class="sub-options">
                        <?php foreach($this->getAvailableOrders() as $_key=>$_order): ?>
                            <?php
                            if (in_array($_key, $orderOptions)) {
                                continue;
                            }
                            ?>
                            <?php if($this->isOrderCurrent($_key)) continue; ?>
                            <li><span class="clever-link" data-link="<?php echo $this->getOrderUrl($_key, 'asc') ?>"><?php echo $this->__($_order) ?></span></li>
                        <?php endforeach; ?>
                    </ul>
                </div>

                <?php if($this->getCurrentDirection() == 'desc'): ?>
                    <span data-link="<?php echo $this->getOrderUrl(null, 'asc') ?>" class="sorting-arrow clever-link">
                        <svg aria-hidden="true" class="icon-svg arrow-up">
                            <use href="<?php echo $this->getBaseUrl().'svg/svg-sprite.svg' . '#arrow-up' ?>"></use>
                        </svg>
                    </span>
                <?php else: ?>
                    <span data-link="<?php echo $this->getOrderUrl(null, 'desc') ?>" class="sorting-arrow clever-link">
                        <svg aria-hidden="true" class="icon-svg arrow-down">
                            <use href="<?php echo $this->getBaseUrl().'svg/svg-sprite.svg' . '#arrow-down' ?>"></use>
                        </svg>
                    </span>
                <?php endif; ?>
            </span>

            <span class="sort-block second">
                <span class="sort-title"><?php echo $this->__('Show by'); ?>:</span>
                <div class="drop-down drop-down-js openonclick">
                    <?php foreach ($this->getAvailableLimit() as $_key=>$_limit): ?>
                        <?php if($this->isLimitCurrent($_key)): ?>
                            <span href="javascript:;" class="open-item"><?php echo $_limit ?></span>
                        <?php endif; ?>
                    <?php endforeach; ?>
                    <ul class="sub-options">
                        <?php foreach ($this->getAvailableLimit() as $_key=>$_limit): ?>
                            <?php if($this->isLimitCurrent($_key)) continue; ?>
                            <li><span class="clever-link" data-link="<?php echo $this->getLimitUrl($_key) ?>"><?php echo $_limit ?></span></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </span>

            <?php if( $this->isEnabledViewSwitcher() ): ?>
                <?php $_modes = $this->getModes(); ?>
                <?php if($_modes && count($_modes)>1): ?>
                    <div class="view-mode">
                        <?php foreach ($this->getModes() as $_code=>$_label): ?>
                            <?php if($this->isModeActive($_code)): ?>
                                <a title="<?php echo $_label ?>" class="<?php echo strtolower($_code); ?> active">
                                    <svg aria-hidden="true" class="icon-svg <?php echo strtolower($_code); ?>-view">
                                        <use href="<?php echo $this->getBaseUrl().'svg/svg-sprite.svg' . '#' ?><?php echo strtolower($_code); ?><?php echo "-view"; ?>"></use>
                                    </svg>
                                </a>
                            <?php else: ?>
                                <a href="<?php echo $this->getModeUrl($_code) ?>" class="<?php echo strtolower($_code); ?>">
                                    <svg aria-hidden="true" class="icon-svg <?php echo strtolower($_code); ?>-view">
                                        <use href="<?php echo $this->getBaseUrl().'svg/svg-sprite.svg' . '#' ?><?php echo strtolower($_code); ?><?php echo "-view"; ?>"></use>
                                    </svg>
                                </a>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            <?php endif; ?>

            <?php if (0): ?>
                <div class="show-only-available">
                    <input type="checkbox" class="checkbox" id="show-only-available">
                    <label for="show-only-available"><?php echo $this->__('Show only available') ?></label>
                </div>
            <?php endif ?>

            <?php echo $this->getPagerHtml() ?>
        </div>
    </div>
<?php endif ?>
