<?php
if (!Mage::helper('stenik_site')->canShowMakersFiltersLinks()) {
    return;
}
?>
<?php if($this->canShowBlock()): ?>
    <?php if($this->canShowOptions()): ?>
        <?php
            $_filters = $this->getFilters();
            $currentCategory = Mage::registry('current_category');
            $partVehicleMakerRand = Mage::helper('stenik_site')->getPartVehicleMakerRand();
        ?>
        <div class="parts-links">
            <?php foreach ($_filters as $_filter): ?>
                <?php
                    if (!$this->getShowCategoryFilter() &&
                        in_array($_filter->getType(), array('catalog/layer_filter_category', 'Mage_Catalog_Block_Layer_Filter_Category'))
                    ) {
                        continue;
                    }
                ?>
                <?php if($_filter->getItemsCount()): ?>
                    <?php
                        $attributeCode = null;
                        if ($_filter->getAttributeModel()) {
                            $attributeCode = $_filter->getAttributeModel()->getAttributeCode();
                        }
                        if ($attributeCode != 'part_vehicle_maker') {
                            continue;
                        }

                        $countPartVehicleMaker = Mage::helper('stenik_site')->countPartVehicleMaker($_filter, $partVehicleMakerRand);

                        $i = 0;
                        $j = 0;
                    ?>
                    <?php foreach ($_filter->getItems() as $item): ?>
                        <?php
                            if (!in_array($item->getLabel(), $partVehicleMakerRand)) {
                                continue;
                            }
                        ?>
                                <a href="<?php echo $item->getUrl(); ?>" class="parts-links-item"><?php echo $item->getLabel() . ' ' . mb_strtolower($currentCategory->getName(), 'UTF-8'); ?></a>
                        <?php
                            if ($i == 12) {
                                break;
                            }
                        ?>
                    <?php endforeach; ?>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
<?php endif; ?>