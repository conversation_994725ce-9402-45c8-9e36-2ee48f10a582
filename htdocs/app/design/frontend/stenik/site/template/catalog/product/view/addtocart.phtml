<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2015 X.commerce, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php $_product = $this->getProduct(); ?>
<?php $brandsUniqueId = 'productSlider_cplsd' . md5(uniqid('', true)); ?>
<?php
$addToCartSelectors            = array_unique(array_merge(Mage::helper('stenik_ajaxaddtocart')->getAddToCartTriggerSelectors(), Mage::helper('stenik_ajaxaddtocart')->getAddToCartFormTriggerSelectors()));
$addToCartFormButtonsSelectors = Mage::helper('stenik_ajaxaddtocart')->getAddToCartFormTriggerSelectors();
$data = array(
	'product' => $_product->getId(),
);
?>
<div class="lazyload addtocart-lazy" id="<?= $brandsUniqueId; ?>" data-include="<?php echo Mage::getBaseUrl().'lazyblock/index/addtocart?'.http_build_query($data) ?>" data-expand="20"></div>
<script type="text/javascript">
	jQuery(document).on('lazyloaded', function (e) {
		if (!e.hasOwnProperty('target')) {
			return;
		}
		let currentTarget = jQuery(e.target).first();
		if (!currentTarget.hasClass('addtocart-lazy')) {
			return;
		}

		let csfrContent = jQuery(currentTarget).find('#ajax_form_key').first();
		if (typeof csfrContent === 'undefined') {
			return;
		}
		csfrContent = csfrContent.val();
		if (!csfrContent) {
			return;
		}

		let addToCartForm = jQuery('#product_addtocart_form');
		if (typeof addToCartForm === 'undefined') {
			return;
		}
		let newActionUrl = addToCartForm.attr('action').replace(/form_key\/[a-zA-Z0-9]+\//, 'form_key/'+ csfrContent + '/');
		// change the action of the form
		addToCartForm.attr('action', newActionUrl);

		let formKeyElements = addToCartForm.find('input[name="form_key"]');
		if (typeof formKeyElements === 'undefined') {
			return;
		}

		formKeyElements.each(function(elementIndex, elementItself){
			jQuery(elementItself).val(csfrContent);
		});
	});

	jQuery(document).on('lazyloaded', function (e) {
		if (jQuery(e.target).first().hasClass('addtocart-lazy')) {
			(function ($) {
				var areFieldsValid = function ($fields) {
					var $fields = $($fields);

					var areAllFieldsValid = true;

					$fields.each(function () {
						var fieldClasses = this.className.split(' ');
						for (var i = 0; i < fieldClasses.length; i++) {
							try {
								var validator = Validation.get(fieldClasses[i]);

								if (Validation.isVisible(this) && !validator.test($F(this), this)) {
									areAllFieldsValid = false;
									return false;
								}
							} catch (e) {
								areAllFieldsValid = false;
								return false;
							}
						}
					});

					return areAllFieldsValid;
				};

				var addToCartTrigger = function (event) {
					var $element = $(this);
					var $form = $element.closest('form');

					var data = {};
					if ($form.length) {
						if (!areFieldsValid($form.find('input,select,textarea'))) {
							return true;
						}

						var dataArray = $form.serializeArray();

						for (var i = 0; i < dataArray.length; i++) {
							if (typeof dataArray[i].name === 'undefined') {
								continue;
							}

							if (typeof dataArray[i].value === 'undefined') {
								dataArray[i].value = '';
							}

							if (dataArray[i].name.match(/\[\]$/)) {
								// Array name

								if (typeof data[dataArray[i].name] == 'undefined') {
									data[dataArray[i].name] = [];
								}

								data[dataArray[i].name].push(dataArray[i].value);
							} else {
								data[dataArray[i].name] = dataArray[i].value;
							}
						}
					}

					if (typeof data.product === 'undefined') {
						data.product = false;

						var destination = $element.data('origOnclickAttr');
						if (!destination) {
							destination = $element.attr('href');
						}

						if (destination) {
							var matches;
							if (matches = destination.match(/product\/(\d+)/)) {
								data.product = matches[1];
							}

							if (matches = destination.match(/form_key\/([^\/]+)/)) {
								data.form_key = matches[1];
							}
						}
					}

					if (!data.product) {
						return true;
					}

					if (typeof data.form_key === 'undefined') {
						data.form_key = <?php echo json_encode($this->getFormKey()) ?>
					}


					$('#stenik-ajaxaddtocart-overlay').show();

					var hideOverlay = true;
					$.ajax({
						url: <?php echo json_encode($this->getUrl('stenik_ajaxaddtocart/index/add')) ?>,
						method: 'post',
						dataType: 'json',
						data: data
					}).done(function (response) {
						if ((typeof response.update !== 'undefined') && response.update) {
							for (var i = 0; i < response.update.length; i++) {
								$(response.update[i].selector).replaceWith(response.update[i].html);
							}
						}

						if ((typeof response.html !== 'undefined') && response.html) {
							hideOverlay = false;
							var $resultWrapper = $('#stenik-ajaxaddtocart-result');
							$resultWrapper.html(response.html);
							$resultWrapper.find('.closeAddToCartResult').click(function () {
								$resultWrapper.hide();
								$('#stenik-ajaxaddtocart-overlay').hide();
							});
							$(document).keyup(function (e) {
								if (e.keyCode == 27) {
									$resultWrapper.hide();
									$('#stenik-ajaxaddtocart-overlay').hide();
								}
							});

							$resultWrapper.show();
						}
					}).fail(function () {
						alert(<?php echo json_encode($this->__('Something went wrong. Please try again.')) ?>);
					}).always(function () {
						if (hideOverlay) {
							$('#stenik-ajaxaddtocart-overlay').hide();
						}
					});

					if (typeof event != 'undefined') {
						Event.stop(event);
						event.preventDefault();
						event.stopPropagation();
						event.stopImmediatePropagation();
					}

					return false;
				};

				$(<?php echo json_encode(implode(',', $addToCartSelectors)) ?>).each(function () {
					var $element = $(this);
					$element.click(addToCartTrigger.bind(this));

					var onclickAttrFunc = $element.prop('onclick');
					if (onclickAttrFunc) {
						$element.data('origOnclickAttr', $element.attr('onclick'));
						$element.removeProp('onclick');
						$element.removeAttr('onclick');
						$element.click(onclickAttrFunc);
					}
				});

				<?php if (count($addToCartFormButtonsSelectors)): ?>
				$(<?php echo json_encode(implode(',', $addToCartFormButtonsSelectors)) ?>).each(function () {
					$(this).closest('form').submit(addToCartTrigger.bind(this));
				});
				<?php endif; ?>
				$('body').on('click', <?php echo json_encode(implode(',', $addToCartSelectors)) ?>, addToCartTrigger);
			})(jQuery);
		}

	});

</script>
