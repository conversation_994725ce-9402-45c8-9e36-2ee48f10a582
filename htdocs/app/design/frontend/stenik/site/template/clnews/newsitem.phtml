<?php $item = $this->getNewsitem();  ?>
<h1><?php echo $item->getTitle() ?></h1>
<div class="newsContainer">
    <div class="newsHeader">
		<?php if (Mage::helper('clnews')->showDate()): ?>
            <span class="newsDate"><?php echo Mage::helper('clnews')->formatDate($item->getNewsTime()) ?></span>
		<?php endif; ?>
    </div>

    <div class="textPage">
		<?php if ($item->getImageFullContentShow() && $item->getImageShortContent()): ?>
            <div style="text-align: center">
                <img style="margin:20px 0;" src="<?php echo Mage::helper('clnews')->resizeImage(str_replace('clnews/', '', $item->getImageShortContent()), 450, null, 'clnews'); ?>" />
            </div>
		<?php endif; ?>
		<?php echo Mage::helper('clnews')->contentFilter($item->getFullContent()) ?>
    </div>
	
	<?php echo $this->getMessagesBlock()->getGroupedHtml(); ?>

    <div class="newsCommentBox">
		<?php if($item->getCommentsEnabled()): ?>
			<?php $comments = $this->getComments(); ?>
			<?php if (!$this->getRequireLogin() || ($this->getRequireLogin()&&$this->helper('customer')->isLoggedIn())): ?>
            <div class="commentsForm">
                <form id="postComment" method="post" action="">
                    <h2><?php echo Mage::helper('clnews')->__('Leave a Comment') ?></h2>
					<?php if (!$this->helper('customer')->isLoggedIn()): ?>
                        <div class="input-box">
                            <label for="name">* <?php echo Mage::helper('clnews')->__('Name') ?>:</label>
                            <input name="user" id="user" value="<?php echo $this->getCommentName(); ?>" title="<?php echo $this->__('Name') ?>" value="<?php echo $this->htmlEscape($this->helper('clnews')->getUserName()) ?>" class="required-entry input-text" type="text" />
                        </div>
                        <div class="input-box">
                            <label for="email">* <?php echo Mage::helper('clnews')->__('E-mail') ?>:</label>
                            <input name="email" id="email" value="<?php echo $this->getCommentEmail(); ?>" title="<?php echo $this->__('Email') ?>" value="<?php echo $this->htmlEscape($this->helper('clnews')->getUserEmail()) ?>" class="required-entry input-text validate-email" type="text" />
                        </div>
					<?php else: ?>
                        <input name="post_id" type="hidden" value="<?php echo $item->getPostId();?>" />
                        <input name="email" type="hidden" value="<?php echo $this->htmlEscape($this->helper('clnews')->getUserEmail()) ?>"/><br/>
                        <input name="user" type="hidden" value="<?php echo $this->htmlEscape($this->helper('clnews')->getUserName()) ?>"/><br/>
					<?php endif ?>
                    <div class="input-box textarea">
                        <label for="comment">* <?php echo Mage::helper('clnews')->__('Comment') ?>:</span></label>
                        <textarea name="comment" id="comment" title="<?php echo Mage::helper('clnews')->__('Comment') ?>" class="required-entry" ><?php echo $this->getCommentText(); ?></textarea>
                    </div>
					<?php if (Mage::getStoreConfig('clnews/captcha/enabled')): ?>
                        <!-- - -->
					<?php endif ?>
                    <div class="captchaContainer">
						<?php echo $this->getChildHtml('studioforty9.recaptcha.explicit'); ?>
                    </div>
                    <input name="news_id" type="hidden" value="<?php echo $item->getId() ?>" />
                    <button class="button" type="submit"><?php echo Mage::helper('clnews')->__('Send') ?></button>
                </form>
            </div>
            <script type="text/javascript">
				var contactForm = new VarienForm('postComment', false);
            </script>
		<?php else: ?>
            <p><?php echo Mage::helper('clnews')->__('You must be logged in to post a comment.');?></p>
            <p><a href="<?php echo Mage::helper('customer')->getLoginUrl(); ?>"><?php echo Mage::helper('clnews')->__('click here');?></a> <?php echo Mage::helper('clnews')->__('to log in');?></p>
		<?php endif;
		if(count($comments) > 0){
		?>
            <div class="commentsListing">
                <h2><?php echo $this->__('Comments') ?></h2>
                <div id="comment_block">
					<?php $k = 0;?>
					<?php foreach ($comments as $comment):  ?>
                        <div id="comment_item_<?php echo $k;?>" class="comment-item commentBox">
                            <div class="commentHeader">
                                <span class="commentAuthor"><?php echo $comment->getUser();?></span>
                                <span class="sep"></span>
                                <span class="commentDate"><?php echo Mage::helper('clnews')->formatDate($comment->getCreatedTime());?></span>
                            </div>
                            <div class="commentText"><?php echo $comment->getComment();?></div>
                        </div>
						<?php $k++;?>
					<?php endforeach;   ?>
                </div>
				<?php if($this->getLastPageNum()>1): ?>
                    <nav class="paging">
                        <ul>
                            <li><span id="prev"><span class="prev" style="display:none;"><?php echo $this->__('Previous') ?></span></span></li>
							<?php foreach ($this->getPages() as $_page): ?>
								<?php if ($this->isPageCurrent($_page)): ?>
                                    <li><span id="page_<?php echo $_page ?>" class="page current" onclick="AjaxSend(<?php echo $_page ?>, <?php echo $item->getId() ?>, true);"><?php echo $_page ?></span></li>
								<?php else: ?>
                                    <li><span id="page_<?php echo $_page ?>" class="page" onclick="AjaxSend(<?php echo $_page ?>, <?php echo $item->getId() ?>, true);"><?php echo $_page ?></span></li>
								<?php endif ?>
							<?php endforeach; ?>
                            <li><span id="next"><span class="next"><?php echo $this->__('Next') ?></span></span></li>
                        </ul>
                    </nav>
				<?php endif;?>
            </div>
		<?php } endif; ?>
    </div>
    <a href="<?php echo $this->getBackUrl() ?>" class="newsBackLink"><?php echo $this->__('Back to all news') ?></a>
</div>
<script type="text/javascript">
	function AjaxSend(page, id, showLoader)
	{
		var url = "<?php echo Mage::getBaseUrl()?>clnews/newsitem/ajax/id/" + id + "/page/" + page + "/";
		if (showLoader) {
			jQuery("#clloader").css("display","block");
		}
		jQuery.ajax({
			url: url,
			dataType: 'json',
			success: function(data) {
				var content = '';
				jQuery(".comment-item").remove();
				for(var i = 0; i < data['dat'].cnt; i++) {
					content = '<div id="comment_item_'+ i +'" class="comment-item"><h4 class="username">'+ data['collection'][i].user +'</h4> <?php echo $this->__("posted on")?> ' + data['collection'][i].created_time + '<div>' + data['collection'][i].comment + '</div></div>';
					jQuery('#comment_block').append(content);
				}
				jQuery(".prev").remove();
				if (typeof(data['dat'].back_url) != 'undefined') {
					jQuery("#prev").append('<span class="prev" ' + data['dat'].back_url + ' ><?php echo $this->__('Previous') ?></span>');
				}
				jQuery(".next").remove();
				if (typeof(data['dat'].fovard_url) != 'undefined') {
					jQuery("#next").append('<span class="next" ' + data['dat'].fovard_url + ' ><?php echo $this->__('Next') ?></span>');
				}
				jQuery("#clloader").css("display","none");
			}
		});
		jQuery(".page").removeClass("current");
		var cl = 'page_' + page;
		jQuery("#" + cl).addClass("current");
	}
</script>