<?php
/** @var CommerceLab_News_Block_News $this */
$blogUrl = Mage::getUrl(Mage::helper('clnews')->getRoute());
$currentCategory = $this->getCurrentCategory();
?>
<?php if ($currentCategory): ?>
    <?php $category = $currentCategory; ?>
    <h1><?= $category->getTitle(); ?></h1>
<?php else: ?>
    <h1><?= $this->__(Mage::getStoreConfig('clnews/news/title')) ?></h1>
<?php endif ?>
<?php $items = $this->getNewsItems() ?>
<div class="newsListing">
    <?php $i=0; foreach ($items as $item):
		$i++;
        /** @var CommerceLab_News_Model_News $item */
	?>
        <div class="newsBox">
            <?php if ($item->getImageShortContentShow() && $item->getImageShortContent()): ?>
                <?php $imageSize = $this->getShortImageSize($item) ?>
                <a href="<?= $item->getUrl($this->getCategory()) ?>" class="newsImg">
                    <img src="<?= Mage::helper('clnews')->resizeImage(str_replace('clnews/', '', $item->getImageShortContent()), $imageSize['width'], $imageSize['height'], 'clnews'); ?>"
                         alt="<?= $item->getTitle();?>"/>
                </a>
            <?php endif; ?>
            <div class="newsBoxInfo">
                <?php if (Mage::helper('clnews')->showDate()): ?>
                    <span class="newsDate"><?= Mage::helper('clnews')->formatDate($item->getNewsTime()) ?></span>
                <?php endif ?>
                <a class="newsBoxTitle" href="<?= $item->getUrl($this->getCategory()) ?>" ><?= $item->getTitle();?></a>
                <div class="newsBoxText"><?= mb_strimwidth(strip_tags(Mage::helper('clnews')->contentFilter($item->getShortContent())), 0, 100, "...","UTF-8"); ?></div>
                <?php if(Mage::helper('clnews')->enableLinkRoute()): ?>
                    <?php if($link = Mage::helper('clnews')->getLinkRoute()): ?>
                        <a href="<?= $item->getUrl($this->getCategory()) ?>" class="readMoreLink"><?= $this->__('Read more') ;?></a>
                    <?php else: ?>
                        <a href="<?= $item->getUrl($this->getCategory()) ?>" class="readMoreLink"><?= $this->__('Read more') ;?></a>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    <?php endforeach; ?>
</div>

<?php if($this->getLastPageNum()>1): ?>
<div class="newsPaging">
    <ul class="paging">
        <?php if (!$this->isFirstPage()): ?>
        <?php
            $currentPage = (int) Mage::app()->getRequest()->getParam('page', 1);
            if($currentPage - 1 == 1) {
                $prevUrl = $blogUrl;
            } else {
                $prevUrl = $this->getPreviousPageUrl();
            }
        ?>
            <li><a class="prev" href="<?= $prevUrl ?>" title="<?= $this->__('Previous') ?>"></a></li>
        <?php endif;?>
        <?php foreach ($this->getPages() as $_page): ?>
            <?php if ($this->isPageCurrent($_page)): ?>
                <?php if ($_page == 1): ?>
                    <li class="active"><a href="<?= $blogUrl; ?>"><?= $_page ?></a></li>
                <?php else: ?>
                    <li class="active"><a href="<?= $this->getPageUrl($_page) ?>"><?= $_page ?></a>
                <?php endif; ?>
            <?php else: ?>
                    <?php if ($_page == 1): ?>
                    <li><a href="<?= $blogUrl; ?>"><?= $_page ?></a></li>
                <?php else: ?>
                    <li><a href="<?= $this->getPageUrl($_page) ?>"><?= $_page ?></a>
                <?php endif; ?>
            <?php endif;?>
        <?php endforeach; ?>
        <?php if (!$this->isLastPage()): ?>
            <li><a class="next" href="<?= $this->getNextPageUrl() ?>" title="<?= $this->__('Next') ?>"></a></li>
        <?php endif ?>
    </ul>
</div>
<?php endif;?>
