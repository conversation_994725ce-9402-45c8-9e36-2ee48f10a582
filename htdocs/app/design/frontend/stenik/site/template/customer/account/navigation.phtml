<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2010 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<a href="javascript:;" class="button open-responsive-sidebar"><?php echo $this->__('Navigation');?></a>
<div class="sidebar-responsive-wrapper">
    <nav class="sidebar-nav">
        <ul>
            <?php
                $_links = $this->getLinks();
                foreach ($_links as $key => $_link) {
                    if (!$_link->getLabel())
                        unset($_links[$key]);
                }
                $_index = 1;
                $_count = count($_links);
            ?>

            <?php foreach ($_links as $_link): ?>
                <?php $_last = ($_index++ >= $_count); ?>
                <?php if ($this->isActive($_link)): ?>
                    <li class="active<?php echo ($_last ? ' last' : '') ?>"><a href="<?php echo $_link->getUrl() ?>"><?php echo $_link->getLabel() ?></a></li>
                <?php else: ?>
                    <li<?php echo ($_last ? ' class="last"' : '') ?>><a href="<?php echo $_link->getUrl() ?>"><?php echo $_link->getLabel() ?></a></li>
                <?php endif; ?>
            <?php endforeach; ?>
            </li>
        </ul>
    </nav>
</div>

