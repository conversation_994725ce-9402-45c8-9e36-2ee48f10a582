<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<?php echo $this->getMessagesBlock()->toHtml(); ?>

<h1><?php echo $this->__('Reset a Password'); ?></h1>

<div class="clearH"></div>

<div class="col-xs-6">

	<h4><?php echo $this->__('Reset a Password'); ?></h4>

    <form action="<?php echo $this->getUrl('*/*/resetpasswordpost', array('_query' => array('id' => $this->getCustomerId(), 'token' => $this->getResetPasswordLinkToken()))); ?>" method="post" id="form-validate">

    	<label for="password"><?php echo $this->__('New Password'); ?> <em>*</em></label>
        <?php $minPasswordLength = $this->getMinPasswordLength(); ?>
        <input type="password"
               class="input-text required-entry validate-password min-pass-length-<?php echo $minPasswordLength; ?>"
               name="password"
               id="password" />

		<label for="confirmation"><?php echo $this->__('Confirm New Password'); ?> <em>*</em></label>
        <input type="password" class="input-text required-entry validate-cpassword" name="confirmation" id="confirmation" />

		<div class="celarH2"></div>

        <button type="submit" title="<?php echo $this->__('Reset a Password'); ?>" class="button"><?php echo $this->__('Reset a Password'); ?></button>

		<div class="celarH2"></div>

    </form>

</div>

<script>
    var dataForm = new VarienForm('form-validate', true);
</script>
