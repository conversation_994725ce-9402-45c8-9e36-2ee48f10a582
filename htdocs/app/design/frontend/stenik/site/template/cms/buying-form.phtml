<?php
/**
 * @package Stenik_Form
 * <AUTHOR> Magento Team <<EMAIL>>
 */

/**
 * @see Stenik_Form_Block_Form
 */
?>

<?php
if (Mage::getSingleton('cms/page')->getIdentifier() != 'zapitvane-za-izkupuvane-na-kola') {
    return;
}

$brandModelsMapping = Mage::helper('stenik_site')->getBuyingFormBrandModelsMapping();
$brands = array_keys($brandModelsMapping);
$models = count($brandModelsMapping) ? call_user_func_array('array_merge', $brandModelsMapping) : array();
$models = array_unique($models);

?>

<div class="buying-form-wrapper">
    <?php echo $this->getChildHtml('form_before') ?>

    <?php $formId = $this->getFormId() ? $this->getFormId() : md5($this->getNameInLayout()); ?>

    <form action="<?php echo $this->getActionUrl() ?>" method="<?php echo $this->getActionMethod() ?>" id="<?php echo $this->getHtmlIdPrefix() ?><?php echo $formId ?>Form" enctype="multipart/form-data">
        <?php echo $this->getChildHtml('elements_before') ?>

        <?php
            $fieldsets = $this->getFieldsets();
            $fieldsetCount = count($fieldsets);
            $fieldsetCounter = 0;
        ?>
        <?php foreach ($fieldsets as $fieldsetName => $fieldset): ?>
            <div class="fieldset-wrapper fieldset-wrapper-<?php echo $fieldsetName ?>" id="<?php echo $this->getHtmlIdPrefix() ?><?php echo $fieldset->getId() ?>_box">
                <?php if (!$fieldset->getIsHidden() && $fieldset->getLegend()): ?>
                    <div class="fieldset-legend fieldset-legend-<?php echo $fieldsetName ?>">
                        <span class="title">
                            <?php echo $this->escapeHtml($fieldset->getLegend()) ?>
                        </span>
                    </div>
                <?php endif ?>

                <?php
                    $fieldsetClasses = array(
                        'fieldset-' . $fieldsetName,
                        $fieldset->getClass(),
                    )
                ?>
                <div class="<?php echo trim(implode(' ', $fieldsetClasses)) ?>">
                    <?php foreach ($fieldset->getElements() as $element): ?>
                        <div class="field-row-wrapper input-box <?php echo $element->getWrapperClass(); ?>" id="<?php echo $element->getId(); ?>_row">
                        <?php if ($element->getId() == 'buying_form_submit'): ?>
                            <?php echo $element->getElementHtml(); ?>
                        <?php elseif ($element->getType() == 'checkbox'): ?>
                            <?php echo $element->getElementHtml(); ?>
                            <?php echo $element->getLabelHtml(); ?>
                        <?php else: ?>
                            <?php echo $element->setNoSpan(true)->toHtml(); ?>
                        <?php endif; ?>
                        </div>
                    <?php endforeach ?>
                </div>
            </div>
        <?php endforeach ?>

        <div class="clearH"></div>

        <?php echo $this->getChildHtml('elements_after') ?>
    </form>
</div>
<script>
    jQuery(function($) {
        $('#buying_formForm select').select2();
        $('input[type="file"]').on('change', function(event){
            console.log(event.target.files[0].name);
            $(this).parents('.input-file-box').find('.fake-input').text(event.target.files[0].name);
        });
    })
    function checkFile(sender) {
        var validExts = sender.accept;
        var fileExt = sender.value;
        fileExt = fileExt.substring(fileExt.lastIndexOf('.'));

        if (validExts.indexOf(fileExt) < 0 && fileExt != "") {
            sender.value = "";

            var msg = <?php echo json_encode(Mage::helper('stenik_form')->__('Invalid file selected, valid files are of %s types.', '{{validexts}}')) ?>;
            alert(msg.replace('{{validexts}}', validExts.toString()));
            return false;
        }
        else return true;
    }

    (function($) {
        var $brandInput = $('#buying_form_brand');
        var $modelInput = $('#buying_form_model');

        var allBrands = <?php echo json_encode(array_values($brands)); ?>;
        var brandModelsMap = <?php echo json_encode($brandModelsMapping); ?>;

        var $options = [
            $('<option/>')
                .attr('value', '')
                .text('')
        ];

        for (var i = 0; i < allBrands.length; i++) {
            $options.push(
                $('<option/>')
                    .prop('value', allBrands[i])
                    .attr('value', allBrands[i])
                    .text(allBrands[i])
            );
        }
        $brandInput.append($options);

        $modelInput.prop('disabled', true);
        $modelInput.attr('disabled', true);

        $brandInput.change(function() {
            var brand = $(this).val();

            $modelInput.find('option').remove();

            if (typeof brandModelsMap[brand] != 'undefined') {
                $modelInput.prop('disabled', false);
                $modelInput.attr('disabled', false);

                var $options = [
                    $('<option/>')
                        .attr('value', '')
                        .text('')
                ];

                for (var i = 0; i < brandModelsMap[brand].length; i++) {
                    $options.push(
                        $('<option/>')
                            .prop('value', brandModelsMap[brand][i])
                            .attr('value', brandModelsMap[brand][i])
                            .text(brandModelsMap[brand][i])
                    );
                }
                $modelInput.append($options);
            } else {
                $modelInput.prop('disabled', true);
                $modelInput.attr('disabled', true);
            }
        });

    })(jQuery);
</script>

<?php echo $this->getChildHtml('form_after') ?>

<script>
    var <?php echo $this->getHtmlIdPrefix() ?><?php echo $formId ?>Form = new VarienForm('<?php echo $this->getHtmlIdPrefix() ?><?php echo $formId ?>Form', false);
</script>