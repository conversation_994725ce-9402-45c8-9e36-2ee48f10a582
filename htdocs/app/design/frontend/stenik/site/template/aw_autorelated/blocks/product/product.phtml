<?php

$productCollection = $this->getCollection();
$_helper = $this->helper('catalog/output');

$productCount = null;
if (!$productCollection || !($productCount = $productCollection->getSize())) {
	return;
}

$products = array_slice($productCollection->getAllIds(), 0, 50);
$data = array(
	'template' => 'replacements',
	'title' => $this->htmlEscape($this->getData('name')),
	'products' => json_encode($products)
);
?>
<div class="lazyload replacements" data-include="<?php echo Mage::getBaseUrl().'lazyblock/index/dynamic?'.http_build_query($data) ?>"></div>
