<?php
/**
 * aheadWorks Co.
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the EULA
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://ecommerce.aheadworks.com/AW-LICENSE.txt
 *
 * =================================================================
 *                 MAGENTO EDITION USAGE NOTICE
 * =================================================================
 * This software is designed to work with Magento community edition and
 * its use on an edition other than specified is prohibited. aheadWorks does not
 * provide extension support in case of incorrect edition use.
 * =================================================================
 *
 * @category   AW
 * @package    AW_Autorelated
 * @version    2.5.0
 * @copyright  Copyright (c) 2010-2012 aheadWorks Co. (http://www.aheadworks.com)
 * @license    http://ecommerce.aheadworks.com/AW-LICENSE.txt
 */
?><?php
/** @var $abstractBlock Mage_Catalog_Block_Product_List_Related */
$abstractBlock = $this->helper('awautorelated')->getAbstractProductBlock();
?>
<?php if ($this->getCollection() && $this->getCollection()->getSize()) : ?>
    <div class="crosssell block-related-shoppingcart">
        <h2><?php echo $this->__('Based on your selection, you may be interested in the following items:') ?></h2>
        <ul id="crosssell-products-list">
            <?php foreach ($this->getCollection() as $_item) : ?>
                <li class="item">
                    <a class="product-image" href="<?php echo $abstractBlock->getProductUrl($_item) ?>"
                       title="<?php echo $this->htmlEscape($_item->getName()) ?>"><img
                            src="<?php echo $this->helper('catalog/image')->init($_item, 'thumbnail')->resize(75); ?>"
                            width="75" height="75" alt="<?php echo $this->htmlEscape($_item->getName()) ?>"/></a>

                    <div class="product-details">
                        <h3 class="product-name">
                            <a href="<?php echo $abstractBlock->getProductUrl($_item) ?>"><?php echo $this->htmlEscape($_item->getName()) ?></a>
                        </h3>
                        <?php echo $abstractBlock->getPriceHtml($_item, true, '-related') ?>
                        <button type="button" title="<?php echo $this->__('Add to Cart') ?>" class="button btn-cart"
                                onclick="setLocation('<?php echo $abstractBlock->getAddToCartUrl($_item) ?>')">
                            <span><span><?php echo $this->__('Add to Cart') ?></span></span></button>
                        <ul class="add-to-links">

                            <?php if ($this->helper('wishlist')->isAllow()) : ?>
                                <li><a href="javascript:setLocation('<?php echo $abstractBlock->getAddToWishlistUrl($_item) ?>')"
                                       class="link-wishlist"><?php echo $this->__('Add to Wishlist') ?></a></li>
                            <?php endif; ?>

                            <?php if ($_compareUrl = $abstractBlock->getAddToCompareUrl($_item)): ?>
                                <li>
                                    <span class="separator">|</span>
                                    <a href="javascript:setLocation('<?php echo $_compareUrl ?>')" class="link-compare"><?php echo $this->__('Add to Compare') ?></a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </li>
            <?php endforeach; ?>
        </ul>
        <script type="text/javascript">decorateList('crosssell-products-list', 'none-recursive')</script>
    </div>
<?php endif; ?>