<?php
/** @var Carco_Catalog_Block_Category_Filter_Tires $this */
$formId = uniqid(true);
?>
<div class="search-box col-xs-12">
  <div class="quick-search-result-box">
    <div class="row-header shown-filters">
      <span class="row-title">
        <?= $this->getTitle(); ?>
      </span>
      <form action="#" class="quick-filter-form homepage-search-form" id="filter_<?= $formId ?>_form" method="post">
        <div class="search-fields">
            <?php $filterCounter = 0; ?>
            <?php $filters = $this->getFilters(); ?>
            <?php $filterCount = count($filters); ?>
            <?php foreach ($filters as $i => $filter): ?>
                <?php $filterCounter++; ?>
                <?php $filterItems = $filter->getItems(); ?>
              <div class="search-field-box <?php if(count($filterItems) < 1): ?>disabled-box<?php endif; ?> filterBox-part_vehicle">
                <div class="search-field-label">
                  <span class="search-field-text"><?= $filterCounter; ?>. <?= $filter->getName(); ?></span>
                </div>

                <select
                     name="<?= $filter->getRequestVar() ?>"
                     id="quick_filter_<?= uniqid(true) ?>_<?= $filter->getCode() ?>"
                     data-filter-code="<?= $filter->getCode() ?>"
                     class="search-select <?php if ($filterCounter != $filterCount): ?>limitingFilter<?php else: ?>unlockSearchButton<?php endif ?> filterToBeLimited"
                >
                  <option value=""><?= $this->escapeHtml($filter->getName()) ?></option>
                    <?php foreach ($filterItems as $_i => $_item): ?>
                        <?php /** @var Bubble_Layer_Model_Catalog_Layer_Filter_Item $_item */ ?>
                      <option value="<?= $_item->getValueString(); ?>"
                              <?php if($_item->isSelected()): ?>selected="selected"<?php endif; ?>
                      >
                          <?= $this->escapeHtml($_item->getLabel()); ?>
                      </option>
                    <?php endforeach ?>
                </select>
              </div>
            <?php endforeach; ?>
        </div>
        <div class="button-container">
          <button class="widget-button show-quick-fitler-results" id="<?= $formId; ?>_submit">
            <svg aria-hidden="true" class="icon-svg search">
              <use href="<?= $this->helper('carcobase')->getSvgUrl('svg-sprite.svg#search'); ?>"></use>
            </svg>
              <?= $this->__('Search'); ?>
          </button>
        </div>
        <span class="quick-search-overlay"><span class="quick-search-preloader"></span></span>
      </form>
    </div>
  </div>
</div>
<script type="text/javascript">
    (function ($) {

        function updateQueryStringParameter(uri, key, value) {
            var re = new RegExp("([?&])" + key + "=.*?(&|$)", "i");
            var separator = uri.indexOf('?') !== -1 ? "&" : "?";
            if (uri.match(re)) {
                return uri.replace(re, '$1' + key + "=" + value + '$2');
            } else {
                return uri + separator + key + "=" + value;
            }
        }

        document.getElementById('<?= $formId; ?>_submit')
            .addEventListener('click', function (e) {
                e.preventDefault();
                const formData = $('#filter_<?= $formId ?>_form').serializeArray();
                let filter
                let url = '<?= $this->getCurrentUrl(); ?>'
                for (let i =0;i<formData.length;i++) {
                    filter = formData[i]
                    if (filter['value']) {
                        url = updateQueryStringParameter(url, filter['name'], filter['value'])
                    }
                }

                window.location = url
            })
    })(jQuery)
</script>
