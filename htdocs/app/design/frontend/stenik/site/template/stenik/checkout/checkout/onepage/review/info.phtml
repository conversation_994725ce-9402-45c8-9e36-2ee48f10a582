<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2014 X.commerce, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php echo $this->getChildHtml('items_before'); ?>
<?php $itemsCount = count($this->getItems()); ?>

<div class="checkout-review-box">
    <span class="review-title opened">
        <?php echo $this->__('Cart order review'); ?>
        <span class="items-qty">
            <?php if ($itemsCount <= 1): ?>
                (<?php echo $itemsCount; ?>) <?php echo $this->__('Product'); ?>
            <?php else: ?>
                (<?php echo $itemsCount; ?>) <?php echo $this->__('Products'); ?>
            <?php endif ?>
        </span>
    </span>
    <div class="review-items-box">
        <?php foreach($this->getItems() as $_item): ?>
            <?php echo $this->getItemHtml($_item)?>
        <?php endforeach ?>
    </div>
    <table class="total-table">
        <?php echo $this->getChildHtml('totals'); ?>
    </table>
</div>

<?php echo $this->getChildHtml('items_after'); ?>

<script type="text/javascript">
    truncateOptions();

    jQuery(function($) {

        if ($(window).width() < 991) {
            $('.checkout-review-box .review-title').removeClass('opened');
        };

        $('.checkout-review-box .review-title').click(function() {
            if($(this).hasClass('opened')) {
                $(this).removeClass('opened');
                $('.review-items-box').stop(true, true).slideUp();
            }
            else {
                $(this).addClass('opened');
                $('.review-items-box').stop(true, true).slideDown();
            }
        });

    });
</script>
