<?php
/**
 * @package Stenik_QuickFilter
 * <AUTHOR> Magento Team <<EMAIL>>
 */

/**
 * @see Stenik_QuickFilter_Block_Form
 */
?>

<?php
    $filters    = $this->getFilters();
    $quickFilterId = 'quickfilter_' . md5(uniqid(true));

    if (!$this->isQuickSearchApplied() && $this->areThereActiveFilters()) {
        return;
    }

    $categories = Mage::helper('stenik_site')->getCategories();

    $i = 0;
?>

<?php if (count($filters)): ?>
    <div class="<?php echo $quickFilterId; ?> progress-bar">
        <span class="progress-item active"></span>
        <span class="progress-item-for-part_vehicle_maker progress-item active"></span>
    </div>

    <div class="quick-search-result-box">
        <?php if ($this->isQuickSearchApplied()): ?>
            <?php if ($layer = $this->getLayer()): ?>
                <span class="title">
                    <?php
                        echo sprintf('%s:&nbsp;<strong>%s</strong>',
                            $this->__('Search results'),
                            $layer->getProductCollection()->getSize()
                        );
                    ?>
                </span>

                <a href="javascript:;" class="edit-search">
                    <span class="edit-text"><?php echo $this->__('New search criteria');?></span>
                    <span class="hide-search-text"><?php echo $this->__('Slide up search');?></span>
                </a>
                <div class="clear"></div>
            <?php endif ?>
        <?php endif ?>

        <div class="row-header<?php if ($this->isQuickSearchApplied()): ?> hide-quick-fiters<?php else: ?> shown-filters<?php endif ?>" id="<?php echo $quickFilterId ?>">
            <?php if ($this->getTitle()): ?>
                <span class="row-title"><?php echo $this->getTitle(); ?></span>
            <?php endif ?>
            <form action="<?php echo $this->getUrl('stenik_quickfilter/form/submit') ?>" class="quick-filter-form homepage-search-form" id="<?php echo $quickFilterId ?>_form" method="post">
                <?php if ($this->getCategoryId()): ?>
                    <input type="hidden" name="filter[category]" value="<?php echo $this->getCategoryId() ?>" class="limitingFilter">
                <?php endif ?>
                <div class="search-fields">
                    <div class="search-field-box filterBox-category" data-select-id="quick_filter_<?php echo $quickFilterId ?>_category">
                        <div class="search-field-label">
                            <span class="search-field-icon"><img src="<?php echo $this->getBaseUrl().'svg/category.svg' ?>" alt="<?php echo $this->__('Category'); ?>"></span>
                            <span class="search-field-text"><?php echo (++$i); ?>. <?php echo $this->__('Category'); ?></span>
                        </div>
                        <select
                            name="filter[category]"
                            id="quick_filter_<?php echo $quickFilterId ?>_category"
                            data-filter-to-fetch="part_vehicle_maker"
                            class="search-select limitingFilter"
                        >
                            <option value="<?php echo $this->getCategoryId() ?>"><?php echo $this->escapeHtml($this->__('Category')) ?></option>
                        </select>
                    </div>
                    <?php if ($filterCount = count($filters)): ?>
                        <?php $filterCounter = 0; ?>
                        <?php foreach ($filters as $filter): ?>
                            <?php $filterCounter++; ?>
                            <?php if ($filter->getType() == 'select'): ?>
                                <div class="search-field-box <?php if ($filterCounter != 1): ?>disabled-box<?php endif ?> filterBox-<?php echo $filter->getCode() ?>" data-select-id="quick_filter_<?php echo $quickFilterId ?>_<?php echo $filter->getCode() ?>">
                                    <?php $filterInfo = Mage::helper('stenik_site')->getFilterInfo($filter->getCode()); ?>
                                    <div class="search-field-label">
                                        <span class="search-field-icon"><img src="<?php echo $this->getBaseUrl().'svg/' . $filterInfo['icon'] . '.svg' ?>" alt="<?php echo $this->__($filterInfo['label']); ?>"></span>
                                        <span class="search-field-text"><?php echo (++$i); ?>. <?php echo $this->__($filterInfo['label']); ?></span>
                                    </div>
                                    <?php
                                        $filterToFetch = "";
                                        if ($nextFilter = next($filters)) {
                                            $filterToFetch = $nextFilter->getCode();
                                        }
                                    ?>
                                    <select
                                        name="filter[<?php echo $filter->getCode() ?>]"
                                        id="quick_filter_<?php echo $quickFilterId ?>_<?php echo $filter->getCode() ?>"
                                        data-filter-code="<?php echo $filter->getCode() ?>"
                                        data-filter-to-fetch="<?php echo $filterToFetch; ?>"
                                        class="search-select <?php if ($filterCounter != $filterCount): ?>limitingFilter<?php else: ?>unlockSearchButton<?php endif ?> filterToBeLimited"
                                        <?php if ($filterCounter != 1): ?>disabled="disabled"<?php endif ?>
                                    >
                                        <option value=""><?php echo $this->escapeHtml($filter->getLabel()) ?></option>
                                        <?php if ($filter->getCode() == 'part_vehicle_maker'): ?>
                                            <?php foreach ($filter->getOptions() as $option): ?>
                                                <option value="<?php echo $option['value'] ?>"><?php echo $this->escapeHtml($option['label']) ?></option>
                                           <?php endforeach ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                            <?php elseif ($filter->getType() == 'checkbox'): ?>
                                <div class="filterBox disabled-box checkbox-stylized-box marginTopCheck">
                                    <span class="input-wrapper">
                                        <input
                                            type="checkbox"
                                            class="hidden-checkbox <?php if ($filterCounter != $filterCount): ?>limitingFilter<?php else: ?>unlockSearchButton<?php endif ?>"
                                            id="<?php echo $quickFilterId ?>_filter_<?php echo $filter->getCode() ?>"
                                            name="filter[<?php echo $filter->getCode() ?>]"
                                            value="<?php echo $filter->getValue() ?>"
                                        >
                                    </span>
                                    <label for="<?php echo $quickFilterId ?>_filter_<?php echo $filter->getCode() ?>" class="click-stylized-input">
                                        <?php echo $this->__('Only in stock');?>
                                    </label>
                                </div>
                            <?php endif ?>
                        <?php endforeach ?>
                    <?php endif ?>
                </div>
                <div class="button-container">
                    <button class="widget-button show-quick-fitler-results" onclick="ga('send', 'event', 'Homepage', 'Click', 'CalcButton');">
                        <svg aria-hidden="true" class="icon-svg search">
                            <use href="<?php echo $this->getBaseUrl().'svg/svg-sprite.svg' . '#search' ?>"></use>
                        </svg>
                        <?php echo $this->__('Search') ?>
                    </button>
                </div>
                <span class="quick-search-overlay"><span class="quick-search-preloader"></span></span>
            </form>
        </div>
        <script>
            jQuery(function($) {
                let filters = $('.search-select');
                let flag = 0;

                filters.select2({
                    dropdownPosition: 'below',
                    templateResult: function (option) {
                        if ($(option.element).data('category-level') == 1) {
                            if (option.id == <?php echo Mage::getStoreConfig('archive_id/settings/archive_cat_id'); ?>) {
                                return;
                            }
                            return $('<strong>' + option.text + '<strong>');
                        }
                        return option.text;
                    }
                });


                filters.on('select2:open', function (){
                    let that = $(this);
                    if(flag === 0) {
                        $.ajax({
                            type: "POST",
                            url: "<?php echo Mage::getBaseUrl() . 'ajax_filter' ?>",
                            dataType: "html",
                            success: function (result) {
                                $('#quick_filter_<?php echo $quickFilterId ?>_category').append(result);
                            },
                            error: function () {
                                $('#quick_filter_<?php echo $quickFilterId ?>_category').prop( "disabled", true );
                            }

                        }).done(function () {
                            that.select2("destroy");
                            that.select2({dropdownPosition: 'below'});
                            that.select2('open');
                        });
                        flag = 1;
                    }
                });

                var $button = $('#<?php echo $quickFilterId ?>_form button');

                var checkSearchButtonUnlocking = function() {
                    if ($(this).hasClass('unlockSearchButton')) {
                        var val = $(this).is(':disabled') ? null : $(this).val();
                        $('#<?php echo $quickFilterId; ?> .show-quick-fitler-results')
                            .prop('disabled', !val)
                            .attr('disabled', !val);
                    }
                };

                // $('#<?php echo $quickFilterId ?> .unlockSearchButton').change(checkSearchButtonUnlocking);
                // $('#<?php echo $quickFilterId ?> .unlockSearchButton').on('checkSearchButtonUnlocking', checkSearchButtonUnlocking);

                $('#<?php echo $quickFilterId ?> .filterToBeLimited').each(function() {
                    var origOptions = [];
                    $(this).find('option').each(function() {
                        origOptions.push(this);
                    });

                    $(this).data('orig_options', origOptions);
                });

                var limitSelectOptions = function($select, values, options) {
                    if (typeof options === 'undefined') {
                        options = null;
                    }

                    var $select = $($select);

                    var origOptions = $select.data('orig_options');

                    if (!options) {
                        options = origOptions;
                    } else {
                        var optionAsObjects = [];

                        for (value in options) {
                            if (options.hasOwnProperty(value)) {
                                var optionFound = false;
                                for (var i = 0; i < origOptions.length; i++) {
                                    if (origOptions[i].value == value) {
                                        optionFound = true;
                                        break;
                                    }
                                }
                                if (optionFound === false) {
                                    optionAsObjects.push($('<option/>').attr('value', value).html(options[value]).get(0));
                                }
                            }
                        }

                        options = optionAsObjects;

                        if (origOptions) {
                            options = origOptions.concat(options);
                        }
                    }

                    if (!options) return;


                    var newOptions = [];

                    for (var i = 0; i < options.length; i++) {
                        var option = $(options[i]);
                        if (option.attr('value') == '' ||
                            values === 'none' ||
                            values.indexOf(option.attr('value')) != -1
                        ) {
                            newOptions.push(option);
                        }
                    }

                    newOptions = newOptions.sort(function($a, $b) {
                        if ($a.prop('value') == '') {
                            return 1;
                        }

                        if ($b.prop('value') == '') {
                            return 1;
                        }

                        return $a.text().trim().localeCompare($b.text().trim(), 'en', {sensitivity: 'base', numeric: true});
                    });

                    var origVal = $select.val();

                    var selectHtmlBeforeChange = $select.html();
                    $select.find('option').remove();

                    for (var i = 0; i < newOptions.length; i++) {
                        $select.append(newOptions[i])
                    }

                    $select.val(origVal);
                    var selectHtmlAfterChange = $select.html();

                    return selectHtmlBeforeChange.replace(/\w/g, '') != selectHtmlAfterChange.replace(/\w/g, '');
                };

                $('#<?php echo $quickFilterId ?> .filterToBeLimited').each(function() {
                    if ($(this).is('select')) {
                        if (limitSelectOptions(this, 'none')) {
                            $(this).change();
                        }
                    }
                });

                var chainDisable = function($input) {
                    $input = $($input);

                    $input.data('_chainDisable_currently_disabling', true);

                    $input.prop('disabled', true);
                    $input.attr('disabled', true);
                    $input.parent('.search-field-box').addClass('disabled-box');
                    $('.<?php echo $quickFilterId ?> .progress-item-for-' + $input.data('filter-code')).remove();

                    limitSelectOptions($input, 'none');
                    $input.trigger('checkSearchButtonUnlocking');
                    if ($input.data('filter-to-fetch')) {
                        $('#<?php echo $quickFilterId ?> .filterToBeLimited').each(function() {
                            if ($(this).data('filter-code') == $input.data('filter-to-fetch')) {
                                if (!$(this).data('_chainDisable_currently_disabling')) {
                                    chainDisable(this);
                                }
                            }
                        });
                    }

                    $input.data('_chainDisable_currently_disabling', null);
                };

                var currentlyFiltering = false;
                $('#<?php echo $quickFilterId ?> .limitingFilter').change(function() {
                    if (currentlyFiltering) {
                        return;
                    }

                    var filtersSerialized = $('#<?php echo $quickFilterId ?> .limitingFilter').serializeArray();
                    var filters = {};
                    for (var i = filtersSerialized.length - 1; i >= 0; i--) {
                        if (filtersSerialized[i].value) {
                            filters[filtersSerialized[i].name] = filtersSerialized[i].value;
                        }
                    }

                    var filtersToFetch = [];
                    /*$('#<?php echo $quickFilterId ?> .filterToBeLimited').each(function() {
                        if ($(this).data('filter-code')) {
                            filtersToFetch.push($(this).data('filter-code'));
                        }
                    });*/
                    $('#<?php echo $quickFilterId ?> .filterToBeLimited').change(function() {
                        if ($(this).data('filter-to-fetch')) {
                            filtersToFetch.push($(this).data('filter-to-fetch'));
                        }
                    });

                    if ($(this).data('filter-to-fetch')) {
                        filtersToFetch.push($(this).data('filter-to-fetch'));
                    }

                    $('#<?php echo $quickFilterId ?> .filterToBeLimited').each(function() {
                        if ($(this).is('select') && filtersToFetch.indexOf($(this).data('filter-code')) != -1) {
                            chainDisable(this);
                        }
                    });
                    if (!$(this).val()) {
                        return;
                    }

                    $('#<?php echo $quickFilterId ?>').closest('.quick-search-result-box').addClass('loading');

                    $.ajax({
                        url: <?php echo json_encode($this->getUrl('stenik_quickfilter/form/getFilters')) ?>,
                        type: 'post',
                        dataType: 'json',
                        data: $.extend(filters, {filters_to_fetch: filtersToFetch, options_with_labels: true})
                    }).done(function(response) {
                        currentlyFiltering = true;
                        $('#<?php echo $quickFilterId ?> .filterToBeLimited').each(function() {
                            if ($(this).is('select') && filtersToFetch.indexOf($(this).data('filter-code')) != -1) {
                                if (limitSelectOptions(this, 'none')) {
                                    $(this).change();
                                }
                            }
                        });
                        if (response) {
                            for (key in response) {
                                if (Object.keys(response[key]).length) {
                                    $('#<?php echo $quickFilterId ?> .filterToBeLimited[data-filter-code="' + key + '"]').each(function() {
                                        var fireChangeEvent = false;
                                        if ($(this).is('select')) {
                                            if (limitSelectOptions(this, Object.keys(response[key]), response[key])) {
                                                fireChangeEvent = true;
                                            }
                                        }

                                        $(this).prop('disabled', false).attr('disabled', false);
                                        $(this).parent('.search-field-box').removeClass('disabled-box');
                                        var span = $('<span>');
                                        span.addClass('progress-item-for-' + $(this).data('filter-code'));
                                        span.addClass('progress-item');
                                        span.addClass('active');
                                        $('.<?php echo $quickFilterId; ?>.progress-bar').append(span);

                                        $(this).trigger('checkSearchButtonUnlocking');
                                        if (fireChangeEvent) {
                                            $(this).change();
                                        }
                                    });
                                }
                            }
                        }
                        currentlyFiltering = false;
                        $('#<?php echo $quickFilterId ?>').closest('.quick-search-result-box').removeClass('loading');
                    });
                });
            });
        </script>
    </div>
<?php endif ?>
