<?php
/**
 * @see Stenik_AjaxAddToCart_Block_Js
 */
?>

<div id="stenik-ajaxaddtocart-result" style="display: none"></div>
<div id="stenik-ajaxaddtocart-overlay" style="display: none"></div>

<script>
  if (window.innerWidth > 960) {
    docReady(function () {
      const addToCartTrigger = function(event) {
        const $element = jQuery(this);

        const data = {};
        if (typeof data.product === 'undefined') {
          data.product = false;

          let destination = $element.data('origOnclickAttr');
          if (!destination) {
            destination = $element.attr('href');
          }

          if (destination) {
            let matches;
            if (matches = destination.match(/product\/(\d+)/)) {
              data.product = matches[1];
            }
          }
        }

        if (!data.product) {
          return true;
        }

        data.form_key = getUserFormKey();

        jQuery('#stenik-ajaxaddtocart-overlay').show();

        let hideOverlay = true;
        jQuery.ajax({
          url: <?php echo json_encode($this->getUrl('stenik_ajaxaddtocart/index/add')) ?>,
          method: 'post',
          dataType: 'json',
          data: data
        }).done(function(response) {
          if ((typeof response.update !== 'undefined') && response.update) {
            for (let i = 0; i < response.update.length; i++) {
              jQuery(response.update[i].selector).replaceWith(response.update[i].html);
            }
          }

          if ((typeof response.html !== 'undefined') && response.html) {
            hideOverlay = false;
            const $resultWrapper = jQuery('#stenik-ajaxaddtocart-result');
            $resultWrapper.html(response.html);
            $resultWrapper.find('.closeAddToCartResult').click(function() {
              $resultWrapper.hide();
              jQuery('#stenik-ajaxaddtocart-overlay').hide();
            });
            jQuery(document).keyup(function(e) {
              if (e.keyCode == 27) {
                $resultWrapper.hide();
                jQuery('#stenik-ajaxaddtocart-overlay').hide();
              }
            });

            $resultWrapper.show();
          }
        }).fail(function() {
          alert(<?php echo json_encode($this->__('Something went wrong. Please try again.')) ?>);
        }).always(function() {
          if (hideOverlay) {
            jQuery('#stenik-ajaxaddtocart-overlay').hide();
          }
        });

        if (typeof event != 'undefined') {
          Event.stop(event);
          event.preventDefault();
          event.stopPropagation();
          event.stopImmediatePropagation();
        }

        return false;
      };

      jQuery('.button.add-to-cart').each(function() {
        const $element = jQuery(this);
        $element.click(addToCartTrigger.bind(this));

        const onclickAttrFunc = $element.prop('onclick');
        if (onclickAttrFunc) {
          $element.data('origOnclickAttr', $element.attr('onclick'));
          $element.removeProp('onclick');
          $element.removeAttr('onclick');
          $element.click(onclickAttrFunc);
        }
      });
    })
  }
</script>
