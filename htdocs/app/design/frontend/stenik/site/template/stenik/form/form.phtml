<?php
/**
 * @package Stenik_Form
 * <AUTHOR> Magento Team <<EMAIL>>
 */

/**
 * @see Stenik_Form_Block_Form
 */
?>

<?php echo $this->getChildHtml('form_before') ?>

<?php $formId = $this->getFormId() ? $this->getFormId() : md5($this->getNameInLayout()); ?>

<form action="<?php echo $this->getActionUrl() ?>" method="<?php echo $this->getActionMethod() ?>" id="<?php echo $this->getHtmlIdPrefix() ?><?php echo $formId ?>Form">
    <?php echo $this->getChildHtml('elements_before') ?>

    <?php
        $fieldsets = $this->getFieldsets();
        $fieldsetCount = count($fieldsets);
        $fieldsetCounter = 0;
    ?>
    <?php foreach ($fieldsets as $fieldsetName => $fieldset): ?>
        <div class="fieldset-wrapper fieldset-wrapper-<?php echo $fieldsetName ?>" id="<?php echo $this->getHtmlIdPrefix() ?><?php echo $fieldset->getId() ?>_box">
            <?php if (!$fieldset->getIsHidden() && $fieldset->getLegend()): ?>
                <div class="fieldset-legend fieldset-legend-<?php echo $fieldsetName ?>">
                    <span class="title">
                        <?php echo $this->escapeHtml($fieldset->getLegend()) ?>
                    </span>
                </div>
            <?php endif ?>

            <?php
                $fieldsetClasses = array(
                    'fieldset-' . $fieldsetName,
                    $fieldset->getClass(),
                )
            ?>
            <div class="<?php echo trim(implode(' ', $fieldsetClasses)) ?>">
                <?php foreach ($fieldset->getElements() as $element): ?>
                    <div class="field-row-wrapper input-box <?php echo $element->getWrapperClass(); ?>" id="<?php echo $element->getId(); ?>_row">
                    <?php if ($element->getId() == 'stenik_form_errorsignal_form_submit'): ?>
                        <?php echo $element->getElementHtml(); ?>
                    <?php elseif ($element->getType() == 'checkbox'): ?>
                        <?php echo $element->getElementHtml(); ?>
                        <?php echo $element->getLabelHtml(); ?>
                    <?php else: ?>
                        <?php echo $element->setNoSpan(true)->toHtml(); ?>
                    <?php endif; ?>
                    </div>
                <?php endforeach ?>
            </div>
        </div>
    <?php endforeach ?>

    <div class="clearH"></div>

    <?php echo $this->getChildHtml('elements_after') ?>
</form>

<?php echo $this->getChildHtml('form_after') ?>

<script>
    var <?php echo $this->getHtmlIdPrefix() ?><?php echo $formId ?>Form = new VarienForm('<?php echo $this->getHtmlIdPrefix() ?><?php echo $formId ?>Form', false);
</script>