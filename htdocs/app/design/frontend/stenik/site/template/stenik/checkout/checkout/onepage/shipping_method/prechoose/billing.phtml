<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2014 X.commerce, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<ul class="form-list">
    <?php if ($this->customerHasAddresses()): ?>
        <li class="fields wide">
            <label
                for="billing-address-select"><?php echo $this->__('Select a billing address from your address book or enter a new address.') ?></label>
            <div class="input-box">
                <?php echo $this->getAddressesHtmlSelect('billing') ?>
            </div>
        </li>
    <?php endif; ?>
    <li id="billing-new-address-form"<?php if ($this->customerHasAddresses()): ?> style="display:none;"<?php endif; ?>>
        <input type="hidden" name="billing[address_id]" value="<?php echo $this->getAddress()->getId() ?>"
               id="billing:address_id" />
        <ul>
            <li class="fields">
                <?php echo $this->getLayout()->createBlock('customer/widget_name')->setObject($this->getAddress()->getFirstname() ? $this->getAddress() : $this->getQuote()->getCustomer())->setForceUseCustomerRequiredAttributes(!$this->isCustomerLoggedIn())->setFieldIdFormat('billing:%s')->setFieldNameFormat('billing[%s]')->toHtml() ?>
            </li>
            <li class="fields">
                <?php if (!$this->isCustomerLoggedIn()): ?>
                    <div class="field">
                        <label for="billing:email" class="required"><?php echo $this->__('Email') ?> <em>*</em></label>
                        <div class="input-box">
                            <input type="text" name="billing[email]" id="billing:email"
                                   value="<?php echo $this->escapeHtml($this->getAddress()->getEmail()) ?>"
                                   title="<?php echo $this->__('Email') ?>"
                                   class="input-text validate-email required-entry js-email" />
                        </div>
                    </div>
                <?php endif; ?>
                <div class="field">
                    <label for="billing:telephone" class="required"><?php echo $this->__('Telephone') ?>
                        <em>*</em></label>
                    <div class="input-box">
                        <input type="tel" name="billing[telephone]"
                               value="<?php echo $this->escapeHtml($this->getAddress()->getTelephone()) ?>"
                               title="<?php echo $this->__('Telephone') ?>"
                               class="input-text <?php echo $this->helper('customer/address')->getAttributeValidationClass('telephone') ?>"
                               id="billing:telephone" />
                    </div>
                </div>
            </li>
            <li class="fields">
                <div class="field">
                    <label for="billing:country_id" class="required"><?php echo $this->__('Country') ?>
                        <em>*</em></label>
                    <div class="input-box">
                        <?php echo $this->getCountryHtmlSelect('billing') ?>
                    </div>
                </div>
                <div class="field">
                    <div class="input-box">
                        <label for="billing:region_id" class="required"><?php echo $this->__('State/Province') ?>
                            <em>*</em></label>
                        <select id="billing:region_id" name="billing[region_id]"
                                title="<?php echo $this->__('State/Province') ?>" class="validate-select"
                                style="display:none;">
                            <option value=""><?php echo $this->__('Please select region, state or province') ?></option>
                        </select>
                        <script type="text/javascript">
                          $('billing:region_id').setAttribute('defaultValue', "<?php echo $this->getAddress()->getRegionId() ?>")
                        </script>
                        <input type="text" id="billing:region" name="billing[region]"
                               value="<?php echo $this->escapeHtml($this->getAddress()->getRegion()) ?>"
                               title="<?php echo $this->__('State/Province') ?>"
                               class="input-text <?php echo $this->helper('customer/address')->getAttributeValidationClass('region') ?>"
                               style="display:none;" />
                    </div>
                </div>
            </li>
            <li class="fields wide">
                <div class="delivery-to-wrapper">
                    <div class="step-sub-title"><?php echo $this->__('Delivery'); ?>: <em>*</em></div>
                    <div class="shipping-methods-group">
                        <?php echo $this->getChildHtml('shipping_methods') ?>
                    </div>

                    <?php echo $this->getChildHtml('additional_address_fields') ?>
                    <div class="standart-address-fileds-wrapper">
                        <div class="non-custom-address-fields-content address-fields">
                            <div class="field">
                                <label for="billing:city" class="required"><?php echo $this->__('City') ?>
                                    <em>*</em></label>
                                <div class="input-box">
                                    <input type="text" title="<?php echo $this->__('City') ?>" name="billing[city]"
                                           value="<?php echo $this->escapeHtml($this->getAddress()->getCity()) ?>"
                                           class="input-text <?php echo $this->helper('customer/address')->getAttributeValidationClass('city') ?>"
                                           id="billing:city" />
                                </div>
                            </div>
                            <div class="field">
                                <label for="billing:postcode"
                                       class="required"><?php echo $this->__('Zip/Postal Code') ?> <em>*</em></label>
                                <div class="input-box">
                                    <input type="text" title="<?php echo $this->__('Zip/Postal Code') ?>"
                                           name="billing[postcode]" id="billing:postcode"
                                           value="<?php echo $this->escapeHtml($this->getAddress()->getPostcode()) ?>"
                                           class="input-text validate-zip-international <?php echo $this->helper('customer/address')->getAttributeValidationClass('postcode') ?>" />
                                </div>
                            </div>
                        </div>
                        <div class="non-custom-address-fields-content address-fields">
                            <div class="field" style='width:100%;'>
                                <?php $_streetValidationClass = $this->helper('customer/address')->getAttributeValidationClass('street'); ?>
                                <?php $_streetValidationClass = trim(str_replace('required-entry', '', $_streetValidationClass)); ?>
                                <label for="billing:street1" class="required"><?php echo $this->__('Address') ?>
                                    <em>*</em></label>
                                <div class="input-box">
                                    <input type="text" title="<?php echo $this->__('Street Address') ?>"
                                           name="billing[street][]" id="billing:street1"
                                           value="<?php echo $this->escapeHtml($this->getAddress()->getStreet(1)) ?>"
                                           class="input-text <?php echo $_streetValidationClass ?>" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </li>
            <?php if ($this->helper('customer/address')->isVatAttributeVisible()) : ?>
                <li class="fields wide">
                    <label for="billing:vat_id"><?php echo $this->__('VAT Number') ?></label>
                    <div class="input-box">
                        <input type="text" id="billing:vat_id" name="billing[vat_id]"
                               value="<?php echo $this->escapeHtml($this->getAddress()->getVatId()) ?>"
                               title="<?php echo $this->__('VAT Number') ?>"
                               class="input-text <?php echo $this->helper('customer/address')->getAttributeValidationClass('vat_id') ?>" />
                    </div>
                </li>
            <?php endif; ?>
            <?php $_dob = $this->getLayout()->createBlock('customer/widget_dob') ?>
            <?php $_gender = $this->getLayout()->createBlock('customer/widget_gender') ?>
            <?php if ($_dob->isEnabled() || $_gender->isEnabled()): ?>
                <li class="fields wide">
                    <?php if ($_dob->isEnabled()): ?>
                        <?php echo $_dob->setDate($this->getQuote()->getCustomerDob())->setFieldIdFormat('billing:%s')->setFieldNameFormat('billing[%s]')->toHtml() ?>
                    <?php endif; ?>
                    <?php if ($_gender->isEnabled()): ?>
                        <?php echo $_gender->setGender($this->getQuote()->getCustomerGender())->setFieldIdFormat('billing:%s')->setFieldNameFormat('billing[%s]')->toHtml() ?>
                    <?php endif ?>
                </li>
            <?php endif ?>
            <?php if (!$this->isCustomerLoggedIn()): ?>
                <?php $createAccountMethod = ($this->getQuote()->getCheckoutMethod() == Mage_Checkout_Model_Type_Onepage::METHOD_REGISTER) ?>

                <?php if (!$createAccountMethod): ?>
                    <li class="fields wide create-account-checkbox">
                        <input type="checkbox" name="create_account" id="billing:create_account" class="input-checkbox"
                               <?php if ($createAccountMethod): ?>checked="checked"<?php endif ?>>
                        <label for="billing:create_account"
                               class="label-checkbox"><?php echo $this->__('Create Account for easier future orders') ?></label>
                    </li>
                <?php endif ?>

                <li class="fields" id="register-customer-password"
                    <?php if (!$createAccountMethod): ?>style="display: none"<?php endif ?>>
                    <?php if ($createAccountMethod): ?>
                        <input type="hidden" name="create_account" id="billing:create_account" value="1">
                    <?php endif ?>

                    <div class="field">
                        <label for="billing:customer_password" class="required"><?php echo $this->__('Password') ?>
                            <em>*</em></label>
                        <div class="input-box">
                            <input type="password" name="billing[customer_password]" id="billing:customer_password"
                                   title="<?php echo $this->__('Password') ?>"
                                   class="input-text required-entry validate-password" />
                        </div>
                    </div>
                    <div class="field">
                        <label for="billing:confirm_password"
                               class="required"><?php echo $this->__('Confirm Password') ?> <em>*</em></label>
                        <div class="input-box">
                            <input type="password" name="billing[confirm_password]"
                                   title="<?php echo $this->__('Confirm Password') ?>" id="billing:confirm_password"
                                   class="input-text required-entry validate-cpassword" />
                        </div>
                    </div>
                </li>
            <?php endif; ?>
            <?php if ($this->isCustomerLoggedIn() && $this->customerHasAddresses()): ?>
                <li class="fields wide control">
                    <input type="checkbox" name="billing[save_in_address_book]" value="1"
                           title="<?php echo $this->__('Save in address book') ?>" id="billing:save_in_address_book"
                           onchange="if(window.shipping) shipping.setSameAsBilling(false);"<?php if ($this->getAddress()->getSaveInAddressBook()): ?> checked="checked"<?php endif; ?>
                           class="checkbox" /><label
                        for="billing:save_in_address_book"><?php echo $this->__('Save in address book') ?></label>
                </li>
            <?php else: ?>
                <li class="fields wide no-display"><input type="hidden" name="billing[save_in_address_book]"
                                                          value="1" /></li>
            <?php endif; ?>
            <?php echo $this->getChildHtml('form.additional.info'); ?>
        </ul>
    </li>
    <?php $_taxvat = $this->getLayout()->createBlock('customer/widget_taxvat') ?>
    <?php if ($_taxvat->isEnabled()): ?>
        <li class="fields wide">
            <?php echo $_taxvat->setTaxvat($this->getQuote()->getCustomerTaxvat())->setFieldIdFormat('quote:%s')->setFieldNameFormat('quote[%s]')->toHtml() ?>
        </li>
    <?php endif ?>
    <?php echo $this->getChildHtml('checkout.onepage.billing.extra') ?>
</ul>

<input type="hidden" name="billing[use_for_shipping]" value="1" />
<?php echo $this->getBlockHtml('formkey') ?>
<script>
  (function($) {
    $('body').on('change', '.stenik-checkout .js-email', function(e) {
      $('.js-email').val($.trim($('.js-email').val()))
    })

    $('#billing-address-select').change(function() {
      if ($(this).val() == '') {
        $('#billing-new-address-form').show()
      } else {
        $('#billing-new-address-form').hide()
      }
    })

    $('#billing\\:create_account').change(function() {
      if ($(this).is(':checked')) {
        $('#register-customer-password').show()
      } else {
        $('#register-customer-password').hide()
      }
    })

      <?php /** Prechoose shipping method **/ ?>

      <?php
      $customerAddressCountryIdMap = [];
      if ($this->isCustomerLoggedIn()) {
          foreach ($this->getCustomer()->getAddresses() as $address) {
              $customerAddressCountryIdMap[$address->getId()] = $address->getCountryId();
          }
      }
      ?>
    var customerAddressCountryIdMap = <?php echo json_encode($customerAddressCountryIdMap) ?>;

    var refreshingShippingMethodsVisibility = false
    var refreshShippingMethodsVisibility = function() {
      if (refreshingShippingMethodsVisibility) {
        return
      }

      refreshingShippingMethodsVisibility = true

      var countryId = $('#billing\\:country_id').val()

      var addressId = $('#billing-address-select').val()
      if (addressId != '' && typeof customerAddressCountryIdMap[addressId] != 'undefined') {
        countryId = customerAddressCountryIdMap[addressId]
      }


      $('.stenik-checkout .shippingMethod').each(function() {
        if (applicableCountries = $(this).data('applicable-countries')) {
          applicableCountries = applicableCountries.split(',')

          if (applicableCountries.indexOf(countryId) >= 0) {
            var $input = $(this).show().find('input.prechoose')
            $input.prop('disabled', false)

            var $checkedReadShippingMethod = $('#s_available_method:checked')
            if ($checkedReadShippingMethod.prop('name') == $input.prop('name')) {
              $checkedReadShippingMethod.prop('checked', false).change()
            }
          } else {
            $(this).find('input.prechoose:checked').prop('checked', false).change()
            $(this).hide().find('input.prechoose').prop('disabled', true)
          }
        }
      })

      var $addressFields = $('.stenik-checkout #billing-new-address-form .address-fields')
      $addressFields.hide()

      // $('.stenik-checkout .shippingMethod input:not(:checked)').each(function() {
      //     $('.' + $(this).attr('id') + '-content').hide();
      //     $('.' + $(this).attr('id') + '-hidden-content').show();
      // });

      $('.stenik-checkout .shippingMethod input:checked').each(function() {
        $addressFields.filter('.' + $(this).data('used-address-fields') + '-content').show()
        // $('.' + $(this).attr('id') + '-content');
        // $('.' + $(this).attr('id') + '-content').show();
        // $('.' + $(this).attr('id') + '-hidden-content').hide();
      })


      refreshingShippingMethodsVisibility = false
    }

    refreshShippingMethodsVisibility()

    jQuery(function() {
      refreshShippingMethodsVisibility()
    })

    $('#billing\\:country_id').change(refreshShippingMethodsVisibility)
    $('.shippingMethod input').change(refreshShippingMethodsVisibility)
    $('#billing-address-select').change(refreshShippingMethodsVisibility)

    var billingRegionUpdater = new RegionUpdater('billing:country_id', 'billing:region', 'billing:region_id', <?php echo $this->helper('directory')->getRegionJson() ?>, undefined, 'billing:postcode')

  })(jQuery)
</script>
