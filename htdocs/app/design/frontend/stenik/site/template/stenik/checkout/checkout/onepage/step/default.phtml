<?php

?>

<?php
    $stepCode = $this->getBlockAlias() ? : $this->getNameInLayout();
?>

<div id="sc-checkout-step-<?php echo $stepCode ?>" data-checkout-step="<?php echo $stepCode ?>" class="sc-checkout-step" style="display:none;">
    <form id="co-<?php echo $stepCode ?>-form" action="">
        <div class="row">
            <div class="col-xs-8">
                <span class="step-title"><span class="step-number">1.</span> <?php echo $this->__('Delivery details'); ?></span>
                <?php echo $this->getChildHtml('checkout.onepage.section.billing'); ?>
                <span class="step-title"><span class="step-number">2.</span> <?php echo $this->__('Delivery method'); ?></span>
                <?php echo $this->getChildHtml('checkout.onepage.section.shipping_method'); ?>
                <span class="step-title"><span class="step-number">3.</span> <?php echo $this->__('Payment Method'); ?></span>
                <?php echo $this->getChildHtml('checkout.onepage.section.payment_method'); ?>
                <div class="coupon-review-col">
                    <div class="coupon-review-wrapper">
                        <?php echo $this->getChildHtml('checkout.onepage.section.review'); ?>
                    </div>
                </div>
                <span class="step-title"><span class="step-number">4.</span> <?php echo $this->__('Complete the order'); ?></span>
                <?php echo $this->getChildHtml('checkout.onepage.section.comment.agreements'); ?>

                <div class="buttons-set" id="<?php echo $stepCode ?>-buttons-container">
                    <?php
                        $buttonTitle = $this->__('Continue');
                        if ($this->getIsLastStep()) {
                            $buttonTitle = $this->__('Place Order');
                        }
                    ?>

                    <?php if ($this->getIsLastStep()): ?>
                        <input type="hidden" name="place_order" value="1">
                    <?php endif ?>

                    <button
                        type="button"
                        title="<?php echo $buttonTitle ?>"
                        class="button checkout-color"
                        onclick="stenikOnepageCheckout.save('<?php echo $stepCode ?>'); ga('send', 'event', 'Checkout', 'Click', 'PlaceOrder');"
                    >
                        <?php echo $buttonTitle ?>
                    </button>
                    <span class="please-wait" id="<?php echo $stepCode ?>-please-wait" style="display:none;"></span>
                </div>
            </div>
        </div>
    </form>
    <div class="stenik-onepage-section-overlay stenik-checkout-overlay"><span class="loaderIcon"></span></div>
</div>

<script>
    stenikOnepageCheckout.addStep(<?php echo json_encode($stepCode) ?>);
    // var addressAndShippingMethodForm = new VarienForm('co-<?php echo $stepCode ?>-form');
    // $(addressAndShippingMethodForm.form).observe('submit', function(event){
    //     this.save('<?php echo $stepCode ?>', $('co-<?php echo $stepCode ?>-form'));
    //     Event.stop(event);
    // }.bind(stenikOnepageCheckout));

    jQuery(function($) {

        if ($(window).width() > 991) {
            var $checkoutSidebarBox = $('.coupon-review-wrapper');
            var $rightSidebarCol    = $('.coupon-review-col');
            var $stenikCheckout     = $('.stenik-checkout');
            var checkoutSidebarBoxTopOffset = $checkoutSidebarBox.offset().top; <?php // No funny classes yet, so it's at the begining of the column. ?>

            if (window.stenikCheckoutSidebarPositionReviewBox) {
                $(window).unbind('scroll', window.stenikCheckoutSidebarPositionReviewBox);
                $stenikCheckout.unbind('resize', window.stenikCheckoutSidebarPositionReviewBox);
            }

            window.stenikCheckoutSidebarPositionReviewBox = function() {
                $rightSidebarCol.css({height: 'auto'});

                var scrollPosition           = $(document).scrollTop();
                var checkoutSidebarBoxHeight = $checkoutSidebarBox.outerHeight(true);
                var mainColHeight            = $stenikCheckout.outerHeight(true);

                $rightSidebarCol.css({height: (mainColHeight-70)+'px'});

                var rightSidebarColBottomOffset = $rightSidebarCol.offset().top + $rightSidebarCol.height();

                if (scrollPosition >= checkoutSidebarBoxTopOffset && checkoutSidebarBoxHeight < mainColHeight) {
                    $checkoutSidebarBox.addClass('sticky-sidebar');
                    if (scrollPosition > rightSidebarColBottomOffset + 70 - checkoutSidebarBoxHeight) {
                        $checkoutSidebarBox.addClass('bottom-stop');
                    } else {
                        $checkoutSidebarBox.removeClass('bottom-stop');
                    }
                } else {
                    $checkoutSidebarBox.removeClass('sticky-sidebar');
                };
            };

            $(window).scroll(window.stenikCheckoutSidebarPositionReviewBox);
            $stenikCheckout.resize(window.stenikCheckoutSidebarPositionReviewBox);
            window.stenikCheckoutSidebarPositionReviewBox();
        }

    });
</script>
