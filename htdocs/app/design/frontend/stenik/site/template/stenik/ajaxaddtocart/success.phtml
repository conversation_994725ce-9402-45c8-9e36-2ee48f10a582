<?php
/**
 * @package base_default
 * <AUTHOR> Magento Team <<EMAIL>>
 */

/**
 * @see Stenik_AjaxAddToCart_Block_Result
 */
?>
<div class="stenik-ajaxAddToCart-result stenik-ajaxAddToCart-success">
    <?php if ($this->getMessage()): ?>
        <div class="success-message">
            <?php echo $this->getMessage() ?>
        </div>
    <?php endif ?>

    <?php echo $this->getItemHtml() ?>

    <div class="ajax-cart-total">
        <?php echo $this->getChildHtml('ajaxcartTotal') ?>
        <div class="clear"></div>
        <span data-link="<?php echo Mage::helper('checkout/cart')->getCartUrl() ?>" class="button checkout-button checkout-color clever-link">
            <?php echo $this->__('View shopping Cart'); ?>
        </span>
    </div>

    <?php echo $this->getChildHtml('ajaxcartFreeshipping') ?>

    <?php echo $this->getChildHtml('crosssell') ?>

    <span href="javascript:;" class="close-popup closeAddToCartResult"></span>
</div>

<span class="close-popup-area closeAddToCartResult"></span>