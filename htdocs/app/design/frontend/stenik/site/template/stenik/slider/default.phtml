
<?php $slides = $this->getSlides(); ?>
<?php if (count($slides)): ?>
    <?php $sliderId = 'slider' . (uniqid(true)) ?>
    <div class="slider-wrapper">
        <div class="owl-carousel banner-slider">
			<?php foreach ($slides as $slide): ?>
				<?php $imageSize = getimagesize(Mage::getBaseDir('media') . DS . $slide->getImage()); ?>
				<?php if ($slide->getLink()): ?>
                    <div class="item">
                        <div class="widget-box">
                            <a href="<?php echo $slide->getLink() ?>" <?php if ($slide->getLinkOpenInNewWindow()): ?> data-rel="blank"<?php endif; ?>>
                                <img <?= $imageSize ? 'height="' . $imageSize[1] . '"' : ''; ?> src="<?php echo $slide->getImageUrl() ?>" alt="banner" />
                            </a>
							<?php if ($slide->getFrontendTitle() || $slide->getFrontendSubtitle()): ?>
                                <div class="widget-info">
                                    <a href="<?php echo $slide->getLink() ?>" class="title"><?php echo $slide->getFrontendTitle() ?></a>
									<?php if ($slide->getShowBuyButton()): ?>
                                        <a href="<?php echo $slide->getLink() ?>" class="button no-background"><?php echo $slide->getFrontendSubtitle() ?></a>
									<?php endif ?>
                                </div>
							<?php endif ?>
                        </div>
                    </div>
				<?php else: ?>
                    <div class="item">
                        <div class="widget-box">
                            <img <?= $imageSize ? 'height="' . $imageSize[1] . '"' : ''; ?> src="<?php echo $slide->getImageUrl() ?>" alt="banner">
							<?php if ($slide->getFrontendTitle() || $slide->getFrontendSubtitle()): ?>
                                <div class="widget-info">
                                    <span class="title"><?php echo $slide->getFrontendTitle() ?></span>
                                </div>
							<?php endif ?>
                        </div>
                    </div>
				<?php endif; ?>
			<?php endforeach ?>
        </div>
    </div>

    <script type="text/javascript">
      jQuery(".banner-slider").owlCarousel({
            loop: true,
            items: 1,
            nav: true,
            navText: false,
            slideSpeed: 500,
            autoplay: true,
            autoplayTimeout: 5000,
            autoplayHoverPause: true,
            addClassActive: true,
            dots: true,
            autoHeight: true
        });
    </script>
<?php endif ?>
