<?php
/** @var PFG_StorePickup_Block_Checkout_Form $_code */
?>

<script>
  (function($) {
    let storeRadio = $('#s_method_prechoose_<?= PFG_StorePickup_Model_Carrier_Store::FULL_METHOD_CODE ?>')
    let standardFieldsWrapper = $('.standart-address-fileds-wrapper')
    let addressLine = $('#billing\\:street1')
    let cityLine = $('#billing\\:city')
    let zipLine = $('#billing\\:postcode')


    function showFormData() {
      standardFieldsWrapper.show()
      addressLine.attr('readonly', true)
      cityLine.attr('readonly', true)
      zipLine.attr('readonly', true)
      addressLine.val("<?php echo Mage::getStoreConfig('carriers/pfg_store_pickup/address_for_pickup'); ?>")
      cityLine.val("<?php echo Mage::getStoreConfig('carriers/pfg_store_pickup/city_for_pickup'); ?>")
      zipLine.val("<?php echo Mage::getStoreConfig('carriers/pfg_store_pickup/zip_code_for_pickup'); ?>")
    }


    /* Preselect at page load but clear the address */
    if (storeRadio.is(':checked')) {
      showFormData()
    }

    stenikOnepageCheckout.addFormDataObserver(
      's_method_prechoose_<?= PFG_StorePickup_Model_Carrier_Store::FULL_METHOD_CODE ?>',
      showFormData,
    )
  })(jQuery)
</script>
