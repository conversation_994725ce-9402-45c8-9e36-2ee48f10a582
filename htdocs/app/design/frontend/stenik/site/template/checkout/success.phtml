<?php
    $_helper = Mage::helper('pfg_checkout');
    $specialMessageEnabled = $_helper->getEnabled();
?>
<div class="text-page">
    <h1><?php echo $this->__('Your order has been received.') ?></h1>
    <?php echo $this->getMessagesBlock()->getGroupedHtml() ?>
    <h2 class="sub-title"><?php echo $this->__('Thank you for your purchase!') ?></h2>


    <?php if ($this->getOrderId()):?>
    <?php if ($this->getCanViewOrder()) :?>
        <p><?php echo $this->__('Your order # is: %s.', sprintf('<a href="%s">%s</a>', $this->escapeHtml($this->getViewOrderUrl()), $this->escapeHtml($this->getOrderId()))) ?></p>
    <?php  else :?>
        <p><?php echo $this->__('Your order # is: %s.', $this->escapeHtml($this->getOrderId())) ?></p>
    <?php endif;?>
        <p><?php echo $this->__('You will receive an order confirmation email with details of your order and a link to track its progress.') ?></p>
    <?php if ($this->getCanViewOrder() && $this->getCanPrintOrder()) :?>
        <p>
            <?php //echo $this->__('Click <a href="%s" onclick="this.target=\'_blank\'">here to print</a> a copy of your order confirmation.', $this->getPrintUrl()) ?>
            <?php echo $this->__('Кликнете <a href="%s" onclick="this.target=\'_blank\'">тук</a>, за да принтирате потвърждението на Вашата поръчка.', $this->getPrintUrl()) ?>
            <?php echo $this->getChildHtml() ?>
        </p>
    <?php endif;?>
    <?php endif;?>

    <?php if ($this->getAgreementRefId()): ?>
        <p><?php echo $this->__('Your billing agreement # is: %s.', sprintf('<a href="%s">%s</a>', $this->escapeHtml($this->getAgreementUrl()), $this->escapeHtml($this->getAgreementRefId())))?></p>
    <?php endif;?>

    <?php if ($profiles = $this->getRecurringProfiles()):?>
    <p><?php echo $this->__('Your recurring payment profiles:'); ?></p>
    <ul class="disc">
    <?php foreach($profiles as $profile):?>
    <?php $profileIdHtml = ($this->getCanViewProfiles() ? sprintf('<a href="%s">%s</a>', $this->escapeHtml($this->getProfileUrl($profile)), $this->escapeHtml($this->getObjectData($profile, 'reference_id'))) : $this->escapeHtml($this->getObjectData($profile, 'reference_id')));?>
        <li><?php echo $this->__('Payment profile # %s: "%s".', $profileIdHtml, $this->escapeHtml($this->getObjectData($profile, 'schedule_description')))?></li>
    <?php endforeach;?>
    </ul>
    <?php endif;?>

	<?php if($specialMessageEnabled): ?>
        <div class="checkout-special-message">
            <span><?= $_helper->getMessage(); ?></span>
        </div>
	<?php endif; ?>

    <div class="buttons-set">
        <button type="button" class="button" title="<?php echo $this->__('Continue Shopping') ?>" onclick="window.location='<?php echo $this->getUrl() ?>'"><?php echo $this->__('Continue Shopping') ?></button>
    </div>
</div>