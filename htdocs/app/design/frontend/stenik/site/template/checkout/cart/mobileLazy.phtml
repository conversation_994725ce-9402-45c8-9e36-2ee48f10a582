<?php /** @var PFG_Lazyblocks_Block_Html_Cart $this */ ?>
<?php $itemCount = count($this->getItems()); ?>
<a aria-label="<?= $this->__('Mobile cart');?>" class="responsive-cart<?php if($itemCount > 0): ?> has-items<?php endif ?>" href="<?= Mage::getUrl('checkout/cart') ?>">
  <svg aria-hidden="true" class="icon-svg shopping-cart"><use xlink:href="<?= $this->getBaseUrl().'svg/svg-sprite.svg' . '#shopping-cart' ?>"></use></svg>
  <span class="notification"><?= $this->getSummaryCount() ; ?></span>
</a>
