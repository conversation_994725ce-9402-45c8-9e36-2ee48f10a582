<?php
/**
 * @package     Stenik_Template
 * <AUTHOR> <<EMAIL>>
 */
?>
<?php
    $_item = $this->getItem();
    $_product = Mage::getModel('catalog/product')->load($_item->getProductId());
    $isVisibleProduct = $_item->getProduct()->isVisibleInSiteVisibility();
    $canApplyMsrp = Mage::helper('catalog')->canApplyMsrp($_item->getProduct(), Mage_Catalog_Model_Product_Attribute_Source_Msrp_Type::TYPE_BEFORE_ORDER_CONFIRM);
?>

<div class="cart-row item">

    <div class="cell col1">
        <?php if ($this->hasProductUrl()): ?>
            <a href="<?php echo $this->getProductUrl() ?>" title="<?php echo $this->escapeHtml($this->getProductName()) ?>" class="cart-img-wrapper">
                <img
                    src="<?php echo $this->getProductThumbnail()->resize(120,120) ?>"
                    srcset="<?php echo $this->getProductThumbnail()->resize(285,285) ?> 2x"
                    alt="<?php echo $this->escapeHtml($this->getProductName()) ?>"
                >
            </a>
        <?php else: ?>
            <span class="cart-img-wrapper">
                <img
                    src="<?php echo $this->getProductThumbnail()->resize(120,120) ?>"
                    srcset="<?php echo $this->getProductThumbnail()->resize(285,285) ?> 2x"
                    alt="<?php echo $this->escapeHtml($this->getProductName()) ?>"
                >
            </span>
        <?php endif ?>

        <div class="cart-info">
            <?php if ($this->hasProductUrl()):?>
                <a class="item-title" href="<?php echo $this->getProductUrl() ?>"><?php echo $this->escapeHtml($this->getProductName()) ?></a>
            <?php else: ?>
                <span class="item-title"><?php echo $this->escapeHtml($this->getProductName()) ?></span>
            <?php endif ?>

            <?php if ($_options = $this->getOptionList()):?>
                <span class="attributes">
                    <?php foreach ($_options as $_option) : ?>
                        <?php $_formatedOptionValue = $this->getFormatedOptionValue($_option) ?>
                        <?php echo $this->escapeHtml($_option['label']) ?>: <?php echo $_formatedOptionValue['value'] ?>
                    <?php endforeach; ?>
                </span>
            <?php endif;?>
            <?php if ($messages = $this->getMessages()): ?>
                <?php foreach ($messages as $message): ?>
                    <p class="item-msg <?php echo $message['type'] ?>">* <?php echo $this->escapeHtml($message['text']) ?></p>
                <?php endforeach; ?>
                <?php $addInfoBlock = $this->getProductAdditionalInformationBlock(); ?>
                <?php if ($addInfoBlock): ?>
                    <?php echo $addInfoBlock->setItem($_item)->toHtml() ?>
                <?php endif;?>
            <?php endif; ?>
        </div>

    </div>

    <div class="cell col2">
        <div class="price-box">
            <?php echo Mage::helper('stenik_themebase/checkout_cart')->getItemPriceRenderer($_item)->toHtml(); ?>
        </div>
    </div>

    <div class="cell col3">
        <div class="spinner-box">
            <input type="text" name="cart[<?php echo $_item->getId() ?>][qty]" class="amount" value="<?php echo $this->getQty() ?>" id="spinner-<?php echo $_item->getId() ?>" title="<?php echo Mage::helper('checkout')->__('Qty') ?>" />
            <span class="loader" id="row_loader<?php echo $_item->getId() ?>" style="display: none;"></span>
        </div>
    </div>

    <div class="cell col4">
        <div class="price-box">
            <span class="regular-price">
                <?php if (($this->helper('tax')->displayCartPriceExclTax() || $this->helper('tax')->displayCartBothPrices()) && !$_item->getNoSubtotal()): ?>
                    <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(1, 4), 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                        <span class="cart-tax-total" onclick="taxToggle('esubtotal-item-tax-details<?php echo $_item->getId(); ?>', this, 'cart-tax-total-expanded');">
                    <?php else: ?>
                        <span class="cart-price">
                    <?php endif; ?>

                        <?php if ($canApplyMsrp): ?>
                            <span class="cart-msrp-subtotal">--</span>
                        <?php else: ?>
                            <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                                <?php echo $this->helper('checkout')->formatPrice($_item->getRowTotal()+$_item->getWeeeTaxAppliedRowAmount()+$_item->getWeeeTaxRowDisposition()); ?>
                            <?php else: ?>
                                <?php echo $this->helper('checkout')->formatPrice($_item->getRowTotal()) ?>
                            <?php endif; ?>
                        <?php endif; ?>

                    </span>
                    <?php if (Mage::helper('weee')->getApplied($_item)): ?>

                        <div class="cart-tax-info" id="esubtotal-item-tax-details<?php echo $_item->getId(); ?>" style="display:none;">
                            <?php if (Mage::helper('weee')->typeOfDisplay($_item, 1, 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                    <span class="weee"><?php echo $tax['title']; ?>: <?php echo Mage::helper('checkout')->formatPrice($tax['row_amount'],true,true); ?></span>
                                <?php endforeach; ?>
                            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                    <span class="weee"><?php echo $tax['title']; ?>: <?php echo Mage::helper('checkout')->formatPrice($tax['row_amount'],true,true); ?></span>
                                <?php endforeach; ?>
                            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 4, 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                    <span class="weee"><?php echo $tax['title']; ?>: <?php echo Mage::helper('checkout')->formatPrice($tax['row_amount'],true,true); ?></span>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>

                        <?php if (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                            <div class="cart-tax-total" onclick="taxToggle('esubtotal-item-tax-details<?php echo $_item->getId(); ?>', this, 'cart-tax-total-expanded');">
                                <span class="weee"><?php echo Mage::helper('weee')->__('Total'); ?>: <?php echo $this->helper('checkout')->formatPrice($_item->getRowTotal()+$_item->getWeeeTaxAppliedRowAmount()+$_item->getWeeeTaxRowDisposition()); ?></span>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                <?php endif; ?>
                <?php if (($this->helper('tax')->displayCartPriceInclTax() || $this->helper('tax')->displayCartBothPrices()) && !$_item->getNoSubtotal()): ?>
                    <?php $_incl = $this->helper('checkout')->getSubtotalInclTax($_item); ?>
                    <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(1, 4), 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                        <span class="cart-tax-total" onclick="taxToggle('subtotal-item-tax-details<?php echo $_item->getId(); ?>', this, 'cart-tax-total-expanded');">
                    <?php else: ?>
                        <span class="cart-price">
                    <?php endif; ?>

                        <?php if ($canApplyMsrp): ?>
                            <span class="cart-msrp-subtotal">--</span>
                        <?php else: ?>
                            <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                                <?php echo $this->helper('checkout')->formatPrice($_incl + Mage::helper('weee')->getRowWeeeTaxInclTax($_item)); ?>
                            <?php else: ?>
                                <?php echo $this->helper('checkout')->formatPrice($_incl-$_item->getWeeeTaxRowDisposition()) ?>
                            <?php endif; ?>
                        <?php endif; ?>

                    </span>


                    <?php if (Mage::helper('weee')->getApplied($_item)): ?>

                        <div class="cart-tax-info" id="subtotal-item-tax-details<?php echo $_item->getId(); ?>" style="display:none;">
                            <?php if (Mage::helper('weee')->typeOfDisplay($_item, 1, 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                    <span class="weee"><?php echo $tax['title']; ?>: <?php echo Mage::helper('checkout')->formatPrice($tax['row_amount_incl_tax'],true,true); ?></span>
                                <?php endforeach; ?>
                            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                    <span class="weee"><?php echo $tax['title']; ?>: <?php echo Mage::helper('checkout')->formatPrice($tax['row_amount_incl_tax'],true,true); ?></span>
                                <?php endforeach; ?>
                            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 4, 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                    <span class="weee"><?php echo $tax['title']; ?>: <?php echo Mage::helper('checkout')->formatPrice($tax['row_amount_incl_tax'],true,true); ?></span>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>

                        <?php if (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales') && $_item->getWeeeTaxAppliedAmount()): ?>
                            <div class="cart-tax-total" onclick="taxToggle('subtotal-item-tax-details<?php echo $_item->getId(); ?>', this, 'cart-tax-total-expanded');">
                                <span class="weee"><?php echo Mage::helper('weee')->__('Total incl. tax'); ?>: <?php echo $this->helper('checkout')->formatPrice($_incl + Mage::helper('weee')->getRowWeeeTaxInclTax($_item)); ?></span>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                <?php endif; ?>
            </span>
        </div>
    </div>

    <div class="cell col5">
        <span data-link="<?php echo $this->getDeleteUrl() ?>" id="delete_item-<?php echo $_item->getId() ?>" class="item-remove clever-link" title="<?php echo Mage::helper('checkout')->__('Remove') ?>">x</span>
    </div>
</div>