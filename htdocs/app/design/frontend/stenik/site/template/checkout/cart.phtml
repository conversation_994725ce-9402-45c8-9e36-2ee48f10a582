<?php
/**
 * @package     Stenik_Template
 * <AUTHOR> <<EMAIL>>
 * @see         Mage_Checkout_Block_Cart
 */
?>

<div class="row">
    <div class="col-xs-12 col-sm-9 checkout-page-top">
        <h1><?php echo Mage::helper('checkout')->__('Shopping Cart') ?></h1>
        <?php foreach ($this->getMethods('top_methods') as $method): ?>
            <?php if ($methodHtml = $this->getMethodHtml($method)): ?>
                <?php echo $methodHtml; ?>
            <?php endif; ?>
        <?php endforeach; ?>
    </div>
</div>

<?php echo $this->getMessagesBlock()->getGroupedHtml() ?>

<?php if(!$this->hasError()): ?>
    <button class="button btn-proceed-checkout btn-checkout checkout-color has-forword-arrow cart-responsive-btn" title="<?php echo $this->__('Proceed to checkout') ?>" type="button" onclick="window.location='<?php echo $this->getCheckoutUrl() ?>';">
        <?php echo $this->__('Proceed to checkout') ?>
    </button>
<?php endif ?>

<div class="row">
    <div class="col-xs-12 col-sm-9 shopping-cart">

        <?php echo $this->getChildHtml('form_before') ?>

        <form action="<?php echo $this->getUrl('checkout/cart/updatePost') ?>" method="post" id="cart-form">

            <?php echo $this->getBlockHtml('formkey'); ?>

            <div class="shopping-cart-items" id="shopping-cart-table">

                <div class="cart-row header-row">
                    <div class="cell col1"><?php echo Mage::helper('checkout')->__('Product') ?></div>
                    <div class="cell col2"><?php echo Mage::helper('checkout')->__('Single price') ?></div>
                    <div class="cell col3"><?php echo Mage::helper('checkout')->__('Quantity') ?></div>
                    <div class="cell col4"><?php echo Mage::helper('checkout')->__('Sum') ?></div>
                    <div class="cell col5">&nbsp;</div>
                </div>

                <?php foreach($this->getItems() as $_item): ?>
                    <?php echo $this->getItemHtml($_item) ?>
                <?php endforeach ?>

            </div>

            <div class="clearH2"></div>

            <a href="javascript:;" class="continue-shopping" onclick="window.history.back(); return false;"><?php echo $this->__('Continue shopping');?></a>
        </form>
        <?php if (0): ?>
            <?php if (!$this->getIsVirtual()): echo $this->getChildHtml('shipping'); endif; ?>
        <?php endif ?>
        <?php echo $this->getChildHtml('checkout.cart.extra') ?>
    </div>
    <aside class="sidebar col-xs-12 col-sm-3">
        <div class="cart-sidebar">

            <?php echo $this->getChildHtml('coupon') ?>

            <?php if ($this->helper('stenik_sitesettings')->getfreeshipping()): ?>
                <?php
                    $grandTotal = $this->getQuote()->getGrandTotal();
                    $freeShippingAmounts = $this->helper('stenik_sitesettings')->getfreeshipping();
                    $toFreeDelivery = ($this->helper('stenik_sitesettings')->getfreeshipping() - $grandTotal);
                ?>
                <?php if ($toFreeDelivery <= 0): ?>
                    <div class="info-cms-block free-shipping">
                        <svg aria-hidden="true" class="icon-svg delivery">
                            <use href="<?php echo $this->getBaseUrl().'svg/svg-sprite.svg' . '#delivery' ?>"></use>
                        </svg>
                        <?php echo $this->__('<strong>Free shipping</strong>');?>
                    </div>
                <?php else: ?>
                    <div class="info-cms-block">
                        <img src="<?php echo $this->getBaseUrl().'svg/delivery-icon.svg' ?>" alt="<?php echo $this->__('Add items to get free shipping');?>">
                        <?php echo $this->__('Free delivery for amounts over');?>
                        <?php echo $this->helper('checkout')->formatPrice($freeShippingAmounts); ?>
                    </div>
                <?php endif ?>
            <?php endif ?>

            <?php echo $this->getChildHtml('totals'); ?>

            <?php if(!$this->hasError()): ?>
                <button class="button btn-proceed-checkout btn-checkout checkout-color has-forword-arrow" title="<?php echo $this->__('Proceed to checkout') ?>" type="button" onclick="window.location='<?php echo $this->getCheckoutUrl() ?>';">
                    <?php echo $this->__('Proceed to checkout') ?>
                </button>
            <?php endif ?>

            <?php echo $this->getChildHtml('checkout.cart.methods.paypal_express.bottom'); ?>
        </div>
    </aside>
</div>

<?php echo $this->getChildHtml('crosssell') ?>


<script>
    var cartAjaxTimer;
    var cartAjax;

    jQuery(function(){
        initCartContent();
        jQuery('.shopping-cart-items .amount').spinner({min:1, max:50});
    });

    function initCartContent() {
        jQuery('.shopping-cart-items .amount').spinner({
            min: 1,
            stop: function(event, ui) {
                var id = jQuery(this).attr('id');
                id = id.substring('spinner-'.length, id.length);

                clearTimeout(cartAjaxTimer);
                if (cartAjax) { cartAjax.abort(); }

                jQuery('#row_loader'+id).fadeIn();

                cartAjaxTimer = setTimeout(function() { ajaxCart(id) }, 500);
            }
        });

        jQuery('.cell .item-remove').click(function(e){

            var id = jQuery(this).attr('id');
            if (!id)
                return false;

            id = id.substring('delete_item-'.length, id.length); //delete_item-101
            clearTimeout(cartAjaxTimer);
            if (cartAjax) cartAjax.abort();

            jQuery('.cell .loader').stop(true, true).hide();
            jQuery('#row_loader'+id).fadeIn('fast');
            jQuery('.main-content').css({position:'relative'});
            jQuery('.main-content').append('<div id="overlay-checkout-cart" style=" position:absolute; top:0; left:0; width:100%; height:100%; opacity:0.5; background:white; z-index:101;"></div>');

            cartAjaxTimer = setTimeout(function() { ajaxCart(id, true) }, 500);
            e.preventDefault();
            return false;
        });
    }

    function ajaxCart(id, del) {
        if (del) { url = '<?php echo $this->getUrl('checkout/ajax/delete'); ?>'; }
        else { url = '<?php echo $this->getUrl('checkout/ajax/cart'); ?>'; }
        cartAjax = jQuery.ajax({
            type: 'post',
            data: del ? {id:id} : jQuery('#cart-form').serialize() ,
            url: url,
            dataType: 'json',
            success: function(json) {
                if (json['cart']) {
                    loaderIds = new Array();
                    jQuery('.cart-wrapper').find('.loader:visible').each(function(){
                        loaderIds.push(jQuery(this).attr('id'));
                    });
                    jQuery('.cart-wrapper').html(json['cart']);
                    for (var i = loaderIds.length - 1; i >= 0; i--) {
                        jQuery('.cart-wrapper').find('#'+loaderIds[i]).show().fadeOut(300);
                    }
                    jQuery('.shopping-cart-items .amount').spinner({min:1, max:50});
                }
                if (json['minicart']) {
                    jQuery('.mini-cart-wrapper').html(json['minicart']);
                }
                if (json['responsiveminicart']) {
                    jQuery('.responsive-mini-cart-wrapper').html(json['responsiveminicart']);
                }
                jQuery('#overlay-checkout-cart').remove();
                if (typeof PS != 'undefined' && typeof PS._onload != 'undefined') {
                    PS._onload();
                }
            }
        });
    }
</script>
