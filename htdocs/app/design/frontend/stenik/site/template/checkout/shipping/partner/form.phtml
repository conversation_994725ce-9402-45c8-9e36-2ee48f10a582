<?php
/** @var PFG_Shop_Block_Shipping_Partner $this */

$_helper = $this->helper('pfg_shop');
$_htmlId = 'pfg_partner_shipping-form';
$_partnerCollection = $this->getPartnerCollection();
$_citiesCollection = $this->getCityCollection();
?>
<style>
    #pfg_partner_shipping-form {
        float: left;
        width: 100%;
    }

    #pfg_partner_shipping-form #partner-list {
        display: none;
    }

    #pfg_partner_shipping-form #partner-list .skip-option {
        display: none;
    }

    .partners-wrapper {
        padding: 10px 0 10px 0;
        margin: 0 -20px 0 -20px;
    }

    .partners-wrapper .clear:after {
        content: "";
        clear: both;
        display: table;
    }

    .partners-wrapper .gmap-content {
        margin: 0 20px 0 20px;
        width: calc(100% - 40px);
    }

    .partners-wrapper .no-partner-found p {
        color: #ce181e;
        font-weight: bold;
    }

    .select2-container--default .select2-results__option[aria-disabled=true] {
        display: none;
    }
</style>
<div id="<?= $_htmlId; ?>" style="display: none;">
    <div class="partners-wrapper fields">
        <div class="field">
            <select title="<?= $_helper->__('Populated location'); ?>" id="partner-city-select"
                    class="select">
                <option value=""><?= $_helper->__('-- Choose city --'); ?></option>
                <?php foreach ($_citiesCollection as $_city): ?>
                    <option value="<?= $_city['value']; ?>"><?= $_city['label']; ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="field">
            <select title="<?= $_helper->__('Partner list'); ?>" id="partner-list" class="select">
                <option value=""><?= $_helper->__('-- Partner list --'); ?></option>
                <?php foreach ($_partnerCollection as $_partner): ?>
                    <option value="<?= $_partner->getCity(); ?>"
                            data-partner-id="<?= $_partner->getId(); ?>"
                            data-partner-name="<?= $_partner->getName(); ?>"
                            data-partner-address="<?= $_partner->getAddress(); ?>"
                            data-partner-city="<?= $_partner->getCity(); ?>"
                            data-partner-post-code="<?= $_partner->getPostCode(); ?>"
                            data-partner-latitude="<?= $_partner->getLatitude(); ?>"
                            data-partner-longitude="<?= $_partner->getLongitude(); ?>">
                        <?= $_partner->getName(); ?> - <?= $_partner->getAddress(); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="clear"></div>

        <div class="gmap-content" style="display: none">
            <div id="map_container"></div>
        </div>
        <div class="no-partner-found field" style="display: none">
            <p><?= $_helper->__('No found partners in this city') ?></p>
        </div>
    </div>
</div>
<script type="text/javascript"
        src="https://maps.googleapis.com/maps/api/js?v=3&amp;key=<?= Mage::getStoreConfig('pfg_shop/settings/google_maps_key') ?>"
        async defer></script>


<script>
  jQuery(function($) {
    let partnerRadio = $('#s_method_prechoose_pfg_partner_shipping_pfg_partner_shipping')
    let standardFieldsWrapper = $('.standart-address-fileds-wrapper')
    let citySelect = $('#partner-city-select')
    let partnerSelect = $('#partner-list')
    let wrapper = $('#<?= $_htmlId; ?>')
    let addressLine = $('#billing\\:street1')
    let cityLine = $('#billing\\:city')
    let zipLine = $('#billing\\:postcode')
    let errorMessage = $('.no-partner-found')
    let mapContainer = $('.gmap-content')

    function showFormData() {
      citySelect.val(null)
      citySelect.select2()
      partnerSelect.val(null)
      partnerSelect.select2()
      standardFieldsWrapper.hide()
      wrapper.show()
      addressLine.attr('readonly', true).val('')
      cityLine.attr('readonly', true).val('')
      zipLine.attr('readonly', true).val('')
    }

    function hideFormData() {
      citySelect.val(null)
      citySelect.select2()
      partnerSelect.val(null)
      partnerSelect.select2()
      standardFieldsWrapper.hide()
      wrapper.hide()
      addressLine.attr('readonly', true).val('')
      cityLine.attr('readonly', true).val('')
      zipLine.attr('readonly', true).val('')
    }


    /* Initial */
    if (partnerRadio.is(':checked')) {
      showFormData()
    }

    /* On change main radio */

    stenikOnepageCheckout.addFormDataObserver(
      's_method_prechoose_pfg_partner_shipping_pfg_partner_shipping',
      showFormData,
      hideFormData,
    )

    citySelect.select2()

    /* City select */
    citySelect.on('change', function(e) {
      partnerSelect.prop('selected', false)
      partnerSelect.trigger('selected_city_changed')
    })

    /* After city select */
    partnerSelect.on('selected_city_changed', function(e) {
      partnerSelect.val(null).trigger('change')
      let options = $('#partner-list option')

      options.each(function(i, el) {
        let option = $(el)
        option.removeAttr('selected')
        option.removeAttr('disabled')
        option.removeClass('skip-option')
        if (option.data('partner-city') !== citySelect.val()) {
          option.attr('disabled', 'disabled')
          option.addClass('skip-option')
        }
      })
      partnerSelect.select2()

      if ($('#partner-list option.skip-option').length === options.length) {
        partnerSelect.hide()
        errorMessage.show()
        addressLine.val('')
        cityLine.val('')
        zipLine.val('')
      } else {
        partnerSelect.show()
        errorMessage.hide()
      }
    })

    /* After partner select */
    partnerSelect.on('change', function(e) {
      let selectedOption = $(this).find('option:selected')
      let partnerCity = selectedOption.data('partner-city')
      let partnerAddress = selectedOption.data('partner-address')
      let partnerPostCode = selectedOption.data('partner-post-code')
      let partnerLatitude = selectedOption.data('partner-latitude')
      let partnerLongitude = selectedOption.data('partner-longitude')

      addressLine.val(partnerAddress)
      cityLine.val(partnerCity)
      zipLine.val(partnerPostCode)
      window.initMap = initMap

      initMap(partnerLatitude, partnerLongitude)
    })

    function initMap(lat, lng) {
      mapContainer.show()
      let map
      let styles = []
      let markerPosition = new google.maps.LatLng(lat, lng)
      let mapOptions = {
        zoom: 16,
        center: markerPosition,
        mapTypeControlOptions: {
          mapTypeIds: [google.maps.MapTypeId.ROADMAP, 'map_style'],
        },
      }
      let styledMap = new google.maps.StyledMapType(styles, { name: 'Styled Map' })
      map = new google.maps.Map(document.getElementById('map_container'), mapOptions)

      let iconUrl = "<?php echo $this->getBaseUrl() . 'svg/map-pin.png' ?>",
        iconSize = new google.maps.Size(26, 40)

      let marker = new google.maps.Marker({
        position: markerPosition,
        map: map,
        animation: google.maps.Animation.DROP,
        icon: {
          url: iconUrl,
          scaledSize: iconSize,
          anchor: new google.maps.Point(26, 40),
        },
      })
      map.mapTypes.set('map_style', styledMap)
      map.setMapTypeId('map_style')
    }
  })
</script>
