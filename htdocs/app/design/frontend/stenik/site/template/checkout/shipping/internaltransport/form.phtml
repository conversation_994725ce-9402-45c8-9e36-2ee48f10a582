<?php
/** @var Carco_InternalTransport_Block_Checkout_Form_Shipping $this */
$test = 2;
$radioButtonId = 's_method_prechoose_' . Carco_InternalTransport_Model_Carrier_Internal::FULL_METHOD_CODE;
?>
<style>
    .carco-internaltransport-form {
        float: left;
        width: 100%;
    }

    .carco-internaltransport-form:after {
        content: "";
        clear: both;
        display: table;
    }
</style>
<div id='form-internal-transport'
     class="carco-internaltransport-form"
     style='display: none'
>
    <p><strong><span style="color: #ce181e;"><?= $this->getNote() ?></span></strong></p>
</div>

<script>
  (function($) {
    const radioOption = $('#<?= $radioButtonId ?>')
    const wrapper = $('#form-internal-transport')
    const standardFieldsWrapper = $('.standart-address-fileds-wrapper')
    const addressLine = $('#billing\\:street1')
    const cityLine = $('#billing\\:city')
    const zipLine = $('#billing\\:postcode')


    function showFormData() {
      wrapper.show()
      standardFieldsWrapper.show()
      addressLine.attr('readonly', false).val('')
      addressLine.addClass('required-entry').removeClass('validation-passed')
      cityLine.attr('readonly', true).val('София')
      zipLine.attr('readonly', true).val('1000')
    }

    function hideFormData() {
      wrapper.hide()
      standardFieldsWrapper.hide()
      addressLine.addClass('validation-passed').removeClass('required-entry')
      addressLine.attr('readonly', true).val('')
      cityLine.attr('readonly', true).val('')
      zipLine.attr('readonly', true).val('')
    }

    if (radioOption.is(':checked')) {
      showFormData()
    }

    stenikOnepageCheckout.addFormDataObserver(
      '<?= $radioButtonId ?>',
      showFormData,
      hideFormData,
    )
  })(jQuery)
</script>
