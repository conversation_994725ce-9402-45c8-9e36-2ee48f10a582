<?xml version="1.0"?>

<layout version="0.1.0">
    <!-- Everywhere -->
    <default>
        <reference name="head">
            <action method="addJs" ifconfig="mailchimp/ecommerce/active">
                <script>ebizmarts/mailchimp/campaignCatcher.js</script><!-- remove async param -->
            </action>
            <action method="addJs" ifconfig="mailchimp/emailcatcher/popup_general">
                <script>prototype/window.js</script>
            </action>
            <action method="addJs" ifconfig="mailchimp/emailcatcher/popup_general">
                <script>scriptaculous/scriptaculous.js</script>
            </action>
            <action method="addItem" ifconfig="mailchimp/emailcatcher/popup_general">
                <type>js_css</type>
                <script>prototype/windows/themes/default.css</script>
            </action>
            <action method="addItem" ifconfig="mailchimp/emailcatcher/popup_general">
                <type>js_css</type>
                <script>ebizmarts/mailchimp/popup.css</script>
            </action>
            <block type="core/text" name="addMCJs">
                <action method="setText">
                    <text helper="mailchimp/getMCJs"></text>
                </action>
            </block>
        </reference>
        <reference name="content">
            <block type="mailchimp/popup_emailcatcher" name="emailcatcher"
                   template="ebizmarts/mailchimp/popup/emailcatcher.phtml"/>
        </reference>
    </default>
    <!-- Everywhere -->

    <!-- Customer Account -->
    <newsletter_manage_index>
        <reference name="customer_newsletter">
            <block name="customer.form.newsletter.extra"
                   type="mailchimp/customer_newsletter_index"
                   template="ebizmarts/mailchimp/customer/newsletter/index.phtml">
                <block type="mailchimp/group_type" name="mailchimp.group.type"/>
            </block>
        </reference>
    </newsletter_manage_index>
    <!-- Customer Account -->

    <!-- Checkout -->
    <checkout_onepage_review>
        <reference name="checkout.onepage.review.info.items.after">
            <block type="mailchimp/checkout_subscribe" name="mailchimp.subscribe"
                   template="ebizmarts/mailchimp/checkout/subscribe.phtml"/>
        </reference>
    </checkout_onepage_review>
    <checkout_onepage_success>
        <reference name="content">
            <block type="mailchimp/checkout_success_groups" name="mailchimp.checkout.success"
                   template="ebizmarts/mailchimp/checkout/success/groups.phtml">
                <block type="mailchimp/group_type" name="mailchimp.group.type"/>
            </block>
        </reference>
    </checkout_onepage_success>
    <!-- Checkout -->
</layout>
