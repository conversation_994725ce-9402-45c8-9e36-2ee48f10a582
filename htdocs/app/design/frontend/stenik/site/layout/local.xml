<?xml version="1.0"?>

<layout version="0.1.0">
    <required_on_all_pages>
        <reference name="head">
            <action method="massRemoveItems"><type>js</type></action>
            <action method="massRemoveItems"><type>skin_js</type></action>
            <action method="massRemoveItems"><type>js_css</type></action>
            <action method="massRemoveItems"><type>skin_css</type></action>
            <!-- JS REQUIRED ON EVERY PAGE -->
            <action method="addJs"><js>prototype/prototype.js</js><params>js1</params></action>
            <action method="addJs"><js>prototype/validation.min.js</js><params>js1</params></action>
            <action method="addJs"><js>prototype/effects.js</js><param>js1</param></action>
            <action method="addJs"><js>varien/form.min.js</js><params>js1</params></action>
            <action method="addJs"><js>mage/translate.js</js><params>js1</params></action>
            <action method="addJs"><js>mage/cookies.js</js><params>js1</params></action>
            <action method="addJs"><js>jquery/jquery-3.5.1.min.js</js><params>js1</params></action>
            <action method="addJs"><js>stenik/fbpixels/script.js</js><params>js1</params></action>
            <action method="addJs"><js>pfg/sw/base.js</js><params>js1</params></action>
            <action method="addSkinJs"><js>js/lazysizes.min.js</js><params>js1</params></action>
            <action method="addSkinJs"><js>js/ls.include.js</js><params>js1</params></action>
            <action method="addSkinJs"><js>js/stenik.dropdown.js</js><params>js1</params></action>
            <action method="addSkinJs"><js>js/stenik.js</js><params>js1</params></action>
            <action method="addSkinJs"><js>js/select2/select2.full.min.js</js><params>js1</params></action>
            <action method="addSkinJs"><js>js/select2/select2.addon.js</js><params>js1</params></action>
<!--            <action method="addJs"><js>jquery/jquery.qtip.min.js</js><params>js1</params></action>-->
            <action method="addJs"><js>jquery/jquery.jscrollpane.min.js</js><params>js1</params></action>
            <action method="addJs"><js>jquery/owl.carousel.min.js</js><params>js1</params></action>
<!--            <action method="addJs"><js>jquery/jquery-ui-spinner.min.js</js><params>js1</params></action>-->
<!--            <action method="addJs"><js>jquery/svg4everybody.min.js</js><params>js1</params></action>-->
<!--            <action method="addJs" ifconfig="mailchimp/ecommerce/active"><js>ebizmarts/mailchimp/campaignCatcher.js</js><params>js1</params></action>-->
            <action method="addSkinJs"><js>js/bubblelayer.min.js</js><params>js1</params></action>
            <!-- CSS REQUIRED ON EVERY PAGE -->
            <action method="addCss"><css>css/bootstrap.min.css</css></action>
            <action method="addCss"><css>css/style.css</css></action>
            <action method="addCss"><css>valdecode/cookielaw/css/cookielaw.css</css></action>
            <action method="addCss"><css>css/jquery.jscrollpane.css</css></action>
            <action method="addCss"><css>css/owl.carousel.min.css</css></action>
            <action method="addCss"><css>css/slick.css</css></action>
            <action method="addCss"><css>css/select2.refactored.css</css></action>
            <action method="addSkinCss"><css>css/stenik_ajaxaddtocart/ajaxaddtocart.css</css></action>

            <block type="core/template" name="ogmeta" template="carco/seo/og_meta.phtml" />
        </reference>
    </required_on_all_pages>

	<default>
        <update handle="required_on_all_pages" />

        <reference name="header">
            <block type="directory/currency" name="currency" template="directory/currency.phtml"/>
            <block type="cms/block" name="cms_block.header_top_menu">
                <action method="setBlockId"><identifier>header_top_menu</identifier></action>
            </block>
            <block type="core/template" name="header_customer" template="page/html/header-customer.phtml"/>
            <block type="page/html_wrapper" name="mini_cart_wrapper" as="header_cart">
                <action method="setElementClass"><value>mini-cart-wrapper</value></action>
                <block type="checkout/cart_sidebar" name="mini_cart" template="checkout/cart/header.phtml" />
            </block>
            <block type="stenik_base/html_topmenu" name="stenik.catalog.topnav" template="page/html/topmenu.phtml">
                <block type="core/template" name="catalog.topnav.renderer" template="page/html/sidemenu/renderer.phtml"/>
            </block>
			<remove name="top.menu" />
        </reference>

		<reference name="root">
			<block type="core/text_list" name="categories_wrapper" as="categories_wrapper">
				<label>Categories Wrapper</label>
			</block>
		</reference>

        <reference name="categories_wrapper" >
            <block type="core/template" name="footer_categories" template="lazy-load/catalog/categories-loader.phtml" />
        </reference >

        <reference name="footer">
			<block type="cms/block" name="cms_block.footer_col1">
                <action method="setBlockId"><identifier>footer_col1</identifier></action>
            </block>
            <block type="cms/block" name="cms_block.footer_col2">
                <action method="setBlockId"><identifier>footer_col2</identifier></action>
            </block>
            <block type="cms/block" name="cms_block.footer_col3">
                <action method="setBlockId"><identifier>footer_col3</identifier></action>
            </block>
            <block type="newsletter/subscribe" name="newsletter" template="newsletter/subscribe.phtml"/>
            <block type="cms/block" name="cms_block.footer_col4">
                <action method="setBlockId"><identifier>footer_col4</identifier></action>
            </block>
            <block type="cms/block" name="cms_block.footer_payments_text">
                <action method="setBlockId"><identifier>footer_payments_text</identifier></action>
            </block>
            <block type="cms/block" name="cms_block.footer_payments">
                <action method="setBlockId"><identifier>footer_payments</identifier></action>
            </block>
        </reference>

        <reference name="before_body_end">
            <block type="page/html_header" name="responsive-header" template="page/html/responsive-header.phtml">
                <block type="core/template" name="top.search.responsive" template="catalogsearch/form.mini.responsive.phtml"/>

                <block type="cms/block" name="cms_block.header_top_menu">
                    <action method="setBlockId"><identifier>header_top_menu</identifier></action>
                </block>
                <action method="insert"><name>store_language</name></action>
                <block type="core/template" name="header_customer_responsive" template="page/html/responsive-header-customer.phtml"/>
                <block type="page/html_wrapper" name="responsive_mini_cart_wrapper" as="responsive_header_cart">
                    <action method="setElementId"><value>responsive-mini-cart-wrapper</value></action>
                    <action method="setElementClass"><value>responsive-mini-cart-wrapper</value></action>
                    <block type="checkout/cart_sidebar" name="responsive_mini_cart" template="checkout/cart/responsive-header.phtml"/>
                </block>
            </block>
        </reference>

        <block type="core/template" name="product.labels" template="catalog/product/labels.phtml"/>

        <remove name="left.newsletter"/>
        <remove name="left.permanent.callout"/>
        <remove name="right.permanent.callout"/>
        <remove name="product.clone_prices"/>
        <remove name="cart_sidebar"/>
        <remove name="sale.reorder.sidebar"/>
        <remove name="catalog.product.related"/>
        <remove name="right.reports.product.viewed"/>
        <remove name="right.reports.product.compared"/>
        <remove name="right.poll"/>
        <remove name="tags_popular"/>
        <remove name="paypal.partner.right.logo"/>
        <remove name="sale.reorder.sidebar"/>
        <remove name="customer_account_dashboard_info1"/>
        <remove name="product_review_list.count"/>
    </default>

    <cms_index_index>
        <reference name="head">
            <action method="addJs"><js>pfg/sw/home.js</js><params>js1</params></action>
        </reference>
    </cms_index_index>

    <catalog_product_view>
        <reference name="head">
<!--            <action method="addJs"><js>varien/product.min.js</js><params>js2</params></action>-->
<!--            <action method="addJs"><js>varien/product_options.min.js</js><params>js2</params></action>-->
            <action method="addJs"><js>jquery/slick.min.js</js><params>js2</params></action>
            <action method="addJs"><js>jquery/colorbox.min.js</js><params>js2</params></action>
            <action method="addSkinJs"><js>js/plumrocket/cartreservation/cart.js</js><params>js2</params></action>
            <action method="addJs"><js>plumrocket/countdown-1.5.1/jquery.countdown.min.js</js><params>js2</params></action>
            <action method="addSkinCss"><css>css/colorbox/colorbox.css</css></action>

            <action method="removeItem"><type>js</type><name>varien/product.js</name></action>
            <action method="removeItem"><type>js</type><name>varien/product_options.js</name></action>
            <action method="removeItem"><type>js</type><name>varien/configurable.js</name></action>
            <action method="removeItem"><type>js</type><name>calendar/calendar.js</name></action>
            <action method="removeItem"><type>js</type><name>calendar/calendar-setup.js</name></action>
        </reference>

        <reference name="categories_wrapper" >
            <remove name="footer_categories" />
            <block type="carco_catalog/categories" name="footer_categories_loaded" template="carco/categories.phtml" />
        </reference >
    </catalog_product_view>

    <pfg_add_category_js>
        <reference name="head">
            <action method="addJs"><js>varien/js.min.js</js><params>js2</params></action>
            <action method="addJs"><js>plumrocket/countdown-1.5.1/jquery.countdown.min.js</js><params>js2</params></action>
<!--            <action method="addJs"><js>scriptaculous/builder.js</js><params>js2</params></action>-->
            <action method="addJs"><js>scriptaculous/effects.min.js</js><params>js2</params></action>
<!--            <action method="addJs"><js>scriptaculous/dragdrop.js</js><params>js2</params></action>-->
            <action method="addJs"><js>scriptaculous/controls.min.js</js><params>js2</params></action>
            <action method="addJs"><js>scriptaculous/slider.min.js</js><params>js2</params></action>

            <action method="removeItem"><type>js</type><name>jquery/owl.carousel.min.js</name></action>
            <action method="removeItem"><type>skin_css</type><name>css/owl.carousel.min.css</name></action>
            <action method="removeItem"><type>skin_js</type><name>js/select2/select2.full.min.js</name></action>
            <action method="removeItem"><type>skin_js</type><name>js/select2/select2.addon.js</name></action>
        </reference>
    </pfg_add_category_js>

    <catalog_category_default>
        <update handle="pfg_add_category_js" />
    </catalog_category_default>

    <catalog_category_layered>
        <update handle="pfg_add_category_js" />
    </catalog_category_layered>

    <checkout_onepage_index>
        <update handle="required_on_all_pages" />

        <reference name="head">
            <action method="addSkinJs"><js>js/select2/select2.full.min.js</js><param>js1</param></action>
            <action method="addSkinJs"><js>js/select2/select2.addon.js</js><param>js1</param></action>
            <action method="addSkinJs"><js>js/select2/i18n/bg.js</js>><param>js1</param></action>
            <action method="addSkinJs"><js>js/stenik.tabs.js</js><group>js1</group></action>
            <action method="addJs"><js>varien/js.min.js</js><params>js1</params></action>
            <action method="addJs"><js>jquery/colorbox.min.js</js><params>js1</params></action>
            <action method="addSkinCss"><css>css/select2.min.css</css></action>
            <action method="addSkinCss"><css>css/colorbox/colorbox.css</css></action>
            <action method="removeItem"><type>js</type><name>mage/directpost.js</name></action>
        </reference>
    </checkout_onepage_index>

    <checkout_cart_index>
        <reference name="head">
            <action method="addJs"><js>jquery/jquery-ui-spinner.min.js</js><params>js1</params></action>
        </reference>
    </checkout_cart_index>

    <customer_account_create>
        <reference name="head">
            <action method="addJs"><js>jquery/colorbox.min.js</js><params>js1</params></action>
            <action method="addSkinCss"><css>css/colorbox/colorbox.css</css></action>
            <action method="removeItem"><type>js</type><name>mage/captcha.js</name></action>
        </reference>
    </customer_account_create>

    <contacts_index_index>
        <reference name="head">
            <action method="addJs"><js>jquery/colorbox.min.js</js><params>js1</params></action>
            <action method="addSkinCss"><css>css/colorbox/colorbox.css</css></action>
        </reference>
    </contacts_index_index>

    <cms_index_index>
		<remove name="page_content_heading"/>

		<reference name="root">
            <action method="setTemplate"><template>page/homepage.phtml</template></action>
        </reference>
        <reference name="content">
            <block type="core/template" name="homepage" template="cms/homepage.phtml">
                <block type="cms/block" name="cms_block.homepage_top_image">
                    <action method="setBlockId"><identifier>homepage_top_image</identifier></action>
                </block>
                <block type="stenik_base/html_topmenu" name="stenik.catalog.topnav" template="page/html/topmenu.phtml">
                    <block type="core/template" name="catalog.topnav.renderer" template="page/html/sidemenu/renderer.phtml"/>
                </block>

                <block type="core/template" name="stenik_quickfilter.homepage.quick_filter" as="quick_filter" template="cms/quick-filter.phtml">
                    <block type="stenik_quickfilter/form" name="stenik_quickfilter.quick_filter.search_autoparts" as="search_autoparts" template="stenik/quickfilter/form.phtml">
                        <action method="setSkipOptionsLoading"><flag>1</flag></action>
                        <action method="setTitle" translate="title"><title>Search for second hand auto parts</title></action>
                        <action method="setCategoryId"><category>3</category></action><!-- Части (ID: 3) -->
                        <action method="addAttributeFilter">
                            <filters>
                                <part_vehicle_maker>part_vehicle_maker</part_vehicle_maker>
                                <!--<part_vehicle_model>part_vehicle_model</part_vehicle_model>-->
                                <part_vehicle_model_short>part_vehicle_model_short</part_vehicle_model_short>
                                <part_vehicle>part_vehicle</part_vehicle>
                            </filters>
                        </action>
                        <action method="setLimitAttributeOptionsByCategory"><flag>1</flag></action>
                    </block>

                    <block type="cms/block" name="suggestion.box" as="suggestion-box">
                        <action method="setBlockId"><identifier>suggestion-box</identifier></action>
                    </block>

                </block>

				<block type="core/template" name="page_content_heading_below" template="cms/content_headingHome.phtml"/>

                <block type="stenik_slider/slider" name="homepage_slider" template="stenik/slider/default.phtml">
                    <action method="setSliderKey"><key>homepage</key></action>
                </block>
                <block type="cms/block" name="cms_block.homepage_top_widget">
                    <action method="setBlockId"><identifier>homepage_top_widget</identifier></action>
                </block>
                <block type="cms/block" name="cms_block.homepage_middle_widget">
                    <action method="setBlockId"><identifier>homepage_middle_widget</identifier></action>
                </block>
                <block type="cms/block" name="cms_block.homepage_bottom_widget">
                    <action method="setBlockId"><identifier>homepage_bottom_widget</identifier></action>
                </block>
                <block type="cms/block" name="cms_block.homepage_products">
                    <action method="setBlockId"><identifier>homepage_products</identifier></action>
                </block>
                <block type="cms/block" name="cms_block.homepage_text_block">
                    <action method="setBlockId"><identifier>homepage_text_block</identifier></action>
                </block>
            </block>
        </reference>
        <reference name="cms_block.homepage_top_widget">
            <action method="setCacheLifetime" />
        </reference>
        <reference name="cms_block.homepage_middle_widget">
            <action method="setCacheLifetime" />
        </reference>
        <reference name="cms_block.homepage_bottom_widget">
            <action method="setCacheLifetime" />
        </reference>
        <reference name="cms_block.homepage_products">
            <action method="setCacheLifetime" />
        </reference>

        <remove name="cms.wrapper" />
        <block type="cms/page" name="cms_page"/>
		<remove name="footer_categories" />
	</cms_index_index>

    <catalog_category_default>
        <reference name="root">
            <action method="setTemplate">
                <template>page/2columns-left.phtml</template>
            </action>

            <block type="carco_catalog/category_quickFilter" name="category_quick_filter"/>
        </reference>
        <reference name="left">
            <block type="core/template" name="catalog.left.wrapper" before="-" template="catalog/category/left-wrapper.phtml">
                <action method="insert"><name>catalog.leftnav</name></action>
            </block>
            <action method="unsetChild"><name>catalog.leftnav</name></action>
        </reference>
        <reference name="before_body_end">
            <block type="core/template" name="catalog.category.js" template="catalog/category/js.phtml"/>
        </reference>
    </catalog_category_default>

    <catalog_category_layered>
        <reference name="root">
            <action method="setTemplate">
                <template>page/2columns-left.phtml</template>
            </action>

            <block type="carco_catalog/category_quickFilter" name="category_quick_filter"/>
        </reference>
        <reference name="left">
            <block type="core/template" name="catalog.left.wrapper" before="-" template="catalog/category/left-wrapper.phtml">
                <action method="insert"><name>catalog.leftnav</name></action>
                <block type="catalog/navigation" name="catalog.leftnav.categories" before="-" template="catalog/navigation/left.phtml"/>
            </block>
            <action method="unsetChild"><name>catalog.leftnav</name></action>
        </reference>
        <reference name="before_body_end">
            <block type="core/template" name="catalog.category.js" template="catalog/category/js.phtml"/>
            <block type="stenik_site/catalog_category_prepareBreadcrumbs" name="stenik_site.catalog.category.prepare_breadcrumbs"/>
            <!-- <block type="stenik_site/catalog_category_prepareLinks2" name="stenik_site.catalog.category.prepare_links2"/> -->
        </reference>
    </catalog_category_layered>

    <catalogsearch_result_index>
        <reference name="root">
            <action method="setTemplate"><template>page/2columns-left.phtml</template></action>
        </reference>
        <reference name="left">
            <block type="core/template" name="catalog.left.wrapper" before="-" template="catalog/category/left-wrapper.phtml">
                <action method="insert"><name>catalogsearch.leftnav</name></action>
            </block>
            <action method="unsetChild"><name>catalogsearch.leftnav</name></action>
        </reference>
    </catalogsearch_result_index>

    <catalogsearch_advanced_index>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
    </catalogsearch_advanced_index>

    <catalogsearch_advanced_result>
        <reference name="root">
            <action method="setTemplate"><template>page/2columns-left.phtml</template></action>
        </reference>
        <reference name="left">
            <block type="core/template" name="catalog.left.wrapper" before="-" template="catalog/category/left-wrapper.phtml">
                <action method="insert"><name>catalogsearch.leftnav</name></action>
            </block>
            <action method="unsetChild"><name>catalogsearch.leftnav</name></action>
        </reference>
    </catalogsearch_advanced_result>

    <catalog_product_compare_index>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>

        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="checkout">
                <crumbName>compare</crumbName>
                <crumbInfo><label>Compare Products</label><title>Compare Products</title></crumbInfo>
            </action>
        </reference>
    </catalog_product_compare_index>

    <lazyblock_product_error>
        <reference name="root">
            <action method="setTemplate"><template>page/empty.phtml</template></action>
        </reference>
        <reference name="content">
            <block type="stenik_form/form" name="stenik_form.errorsignal_form" template="stenik/form/form.phtml">
                <action method="addConfigData">
                    <config>
                        <trans_email_id>2</trans_email_id>
                        <email_sender>general</email_sender>
                        <email_recipient>custom1</email_recipient>
                        <entity_id_product>
                            <key>product</key>
                            <registry>product</registry>
                            <class>catalog/product</class>
                        </entity_id_product>
                    </config>
                </action>
                <action method="addField" translate="config.label config.title config.placeholder">
                    <id>name</id>
                    <type>hidden</type>
                    <config>
                        <name>name</name>
                        <value>Carco</value>
                    </config>
                </action>
                <action method="addField" translate="config.label config.title config.placeholder">
                    <id>wrong_image</id>
                    <type>checkbox</type>
                    <config>
                        <name>wrong_image</name>
                        <label>Wrong image/images</label>
                        <value>Да</value>
                        <class></class>
                    </config>
                </action>
                <action method="addField" translate="config.label config.title config.placeholder">
                    <id>wrong_title</id>
                    <type>checkbox</type>
                    <config>
                        <name>wrong_title</name>
                        <label>Wrong title</label>
                        <value>Да</value>
                        <class></class>
                    </config>
                </action>
                <action method="addField" translate="config.label config.title config.placeholder">
                    <id>wrong_category</id>
                    <type>checkbox</type>
                    <config>
                        <name>wrong_category</name>
                        <label>Wrong category</label>
                        <value>Да</value>
                        <class></class>
                    </config>
                </action>
                <action method="addField" translate="config.label config.title config.placeholder">
                    <id>comment</id>
                    <type>textarea</type>
                    <config>
                        <name>comment</name>
                        <label>Comment</label>
                        <class>comment</class>
                        <required>1</required>
                    </config>
                </action>
                <action method="addField" translate="config.label config.title config.placeholder">
                    <id>captcha</id>
                    <type>captcha</type>
                    <config/>
                    <after/>
                </action>
                <action method="addField" translate="config.label config.title config.placeholder">
                    <id>submit</id>
                    <type>button</type>
                    <config>
                        <label>Send</label>
                        <class>button</class>
                        <onclick>ga('send', 'event', 'ProductDetailPage', 'Click', 'ErrorSignal');</onclick>
                    </config>
                </action>
            </block>
        </reference>
    </lazyblock_product_error>

    <catalog_product_view>
        <reference name="root">
            <action method="setTemplate"><template>page/product-view.phtml</template></action>
        </reference>

		<reference name="after_body_start">
			<block type="core/template" name="product_schema" template="carco/seo/product_schema.phtml"/>
		</reference>

<!--        <reference name="head">-->
<!--            <action method="addJs"><js>jquery/jquery.rating.js</js><group>data-jquery</group></action>-->
<!--        </reference>-->

        <reference name="product.info">
            <action method="addReviewSummaryTemplate"><type>default</type><template>review/helper/summary_short.phtml</template></action>
            <block type="cms/block" name="cms_block.product_view_delivery">
                <action method="setBlockId"><identifier>product_view_delivery</identifier></action>
            </block>
            <block type="cms/block" name="cms_block.product_view_help">
                <action method="setBlockId"><identifier>product_view_help</identifier></action>
            </block>
            <block type="catalog/product_view_attributes" name="product.info.attributes" as="attributes" template="catalog/product/view/attributes.phtml"/>
<!--            <block type="catalog/product_list_related" name="related_products" template="catalog/product/list/related.phtml" />-->
            <block type="catalog/product_list_upsell" name="product.info.upsell" as="upsell_products" template="catalog/product/list/upsell.phtml">
                <action method="setItemLimit"><type>upsell</type><limit>16</limit></action>
            </block>
            <block type="review/product_view_list" name="product.info.review.list" as="reviews_list" template="review/product/view/list.phtml"/>
            <block type="core/template" name="product.info.compare.button" as="compare_button" template="catalog/product/view/compare.phtml"/>
        </reference>

        <remove name="product.info.addto" />
        <remove name="product.clone_prices" />
    </catalog_product_view>

    <PRODUCT_TYPE_bundle translate="label" module="bundle">
        <reference name="product.info">
            <block type="bundle/catalog_product_price" name="bundle.prices" as="bundle_prices" template="bundle/catalog/product/view/price.phtml">
                <action method="setMAPTemplate"><tmpl>catalog/product/price_msrp_item.phtml</tmpl></action>
            </block>
        </reference>
    </PRODUCT_TYPE_bundle>

    <checkout_cart_index>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="checkout">
                <crumbName>cart</crumbName>
                <crumbInfo><label>Shopping Cart</label><title>Shopping Cart</title></crumbInfo>
            </action>
        </reference>
        <reference name="content">
            <action method="unsetChild"><name>checkout.cart</name></action>
            <block type="page/html_wrapper" name="checkout.cartWrapper" as="cartWrapper">
                <action method="setElementClass"><value>cart-wrapper</value></action>
                <action method="insert"><blockName>checkout.cart</blockName></action>
            </block>
        </reference>
        <reference name="checkout.cart">
            <block type="paypal/express_shortcut" name="checkout.cart.methods.paypal_express.bottom" after="-" template="paypal/express/shortcut.phtml">
                <action method="setIsQuoteAllowed"><value>1</value></action>
            </block>
        </reference>

        <reference name="checkout.cart.methods">
            <action method="unsetChild"><name>checkout.cart.methods.paypal_express.bottom</name></action>
        </reference>
    </checkout_cart_index>

    <checkout_onepage_index>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>Checkout</crumbName>
                <crumbInfo>
                    <label>Checkout</label>
                    <title>Checkout</title>
                </crumbInfo>
            </action>
        </reference>
        <reference name="checkout.onepage.login">
            <block type="cms/block" name="cms_block.checkout_register">
                <action method="setBlockId"><identifier>checkout_register</identifier></action>
            </block>
        </reference>
    </checkout_onepage_index>

    <checkout_onepage_success>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>Home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>order success</crumbName>
                <crumbInfo><label>Order Success</label><title>Order Success</title></crumbInfo>
            </action>
        </reference>
    </checkout_onepage_success>

    <checkout_onepage_failure>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>Failure payment process</crumbName>
                <crumbInfo>
                    <label>Failure payment process</label>
                    <title>Failure payment process</title>
                </crumbInfo>
            </action>
        </reference>
    </checkout_onepage_failure>


    <cms_page>
        <reference name="content">
            <action method="unsetChild"><name>cms.wrapper</name></action>
            <block type="page/html_wrapper" name="cms.wrapper">
                <action method="setElementClass"><value>text-page</value></action>
                <block type="cms/page" name="cms_page"/>
            </block>

            <block type="stenik_form/form" name="buying.form" template="cms/buying-form.phtml" after="page_content_heading">
                <action method="addConfigData">
                    <config>
                        <trans_email_id>1</trans_email_id>
                        <email_sender>same_as_email</email_sender>
                        <email_recipient>custom1</email_recipient>
                    </config>
                </action>
                <action method="addField" translate="config.label config.title config.placeholder">
                    <id>brand</id>
                    <type>select</type>
                    <config>
                        <name>brand</name>
                        <label>Brand</label>
                        <class>brand</class>
                        <required>1</required>
                    </config>
                </action>
                <action method="addField" translate="config.label config.title config.placeholder">
                    <id>model</id>
                    <type>select</type>
                    <config>
                        <name>model</name>
                        <label>Model</label>
                        <class>model</class>
                        <required>1</required>
                    </config>
                </action>
                <action method="addField" translate="config.label config.title config.placeholder">
                    <id>year</id>
                    <type>select</type>
                    <config>
                        <name>year</name>
                        <label>Year of manufacture</label>
                        <class>year</class>
                        <required>1</required>
                        <values>
                            <year1990>1990</year1990>
                            <year1991>1991</year1991>
                            <year1992>1992</year1992>
                            <year1993>1993</year1993>
                            <year1994>1994</year1994>
                            <year1995>1995</year1995>
                            <year1996>1996</year1996>
                            <year1997>1997</year1997>
                            <year1998>1998</year1998>
                            <year1999>1999</year1999>
                            <year2000>2000</year2000>
                            <year2001>2001</year2001>
                            <year2002>2002</year2002>
                            <year2003>2003</year2003>
                            <year2004>2004</year2004>
                            <year2005>2005</year2005>
                            <year2006>2006</year2006>
                            <year2007>2007</year2007>
                            <year2008>2008</year2008>
                            <year2009>2009</year2009>
                            <year2010>2010</year2010>
                            <year2011>2011</year2011>
                            <year2012>2012</year2012>
                            <year2013>2013</year2013>
                            <year2014>2014</year2014>
                            <year2015>2015</year2015>
                            <year2016>2016</year2016>
                            <year2017>2017</year2017>
                            <year2018>2018</year2018>
                            <year2019>2019</year2019>
                        </values>
                    </config>
                </action>
                <action method="addField" translate="config.label config.title config.placeholder">
                    <id>doors</id>
                    <type>select</type>
                    <config>
                        <name>doors</name>
                        <label>Coupe, number of doors</label>
                        <class>doors</class>
                        <required>1</required>
                        <values>
                            <option1>Седан</option1>
                            <option2>Комби</option2>
                            <option3>Хечбек - 3 врати</option3>
                            <option4>Хечбек - 5 врати</option4>
                            <option5>Купе</option5>
                            <option6>Кабрио</option6>
                            <option7>Джип - 3 врати</option7>
                            <option8>Джип - 4 врати</option8>
                            <option9>Бус</option9>
                        </values>
                    </config>
                </action>
                <action method="addField" translate="config.label config.title config.placeholder">
                    <id>engine</id>
                    <type>select</type>
                    <config>
                        <name>engine</name>
                        <label>Engine</label>
                        <class>engine</class>
                        <required>1</required>
                        <values>
                            <option1>Бензинов</option1>
                            <option2>Дизелов</option2>
                            <option3>Газ/Бензин</option3>
                            <option4>Хибриден</option4>
                            <option5>Електрически</option5>
                        </values>
                    </config>
                </action>
                <action method="addField" translate="config.label config.title config.placeholder">
                    <id>transmission</id>
                    <type>select</type>
                    <config>
                        <name>transmission</name>
                        <label>Transmission</label>
                        <class>transmission</class>
                        <required>1</required>
                        <values>
                            <option1>Ръчна</option1>
                            <option2>Автоматична</option2>
                            <option3>Полуавтоматична</option3>
                        </values>
                    </config>
                </action>
                <action method="addField" translate="config.label config.title config.placeholder">
                    <id>price</id>
                    <type>text</type>
                    <config>
                        <name>price</name>
                        <label>Desired price</label>
                        <class>price</class>
                        <required>1</required>
                    </config>
                </action>
                <action method="addField" translate="config.label config.title config.placeholder">
                    <id>city</id>
                    <type>text</type>
                    <config>
                        <name>city</name>
                        <label>Your city</label>
                        <class>city</class>
                        <required>1</required>
                    </config>
                </action>
                <action method="addField" translate="config.label config.title config.placeholder">
                    <id>phone</id>
                    <type>text</type>
                    <config>
                        <name>phone</name>
                        <label>Contact phone</label>
                        <class>phone</class>
                        <required>1</required>
                    </config>
                </action>
                <action method="addField" translate="config.label config.title config.placeholder">
                    <id>email</id>
                    <type>email</type>
                    <config>
                        <name>email</name>
                        <label>Email</label>
                        <class>email</class>
                        <required>1</required>
                    </config>
                </action>
                <action method="addField" translate="config.label config.title config.placeholder">
                    <id>images</id>
                    <type>file</type>
                    <config>
                        <name>images[]</name>
                        <label>Attach up to 5 photos</label>
                        <class>input-text input-file</class>
                        <path>var/uploads/buying</path>
                        <extensions>jpg,jpeg,png</extensions>
                        <onchange>checkFile(this, 5)</onchange>
                        <max_file_upload>5</max_file_upload>
                        <multiple/>
                    </config>
                </action>
                <action method="addField" translate="config.label config.title config.placeholder">
                    <id>comment</id>
                    <type>textarea</type>
                    <config>
                        <name>comment</name>
                        <label>Comment</label>
                        <class>comment</class>
                        <required>0</required>
                    </config>
                </action>
                <action method="addField" translate="config.label config.title config.placeholder">
                    <id>captcha</id>
                    <type>captcha</type>
                    <config/>
                    <after/>
                </action>
                <action method="addField" translate="config.label config.title config.placeholder">
                    <id>submit</id>
                    <type>button</type>
                    <config>
                        <label>Send</label>
                        <class>button</class>
                    </config>
                </action>
            </block>
        </reference>
        <reference name="left">
            <block name="cms_left" type="core/template" template="cms/cms_left.phtml">
                <block type="cms/block" name="cms_block.sidebar_static_menu">
                    <action method="setBlockId"><identifier>sidebar_static_menu</identifier></action>
                </block>
            </block>
        </reference>
    </cms_page>

    <customer_account_login>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>Home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>login</crumbName>
                <crumbInfo><label>Login or Create an Account</label><title>Login</title></crumbInfo>
            </action>
        </reference>
        <reference name="customer_form_login">
            <block type="cms/block" name="cms.block_registration_info_text">
                <action method="setBlockId"><identifier>login_registration_text</identifier></action>
            </block>
        </reference>
    </customer_account_login>

    <customer_account_create>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>register</crumbName>
                <crumbInfo><label>Create an Account</label><title>Create an Account</title></crumbInfo>
            </action>
        </reference>
        <reference name="customer_form_register">
            <block type="cms/block" name="cms_block.customer_registration">
                <action method="setBlockId"><identifier>customer_registration</identifier></action>
            </block>
        </reference>
    </customer_account_create>

    <customer_account_logoutsuccess>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>logout</crumbName>
                <crumbInfo><label>Logout</label><title>Logout</title></crumbInfo>
            </action>
        </reference>
    </customer_account_logoutsuccess>

    <customer_account_forgotpassword>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>forgotpassword</crumbName>
                <crumbInfo><label>Forgot Password</label><title>Forgot Password</title></crumbInfo>
            </action>
        </reference>
    </customer_account_forgotpassword>

    <customer_account_resetpassword>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>resetPassword</crumbName>
                <crumbInfo><label>Reset a Password</label><title>Reset a Password</title></crumbInfo>
            </action>
        </reference>
    </customer_account_resetpassword>

    <contacts_index_index>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>

        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="contacts">
                <crumbName>contact us</crumbName>
                <crumbInfo>
                    <label>Contact Us</label>
                    <title>Contact Us</title>
                </crumbInfo>
            </action>
        </reference>

        <reference name="contactForm">
            <block type="cms/block" name="cms_block.contacts_info_text">
                <action method="setBlockId"><block_id>contacts_info_text</block_id></action>
            </block>
        </reference>
    </contacts_index_index>

    <customer_account>
        <reference name="root">
            <action method="addBodyClass"><class>customer-account</class></action>
        </reference>
        <reference name="left">
            <action method="unsetChild"><block>cart_sidebar</block></action>
            <remove name="left.permanent.callout"/>
            <remove name="sale.reorder.sidebar"/>
        </reference>
        <reference name="customer_account_navigation">
            <action method="addLink" translate="label" module="customer"><name>billing_agreements</name><path></path><label></label></action>
            <action method="addLink" translate="label" module="customer"><name>recurring_profiles</name><path></path><label></label></action>
            <action method="addLink" translate="label" module="customer"><name>reviews</name><path></path><label></label></action>
            <action method="addLink" translate="label" module="customer"><name>tags</name><path></path><label></label></action>
            <action method="addLink" translate="label" module="customer"><name>wishlist</name><path></path><label></label></action>
            <action method="addLink" translate="label" module="customer"><name>downloadable_products</name><path></path><label></label></action>
            <action method="addLink" translate="label" module="customer"><name>OAuth Customer Tokens</name><path></path><label></label></action>
            <action method="addLink" translate="label" module="customer"><name>logoutLink</name><path>customer/account/logout/</path><label>Logout</label></action>
        </reference>
    </customer_account>

    <customer_account_index>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>dashboard</crumbName>
                <crumbInfo>
                    <label>My Dashboard</label>
                    <title>My Dashboard</title>
                </crumbInfo>
            </action>
        </reference>
    </customer_account_index>

    <customer_account_edit>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>dashboard</crumbName>
                <crumbInfo>
                    <label>Account Information</label>
                    <title>Account Information</title>
                </crumbInfo>
            </action>
        </reference>
    </customer_account_edit>

    <customer_address_index>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>address book</crumbName>
                <crumbInfo>
                    <label>Address Book</label>
                    <title>Address Book</title>
                </crumbInfo>
            </action>
        </reference>
        <reference name="address_book">
            <block type="cms/block" name="text_customer_teacher_edit_address_1">
                <action method="setBlockId"><block_id>text_customer_teacher_edit_address</block_id></action>
            </block>
        </reference>
    </customer_address_index>

    <customer_address_form>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>address book</crumbName>
                <crumbInfo>
                    <label>Address Book</label>
                    <title>Address Book</title>
                </crumbInfo>
            </action>
        </reference>
        <reference name="customer_address_edit">
            <block type="cms/block" name="text_customer_teacher_edit_address_2">
                <action method="setBlockId"><block_id>text_customer_teacher_edit_address</block_id></action>
            </block>
        </reference>
    </customer_address_form>

    <wishlist_index_index>
        <reference name="root">
            <action method="setTemplate"><template>page/2columns-left.phtml</template></action>
        </reference>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>wishlist</crumbName>
                <crumbInfo>
                    <label>My Wishlist</label>
                    <title>My Wishlist</title>
                </crumbInfo>
            </action>
        </reference>
    </wishlist_index_index>

    <wishlist_index_share>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>wishlist</crumbName>
                <crumbInfo>
                    <label>My Wishlist</label>
                    <title>My Wishlist</title>
                </crumbInfo>
            </action>
        </reference>
    </wishlist_index_share>

    <wishlist_shared_index>
        <reference name="root">
            <action method="setTemplate"><template>page/2columns-left.phtml</template></action>
        </reference>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>wishlist</crumbName>
                <crumbInfo>
                    <label>My Wishlist</label>
                    <title>My Wishlist</title>
                </crumbInfo>
            </action>
        </reference>
    </wishlist_shared_index>

    <sales_order_view>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>orders</crumbName>
                <crumbInfo>
                    <label>My Orders</label>
                    <title>My Orders</title>
                    <link>/sales/order/history/</link>
                </crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>order</crumbName>
                <crumbInfo>
                    <label>Order</label>
                    <title>Order</title>
                </crumbInfo>
            </action>
        </reference>
    </sales_order_view>

    <sales_order_invoice>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>orders</crumbName>
                <crumbInfo>
                    <label>My Orders</label>
                    <title>My Orders</title>
                    <link>/sales/order/history/</link>
                </crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>order</crumbName>
                <crumbInfo>
                    <label>Order</label>
                    <title>Order</title>
                </crumbInfo>
            </action>
        </reference>
    </sales_order_invoice>

    <sales_order_shipment>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>orders</crumbName>
                <crumbInfo>
                    <label>My Orders</label>
                    <title>My Orders</title>
                    <link>/sales/order/history/</link>
                </crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>order</crumbName>
                <crumbInfo>
                    <label>Order</label>
                    <title>Order</title>
                </crumbInfo>
            </action>
        </reference>
    </sales_order_shipment>

    <sales_order_history>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>orders</crumbName>
                <crumbInfo>
                    <label>My Orders</label>
                    <title>My Orders</title>
                </crumbInfo>
            </action>
        </reference>
    </sales_order_history>

    <newsletter_manage_index>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>newsletter</crumbName>
                <crumbInfo>
                    <label>Newsletter Subscriptions</label>
                    <title>Newsletter Subscriptions</title>
                </crumbInfo>
            </action>
        </reference>
    </newsletter_manage_index>

    <inchoo_socialconnect_account_google>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>Google Connect</crumbName>
                <crumbInfo>
                    <label>Google Connect</label>
                    <title>Google Connect</title>
                </crumbInfo>
            </action>
        </reference>
    </inchoo_socialconnect_account_google>

    <inchoo_socialconnect_account_facebook>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>Facebook Connect</crumbName>
                <crumbInfo>
                    <label>Facebook Connect</label>
                    <title>Facebook Connect</title>
                </crumbInfo>
            </action>
        </reference>
    </inchoo_socialconnect_account_facebook>

    <cms_index_noroute>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>Page not found</crumbName>
                <crumbInfo>
                    <label>Page not found</label>
                    <title>Page not found</title>
                </crumbInfo>
            </action>
        </reference>
    </cms_index_noroute>

    <catalog_seo_sitemap>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>sitemap</crumbName>
                <crumbInfo>
                    <label>Sitemap</label>
                    <title>Sitemap</title>
                </crumbInfo>
            </action>
        </reference>
    </catalog_seo_sitemap>

    <paypal_express_review translate="label">
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>pay pal</crumbName>
                <crumbInfo>
                    <label>PayPal</label>
                    <title>PayPal</title>
                </crumbInfo>
            </action>
        </reference>
    </paypal_express_review>

    <print translate="label" module="page">
        <label>All Pages (Print Version)</label>
        <!-- Mage_Page -->
        <block type="page/html" name="root" output="toHtml" template="page/print.phtml">

            <block type="page/html_head" name="head" as="head">
<!--                <action method="addJs"><script>prototype/prototype.js</script></action>-->
<!--                <action method="addJs"><script>mage/translate.js</script></action>-->
                <action method="addJs"><script>.lib/ccard.js</script></action>
<!--                <action method="addJs"><script>prototype/validation.js</script></action>-->
                <action method="addJs"><script>varien/js.js</script></action>

                <action method="addCss"><stylesheet>css/styles.css</stylesheet></action>
                <action method="addItem"><type>skin_css</type><name>css/styles-ie.css</name><params/><if>lt IE 8</if></action>
                <action method="addCss"><stylesheet>css/widgets.css</stylesheet></action>
                <action method="addCss"><stylesheet>css/print.css</stylesheet><params>media="print"</params></action>

                <action method="addItem"><type>js</type><name>lib/ds-sleight.js</name><params/><if>lt IE 7</if></action>
                <action method="addItem"><type>skin_js</type><name>js/ie6.js</name><params/><if>lt IE 7</if></action>

            </block>

            <block type="core/text_list" name="content" as="content" translate="label">
                <label>Main Content Area</label>
            </block>

        </block>
    </print>
</layout>
