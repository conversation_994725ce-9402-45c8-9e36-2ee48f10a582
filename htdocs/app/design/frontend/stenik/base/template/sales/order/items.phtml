<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2013 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php  $_order = $this->getOrder() ?>
<table class="data-table" id="my-orders-table" summary="<?php echo $this->__('Items Ordered') ?>">
    <thead>
        <tr>
            <th><?php echo $this->__('Product Name') ?></th>
            <th><?php echo $this->__('SKU') ?></th>
            <th class="a-right"><?php echo $this->__('Price') ?></th>
            <th class="a-center"><?php echo $this->__('Qty') ?></th>
            <th class="a-right"><?php echo $this->__('Subtotal') ?></th>
        </tr>
    </thead>
    <tfoot>
        <?php echo $this->getChildHtml('order_totals') ?>
    </tfoot>
        <?php $_items = $_order->getItemsCollection(); ?>
        <?php $_index = 0; ?>
            <?php $_count = $_items->count(); ?>
        <?php foreach ($_items as $_item): ?>
        <?php if ($_item->getParentItem()) continue; ?>
        <tbody>
            <?php echo $this->getItemHtml($_item) ?>
            <?php if($this->helper('giftmessage/message')->getIsMessagesAvailable('order_item', $_item) && $_item->getGiftMessageId()): ?>
            <tr class="border<?php echo ($_index++ > $_count ?' last':'') ?>" id="order-item-gift-message-<?php echo $_item->getId() ?>" style="display:none;">
                <?php $_giftMessage=$this->helper('giftmessage/message')->getGiftMessageForEntity($_item); ?>
                <td class="gift-message-row" colspan="7">
                    <a href="#" title="<?php echo $this->__('Close') ?>" onclick="return giftMessageToogle('<?php echo $_item->getId() ?>');" class="btn-close"><?php echo $this->__('Close') ?></a>
                    <dl class="gift-message">
                        <dt><strong><?php echo $this->__('From:') ?></strong> <?php echo $this->escapeHtml($_giftMessage->getRecipient()) ?></dt>
                        <dt><strong><?php echo $this->__('To:') ?></strong> <?php echo $this->escapeHtml($_giftMessage->getSender()) ?></dt>
                        <dd><?php echo $this->helper('giftmessage/message')->getEscapedGiftMessage($_item) ?></dd>
                    </dl>
                </td>
            </tr>
            <?php endif ?>
        </tbody>
        <?php endforeach; ?>
</table>
<script type="text/javascript">decorateTable('my-orders-table', {'tbody' : ['odd', 'even'], 'tbody tr' : ['first', 'last']})</script>
