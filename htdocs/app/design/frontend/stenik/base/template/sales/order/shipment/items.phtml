<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2013 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php  $_order = $this->getOrder() ?>
<p class="order-links">
    <?php  if ($_order->getTracksCollection()->count()) : ?>
        <a href="#" onclick="popWin('<?php echo $this->helper('shipping')->getTrackingPopupUrlBySalesModel($_order) ?>','trackorder','width=800,height=600,top=0,left=0,resizable=yes,scrollbars=yes'); return false;" title="<?php echo $this->__('Track all shipment(s)') ?>"><?php echo $this->__('Track all shipments') ?></a> <span class="separator">|</span>
    <?php endif; ?>
    <a href="<?php echo $this->getPrintAllShipmentsUrl($_order) ?>" onclick="this.target='_blank'" class="link-print"><?php echo $this->__('Print All Shipments') ?></a>
</p>
<?php foreach ($_order->getShipmentsCollection() as $_shipment): ?>
<h2 class="sub-title"><?php echo $this->__('Shipment #') ?><?php echo $_shipment->getIncrementId(); ?> <span class="separator">|</span> <a href="<?php echo $this->getPrintShipmentUrl($_shipment) ?>" onclick="this.target='_blank'" class="link-print"><?php echo $this->__('Print Shipment') ?></a></h2>
<?php $tracks = $_shipment->getTracksCollection(); ?>
<?php  if ($tracks->count()): ?>
    <table class="data-table tracking-table" id="my-tracking-table-<?php echo $_shipment->getId(); ?>">
        <tbody>
            <tr>
                <td colspan="2">
                    <a href="#" onclick="popWin('<?php echo $this->helper('shipping')->getTrackingPopupUrlBySalesModel($_shipment) ?>','trackshipment','width=800,height=600,top=0,left=0,resizable=yes,scrollbars=yes'); return false;" title="<?php echo $this->__('Track this shipment') ?>"><?php echo $this->__('Track this shipment') ?></a>
                </td>
            </tr>
            <tr>
                <th class="label"><?php echo $this->__('Tracking Number(s):') ?></th>
                <td>&nbsp;
                <?php
                $i = 1;
                $_size = $tracks->count();
                foreach($tracks as $track): ?>
                <?php if($track->isCustom()): ?>
                    <?php echo $this->escapeHtml($track->getNumber()) ?>
                <?php else: ?>
                    <a href="#" onclick="popWin('<?php echo $this->helper('shipping')->getTrackingPopupUrlBySalesModel($track) ?>','trackorder','width=800,height=600,left=0,top=0,resizable=yes,scrollbars=yes')" ><?php echo $this->escapeHtml($track->getNumber()) ?></a>
                <?php endif; ?>
                <?php if($i!=$_size): ?>, <?php endif; ?>
                <?php $i++;
                endforeach; ?>
                </td>
            </tr>
        </tbody>
    </table>
    <script type="text/javascript">decorateTable('my-tracking-table-<?php echo $_shipment->getId(); ?>')</script>
<?php  endif; ?>
<h3 class="table-caption"><?php echo $this->__('Items Shipped') ?></h3>
<table class="data-table" id="my-shipment-table-<?php echo $_shipment->getId(); ?>">
    <thead>
        <tr>
            <th><?php echo $this->__('Product Name') ?></th>
            <th><?php echo $this->__('SKU') ?></th>
            <th class="a-center"><span class="nobr"><?php echo $this->__('Qty Shipped') ?></span></th>
        </tr>
    </thead>
    <?php $_items = $_shipment->getAllItems(); ?>
    <?php $_count = count($_items) ?>
    <?php foreach ($_items as $_item): ?>
    <?php if ($_item->getOrderItem()->getParentItem()) continue; ?>
    <tbody>
        <?php echo $this->getItemHtml($_item) ?>
    </tbody>
    <?php endforeach; ?>
</table>
<script type="text/javascript">decorateTable('my-shipment-table-<?php echo $_shipment->getId(); ?>', {'tbody' : ['odd', 'even'], 'tbody tr' : ['first', 'last']})</script>
<?php echo $this->getCommentsHtml($_shipment)?>
<?php endforeach; ?>
