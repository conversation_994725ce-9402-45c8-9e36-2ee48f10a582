<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2016 X.commerce, Inc. and affiliates (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/* @var $this Mage_Wishlist_Block_Customer_Wishlist */
?>
<?php if ($this->helper('wishlist')->isAllow()) : ?>

    <div class="my-wishlist">

        <h1><?php echo $this->getTitle(); ?></h1>

        <?php echo $this->getMessagesBlock()->toHtml() ?>

        <form id="wishlist-view-form" action="<?php echo $this->getUrl('*/*/update', array('wishlist_id' => $this->getWishlistInstance()->getId())) ?>" method="post">
            <?php echo $this->getChildHtml('top'); ?>

            <?php if ($this->hasWishlistItems()): ?>
                    <?php echo $this->getBlockHtml('formkey');?>
                    <?php $this->getChild('items')->setItems($this->getWishlistItems()); ?>
                    <?php echo $this->getChildHtml('items');?>
                    <script type="text/javascript">decorateTable('wishlist-table')</script>
            <?php else: ?>
                <p class="wishlist-empty"><?php echo $this->__('You have no items in your wishlist.') ?></p>
            <?php endif ?>
            <div class="buttons-set buttons-set2">
                <?php echo $this->getChild('control_buttons')->toHtml();?>
            </div>
        </form>

        <form id="wishlist-allcart-form" action="<?php echo $this->getUrl('*/*/allcart') ?>" method="post">
            <?php echo $this->getBlockHtml('formkey') ?>
            <div class="no-display">
                <input type="hidden" name="wishlist_id" id="wishlist_id" value="<?php echo $this->getWishlistInstance()->getId() ?>" />
                <input type="hidden" name="qty" id="qty" value="" />
            </div>
        </form>

        <script type="text/javascript">
            var wishlistForm = new Validation($('wishlist-view-form'));
            var wishlistAllCartForm = new Validation($('wishlist-allcart-form'));

            function calculateQty() {
                var itemQtys = new Array();
                $$('#wishlist-view-form .qty').each(
                    function (input, index) {
                        var idxStr = input.name;
                        var idx = idxStr.replace( /[^\d.]/g, '' );
                        itemQtys[idx] = input.value;
                    }
                );

                $$('#qty')[0].value = JSON.stringify(itemQtys);
            }

            function addAllWItemsToCart() {
                calculateQty();
                wishlistAllCartForm.form.submit();
            }
        </script>
    </div>

    <?php echo $this->getChildHtml('bottom'); ?>

<?php endif ?>
