<?php
/**
 * @category    Fishpig
 * @package     Fishpig_AttributeSplash
 * @license     http://fishpig.co.uk/license.txt
 * <AUTHOR> <<EMAIL>>
 */
 ?>
<?php if ($splashPage = $this->getSplashPage()): ?>

	<?php echo $this->getMessagesBlock()->getGroupedHtml() ?>
    
    <?php if (Mage::app()->getRequest()->getParam('p',0) <= 1): // Show description only on first page ?>

        <div class="row">
            <div class="col-xs-12">
                <h1><?php echo $this->escapeHtml($splashPage->getName()) ?></h1>
                <div class="brand-description">
                    <div class="brand-item">
                        <?php if ($splashPage->getThumbnail()): ?>
                            <img
                                src="<?php echo $this->helper('attributeSplash/image')->init($splashPage, 'thumbnail')->keepFrame(false)->constrainOnly(true)->resize(146, 146) ?>"
                                alt="<?php echo $this->escapeHtml($splashPage->getName()) ?>"
                            >
                        <?php else: ?>
                            <img 
                                src="<?php echo $this->getSkinUrl('images/no-brand-logo.jpg') ?>"
                                alt="<?php echo $this->escapeHtml($splashPage->getName()) ?>"
                            >
                        <?php endif; ?>
                    </div>
                    <?php if ($description = $splashPage->getDescription()): ?>
                        <?php echo $description ?>
                    <?php endif; ?>
                </div>
                <?php if ($splashPage->getImage()): ?>
                    <div class="category-banner">
                        <img class="brandImage" src="<?php echo $this->helper('attributeSplash/image')->init($splashPage, 'image')->keepFrame(false)->constrainOnly(true)->resize(895, null) ?>" alt="<?php echo $this->escapeHtml($splashPage->getName()) ?>">
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>

	<?php if($this->isContentMode()): ?>
		<?php echo $this->getCmsBlockHtml() ?>
	<?php elseif($this->isMixedMode()): ?>
		<?php echo $this->getCmsBlockHtml() ?>
		<?php echo $this->getProductListHtml() ?>
	<?php else: ?>
		<?php echo $this->getProductListHtml() ?>
	<?php endif; ?>

<?php endif; ?>
