<?php
/**
 * @category    Fishpig
 * @package     Fishpig_AttributeSplash
 * @license     http://fishpig.co.uk/license.txt
 * <AUTHOR> <<EMAIL>>
 */
?>
<?php if ($splashGroup = $this->getSplashGroup()): ?>
	<?php $splashPages = $this->getSplashPages() ?>

	<?php echo $this->getMessagesBlock()->getGroupedHtml() ?>

	<h1><?php echo $splashGroup->getName(); ?></h1>

	<?php if($this->isContentMode()): ?>
		<?php echo $this->getCmsBlockHtml() ?>
	<?php elseif($this->isMixedMode()): ?>
		<?php echo $this->getCmsBlockHtml() ?>
	<?php endif; ?>

	<?php if ($this->isProductMode() || $this->isMixedMode()): ?>

		<div class="brands-listing">
		    <div class="row">
		    	<?php $_columnCount = 2; ?>
		    	<?php $_collectionSize = $splashPages->count() ?>
		    	<?php $i = 0; foreach($splashPages as $splashPage): ?>
		    		<?php if ($i++%$_columnCount==0): ?>
		    		<div class="col-xs-6 col-sm-3">
		    		    <div class="row">
		    		<?php endif ?>
		    		        <div class="col-xs-6 col-sm-6">
		    		            <a href="<?php echo $splashPage->getUrl() ?>" class="brand-item" title="<?php echo $this->escapeHtml($splashPage->getName()) ?>">
		    		                <?php if ($splashPage->getThumbnail()): ?>
		    		                	<img
		    		                		src="<?php echo $this->helper('attributeSplash/image')->init($splashPage, 'thumbnail')->keepFrame(false)->constrainOnly(true)->resize(146, 146) ?>"
		    		                		alt="<?php echo $this->escapeHtml($splashPage->getName()) ?>"
		    		                	>
		    		                <?php else: ?>
		    		                	<img
		    		                		src="<?php echo $this->getSkinUrl('images/no-brand-logo.jpg') ?>"
		    		                		alt="<?php echo $this->escapeHtml($splashPage->getName()) ?>"
		    		                	>
		    		                <?php endif; ?>
		    		            </a>
		    		        </div>
		    		<?php if ($i%$_columnCount==0 || $i==$_collectionSize): ?>
		    		    </div>
		    		</div>
		    		<?php endif ?>
	       		<?php endforeach ?>
       		</div>
		</div>

        <?php if ($this->hasPagerBlock()): ?>
        	<?php echo $this->getPagerHtml() ?>
        <?php endif; ?>

	<?php endif; ?>

<?php endif; ?>