<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2014 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?><?php if(count($this->getItemCollection()->getItems())): ?>
    
    <?php $productImageSufix = $this->helper('stenik_sitesettings')->getproductImagesAltSufix(); ?>

    <div class="wide-area upsell-products">
        <div class="container">
            <div class="row">
                <div class="col-xs-12">
                    <div class="row-header marginB30 clearfix">
                        <span class="row-title"><?php echo $this->__('You may also like');?></span>
                    </div>
                </div>
            </div>
            <div class="row products-list">
                <?php foreach ($this->getItemCollection()->getItems() as $_link): ?>

                    <?php $secondImg = ($this->helper('stenik_sitesettings')->getProductListingSecondImg() ? Mage::helper('stenik_base/catalog_product')->getSecondImageFile($_link) : null) ?>

                    <div class="col-xs-3 col-sm-3">
                        <a class="product-box" href="<?php echo $_link->getProductUrl() ?>">
                            <span class="image-wrapper<?php if ($secondImg): ?> has-second-img<?php endif; ?>">
                                <img
                                    class="first"
                                    src="<?php echo $this->helper('catalog/image')->init($_link, 'small_image')->resize(285, 285); ?>"
                                    srcset="<?php echo $this->helper('catalog/image')->init($_link, 'small_image')->resize(570, 570); ?> 2x"
                                    alt="<?php echo $this->stripTags($this->getImageLabel($_link, 'small_image'), null, true) ?>"
                                    title="<?php echo $this->stripTags($this->getImageLabel($_link, 'small_image'), null, true)  . ' - ' . $productImageSufix ?>"
                                >
                                <?php if ($secondImg): ?>
                                    <img
                                        class="second"
                                        src="<?php echo $this->helper('catalog/image')->init($_link, 'small_image', $secondImg)->resize(285, 285); ?>"
                                        srcset="<?php echo $this->helper('catalog/image')->init($_link, 'small_image', $secondImg)->resize(570, 570); ?> 2x"
                                        alt="<?php echo $this->stripTags($this->getImageLabel($_link, 'small_image'), null, true) ?> - 2"
                                        title="<?php echo $this->stripTags($this->getImageLabel($_link, 'small_image'), null, true)  . ' - 2 - ' . $productImageSufix ?>"
                                    >
                                <?php endif; ?>
                                <?php echo Mage::app()->getLayout()->getBlock('product.labels')->setProduct($_link)->toHtml(); ?>
                                <span class="actions">
                                    <?php if($_link->isSaleable()): ?>
                                        <span class="button checkout-color add-to-cart" onclick="document.location.href='<?php echo $this->getAddToCartUrl($_link) ?>'; return false">
                                            <?php echo $this->__('Add to Cart');?>
                                        </span>
                                    <?php endif; ?>
                                    <?php if ($this->helper('wishlist')->isAllow()) : ?>
                                        <span class="icon-link" onclick="document.location.href='<?php echo $this->helper('wishlist')->getAddUrl($_link) ?>'; return false">
                                            <svg aria-hidden="true" class="icon-svg wishlist"><use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#wishlist' ?>"></use></svg>
                                        </span>
                                    <?php endif; ?>
                                    <?php if($_compareUrl=$this->getAddToCompareUrl($_link)): ?>
                                        <span class="icon-link" onclick="document.location.href='<?php echo $_compareUrl ?>'; return false">
                                            <svg aria-hidden="true" class="icon-svg compare"><use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#compare' ?>"></use></svg>
                                        </span>
                                    <?php endif; ?>
                                </span>
                            </span>

                            <?php $confOptions = Mage::helper('stenik_base/catalog_product')->getProductConfigurableAttributeOptions($_link, 'color'); ?>

                            <?php if ($count = count($confOptions)): ?>
                                <span class="attributes">
                                    <?php echo $count; ?> <?php echo $this->__($count == 1 ? 'color' : 'colors'); ?>
                                </span>
                            <?php endif ?>

                            <span class="title"><?php echo $this->escapeHtml($_link->getName()) ?></span>
                            <?php echo $this->getPriceHtml($_link, true, '-upsell') ?>
                        </a>
                    </div>
                <?php endforeach ?>              
            </div>
        </div>
    </div>
<?php endif ?>