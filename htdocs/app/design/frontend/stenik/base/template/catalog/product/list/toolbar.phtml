<?php
/**
 * Product list toolbar
 *
 * @see Mage_Catalog_Block_Product_List_Toolbar
 */
?>
<?php if($this->getCollection()->getSize()): ?>
    <div class="col-xs-12">
        <div class="toolbar">

            <div class="drop-down openonclick drop-down-js">
                <?php foreach ($this->getAvailableLimit() as $_key=>$_limit): ?>
                    <?php if($this->isLimitCurrent($_key)): ?>
                        <a href="javascript:;" class="open-item"><?php echo $this->__('Show by'); ?> <?php echo $_limit ?></a>
                    <?php endif; ?>
                <?php endforeach; ?>
                <ul class="sub-options">
                    <?php foreach ($this->getAvailableLimit() as $_key=>$_limit): ?>
                        <?php if($this->isLimitCurrent($_key)) continue; ?>
                        <li><a href="<?php echo $this->getLimitUrl($_key) ?>"><?php echo $_limit ?></a></li>
                    <?php endforeach; ?>
                </ul>
            </div>

            <div class="drop-down openonclick drop-down-js">
                <?php foreach($this->getAvailableOrders() as $_key=>$_order): ?>
                    <?php if($this->isOrderCurrent($_key)): ?>
                        <a href="javascript:;" class="open-item"><?php echo $this->__('Sort by'); ?> <?php echo $this->__($_order) ?></a>
                    <?php endif; ?>
                <?php endforeach; ?>
                <ul class="sub-options">
                    <?php foreach($this->getAvailableOrders() as $_key=>$_order): ?>
                        <?php if($this->isOrderCurrent($_key)) continue; ?>
                        <li><a href="<?php echo $this->getOrderUrl($_key, 'asc') ?>"><?php echo $this->__($_order) ?></a></li>
                    <?php endforeach; ?>
                </ul>
            </div>

            <?php if($this->getCurrentDirection() == 'desc'): ?>
                <a href="<?php echo $this->getOrderUrl(null, 'asc') ?>" class="sorting-arrow">
                    <svg aria-hidden="true" class="icon-svg arrow-up">
                        <use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#arrow-up' ?>"></use>
                    </svg>
                </a>
            <?php else: ?>
                <a href="<?php echo $this->getOrderUrl(null, 'desc') ?>" class="sorting-arrow">
                    <svg aria-hidden="true" class="icon-svg arrow-down">
                        <use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#arrow-down' ?>"></use>
                    </svg>
                </a>
            <?php endif; ?>

            <?php if( $this->isEnabledViewSwitcher() ): ?>
                <?php $_modes = $this->getModes(); ?>
                <?php if($_modes && count($_modes)>1): ?>
                    <div class="view-mode">
                        <?php foreach ($this->getModes() as $_code=>$_label): ?>
                            <?php if($this->isModeActive($_code)): ?>
                                <a title="<?php echo $_label ?>" class="<?php echo strtolower($_code); ?> active">
                                    <svg aria-hidden="true" class="icon-svg <?php echo strtolower($_code); ?>-view">
                                        <use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#' ?><?php echo strtolower($_code); ?><?php echo "-view"; ?>"></use>
                                    </svg>
                                </a>
                            <?php else: ?>
                                <a href="<?php echo $this->getModeUrl($_code) ?>" class="<?php echo strtolower($_code); ?>">
                                    <svg aria-hidden="true" class="icon-svg <?php echo strtolower($_code); ?>-view">
                                        <use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#' ?><?php echo strtolower($_code); ?><?php echo "-view"; ?>"></use>
                                    </svg>
                                </a>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            <?php endif; ?>

            <?php if (0): ?>
                <div class="show-only-available">
                    <input type="checkbox" class="checkbox" id="show-only-available">
                    <label for="show-only-available"><?php echo $this->__('Show only available') ?></label>
                </div>
            <?php endif ?>

            <?php echo $this->getPagerHtml() ?>
        </div>
    </div>
<?php endif ?>