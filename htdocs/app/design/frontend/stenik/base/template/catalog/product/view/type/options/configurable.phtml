<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2015 X.commerce, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<?php
    $_product    = $this->getProduct();
    $_attributes = Mage::helper('core')->decorateArray($this->getAllowAttributes());
?>

<?php if ($_product->isSaleable() && count($_attributes)):?>
    <?php foreach($_attributes as $_attribute): ?>
        <div class="conf-option">
            <label for="attribute<?php echo $_attribute->getAttributeId() ?>"><?php echo $this->__($_attribute->getLabel());?></label>
            <select name="super_attribute[<?php echo $_attribute->getAttributeId() ?>]" id="attribute<?php echo $_attribute->getAttributeId() ?>" class="required-entry super-attribute-select">
            </select>
        </div>
    <?php endforeach; ?>
    <script type="text/javascript">
        var spConfig = new Product.Config(<?php echo $this->getJsonConfig() ?>);

        <?php foreach($_attributes as $_attribute): ?>
            <?php if ($_attribute->getProductAttribute()->getAttributeCode() == 'color'): ?>
                <?php
                    $valueLabelMap = array();
                    foreach ($_attribute->getPrices() as $priceData) {
                        $valueLabelMap[$priceData['value_index']] = $priceData['label'];
                    }
                ?>

                jQuery(function($) {
                    var valueLabelMap = <?php echo json_encode($valueLabelMap); ?>;
                    $('#attribute<?php echo $_attribute->getAttributeId() ?>').change(function() {
                        var optionId = parseInt($(this).val());

                        if (typeof valueLabelMap[optionId] !== 'undefined' && valueLabelMap[optionId]) {
                            <?php // Slick ?>
                            $('.gallery-box .gallery-thumbnails .thumb img').each(function() {
                                if ($(this).data('label') == valueLabelMap[optionId]) {
                                    $(this).click();
                                    return false;
                                }
                            });
                        }

                    });
                });

            <?php endif; ?>
        <?php endforeach ?>
    </script>
<?php endif;?>