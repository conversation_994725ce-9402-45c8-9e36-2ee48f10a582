<?php
/**
 * Product labels
 *
 * @package  stenik_default
 * <AUTHOR> <<EMAIL>>
 * @see      Stenik_Base_Helper_Product
 */

$_product = $this->getProduct();

    /* TODO: This template shows only price with excluding tax. And don`t include Wee Taxes */
    $product             = $this->getProduct();
    if(!$product) {
        return '';
    }
    $store               = $product->getStore();
    $taxHelper           = Mage::helper('tax');
    $simplePricesTax     = ($taxHelper->displayPriceIncludingTax() || $taxHelper->displayBothPrices());
    $convertedPrice      = $store->roundPrice($store->convertPrice($product->getPrice()));
    $convertedFinalPrice = $store->roundPrice($store->convertPrice($product->getFinalPrice()));
    $regularPrice        = $taxHelper->getPrice($product, $convertedPrice, $simplePricesTax);
    $price               = $taxHelper->getPrice($product, $convertedPrice);
    $finalPrice          = $taxHelper->getPrice($product, $convertedFinalPrice);
    
?>

<?php if (Mage::helper('stenik_base/catalog_product')->isPromo($_product) || Mage::helper('stenik_base/catalog_product')->isPromoBySpecialPrice($_product)): ?>
	<span class="label promo"><?php echo -round((($price-$finalPrice)/$price)*100) ?>%</span>
<?php elseif (Mage::helper('stenik_base/catalog_product')->isNew($_product)): ?>
	<span class="label new"><?php echo $this->__('New');?></span>
<?php endif ?>

