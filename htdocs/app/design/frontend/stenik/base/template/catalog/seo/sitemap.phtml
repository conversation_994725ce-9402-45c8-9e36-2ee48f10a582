<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2014 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * @see Mage_Catalog_Block_Seo_Sitemap_
 */
?>
<?php $_items = $this->getCollection(); ?>
<?php if($_items->getSize()): ?>
    <ul class="sitemap">
        <?php foreach ($_items as $_item): ?>
            <li><a href="<?php echo $this->getItemUrl($_item) ?>"><?php echo $this->escapeHtml($_item->name) ?></a></li>
        <?php endforeach; ?>
    </ul>
<?php else: ?>
    <p class="note-msg">
        <?php echo $this->__('There are no %s available.', $this->getItemsTitle()); ?>
    </p>
    <script type="text/javascript">
    //<![CDATA[
        if ($('sitemap_top_links') != undefined) {
            $('sitemap_top_links').hide();
        }
    //]]>
    </script>
<?php endif ?>
