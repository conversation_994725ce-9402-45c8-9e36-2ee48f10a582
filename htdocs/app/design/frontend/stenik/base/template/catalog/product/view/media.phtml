<?php
/**
 * @package  Stenik_Template
 * <AUTHOR> <<EMAIL>>
 * @see      Mage_Catalog_Block_Product_View_Media
 */
?>

<?php
    $_product = $this->getProduct();
    $_helper  = $this->helper('catalog/output');
    $gallery  = array();
    $productImageSufix = $this->helper('stenik_sitesettings')->getproductImagesAltSufix();
    $imageCounter = -1;
    $initialSlide = 0;

    foreach ($this->getGalleryImages() as $image) {
        $imageCounter++;
        if ($_product->getImage() == $image->getFile()) {
            $initialSlide = $imageCounter;
        }
        if ($this->isGalleryImageVisible($image)) {
            $gallery[]  = array(
                'thumb'    => (string)$this->helper('catalog/image')->init($_product, 'thumbnail', $image->getFile())->setQuality(80)->resize(120,120),
                'thumbx2'    => (string)$this->helper('catalog/image')->init($_product, 'thumbnail', $image->getFile())->setQuality(80)->resize(285,285),
                'view'     => (string)$this->helper('catalog/image')->init($_product, 'small_image', $image->getFile())->setQuality(80)->resize(570,570),
                'viewx2'     => (string)$this->helper('catalog/image')->init($_product, 'image', $image->getFile())->setQuality(80)->resize(1140,1140),
                'orig'     => (string)$this->helper('catalog/image')->init($_product, 'image', $image->getFile())->setQuality(80),
                'alt'      => $this->htmlEscape($image->getLabel() ? $image->getLabel() : $this->getImageLabel())
            );
        }
    }
?>

<?php if (count($gallery) < 2): ?>
    <div class="gallery-main-images one-image">
        <a class="main-image" href="<?php echo (string)$this->helper('catalog/image')->init($_product, 'image')->setQuality(80) ?>" title="<?php echo $this->getImageLabel() . ' - ' . $productImageSufix ?>">
            <img
                src="<?php echo $this->helper('catalog/image')->init($_product, 'small_image')->setQuality(80)->resize(570,570); ?>"
                srcset="<?php echo $this->helper('catalog/image')->init($_product, 'image')->setQuality(80)->resize(1140,1140); ?> 2x"
                alt="<?php echo $this->getImageLabel() ?>"
                title="<?php echo $this->getImageLabel() . ' - ' . $productImageSufix ?>"
            >
        </a>
    </div>
<?php else: ?>
    <div class="gallery-main-images">
        <?php $i = 1; foreach ($gallery as $image): ?>
            <a class="main-image" href="<?php echo $image['orig'] ?>" title="<?php echo $image['alt'] . ' - ' . $i . ' - ' . $productImageSufix ?>">
                <img
                    src="<?php echo $image['view'] ?>"
                    srcset="<?php echo $image['viewx2'] ?> 2x"
                    alt="<?php echo $image['alt'] . ' - ' . $i ?>"
                    title="<?php echo $image['alt'] . ' - ' . $i . ' - ' . $productImageSufix ?>"
                >
            </a>
            <?php $i++; ?>
        <?php endforeach ?>
    </div>
<?php endif; ?>

<?php if (count($gallery) > 1): ?>
    <div class="gallery-thumbnails">
        <?php $i = 1; foreach ($gallery as $image): ?>
            <div class="thumb">
                <img
                    src="<?php echo $image['thumb'] ?>"
                    srcset="<?php echo $image['thumbx2'] ?> 2x"
                    alt="<?php echo $image['alt'] . ' - ' . $i ?>t"
                    title="<?php echo $image['alt'] . ' - ' . $i . 't - ' . $productImageSufix ?>"
                    data-label="<?php echo $this->escapeHtml(trim($image['alt'])) ?>"
                >
            </div>
            <?php $i++; ?>
        <?php endforeach ?>
    </div>
<?php endif; ?>

<div style="display:none;">
    <img
        itemprop="image"
        src="<?php echo $this->helper('catalog/image')->init($_product, 'small_image')->resize(285,285); ?>"
        srcset="<?php echo $this->helper('catalog/image')->init($_product, 'small_image')->resize(570,570); ?> 2x"
        alt="<?php echo $this->htmlEscape($this->getImageLabel()); ?>"
    >
</div>


<script>
    jQuery(function($){

        $('.gallery-main-images').slick({
            initialSlide: <?php echo $initialSlide; ?>,
            slidesToShow: 1,
            slidesToScroll: 1,
            autoplay: false,
            arrows: false,
            fade: true,
            asNavFor: '.gallery-thumbnails',
            responsive: [
                {
                  breakpoint: 768,
                  settings: {
                    dots: true
                  }
                }
            ]
        });

        <?php if (count($gallery) > 1): ?>
            $('.gallery-thumbnails').slick({
                slidesToShow: 5,
                slidesToScroll: 1,
                dots: false,
                arrows: true,
                focusOnSelect: true,
                vertical: true,
                verticalSwiping: true,
                asNavFor: '.gallery-main-images',
                responsive: [
                    {
                      breakpoint: 992,
                      settings: {
                        slidesToShow: 4,
                        vertical: false,
                        verticalSwiping: false,
                        arrows: false
                      }
                    }
                ]
            });
        <?php endif; ?>

        $(".gallery-main-images a").colorbox({ rel: 'gal', maxWidth: '90%', maxHeight: '90%', current: "{current}/{total}"});

    });
</script>

