<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2010 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

/**
 * Product additional attributes template
 *
 * @see Mage_Catalog_Block_Product_View_Attributes
 */
?>
<?php
    $_helper = $this->helper('catalog/output');
    $_product = $this->getProduct()
?>
<?php if($_additional = $this->getAdditionalData()): ?>
    <table class="stylized attributes">
        <?php foreach ($_additional as $_data): ?>
            <?php if ($_data['value']!='' && $_data['value']!='No' && $_data['value']!='N/A' && $_data['value']!='Не'):?>
                <tr>
                    <td><?php echo $this->htmlEscape($this->__($_data['label'])) ?></td>
                    <td><?php echo $_helper->productAttribute($_product, $_data['value'], $_data['code']) ?></td>
                </tr>
            <?php endif; ?>
        <?php endforeach; ?>
    </table>
<?php endif;?>
