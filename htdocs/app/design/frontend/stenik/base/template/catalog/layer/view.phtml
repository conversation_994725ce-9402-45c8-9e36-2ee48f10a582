<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2014 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * Category layered navigation
 *
 * @see Mage_Catalog_Block_Layer_View
 */
?>

<?php if($this->canShowBlock()): ?>

    <div class="block block-layered-nav<?php if (!$this->getLayer()->getState()->getFilters()): ?> block-layered-nav--no-filters<?php endif; ?>">

        <?php echo $this->getStateHtml() ?>

        <?php if($this->canShowOptions()): ?>

            <?php
                $_filters = $this->getFilters();
                $counter = 0;
                $count = count($_filters);
            ?>
            
            <?php foreach ($_filters as $_filter): ?>
                <?php $counter++; ?>
                <?php
                    if (!$this->getShowCategoryFilter() &&
                        in_array($_filter->getType(), array('catalog/layer_filter_category', 'Mage_Catalog_Block_Layer_Filter_Category'))
                    ) {
                        continue;
                    }
                ?>
                <?php if($_filter->getItemsCount()): ?>
                    <?php
                        $attributeCode = null;
                        if ($_filter->getAttributeModel()) {
                            $attributeCode = $_filter->getAttributeModel()->getAttributeCode();
                        }
                    ?>
                    <div class="drop-down multiselect <?php echo $attributeCode; ?>">
                        <a href="javascript:;" class="open-item"><?php echo $this->__($_filter->getName()) ?></a>
                        <?php echo $_filter->getHtml() ?>
                    </div>
                <?php endif; ?>
            <?php endforeach; ?>

        <?php endif; ?>
        
    </div>

<?php endif; ?>