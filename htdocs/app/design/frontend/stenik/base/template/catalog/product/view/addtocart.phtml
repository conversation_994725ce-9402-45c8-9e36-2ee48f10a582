<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2014 X.commerce, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<?php
    $_product = $this->getProduct();
    $_wishlistSubmitUrl = $this->helper('wishlist')->getAddUrl($_product);
    $_compareUrl = $this->helper('catalog/product_compare')->getAddUrl($_product);
?>

<?php if($_product->isSaleable()): ?>
    <?php if(!$_product->isGrouped()): ?>
        <input type="text" name="qty" id="qty" value="1" title="<?php echo $this->__('Qty') ?>" class="qty hidden-qty" readonly />
    <?php endif; ?>

    <div class="clearH"></div>
    
    <a class="button checkout-color add-to-cart" href="javascript:;" onclick="productAddToCartForm.submit(this)">
        <svg aria-hidden="true" class="icon-svg shopping-cart"><use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#shopping-cart' ?>"></use></svg>
        <?php echo $this->__('Add to Cart'); ?>
    </a>

    <?php echo $this->getChildHtml('', true, true) ?>
<?php endif; ?>


