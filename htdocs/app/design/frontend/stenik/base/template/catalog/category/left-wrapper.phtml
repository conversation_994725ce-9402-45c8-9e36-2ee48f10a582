
<a href="javascript:;" class="button open-responsive-sidebar" id="button-responsive-sidebar">
    <?php echo $this->__('Filter by');?>
    <span id="countContainer">
        <span id="filtersCount">
            <?php
                if ($leftNavBlock = $this->getChild('catalog.leftnav')) {
                    if ($stateBlock = $leftNavBlock->getChild('layer_state')) {
                        $activeFilters = $stateBlock->getActiveFilters();
                        if (count($activeFilters)) {
                            echo '(' . count($activeFilters) . ')';
                        }
                    }
                }

            ?>
        </span>
    </span>
</a>

<div class="sidebar-responsive-wrapper background-color1">
    <?php echo $this->getChildHtml('catalog.leftnav.categories'); ?>
    <?php echo $this->getChildHtml('catalog.leftnav'); ?>
    <?php echo $this->getChildHtml('catalogsearch.leftnav'); ?>
</div>


