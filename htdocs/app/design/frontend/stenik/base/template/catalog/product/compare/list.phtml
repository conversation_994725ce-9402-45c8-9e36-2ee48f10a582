<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2016 X.commerce, Inc. and affiliates (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/* @var $this Mage_Catalog_Block_Product_Compare_List */
?>

<h1><?php echo $this->__('Compare Products') ?></h1>

<?php $_total=$this->getItems()->getSize() ?>

<?php if($_total): ?>
    <a href="javascript:;" class="button white-background more-items-for-compare" onclick="window.history.back(); return false;"><?php echo $this->__('Add more items to compare') ?></a>

    <div class="compare-table-wrapper">
        <table class="compare-table">

            <tr>
                <?php $_i=0 ?>
                <?php foreach($this->getItems() as $_item): ?>
                    <?php if($_i++%10==0): ?>
                        <th><?php echo $this->__('Product') ?></th>
                    <?php endif; ?>
                    <td>
                        <a class="compare-img" href="javascript:;" onclick="setPLocation('<?php echo $this->getProductUrl($_item) ?>', true)" title="<?php echo $this->stripTags($_item->getName(), null, true) ?>">
                            <img
                                src="<?php echo $this->helper('catalog/image')->init($_item, 'small_image')->resize(120, 120); ?>"
                                srcset="<?php echo $this->helper('catalog/image')->init($_item, 'small_image')->resize(285, 285); ?> 2x"
                                alt="<?php echo $this->stripTags($_item->getName(), null, true) ?>"
                            >
                        </a>
                        <a class="title" href="#" onclick="setPLocation('<?php echo $this->getProductUrl($_item) ?>', true)" title="<?php echo $this->stripTags($_item->getName(), null, true) ?>">
                            <?php echo $this->helper('catalog/output')->productAttribute($_item, $_item->getName(), 'name') ?>
                        </a>
                        <a href="<?php echo $this->helper('catalog/product_compare')->getRemoveUrl($_item) ?>" class="item-remove" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Remove')) ?>">
                            <svg aria-hidden="true" class="icon-svg close"><use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#close' ?>"></use></svg>
                        </a>
                    </td>
                <?php endforeach; ?>
            </tr>

            <?php foreach ($this->getAttributes() as $_attribute): ?>
                <tr>
                    <?php $_i=0 ?>
                    <?php foreach($this->getItems() as $_item): ?>
                        <?php if($_i++%10==0): ?>
                            <th><?php echo $_attribute->getStoreLabel() ?></th>
                        <?php endif; ?>
                            <td>
                                <?php switch ($_attribute->getAttributeCode()) {
                                    case "price": ?>
                                        <?php echo $this->getPriceHtml($_item, true, '-compare-list-' . $_attribute->getCode()) ?>
                                        <?php break;
                                    case "small_image": ?>
                                        <img src="<?php echo $this->helper('catalog/image')->init($_item, 'small_image')->resize(125, 125); ?>" width="125" height="125" alt="<?php echo $this->escapeHtml($_item->getName()) ?>" title="<?php echo $this->escapeHtml($_item->getName()) ?>" />
                                        <?php break;
                                    case "date":
                                          echo substr($this->getProductAttributeValue($_item, $_attribute),0,10);
                                          break;
                                    default: ?>
                                        <div class="std">
                                            <?php echo $this->helper('catalog/output')->productAttribute($_item, $this->getProductAttributeValue($_item, $_attribute), $_attribute->getAttributeCode()) ?>
                                        </div>
                                        <?php break;
                                } ?>
                            </td>
                    <?php endforeach; ?>
                </tr>
            <?php endforeach; ?>

            <tr>
                <?php $_i=0 ?>
                <?php foreach($this->getItems() as $_item): ?>
                    <?php if($_i++%10==0): ?>
                        <th>&nbsp;</th>
                    <?php endif; ?>
                    <td>
                        <?php if($_item->isSaleable()): ?>
                            <a href="<?php echo $this->helper('checkout/cart')->getAddUrl($_item); ?>" class="button checkout-color">
                                <svg aria-hidden="true" class="icon-svg shopping-cart"><use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#shopping-cart' ?>"></use></svg>
                                <?php echo $this->__('Buy') ?>
                            </a>
                        <?php else: ?>
                            <p class="availability out-of-stock"><span><?php echo $this->__('Out of stock') ?></span></p>
                        <?php endif; ?>
                    </td>
                <?php endforeach; ?>
            </tr>
            <tr>
                <?php $_i=0 ?>
                <?php foreach($this->getItems() as $_item): ?>
                    <?php if($_i++%10==0): ?>
                        <th>&nbsp;</th>
                    <?php endif; ?>
                    <td>
                        <a href="<?php echo $this->getProductUrl($_item) ?>" class="view-more"><?php echo $this->__('See detailed presentation') ?></a>  
                    </td>
                <?php endforeach; ?>
            </tr>
        </table>
    </div>

    <script>
        decorateTable('product_comparison');
        /**
         * Send remove item request, after that reload windows
         */
        function removeItem(url)
        {
            new Ajax.Request(url, {
                parameters: {isAjax: 1, method: 'POST'},
                onLoading: function(){$('compare-list-please-wait').show();},
                onSuccess: function(transport) {
                    $('compare-list-please-wait').hide();
                    window.location.reload();
                    window.opener.location.reload();
                }
            });
        }
    </script>

<?php else: ?>
    <script type="text/javascript">window.close();</script>
<?php endif; ?>
