<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2017 X.commerce, Inc. and affiliates (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

/**
 * Grouped product data template
 *
 * @see Mage_Catalog_Block_Product_View_Media
 * @see Mage_Catalog_Block_Product_View_Type_Grouped
 */
?>
<?php $this->setPreconfiguredValue(); ?>
<?php $_product = $this->getProduct(); ?>
<?php $_associatedProducts = $this->getAssociatedProducts(); ?>
<?php $_hasAssociatedProducts = count($_associatedProducts) > 0; ?>

<?php echo $this->getChildHtml('product_type_data_extra') ?>

<div class="clearH"></div>

<table class="data-table grouped-items-table" id="super-product-table">
    <thead>
        <tr>
            <th><?php echo $this->__('Product Name') ?></th>
            <?php if ($this->getCanShowProductPrice($_product)): ?>
            <th class="a-right"><?php echo $this->__('Price') ?></th>
            <?php endif; ?>
            <?php if ($_product->isSaleable()): ?>
            <th class="a-center"><?php echo $this->__('Qty') ?></th>
            <?php endif; ?>
        </tr>
    </thead>

    <tbody>
        <?php if ($_hasAssociatedProducts): ?>
            <?php foreach ($_associatedProducts as $_item): ?>
                <?php $_finalPriceInclTax = $this->helper('tax')->getPrice($_item, $_item->getFinalPrice(), true) ?>
                <tr>
                    <td><?php echo $this->escapeHtml($_item->getName()) ?></td>
                    <?php if ($this->getCanShowProductPrice($_product)): ?>
                    <td class="a-right">
                        <?php if ($this->getCanShowProductPrice($_item)): ?>
                        <?php echo $this->getPriceHtml($_item, true) ?>
                        <?php echo $this->getTierPriceHtml($_item) ?>
                        <?php endif; ?>
                    </td>
                    <?php endif; ?>
                    <?php if ($_product->isSaleable()): ?>
                    <td class="a-center">
                    <?php if ($_item->isSaleable()) : ?>
                        <input type="text" name="super_group[<?php echo $_item->getId() ?>]" maxlength="12" value="<?php echo $_item->getQty()*1 ?>" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Qty')) ?>" class="input-text qty" />
                    <?php else: ?>
                        <p class="availability out-of-stock"><span><?php echo $this->__('Out of stock') ?></span></p>
                    <?php endif; ?>
                    </td>
                    <?php endif; ?>
                </tr>
            <?php endforeach; ?>
        <?php else: ?>
           <tr>
               <td colspan="<?php if ($_product->isSaleable()): ?>4<?php else : ?>3<?php endif; ?>"><?php echo $this->__('No options of this product are available.') ?></td>
           </tr>
        <?php endif; ?>
    </tbody>
</table>

