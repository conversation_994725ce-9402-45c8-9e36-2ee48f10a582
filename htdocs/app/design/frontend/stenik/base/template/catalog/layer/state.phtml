<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2014 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * Category layered navigation state
 *
 * @see Mage_Catalog_Block_Layer_State
 */
?>
<?php $_filters = $this->getActiveFilters() ?>

<?php if(!empty($_filters)): ?>
    <div class="state-content">

        <span class="state-title"><?php echo $this->__('Active filters');?></span>

        <?php $lastShowedH3 = null; ?>
        <?php $filterCounter = 0; ?>
        <?php $filterCount = count($_filters); ?>


        <?php foreach ($_filters as $_filter): ?>
            <?php $filterCounter++ ?>

            <?php $openFilterBox      = (bool) (!$_filter->getName() || $_filter->getName() != $lastShowedH3); ?>
            <?php $closePrevFilterBox = (bool) ($filterCounter > 1 && (!$_filter->getName() || $_filter->getName() != $lastShowedH3)); ?>
            <?php $closeFilterBox     = (bool) ($filterCounter == $filterCount); ?>

            <?php if ($closePrevFilterBox): ?>
                </div>
            <?php endif ?>

            <?php if ($openFilterBox): ?>
                <div class="state-filter">
                   <span class="filter-label"><?php echo $this->__($_filter->getName()) ?>:</span>
            <?php endif ?>

                <a class="filter" href="<?php echo $_filter->getRemoveUrl() ?>">
                    <?php echo $this->stripTags($_filter->getLabel()) ?>
                </a>

            <?php if ($closeFilterBox): ?>
                </div>
            <?php endif ?>

            <?php if($_filter->getName() != $lastShowedH3) $lastShowedH3 = $_filter->getName(); ?>
        <?php endforeach; ?>        
           
        <?php if ($this->getLayer()->getState()->getFilters()): ?>
            <a href="<?php echo $this->getClearUrl() ?>" class="remove-all-filters" title="<?php echo $this->__('Clear all') ?>"><?php echo $this->__('Clear all') ?></a>
        <?php endif; ?>

    </div>
<?php endif; ?>