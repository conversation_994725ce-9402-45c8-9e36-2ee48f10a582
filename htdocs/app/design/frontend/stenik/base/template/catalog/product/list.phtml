<?php
/**
 * Product list template
 *
 * @see Mage_Catalog_Block_Product_List
 */
?>
<?php
    $_productCollection=$this->getLoadedProductCollection();
    $_helper = $this->helper('catalog/output');
    $productImageSufix = $this->helper('stenik_sitesettings')->getproductImagesAltSufix();
?>

<?php if(!$_productCollection->count()): ?>
    <p class="note-msg"><?php echo $this->__('There are no products in this category') ?></p>
<?php else: ?>
    <?php $_collectionSize = $_productCollection->count() ?>
    <?php $_columnCount = $this->getColumnCount(); ?>
    <?php $counter = 0; ?>

    <div class="category-products">
        <div class="row products-list <?php echo $this->getMode(); ?>-mode" id="products-list">
            <?php echo $this->getToolbarHtml() ?>
            <?php if($this->getMode()!='grid'): ?>
                <?php foreach ($_productCollection as $_product): ?>

                    <?php $secondImg = ($this->helper('stenik_sitesettings')->getProductListingSecondImg() ? Mage::helper('stenik_base/catalog_product')->getSecondImageFile($_product) : null) ?>

                    <div class="col-xs-12">
                        <a class="product-box" href="<?php echo $_product->getProductUrl() ?>">
                            <span class="image-wrapper<?php if ($secondImg): ?> has-second-img<?php endif; ?>">
                                <img
                                    class="first"
                                    src="<?php echo $this->helper('catalog/image')->init($_product, 'small_image')->resize(285, 285); ?>"
                                    srcset="<?php echo $this->helper('catalog/image')->init($_product, 'small_image')->resize(570, 570); ?> 2x"
                                    alt="<?php echo $this->stripTags($this->getImageLabel($_product, 'small_image'), null, true) ?>"
                                    title="<?php echo $this->stripTags($this->getImageLabel($_product, 'small_image'), null, true)  . ' - ' . $productImageSufix ?>"
                                >
                                <?php if ($secondImg): ?>
                                    <img
                                        class="second"
                                        src="<?php echo $this->helper('catalog/image')->init($_product, 'small_image', $secondImg)->resize(285, 285); ?>"
                                        srcset="<?php echo $this->helper('catalog/image')->init($_product, 'small_image', $secondImg)->resize(570, 570); ?> 2x"
                                        alt="<?php echo $this->stripTags($this->getImageLabel($_product, 'small_image'), null, true) ?> - 2"
                                        title="<?php echo $this->stripTags($this->getImageLabel($_product, 'small_image'), null, true)  . ' - 2 - ' . $productImageSufix ?>"
                                    >
                                <?php endif; ?>
                                <?php echo Mage::app()->getLayout()->getBlock('product.labels')->setProduct($_product)->toHtml(); ?>
                            </span>
                            <div class="product-info">
                                <span class="title"><?php echo $_helper->productAttribute($_product, $_product->getName(), 'name') ?></span>                            
                                
                                <?php $confOptions = Mage::helper('stenik_base/catalog_product')->getProductConfigurableAttributeOptions($_product, 'color'); ?>

                                <?php if ($count = count($confOptions)): ?>
                                    <span class="attributes">
                                        <?php echo $this->escapeHtml(Mage::helper('stenik_base/catalog_product')->getAttributeStoreLabel('color')); ?>:
                                        <?php $counter = 0 ?>
                                        <?php foreach ($confOptions as $option): $counter++; ?>
                                            <span class="<?php if (!$option->getIsSalable()): ?>disabled<?php endif ?>"><?php echo $this->escapeHtml($option->getLabel());?></span><?php if ($counter != $count): ?>, <?php endif ?>
                                         <?php endforeach ?>
                                    </span>
                                <?php endif ?>

                                <span class="short-description"><?php echo $_helper->productAttribute($_product, $_product->getShortDescription(), 'short_description') ?></span>
                            </div>
                            <div class="price-actions-col">
                                <?php echo $this->getPriceHtml($_product, true) ?>
                                <?php echo $this->getReviewsSummaryHtml($_product) ?>
                                <span class="actions">
                                    <?php if($_product->isSaleable() && $_product->getStockItem()->getIsInStock()): ?>
                                        <span class="button checkout-color add-to-cart" onclick="document.location.href='<?php echo $this->getAddToCartUrl($_product) ?>'; return false">
                                            <svg aria-hidden="true" class="icon-svg shopping-cart">
                                                <use xlink:href="images/svg-sprite.svg#shopping-cart"></use>
                                            </svg>
                                            <?php echo $this->__('Add to Cart');?>
                                        </span>
                                    <?php endif; ?>
                                    <?php if ($this->helper('wishlist')->isAllow()) : ?>
                                        <span class="button grey-background" onclick="document.location.href='<?php echo $this->helper('wishlist')->getAddUrl($_product) ?>'; return false">
                                            <svg aria-hidden="true" class="icon-svg wishlist">
                                                <use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#wishlist' ?>"></use>
                                            </svg>
                                            <?php echo $this->__('Add to Wishlist');?>
                                        </span>
                                    <?php endif; ?>
                                    <?php if($_compareUrl=$this->getAddToCompareUrl($_product)): ?>
                                        <span class="button grey-background" onclick="document.location.href='<?php echo $_compareUrl ?>'; return false">
                                            <svg aria-hidden="true" class="icon-svg compare">
                                                <use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#compare' ?>"></use>
                                            </svg>
                                            <?php echo $this->__('Add to Compare');?>
                                        </span>
                                    <?php endif; ?>
                                </span>
                            </div>
                        </a>
                    </div>
                <?php endforeach ?>
            <?php else: ?>
                <?php foreach ($_productCollection as $_product): ?>
                    
                    <?php $secondImg = ($this->helper('stenik_sitesettings')->getProductListingSecondImg() ? Mage::helper('stenik_base/catalog_product')->getSecondImageFile($_product) : null) ?>

                    <div class="col-xs-3 col-sm-4">
                        <a class="product-box" href="<?php echo $_product->getProductUrl() ?>">
                            <span class="image-wrapper<?php if ($secondImg): ?> has-second-img<?php endif; ?>">
                                <img
                                    class="first"
                                    src="<?php echo $this->helper('catalog/image')->init($_product, 'small_image')->resize(285, 285); ?>"
                                    srcset="<?php echo $this->helper('catalog/image')->init($_product, 'small_image')->resize(570, 570); ?> 2x"
                                    alt="<?php echo $this->stripTags($this->getImageLabel($_product, 'small_image'), null, true) ?>"
                                    title="<?php echo $this->stripTags($this->getImageLabel($_product, 'small_image'), null, true)  . ' - ' . $productImageSufix ?>"
                                >

                                <?php if ($secondImg): ?>
                                    <img
                                        class="second"
                                        src="<?php echo $this->helper('catalog/image')->init($_product, 'small_image', $secondImg)->resize(285, 285); ?>"
                                        srcset="<?php echo $this->helper('catalog/image')->init($_product, 'small_image', $secondImg)->resize(570, 570); ?> 2x"
                                        alt="<?php echo $this->stripTags($this->getImageLabel($_product, 'small_image'), null, true) ?> - 2"
                                        title="<?php echo $this->stripTags($this->getImageLabel($_product, 'small_image'), null, true)  . ' - 2 - ' . $productImageSufix ?>"
                                    >
                                <?php endif; ?>
                                
                                <?php echo Mage::app()->getLayout()->getBlock('product.labels')->setProduct($_product)->toHtml(); ?>
                                <span class="actions">
                                    <?php if($_product->isSaleable() && $_product->getStockItem()->getIsInStock()): ?>
                                        <span class="button checkout-color add-to-cart" onclick="document.location.href='<?php echo $this->getAddToCartUrl($_product) ?>'; return false">
                                            <?php echo $this->__('Add to Cart');?>
                                        </span>
                                    <?php endif; ?>
                                    <?php if ($this->helper('wishlist')->isAllow()) : ?>
                                        <span class="icon-link" onclick="document.location.href='<?php echo $this->helper('wishlist')->getAddUrl($_product) ?>'; return false">
                                            <svg aria-hidden="true" class="icon-svg wishlist"><use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#wishlist' ?>"></use></svg>
                                        </span>
                                    <?php endif; ?>
                                    <?php if($_compareUrl=$this->getAddToCompareUrl($_product)): ?>
                                        <span class="icon-link" onclick="document.location.href='<?php echo $_compareUrl ?>'; return false">
                                            <svg aria-hidden="true" class="icon-svg compare"><use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#compare' ?>"></use></svg>
                                        </span>
                                    <?php endif; ?>
                                </span>
                            </span>

                            <?php $confOptions = Mage::helper('stenik_base/catalog_product')->getProductConfigurableAttributeOptions($_product, 'color'); ?>

                            <?php if ($count = count($confOptions)): ?>
                                 <span class="attributes">
                                     <?php echo $count; ?> <?php echo $this->__($count == 1 ? 'color' : 'colors'); ?>
                                 </span>
                            <?php endif ?>

                            <span class="title"><?php echo $_helper->productAttribute($_product, $_product->getName(), 'name') ?></span>
                            <?php echo $this->getPriceHtml($_product, true) ?>
                        </a>
                    </div>
                <?php endforeach ?>
            <?php endif; ?>
            <?php echo $this->getToolbarHtml() ?>
        </div>
    </div>

<?php endif; ?>