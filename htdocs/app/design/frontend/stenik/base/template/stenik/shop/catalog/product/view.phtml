<?php
/**
 * @package Stenik_Shop
 * <AUTHOR> Magento Team <<EMAIL>>
 */
?>

<?php
    $_product = Mage::registry('product');
    $attributevalue = $_product->getData('shop');
    $optionIds = explode(",", $attributevalue);


    $shopCollection = $this->getShopCollection();
    $shopCount      = $shopCollection->getSize();

    $cities = array();
    foreach ($shopCollection as $shop) {
        if (!in_array($shop->getCity(), $cities))
            $cities[] = $shop->getCity();
    }

?>

<?php if ($this->getChildHtml('shopListingText')): ?>
    <div class="shopsDescription">
        <div class="textPage">
            <?php echo $this->getChildHtml('shopListingText'); ?>
        </div>
    </div>
<?php endif ?>


<div class="shopsListingWrapper">
    <?php if ($shopCount): ?>

        <script type="text/javascript" src="https://maps.googleapis.com/maps/api/js?v=3.25&amp;key=AIzaSyAmZTitTy2nZRsVaSFryJnIptStHv5VsNg"></script>

        <script>
            function initialize() {
                <?php
                    $shopsData = array();
                    foreach ($shopCollection as $shop) {
                        if (!in_array($shop->getId(), $optionIds)) continue;{
                            $shopsData[] = array(
                                'id' => $shop->getId(),
                                'title' => $shop->getName(),
                                'latitude' => $shop->getLatitude(),
                                'longitude' => $shop->getLongitude(),
                            );
                        }
                    }
                ?>

                var shopsData = <?php echo json_encode($shopsData); ?>;

                if (!shopsData.length) {
                    return;
                }


                var mapOptions = {
                    center: new google.maps.LatLng(shopsData[0].latitude, shopsData[0].longitude),
                    scrollwheel: false,
                    zoom: 8
                };
                var map = new google.maps.Map(document.getElementById('gmapContent'), mapOptions);

                map.setCenter({lat: parseFloat(shopsData[0].latitude), lng: parseFloat(shopsData[0].longitude)});


                var mapType = new google.maps.StyledMapType(
                    [
                        {
                          featureType: "all",
                          elementType: "all",
                          stylers: [
                            { saturation: -100 }
                          ]
                        }
                    ], { name:"Grayscale" });
                map.mapTypes.set('grayscale', mapType);
                map.setMapTypeId('grayscale');


                for (var i = shopsData.length - 1; i >= 0; i--) {
                    (function() {

                        var image = '<?php echo Mage::helper('stenik_shop/shop')->getMarkerIconUrl() ?>';
                        var shopData = shopsData[i];
                        shopData.marker = new google.maps.Marker({
                            position: new google.maps.LatLng(shopData.latitude, shopData.longitude),
                            map: map,
                            animation: google.maps.Animation.DROP,
                            title: shopData.title,
                            icon: image
                        });

                        if (shopData.latitude != 0 || shopData.longitude != 0) {
                            jQuery('.shopsListing .shopsBox[data-shop="' + shopData.id + '"] a.viewOnMap').click(function(e) {
                                jQuery('.shopsListing .shopsBox').removeClass('selected');
                                jQuery(this).addClass('selected');

                                map.panTo(shopData.marker.getPosition());
                                map.setZoom(18);
                                map.setCenter({lat: parseFloat(shopData.latitude), lng: parseFloat(shopData.longitude)});
                            });
                        }
                    })();
                }
            }
            google.maps.event.addDomListener(window, 'load', initialize);
        </script>

         <script>
            jQuery(function($){
                $('.storesListing .filterBox').each(function() {
                    $(this).on('openselect', function(){

                        $('.subOptions').stop(true, true).hide();
                        $(this).removeClass("open");

                        $('.subOptions', this).show(0, function(){
                            //$(this).jScrollPane({showArrows: false});
                        });
                        $(this).addClass("open");
                    });

                    $(this).on('closeselect', function(){
                        $('.subOptions', this).stop(true, true).hide();
                        $(this).removeClass("open");
                    });

                    if ($(this).hasClass('openonclick')) {
                        $(this).click(function() {
                            if ($('.subOptions', this).is(':visible')) {
                                $(this).trigger('closeselect');
                            } else {
                                if (!$(this).hasClass('disabled')) {
                                    $(this).trigger('openselect');
                                }
                            }
                        });
                    } else {
                        $(this).hover(function() {
                            if (!$(this).hasClass('disabled')) {
                                $(this).trigger('openselect');
                            }
                        }, function() {
                            $(this).trigger('closeselect');
                        });
                    }
                });

                $('.shopsListing .filterBox').each(function(){
                    var $sortOptions = $(this);
                    $sortOptions.find('.subOptions a').click(function(){
                        var $this = $(this);
                        $this.parents('.filterBox').first().find('.openFilters').html($this.html());
                        var cityId = $this.data('shop-city-id');
                        var $cityShops = $('.shopsListing .shopsBox[data-shop-city-id="' + cityId + '"]');
                        $('.shopsListing .shopsBox').not($cityShops).hide();
                        $cityShops.show();
                        $cityShops.first().click();
                    });
                });

                $('.shopsListing .filterBox').trigger('closeselect');

                if ($(window).width() < 1025) {
                    $(".viewOnMap").on("click", function (e) {
                        e.preventDefault();
                        $("body, html").animate({ scrollTop: $('#gmapContent').offset().top - 50 }, 1000);

                    });
                }

                $('.responsiveBackToTop').click(function() {
                    $('body,html').animate({scrollTop:0},1000);
                });

            });
        </script>

        <div class="shopsListing">
        <?php if (!empty($optionIds)): ?>
            <?php foreach ($optionIds as $optionId): ?>
                <?php $shop = $shopCollection->getItemById($optionId); ?>
                    <?php if ($optionId == $shop->getId()): ?>
                        <div class="shopsBox" data-shop="<?php echo $shop->getId(); ?>" data-shop-city-id="<?php echo md5($shop->getCity()) ?>">
                            <div class="textPage">
                                <a class="title" href="<?php echo $shop->getUrl() ?>"><?php echo $shop->getName() ?></a>
                                <p><?php echo $shop->getCity() ?>, <?php echo $shop->getAddress() ?></p>
                                <p><?php echo $this->__('Telephone');?>: <?php echo $shop->getMobilePhone() ?></p>
                                <p>
                                    <a class="viewOnMap" href="javascript:;"><?php echo $this->__('View on map');?></a>
                                    <a class="viewMore" href="<?php echo $shop->getUrl() ?>"><?php echo $this->__('View more');?></a>
                                </p>
                            </div>
                        </div>
                    <?php endif ?>
            <?php endforeach ?>
        <?php endif ?>
        </div>

        <div class="shopsListingGoogleMap">
            <div id="gmapContent"></div>
        </div>

    <?php else: ?>

        <div class="noShopsFound"><?php echo $this->__('No shops found') ?></div>

    <?php endif; ?>
</div>


<?php if ($shopCount): ?>
    <script>
        (function(){
            var container = jQuery('.shopsListing');
            var storeListingWidth = container.find('.shopsListing').first().width();
            var storeBoxOuterWidth = container.find('.shopsListing .shopsBox').first().outerWidth();

            var currentFilters = {};

            function storeBoxShowFromList(jqElements, filterCode) {

                var elementsToHide = container.find('.shopsListing .shopsBox:visible').not(jqElements);
                if (elementsToHide.length) {
                    jQuery.when(
                        elementsToHide.css({opacity: 0, visibility: 'hidden'}).delay(350-10).hide(0)
                    ).then(function(){
                        jQuery.when(
                            jqElements.show(0).css({opacity: 1, visibility: 'visible'}).delay(10)
                        ).then(function() {
                            if (jqElements.length)
                                container.find('.noShopsFound').hide();
                            else container.find('.noShopsFound').show();
                        });
                    });
                } else {
                    jQuery.when(
                        jqElements.show(0).css({opacity: 1, visibility: 'visible'})
                    ).then(function(){
                        if (jqElements.length)
                            container.find('.noShopsFound').hide();
                        else container.find('.noShopsFound').show();
                    });
                }

            }

        })();
    </script>
<?php endif; ?>
