<?php
/**
 * @package Stenik_Shop
 * <AUTHOR> Magento Team <<EMAIL>>
 */
?>

<?php
    $shopCollection = $this->getShopCollection();
    $shopCount      = $shopCollection->getSize();

    $cities = array();
    foreach ($shopCollection as $shop) {
        if (!in_array($shop->getCity(), $cities))
            $cities[] = $shop->getCity();
    }

?>

<h1><?php echo Mage::helper('checkout')->__('Shops') ?></h1>

<div class="shops-listing-wrapper">
    <div class="row">
        <?php if ($shopCount): ?>

            <script type="text/javascript" src="https://maps.googleapis.com/maps/api/js?v=3&amp;key=<?php echo $this->helper('stenik_sitesettings')->getGoogleMapsKey() ?>"></script>

            <script>
                function initialize() {
                    <?php
                        $shopsData = array();
                        foreach ($shopCollection as $shop) {
                            $shopsData[] = array(
                                'id'        => $shop->getId(),
                                'title'     => $shop->getName(),
                                'latitude'  => $shop->getLatitude(),
                                'longitude' => $shop->getLongitude(),
                                'address'   => $shop->getAddress(),
                                'phone'     => $shop->getPhone(),
                                'mobile'    => $shop->getMobilePhone(),
                                'email'     => $shop->getEmail(),
                                'workTime'  => $shop->getBusinessHoursAsText(),
                                'url'       => $shop->getUrl(),
                            );
                        }
                    ?>

                    var shopsData = <?php echo json_encode($shopsData); ?>;
                    window.stenikShopShopsData = shopsData;

                    if (!shopsData.length) {
                        return;
                    }

                    var markers = [];

                    var mapOptions = {
                        center: new google.maps.LatLng(shopsData[0].latitude, shopsData[0].longitude),
                        scrollwheel: false,
                        zoom: 8
                    };
                    var map = new google.maps.Map(document.getElementById('gmap'), mapOptions);
                    window.stenikShopMap = map;
                    map.setCenter({lat: parseFloat(shopsData[0].latitude), lng: parseFloat(shopsData[0].longitude)});

                    var mapType = new google.maps.StyledMapType(
                        [
                            {
                              featureType: "all",
                              elementType: "all",
                              stylers: [
                                { saturation: -100 }
                              ]
                            }
                        ], { name:"Grayscale" });
                    map.mapTypes.set('grayscale', mapType);
                    map.setMapTypeId('grayscale');

                    var latlngbounds = new google.maps.LatLngBounds();

                    for (var i = shopsData.length - 1; i >= 0; i--) {
                        (function() {

                            var image = '<?php echo Mage::helper('stenik_shop/shop')->getMarkerIconUrl() ?>';
                            var shopData = shopsData[i];

                            shopData.title = shopData.title || "";
                            shopData.phone = shopData.phone || "";
                            shopData.mobile = shopData.mobile || "";
                            shopData.email = shopData.email || "";

                            var contentString = '<div class="mapInfo"><span class="shopName">' + shopData.title +
                                                '<br /><span class="shopDesc"><strong><?php echo $this->__('Telephone') ?>: </strong> ' + shopData.phone +
                                                '<br /><strong><?php echo $this->__('Mobile phone') ?>: </strong> ' + shopData.mobile +
                                                '<br /><strong><?php echo $this->__('Email') ?>: </strong>' + shopData.email +
                                                '<br /><a class="view-more" href="' + shopData.url + '"><?php echo $this->__('View more') ?> »</a>' +
                                                '</span></div>'

                            var infowindow = new google.maps.InfoWindow({
                                content: contentString
                            });

                            shopData.marker = new google.maps.Marker({
                                position: new google.maps.LatLng(shopData.latitude, shopData.longitude),
                                map: map,
                                animation: google.maps.Animation.DROP,
                                title: shopData.title,
                                phone: shopData.phone,
                                email: shopData.email,
                                icon: image
                            });

                            if (shopData.latitude != 0 || shopData.longitude != 0) {
                                jQuery('.shops-listing .shop-box[data-shop="' + shopData.id + '"] a.view-on-map').click(function(e) {
                                    jQuery('.shops-listing .shop-box').removeClass('selected');
                                    jQuery(this).closest('.shop-box').addClass('selected');

                                    map.panTo(shopData.marker.getPosition());
                                    map.setZoom(17);
                                    map.setCenter({lat: parseFloat(shopData.latitude), lng: parseFloat(shopData.longitude)});
                                });
                            }
                            google.maps.event.addListener(shopData.marker, 'click', function () { infowindow.open(map, shopData.marker); });
                            markers.push(shopData.marker);

                            latlngbounds.extend(shopData.marker.position);
                        })();
                    }

                    map.setCenter(latlngbounds.getCenter());
                    map.fitBounds(latlngbounds);
                }
                google.maps.event.addDomListener(window, 'load', initialize);
            </script>

            <script>
                jQuery(function($){

                    $('.toolbar .drop-down-js.chose-city').each(function(){
                        var $sortOptions = $(this);
                        $sortOptions.find('.sub-options a').click(function(){
                            var $this = $(this);
                            $this.parents('.drop-down-js').first().find('.open-item').html($this.html());
                            var cityId = $this.data('shop-city-id');

                            var $cityShops = null;
                            if (cityId == 'all') {
                                $cityShops = $('.shops-listing .shop-box');
                            } else {
                                $cityShops = $('.shops-listing .shop-box[data-shop-city-id="' + cityId + '"]');
                                $('.shops-listing .shop-box').not($cityShops).hide();
                            }

                            $cityShops.show();

                            if (window.stenikShopMap && window.stenikShopShopsData) {
                                <?php // Calculate position and zoom ?>
                                var bounds = new google.maps.LatLngBounds();
                                $cityShops.each(function() {
                                    var shopId = $(this).data('shop');

                                    for (var i = window.stenikShopShopsData.length - 1; i >= 0; i--) {
                                        if (window.stenikShopShopsData[i].id == shopId) {
                                            bounds.extend(new google.maps.LatLng(window.stenikShopShopsData[i].latitude, window.stenikShopShopsData[i].longitude));
                                        }
                                    }
                                });

                                window.stenikShopMap.fitBounds(bounds);

                                if (window.stenikShopMap.getZoom() > 13) {
                                    window.stenikShopMap.setZoom(13);
                                }
                            }

                            $('.toolbar .drop-down-js.chose-city').trigger('closeselect');

                            return false;
                        });
                    });

                    $('.shops-listing .drop-down-js.chose-city').trigger('closeselect');

                    if ($(window).width() > 767) {
                        var $map = $('#gmap');
                        var $mapPosition = $map.offset().top;
                        $(window).scroll(function() {
                            var $scrollPosition = $(document).scrollTop();
                            var $footerHeight = $('footer').outerHeight();
                            var $mapHeight = $map.outerHeight(true);
                            var $mainContentHeight = $('body').outerHeight();
                            var $shopsListingHegiht = $('.shops-listing').height();
                            if ($scrollPosition >= $mapPosition && $mapHeight < $shopsListingHegiht) {
                                $map.addClass('sticky-map');
                                if ($scrollPosition > $mainContentHeight - $footerHeight - $mapHeight - 100) {
                                    $map.addClass('bottom-stop');
                                } else {
                                    $map.removeClass('bottom-stop');
                                }
                            } else {
                                $map.removeClass('sticky-map');
                            };
                        });
                    } else {
                        $(".view-on-map").click(function() {
                            $('html, body').animate({
                                scrollTop: $(".shops-listing-google-map").offset().top - 50
                            }, 1000);
                        });
                    }

                });
            </script>

            <div class="col-xs-6 shops-listing">
                <?php if (!empty($cities)): ?>
                    <div class="toolbar">
                        <div class="drop-down chose-city drop-down-js">
                            <a class="open-item" href="javascript:;">
                                <?php echo $this->__('Choose city'); ?>
                            </a>
                            <ul class="sub-options">
                                <li class="all-cities"><a href="javascript:;" data-shop-city-id="all"><?php echo $this->__('All cities');?></a></li>
                                <?php $i = 0; foreach ($cities as $city): ?>
                                    <li>
                                        <a href="javascript:;" data-shop-city-id="<?php echo md5($city); ?>">
                                            <?php echo $city; ?>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                    <div class="clearH"></div>
                <?php endif; ?>
                <?php foreach ($shopCollection as $shop): ?>
                    <div class="shop-box" data-shop="<?php echo $shop->getId(); ?>" data-shop-city-id="<?php echo md5($shop->getCity()) ?>">
                        <a class="title" href="<?php echo $shop->getUrl() ?>"><?php echo $shop->getName() ?></a>
                        <p><?php echo $shop->getCity() ?>, <?php echo $shop->getAddress() ?></p>
                        <?php if ($shop->getMobilePhone()): ?><p><?php echo $this->__('Telephone');?>: <?php echo $shop->getMobilePhone(); ?></p><?php endif ?>
                        <p>
                            <a class="view-on-map" href="javascript:;"><?php echo $this->__('View on map');?></a>
                            <a class="view-more" href="<?php echo $shop->getUrl() ?>"><?php echo $this->__('View more');?></a>
                        </p>
                    </div>
                <?php endforeach ?>
            </div>

            <div class="col-xs-6 shops-listing-google-map">
                <div id="gmap"></div>
                <a href="javascript:;" class="responsive-back-to-top"><?php echo $this->__('Back to top');?></a>
            </div>

        <?php else: ?>
            <p class="no-shops-found"><?php echo $this->__('No shops found') ?></p>
        <?php endif; ?>
    </div>
</div>

<?php if ($shopCount): ?>
    <script>
        (function(){
            var container = jQuery('.shops-listing');
            var storeListingWidth = container.find('.shops-listing').first().width();
            var storeBoxOuterWidth = container.find('.shops-listing .shop-box').first().outerWidth();

            var currentFilters = {};

            function storeBoxShowFromList(jqElements, filterCode) {

                var elementsToHide = container.find('.shops-listing .shop-box:visible').not(jqElements);
                if (elementsToHide.length) {
                    jQuery.when(
                        elementsToHide.css({opacity: 0, visibility: 'hidden'}).delay(350-10).hide(0)
                    ).then(function(){
                        jQuery.when(
                            jqElements.show(0).css({opacity: 1, visibility: 'visible'}).delay(10)
                        ).then(function() {
                            if (jqElements.length)
                                container.find('.noShopsFound').hide();
                            else container.find('.noShopsFound').show();
                        });
                    });
                } else {
                    jQuery.when(
                        jqElements.show(0).css({opacity: 1, visibility: 'visible'})
                    ).then(function(){
                        if (jqElements.length)
                            container.find('.noShopsFound').hide();
                        else container.find('.noShopsFound').show();
                    });
                }

            }

        })();
    </script>
<?php endif; ?>
