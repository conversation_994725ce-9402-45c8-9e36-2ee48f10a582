<div class="col-xs-12">
    <div class="wide-area parallax-widget-area">
        <div class="container">
            <div class="parallax-info-box">
                <?php if ($this->getTitle() && $this->getData('url')): ?>
                    <a href="<?php echo $this->getData('url'); ?>" class="parallax-title"><?php echo $this->getTitle(); ?></a>
                <?php elseif ($bannerTitle = $this->getTitle()): ?>
                    <span class="parallax-title"><?php echo $bannerTitle; ?></span>
                <?php endif; ?>
                <?php if ($bannerDescription = $this->getDescription()): ?>
                    <div class="parallax-desc"><?php echo $bannerDescription; ?></div>
                <?php endif ?>
            </div>
        </div>
        <?php if ($this->getImageSrc()): ?>
            <div class="parallax-container">
                <a href="<?php echo $this->getData('url'); ?>" class="parallax-image">
                    <div class="parallax">
                        <img src="<?php echo $this->getImageSrc(); ?>" alt="<?php echo $this->getTitle(); ?>">
                    </div>
                </a>
            </div>

            <script>
                jQuery(function($){
                    $('.parallax').parallax();
                });
            </script>

        <?php endif ?>
    </div>
    <div class="clearH2"></div>
</div>