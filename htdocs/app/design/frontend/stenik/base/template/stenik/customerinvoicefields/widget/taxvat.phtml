<?php
/**
 * @package Stenik_CustomerInvoiceFields
 * <AUTHOR> Magento Team <<EMAIL>>
 */
?>

<?php
    $invoiceTypeCompany  = Stenik_CustomerInvoiceFields_Model_Entity_Attribute_Source_InvoiceType::VALUE_COMPANY;
    $invoiceTypePersonal = Stenik_CustomerInvoiceFields_Model_Entity_Attribute_Source_InvoiceType::VALUE_PERSONAL;
    $invoiceType = $this->getInvoiceData('customer_invoice_type') ? : $invoiceTypeCompany;
?>

<ul class="invoice-fields-wrapper"  id="<?php echo $this->getFieldId('wrapper') ?>">
    <li class="show-invoice-fileds">
        <input type="hidden"
               id="<?php echo $this->getFieldId('customer_invoice_update')?>"
               name="<?php echo $this->getFieldName('customer_invoice_update')?>"
               value="1"
        />
        <input type="checkbox"
               id="<?php echo $this->getFieldId('customer_invoice')?>"
               name="<?php echo $this->getFieldName('customer_invoice')?>"
               value="1"
               <?php if ($this->getInvoiceData('customer_invoice')): ?>
                   checked="checked"
               <?php endif ?>
               title="<?php echo Mage::helper('core')->quoteEscape($this->__('Invoice')) ?>"
               class="checkbox <?php echo $this->helper('customer/address')->getAttributeValidationClass('customer_invoice') ?>"
        />
        <label for="<?php echo $this->getFieldId('customer_invoice')?>">
            <?php echo $this->__('Invoice') ?>
        </label>
    </li>

    <li class="fields invoice-personal-fields invoice-company-fields" <?php if (!$this->getInvoiceData('customer_invoice')): ?>style="display: none"<?php endif ?>>
        <label for="<?php echo $this->getFieldId('customer_invoice_type')?>"><?php echo $this->__('Invoice Type') ?></label>

        <select
            id="<?php echo $this->getFieldId('customer_invoice_type')?>"
            name="<?php echo $this->getFieldName('customer_invoice_type')?>"
            value="1"
            title="<?php echo Mage::helper('core')->quoteEscape($this->__('Invoice')) ?>"
            class="input-checkbox <?php echo $this->helper('customer/address')->getAttributeValidationClass('customer_invoice_type') ?>"
        >
            <?php foreach (array(
                $invoiceTypeCompany  => 'Company',
                $invoiceTypePersonal => 'Personal',
            ) as $value => $label): ?>

                <option
                    value="<?php echo $value ?>"
                    <?php if ($value == $invoiceType): ?>
                        selected="selected"
                    <?php endif ?>
                >
                    <?php echo $this->__($label);?>
                </option>
            <?php endforeach ?>
        </select>
    </li>

    <li class="fields invoice-company-fields" <?php if (!$this->getInvoiceData('customer_invoice') || $invoiceType != $invoiceTypeCompany): ?>style="display: none"<?php endif ?>>
        <label for="<?php echo $this->getFieldId('customer_invoice_company_name')?>">
            <em>*</em>
            <?php echo $this->__('Invoice Company Name') ?>
        </label>

        <div class="input-box">
            <input type="text"
                   id="<?php echo $this->getFieldId('customer_invoice_company_name')?>"
                   name="<?php echo $this->getFieldName('customer_invoice_company_name')?>"
                   value="<?php echo $this->escapeHtml($this->getInvoiceData('customer_invoice_company_name')) ?>"
                   title="<?php echo Mage::helper('core')->quoteEscape($this->__('Invoice Company Name')) ?>"
                   class="input-text required-entry <?php echo $this->helper('customer/address')->getAttributeValidationClass('customer_invoice_company_name') ?>"
            />
        </div>
    </li>

    <li class="fields invoice-company-fields" <?php if (!$this->getInvoiceData('customer_invoice') || $invoiceType != $invoiceTypeCompany): ?>style="display: none"<?php endif ?>>
        <label for="<?php echo $this->getFieldId('customer_invoice_company_urn')?>">
            <em>*</em>
            <?php echo $this->__('Invoice Company URN') ?>
        </label>

        <div class="input-box">
            <input type="text"
                   id="<?php echo $this->getFieldId('customer_invoice_company_urn')?>"
                   name="<?php echo $this->getFieldName('customer_invoice_company_urn')?>"
                   value="<?php echo $this->escapeHtml($this->getInvoiceData('customer_invoice_company_urn')) ?>"
                   title="<?php echo Mage::helper('core')->quoteEscape($this->__('Invoice Company URN')) ?>"
                   class="input-text required-entry <?php echo $this->helper('customer/address')->getAttributeValidationClass('customer_invoice_company_urn') ?>"
            />
        </div>
    </li>

    <li class="fields invoice-company-fields" <?php if (!$this->getInvoiceData('customer_invoice') || $invoiceType != $invoiceTypeCompany): ?>style="display: none"<?php endif ?>>
        <input type="checkbox"
               id="<?php echo $this->getFieldId('customer_invoice_company_vat_registered')?>"
               name="<?php echo $this->getFieldName('customer_invoice_company_vat_registered')?>"
               value="1"
               <?php if (preg_match('/^BG/i', $this->getInvoiceData('customer_invoice_company_vat'))): ?>
                   checked="checked"
               <?php endif ?>
               title="<?php echo Mage::helper('core')->quoteEscape($this->__('VAT Registered')) ?>"
               class="checkbox <?php echo $this->helper('customer/address')->getAttributeValidationClass('customer_invoice_company_vat_registered') ?>"
        />
        <label for="<?php echo $this->getFieldId('customer_invoice_company_vat_registered')?>">
            <?php echo $this->__('VAT Registered') ?>
        </label>
    </li>

    <li class="fields invoice-company-fields" <?php if (!$this->getInvoiceData('customer_invoice') || $invoiceType != $invoiceTypeCompany): ?>style="display: none"<?php endif ?>>
        <label for="<?php echo $this->getFieldId('customer_invoice_company_vat')?>">
            <em>*</em>
            <?php echo $this->__('Invoice Company VAT') ?>
        </label>

        <div class="input-box">
            <input type="text"
                   id="<?php echo $this->getFieldId('customer_invoice_company_vat')?>"
                   name="<?php echo $this->getFieldName('customer_invoice_company_vat')?>"
                   value="<?php echo $this->escapeHtml($this->getInvoiceData('customer_invoice_company_vat')) ?>"
                   title="<?php echo Mage::helper('core')->quoteEscape($this->__('Invoice Company VAT')) ?>"
                   class="input-text required-entry <?php echo $this->helper('customer/address')->getAttributeValidationClass('customer_invoice_company_vat') ?>"
            />
            <input type="hidden"
                   id="<?php echo $this->getFieldId('taxvat')?>"
                   name="<?php echo $this->getFieldName('taxvat')?>"
                   value="<?php echo $this->escapeHtml($this->getTaxvat()) ?>"
                   title="<?php echo Mage::helper('core')->quoteEscape($this->__('Tax/VAT number')) ?>"
                   class="input-text <?php echo $this->helper('customer/address')->getAttributeValidationClass('taxvat') ?>"
                   <?php echo $this->getFieldParams()?>
            />
        </div>
    </li>

    <?php if (0): ?>
        <li class="fields invoice-company-fields" <?php if (!$this->getInvoiceData('customer_invoice') || $invoiceType != $invoiceTypeCompany): ?>style="display: none"<?php endif ?>>
          <label for="<?php echo $this->getFieldId('customer_invoice_company_city')?>">
              <?php echo $this->__('Invoice Company City') ?>
          </label>

          <div class="input-box">
              <input type="text"
                     id="<?php echo $this->getFieldId('customer_invoice_company_city')?>"
                     name="<?php echo $this->getFieldName('customer_invoice_company_city')?>"
                     value="<?php echo $this->escapeHtml($this->getInvoiceData('customer_invoice_company_city')) ?>"
                     title="<?php echo Mage::helper('core')->quoteEscape($this->__('Invoice Company City')) ?>"
                     class="input-text <?php echo $this->helper('customer/address')->getAttributeValidationClass('customer_invoice_company_city') ?>"
              />
          </div>
        </li>
    <?php endif ?>

    <li class="fields invoice-company-fields" <?php if (!$this->getInvoiceData('customer_invoice') || $invoiceType != $invoiceTypeCompany): ?>style="display: none"<?php endif ?>>
        <label for="<?php echo $this->getFieldId('customer_invoice_company_addr')?>">
            <em>*</em>
            <?php echo $this->__('Invoice Company Address') ?>
        </label>

        <div class="input-box">
            <input type="text"
                   id="<?php echo $this->getFieldId('customer_invoice_company_addr')?>"
                   name="<?php echo $this->getFieldName('customer_invoice_company_addr')?>"
                   value="<?php echo $this->escapeHtml($this->getInvoiceData('customer_invoice_company_addr')) ?>"
                   title="<?php echo Mage::helper('core')->quoteEscape($this->__('Invoice Company Address')) ?>"
                   class="input-text required-entry <?php echo $this->helper('customer/address')->getAttributeValidationClass('customer_invoice_company_addr') ?>"
            />
        </div>
    </li>

    <li class="fields invoice-company-fields" <?php if (!$this->getInvoiceData('customer_invoice') || $invoiceType != $invoiceTypeCompany): ?>style="display: none"<?php endif ?>>
        <label for="<?php echo $this->getFieldId('customer_invoice_company_pic')?>">
            <em>*</em>
            <?php echo $this->__('Invoice Company PIC') ?>
        </label>

        <div class="input-box">
            <input type="text"
                   id="<?php echo $this->getFieldId('customer_invoice_company_pic')?>"
                   name="<?php echo $this->getFieldName('customer_invoice_company_pic')?>"
                   value="<?php echo $this->escapeHtml($this->getInvoiceData('customer_invoice_company_pic')) ?>"
                   title="<?php echo Mage::helper('core')->quoteEscape($this->__('Invoice Company PIC')) ?>"
                   class="input-text required-entry <?php echo $this->helper('customer/address')->getAttributeValidationClass('customer_invoice_company_pic') ?>"
            />
        </div>
    </li>

    <li class="fields invoice-personal-fields" <?php if (!$this->getInvoiceData('customer_invoice') || $invoiceType != $invoiceTypePersonal): ?>style="display: none"<?php endif ?>>
        <label for="<?php echo $this->getFieldId('customer_invoice_personal_name')?>">
            <em>*</em>
            <?php echo $this->__('Invoice Personal Name') ?>
        </label>

        <div class="input-box">
            <input type="text"
                   id="<?php echo $this->getFieldId('customer_invoice_personal_name')?>"
                   name="<?php echo $this->getFieldName('customer_invoice_personal_name')?>"
                   value="<?php echo $this->escapeHtml($this->getInvoiceData('customer_invoice_personal_name')) ?>"
                   title="<?php echo Mage::helper('core')->quoteEscape($this->__('Invoice Personal Name')) ?>"
                   class="input-text required-entry <?php echo $this->helper('customer/address')->getAttributeValidationClass('customer_invoice_personal_name') ?>"
            />
        </div>
    </li>
    <?php if (0): ?>
        <li class="fields invoice-personal-fields" <?php if (!$this->getInvoiceData('customer_invoice') || $invoiceType != $invoiceTypePersonal): ?>style="display: none"<?php endif ?>>
            <label for="<?php echo $this->getFieldId('customer_invoice_personal_city')?>">
                <em>*</em>
                <?php echo $this->__('Invoice Personal City') ?>
            </label>

            <div class="input-box">
                <input type="text"
                       id="<?php echo $this->getFieldId('customer_invoice_personal_city')?>"
                       name="<?php echo $this->getFieldName('customer_invoice_personal_city')?>"
                       value="<?php echo $this->escapeHtml($this->getInvoiceData('customer_invoice_personal_city')) ?>"
                       title="<?php echo Mage::helper('core')->quoteEscape($this->__('Invoice Personal City')) ?>"
                       class="input-text required-entry <?php echo $this->helper('customer/address')->getAttributeValidationClass('customer_invoice_personal_city') ?>"
                />
            </div>
        </li>
    <?php endif ?>
    <li class="fields invoice-personal-fields" <?php if (!$this->getInvoiceData('customer_invoice') || $invoiceType != $invoiceTypePersonal): ?>style="display: none"<?php endif ?>>
        <label for="<?php echo $this->getFieldId('customer_invoice_personal_addr')?>">
            <em>*</em>
            <?php echo $this->__('Invoice Personal Address') ?>
        </label>

        <div class="input-box">
            <input type="text"
                   id="<?php echo $this->getFieldId('customer_invoice_personal_addr')?>"
                   name="<?php echo $this->getFieldName('customer_invoice_personal_addr')?>"
                   value="<?php echo $this->escapeHtml($this->getInvoiceData('customer_invoice_personal_addr')) ?>"
                   title="<?php echo Mage::helper('core')->quoteEscape($this->__('Invoice Personal Address')) ?>"
                   class="input-text required-entry <?php echo $this->helper('customer/address')->getAttributeValidationClass('customer_invoice_personal_addr') ?>"
            />
        </div>
    </li>
    <li class="fields invoice-personal-fields" <?php if (!$this->getInvoiceData('customer_invoice') || $invoiceType != $invoiceTypePersonal): ?>style="display: none"<?php endif ?>>
        <label for="<?php echo $this->getFieldId('customer_invoice_personal_pin')?>">
            <em>*</em>
            <?php echo $this->__('Invoice Personal PIN') ?>
        </label>

        <div class="input-box">
            <input type="text"
                   id="<?php echo $this->getFieldId('customer_invoice_personal_pin')?>"
                   name="<?php echo $this->getFieldName('customer_invoice_personal_pin')?>"
                   value="<?php echo $this->escapeHtml($this->getInvoiceData('customer_invoice_personal_pin')) ?>"
                   title="<?php echo Mage::helper('core')->quoteEscape($this->__('Invoice Personal PIN')) ?>"
                   class="input-text required-entry <?php echo $this->helper('customer/address')->getAttributeValidationClass('customer_invoice_personal_pin') ?>"
            />
        </div>
    </li>
</ul>

<script>
    (function(hasjQuery) {
        if (hasjQuery) {
            // jQuery

            var updateOriginalTaxvatField = function() {
                var vat = jQuery("#<?php echo str_replace(':', '\\\\:', $this->getFieldId('customer_invoice_company_vat')) ?>").val();

                if (!vat) {
                    vat = jQuery("#<?php echo str_replace(':', '\\\\:', $this->getFieldId('customer_invoice_company_urn')) ?>").val();
                }

                if (jQuery("#<?php echo str_replace(':', '\\\\:', $this->getFieldId('customer_invoice_company_vat_registered')) ?>").is(':checked')) {
                    if (!vat.match(/^BG/i)) {
                        vat = 'BG' + vat;
                    }
                } else {
                    if (vat.match(/^BG/i)) {
                        vat = vat.replace(/^BG/i, '');
                    }
                }

                jQuery("#<?php echo str_replace(':', '\\\\:', $this->getFieldId('customer_invoice_company_vat')) ?>").val(vat);

                jQuery("#<?php echo str_replace(':', '\\\\:', $this->getFieldId('taxvat')) ?>").val(
                    jQuery("#<?php echo str_replace(':', '\\\\:', $this->getFieldId('customer_invoice_company_vat')) ?>").val()
                );
            };

            var showFieldsByInvoiceType = function() {
                var invoiceType = jQuery("#<?php echo str_replace(':', '\\\\:', $this->getFieldId('customer_invoice_type')) ?>").val();

                jQuery("#<?php echo str_replace(':', '\\\\:', $this->getFieldId('wrapper')) ?>").find('.fields').hide();

                if (jQuery("#<?php echo str_replace(':', '\\\\:', $this->getFieldId('customer_invoice')) ?>").is(':checked')) {
                    jQuery("#<?php echo str_replace(':', '\\\\:', $this->getFieldId('wrapper')) ?>").find('.invoice-' + invoiceType + '-fields').show();
                }
            };

            jQuery("#<?php echo str_replace(':', '\\\\:', $this->getFieldId('customer_invoice_company_vat')) ?>").change(updateOriginalTaxvatField);
            jQuery("#<?php echo str_replace(':', '\\\\:', $this->getFieldId('customer_invoice_company_urn')) ?>").change(updateOriginalTaxvatField);
            jQuery("#<?php echo str_replace(':', '\\\\:', $this->getFieldId('customer_invoice_company_vat_registered')) ?>").change(updateOriginalTaxvatField);
            jQuery("#<?php echo str_replace(':', '\\\\:', $this->getFieldId('customer_invoice')) ?>").change(showFieldsByInvoiceType);
            jQuery("#<?php echo str_replace(':', '\\\\:', $this->getFieldId('customer_invoice_type')) ?>").change(showFieldsByInvoiceType);
            showFieldsByInvoiceType();
        } else {
            // Prototype

            var updateOriginalTaxvatField = function() {
                var vat = $("<?php echo $this->getFieldId('customer_invoice_company_vat') ?>").value;

                if (!vat) {
                    vat = $("<?php echo $this->getFieldId('customer_invoice_company_urn') ?>").value;
                }

                if (this.checked) {
                    if (!vat.match(/^BG/i)) {
                        vat = 'BG' + vat;
                    }
                } else {
                    if (vat.match(/^BG/i)) {
                        vat = vat.replace(/^BG/i, '');
                    }
                }

                $("<?php echo $this->getFieldId('customer_invoice_company_vat') ?>").value = vat;

                $("<?php echo $this->getFieldId('taxvat')?>").value = $("<?php echo $this->getFieldId('customer_invoice_company_vat')?>").value;
            }

            var showFieldsByInvoiceType = function() {
                var invoiceType = $("<?php echo $this->getFieldId('customer_invoice_type') ?>").value;

                $("<?php echo $this->getFieldId('wrapper') ?>").select('.fields').each(function(el) { el.hide(); });

                if (jQuery("#<?php echo str_replace(':', '\\\\:', $this->getFieldId('customer_invoice')) ?>").is(':checked')) {
                    $("<?php echo $this->getFieldId('wrapper') ?>").select('.invoice-' + invoiceType + '-fields').each(function(el) { el.show(); });
                }
            };

            $("<?php echo $this->getFieldId('customer_invoice_company_vat') ?>").observe('change', updateOriginalTaxvatField);
            $("<?php echo $this->getFieldId('customer_invoice_company_urn') ?>").observe('change', updateOriginalTaxvatField);
            $("<?php echo $this->getFieldId('customer_invoice_company_vat_registered') ?>").observe('change', updateOriginalTaxvatField);
            $("<?php echo $this->getFieldId('customer_invoice') ?>").observe('change', showFieldsByInvoiceType);
            $("<?php echo $this->getFieldId('customer_invoice_type') ?>").observe('change', showFieldsByInvoiceType);
            showFieldsByInvoiceType();
        }
    })(typeof jQuery !== 'undefined');
</script>