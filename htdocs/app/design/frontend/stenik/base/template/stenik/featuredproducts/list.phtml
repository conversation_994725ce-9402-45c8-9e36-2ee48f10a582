<?php
/**
 * Featured product list
 *
 * @package stenik_default
 * <AUTHOR> Magento Team <<EMAIL>>
 */
?><?php // No white space coz the template must to have the chance to return empty string

$productCollection = $this->getProductCollection();
$_helper = $this->helper('catalog/output');
$_helperStenikBase = $this->helper('stenik_base');
$productImageSufix = $this->helper('stenik_sitesettings')->getproductImagesAltSufix();

$productCount = null;
if (!$productCollection || !($productCount = $productCollection->getSize())) {
    return;
}

// Generate unique id. uniqid() is slow!
static $catalogProductListSliderDefault_sliderUniqueIdCounter = 0;
$sliderUniqueId = 'productSlider_cplsd' . (++$catalogProductListSliderDefault_sliderUniqueIdCounter);

// $sliderUniqueId can be used for initializing sliders and etc.
?>
<div class="wide-area products">
    <div class="container">
        <div class="row">
            <div class="col-xs-12">
                <div class="row-header marginB30 clearfix">
                    <a class="row-title" href="<?php echo Mage::getModel("catalog/category")->load($this->getCategoryId())->getUrl() ?>">
                        <?php echo Mage::getModel("catalog/category")->load($this->getCategoryId())->getName() ?>
                    </a>
                </div>
            </div>
        </div>
        <div class="owl-carousel product-slider">
            <?php foreach ($productCollection as $product): ?>

                <?php $secondImg = ($this->helper('stenik_sitesettings')->getProductListingSecondImg() ? Mage::helper('stenik_base/catalog_product')->getSecondImageFile($product) : null) ?>

                <a class="product-box" href="<?php echo $product->getProductUrl() ?>">
                    <span class="image-wrapper<?php if ($secondImg): ?> has-second-img<?php endif; ?>">
                        <img
                            class="first lazyload"
                            data-src="<?php echo $this->helper('catalog/image')->init($product, 'small_image')->resize(285, 285); ?>"
                            data-srcset="<?php echo $this->helper('catalog/image')->init($product, 'small_image')->resize(570, 570); ?> 2x"
                            alt="<?php echo $this->stripTags($this->getImageLabel($product, 'small_image'), null, true) ?>"
                            title="<?php echo $this->stripTags($this->getImageLabel($product, 'small_image'), null, true)  . ' - ' . $productImageSufix ?>"
                        >
                        <?php if ($secondImg): ?>
                            <img
                                class="second lazyload"
                                data-src="<?php echo $this->helper('catalog/image')->init($product, 'small_image', $secondImg)->resize(285, 285); ?>"
                                data-srcset="<?php echo $this->helper('catalog/image')->init($product, 'small_image', $secondImg)->resize(570, 570); ?> 2x"
                                alt="<?php echo $this->stripTags($this->getImageLabel($product, 'small_image'), null, true) ?> - 2"
                                title="<?php echo $this->stripTags($this->getImageLabel($product, 'small_image'), null, true)  . ' - 2 - ' . $productImageSufix ?>"
                            >
                        <?php endif; ?>
                        <?php echo Mage::app()->getLayout()->getBlock('product.labels')->setProduct($product)->toHtml(); ?>
                        <span class="actions">
                            <?php if($product->isSaleable()): ?>
                                <span class="button checkout-color add-to-cart" onclick="document.location.href='<?php echo $this->getAddToCartUrl($product) ?>'; return false">
                                    <?php echo $this->__('Add to Cart');?>
                                </span>
                            <?php endif; ?>
                            <?php if ($this->helper('wishlist')->isAllow()) : ?>
                                <span class="icon-link" onclick="document.location.href='<?php echo $this->helper('wishlist')->getAddUrl($product) ?>'; return false">
                                    <svg aria-hidden="true" class="icon-svg wishlist"><use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#wishlist' ?>"></use></svg>
                                </span>
                            <?php endif; ?>
                            <?php if($_compareUrl=$this->getAddToCompareUrl($product)): ?>
                                <span class="icon-link" onclick="document.location.href='<?php echo $_compareUrl ?>'; return false">
                                    <svg aria-hidden="true" class="icon-svg compare"><use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#compare' ?>"></use></svg>
                                </span>
                            <?php endif; ?>
                        </span>
                    </span>

                    <?php $confOptions = Mage::helper('stenik_base/catalog_product')->getProductConfigurableAttributeOptions($product, 'attribute_7'); ?>

                    <?php if ($count = count($confOptions)): ?>
                        <span class="attributes">
                            <?php echo $count; ?> <?php echo $this->__($count == 1 ? 'color' : 'colors'); ?>
                        </span>
                    <?php endif ?>

                    <span class="title"><?php echo $_helper->productAttribute($product, $product->getName(), 'name') ?></span>
                    <?php echo $this->getPriceHtml($product, true) ?>
                </a>
            <?php endforeach ?>
        </div>
    </div>
</div>

<script>
    jQuery(function($){

        $(".product-slider").owlCarousel({
            loop: false,
            items: 4,
            slideBy: 2,
            nav: true,
            navText: false,
            slideSpeed: 500,
            autoPlay: false,
            autoplayTimeout: 5000,
            autoplayHoverPause: true,
            addClassActive: true,
            dots: false,
            stagePadding: 1,
            responsive : {
                0 : { items: 2 },
                480 : { items: 4 },
                767 : { items: 4}
            }
        });

    });
</script>
