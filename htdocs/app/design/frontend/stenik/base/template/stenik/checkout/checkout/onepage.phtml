

<script type="text/javascript" src="<?php echo $this->getJsUrl('varien/accordion.js') ?>"></script>
<script type="text/javascript" src="<?php echo $this->getSkinUrl('js/stenik/checkout/opcheckout.js') ?>"></script>
<script>
    var stenikOnepageCheckout = new window.Stenik.Checkout('<?php echo $this->getUrl('checkout/onepage/saveAll', array('form_key' => Mage::getSingleton('core/session')->getFormKey())) ?>');
    countryRegions = <?php echo $this->helper('directory')->getRegionJson() ?>
</script>

<div class="stenik-checkout">

    <h1><?php echo $this->__('Checkout') ?></h1>

    <?php if (!$this->isCustomerLoggedIn()): ?>
        <?php echo $this->getChildHtml('login_popup'); ?>
    <?php endif ?>

    <div style="display: none">
        <?php // dummy elements ?>
        <div id="shipping-method-please-wait"></div>
        <div id="shipping-method-buttons-container"><button class="button"></button></div>
    </div>

    <div class="stenik-checkout-steps-content" id="stenik-checkout-steps-content">
        <?php $i=0; foreach($this->getSteps() as $_stepId => $_stepInfo) : ?>
            <?php if (!$this->getChild($_stepId) || !$this->getChild($_stepId)->isShow()): continue; endif; $i++ ?>
            <?php echo $this->getChildHtml($_stepId) ?>
        <?php endforeach ?>
    </div>

    <div class="stenik-onepage-section-overlay stenik-checkout-overlay"><span class="loaderIcon"></span></div>

</div>