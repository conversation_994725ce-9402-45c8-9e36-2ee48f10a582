<?php
/**
 * @package  Stenik_Template
 * <AUTHOR> <<EMAIL>>
 * @see      Mage_Review_Block_Product_View_List
 */
?>
<?php $_reviews = $this->getReviewsCollection()->getItems(); ?>
<?php
    $referer = Mage::helper('core/url')->getCurrentUrl();
    $referer = Mage::helper('core')->urlEncode($referer);
    $params = array('referer' => $referer);
    $productImageSufix = $this->helper('stenik_sitesettings')->getproductImagesAltSufix();
?>

<?php if (count($_reviews)): ?>

    <?php $showMoreReviews = false; ?>

    <div class="col-xs-6 reviews-listing" itemprop="review" itemscope itemtype="http://schema.org/Review">

        <h6><?php echo $this->__('Comments');?></h6>

        <?php foreach ($_reviews as $_review): ?>
            <?php $_votes = $_review->getRatingVotes(); ?>
            <div class="review-item">
                <div class="rating-box-title">
                    <?php if (count($_votes)): ?>
                        <?php $summaryRating = 0; ?>
                        <?php foreach($_votes as $_vote): ?>
                            <?php $summaryRating = $_vote->getValue(); ?>
                            <div class="rating-box" itemprop="reviewRating" itemscope itemtype="http://schema.org/Rating">
                                <div class="rating" style="width:<?php echo ((int)$summaryRating * 20) ?>%"></div>
                                <meta itemprop="worstRating" content="1">
                                <meta itemprop="ratingValue" content="<?php echo (int) $_vote->getValue() ?>">
                                <meta itemprop="bestRating" content="5">
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                    <span class="title" itemprop="name"><?php echo $this->escapeHtml($_review->getTitle()) ?></span>
                </div>
                <span itemprop="author" class="author"><?php echo $this->escapeHtml($_review->getNickname()) ?></span> &nbsp;|&nbsp;
                <span itemprop="datePublished" content="<?php echo date('Y-m-d', strtotime($_review->getCreatedAt())); ?>">
                    <?php echo Mage::app()->getLocale()->date()->setDate($this->formatDate($_review->getCreatedAt()))->toString('FFF') ?>
                </span>
                <div class="clearH"></div>
                <div class="comment" itemprop="reviewBody">
                    <?php echo nl2br($this->escapeHtml($_review->getDetail())) ?>
                </div>
            </div>
        <?php endforeach ?>

    </div>

<?php else: ?>

    <div class="col-xs-6 reviews-listing" itemprop="review" itemscope itemtype="http://schema.org/Review">
        <h6><?php echo $this->__('No customer review for this product');?></h6>
        <p><?php echo $this->__('Fill out the form and leave your review, if you do not see the form please log in');?></p>
        <span itemprop="author" style="display:none;"><?php echo $productImageSufix; ?></span>
    </div>

<?php endif ?>