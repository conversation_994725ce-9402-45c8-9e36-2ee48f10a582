<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2014 X.commerce, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * @see Mage_Checkout_Block_Agreements
 */
?>

<form action="" id="checkout-agreements" onsubmit="return false;">

    <div class="order-comment">
        <label for="checkout-customer_note"><?php echo $this->__('Order Comment') ?></label>
        <textarea id="checkout-customer_note" name="customer_note"></textarea>
    </div>

    <?php if ($this->getAgreements()): ?>
        <ol class="checkout-agreements">
            <?php if (Mage::helper('core')->isModuleEnabled('Stenik_GdprCompliance')): ?>
                <?php if (Mage::helper('stenik_gdprcompliance')->isEnabled() && !$this->helper('customer')->isLoggedIn()): ?>
                    <?php echo $this->getLayout()->createBlock('stenik_gdprcompliance/widget_checkout_agreements')->setFormData(new Varien_Object())->toHtml() ?>
                <?php else: ?>
                    <?php foreach ($this->getAgreements() as $_a): ?>
                        <li>
                            <input type="checkbox" id="agreement-<?php echo $_a->getId()?>" name="agreement[<?php echo $_a->getId()?>]" value="1" title="<?php echo $this->htmlEscape($_a->getCheckboxText()) ?>" class="checkbox" />
                            <label for="agreement-<?php echo $_a->getId()?>"><?php echo $_a->getIsHtml() ? $_a->getCheckboxText() : $this->htmlEscape($_a->getCheckboxText()) ?></label>
                        </li>
                    <?php endforeach ?>
                <?php endif; ?>
            <?php else: ?>
                <?php foreach ($this->getAgreements() as $_a): ?>
                    <li>
                        <input type="checkbox" id="agreement-<?php echo $_a->getId()?>" name="agreement[<?php echo $_a->getId()?>]" value="1" title="<?php echo $this->htmlEscape($_a->getCheckboxText()) ?>" class="checkbox" />
                        <label for="agreement-<?php echo $_a->getId()?>"><?php echo $_a->getIsHtml() ? $_a->getCheckboxText() : $this->htmlEscape($_a->getCheckboxText()) ?></label>
                    </li>
                <?php endforeach ?>
            <?php endif ?>
        </ol>
    <?php endif; ?>

    <div style="display:none;">
        <div id="termsPopUp" class="text-page terms-popup">
            <?php
                $page = Mage::getModel('cms/page')->getCollection()
                      ->addStoreFilter(Mage::app()->getStore()->getId())
                      ->addFieldToFilter('identifier','terms')->getFirstItem();
                $html = Mage::helper('cms')->getPageTemplateProcessor()->filter($page->getContent());
            ?>
            <h1><?php echo $page->getTitle() ?></h1>
            <?php echo $this->getMessagesBlock()->toHtml() . $html ?>
        </div>
    </div>
    <script>
        jQuery(function(){
            jQuery('.termsOpen').colorbox({inline:true});
        });
    </script>

</form>

<div class="clearH"></div>

