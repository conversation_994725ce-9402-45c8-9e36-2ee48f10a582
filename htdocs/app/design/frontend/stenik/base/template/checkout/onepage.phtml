<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2014 X.commerce, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<script type="text/javascript" src="<?php echo $this->getJsUrl('varien/accordion.js') ?>"></script>
<script type="text/javascript" src="<?php echo $this->getSkinUrl('js/opcheckout.js') ?>"></script>

<h1><?php echo $this->__('Checkout');?></h1>

<div class="clearH"></div>

<ol class="opc" id="checkoutSteps">
    <?php $i=0; foreach($this->getSteps() as $_stepId => $_stepInfo): ?>
    <?php if (!$this->getChild($_stepId) || !$this->getChild($_stepId)->isShow()): continue; endif; $i++ ?>
        <li id="opc-<?php echo $_stepId ?>" class="section<?php echo !empty($_stepInfo['allow'])?' allow':'' ?><?php echo !empty($_stepInfo['complete'])?' saved':'' ?>">
            <div class="step-title">
                <span class="number"><?php echo $i ?></span>
                <h2><?php echo $_stepInfo['label'] ?></h2>
                <a href="javascript:;" class="editLink"><?php echo $this->__('Edit') ?></a>
            </div>
            <div id="checkout-step-<?php echo $_stepId ?>" class="step a-item" style="display:none;">
                <?php echo $this->getChildHtml($_stepId) ?>
            </div>
        </li>
    <?php endforeach ?>
</ol>


<script>
    var accordion = new Accordion('checkoutSteps', '.step-title', true);
    <?php if($this->getActiveStep()): ?>
    accordion.openSection('opc-<?php echo $this->getActiveStep() ?>');
    <?php endif ?>
    var checkout = new Checkout(accordion,{
        progress: '<?php echo $this->getUrl('checkout/onepage/progress') ?>',
        review: '<?php echo $this->getUrl('checkout/onepage/review') ?>',
        saveMethod: '<?php echo $this->getUrl('checkout/onepage/saveMethod') ?>',
        failure: '<?php echo $this->getUrl('checkout/cart') ?>'}
    );

    jQuery(function($){
        var $lastSection;

        var scrollUpdate = function() {
            var $currentSection = $('.section.active');
            if (typeof $lastSection == 'undefined' || $lastSection.attr('id') != $currentSection.attr('id')) {
                if ($currentSection.offset().top - jQuery('html').scrollTop() < 0) {
                    jQuery('html').animate({scrollTop: $currentSection.offset().top - 50}, 200);
                }
                $lastSection = $currentSection;
            }
        };

        jQuery('#checkoutSteps .section .step-title').click(scrollUpdate);
        Ajax.Responders.register({onComplete: scrollUpdate});
    });

</script>
