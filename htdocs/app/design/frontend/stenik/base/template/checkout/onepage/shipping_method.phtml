<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2015 X.commerce, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<form id="co-shipping-method-form" action="">
    <div id="checkout-shipping-method-load">
       <!-- Content loaded dynamically -->
    </div>
    <script type="text/javascript">
    //<![CDATA[
        var shippingMethod = new ShippingMethod('co-shipping-method-form', "<?php echo $this->getUrl('checkout/onepage/saveShippingMethod') ?>");
    //]]>
    </script>
    <div id="onepage-checkout-shipping-method-additional-load">
        <?php echo $this->getChildHtml('additional') ?>
    </div>
    <div class="buttons-set" id="shipping-method-buttons-container">
        <a class="backLink" href="javascript:;" onclick="checkout.back(); return false;"><?php echo $this->__('Back') ?></a>
        <button type="button" class="button" onclick="shippingMethod.save()"><?php echo $this->__('Continue') ?></button>
        <span id="shipping-method-please-wait" class="please-wait" style="display:none;">
            <img src="<?php echo $this->getSkinUrl('images/opc-ajax-loader.gif') ?>" alt="<?php echo $this->__('Loading next step...') ?>" title="<?php echo $this->__('Loading next step...') ?>" class="v-middle" /> <?php echo $this->__('Loading next step...') ?>
        </span>
    </div>
    <?php echo $this->getBlockHtml('formkey') ?>
</form>
