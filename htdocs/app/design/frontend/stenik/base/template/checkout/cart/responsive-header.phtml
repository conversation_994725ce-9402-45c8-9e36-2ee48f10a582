<?php
/**
 * @package  Stenik_Template
 * <AUTHOR> <<EMAIL>>
 * @see      Mage_Checkout_Block_Cart_Sidebar
 */
?>


<?php if ($this->getIsNeedToDisplaySideBar()): ?>
    <?php $itemCount = (int)count($this->getItems()); ?>
    <?php $_cartQty = $this->getSummaryCount() ?>

    <a aria-label="<?php echo $this->__('Mobile cart');?>" class="responsive-cart<?php if($itemCount > 0): ?> has-items<?php endif ?>" href="<?php echo Mage::getUrl('checkout/cart') ?>">
        <svg aria-hidden="true" class="icon-svg shopping-cart"><use xlink:href="<?php echo $this->getBaseUrl().'svg/svg-sprite.svg' . '#shopping-cart' ?>"></use></svg>
    </a>
    
<?php endif ?>
