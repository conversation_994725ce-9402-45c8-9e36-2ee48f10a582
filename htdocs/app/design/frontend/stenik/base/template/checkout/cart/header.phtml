<?php
/**
 * @package  Stenik_Template
 * <AUTHOR> <<EMAIL>>
 * @see      Mage_Checkout_Block_Cart_Sidebar
 */
/** @var $this Mage_Checkout_Block_Cart_Sidebar */
?>


<?php if ($this->getIsNeedToDisplaySideBar()): ?>
    <?php $itemCount = (int)count($this->getItems()); ?>
    <?php $_cartQty = $this->getSummaryCount() ?>
    <div class="mini-cart<?php if($itemCount > 0): ?> has-item<?php endif ?>">

        <a class="mini-cart-open" href="<?php echo Mage::getUrl('checkout/cart') ?>">
            <svg aria-hidden="true" class="icon-svg shopping-cart">
                <use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#shopping-cart' ?>"></use>
            </svg>
            <span class="open-title"><?php echo $this->__('Your cart');?></span>
            <span class="items-price">
                <?php if($itemCount > 0): ?><?php echo $_cartQty; ?><?php else: ?>0<?php endif ?>
                <?php echo $this->__('products');?> | <?php echo Mage::helper('checkout')->formatPrice($this->getSubtotal(false)) ?>
            </span>
        </a>

        <?php if($itemCount > 0): ?>
            <div class="mini-cart-sub">
                <?php $priceDiscountSum = 0; ?>
                <?php $i = 0; ?>

                <span class="top-title"><?php echo $this->__('Recently added items');?></span>

                <?php foreach ($this->getItems() as $item): ?>
                    <?php echo $this->getItemHtml($item) ?>
                <?php endforeach ?>

                <span class="sub-total"><?php echo $this->__('Overall');?> <?php echo Mage::helper('checkout')->formatPrice($this->getSubtotal(false)); ?></span>

                <?php if ($this->helper('stenik_sitesettings')->getFreeshipping()): ?>
                    <?php
                        $grandTotal = $this->getQuote()->getGrandTotal();
                        $toFreeDelivery = ($this->helper('stenik_sitesettings')->getFreeshipping() - $grandTotal);
                    ?>
                    <?php if ($toFreeDelivery <= 0): ?>
                        <div class="delivery-price free-shipping">
                            <svg aria-hidden="true" class="icon-svg delivery">
                                <use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#delivery' ?>"></use>
                            </svg>
                            <?php echo $this->__('<strong>Free shipping</strong>');?>
                        </div>
                    <?php else: ?>
                        <div class="delivery-price">
                            <svg aria-hidden="true" class="icon-svg delivery">
                                <use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#delivery' ?>"></use>
                            </svg>
                            <?php echo $this->__('Add items for');?>
                            <?php echo $this->helper('checkout')->formatPrice($toFreeDelivery); ?>,
                            <?php echo $this->__('to get <strong>free shipping</strong>');?>!
                        </div>
                    <?php endif ?>
                <?php endif ?>

                <a href="<?php echo Mage::getUrl('checkout/cart') ?>" class="button half-width"><?php echo $this->__('View cart');?></a>
                <a href="<?php echo Mage::getUrl('checkout/onepage') ?>" class="button checkout-color half-width"><?php echo $this->__('Proceed to checkout');?></a>
            </div>
        <?php endif ?>
    </div>
<?php endif ?>