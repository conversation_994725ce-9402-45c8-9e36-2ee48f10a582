<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2014 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<?php $_product = $this->getProduct(); ?>
<?php $buttonTitle = $this->__('Update Cart'); ?>
<?php if ($_product->isSaleable()): ?>

    <?php if (0): ?> <?php //Disable Qty ?>
        <div class="amountBox">
            <span class="countTitle"><?php echo $this->__('Qty'); ?></span>
            <input type="text" name="qty" id="qty" maxlength="12" value="<?php echo $this->getProductDefaultQty() * 1 ?>" title="<?php echo $this->__('Qty') ?>" class="input-text qty amount" readonly />
        </div>
    <?php endif ?>

    <a href="javascript:;" class="addToCartBtn btn-cart button" title="<?php echo $this->__('Add to cart'); ?>" onclick="productAddToCartForm.submit(this)">
        <?php echo $this->__('Add to cart'); ?>
    </a>
<?php endif; ?>
