<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2015 X.commerce, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<script type="text/javascript">
//<![CDATA[
    var quoteBaseGrandTotal = <?php echo (float)$this->getQuoteBaseGrandTotal(); ?>;
    var checkQuoteBaseGrandTotal = quoteBaseGrandTotal;
    var payment = new Payment('co-payment-form', '<?php echo $this->getUrl('checkout/onepage/savePayment') ?>');
    var lastPrice;
//]]>
</script>
<form action="" id="co-payment-form">
    <fieldset>
        <?php echo $this->getChildHtml('methods') ?>
    </fieldset>
    <?php echo $this->getBlockHtml('formkey') ?>
</form>
<div class="tool-tip" id="payment-tool-tip" style="display:none;">
    <div class="btn-close"><a href="#" id="payment-tool-tip-close" title="<?php echo $this->__('Close') ?>"><?php echo $this->__('Close') ?></a></div>
    <div class="tool-tip-content"><img src="<?php echo $this->getSkinUrl('images/cvv.gif') ?>" alt="<?php echo $this->__('Card Verification Number Visual Reference') ?>" title="<?php echo $this->__('Card Verification Number Visual Reference') ?>" /></div>
</div>
<div class="buttons-set" id="payment-buttons-container">
    <a class="backLink" href="javascript:;" onclick="checkout.back(); return false;"><?php echo $this->__('Back') ?></a>
    <button type="button" class="button" onclick="payment.save()"><?php echo $this->__('Continue') ?></button>
    <span class="please-wait" id="payment-please-wait" style="display:none;">
        <img src="<?php echo $this->getSkinUrl('images/opc-ajax-loader.gif') ?>" alt="<?php echo $this->__('Loading next step...') ?>" title="<?php echo $this->__('Loading next step...') ?>" class="v-middle" /> <?php echo $this->__('Loading next step...') ?>
    </span>
</div>
<script type="text/javascript">
//<![CDATA[
    function toggleToolTip(event){
        if($('payment-tool-tip')){
            $('payment-tool-tip').setStyle({
                top: (Event.pointerY(event)-560)+'px'//,
                //left: (Event.pointerX(event)+100)+'px'
            })
            $('payment-tool-tip').toggle();
        }
        Event.stop(event);
    }
    if($('payment-tool-tip-close')){
        Event.observe($('payment-tool-tip-close'), 'click', toggleToolTip);
    }
//]]>
</script>
<script type="text/javascript">
//<![CDATA[
    payment.currentMethod = "<?php echo $this->getChild('methods')->getSelectedMethodCode() ?>";
//]]>
</script>
