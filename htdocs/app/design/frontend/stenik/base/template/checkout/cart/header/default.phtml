
<?php
    $_item = $this->getItem();
    $isVisibleProduct = $_item->getProduct()->isVisibleInSiteVisibility();
?>

<div class="mini-cart-item">
    <?php if ($this->hasProductUrl()): ?>
        <a href="<?php echo $this->getProductUrl()?>" title="<?php echo $this->escapeHtml($this->getProductName()) ?>" class="item-image">
            <img
                src="<?php echo $this->getProductThumbnail()->resize(120, 120); ?>"
                srcset="<?php echo $this->getProductThumbnail()->resize(285,285) ?> 2x"
                alt="<?php echo $this->escapeHtml($this->getProductName()) ?>"
            />
        </a>
    <?php else: ?>
        <span class="item-image">
            <img
                src="<?php echo $this->getProductThumbnail()->resize(120, 120); ?>"
                srcset="<?php echo $this->getProductThumbnail()->resize(285,285) ?> 2x"
                alt="<?php echo $this->escapeHtml($this->getProductName()) ?>"
            />
        </span>
    <?php endif; ?>
    <div class="item-info">
        <?php if ($this->hasProductUrl()): ?>
            <a class="title" href="<?php echo $this->getProductUrl() ?>"><?php echo $this->escapeHtml($this->getProductName()) ?></a>
        <?php else: ?>
            <span class="title"><?php echo $this->escapeHtml($this->getProductName()) ?></span>
        <?php endif; ?>

        <div class="price-box">
            <?php echo Mage::helper('stenik_themebase/checkout_cart')->getItemPriceRenderer($_item)->toHtml(); ?>
        </div>

        <?php if ($_options = $this->getOptionList()):?>
            <?php foreach ($_options as $_option): ?>
                <span class="attribute">
                    <?php echo $this->escapeHtml($_option['label']) ?>:
                    <?php if (is_array($_option['value'])): ?><?php echo nl2br(implode("\n", $_option['value'])) ?><?php else: ?><?php echo $_option['value'] ?><?php endif; ?>
                </span>
            <?php endforeach; ?>
        <?php endif; ?>
        <span class="attribute"><?php echo $this->__('Quantity');?>: <?php echo $this->getQty() ?></span>
    </div>
    <?php
        $deleteUrl = $this->getUrl('checkout/cart/delete', array(
            'id'       => $_item->getId(),
            'form_key' => Mage::getSingleton('core/session')->getFormKey(),
            Mage_Core_Controller_Front_Action::PARAM_NAME_URL_ENCODED => $this->helper('core/url')->urlEncode(Mage::getUrl('checkout/cart')),
        ));
    ?>
    <a href="<?php echo $deleteUrl; ?>" id="delete_item_minicart-<?php echo $_item->getId() ?>" class="item-remove" title="<?php echo Mage::helper('checkout')->__('Remove') ?>">
        <svg aria-hidden="true" class="icon-svg close"><use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#close' ?>"></use></svg>
    </a>
</div>

