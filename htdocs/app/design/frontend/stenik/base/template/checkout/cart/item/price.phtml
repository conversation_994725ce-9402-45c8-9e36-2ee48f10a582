<?php
/**
 * @package stenik_base
 * <AUTHOR> Magento Team <<EMAIL>>
 */
?>
<?php

/**
 * @method self setShowAsRowTotal(boolean)
 * @method null|boolean getShowAsRowTotal()
 * @method self setHidePromoPrice(boolean)
 * @method null|boolean getHidePromoPrice()
 */
$this;

?>

<?php
    $_item = $this->getItem();
    $canApplyMsrp = Mage::helper('catalog')->canApplyMsrp(
        $_item->getProduct(),
        Mage_Catalog_Model_Product_Attribute_Source_Msrp_Type::TYPE_BEFORE_ORDER_CONFIRM
    );


    $weeTaxAmountKey        = 'amount';
    $weeTaxAmountInclTaxKey = 'amount_incl_tax';
    $taxToggleEunitPrefix   = 'eunit';
    $taxToggleUnitPrefix    = 'unit';

    $itemPrice                           = $_item->getCalculationPrice();
    $itemPriceInclTax                    = $this->helper('checkout')->getPriceInclTax($_item);
    $itemCalculationOriginalPrice        = $_item->getCalculationOriginalPrice();
    $itemCalculationOriginalPriceInclTax = $_item->getCalculationOriginalPriceInclTax();
    $weeeTaxAppliedAmount                = $_item->getWeeeTaxAppliedAmount();
    $weeeTaxDisposition                  = $_item->getWeeeTaxDisposition();
    $weeeTaxInclTax                      = Mage::helper('weee')->getWeeeTaxInclTax($_item);

    if ($this->getShowAsRowTotal()) {
        $weeTaxAmountKey        = 'row_amount';
        $weeTaxAmountInclTaxKey = 'row_amount_incl_tax';
        $taxToggleEunitPrefix   = 'esubtotal';
        $taxToggleUnitPrefix    = 'subtotal';

        $itemPrice                           = $_item->getRowTotal();
        $itemPriceInclTax                    = $this->helper('checkout')->getSubtotalInclTax($_item);
        $itemCalculationOriginalPrice        = $_item->getRowCalculationOriginalPrice();
        $itemCalculationOriginalPriceInclTax = $_item->getRowCalculationOriginalPriceInclTax();
        $weeeTaxAppliedAmount                = $_item->getWeeeTaxAppliedRowAmount();
        $weeeTaxDisposition                  = $_item->getWeeeTaxRowDisposition();
        $weeeTaxInclTax                      = Mage::helper('weee')->getRowWeeeTaxInclTax($_item);

        $canApplyMsrp = false;
    }

    if ($this->getHidePromoPrice()) {
        $itemCalculationOriginalPrice        = $itemPrice;
        $itemCalculationOriginalPriceInclTax = $itemPriceInclTax;
    }
?>

<?php if ($canApplyMsrp): ?>
    <span class="cart-price">
        <span class="cart-msrp-unit"><?php echo $this->__('See price before order confirmation.'); ?></span>
        <?php $helpLinkId = 'cart-msrp-help-' . $_item->getId(); ?>
        <a id="<?php echo $helpLinkId ?>" href="#" class="map-help-link"><?php echo $this->__("What's this?"); ?></a>
        <script type="text/javascript">
            Catalog.Map.addHelpLink($('<?php echo $helpLinkId ?>'), "<?php echo $this->__("What's this?") ?>");
        </script>
    </span>
<?php else: ?>
    <?php if ($itemPrice < $itemCalculationOriginalPrice): ?>
        <div class="old-price">
            <?php if ($this->helper('tax')->displayCartPriceExclTax() || $this->helper('tax')->displayCartBothPrices()): ?>
                <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(1, 4), 'sales') && $weeeTaxAppliedAmount): ?>
                    <span class="cart-tax-total" onclick="taxToggle('<?php echo $taxToggleEunitPrefix ?>-item-tax-details<?php echo $_item->getId(); ?>', this, 'cart-tax-total-expanded');">
                <?php else: ?>
                    <span class="cart-price">
                <?php endif; ?>
                    <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'sales') && $weeeTaxAppliedAmount): ?>
                        <?php echo $this->helper('checkout')->formatPrice($itemCalculationOriginalPrice+$weeeTaxAppliedAmount+$weeeTaxDisposition); ?>
                    <?php else: ?>
                        <?php echo $this->helper('checkout')->formatPrice($itemCalculationOriginalPrice) ?>
                    <?php endif; ?>

                </span>
            <?php endif; ?>

            <?php if ($this->helper('tax')->displayCartPriceInclTax() || $this->helper('tax')->displayCartBothPrices()): ?>
                <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(1, 4), 'sales') && $weeeTaxAppliedAmount): ?>
                    <span class="cart-tax-total" onclick="taxToggle('<?php echo $taxToggleUnitPrefix ?>-item-tax-details<?php echo $_item->getId(); ?>', this, 'cart-tax-total-expanded');">
                <?php else: ?>
                    <span class="cart-price">
                <?php endif; ?>

                    <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'sales') && $weeeTaxAppliedAmount): ?>
                        <?php echo $this->helper('checkout')->formatPrice($itemCalculationOriginalPriceInclTax + $weeeTaxInclTax); ?>
                    <?php else: ?>
                        <?php echo $this->helper('checkout')->formatPrice($itemCalculationOriginalPriceInclTax - $weeeTaxDisposition) ?>
                    <?php endif; ?>
                </span>
            <?php endif; ?>
        </div>
    <?php endif ?>

    <div class="<?php echo $itemPrice < $itemCalculationOriginalPrice ? 'special-price' : 'regular-price' ?>">
        <?php if ($this->helper('tax')->displayCartPriceExclTax() || $this->helper('tax')->displayCartBothPrices()): ?>
            <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(1, 4), 'sales') && $weeeTaxAppliedAmount): ?>
                <span class="cart-tax-total" onclick="taxToggle('<?php echo $taxToggleEunitPrefix ?>-item-tax-details<?php echo $_item->getId(); ?>', this, 'cart-tax-total-expanded');">
            <?php else: ?>
                <span class="cart-price">
            <?php endif; ?>
                <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'sales') && $weeeTaxAppliedAmount): ?>
                    <?php echo $this->helper('checkout')->formatPrice($itemPrice+$weeeTaxAppliedAmount+$weeeTaxDisposition); ?>
                <?php else: ?>
                    <?php echo $this->helper('checkout')->formatPrice($itemPrice) ?>
                <?php endif; ?>

            </span>

            <?php if (Mage::helper('weee')->getApplied($_item)): ?>
                <div class="cart-tax-info" id="<?php echo $taxToggleEunitPrefix ?>-item-tax-details<?php echo $_item->getId(); ?>" style="display:none;">
                    <?php if (Mage::helper('weee')->typeOfDisplay($_item, 1, 'sales') && $weeeTaxAppliedAmount): ?>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="weee"><?php echo $tax['title']; ?>: <?php echo Mage::helper('checkout')->formatPrice($tax[$weeTaxAmountKey],true,true); ?></span>
                        <?php endforeach; ?>
                    <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales') && $weeeTaxAppliedAmount): ?>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="weee"><?php echo $tax['title']; ?>: <?php echo Mage::helper('checkout')->formatPrice($tax[$weeTaxAmountKey],true,true); ?></span>
                        <?php endforeach; ?>
                    <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 4, 'sales') && $weeeTaxAppliedAmount): ?>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="weee"><?php echo $tax['title']; ?>: <?php echo Mage::helper('checkout')->formatPrice($tax[$weeTaxAmountKey],true,true); ?></span>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <?php if (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales') && $weeeTaxAppliedAmount): ?>
                    <div class="cart-tax-total" onclick="taxToggle('<?php echo $taxToggleEunitPrefix ?>-item-tax-details<?php echo $_item->getId(); ?>', this, 'cart-tax-total-expanded');">
                        <span class="weee"><?php echo Mage::helper('weee')->__('Total'); ?>: <?php echo $this->helper('checkout')->formatPrice($itemPrice+$weeeTaxAppliedAmount+$weeeTaxDisposition); ?></span>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        <?php endif; ?>

        <?php if ($this->helper('tax')->displayCartPriceInclTax() || $this->helper('tax')->displayCartBothPrices()): ?>
            <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(1, 4), 'sales') && $weeeTaxAppliedAmount): ?>
                <span class="cart-tax-total" onclick="taxToggle('<?php echo $taxToggleUnitPrefix ?>-item-tax-details<?php echo $_item->getId(); ?>', this, 'cart-tax-total-expanded');">
            <?php else: ?>
                <span class="cart-price">
            <?php endif; ?>

                <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'sales') && $weeeTaxAppliedAmount): ?>
                    <?php echo $this->helper('checkout')->formatPrice($itemPriceInclTax + $weeeTaxInclTax); ?>
                <?php else: ?>
                    <?php echo $this->helper('checkout')->formatPrice($itemPriceInclTax-$weeeTaxDisposition) ?>
                <?php endif; ?>

            </span>

            <?php if (Mage::helper('weee')->getApplied($_item)): ?>
                <div class="cart-tax-info" id="<?php echo $taxToggleUnitPrefix ?>-item-tax-details<?php echo $_item->getId(); ?>" style="display:none;">
                    <?php if (Mage::helper('weee')->typeOfDisplay($_item, 1, 'sales') && $weeeTaxAppliedAmount): ?>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="weee"><?php echo $tax['title']; ?>: <?php echo Mage::helper('checkout')->formatPrice($tax[$weeTaxAmountInclTaxKey],true,true); ?></span>
                        <?php endforeach; ?>
                    <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales') && $weeeTaxAppliedAmount): ?>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="weee"><?php echo $tax['title']; ?>: <?php echo Mage::helper('checkout')->formatPrice($tax[$weeTaxAmountInclTaxKey],true,true); ?></span>
                        <?php endforeach; ?>
                    <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 4, 'sales') && $weeeTaxAppliedAmount): ?>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="weee"><?php echo $tax['title']; ?>: <?php echo Mage::helper('checkout')->formatPrice($tax[$weeTaxAmountInclTaxKey],true,true); ?></span>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <?php if (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales') && $weeeTaxAppliedAmount): ?>
                    <div class="cart-tax-total" onclick="taxToggle('<?php echo $taxToggleUnitPrefix ?>-item-tax-details<?php echo $_item->getId(); ?>', this, 'cart-tax-total-expanded');">
                        <span class="weee"><?php echo Mage::helper('weee')->__('Total incl. tax'); ?>: <?php echo $this->helper('checkout')->formatPrice($itemPriceInclTax + $weeeTaxInclTax); ?></span>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        <?php endif; ?>
    </div>
<?php endif; ?>