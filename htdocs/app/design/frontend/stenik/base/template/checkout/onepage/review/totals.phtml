<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2015 X.commerce, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

/**
 * @see Mage_Checkout_Block_Cart_Totals
 */
?>
<?php if ($this->getTotals()): ?>
    <tbody>
        <?php $_colspan = $this->helper('tax')->displayCartBothPrices() ? 5 : 3; ?>
        <?php echo $this->renderTotals(null, $_colspan); ?>
    </tbody>

    <tfoot>
        <?php echo $this->renderTotals('footer', $_colspan); ?>
            <?php if ($this->needDisplayBaseGrandtotal()):?>
            <tr>
                <th>
                    <?php echo $this->helper('sales')->__('Your credit card will be charged for') ?>
                </th>
                <td>
                    <?php echo $this->displayBaseGrandtotal() ?>
                </td>
            </tr>
        <?php endif?>
    </tfoot>
<?php endif; ?>
