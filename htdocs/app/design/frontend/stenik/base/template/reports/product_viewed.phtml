<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2014 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/* @var $this Mage_Reports_Block_Product_Viewed */
?>
<?php if ($_products = $this->getRecentlyViewedProducts()): ?>

    <?php $productImageSufix = $this->helper('stenik_sitesettings')->getproductImagesAltSufix(); ?>

    <div class="wide-area last-seen-products marginB20">
        <div class="container">
            <div class="row">
                <div class="col-xs-12">
                    <div class="row-header marginB30 clearfix">
                        <span class="row-title"><?php echo $this->__('Last seen products');?></span>
                    </div>
                </div>
            </div>
            <div class="row products-list last-seen">
                <?php foreach ($_products as $_item): ?>
                    <div class="col-xs-2">
                        <a href="<?php echo $_item->getProductUrl() ?>" class="product-box" rel="nofollow" title="<?php echo $this->stripTags($this->getImageLabel($_item, 'small_image'), null, true) ?>">
                            <span class="image-wrapper">
                                <img
                                    src="<?php echo $this->helper('catalog/image')->init($_item, 'small_image')->resize(285,285); ?>"
                                    srcset="<?php echo $this->helper('catalog/image')->init($_item, 'small_image')->resize(570, 570); ?> x2"
                                    alt="<?php echo $this->stripTags($this->getImageLabel($_item, 'small_image'), null, true) ?>"
                                    title="<?php echo $this->stripTags($this->getImageLabel($_item, 'small_image'), null, true)  . ' - ' . $productImageSufix ?>"
                                >
                            </span>
                        </a>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
<?php endif; ?>