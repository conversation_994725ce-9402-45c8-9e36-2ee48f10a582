<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2016 X.commerce, Inc. and affiliates (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php if($this->getResultCount()): ?>

    <div class="text-page">
        <?php echo $this->getMessagesBlock()->toHtml() ?>

        <?php if ($this->helper('rss/catalog')->getTagFeedUrl()): ?>
            <a href="<?php echo $this->helper('rss/catalog')->getTagFeedUrl() ?>" class="nobr link-rss"><?php echo $this->__('Subscribe to Feed') ?></a>
        <?php endif; ?>

        <h2><?php echo ($this->getHeaderText() || $this->getHeaderText() === false) ? $this->getHeaderText() : $this->__("Search results for '%s'", $this->helper('catalogsearch')->getEscapedQueryText()) ?></h2>

        <?php if ($messages = $this->getNoteMessages()):?>
            <p class="note-msg">
                <?php foreach ($messages as $message):?>
                    <?php echo $message?><br />
                <?php endforeach;?>
            </p>
        <?php endif; ?>
    </div>

    <?php echo $this->getProductListHtml() ?>

<?php else: ?>

    <div class="text-page">
        <h2>
            <?php echo ($this->getHeaderText() || $this->getHeaderText() === false) ? $this->getHeaderText() : $this->__("Search results for '%s'", $this->helper('catalogsearch')->getEscapedQueryText()) ?>
        </h2>

        <p class="note-msg">
            <?php echo ($this->getNoResultText()) ? $this->getNoResultText() : $this->__('Your search returns no results.') ?>
            <?php if ($messages = $this->getNoteMessages()):?>
                <?php foreach ($messages as $message):?>
                <br /><?php echo $message?>
                <?php endforeach;?>
            <?php endif; ?>
        </p>
    </div>

<?php endif; ?>
