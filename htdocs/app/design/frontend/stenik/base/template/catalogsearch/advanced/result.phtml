<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2016 X.commerce, Inc. and affiliates (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<?php echo $this->getMessagesBlock()->toHtml() ?>

<div class="text-page">
    <?php if($this->getResultCount()): ?>
        <h3 class="advanced-search-amount">
            <?php echo $this->helper('catalogsearch')->__('<strong>%d item(s)</strong> were found using the following search criteria', $this->getResultCount()); ?>
        </h3>
    <?php else: ?>
        <p class="error-msg">
            <?php echo $this->helper('catalogsearch')->__('No items were found using the following search criteria.');?>
            <!-- <a href="<?php //echo $this->getFormUrl(); ?>"><?php //echo $this->helper('catalogsearch')->__('Modify your search'); ?></a> -->
            <a href="<?php echo $this->getUrl('/') ?>"><?php echo $this->helper('catalogsearch')->__('Modify your search'); ?></a>
        </p>
    <?php endif; ?>

    <div class="advanced-search-summary">
        <?php $searchCriterias=$this->getSearchCriterias(); ?>
            <ul>
                <?php foreach (array('left', 'right') as $side): ?>
                    <?php if(@$searchCriterias[$side]): ?>
                        <?php foreach($searchCriterias[$side] as $criteria): ?>
                            <li><strong><?php echo $this->escapeHtml($this->helper('catalog')->__($criteria['name'])); ?>:</strong> <?php echo $this->escapeHtml($criteria['value']); ?></li>
                        <?php endforeach; ?>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ul>
        <?php if($this->getResultCount()): ?>
            <p>
                <?php echo $this->helper('catalogsearch')->__("Don't see what you're looking for?"); ?>
                <a href="<?php echo $this->getUrl('/') ?>"><?php echo $this->helper('catalogsearch')->__('Modify your search'); ?></a>
            </p>
        <?php endif; ?>
    </div>
</div>

<?php if($this->getResultCount()): ?>
    <?php echo $this->getProductListHtml() ?>
<?php endif; ?>

<?php $this->getSearchCriterias(); ?>
