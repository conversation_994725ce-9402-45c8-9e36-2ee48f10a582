<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2015 X.commerce, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/* @var $this Mage_Core_Block_Template */
/* @var $catalogSearchHelper Mage_Catalogsearch_Helper_Data */
?>

<form id="responsive_search_mini_form" class="search-form" action="<?php echo $this->helper('catalogsearch')->getResultUrl() ?>" method="get">
    <label for="responsive_search"><span class="screenreader"><?php echo Mage::helper('core')->quoteEscape($this->__('Search')) ?></span></label>
    <input id="responsive_search" type="text" name="<?php echo $this->helper('catalogsearch')->getQueryParamName() ?>" value="" class="search-input" placeholder="<?php echo $this->__('Search by brand, model or extra');?>..." />
    <button class="search-submit" title="<?php echo $this->__('Search');?>">
    	<svg aria-hidden="true" class="icon-svg search">
    	    <use xlink:href="<?php echo $this->getBaseUrl().'svg/svg-sprite.svg' . '#search' ?>"></use>
    	</svg>
    </button>
    <div id="responsive_search_autocomplete" class="search-autocomplete"></div>
</form>
