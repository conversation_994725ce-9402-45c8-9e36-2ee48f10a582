<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2016 X.commerce, Inc. and affiliates (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

/**
 * @see Mage_Paypal_Block_Express_Review_Details
 */
?>
<table id="details-table" class="data-table">
    <?php if ($this->helper('tax')->displayCartBothPrices()): $colspan = $rowspan = 2; else: $colspan = $rowspan = 1; endif; ?>
    <?php if ($this->helper('tax')->displayCartBothPrices()): ?>
    <?php endif; ?>
    <thead>
        <tr>
            <th rowspan="<?php echo $rowspan ?>"><?php echo $this->__('Product Name') ?></th>
            <th colspan="<?php echo $colspan ?>" class="a-center"><?php echo $this->__('Price') ?></th>
            <th rowspan="<?php echo $rowspan ?>" class="a-center"><?php echo $this->__('Qty') ?></th>
            <th colspan="<?php echo $colspan ?>" class="a-center"><?php echo $this->__('Subtotal') ?></th>
        </tr>
        <?php if ($this->helper('tax')->displayCartBothPrices()): ?>
            <tr>
                <th class="a-right"><?php echo $this->helper('tax')->getIncExcTaxLabel(false) ?></th>
                <th><?php echo $this->helper('tax')->getIncExcTaxLabel(true) ?></th>
                <th class="a-right"><?php echo $this->helper('tax')->getIncExcTaxLabel(false) ?></th>
                <th><?php echo $this->helper('tax')->getIncExcTaxLabel(true) ?></th>
            </tr>
        <?php endif; ?>
    </thead>
    <tbody>
        <?php foreach($this->getItems() as $_item): ?>
            <?php echo $this->getItemHtml($_item) ?>
        <?php endforeach ?>
    </tbody>
</table>
<div class="totals paypal-total">
    <table class="total-table">
        <?php echo $this->getChildHtml('totals'); ?>
    </table>
</div>
<script type="text/javascript">decorateTable('details-table');</script>
