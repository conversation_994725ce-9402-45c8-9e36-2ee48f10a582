<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2016 X.commerce, Inc. and affiliates (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * @var $this Mage_Paypal_Block_Express_Shortcut
 */
?>
<?php $shortcutHtmlId = $this->getShortcutHtmlId() ?>
<?php $bmlShortcutHtmlId = $this->getBmlShortcutHtmlId() ?>

<div class="paypal-shortcut">
    <p class="or"><span><?php echo $this->__('or'); ?></span></p>
    <a class="paypal-link" data-action="checkout-form-submit" id="<?php echo $shortcutHtmlId ?>" href="<?php echo $this->getCheckoutUrl() ?>">
        <img class="paypal-logo" src="<?php echo $this->getImageUrl() ?>" alt="<?php echo Mage::helper('paypal')->__('Checkout with PayPal'); ?>" title="<?php echo Mage::helper('paypal')->__('Checkout with PayPal'); ?>" />
    </a>    
    <?php if ($this->getIsBmlEnabled()): ?>
        <a id="<?php echo $bmlShortcutHtmlId ?>" href="<?php echo $this->getBmlCheckoutUrl() ?>"><img src="<?php echo $this->getBmlImageUrl() ?>" alt="<?php echo Mage::helper('core')->quoteEscape(Mage::helper('paypal')->__('Checkout with PayPal Paypal Credit')); ?>" title="<?php echo Mage::helper('core')->quoteEscape(Mage::helper('paypal')->__('Checkout with PayPal Paypal Credit')); ?>" /></a>
        <a href="<?php echo $this->getMarketMessageUrl() ?>"><img src="<?php echo $this->getMarketMessage() ?>" /></a>
    <?php endif; ?>
</div>


<?php if ($this->getConfirmationUrl() || $this->getIsInCatalogProduct()): ?>
    <?php if ($this->getIsInCatalogProduct()): ?>
        <input type="hidden" id="pp_checkout_url" name="return_url" value="" />
    <?php endif; ?>

    <script type="text/javascript">
        $$('#<?php echo $shortcutHtmlId ?>','#<?php echo $bmlShortcutHtmlId ?>').invoke('observe', 'click', function(event) {
            <?php if ($this->getConfirmationUrl()): ?>
                if (confirm('<?php echo Mage::helper('core')->jsQuoteEscape($this->getConfirmationMessage()) ?>')) {
                    this.href = '<?php echo $this->getConfirmationUrl() ?>';
                }
            <?php endif; ?>
            <?php if ($this->getIsInCatalogProduct()): ?>
                $('pp_checkout_url').value = this.href;
                productAddToCartForm.submit(this);
                event.stop();
            <?php endif; ?>
        });
    </script>
<?php endif; ?>
