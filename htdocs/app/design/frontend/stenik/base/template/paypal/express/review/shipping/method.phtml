<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2016 X.commerce, Inc. and affiliates (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/** @var $this Mage_Paypal_Block_Express_Review */
?>
<fieldset id="shipping-method-container">
<?php if ($this->getCanEditShippingMethod() || !$this->getCurrentShippingRate()):?>
        <?php if ($groups = $this->getShippingRateGroups()):?>
            <?php $currentRate = $this->getCurrentShippingRate(); ?>
            <select name="shipping_method" id="shipping_method" class="required-entry">
            <?php if (!$currentRate):?>
                <option value=""><?php echo $this->__('Please select a shipping method...') ?></option>
            <?php endif;?>
            <?php foreach ($groups as $code => $rates):?>
                <optgroup label="<?php echo $this->escapeHtml($this->getCarrierName($code)) ?>" style="font-style:normal;">
                <?php foreach ($rates as $rate):?>
                    <option value="<?php echo $this->renderShippingRateValue($rate)?>"<?php echo ($currentRate === $rate) ? ' selected="selected"' : '' ;?>>
                        <?php echo $this->renderShippingRateOption($rate)?>
                    </option>
                <?php endforeach;?>
                </optgroup>
            <?php endforeach;?>
            </select>
        <?php else: ?>
            <p><strong><?php echo $this->__('Sorry, no quotes are available for this order at this time.') ?></strong></p>
        <?php endif;?>
<?php else: ?>
    <p><strong><?php echo $this->renderShippingRateOption($this->getCurrentShippingRate())?></strong></p>
<?php endif; ?>
</fieldset>
<div style="display:none" id="shipping_method_update"><p><?php echo $this->__('Please update order data to get shipping methods and rates') ?></p></div>
