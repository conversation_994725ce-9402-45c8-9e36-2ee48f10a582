<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2016 X.commerce, Inc. and affiliates (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php if(!$this->getDontDisplayContainer()): ?>
<script type="text/javascript">
//<![CDATA[
if(!window.toogleVisibilityOnObjects) {
    var toogleVisibilityOnObjects = function(source, objects) {
        if($(source) && $(source).checked) {
            objects.each(function(item){
                $(item).show();
                $$('#' + item + ' .input-text').each(function(item) {
                    item.removeClassName('validation-passed');
                });
            });
        } else {
            objects.each(function(item){
                if ($(item)) {
                    $(item).hide();
                    $$('#' + item + ' .input-text').each(function(sitem) {
                        sitem.addClassName('validation-passed');
                    });
                    $$('#' + item + ' .giftmessage-area').each(function(sitem) {
                        sitem.value = '';
                    });
                    $$('#' + item + ' .checkbox').each(function(sitem) {
                        sitem.checked = false;
                    });
                    $$('#' + item + ' .select').each(function(sitem) {
                        sitem.value = '';
                    });
                    $$('#' + item + ' .price-box').each(function(sitem) {
                        sitem.addClassName('no-display');
                    });
                }
            });
        }
    }
}

if(!window.toogleRequired) {
    var toogleRequired = function (source, objects)
    {
        if(!$(source).value.blank()) {
            objects.each(function(item) {
               $(item).addClassName('required-entry');
            });
        } else {
            objects.each(function(item) {
                if (typeof shippingMethod != 'undefined' && shippingMethod.validator) {
                   shippingMethod.validator.reset(item);
                }
                $(item).removeClassName('required-entry');
            });

        }
    }
}
if(window.shipping) {

shipping.onSave = function(evt){
    new Ajax.Updater('onepage-checkout-shipping-method-additional-load', '<?php echo $this->getAdditionalUrl(); ?>', {onSuccess: function() {
          this.nextStep(evt);
    }.bind(this), evalScripts:true});
}.bindAsEventListener(shipping);

billing.onSave = function(evt){
    new Ajax.Updater('onepage-checkout-shipping-method-additional-load', '<?php echo $this->getAdditionalUrl(); ?>', {onSuccess: function() {
          this.nextStep(evt);
    }.bind(this), evalScripts:true});
}.bindAsEventListener(billing);

}
//]]>
</script>
<?php endif ?>
<?php if ($this->isMessagesAvailable() || $this->isItemsAvailable()): ?>
<?php switch ($this->getType()): ?>
<?php case 'onepage_checkout': ?>
    <div class="gift-messages">
        <h3><?php echo $this->__('Do you have any gift items in your order?'); ?></h3>
        <p class="control">
            <input type="checkbox" name="allow_gift_messages" id="allow_gift_messages" value="1" onclick="toogleVisibilityOnObjects(this, ['allow-gift-message-container']);"<?php if($this->getItemsHasMesssages() || $this->getEntityHasMessage()): ?> checked="checked"<?php endif; ?> class="checkbox" />
            <label for="allow_gift_messages"><?php echo $this->__('Add gift options.') ?></label>
        </p>
    </div>
    <div class="gift-messages-form" id="allow-gift-message-container">
        <div class="inner-box">
            <?php if ($this->isMessagesAvailable()): ?>
                <h4><?php echo $this->__('Gift Options for the Entire Order.'); ?></h4>
                <p>
                    <input type="checkbox" name="allow_gift_messages_for_order" id="allow_gift_messages_for_order" value="1" onclick="toogleVisibilityOnObjects(this, ['allow-gift-messages-for-order-container']);"<?php if($this->getEntityHasMessage()): ?> checked="checked"<?php endif; ?> class="checkbox" />
                    <label for="allow_gift_messages_for_order"><?php echo $this->__('Add gift options for the Entire Order') ?></label>
                </p>
                <div class="allow-gift-messages-for-order-container" id="allow-gift-messages-for-order-container" style="display:none">
                    <p><?php echo $this->__('You can leave this box blank if you do not wish to add a gift message for whole order.') ?></p>
                    <input type="hidden" name="giftmessage[<?php echo $this->getEntity()->getId() ?>][type]"  value="quote" />
                    <ul class="form-list">
                        <li class="fields">
                            <div class="field">
                                <label for="gift-message-whole-from"><?php echo $this->__('From') ?></label>
                                <div class="input-box">
                                    <input type="text" name="giftmessage[<?php echo $this->getEntity()->getId() ?>][from]" id="gift-message-whole-from" title="<?php echo Mage::helper('core')->quoteEscape($this->__('From')) ?>"  value="<?php echo $this->getEscaped($this->getMessage()->getSender(), $this->getDefaultFrom()) ?>" class="input-text validation-passed" />
                                </div>
                            </div>
                            <div class="field">
                                <label for="gift-message-whole-to"><?php echo $this->__('To') ?></label>
                                <div class="input-box">
                                    <input type="text" name="giftmessage[<?php echo $this->getEntity()->getId() ?>][to]" id="gift-message-whole-to" title="<?php echo Mage::helper('core')->quoteEscape($this->__('To')) ?>" value="<?php echo $this->getEscaped($this->getMessage()->getRecipient(), $this->getDefaultTo()) ?>" class="input-text validation-passed" />
                                </div>
                            </div>
                        </li>
                        <li class="wide">
                            <label for="gift-message-whole-message"><?php echo $this->__('Message') ?></label>
                            <div class="input-box">
                                <textarea id="gift-message-whole-message" onchange="toogleRequired('gift-message-whole-message', ['gift-message-whole-from','gift-message-whole-to'])" class="input-text validation-passed giftmessage-area" name="giftmessage[<?php echo $this->getEntity()->getId() ?>][message]" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Message')) ?>" rows="5" cols="10"><?php echo $this->getEscaped($this->getMessage()->getMessage()) ?></textarea>
                            </div>
                        </li>
                    </ul>
                    <script type="text/javascript">
                    //<![CDATA[
                        toogleRequired('gift-message-whole-message', ['gift-message-whole-from','gift-message-whole-to']);
                    //]]>
                    </script>
                </div>
             <?php endif; ?>
             <?php if($this->isItemsAvailable()): ?>
                 <h4><?php echo $this->__('Gift Options for Individual Items'); ?></h4>
                 <p>
                    <input type="checkbox" name="allow_gift_messages_for_items" id="allow_gift_messages_for_items" value="1" onclick="toogleVisibilityOnObjects(this, ['allow-gift-messages-for-items-container']);"<?php if($this->getItemsHasMesssages()): ?> checked="checked"<?php endif; ?> class="checkbox" />
                    <label for="allow_gift_messages_for_items"><?php echo $this->__('Add gift options for Individual Items') ?></label>
                </p>
                <div id="allow-gift-messages-for-items-container">
                 <p><?php echo $this->__('You can leave this box blank if you do not wish to add a gift message for the item.') ?></p>
                 <ol>
                 <?php foreach($this->getItems() as $_index=>$_item): ?>
                 <?php $_product=$_item->getProduct() ?>
                 <li class="item">
                     <div class="product-img-box">
                         <p class="product-image">
                             <img src="<?php echo $this->helper('catalog/image')->init($_product, 'thumbnail')->resize(120,120); ?>" width="75" height="75" alt="<?php echo $this->escapeHtml($_product->getName()) ?>" title="<?php echo $this->escapeHtml($_product->getName()) ?>" />
                         </p>
                         <p class="number"><?php echo $this->__('Item %d of %d', $_index+1, $this->countItems()) ?></p>
                     </div>
                     <div class="details">
                         <div class="f-fix">
                             <h5 class="product-name"><?php echo $this->escapeHtml($_product->getName()) ?></h5>
                             <input type="hidden" name="giftmessage[<?php echo $_item->getId() ?>][type]" value="quote_item" />
                              <ul class="form-list">
                                 <li class="fields">
                                     <div class="field">
                                         <label for="gift-message-<?php echo $_item->getId() ?>-from"><?php echo $this->__('From') ?></label>
                                         <div class="input-box">
                                             <input type="text" name="giftmessage[<?php echo $_item->getId() ?>][from]" id="gift-message-<?php echo $_item->getId() ?>-from" title="<?php echo Mage::helper('core')->quoteEscape($this->__('From')) ?>" value="<?php echo $this->getEscaped($this->getMessage($_item)->getSender(), $this->getDefaultFrom()) ?>" class="input-text validation-passed" />
                                         </div>
                                     </div>
                                     <div class="field">
                                         <label for="gift-message-<?php echo $_item->getId() ?>-to"><?php echo $this->__('To') ?></label>
                                         <div class="input-box">
                                             <input type="text" name="giftmessage[<?php echo $_item->getId() ?>][to]" id="gift-message-<?php echo $_item->getId() ?>-to" title="<?php echo Mage::helper('core')->quoteEscape($this->__('To')) ?>" value="<?php echo $this->getEscaped($this->getMessage($_item)->getRecipient(), $this->getDefaultTo()) ?>" class="input-text validation-passed" />
                                         </div>
                                     </div>
                                 </li>
                                 <li class="wide">
                                     <label for="gift-message-<?php echo $_item->getId() ?>-message"><?php echo $this->__('Message') ?></label>
                                     <div class="input-box">
                                         <textarea id="gift-message-<?php echo $_item->getId() ?>-message" onchange="toogleRequired('gift-message-<?php echo $_item->getId() ?>-message', ['gift-message-<?php echo $_item->getId() ?>-from','gift-message-<?php echo $_item->getId() ?>-to'])" class="input-text validation-passed giftmessage-area" name="giftmessage[<?php echo $_item->getId() ?>][message]" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Message')) ?>" rows="5" cols="40"><?php echo $this->getEscaped($this->getMessage($_item)->getMessage()) ?></textarea>
                                     </div>
                                 </li>
                             </ul>
                             <script type="text/javascript">
                             //<![CDATA[
                                toogleRequired('gift-message-<?php echo $_item->getId() ?>-message', ['gift-message-<?php echo $_item->getId() ?>-from','gift-message-<?php echo $_item->getId() ?>-to']);
                             //]]>
                             </script>
                         </div>
                     </div>
                 </li>
                 <?php endforeach; ?>
             </ol>
             </div>
             <?php endif; ?>
        </div>
    </div>
    <script type="text/javascript">
    //<![CDATA[
        toogleVisibilityOnObjects('allow_gift_messages', ['allow-gift-message-container']);
        toogleVisibilityOnObjects('allow_gift_messages_for_order', ['allow-gift-messages-for-order-container']);
        toogleVisibilityOnObjects('allow_gift_messages_for_items', ['allow-gift-messages-for-items-container']);
    //]]>
    </script>
<?php break; ?>
<?php case 'multishipping_adress_checkbox': ?>

<?php break; ?>
<?php case 'multishipping_adress': ?>
    <div class="gift-messages">
        <h3><?php echo $this->__('Do you have any gift items in your order?'); ?></h3>
        <p class="control">
            <input type="checkbox" name="allow_gift_messages_<?php echo $this->getEntity()->getId() ?>" id="allow_gift_messages_<?php echo $this->getEntity()->getId() ?>" value="1" onclick="toogleVisibilityOnObjects(this, ['allow-gift-message-container-<?php echo $this->getEntity()->getId() ?>']);"<?php if($this->getItemsHasMesssages() || $this->getEntityHasMessage()): ?> checked="checked"<?php endif ?> class="checkbox" />
            <label for="allow_gift_messages_<?php echo $this->getEntity()->getId() ?>"><?php echo $this->__('Add gift options') ?></label>
        </p>
    </div>
    <div class="gift-messages-form" id="allow-gift-message-container-<?php echo $this->getEntity()->getId() ?>">
        <div class="inner-box">
            <?php if ($this->isMessagesAvailable()): ?>
            <h4><?php echo $this->__('Gift Options for this address.'); ?></h4>
            <p>
                 <input type="checkbox" name="allow_gift_messages_for_order_<?php echo $this->getEntity()->getId() ?>" id="allow_gift_messages_for_order_<?php echo $this->getEntity()->getId() ?>" value="1" onclick="toogleVisibilityOnObjects(this, ['allow-gift-messages-for-order-container-<?php echo $this->getEntity()->getId() ?>']);"<?php if($this->getEntityHasMessage()): ?> checked="checked"<?php endif; ?> class="checkbox" />
                 <label for="allow_gift_messages_for_order"><?php echo $this->__('Add gift options for the Entire Order') ?></label>
            </p>
            <div id="allow-gift-messages-for-order-container-<?php echo $this->getEntity()->getId() ?>">
                <p><?php echo $this->__('You can leave this box blank if you do not wish to add a gift message for this address.') ?></p>
                <input type="hidden" name="giftmessage[<?php echo $this->getEntity()->getId() ?>][type]" value="quote_address" />
                <ul class="form-list">
                    <li class="fields">
                        <div class="field">
                            <label for="gift-message-<?php echo $this->getEntity()->getId() ?>-from"><?php echo $this->__('From') ?></label>
                            <div class="input-box">
                                <input type="text" name="giftmessage[<?php echo $this->getEntity()->getId() ?>][from]" id="gift-message-<?php echo $this->getEntity()->getId() ?>-from" title="<?php echo Mage::helper('core')->quoteEscape($this->__('From')) ?>" value="<?php echo $this->getEscaped($this->getMessage()->getSender(), $this->getDefaultFrom()) ?>" class="input-text validation-passed" />
                            </div>
                        </div>
                        <div class="field">
                            <label for="gift-message-<?php echo $this->getEntity()->getId() ?>-to"><?php echo $this->__('To') ?></label>
                            <div class="input-box">
                                <input type="text" name="giftmessage[<?php echo $this->getEntity()->getId() ?>][to]" id="gift-message-<?php echo $this->getEntity()->getId() ?>-to" title="<?php echo Mage::helper('core')->quoteEscape($this->__('To')) ?>" value="<?php echo $this->getEscaped($this->getMessage()->getRecipient(), $this->getDefaultTo()) ?>" class="input-text validation-passed" />
                            </div>
                        </div>
                    </li>
                    <li class="wide">
                        <label for="gift-message-<?php echo $this->getEntity()->getId() ?>-message"><?php echo $this->__('Message') ?></label>
                        <div class="input-box">
                            <textarea id="gift-message-<?php echo $this->getEntity()->getId() ?>-message" onchange="toogleRequired('gift-message-<?php echo $this->getEntity()->getId() ?>-message', ['gift-message-<?php echo $this->getEntity()->getId() ?>-from','gift-message-<?php echo $this->getEntity()->getId() ?>-to'])" class="input-text validation-passed giftmessage-area" name="giftmessage[<?php echo $this->getEntity()->getId() ?>][message]" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Message')) ?>" rows="5" cols="40"><?php echo $this->getEscaped($this->getMessage()->getMessage()) ?></textarea>
                        </div>
                    </li>
                </ul>
                <script type="text/javascript">
                //<![CDATA[
                    var shippingMethod = new VarienForm('shipping_method_form');
                    toogleRequired('gift-message-<?php echo $this->getEntity()->getId() ?>-message', ['gift-message-<?php echo $this->getEntity()->getId() ?>-from','gift-message-<?php echo $this->getEntity()->getId() ?>-to']);
                //]]>
                </script>
            </div>
            <?php endif; ?>
            <?php if($this->isItemsAvailable()): ?>
            <h4><?php echo $this->__('Gift Options for Individual Items'); ?></h4>
            <p>
                <input type="checkbox" name="allow_gift_messages_for_items_<?php echo $this->getEntity()->getId() ?>" id="allow_gift_messages_for_items_<?php echo $this->getEntity()->getId() ?>" value="1" onclick="toogleVisibilityOnObjects(this, ['allow-gift-messages-for-items-container-<?php echo $this->getEntity()->getId() ?>']);"<?php if($this->getItemsHasMesssages()): ?> checked="checked"<?php endif; ?> class="checkbox" />
                <label for="allow_gift_messages_for_items_<?php echo $this->getEntity()->getId() ?>"><?php echo $this->__('Add gift options for Individual Items') ?></label>
            </p>
            <div id="allow-gift-messages-for-items-container-<?php echo $this->getEntity()->getId() ?>">
             <p><?php echo $this->__('You can leave this box blank if you do not wish to add a gift message for the item.') ?></p>
             <ol>
             <?php foreach($this->getItems() as $_index=>$_item): ?>
             <?php $_product=$_item->getProduct() ?>
                 <li class="item">
                     <div class="product-img-box">
                         <p class="product-image"><img src="<?php echo $this->helper('catalog/image')->init($_product, 'small_image')->resize(75); ?>" width="75" height="75" alt="<?php echo $this->escapeHtml($_product->getName()) ?>" title="<?php echo $this->escapeHtml($_product->getName()) ?>" /></p>
                         <p class="number"><?php echo $this->__('Item %d of %d', $_index+1, $this->countItems()) ?></p>
                     </div>
                     <div class="details">
                         <div class="f-fix">
                             <input type="hidden" name="giftmessage[<?php echo $_item->getId() ?>][type]" value="quote_address_item" />
                             <input type="hidden" name="giftmessage[<?php echo $_item->getId() ?>][address]" value="<?php echo $this->getEntity()->getId()?>" />
                             <h5 class="product-name"><?php echo $this->escapeHtml($_product->getName()) ?></h5>
                             <ul class="form-list">
                                 <li class="fields">
                                     <div class="field">
                                         <label for="gift-message-<?php echo $_item->getId() ?>-from"><?php echo $this->__('From') ?></label>
                                         <div class="input-box">
                                             <input type="text" name="giftmessage[<?php echo $_item->getId() ?>][from]" id="gift-message-<?php echo $_item->getId() ?>-from" title="<?php echo Mage::helper('core')->quoteEscape($this->__('From')) ?>"  value="<?php echo $this->getEscaped($this->getMessage($_item)->getSender(), $this->getDefaultFrom()) ?>" class="input-text validation-passed" />
                                         </div>
                                     </div>
                                     <div class="field">
                                         <label for="gift-message-<?php echo $_item->getId() ?>-to"><?php echo $this->__('To') ?></label>
                                         <div class="input-box">
                                             <input type="text" name="giftmessage[<?php echo $_item->getId() ?>][to]" id="gift-message-<?php echo $_item->getId() ?>-to" title="<?php echo Mage::helper('core')->quoteEscape($this->__('To')) ?>" value="<?php echo $this->getEscaped($this->getMessage($_item)->getRecipient(), $this->getDefaultTo()) ?>" class="input-text validation-passed" />
                                         </div>
                                     </div>
                                 </li>
                                 <li class="wide">
                                     <label for="gift-message-<?php echo $_item->getId() ?>-message"><?php echo $this->__('Message') ?></label>
                                     <div class="input-box">
                                         <textarea id="gift-message-<?php echo $_item->getId() ?>-message" onchange="toogleRequired('gift-message-<?php echo $_item->getId() ?>-message', ['gift-message-<?php echo $_item->getId() ?>-from','gift-message-<?php echo $_item->getId() ?>-to'])" class="input-text validation-passed  giftmessage-area" name="giftmessage[<?php echo $_item->getId() ?>][message]" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Message')) ?>" rows="5" cols="10"><?php echo $this->getEscaped($this->getMessage($_item)->getMessage()) ?></textarea>
                                     </div>
                                 </li>
                             </ul>
                             <script type="text/javascript">
                             //<![CDATA[
                                 toogleRequired('gift-message-<?php echo $_item->getId() ?>-message', ['gift-message-<?php echo $_item->getId() ?>-from','gift-message-<?php echo $_item->getId() ?>-to']);
                             //]]>
                             </script>
                         </div>
                     </div>
                 </li>
             <?php endforeach; ?>
             </ol>
            </div>
            <?php endif; ?>
        </div>
    </div>
    <script type="text/javascript">
    //<![CDATA[
        toogleVisibilityOnObjects('allow_gift_messages_<?php echo $this->getEntity()->getId() ?>', ['allow-gift-message-container-<?php echo $this->getEntity()->getId() ?>']);
        toogleVisibilityOnObjects('allow_gift_messages_for_order_<?php echo $this->getEntity()->getId() ?>', ['allow-gift-messages-for-order-container-<?php echo $this->getEntity()->getId() ?>']);
        toogleVisibilityOnObjects('allow_gift_messages_for_items_<?php echo $this->getEntity()->getId() ?>', ['allow-gift-messages-for-items-container-<?php echo $this->getEntity()->getId() ?>']);
    //]]>
    </script>
    <?php break; ?>
<?php endswitch ?>
<?php endif; ?>
