<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2016 X.commerce, Inc. and affiliates (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<div class="page-title">
    <h1><?php echo $this->__('Edit Account Information') ?></h1>
</div>
<?php echo $this->getMessagesBlock()->toHtml() ?>
<form action="<?php echo $this->getUrl('customer/account/editPost') ?>" method="post" id="form-validate" autocomplete="off">
    <div class="fieldset">
        <?php echo $this->getBlockHtml('formkey')?>
        <h2 class="legend"><?php echo $this->__('Account Information') ?></h2>
        <ul class="form-list">
            <li class="fields">
                <?php echo $this->getLayout()->createBlock('customer/widget_name')->setObject($this->getCustomer())->toHtml() ?>
            </li>
            <li class="fields">
                <div class="field">                    
                    <label for="email" class="required">* <?php echo $this->__('Email') ?></label>
                    <div class="input-box">
                        <input type="email" name="email" id="email" value="<?php echo $this->escapeHtml($this->getCustomer()->getEmail()) ?>" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Email')) ?>" class="input-text required-entry validate-email" />
                    </div>
                </div>
                <div class="field">
                    <label for="current_password" class="required">* <?php echo Mage::helper('core')->quoteEscape($this->__('Current Password')) ?></label>
                    <div class="input-box">
                        <!-- This is a dummy hidden field to trick firefox from auto filling the password -->
                        <input type="text" class="input-text no-display" name="dummy" id="dummy" />
                        <input type="password" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Current Password')) ?>" class="input-text required-entry" name="current_password" id="current_password" />
                    </div>
                </div>
            </li>
            <?php $_dob = $this->getLayout()->createBlock('customer/widget_dob') ?>
            <?php if ($_dob->isEnabled()): ?>
                <li><?php echo $_dob->setDate($this->getCustomer()->getDob())->toHtml() ?></li>
            <?php endif ?>
            <?php $_taxvat = $this->getLayout()->createBlock('customer/widget_taxvat') ?>
            <?php if ($_taxvat->isEnabled()): ?>
                <li><?php echo $_taxvat->setTaxvat($this->getCustomer()->getTaxvat())->toHtml() ?></li>
            <?php endif ?>
            <?php $_gender = $this->getLayout()->createBlock('customer/widget_gender') ?>
            <?php if ($_gender->isEnabled()): ?>
                <li><?php echo $_gender->setGender($this->getCustomer()->getGender())->toHtml() ?></li>
            <?php endif ?>
            <li class="control">
                <input type="checkbox" name="change_password" id="change_password" value="1" onclick="setPasswordForm(this.checked)" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Change Password')) ?>"<?php if($this->getCustomer()->getChangePassword()==1): ?> checked="checked"<?php endif; ?> class="checkbox" />
                <label for="change_password"><?php echo $this->__('Change Password') ?></label>
            </li>
        </ul>
    </div>
    <div class="fieldset" style="display:none;">
        <h2 class="legend"><?php echo $this->__('Change Password') ?></h2>
        <ul class="form-list">
            <li class="fields">
                <div class="field">
                    <label for="password" class="required">* <?php echo $this->__('New Password') ?></label>
                    <div class="input-box">
                        <input type="password" title="<?php echo Mage::helper('core')->quoteEscape($this->__('New Password')) ?>" class="input-text required-entry validate-password" name="password" id="password" />
                    </div>
                </div>
                <div class="field">
                    <label for="confirmation" class="required">* <?php echo $this->__('Confirm New Password') ?></label>
                    <div class="input-box">
                        <input type="password" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Confirm New Password')) ?>" class="input-text required-entry validate-cpassword" name="confirmation" id="confirmation" />
                    </div>
                </div>
            </li>
        </ul>
    </div>
    <div class="buttons-set">
        <button type="submit" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Save')) ?>" class="button"><?php echo $this->__('Save') ?></button>
    </div>
</form>
<script type="text/javascript">
    var dataForm = new VarienForm('form-validate', true);
    function setPasswordForm(arg){
        $('password').up('.fieldset')[arg ? 'show': 'hide']();
    }

    <?php if($this->getCustomer()->getChangePassword()): ?>
        setPasswordForm(true);
    <?php endif; ?>
</script>
