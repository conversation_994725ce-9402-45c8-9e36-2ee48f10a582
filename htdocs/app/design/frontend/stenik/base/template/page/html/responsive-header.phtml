<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2014 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/**
 * @var Mage_Page_Block_Html_Header $this
 */
?>

<?php
    $_helper = $this->helper('catalog/product_compare');
    $_items = $_helper->getItemCount() > 0 ? $_helper->getItemCollection() : null;
?>

<aside class="responsive-header">
    <div class="responsive-menu">
        <a href="javascript:;" class="open-responsive-menu"></a>
        <div class="responsive-menu-sub">
            <div class="tabs-links-content">
                <a href="javascript:;" class="responsive-tab-link tab1 opened"><?php echo $this->__('Menu');?></a>
                <a href="javascript:;" class="responsive-tab-link tab2"><?php echo $this->__('Profile');?></a>
            </div>
            <div class="responsive-menu-tab tab1 opened">
                <?php echo $this->getChildHtml('top.menu.responsive') ?>
            </div>
            <div class="responsive-menu-tab tab2">
                <?php echo $this->getChildHtml('header_customer_responsive'); ?>
            </div>
        </div>
        <div class="responsive-menu-fade"></div>
    </div>
    <?php echo $this->getChildHtml('store_language') ?>
    <a href="<?php echo $this->getUrl('') ?>" class="responsive-logo"><img src="<?php echo $this->getLogoSrcSmall() ?>" alt="<?php echo $this->getLogoAlt() ?>"></a>

    <div class="responsive-search-wrapper">
        <a href="javascript:;" class="open-responsive-search">
            <svg aria-hidden="true" class="icon-svg search">
                <use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#search' ?>"></use>
            </svg>
        </a>
        <div class="search-form-wrapper">
            <?php echo $this->getChildHtml('top.search.responsive') ?>
        </div>
    </div>

    <?php echo $this->getChildHtml('responsive_header_cart'); ?>
</aside>

<a class="back-to-top" href="javascript:;">
    <svg aria-hidden="true" class="icon-svg arrow-up">
        <use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#arrow-up' ?>"></use>
    </svg>
</a>


