<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2014 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * Top menu for store
 *
 * @see Mage_Page_Block_Html_Topmenu
 */
?>
<?php $_menu = $this->getHtml('level-top') ?>

<?php if($_menu): ?>
	<ul>
    	<?php echo $_menu ?>
	</ul>
<?php endif ?>

<?php if ($cmsBlockResponsiveMenuAdditional = $this->getChildHtml('cms_block.responsive_menu_additional_links')): ?>
    <?php echo $cmsBlockResponsiveMenuAdditional; ?>
<?php endif ?>

<div class="clearH"></div>




