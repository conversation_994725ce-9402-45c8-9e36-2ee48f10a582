<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2014 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<footer>

    <div class="wide-area info-cols">
        <div class="container">
            <div class="row">
                <?php if ($cmsBlockFooterCol1 = $this->getChildHtml('cms_block.footer_col1')): ?>
                    <div class="footer-col col-xs-6 cms-col-1 col-sm-3 open-with-click-on-responsive">
                        <?php echo $cmsBlockFooterCol1; ?>
                    </div>
                <?php endif ?>
                <?php if ($cmsBlockFooterCol2 = $this->getChildHtml('cms_block.footer_col2')): ?>
                    <div class="footer-col col-xs-6 cms-col-2 col-sm-3 open-with-click-on-responsive">
                        <?php echo $cmsBlockFooterCol2; ?>
                    </div>
                <?php endif ?>
                <div class="footer-col col-xs-6 newsletter-col col-sm-3">
                    <?php if ($cmsBlockFooterCol3 = $this->getChildHtml('cms_block.footer_col3')): ?>
                        <?php echo $cmsBlockFooterCol3; ?>
                    <?php endif ?>
                    <?php echo $this->getChildHtml('newsletter') ?>
                </div>
                <div class="footer-col col-xs-6 contacts-col col-sm-3">
                    <?php if ($cmsBlockFooterCol4 = $this->getChildHtml('cms_block.footer_col4')): ?>
                        <?php echo $cmsBlockFooterCol4; ?>
                    <?php endif ?>
                    <div class="social-box">
                        <p><?php echo $this->__('Follow us here');?></p>
                        <?php if ($this->helper('stenik_sitesettings')->getFacebookurl()): ?>
                            <a href="<?php echo $this->helper('stenik_sitesettings')->getFacebookurl() ?>" class="social" title="Facebook" data-rel="blank">
                                <svg aria-hidden="true" class="icon-svg facebook">
                                    <use xlink:href="<?php echo $this->getBaseUrl().'svg/svg-sprite.svg' . '#facebook' ?>"></use>
                                </svg>
                            </a>
                        <?php endif ?>
                        <?php if ($this->helper('stenik_sitesettings')->getGoogleplusurl()): ?>
                            <a href="<?php echo $this->helper('stenik_sitesettings')->getGoogleplusurl() ?>" class="social" title="Google +" data-rel="blank">
                                <svg aria-hidden="true" class="icon-svg gplus">
                                    <use xlink:href="<?php echo $this->getBaseUrl().'svg/svg-sprite.svg' . '#gplus' ?>"></use>
                                </svg>
                            </a>
                        <?php endif ?>
                        <?php if ($this->helper('stenik_sitesettings')->getTwitterurl()): ?>
                            <a href="<?php echo $this->helper('stenik_sitesettings')->getTwitterurl() ?>" class="social" title="Twitter" data-rel="blank">
                                <svg aria-hidden="true" class="icon-svg twitter">
                                    <use xlink:href="<?php echo $this->getBaseUrl().'svg/svg-sprite.svg' . '#twitter' ?>"></use>
                                </svg>
                            </a>
                        <?php endif ?>
                        <?php if ($this->helper('stenik_sitesettings')->getInstaurl()): ?>
                            <a href="<?php echo $this->helper('stenik_sitesettings')->getInstaurl() ?>" class="social" title="Instagram" data-rel="blank">
                                <svg aria-hidden="true" class="icon-svg insta">
                                    <use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#insta' ?>"></use>
                                </svg>
                            </a>
                        <?php endif ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="wide-area payments">
        <div class="container">
            <div class="row">
                <div class="col-sm-12">
                    <div class="payments-box">
                        <div class="row">
                            <?php if ($cmsBlockFooterPaymentsText = $this->getChildHtml('cms_block.footer_payments_text')): ?>
                                <div class="col-xs-5 col-sm-6">
                                    <?php echo $cmsBlockFooterPaymentsText; ?>
                                </div>
                            <?php endif ?>
                            <?php if ($cmsBlockFooterPayments = $this->getChildHtml('cms_block.footer_payments')): ?>
                                <div class="col-xs-7 col-sm-6">
                                    <?php echo $cmsBlockFooterPayments; ?>
                                </div>
                            <?php endif ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="wide-area copy-rights">
        <div class="container">
            <div class="row">
                <div class="col-xs-8">
                    <span class="copy">
                        <?php echo $this->getCopyright() ?>
                        &nbsp; | &nbsp;
                        <a href="<?php echo $this->getUrl('catalog/seo_sitemap/category/') ?>"><?php echo $this->__('Sitemap');?></a>
                    </span>
                </div>
                <div class="col-xs-4">
                    <span class="stenik-info">
                        <svg aria-hidden="true" class="icon-svg stenik">
                            <use xlink:href="<?php echo $this->getSkinUrl('images/svg-sprite.svg') . '#stenik' ?>"></use>
                        </svg>
                        <a href="http://www.stenikgroup.com/bg/services/e-commerce" title="<?php echo $this->__('Online shop from Stenik');?>" data-rel="blank">
                            <?php echo $this->__('Online shop from');?>
                            <span class="stenik-color">Stenik</span>
                        </a>
                    </span>
                </div>
            </div>
        </div>
    </div>
</footer>