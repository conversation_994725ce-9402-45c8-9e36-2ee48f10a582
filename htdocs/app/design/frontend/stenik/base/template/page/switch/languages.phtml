<?php
/**
 * Language switcher template
 *
 * @see Mage_Page_Block_Switch
 */
?>

<?php if(count($this->getStores())>1): ?>
	<div class="drop-down language no-flags openonclick drop-down-js">
		<a class="open-item" href="javascript:;">
			<?php echo Mage::app()->getStore()->getCode() ;?>
		</a>
		<ul class="sub-options">
			<?php foreach ($this->getStores() as $_lang): ?>
				<?php if ($_lang->getCode() != Mage::app()->getStore()->getCode()): ?>
					<?php Mage::app()->getLocale()->emulate($_lang->getId()); ?>
						<li>
							<a href="<?php echo $_lang->getCurrentUrl(true) ?>">
								<?php echo $_lang->getCode() ;?>
							</a>
						</li>
					<?php Mage::app()->getLocale()->revert(); ?>
				<?php endif ?>
			<?php endforeach; ?>
		</ul>
	</div>
<?php endif; ?>