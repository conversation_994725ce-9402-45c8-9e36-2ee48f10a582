<?php
/**
 * <PERSON><PERSON><PERSON> is not affiliated with or in any way responsible for this code.
 *
 * Commercial support is available directly from the [extension author](http://www.techytalk.info/contact/).
 *
 * @category Marko-M
 * @package SocialConnect
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) <PERSON><PERSON> (http://www.techytalk.info)
 * @license http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 */
?>

<a href="<?php echo $this->escapeUrl($this->_getButtonUrl()); ?>" class="button gplus-login">
    <svg aria-hidden="true" class="icon-svg gplus"><use xlink:href="<?php echo $this->getBaseUrl().'svg/svg-sprite.svg' . '#gplus' ?>"></use></svg>
    <?php echo $this->__('Google+ registration') ?>
</a>
