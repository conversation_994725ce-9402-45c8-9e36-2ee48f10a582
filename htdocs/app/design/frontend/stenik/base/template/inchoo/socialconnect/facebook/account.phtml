<?php
/**
 * Inchoo is not affiliated with or in any way responsible for this code.
 *
 * Commercial support is available directly from the [extension author](http://www.techytalk.info/contact/).
 *
 * @category Marko-M
 * @package SocialConnect
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) <PERSON><PERSON> (http://www.techytalk.info)
 * @license http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 */
?>
<div class="inchoo-socialconnect-account">

    <h1><?php echo $this->__('Facebook Connect') ?></h1>

    <?php if($this->_hasData()): ?>

        <?php
            $facebookId = $this->_getFacebookId();
            $status = $this->_getStatus();
            $email = $this->_getEmail();
            $name = $this->_getName();
            $picture = $this->_getPicture();
            $gender = $this->_getGender();
            $birthday = $this->_getBirthday();
        ?>

        <?php if(!empty($picture)): ?>
            <p><img src="<?php echo $picture; ?>" alt="<?php echo $this->escapeHtml($name); ?>" /></p>
        <?php endif; ?>

        <?php if(!empty($status)): ?>
            <p><?php printf($this->__('Connected as %s', '<strong>'.$status.'</strong>')) ?></p>
        <?php endif; ?>
        <?php if(!empty($email)): ?>
            <p><?php printf($this->__('Email: %s', '<strong>'.$email.'</strong>')) ?></p>
        <?php endif; ?>
        <?php if(!empty($facebookId)): ?>
            <p><?php printf($this->__('Facebook #: %s', '<strong>'.$facebookId.'</strong>')) ?></p>
        <?php endif; ?>
        <?php if(!empty($gender)): ?>
            <p><?php printf($this->__('Gender: %s', '<strong>'.$gender.'</strong>')) ?></p>
        <?php endif; ?>
        <?php if(!empty($birthday)): ?>
            <p><?php printf($this->__('Birthday: %s', '<strong>'.$birthday.'</strong>')) ?></p>
        <?php endif; ?>

        <div class="clearH"></div>

        <p>
            <a href="<?php echo $this->getUrl('socialconnect/facebook/disconnect') ?>">
                <?php echo $this->__('Disconnect my profile on %s and my Facebook account.', Mage::getStoreConfig('general/store_information/name')) ?>
            </a>
        </p>

    <?php else: ?>

        <p><?php echo $this->__('You can connect store account with your Facebook account so you could login easier in the future.') ?></p>        
        <div class="social-login">
            <?php echo $this->getChildHtml('inchoo_socialconnect_account_facebook_button'); ?>
        </div>

    <?php endif; ?>
</div>
