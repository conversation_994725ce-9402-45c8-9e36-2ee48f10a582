<?php
/**
 * Template for filter items block
 *
 * @var $this Mage_Catalog_Block_Layer_Filter_Abstract
 */
$_limit = (int) Mage::getStoreConfig('bubble_layer/general/items_limit');
?>
<?php if ($this->helper('bubble_layer')->isDropdownBlock($this)): ?>
    <ul class="sub-options has-img">
        <?php
            $items = $this->getItems();
            $firstItem = reset($items);
            $attributeCode = ($firstItem->getFilter()->getData('attribute_model')) ? $firstItem->getFilter()->getAttributeModel()->getAttributeCode() : null;
        ?>
        <?php foreach ($this->getItems() as $_i => $_item): ?>
            <?php
                $_selected  = false;
                if (get_class($_item) == 'Bubble_Layer_Model_Catalog_Layer_Filter_Item')
                    $_selected = $_item->isSelected();
                $_url       = $_selected ? $_item->getRemoveUrl() : $_item->getUrl();
                $_img       = $_item->getIcon();
                $_class     = array();
                if ($_selected) $_class[] = 'active';
                if ($_limit && $_i >= $_limit && !$_selected) $_class[] = 'hideable no-display';
            ?>

            <?php
                $labelHtml = $_item->getLabel();
                $labelTitle = null;
                $optionId = Mage::helper('bubble_layer')->getOptionId($attributeCode, $_item->getValue());
                $labelTitle = $this->escapeHtml($_item->getLabel());
                $imgSrc = Mage::helper('attributeoptionimage')->getAttributeOptionThumb($optionId);
                if ($imgSrc) {
                    $labelHtml = sprintf('<img src="%s" alt="%s"/>',
                        $imgSrc,
                        $labelHtml
                    );
                }
            ?>

            <li class="<?php echo implode(' ', $_class) ?>">
                <?php if ($this->helper('bubble_layer')->isFilterMultiple($_item->getFilter())): ?>
                    <?php $_id  = $_item->getFilter()->getRequestVar() . '-' . $_item->getValue(); ?>
                    <input id="<?php echo $_id ?>"
                           type="checkbox"
                           name="<?php echo $_item->getFilter()->getRequestVar() ?>[]"
                           value="<?php echo $_url ?>"
                        <?php echo $_selected ? ' checked="checked"' : '' ?>
                           class="checkbox-filter" />
                    <?php if ($_img): ?>
                        <img src="<?php echo $this->escapeHtml($_img); ?>" alt="<?php if ($this->shouldDisplayProductCount()): ?><?php echo $_item->getCount() ?><?php endif; ?>" />
                    <?php endif; ?>
                    <label for="<?php echo $_id ?>">
                        <?php if ($_selected || !$_item->getCount()): ?>
                            <a href="<?php echo $_url ?>" class="selected" id="fitler-link-<?php echo $_id ?>"><?php echo $labelHtml ?></a>
                        <?php else: ?>
                            <a href="<?php echo $_url ?>" id="fitler-link-<?php echo $_id ?>"><?php echo $labelHtml ?></a>
                        <?php endif; ?>
                    </label>

                <?php elseif (!$_item->getCount()): ?>
                    <?php if ($_img): ?>
                        <img src="<?php echo $this->escapeHtml($_img); ?>" alt="<?php if ($this->shouldDisplayProductCount()): ?><?php echo $_item->getCount() ?><?php endif; ?>" />
                    <?php endif; ?>
                    <span><?php echo $_item->getLabel() ?></span>
                <?php else: ?>

                    <?php if ($_img): ?>
                        <img src="<?php echo $this->escapeHtml($_img); ?>" alt="<?php if ($this->shouldDisplayProductCount()): ?><?php echo $_item->getCount() ?><?php endif; ?>" />
                    <?php endif; ?>
                    <a href="<?php echo $_url ?>"><?php echo $_item->getLabel() ?></a>
                <?php endif; ?>
            </li>
        <?php endforeach ?>
    </ul>
<?php elseif ($this->helper('bubble_layer')->isLabelBlock($this)): ?>
    <div class="label-filter">
        <ul>
            <?php foreach ($this->getItems() as $_i => $_item): ?>
                <?php
                    $_selected  = $_item->isSelected();
                    $_url       = $_selected ? $_item->getRemoveUrl() : $_item->getUrl();
                ?>
                <li<?php if ($_selected): ?> class="active"<?php endif; ?>>
                    <a href="<?php echo $_url ?>" <?php if ($_selected): ?>class="active"<?php endif; ?>>
                        <?php echo $_item->getLabel() . ($this->shouldDisplayProductCount() ? ' <span class="count">' . $_item->getCount() . '</span>' : '') ?>
                    </a>
                </li>
            <?php endforeach ?>
        </ul>
    </div>
<?php else: ?>
    <ul class="sub-options">
        <?php foreach ($this->getItems() as $_i => $_item): ?>
            <?php
                $_selected  = false;
                if (get_class($_item) == 'Bubble_Layer_Model_Catalog_Layer_Filter_Item')
                    $_selected = $_item->isSelected();

                // $_selected  = $_item->isSelected();
                $_url       = $_selected ? $_item->getRemoveUrl() : $_item->getUrl();
                $_img       = $_item->getIcon();
                $_class     = array();
                if ($_selected) $_class[] = 'active';
                if ($_limit && $_i >= $_limit && !$_selected) $_class[] = 'hideable no-display';
            ?>
            <li class="<?php echo implode(' ', $_class) ?>">
                <?php if ($this->helper('bubble_layer')->isFilterMultiple($_item->getFilter())): ?>
                    <?php $_id  = $_item->getFilter()->getRequestVar() . '-' . $_item->getValue(); ?>
                    <input id="<?php echo $_id ?>"
                           type="checkbox"
                           name="<?php echo $_item->getFilter()->getRequestVar() ?>[]"
                           value="<?php echo $_url ?>"
                        <?php echo $_selected ? ' checked="checked"' : '' ?>
                           class="checkbox-filter" />
                    <?php if ($_img): ?>
                        <img src="<?php echo $this->escapeHtml($_img); ?>" alt="img" />
                    <?php endif; ?>
                    <label for="<?php echo $_id ?>">
                        <?php if ($_selected || !$_item->getCount()): ?>
                            <a href="<?php echo $_url ?>" class="selected" id="filter-link-<?php echo $_id ?>">
                                <?php echo $_item->getLabel() ?>
                                <?php if ($this->shouldDisplayProductCount()): ?>
                                    (<?php echo $_item->getCount() ?>)
                                <?php endif; ?>
                            </a>
                        <?php else: ?>
                            <a href="<?php echo $_url ?>" id="filter-link-<?php echo $_id ?>">
                                <?php echo $_item->getLabel() ?>
                                <?php if ($this->shouldDisplayProductCount()): ?>
                                    (<?php echo $_item->getCount() ?>)
                                <?php endif; ?>
                            </a>
                        <?php endif; ?>
                    </label>
                <?php elseif (!$_item->getCount()): ?>
                    <?php if ($_img): ?>
                        <img src="<?php echo $this->escapeHtml($_img); ?>" alt="img" />
                    <?php endif; ?>
                    <span><?php echo $_item->getLabel() ?></span>
                <?php else: ?>
                    <?php if ($_img): ?>
                        <img src="<?php echo $this->escapeHtml($_img); ?>" alt="img" />
                    <?php endif; ?>
                    <a href="<?php echo $_url ?>"><?php echo $_item->getLabel() ?></a>
                <?php endif; ?>
            </li>
        <?php endforeach ?>
        <?php if ($_limit && isset($_i) && $_i > $_limit): ?>
            <li class="a-right"><a href="#" class="show-hidden"><?php echo $this->__('Show More') ?></a></li>
            <li class="a-right"><a href="#" class="show-hidden no-display"><?php echo $this->__('Show Less') ?></a></li>
        <?php endif; ?>
    </ul>
<?php endif; ?>