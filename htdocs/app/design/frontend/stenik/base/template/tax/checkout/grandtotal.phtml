<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magentocommerce.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2014 Magento Inc. (http://www.magentocommerce.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/**
 * @var $this Mage_Tax_Block_Checkout_Grandtotal
 * @see Mage_Tax_Block_Checkout_Grandtotal
 */
?>
<?php if ($this->includeTax() && $this->getTotalExclTax()>=0):?>
    <tr>
        <th>
            <?php echo $this->helper('tax')->__('Grand Total Excl. Tax')?>
        </th>
        <td>
            <?php echo $this->helper('checkout')->formatPrice($this->getTotalExclTax()) ?>
        </td>
    </tr>
    <?php echo $this->renderTotals('taxes', $this->getColspan()); ?>
    <tr class="totalCartPrice">
        <th>
            <?php echo $this->helper('tax')->__('Grand Total Incl. Tax')?>
        </th>
        <td>
            <?php echo $this->helper('checkout')->formatPrice($this->getTotal()->getValue()) ?>
        </td>
    </tr>
<?php else:?>
    <tr>
        <th><?php echo $this->__('Total');?></th>
        <td><?php echo $this->helper('checkout')->formatPrice($this->getTotal()->getValue()) ?></td>
    </tr>
<?php endif;?>
