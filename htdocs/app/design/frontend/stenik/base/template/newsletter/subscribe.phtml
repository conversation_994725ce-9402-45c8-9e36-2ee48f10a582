<?php
/**
 * @package     stenik_default
 * <AUTHOR> <<EMAIL>>
 * @see         Mage_Newsletter_Block_Subscribe
 */
?>

<form method="post" id="newsletter-validate-detail" class="newsletter-form" action="<?php echo $this->getFormActionUrl(); ?>">
    <input class="input-newsletter required-entry validate-email" type="text" name="email" id="newsletter" placeholder="<?php echo $this->__('Your E-mail');?>">
    <button class="newsletter-button" type="submit" title="<?php echo $this->__('Subscribe');?>"><?php echo $this->__('ОК');?></button>
    <div id="newsletterSubscriberLoader" class="newsletterLoader"></div>
    <div id="resultOfAddSubscribers"></div>
    <?php if (Mage::helper('core')->isModuleEnabled('Stenik_GdprC<PERSON>pliance')): ?>
        <div class="newsletter-terms">
            <?php echo $this->getLayout()->createBlock('stenik_gdprcompliance/widget_newsletter')->toHtml(); ?>
        </div>
    <?php endif ?>
</form>

<script>
    var newsletterSubscriberFormDetail = new VarienForm('newsletter-validate-detail');

    Event.observe(newsletterSubscriberFormDetail.form,'submit', addCustomSubscribers.bind(newsletterSubscriberFormDetail.validator),false);
    function addCustomSubscribers(e) {
        if (!newsletterSubscriberFormDetail.validator.validate()) {
            return false;
        }
        var data = jQuery('#newsletter-validate-detail').serialize();
        jQuery('#newsletterSubscriberLoader').fadeIn('slow');
        jQuery.ajax({
            url: '<?php echo $this->getUrl("newsletter/subscriber/ajaxsubscribe"); ?>',
            type: 'POST',
            data: data,
        }).done(function(result) {
            jQuery('#resultOfAddSubscribers').hide().html(result).fadeIn('slow');
        }).always(function() {
            jQuery('#newsletterSubscriberLoader').fadeOut('slow');
            setTimeout(function() {
                jQuery('#resultOfAddSubscribers').fadeOut('slow');
            }, 4000);
        });
        Event.stop(e);
        e.preventDefault();
        return false;
    }
</script>