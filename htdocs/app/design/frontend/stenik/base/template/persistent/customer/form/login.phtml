<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2014 X.commerce, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * Customer login form template
 *
 * @see app/design/frontend/base/default/template/customer/form/login.phtml
 */
/** @var $this Mage_Customer_Block_Form_Login */
?>

<?php echo $this->getMessagesBlock()->toHtml() ?>

<h1><?php echo $this->__('Login or Create an Account') ?></h1>

<div class="clearH"></div>

<div class="col-xs-6 registered-users">
    <h4><?php echo $this->__('Login to your account') ?></h4>

    <?php echo $this->getChildHtml('inchoo_socialconnect_login')?>

    <form action="<?php echo $this->getPostActionUrl() ?>" method="post" id="login-form" class="login-form">
        
        <?php echo $this->getBlockHtml('formkey'); ?>
        
        <label for="email">* <?php echo $this->__('Email') ?></label>
        <input type="email" name="login[username]" value="<?php echo $this->escapeHtml($this->getUsername()) ?>" id="email" class="input-text required-entry validate-email" title="<?php echo $this->__('Email') ?>" />

        <label for="pass">* <?php echo $this->__('Password') ?></label>
        <input type="password" name="login[password]" class="input-text required-entry validate-password" id="pass" title="<?php echo $this->__('Password') ?>" />

        <a class="forgotpassword" href="<?php echo $this->getForgotPasswordUrl() ?>"><?php echo $this->__('Forgot Password') ?>?</a>

        <div class="clearH2"></div>

        <?php echo $this->getChildHtml('form.additional.info'); ?>
        <?php echo $this->getChildHtml('persistent.remember.me'); ?>
        <?php echo $this->getChildHtml('persistent.remember.me.tooltip'); ?>

        <button type="submit" class="button" title="<?php echo $this->__('Login') ?>" name="send" id="send2"><?php echo $this->__('Login') ?></button>

        <?php if (Mage::helper('checkout')->isContextCheckout()): ?>
            <input name="context" type="hidden" value="checkout" />
        <?php endif; ?>

    </form>
</div>

<div class="col-xs-6 new-users">
    <h4><?php echo $this->__('Registration') ?></h4>
    <?php if ($cmsBlockRegistrationInfoText = $this->getChildHtml('cms.block_registration_info_text')): ?>
        <?php echo $cmsBlockRegistrationInfoText; ?>
    <?php endif ?>
    <div class="clear"></div>
    <a href="javascript:;" class="button" onclick="window.location='<?php echo Mage::helper('persistent')->getCreateAccountUrl($this->getCreateAccountUrl()) ?>';">
        <?php echo $this->__('Registration') ?>
    </a>
</div>

<script>
    var dataForm = new VarienForm('login-form', true);
</script>