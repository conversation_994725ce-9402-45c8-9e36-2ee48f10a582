<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2014 X.commerce, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * Customer onepage checkout login form template
 *
 * @see app/design/frontend/base/default/template/checkout/onepage/login.phtml
 */
/** @var $this Mage_Checkout_Block_Onepage_Login */
?>

<div class="col2-set">
    <?php echo $this->getChildHtml('login_before')?>

    <div class="col-1 checkout-login-chooser">
        <h3>
            <?php if( $this->getQuote()->isAllowedGuestCheckout() ): ?>
                <?php echo $this->__('New Customer') ?>
            <?php else: ?>
                <?php echo $this->__('New Customer') ?>
            <?php endif; ?>
        </h3>

        <?php if( $this->getQuote()->isAllowedGuestCheckout() ): ?>
            
            <div class="buttons-wrapper">
                <a href="javascript:;" onclick="checkout.setForcedMethod('guest', event)" class="button"><?php echo $this->__('Order as guest') ?></a>
                <div class="clear"></div>
                <span class="or"><span><?php echo $this->__('or');?></span></span>
                <div class="clear"></div>
                <a href="javascript:;" onclick="checkout.setForcedMethod('register', event)" class="button"><?php echo $this->__('Create new account and order') ?></a>
            </div>            

        <?php else: ?>            
            
            <div class="buttons-wrapper">
                <?php if (!$this->helper('checkout')->isCustomerMustBeLogged()): ?>
                    <a href="javascript:;" onclick="checkout.setForcedMethod('register', event)" class="button"><?php echo $this->__('Create new account and order') ?></a>
                <?php else: ?>
                    <a href="<?php echo $this->getUrl('customer/account/create') ?>" class="button"><?php echo $this->__('Go to register page') ?></a>
                <?php endif ?>
            </div>

            <input type="hidden" name="checkout_method" id="login:register" value="register" checked="checked" />

        <?php endif; ?>

        <div class="clearH2"></div>

        <?php if ($cmsBlockCheckoutRegister = $this->getChildHtml('cms_block.checkout_register')): ?>
            <div class="text-page">
                <?php echo $cmsBlockCheckoutRegister; ?>
            </div>
        <?php endif ?>

    </div>

    <div class="col-2">
        <h3><?php echo $this->__('Existing client') ?></h3>
        <?php echo $this->getMessagesBlock()->toHtml() ?>
        <form id="login-form" action="<?php echo $this->getPostAction() ?>" method="post">
            <?php echo $this->getBlockHtml('formkey'); ?>

            <?php echo $this->getChildHtml('inchoo_socialconnect_checkout'); ?>

            <ul class="form-list">
                <li>
                    <div class="input-box">
                        <label for="login-email">* <?php echo $this->__('Email ') ?></label>
                        <input type="email" class="input-text required-entry validate-email" id="login-email" name="login[username]" value="<?php echo $this->escapeHtml($this->getUsername()) ?>" />
                    </div>
                </li>
                <li>
                    <div class="input-box">
                        <label for="login-password">* <?php echo $this->__('Password') ?></label>
                        <input type="password" class="input-text required-entry" id="login-password" name="login[password]" />
                    </div>
                </li>
                <?php echo $this->getChildHtml('form.additional.info'); ?>
                <?php echo $this->getChildHtml('persistent.remember.me'); ?>
            </ul>
            <input name="context" type="hidden" value="checkout" />
            
            <div class="clearH"></div>
            
            <button type="submit" class="button" onclick="onepageLogin(this)"><?php echo $this->__('Login') ?></button>
            <a href="<?php echo $this->getUrl('customer/account/forgotpassword') ?>" class="forgotpassword"><?php echo $this->__('Forgot your password?') ?></a>
        </form>
    </div>
</div>


<script type="text/javascript">
    var loginForm = new VarienForm('login-form', true);
    $('login-email').observe('keypress', bindLoginPost);
    $('login-password').observe('keypress', bindLoginPost);
    function bindLoginPost(evt){
        if (evt.keyCode == Event.KEY_RETURN) {
            loginForm.submit();
        }
    }
    function onepageLogin(button)
    {
        if(loginForm.validator && loginForm.validator.validate()){
            button.disabled = true;
            loginForm.submit();
        }
    }

    Checkout.addMethods({
        setForcedMethod: function(method, event) {
            if (method == 'guest') {
                this.method = 'guest';
                var request = new Ajax.Request(
                    this.saveMethodUrl,
                    {method: 'post', onFailure: this.ajaxFailure.bind(this), parameters: {method:'guest'}}
                );
                Element.hide('register-customer-password');
                this.gotoSection('billing', true);
            }
            else if(method == 'register') {
                this.method = 'register';
                var request = new Ajax.Request(
                    this.saveMethodUrl,
                    {method: 'post', onFailure: this.ajaxFailure.bind(this), parameters: {method:'register'}}
                );
                Element.show('register-customer-password');
                this.gotoSection('billing', true);
            }
            Event.stop(event);
        }
    });
</script>

<?php
    $registerParam = $this->getRequest()->getParam('register');
    if ($registerParam || $registerParam === ''):
?>
    <script type="text/javascript">
        document.observe("dom:loaded", function() {
            if($('login:register')) {
                $('login:register').checked = true;
                checkout.setMethod();
            }
        })
    </script>
<?php endif; ?>
