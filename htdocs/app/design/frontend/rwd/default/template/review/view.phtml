<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     rwd_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php if($this->getProductData()->getId()): ?>
<div class="product-review">
    <div class="page-title">
        <h1><?php echo $this->__('Review Details') ?></h1>
    </div>
    <div class="product-img-box">
        <a href="<?php echo $this->getProductData()->getProductUrl() ?>" title="<?php echo $this->escapeHtml($this->getProductData()->getName()) ?>">
            <img src="<?php echo $this->helper('catalog/image')->init($this->getProductData(), 'small_image')->resize(450, 450)->keepFrame(false); ?>"
                 alt="<?php echo $this->escapeHtml($this->getProductData()->getName()) ?>"
                 class="product-image" />
        </a>
        <?php if( $this->getRating() && $this->getRating()->getSize()): ?>
             <p class="label"><?php echo $this->__('Average Customer Rating') ?>:</p>
             <?php echo $this->getReviewsSummaryHtml($this->getProductData()) ?>
        <?php endif; ?>
    </div>
    <div class="product-details">
        <h2 class="product-name"><?php echo $this->escapeHtml($this->getProductData()->getName()) ?></h2>
        <?php if( $this->getRating() && $this->getRating()->getSize()): ?>
            <h3><?php echo $this->__('Product Rating:') ?></h3>
            <table class="ratings-table">
            <?php foreach ($this->getRating() as $_rating): ?>
                <?php if($_rating->getPercent()): ?>
                    <tr>
                        <th><?php echo $this->__($this->escapeHtml($_rating->getRatingCode())) ?></th>
                        <td>
                            <div class="rating-box">
                                <div class="rating" style="width:<?php echo ceil($_rating->getPercent()) ?>%;"></div>
                            </div>
                        </td>
                    </tr>
                <?php endif; ?>
            <?php endforeach; ?>
            </table>
            </dl>
        <?php endif; ?>
        <dl>
            <dt>
                <?php echo $this->__('Product Review (submitted on %s):', $this->dateFormat($this->getReviewData()->getCreatedAt())) ?>
            </dt>
            <dd>
                <?php echo nl2br($this->escapeHtml($this->getReviewData()->getDetail())) ?>
            </dd>
        </dl>
    </div>
    <div class="buttons-set">
        <p class="back-link"><a href="<?php echo $this->getBackUrl() ?>"><small>&laquo; </small><?php echo $this->__('Back to Product Reviews') ?></a></p>
    </div>
</div>
<?php endif; ?>
