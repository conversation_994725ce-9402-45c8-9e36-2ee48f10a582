<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

-->
<layout version="0.1.0">

<!--
Default layout, loads most of the pages
-->

    <default>

        <!-- Mage_Newsletter -->
        <reference name="left">
            <block type="newsletter/subscribe" name="left.newsletter" template="newsletter/subscribe.phtml"/>
        </reference>

    </default>

<!--
Customer account pages, rendered for all tabs in dashboard
-->

    <customer_account>
        <!-- Mage_Newsletter -->
        <reference name="customer_account_navigation">
            <action method="addLink" translate="label" module="newsletter"><name>newsletter</name><path>newsletter/manage/</path><label>Newsletter Subscriptions</label></action>
        </reference>
        <remove name="left.newsletter"/>
    </customer_account>

    <newsletter_manage_index translate="label">
        <label>Customer My Account Newsletter Subscriptions</label>
        <update handle="customer_account"/>
        <reference name="my.account.wrapper">
            <block type="customer/newsletter" name="customer_newsletter">
                <block type="page/html_wrapper" name="customer.newsletter.form.before" as="form_before" translate="label">
                    <label>Newsletter Subscription Form Before</label>
                    <action method="setMayBeInvisible"><value>1</value></action>
                </block>
            </block>
        </reference>
    </newsletter_manage_index>

</layout>
