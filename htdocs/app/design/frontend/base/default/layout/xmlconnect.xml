<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<layout version="0.1.0">
    <default>
    </default>

    <!-- Home -->
    <xmlconnect_index_index>
        <block type="xmlconnect/home" name="xmlconnect.home" output="toHtml"/>
    </xmlconnect_index_index>

    <!-- Localization -->
    <xmlconnect_localization_index>
        <block type="xmlconnect/localization" name="xmlconnect.localization" output="toHtml"/>
    </xmlconnect_localization_index>

    <!-- Home banners -->
    <xmlconnect_homebanners_index>
        <block type="xmlconnect/homebanners" name="xmlconnect.homebanners" output="toHtml"/>
    </xmlconnect_homebanners_index>

    <!-- Catalog -->

    <xmlconnect_catalog_filters>
        <block type="xmlconnect/catalog_filters" name="xmlconnect.catalog.filters" output="toHtml"/>
    </xmlconnect_catalog_filters>

    <!-- Category -->
    <xmlconnect_catalog_category>
        <block type="xmlconnect/catalog_category" name="xmlconnect.catalog.category" output="toHtml">
            <block type="xmlconnect/catalog_product_list" name="xmlconnect.catalog.product.list" as="product_list" output="">
                <block type="xmlconnect/catalog_product_price" name="xmlconnect.catalog.product.price" as="product_price" output="">
                    <action method="addRenderer"><type>bundle</type><renderer>xmlconnect/catalog_product_price_bundle</renderer></action>
                    <action method="addRenderer"><type>giftcard</type><renderer>xmlconnect/catalog_product_price_giftcard</renderer></action>
                </block>
            </block>

            <block type="xmlconnect/catalog_category_info" name="xmlconnect.catalog.category.info" as="category_info" output=""/>
        </block>
    </xmlconnect_catalog_category>

    <xmlconnect_catalog_categorydetails>
        <block type="xmlconnect/catalog_category" name="xmlconnect.catalog.category" output="toHtml">
            <block type="xmlconnect/catalog_product_list" name="xmlconnect.catalog.product.list" as="product_list" output="">
                <block type="xmlconnect/catalog_product_itemPrice" name="xmlconnect.catalog.product.itemprice" as="product_price" output="">
                    <action method="addRenderer"><type>bundle</type><renderer>xmlconnect/catalog_product_itemPrice_bundle</renderer></action>
                    <action method="addRenderer"><type>giftcard</type><renderer>xmlconnect/catalog_product_itemPrice_giftcard</renderer></action>
                </block>
            </block>

            <block type="xmlconnect/catalog_category_info" name="xmlconnect.catalog.category.info" as="category_info" output=""/>
        </block>
    </xmlconnect_catalog_categorydetails>

    <!-- Product -->

    <xmlconnect_catalog_product>
        <block type="xmlconnect/catalog_product" name="xmlconnect.catalog.product" output="toHtml">
            <block type="xmlconnect/catalog_product_related" name="xmlconnect.product.related" as="related_products" output=""/>

            <block type="xmlconnect/catalog_product_price" name="xmlconnect.catalog.product.price" as="product_price" output="">
                <action method="addRenderer"><type>bundle</type><renderer>xmlconnect/catalog_product_price_bundle</renderer></action>
                <action method="addRenderer"><type>giftcard</type><renderer>xmlconnect/catalog_product_price_giftcard</renderer></action>
            </block>

            <block type="xmlconnect/catalog_product_attributes" name="xmlconnect.catalog.product.attributes" as="additional_info" output="" />

            <block type="xmlconnect/catalog_product_options" name="xmlconnect.catalog.product.options" output="">
                <action method="addRenderer"><type>simple</type><renderer>xmlconnect/catalog_product_options_simple</renderer></action>
                <action method="addRenderer"><type>virtual</type><renderer>xmlconnect/catalog_product_options_virtual</renderer></action>
                <action method="addRenderer"><type>configurable</type><renderer>xmlconnect/catalog_product_options_configurable</renderer></action>
                <action method="addRenderer"><type>bundle</type><renderer>xmlconnect/catalog_product_options_bundle</renderer></action>
                <action method="addRenderer"><type>grouped</type><renderer>xmlconnect/catalog_product_options_grouped</renderer></action>
                <action method="addRenderer"><type>giftcard</type><renderer>xmlconnect/catalog_product_options_giftcard</renderer></action>
                <action method="addRenderer"><type>downloadable</type><renderer>xmlconnect/catalog_product_options_downloadable</renderer></action>
            </block>
        </block>
    </xmlconnect_catalog_product>

    <xmlconnect_catalog_productview>
        <block type="xmlconnect/catalog_product" name="xmlconnect.catalog.product" output="toHtml">
            <block type="xmlconnect/catalog_product_related" name="xmlconnect.product.related" as="related_products" output=""/>

            <block type="xmlconnect/catalog_product_itemPrice" name="xmlconnect.catalog.product.price" as="product_price" output="">
                <action method="addRenderer"><type>bundle</type><renderer>xmlconnect/catalog_product_itemPrice_bundle</renderer></action>
                <action method="addRenderer"><type>giftcard</type><renderer>xmlconnect/catalog_product_itemPrice_giftcard</renderer></action>
            </block>

            <block type="xmlconnect/catalog_product_attributes" name="xmlconnect.catalog.product.attributes" as="additional_info" output="" />

            <block type="xmlconnect/catalog_product_options" name="xmlconnect.catalog.product.options" output="">
                <action method="addRenderer"><type>simple</type><renderer>xmlconnect/catalog_product_options_simple</renderer></action>
                <action method="addRenderer"><type>virtual</type><renderer>xmlconnect/catalog_product_options_virtual</renderer></action>
                <action method="addRenderer"><type>configurable</type><renderer>xmlconnect/catalog_product_options_configurable</renderer></action>
                <action method="addRenderer"><type>bundle</type><renderer>xmlconnect/catalog_product_options_bundle</renderer></action>
                <action method="addRenderer"><type>grouped</type><renderer>xmlconnect/catalog_product_options_grouped</renderer></action>
                <action method="addRenderer"><type>giftcard</type><renderer>xmlconnect/catalog_product_options_giftcard</renderer></action>
                <action method="addRenderer"><type>downloadable</type><renderer>xmlconnect/catalog_product_options_downloadable</renderer></action>
            </block>
        </block>
    </xmlconnect_catalog_productview>

    <xmlconnect_catalog_productoptions>
        <block type="xmlconnect/catalog_product_options" name="xmlconnect.catalog.product.options" output="toHtml">
            <action method="addRenderer"><type>simple</type><renderer>xmlconnect/catalog_product_options_simple</renderer></action>
            <action method="addRenderer"><type>virtual</type><renderer>xmlconnect/catalog_product_options_virtual</renderer></action>
            <action method="addRenderer"><type>configurable</type><renderer>xmlconnect/catalog_product_options_configurable</renderer></action>
            <action method="addRenderer"><type>bundle</type><renderer>xmlconnect/catalog_product_options_bundle</renderer></action>
            <action method="addRenderer"><type>grouped</type><renderer>xmlconnect/catalog_product_options_grouped</renderer></action>
            <action method="addRenderer"><type>giftcard</type><renderer>xmlconnect/catalog_product_options_giftcard</renderer></action>
            <action method="addRenderer"><type>downloadable</type><renderer>xmlconnect/catalog_product_options_downloadable</renderer></action>
        </block>
    </xmlconnect_catalog_productoptions>

    <xmlconnect_catalog_productgallery>
        <block type="xmlconnect/catalog_product_gallery" name="xmlconnect.catalog.product.gallery" output="toHtml"/>
    </xmlconnect_catalog_productgallery>

    <xmlconnect_catalog_productreview>
        <block type="xmlconnect/catalog_product_review" name="xmlconnect.catalog.product.review" output="toHtml"/>
    </xmlconnect_catalog_productreview>

    <xmlconnect_catalog_productreviews>
        <block type="xmlconnect/catalog_product_review_list" name="xmlconnect.catalog.product.reviews" output="toHtml"/>
    </xmlconnect_catalog_productreviews>

    <!-- Search -->

    <xmlconnect_catalog_search>
        <block type="xmlconnect/catalog_search" name="xmlconnect.catalog.search" output="toHtml">
            <block type="xmlconnect/catalog_product_list" name="xmlconnect.catalog.product.list" as="product_list" output="">
                <block type="xmlconnect/catalog_product_price" name="xmlconnect.catalog.product.price" as="product_price" output="">
                    <action method="addRenderer"><type>bundle</type><renderer>xmlconnect/catalog_product_price_bundle</renderer></action>
                    <action method="addRenderer"><type>giftcard</type><renderer>xmlconnect/catalog_product_price_giftcard</renderer></action>
                </block>
            </block>
        </block>
    </xmlconnect_catalog_search>

    <xmlconnect_catalog_searchdetails>
        <block type="xmlconnect/catalog_search" name="xmlconnect.catalog.search" output="toHtml">
            <block type="xmlconnect/catalog_product_list" name="xmlconnect.catalog.product.list" as="product_list" output="">
                <block type="xmlconnect/catalog_product_itemPrice" name="xmlconnect.catalog.product.price" as="product_price" output="">
                    <action method="addRenderer"><type>bundle</type><renderer>xmlconnect/catalog_product_itemPrice_bundle</renderer></action>
                    <action method="addRenderer"><type>giftcard</type><renderer>xmlconnect/catalog_product_itemPrice_giftcard</renderer></action>
                </block>
            </block>
        </block>
    </xmlconnect_catalog_searchdetails>

     <xmlconnect_catalog_searchsuggest>
        <block type="xmlconnect/catalog_search_suggest" name="xmlconnect.catalog.search.suggest" output="toHtml"/>
    </xmlconnect_catalog_searchsuggest>

    <!-- Shopping Cart -->
    <xmlconnect_cart_index>
        <block type="xmlconnect/cart" name="xmlconnect.cart" output="toHtml">
            <action method="addItemRender"><type>simple</type><block>xmlconnect/checkout_cart_item_renderer</block><template></template></action>
            <action method="addItemRender"><type>virtual</type><block>xmlconnect/checkout_cart_item_renderer</block><template></template></action>
            <action method="addItemRender"><type>configurable</type><block>xmlconnect/checkout_cart_item_renderer_configurable</block><template></template></action>
            <action method="addItemRender"><type>bundle</type><block>xmlconnect/checkout_cart_item_renderer_bundle</block><template></template></action>
            <action method="addItemRender"><type>grouped</type><block>xmlconnect/checkout_cart_item_renderer_grouped</block><template></template></action>
            <action method="addItemRender"><type>giftcard</type><block>xmlconnect/checkout_cart_item_renderer_giftcard</block><template></template></action>
            <action method="addItemRender"><type>downloadable</type><block>xmlconnect/checkout_cart_item_renderer_downloadable</block><template></template></action>

            <block type="xmlconnect/cart_crosssell" name="xmlconnect.cart.crosssell" as="crosssell">
                <block type="xmlconnect/catalog_product_price" name="xmlconnect.catalog.product.price" as="product_price" output="">
                    <action method="addRenderer"><type>bundle</type><renderer>xmlconnect/catalog_product_price_bundle</renderer></action>
                    <action method="addRenderer"><type>giftcard</type><renderer>giftcard/catalog_product_price_giftcard</renderer></action>
                </block>
            </block>
        </block>
    </xmlconnect_cart_index>

    <xmlconnect_cart_info>
        <block type="xmlconnect/cart_info" name="xmlconnect.cart.info" as="cart_info" output="toHtml">
            <block type="xmlconnect/cart_totals" name="xmlconnect.cart.totals" as="totals"/>
        </block>
    </xmlconnect_cart_info>

    <xmlconnect_cart_shoppingcart>
        <block type="xmlconnect/shoppingCart" name="xmlconnect.cart" output="toHtml">
            <block type="xmlconnect/cart_items" name="xmlconnect.cart.items" as="items">
                <action method="addItemRender"><type>simple</type><block>xmlconnect/checkout_cart_item_renderer</block><template></template></action>
                <action method="addItemRender"><type>virtual</type><block>xmlconnect/checkout_cart_item_renderer</block><template></template></action>
                <action method="addItemRender"><type>configurable</type><block>xmlconnect/checkout_cart_item_renderer_configurable</block><template></template></action>
                <action method="addItemRender"><type>bundle</type><block>xmlconnect/checkout_cart_item_renderer_bundle</block><template></template></action>
                <action method="addItemRender"><type>grouped</type><block>xmlconnect/checkout_cart_item_renderer_grouped</block><template></template></action>
                <action method="addItemRender"><type>giftcard</type><block>xmlconnect/checkout_cart_item_renderer_giftcard</block><template></template></action>
                <action method="addItemRender"><type>downloadable</type><block>xmlconnect/checkout_cart_item_renderer_downloadable</block><template></template></action>
            </block>
            <block type="xmlconnect/cart_crosssell" name="xmlconnect.cart.crosssell" as="crosssell">
                <block type="xmlconnect/catalog_product_itemPrice" name="xmlconnect.catalog.product.price" as="product_price" output="">
                    <action method="addRenderer"><type>bundle</type><renderer>xmlconnect/catalog_product_itemPrice_bundle</renderer></action>
                    <action method="addRenderer"><type>giftcard</type><renderer>xmlconnect/catalog_product_itemPrice_giftcard</renderer></action>
                </block>
            </block>
            <block type="xmlconnect/cart_cartTotals" name="xmlconnect.cart.totals" as="totals"/>
        </block>
    </xmlconnect_cart_shoppingcart>

    <xmlconnect_cart_configure translate="label">
        <label>Configure Cart Item</label>
        <update handle="xmlconnect_catalog_product"/>
        <reference name="product.info">
            <block type="xmlconnect/cart_item_configure" name="checkout.cart.item.configure.block"/>
        </reference>
    </xmlconnect_cart_configure>

    <!-- Customer -->

    <xmlconnect_customer_form>
        <block type="xmlconnect/customer_form" name="xmlconnect.customer.form" output="toHtml"/>
    </xmlconnect_customer_form>

    <xmlconnect_customer_checkoutregistration>
        <block type="xmlconnect/customer_form" name="xmlconnect.customer.checkout.registration" output="toHtml"/>
    </xmlconnect_customer_checkoutregistration>

    <xmlconnect_wishlist_index>
        <block type="xmlconnect/wishlist" name="xmlconnect.wishlist" output="toHtml">
            <block type="xmlconnect/catalog_product_price" name="xmlconnect.catalog.product.price" as="product_price" output="">
                <action method="addRenderer"><type>bundle</type><renderer>xmlconnect/catalog_product_price_bundle</renderer></action>
                <action method="addRenderer"><type>giftcard</type><renderer>xmlconnect/catalog_product_price_giftcard</renderer></action>
            </block>
        </block>
    </xmlconnect_wishlist_index>

    <xmlconnect_wishlist_details>
        <block type="xmlconnect/wishlist" name="xmlconnect.wishlist" output="toHtml">
                <block type="xmlconnect/catalog_product_itemPrice" name="xmlconnect.catalog.product.price" as="product_price" output="">
                    <action method="addRenderer"><type>bundle</type><renderer>xmlconnect/catalog_product_itemPrice_bundle</renderer></action>
                    <action method="addRenderer"><type>giftcard</type><renderer>xmlconnect/catalog_product_itemPrice_giftcard</renderer></action>
                </block>
        </block>
    </xmlconnect_wishlist_details>

    <xmlconnect_customer_address>
        <block type="xmlconnect/customer_address_list" name="xmlconnect.customer.address.list" output="toHtml"/>
    </xmlconnect_customer_address>

    <xmlconnect_customer_addressform>
        <block type="xmlconnect/customer_address_form" name="xmlconnect.customer.address.form" output="toHtml"/>
    </xmlconnect_customer_addressform>

    <xmlconnect_customer_orderlist>
        <block type="xmlconnect/customer_order_list" name="xmlconnect.customer.order.list" output="toHtml"/>
    </xmlconnect_customer_orderlist>

    <xmlconnect_customer_orderdetails>
        <block type="xmlconnect/customer_order_details" name="order.details" output="toHtml">
            <block type="xmlconnect/customer_order_items" name="order.items" output="">
                <action method="addItemRender"><type>default</type><block>xmlconnect/customer_order_item_renderer_default</block><template></template></action>
                <action method="addItemRender"><type>grouped</type><block>xmlconnect/customer_order_item_renderer_grouped</block><template></template></action>
                <action method="addItemRender"><type>bundle</type><block>xmlconnect/customer_order_item_renderer_bundle</block><template></template></action>
                <action method="addItemRender"><type>giftcard</type><block>xmlconnect/customer_order_item_renderer_giftcard</block><template></template></action>
                <action method="addItemRender"><type>downloadable</type><block>xmlconnect/customer_order_item_renderer_downloadable</block><template></template></action>
                <block type="xmlconnect/customer_order_totals" name="order.totals" output="">
                    <block type="xmlconnect/customer_order_totals_tax" name="xmlconnect.customer.order.tax" output=""/>
                </block>
            </block>
        </block>
    </xmlconnect_customer_orderdetails>

    <xmlconnect_customer_storecredit>
        <block type="xmlconnect/customer_storecredit" name="xmlconnect.customer.storecredit" output="toHtml"/>
    </xmlconnect_customer_storecredit>

    <xmlconnect_customer_giftcardcheck>
        <block type="xmlconnect/customer_giftcardCheck" name="xmlconnect.customer.giftcardCheck" output="toHtml"/>
    </xmlconnect_customer_giftcardcheck>

    <xmlconnect_customer_downloads>
        <block type="xmlconnect/customer_downloads" name="xmlconnect.customer.downloads" output="toHtml"/>
    </xmlconnect_customer_downloads>

    <!-- Configuration -->

    <xmlconnect_configuration_index>
        <block type="xmlconnect/configuration" name="xmlconnect.configuration" output="toHtml"/>
    </xmlconnect_configuration_index>

    <!-- Checkout (OnePage) -->

    <xmlconnect_checkout_addressmassaction>
        <block type="xmlconnect/checkout_onepage_address" name="xmlconnect.checkout.onepage.address" output="toHtml">
            <block type="xmlconnect/checkout_onepage_address_list" name="xmlconnect.checkout.onepage.address.list" as="address_list" output=""/>
            <block type="xmlconnect/checkout_onepage_address_form" name="xmlconnect.checkout.onepage.address.form" as="address_form" output=""/>
        </block>
    </xmlconnect_checkout_addressmassaction>

    <xmlconnect_checkout_index>
        <block type="xmlconnect/checkout_address_billing" name="xmlconnect.checkout.address.billing" output="toHtml">
            <block type="xmlconnect/customer_address_list" name="xmlconnect.customer.address.list" as="address_list" output=""/>
        </block>
    </xmlconnect_checkout_index>

    <xmlconnect_checkout_billingaddress>
        <block type="xmlconnect/checkout_address_billing" name="xmlconnect.checkout.address.billing" output="toHtml">
            <block type="xmlconnect/customer_address_list" name="xmlconnect.customer.address.list" as="address_list" output=""/>
        </block>
    </xmlconnect_checkout_billingaddress>

    <xmlconnect_checkout_newbillingaddressform>
        <block type="xmlconnect/checkout_address_form" name="xmlconnect.checkout.new.billing.address.form" output="toHtml">
            <action method="setType"><type>billing</type></action>
        </block>
    </xmlconnect_checkout_newbillingaddressform>

    <xmlconnect_checkout_shippingaddress>
        <block type="xmlconnect/checkout_address_shipping" name="xmlconnect.checkout.address.shipping" output="toHtml">
            <block type="xmlconnect/customer_address_list" name="xmlconnect.customer.address.list" as="address_list" output=""/>
        </block>
    </xmlconnect_checkout_shippingaddress>

    <xmlconnect_checkout_newshippingaddressform>
        <block type="xmlconnect/checkout_address_form" name="xmlconnect.checkout.new.billing.address.form" output="toHtml">
            <action method="setType"><type>shipping</type></action>
        </block>
    </xmlconnect_checkout_newshippingaddressform>

    <xmlconnect_checkout_shippingmethods>
        <block type="xmlconnect/checkout_shipping_method_available" name="xmlconnect.checkout.shipping.method.available" output="toHtml"/>
    </xmlconnect_checkout_shippingmethods>

    <xmlconnect_checkout_shippingmethodslist>
        <block type="xmlconnect/checkout_shipping_method_availableList" name="xmlconnect.checkout.shipping.method.available" output="toHtml"/>
    </xmlconnect_checkout_shippingmethodslist>

    <xmlconnect_checkout_paymentmethods>
        <block type="xmlconnect/checkout_payment_method_list" name="payment.methods" output="">
            <block type="xmlconnect/checkout_payment_method_ccsave" name="xmlconnect.checkout.method.ccsave" as="payment_ccsave" output=""/>
            <block type="xmlconnect/checkout_payment_method_checkmo" name="xmlconnect.checkout.method.checkmo" as="payment_checkmo" output=""/>
            <block type="xmlconnect/checkout_payment_method_purchaseorder" name="xmlconnect.checkout.method.purchaseorder" as="payment_purchaseorder" output=""/>
            <block type="xmlconnect/checkout_payment_method_paypal_payflow" name="xmlconnect.checkout.method.paypal.payflow" as="payment_paypal_payflow" output=""/>
            <block type="xmlconnect/checkout_payment_method_paypal_direct" name="xmlconnect.checkout.method.paypal.direct" as="payment_paypal_direct" output=""/>
            <block type="xmlconnect/checkout_payment_method_authorizenet" name="xmlconnect.checkout.method.authorizenet" as="payment_authorizenet" output=""/>
        </block>
    </xmlconnect_checkout_paymentmethods>

    <xmlconnect_checkout_paymentmethodlist>
        <block type="xmlconnect/checkout_payment_method_listApi23" name="payment.methods" output="">
            <block type="xmlconnect/checkout_payment_method_ccsave" name="xmlconnect.checkout.method.ccsave" as="payment_ccsave" output=""/>
            <block type="xmlconnect/checkout_payment_method_checkmo" name="xmlconnect.checkout.method.checkmo" as="payment_checkmo" output=""/>
            <block type="xmlconnect/checkout_payment_method_purchaseorder" name="xmlconnect.checkout.method.purchaseorder" as="payment_purchaseorder" output=""/>
            <block type="xmlconnect/checkout_payment_method_paypal_payflow" name="xmlconnect.checkout.method.paypal.payflow" as="payment_paypal_payflow" output=""/>
            <block type="xmlconnect/checkout_payment_method_paypal_direct" name="xmlconnect.checkout.method.paypal.direct" as="payment_paypal_direct" output=""/>
            <block type="xmlconnect/checkout_payment_method_authorizenet" name="xmlconnect.checkout.method.authorizenet" as="payment_authorizenet" output=""/>
        </block>
    </xmlconnect_checkout_paymentmethodlist>

    <xmlconnect_checkout_orderreview>
        <block type="xmlconnect/checkout_order_review" name="xmlconnect.checkout.order.review" output="toHtml">
            <block type="xmlconnect/checkout_order_review_info" name="xmlconnect.checkout.order.info" as="order_products">
                <action method="addItemRender"><type>default</type><block>xmlconnect/checkout_cart_item_renderer</block><template></template></action>
                <action method="addItemRender"><type>configurable</type><block>xmlconnect/checkout_cart_item_renderer_configurable</block><template></template></action>
                <action method="addItemRender"><type>bundle</type><block>xmlconnect/checkout_cart_item_renderer_bundle</block><template></template></action>
                <action method="addItemRender"><type>grouped</type><block>xmlconnect/checkout_cart_item_renderer_grouped</block><template></template></action>
                <action method="addItemRender"><type>giftcard</type><block>xmlconnect/checkout_cart_item_renderer_giftcard</block><template></template></action>
                <action method="addItemRender"><type>downloadable</type><block>xmlconnect/checkout_cart_item_renderer_downloadable</block><template></template></action>
            </block>

            <block type="xmlconnect/cart_totals" name="xmlconnect.checkout.order.review.totals" as="totals"/>
            <block type="xmlconnect/checkout_agreements" name="xmlconnect.checkout.agreements" as="agreements" output=""/>
        </block>
    </xmlconnect_checkout_orderreview>

    <xmlconnect_checkout_ordersummary>
        <block type="xmlconnect/checkout_onepage_review" name="xmlconnect.order.details" output="toHtml">
            <block type="xmlconnect/cart_items" name="xmlconnect.cart.items" as="items">
                <action method="addItemRender"><type>simple</type><block>xmlconnect/checkout_cart_item_renderer</block><template></template></action>
                <action method="addItemRender"><type>virtual</type><block>xmlconnect/checkout_cart_item_renderer</block><template></template></action>
                <action method="addItemRender"><type>configurable</type><block>xmlconnect/checkout_cart_item_renderer_configurable</block><template></template></action>
                <action method="addItemRender"><type>bundle</type><block>xmlconnect/checkout_cart_item_renderer_bundle</block><template></template></action>
                <action method="addItemRender"><type>grouped</type><block>xmlconnect/checkout_cart_item_renderer_grouped</block><template></template></action>
                <action method="addItemRender"><type>giftcard</type><block>xmlconnect/checkout_cart_item_renderer_giftcard</block><template></template></action>
                <action method="addItemRender"><type>downloadable</type><block>xmlconnect/checkout_cart_item_renderer_downloadable</block><template></template></action>
            </block>
            <block type="xmlconnect/cart_cartTotals" name="xmlconnect.cart.totals" as="totals"/>
            <block type="xmlconnect/checkout_agreements" name="xmlconnect.checkout.agreements" as="agreements" output=""/>
        </block>
    </xmlconnect_checkout_ordersummary>

    <!-- Checkout with PayPal MEP -->

    <xmlconnect_paypal_mep_shippingmethods>
        <block type="xmlconnect/checkout_shipping_method_available" name="xmlconnect.checkout.shipping.method.available" output="toHtml"/>
    </xmlconnect_paypal_mep_shippingmethods>

    <xmlconnect_paypal_mep_carttotals>
        <block type="xmlconnect/cart_paypal_mep_totals" name="xmlconnect.cart.paypal.mep.totals" output="toHtml"/>
    </xmlconnect_paypal_mep_carttotals>

    <!-- Checkout with PayPal MECL -->

    <xmlconnect_paypal_mecl_review>
        <block type="xmlconnect/cart_paypal_mecl_review" name="xmlconnect.cart.paypal.mecl.review" output="toHtml">
            <block type="xmlconnect/cart_paypal_mecl_details" name="xmlconnect.cart.paypal.mecl.details" as="details">
                <action method="addItemRender"><type>default</type><block>xmlconnect/cart_item_renderer</block><template></template></action>
                <action method="addItemRender"><type>grouped</type><block>xmlconnect/cart_item_renderer_grouped</block><template></template></action>
                <action method="addItemRender"><type>configurable</type><block>xmlconnect/cart_item_renderer_configurable</block><template></template></action>
                <block type="xmlconnect/cart_totals" name="xmlconnect.cart.totals" as="totals"/>
            </block>
            <block type="xmlconnect/checkout_agreements" name="xmlconnect.checkout.agreements" as="agreements" output=""/>
        </block>
    </xmlconnect_paypal_mecl_review>

    <xmlconnect_paypal_mecl_orderreview>
        <block type="xmlconnect/cart_paypal_mecl_review" name="xmlconnect.cart.paypal.mecl.review" output="toHtml">
            <block type="xmlconnect/cart_paypal_mecl_orderDetails" name="xmlconnect.cart.paypal.mecl.details" as="details">
                <action method="addItemRender"><type>default</type><block>xmlconnect/cart_item_renderer</block><template></template></action>
                <action method="addItemRender"><type>grouped</type><block>xmlconnect/cart_item_renderer_grouped</block><template></template></action>
                <action method="addItemRender"><type>configurable</type><block>xmlconnect/cart_item_renderer_configurable</block><template></template></action>
                <block type="xmlconnect/cart_cartTotals" name="xmlconnect.cart.totals" as="totals"/>
            </block>
            <block type="xmlconnect/checkout_agreements" name="xmlconnect.checkout.agreements" as="agreements" output=""/>
        </block>
    </xmlconnect_paypal_mecl_orderreview>

    <xmlconnect_paypal_mecl_shippingmethods>
        <block type="xmlconnect/cart_paypal_mecl_shippingmethods" name="xmlconnect.cart.paypal.mecl.shippingmethods" output="toHtml"></block>
    </xmlconnect_paypal_mecl_shippingmethods>

    <!-- Cms -->

    <xmlconnect_cms_page>
        <block type="xmlconnect/cms_page" name="xmlconnect.cms.page" output="toHtml"/>
    </xmlconnect_cms_page>

    <!-- Payment bridge -->

    <xmlconnect_pbridge_result>
        <block type="xmlconnect/checkout_pbridge_result" name="xmlconnect.checkout.pbridge.result" template="xmlconnect/pbridge/result.phtml" output="toHtml" />
    </xmlconnect_pbridge_result>

    <!-- Product review -->

    <xmlconnect_review_form>
        <block type="xmlconnect/review_form" name="xmlconnect.review.form" output="toHtml"/>
    </xmlconnect_review_form>
</layout>
