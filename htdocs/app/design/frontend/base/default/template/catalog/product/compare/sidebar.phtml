<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/* @var $this Mage_Catalog_Block_Product_Compare_Sidebar */
?>
<?php
$_helper = $this->helper('catalog/product_compare');
$_items = $_helper->getItemCount() > 0 ? $_helper->getItemCollection() : null;
?>
<?php if ($_items): ?>
<div class="block block-list block-compare">
    <div class="block-title">
        <strong><span><?php echo $this->__('Compare Products') ?>
            <?php if($_helper->getItemCount() > 0): ?>
                <small><?php echo $this->__('(%d)', $_helper->getItemCount()) ?></small>
            <?php endif; ?>
        </span></strong>
    </div>
    <div class="block-content">
    <?php if($_helper->getItemCount() > 0): ?>
        <ol id="compare-items">
        <?php foreach($_items as $_index => $_item): ?>
            <li class="item">
                <input type="hidden" class="compare-item-id" value="<?php echo $_item->getId() ?>" />
                <a href="<?php echo $_helper->getRemoveUrl($_item) ?>" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Remove This Item')) ?>" class="btn-remove" onclick="return confirm('<?php echo Mage::helper('core')->quoteEscape($this->__('Are you sure you would like to remove this item from the compare products?')) ?>');"><?php echo $this->__('Remove This Item') ?></a>
                <p class="product-name"><a href="<?php echo $this->getProductUrl($_item) ?>"><?php echo $this->helper('catalog/output')->productAttribute($_item, $_item->getName(), 'name') ?></a></p>
            </li>
        <?php endforeach; ?>
        </ol>
        <script type="text/javascript">decorateList('compare-items')</script>
        <div class="actions">
            <a href="<?php echo $_helper->getClearListUrl() ?>" onclick="return confirm('<?php echo Mage::helper('core')->quoteEscape($this->__('Are you sure you would like to remove all products from your comparison?')) ?>');"><?php echo $this->__('Clear All') ?></a>
            <button type="button" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Compare')) ?>" class="button" onclick="popWin('<?php echo $_helper->getListUrl() ?>','compare','top:0,left:0,width=820,height=600,resizable=yes,scrollbars=yes')"><span><span><?php echo $this->__('Compare') ?></span></span></button>
        </div>
    <?php else: ?>
        <p class="empty"><?php echo $this->__('You have no items to compare.') ?></p>
    <?php endif; ?>
    </div>
</div>
<?php endif; ?>
