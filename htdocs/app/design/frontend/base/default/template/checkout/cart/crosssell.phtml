<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * Cart cross sell items template
 *
 * @see Mage_Checkout_Block_Cart_Crosssell
 */
?>
<?php if($this->getItemCount()): ?>
<?php $_params = $this->escapeHtml(json_encode(array('form_key' => $this->getFormKey()))) ?>
<div class="crosssell">
    <h2><?php echo $this->__('Based on your selection, you may be interested in the following items:') ?></h2>
    <ul id="crosssell-products-list">
    <?php foreach ($this->getItems() as $_item): ?>
        <li class="item">
            <a class="product-image" href="<?php echo $_item->getProductUrl() ?>" title="<?php echo $this->escapeHtml($_item->getName()) ?>"><img src="<?php echo $this->helper('catalog/image')->init($_item, 'thumbnail')->resize(75); ?>" width="75" height="75" alt="<?php echo $this->escapeHtml($_item->getName()) ?>" /></a>
            <div class="product-details">
                <h3 class="product-name"><a href="<?php echo $_item->getProductUrl() ?>"><?php echo $this->escapeHtml($_item->getName()) ?></a></h3>
                <?php echo $this->getPriceHtml($_item, true) ?>
                <button type="button"
                        title="<?php echo Mage::helper('core')->quoteEscape($this->__('Add to Cart')) ?>"
                        class="button btn-cart"
                        onclick="customFormSubmit(
                                '<?php echo $this->getAddToCartUrlCustom($_item, array(), false) ?>',
                                '<?php echo $_params ?>',
                                'post')">
                    <span><span><?php echo $this->__('Add to Cart') ?></span></span>
                </button>
                <ul class="add-to-links">
                    <?php if ($this->helper('wishlist')->isAllow()) : ?>
                        <li>
                            <a href="#"
                               class="link-wishlist"
                               onclick="customFormSubmit(
                                       '<?php echo $this->getAddToWishlistUrlCustom($_item, false) ?>',
                                       '<?php echo $_params ?>',
                                       'post')">
                                <?php echo $this->__('Add to Wishlist') ?>
                            </a>
                        </li>
                    <?php endif; ?>
                    <?php if ($_compareUrl = $this->getAddToCompareUrlCustom($_item, false)) : ?>
                        <li>
                            <span class="separator">|</span>
                            <a href="#"
                               class="link-compare"
                               onclick="customFormSubmit(
                                       '<?php echo $_compareUrl ?>',
                                       '<?php echo $_params ?>',
                                       'post')">
                                <?php echo $this->__('Add to Compare') ?>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </li>
    <?php endforeach; ?>
    </ul>
    <script type="text/javascript">decorateList('crosssell-products-list', 'none-recursive')</script>
</div>
<?php endif; ?>
