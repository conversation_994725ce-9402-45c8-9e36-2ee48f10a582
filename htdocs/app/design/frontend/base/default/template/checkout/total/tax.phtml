<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php global $taxIter; $taxIter++; ?>
<?php if ($this->helper('tax')->displayFullSummary() && $this->getTotal()->getValue()!=0): ?>
<?php $isTop = 1; ?>
            <?php foreach ($this->getTotal()->getFullInfo() as $info): ?>
                <?php if (isset($info['hidden']) && $info['hidden']) continue; ?>
                <?php $percent = $info['percent']; ?>
                <?php $amount = $info['amount']; ?>
                <?php $rates = $info['rates']; ?>
                <?php $isFirst = 1; ?>

                <?php foreach ($rates as $rate): ?>
                <tr class="summary-details-<?php echo $taxIter; ?> summary-details<?php if ($isTop): echo ' summary-details-first'; endif; ?>" style="display:none;">
                    <td class="a-right" style="<?php echo $this->getTotal()->getStyle() ?>" colspan="<?php echo $this->getColspan(); ?>">
                        <?php echo $this->escapeHtml($rate['title']); ?>
                        <?php if (!is_null($rate['percent'])): ?>
                            (<?php echo (float)$rate['percent']; ?>%)
                        <?php endif; ?>
                        <br />
                    </td>
                    <?php if ($isFirst): ?>
                        <td rowspan="<?php echo count($rates); ?>" class="a-right" style="<?php echo $this->getTotal()->getStyle() ?>">
                            <?php echo $this->helper('checkout')->formatPrice($amount); ?>
                        </td>
                    <?php endif; ?>
                </tr>
                <?php $isFirst = 0; ?>
                <?php $isTop = 0; ?>
                <?php endforeach; ?>
            <?php endforeach; ?>
<?php endif;?>
<tr<?php if ($this->helper('tax')->displayFullSummary() && $this->getTotal()->getValue()!=0): ?> class="summary-total" onclick="expandDetails(this, '.summary-details-<?php echo $taxIter;?>')"<?php endif; ?>>
    <td class="a-right" colspan="<?php echo $this->getColspan(); ?>" style="<?php echo $this->getTotal()->getStyle() ?>">
        <?php if ($this->helper('tax')->displayFullSummary()): ?>
            <div class="summary-collapse"><?php echo $this->getTotal()->getTitle() ?></div>
        <?php else: ?>
            <?php echo $this->getTotal()->getTitle() ?>
        <?php endif;?>
    </td>
    <td class="a-right" style="<?php echo $this->getTotal()->getStyle() ?>">
        <strong><?php echo $this->helper('checkout')->formatPrice($this->getTotal()->getValue()) ?></strong>
    </td>
</tr>
