<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
    $columns = $this->getColumns();
?>
<table class="data-table" id="wishlist-table">
    <thead>
        <tr>
            <?php foreach ($columns as $column): ?>
                <th><?php echo $column->getTitle();?></th>
            <?php endforeach; ?>
        </tr>
    </thead>
    <tbody>
        <?php if (count($this->getItems())): ?>
            <?php foreach ($this->getItems() as $item): ?>
                <tr id="item_<?php echo $item->getId();?>">
                    <?php foreach ($columns as $column): ?>
                        <td><?php $column->setItem($item); echo $column->toHtml($item);?></td>
                    <?php endforeach; ?>
                </tr>
            <?php endforeach ?>
        <?php else: ?>
            <td colspan="<?php echo count($columns);?>" class="wishlist-empty"><?php echo $this->__('This Wishlist has no Items');?></td>
        <?php endif; ?>
    </tbody>
</table>
<?php foreach ($columns as $column): ?>
    <?php echo $column->getAdditionalHtml();?>
<?php endforeach; ?>
<script type="text/javascript">
//<![CDATA[
    decorateTable('wishlist-table');

<?php foreach ($columns as $column): ?>
    <?php echo $column->getJs();?>
<?php endforeach; ?>
//]]>
</script>
