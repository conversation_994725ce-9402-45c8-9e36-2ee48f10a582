<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/* @var $this Mage_Wishlist_Block_Customer_Sidebar */
?>
<?php if ($this->helper('wishlist')->isAllow()) : ?>
<div class="block block-wishlist">
    <div class="block-title">
        <strong><span><?php echo $this->getTitle(); ?></span></strong>
    </div>
    <div class="block-content">
    <p class="block-subtitle"><?php echo $this->__('Last Added Items') ?></p>
    <?php if ($this->hasWishlistItems()): ?>
    <?php $_params = $this->escapeHtml(json_encode(array('form_key' => $this->getFormKey()))); ?>
    <ol class="mini-products-list" id="wishlist-sidebar">
    <?php foreach ($this->getWishlistItems() as $_item): ?>
        <?php $product = $_item->getProduct(); ?>
        <li class="item">
            <a href="<?php echo $this->getProductUrl($_item) ?>" title="<?php echo $this->escapeHtml($product->getName()) ?>" class="product-image"><img src="<?php echo $this->helper('catalog/image')->init($product, 'thumbnail')->resize(50); ?>" width="50" height="50" alt="<?php echo $this->escapeHtml($product->getName()) ?>" /></a>
            <div class="product-details">
                <a href="#"
                   class="btn-remove"
                   title="<?php echo $this->quoteEscape($this->__('Remove This Item')) ?>"
                   onclick="if (confirm('<?php echo $this->jsQuoteEscape($this->__('Are you sure you would like to remove this item from the wishlist?')) ?>')) {
                        customFormSubmit('<?php echo $this->getItemRemoveUrlCustom($_item, false) ?>', '<?php echo $_params ?>', 'post')
                    }">
                    <?php echo $this->__('Remove This Item') ?>
                </a>
                <p class="product-name"><a href="<?php echo $this->getProductUrl($_item) ?>"><?php echo $this->escapeHtml($product->getName()) ?></a></p>
                <?php echo $this->getPriceHtml($product, false, '-wishlist') ?>
                <?php if ($product->isSaleable() && $product->isVisibleInSiteVisibility()): ?>
                    <a href="#"
                       class="link-cart"
                       onclick="customFormSubmit('<?php echo $this->getItemAddToCartUrlCustom($_item, false) ?>', '<?php echo $_params ?>', 'post')">
                        <?php echo $this->__('Add to Cart') ?>
                    </a>
                <?php endif; ?>
            </div>
        </li>
    <?php endforeach; ?>
    </ol>
    <script type="text/javascript">decorateList('wishlist-sidebar');</script>
    <div class="actions">
        <a href="<?php echo $this->helper('wishlist')->getListUrl() ?>"><?php echo $this->__('Go to Wishlist') ?></a>
    </div>
    <?php else: ?>
        <p class="empty"><?php echo $this->__('You have no items in your wishlist.') ?></p>
    <?php endif ?>
    </div>
</div>
<?php endif ?>
