<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * Catalog advanced search form
 *
 * @see Mage_CatalogSearch_Block_Advanced_Form
 */
?>
<?php $maxQueryLength= $this->helper('catalogsearch')->getMaxQueryLength();?>
<div class="page-title">
    <h1><?php echo $this->__('Catalog Advanced Search') ?></h1>
</div>
<?php echo $this->getMessagesBlock()->toHtml() ?>
<form action="<?php echo $this->getSearchPostUrl() ?>" method="get" id="form-validate">
    <div class="fieldset advanced-search">
        <h2 class="legend"><?php echo $this->__('Search Settings') ?></h2>
        <ul class="form-list" id="advanced-search-list">
            <?php foreach ($this->getSearchableAttributes() as $_attribute): ?>
            <?php $_code = $_attribute->getAttributeCode() ?>
            <li>
                <label for="<?php echo $_code ?>"><?php echo $this->getAttributeLabel($_attribute) ?></label>
                <?php switch($this->getAttributeInputType($_attribute)):
                    case 'number': ?>
                    <div class="input-range">
                        <input type="text" name="<?php echo $_code ?>[from]" value="<?php echo $this->escapeHtml($this->getAttributeValue($_attribute, 'from')) ?>" id="<?php echo $_code ?>" title="<?php echo $this->escapeHtml($this->getAttributeLabel($_attribute)) ?>" class="input-text validate-number" maxlength="<?php echo $maxQueryLength;?>" />
                        <span class="separator">-</span>
                        <input type="text" name="<?php echo $_code ?>[to]" value="<?php echo $this->escapeHtml($this->getAttributeValue($_attribute, 'to')) ?>" id="<?php echo $_code ?>_to" title="<?php echo $this->escapeHtml($this->getAttributeLabel($_attribute)) ?>" class="input-text validate-number" maxlength="<?php echo $maxQueryLength;?>" />
                    </div>
                    <?php break;
                    case 'price': ?>
                    <div class="input-range">
                        <input name="<?php echo $_code ?>[from]" value="<?php echo $this->escapeHtml($this->getAttributeValue($_attribute, 'from')) ?>" id="<?php echo $_code ?>" title="<?php echo $this->escapeHtml($this->getAttributeLabel($_attribute)) ?>"  class="input-text validate-number" type="text" maxlength="<?php echo $maxQueryLength;?>" />
                        <span class="separator">-</span>
                        <input name="<?php echo $_code ?>[to]" value="<?php echo $this->escapeHtml($this->getAttributeValue($_attribute, 'to')) ?>" id="<?php echo $_code ?>_to" title="<?php echo $this->escapeHtml($this->getAttributeLabel($_attribute)) ?>"  class="input-text validate-number" type="text" maxlength="<?php echo $maxQueryLength;?>" />
                        <small>(<?php echo $this->getCurrency($_attribute); ?>)</small>
                    </div>
                    <?php break;
                    case 'select': ?>
                        <div class="input-box">
                            <?php echo $this->getAttributeSelectElement($_attribute) ?>
                        </div>
                    <?php break;
                    case 'yesno': ?>
                        <?php echo $this->getAttributeYesNoElement($_attribute) ?>
                    <?php break;
                    case 'date': ?>
                    <div class="input-range">
                        <?php echo $this->getDateInput($_attribute, 'from') ?>
                        <span class="separator">-</span>
                        <?php echo $this->getDateInput($_attribute, 'to') ?>
                    </div>
                    <?php break;
                    default: ?>
                    <div class="input-box">
                        <input type="text" name="<?php echo $_code ?>" id="<?php echo $_code ?>" value="<?php echo $this->escapeHtml($this->getAttributeValue($_attribute)) ?>" title="<?php echo $this->escapeHtml($this->getAttributeLabel($_attribute)) ?>"  class="input-text <?php echo $this->getAttributeValidationClass($_attribute) ?>" maxlength="<?php echo $maxQueryLength;?>" />
                    </div>
                <?php endswitch; ?>
            </li>
            <?php endforeach; ?>
        </ul>
        <script type="text/javascript">decorateList('advanced-search-list')</script>
    </div>
    <div class="buttons-set">
        <button type="submit" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Search')) ?>" class="button"><span><span><?php echo $this->__('Search') ?></span></span></button>
    </div>
</form>
<script type="text/javascript">
//<![CDATA[
    var dataForm = new VarienForm('form-validate', true);
//]]>
</script>
