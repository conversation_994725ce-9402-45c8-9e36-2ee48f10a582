<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/* @var $this Mage_Reports_Block_Product_Viewed */
?>
<?php
/**
 * @see Mage_Reports_Block_Product_Viewed
 */
?>
<?php if ($_products = $this->getRecentlyViewedProducts()): ?>
<h2 class="subtitle"><?php echo $this->__('Your Recently Viewed') ?></h2>
<?php $_columnCount = $this->getColumnCount(); ?>
<?php $_params = $this->escapeHtml(json_encode(array('form_key' => $this->getFormKey()))); ?>
    <?php $i=0; foreach ($_products as $_product): ?>
        <?php if ($i++%$_columnCount==0): ?>
        <ul class="products-grid">
        <?php endif; ?>
            <li class="item<?php if(($i-1)%$_columnCount==0): ?> first<?php elseif($i%$_columnCount==0): ?> last<?php endif; ?>">
                <a href="<?php echo $this->getProductUrl($_product) ?>" title="<?php echo $this->stripTags($_product->getName(), null, true) ?>" class="product-image"><img src="<?php echo $this->helper('catalog/image')->init($_product, 'small_image')->resize(135) ?>" width="135" height="135" alt="<?php echo $this->stripTags($_product->getName(), null, true) ?>" /></a>
                <h3 class="product-name"><a href="<?php echo $this->getProductUrl($_product) ?>" title="<?php echo $this->stripTags($_product->getName(), null, true) ?>"><?php echo $this->helper('catalog/output')->productAttribute($_product, $_product->getName() , 'name') ?></a></h3>
                <?php echo $this->getReviewsSummaryHtml($_product, 'short') ?>
                <?php echo $this->getPriceHtml($_product, true, '-home-viewed') ?>
                <div class="actions">
                    <?php if($_product->isSaleable()): ?>
                        <button type="button"
                                title="<?php echo Mage::helper('core')->quoteEscape($this->__('Add to Cart')) ?>"
                                class="button btn-cart"
                                onclick="customFormSubmit(
                                        '<?php echo $this->getAddToCartUrlCustom($_product, array(), false) ?>',
                                        '<?php echo $_params ?>',
                                        'post')">
                            <span><span><?php echo $this->__('Add to Cart') ?></span></span>
                        </button>
                    <?php else: ?>
                        <p class="availability out-of-stock"><span><?php echo $this->__('Out of stock') ?></span></p>
                    <?php endif; ?>
                    <ul class="add-to-links">
                        <?php if ($this->helper('wishlist')->isAllow()) : ?>
                            <?php $_wishlistUrl = $this->getAddToWishlistUrlCustom($_product, false); ?>
                            <li>
                                <a href="#"
                                   data-url="<?php echo $_wishlistUrl ?>"
                                   data-params="<?php echo $_params ?>"
                                   class="link-wishlist"
                                   onclick="customFormSubmit('<?php echo $_wishlistUrl ?>', '<?php echo $_params ?>', 'post')">
                                    <?php echo $this->__('Add to Wishlist') ?>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if ($_compareUrl = $this->getAddToCompareUrlCustom($_product, false)) : ?>
                            <li>
                                <span class="separator">|</span>
                                <a href="#"
                                   class="link-compare"
                                   onclick="customFormSubmit('<?php echo $_compareUrl ?>', '<?php echo $_params ?>', 'post')">
                                    <?php echo $this->__('Add to Compare') ?>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </li>
        <?php if ($i%$_columnCount==0 || $i==count($_products)): ?>
        </ul>
        <?php endif; ?>
    <?php endforeach; ?>
<?php endif; ?>
