<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<div class="page-title">
   <h1><?php echo Mage::helper('sales')->__('Orders and Returns') ?></h1>
</div>


<form id="oar_widget_orders_and_returns_form" action="<?php echo $this->getActionUrl() ?>" method="post" class="search-form" name="guest_post">
    <div class="fieldset">
        <h2 class="legend"><?php echo Mage::helper('sales')->__('Order Information') ?></h2>

        <ul class="form-list">
           <li>
               <div class="input-box">
                   <label for="oar_order_id" class="required"><?php echo Mage::helper('sales')->__('Order ID') ?> <em>*</em></label>
               </div>
               <div class="input-box">
                   <input type="text" class="input-text required-entry" id="oar_order_id" name="oar_order_id" style="width:300px;"/>
               </div>
           </li>
           <li class="wide">
               <div class="input-box">
                   <?php echo Mage::helper('sales')->__('Enter the billing last name and email/ZIP as in the order billing address') ?>.
                </div>
           </li>
           <li>
               <div class="input-box">
                   <label for="oar_billing_lastname" class="required"><?php echo Mage::helper('sales')->__('Billing Last Name') ?> <em>*</em></label>
               </div>
               <div class="input-box">
                   <input type="text" class="input-text required-entry" id="oar_billing_lastname" name="oar_billing_lastname"  style="width:300px;"/>
               </div>
           </li>
           <li>
               <div class="input-box">
                   <label class="required"><?php echo Mage::helper('sales')->__('Find Order By:') ?></label>
               </div>
               <div class="input-box">
                   <select name="oar_type" id="quick_search_type_id" class="select guest-select" title="" onchange="showIdentifyBlock(this.value);">
                       <option value="email"><?php echo Mage::helper('sales')->__('Email Address') ?></option>
                       <option value="zip"><?php echo Mage::helper('sales')->__('ZIP Code') ?></option>
                   </select>
               </div>
           </li>
           <li id="oar-email">
               <div class="input-box">
                   <label for="oar_email" class="required"><?php echo Mage::helper('sales')->__('Email Address') ?> <em>*</em></label>
               </div>
               <div class="input-box">
                   <input type="text" class="input-text validate-email required-entry" id="oar_email" name="oar_email"  style="width:300px;"/>
               </div>
           </li>
           <li id="oar-zip" style="display:none;">
               <div class="input-box">
                   <label for="oar_zip" class="required"><?php echo Mage::helper('sales')->__('Billing ZIP Code') ?> <em>*</em></label>
               </div>
               <div class="input-box">
                   <input type="text" class="input-text required-entry" id="oar_zip" name="oar_zip" style="width:300px;"/>
               </div>
           </li>
       </ul>
    </div>
    <div class="buttons-set">
        <p class="required"><?php echo Mage::helper('sales')->__('* Required Fields') ?></p>
        <button type="submit" title="<?php echo Mage::helper('sales')->__('Continue') ?>" class="button"><span><span><?php echo Mage::helper('sales')->__('Continue') ?></span></span></button>
    </div>
</form>

<script type="text/javascript">
//<![CDATA[
    if ($('quick_search_type_id').value == 'zip') {
        $('oar-zip').show();
        $('oar-email').hide();
    } else {
       $('oar-zip').hide();
       $('oar-email').show();
    }

   var dataForm = new VarienForm('oar_widget_orders_and_returns_form', true);

   function showIdentifyBlock(id)
   {
       if (id == 'zip') {
           $('oar-zip').show();
           $('oar-email').hide();
       } else if (id == 'email') {
           $('oar-zip').hide();
           $('oar-email').show();
       }
       return false;
   }
//]]>
</script>
