<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * @see Mage_Downloadable_Block_Customer_Products_List
 */
?>
<?php $_items = $this->getItems(); ?>
<div class="page-title">
    <h1><?php echo Mage::helper('downloadable')->__('My Downloadable Products') ?></h1>
</div>
<?php echo $this->getMessagesBlock()->toHtml() ?>
<?php echo $this->getChildHtml('pager'); ?>
<?php if(count($_items)): ?>
    <table class="data-table" id="my-downloadable-products-table">
        <col width="1" />
        <col width="1" />
        <col />
        <col width="1" />
        <col width="1" />
        <thead>
            <tr>
                <th><?php echo Mage::helper('downloadable')->__('Order #') ?></th>
                <th><?php echo Mage::helper('downloadable')->__('Date') ?></th>
                <th><?php echo Mage::helper('downloadable')->__('Title') ?></th>
                <th><?php echo Mage::helper('downloadable')->__('Status') ?></th>
                <th><span class="nobr"><?php echo Mage::helper('downloadable')->__('Remaining Downloads') ?></span></th>
            </tr>
        </thead>
        <tbody>
            <?php $_odd = ''; ?>
            <?php foreach ($_items as $_item): ?>
                <tr>
                    <td><a href="<?php echo $this->getOrderViewUrl($_item->getPurchased()->getOrderId()) ?>" title="<?php echo Mage::helper('core')->quoteEscape(Mage::helper('downloadable')->__('View Order')) ?>"><?php echo $_item->getPurchased()->getOrderIncrementId() ?></a></td>
                    <td><span class="nobr"><?php echo $this->formatDate($_item->getPurchased()->getCreatedAt()) ?></span></td>
                    <td><?php echo $this->escapeHtml($_item->getPurchased()->getProductName()) ?> - <a href="<?php echo $this->getDownloadUrl($_item) ?>" title="<?php echo Mage::helper('core')->quoteEscape(Mage::helper('downloadable'))->__('Start Download') ?>" <?php echo $this->getIsOpenInNewWindow()?'onclick="this.target=\'_blank\'"':''; ?>><?php echo $this->escapeHtml($_item->getLinkTitle()); ?></a></td>
                    <td><em><?php echo Mage::helper('downloadable')->__(ucfirst($_item->getStatus())) ?></em></td>
                    <td><?php echo $this->getRemainingDownloads($_item) ?></td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
    <script type="text/javascript">decorateTable('my-downloadable-products-table')</script>
<?php else: ?>
    <p><?php echo Mage::helper('downloadable')->__('You have not purchased any downloadable products yet.'); ?></p>
<?php endif; ?>
<?php echo $this->getChildHtml('pager'); ?>
<div class="buttons-set">
    <p class="back-link"><a href="<?php echo $this->escapeUrl($this->getBackUrl()) ?>"><small>&laquo; </small><?php echo $this->__('Back') ?></a></p>
</div>
