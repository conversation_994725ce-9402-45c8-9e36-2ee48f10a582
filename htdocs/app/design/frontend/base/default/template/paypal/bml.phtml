<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * @see Mage_Paypal_Block_Bml_Banners
 */
?>
<div class="paypal-logo bill-me-latter">
<script type="text/javascript" data-pp-pubid="<?php echo $this->escapeHtml($this->getPublisherId()) ?>" data-pp-placementtype="<?php echo $this->escapeHtml($this->getSize()) ?>"> (function (d, t) {
    "use strict";
    var s = d.getElementsByTagName(t)[0], n = d.createElement(t);
    n.src = "//paypal.adtag.where.com/merchant.js";
    s.parentNode.insertBefore(n, s);
}(document, "script"));
</script>
</div>
