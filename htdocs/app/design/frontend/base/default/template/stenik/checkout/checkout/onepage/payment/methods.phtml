<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2017 X.commerce, Inc. and affiliates (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * One page checkout payment methods
 *
 * @var $this Mage_Checkout_Block_Onepage_Payment_Methods
 */
?>
<script type="text/javascript">
//<![CDATA[
    var quoteBaseGrandTotal = <?php echo (float)$this->getQuoteBaseGrandTotal(); ?>;
    var checkQuoteBaseGrandTotal = quoteBaseGrandTotal;
    var lastPrice;
//]]>
</script>


<?php
    $methods = $this->getMethods();
    $oneMethod = count($methods) <= 1;
?>

<div class="clear"></div>

<div class="step-title">
    <span class="step-title-text"><?php echo $this->__('Payment method'); ?></span>
</div>

<dl class="sp-methods">
<?php if (empty($methods)): ?>
        <dt><?php echo $this->__('No Payment Methods') ?></dt>
<?php else:  ?>
    <?php foreach ($methods as $_method): ?>
        <?php $_code = $_method->getCode(); ?>
        <dt id="dt_method_<?php echo $_code ?>">
        <?php if (!$oneMethod): ?>
            <input id="p_method_<?php echo $_code ?>" data-payment-method-content-selector="#payment_form_<?php echo $_code ?>,#dd_method_<?php echo $_code ?>" class="paymentMethod" value="<?php echo $_code ?>" type="radio" name="payment[method]" title="<?php echo $this->escapeHtml($_method->getTitle()) ?>"<?php if($this->getSelectedMethodCode()==$_code): ?> checked="checked"<?php endif; ?> class="radio" />
        <?php else: ?>
            <span class="no-display"><input id="p_method_<?php echo $_code ?>" value="<?php echo $_code ?>" type="radio" name="payment[method]" checked="checked" class="radio" /></span>
            <?php $oneMethod = $_code; ?>
        <?php endif; ?>
            <label for="p_method_<?php echo $_code ?>"><?php echo $this->escapeHtml($this->getMethodTitle($_method)) ?> <?php echo $this->getMethodLabelAfterHtml($_method) ?></label>
        </dt>
        <?php if ($html = $this->getPaymentMethodFormHtml($_method)): ?>
        <dd id="dd_method_<?php echo $_code ?>" style="display: none">
            <?php echo $html; ?>
        </dd>
        <?php endif; ?>
    <?php endforeach; ?>
<?php endif; ?>
</dl>

<div class="clearH"></div>

<?php echo $this->getChildChildHtml('additional'); ?>

<div class="clearH"></div>

<script type="text/javascript">
    <?php echo $this->getChildChildHtml('scripts'); ?>

    (function($) {
        var changePaymentVisibility = function() {
            $('.sp-methods input.paymentMethod').not(this).each(function() {
                if ($(this).data('payment-method-content-selector')) {
                    var $content = $($(this).data('payment-method-content-selector'));
                    $content.hide();
                    $content.find('input,textarea,select').prop('disabled', true).attr('disabled', true);
                }
            });

            if ($(this).is(':checked') && $(this).data('payment-method-content-selector')) {
                var $content = $($(this).data('payment-method-content-selector'));
                $content.show();
                $content.find('input,textarea,select').prop('disabled', false).attr('disabled', false);
            }
        };
        $('.sp-methods input.paymentMethod').change(changePaymentVisibility);
        $('.sp-methods input.paymentMethod:checked').each(changePaymentVisibility);
    })(jQuery);
</script>
