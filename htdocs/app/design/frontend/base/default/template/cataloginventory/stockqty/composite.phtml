<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

/**
 * @see Mage_CatalogInventory_Block_Stockqty_Composite
 */
?>
<?php if($this->isMsgVisible()): ?>
    <p class="availability-only">
        <a href="#" id="<?php echo $this->getPlaceholderId() ?>" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Only %s left', ($this->getStockQty()))) ?>"><?php echo $this->__('Only %s left', "<strong>{$this->getStockQty()}</strong>") ?></a>
    </p>
    <table id="<?php echo $this->getDetailsPlaceholderId() ?>" class="availability-only-details no-display">
        <col />
        <col width="1" />
        <thead>
            <tr>
                <th><?php echo $this->__('Product Name') ?></th>
                <th class="a-center"><?php echo $this->__('Qty') ?></th>
            </tr>
        </thead>
        <tbody>
        <?php foreach ($this->getChildProducts() as $childProduct) : ?>
            <?php $childProductStockQty = $this->getProductStockQty($childProduct); ?>
            <?php if ($childProductStockQty > 0) : ?>
                <tr>
                    <td><?php echo $childProduct->getName() ?></td>
                    <td class="a-center"><?php echo $childProductStockQty ?></td>
                </tr>
            <?php endif ?>
        <?php endforeach ?>
        </tbody>
    </table>
    <script type="text/javascript">
    //<![CDATA[
    $('<?php echo $this->getPlaceholderId() ?>').observe('click', function(event){
        this.toggleClassName('expanded');
        $('<?php echo $this->getDetailsPlaceholderId() ?>').toggleClassName('no-display');
        event.stop();
        decorateTable('<?php echo $this->getDetailsPlaceholderId() ?>');
    });
    //]]>
    </script>
<?php endif ?>
