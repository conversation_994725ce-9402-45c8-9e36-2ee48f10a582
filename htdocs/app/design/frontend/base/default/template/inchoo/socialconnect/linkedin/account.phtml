<?php
/**
 * Inchoo is not affiliated with or in any way responsible for this code.
 *
 * Commercial support is available directly from the [extension author](http://www.techytalk.info/contact/).
 *
 * @category Marko-M
 * @package SocialConnect
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) <PERSON><PERSON> (http://www.techytalk.info)
 * @license http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 */
?>

<?php /* @var $this Inchoo_SocialConnect_Block_Linkedin_Account */ ?>

<div class="inchoo-socialconnect-account">
    <div class="page-title">
        <h1><?php echo $this->__('LinkedIn Connect') ?></h1>
    </div>

    <?php if($this->_hasData()): ?>
        <?php
            $linkedinId = $this->_getLinkedinId();
            $status = $this->_getStatus();
            $publicProfile = $this->_getPublicProfileUrl();
            $email = $this->_getEmail();
            $name = $this->_getName();
            $picture = $this->_getPicture();
        ?>

        <div class="col3-set">
            <div class="box">
                <div class="box-content">
                    <div class="col-1">
                        <?php if(!empty($picture)): ?>
                        <p>
                            <img src="<?php echo $picture; ?>" alt="<?php echo $this->escapeHtml($name); ?>" />
                        </p>
                        <?php endif; ?>
                    </div>
                    <div class="col-2">
                        <?php if(!empty($status)): ?>
                        <p>
                            <?php printf($this->__('Connected as %s', '<strong>'.$status.'</strong>')); ?>
                        </p>
                        <?php endif; ?>
                        <?php if(!empty($publicProfile)): ?>
                        <p>
                            <?php printf($this->__('Public Profile: %s', '<strong>'.$publicProfile.'</strong>')) ?>
                        </p>    
                        <?php endif; ?>
                        <?php if(!empty($email)): ?>
                        <p>
                            <?php printf($this->__('Email: %s', '<strong>'.$email.'</strong>')) ?>
                        </p>
                        <?php endif; ?>
                        <?php if(!empty($linkedinId)): ?>
                        <p>
                            <?php printf($this->__('LinkedinId #: %s', '<strong>'.$linkedinId.'</strong>')) ?>
                        </p>
                        <?php endif; ?>
                    </div>
                    <div class="col-3">
                        <?php echo $this->getChildHtml('inchoo_socialconnect_account_linkedin_button'); ?>
                    </div>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="col2-set">
            <div class="box-content">
                <div class="col-1">
                    <p>
                        <?php echo $this->__('You can connect store account with your LinkedIn account so you could login easier in the future.') ?>
                    </p>
                </div>
                <div class="col-2">
                    <?php echo $this->getChildHtml('inchoo_socialconnect_account_linkedin_button'); ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>