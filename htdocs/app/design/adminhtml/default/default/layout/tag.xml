<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<layout>
    <adminhtml_tag_edit>
        <reference name="content">
            <block type="adminhtml/tag_edit" name="tag_edit" template="tag/edit/container.phtml"></block>
        </reference>
    </adminhtml_tag_edit>
    <adminhtml_tag_assigned>
            <remove name="root" />
            <block type="adminhtml/tag_assigned_grid" name="tag_assigned_grid" output="toHtml" />
            <block type="adminhtml/widget_grid_serializer" name="tag_grid_serializer" output="toHtml">
                <reference name="tag_grid_serializer">
                    <action method="initSerializerBlock">
                        <grid_block_name>tag_assigned_grid</grid_block_name>
                        <data_callback>getRelatedProducts</data_callback>
                        <hidden_input_name>tag_assigned_products</hidden_input_name>
                        <reload_param_name>assigned_products</reload_param_name>
                    </action>
                </reference>
            </block>
    </adminhtml_tag_assigned>
    <adminhtml_tag_assignedgridonly>
        <remove name="root" />
        <block type="adminhtml/tag_assigned_grid" name="assigned_grid" output="toHtml" />
    </adminhtml_tag_assignedgridonly>

    <adminhtml_tag_index>
        <reference name="content">
            <block type="adminhtml/tag_tag" name="adminhtml.tag.tag"/>
        </reference>
    </adminhtml_tag_index>

    <adminhtml_tag_pending>
        <reference name="content">
            <block type="adminhtml/tag_pending" name="adminhtml.tag.pending"/>
        </reference>
    </adminhtml_tag_pending>

    <adminhtml_tag_ajaxgrid>
        <block type="core/text_list" name="root" output="toHtml">
            <block type="adminhtml/tag_tag_grid" name="adminhtml.tag.tag.grid"/>
        </block>
    </adminhtml_tag_ajaxgrid>

    <adminhtml_tag_ajaxpendinggrid>
        <block type="core/text_list" name="root" output="toHtml">
            <block type="adminhtml/tag_grid_pending" name="adminhtml.tag.grid.pending"/>
        </block>
    </adminhtml_tag_ajaxpendinggrid>

    <adminhtml_tag_product>
        <block type="core/text_list" name="root" output="toHtml">
            <block type="adminhtml/tag_product_grid" name="adminhtml.tag.product.grid"/>
        </block>
    </adminhtml_tag_product>

    <adminhtml_tag_customer>
        <block type="core/text_list" name="root" output="toHtml">
            <block type="adminhtml/tag_customer_grid" name="adminhtml.tag.customer.grid"/>
        </block>
    </adminhtml_tag_customer>
</layout>
