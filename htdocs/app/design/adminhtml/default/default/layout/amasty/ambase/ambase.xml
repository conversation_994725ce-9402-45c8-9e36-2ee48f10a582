<?xml version="1.0"?>
<!--
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) 2020 Amasty (https://www.amasty.com)
 * @package Amasty_Base
 */
-->
<layout>
    <adminhtml_system_config_edit>
        <reference name="head">
            <action method="addCss">
                <name>css/amasty/ambase/style.css</name>
            </action>
            <action method="addJs">
                <name>amasty/ambase/store.js</name>
            </action>
        </reference>

        <reference name="content">
            <block type="ambase/adminhtml_notifications" name="amasty.update.notifications" as="amasty.update.notifications" template="amasty/ambase/notifications.phtml"></block>
        </reference>
    </adminhtml_system_config_edit>

    <adminhtml_ambase_base_ajax>
        <reference name="root">
            <action method="setTemplate">
                <template>empty.phtml</template>
            </action>
        </reference>
    </adminhtml_ambase_base_ajax>

    <default>
        <reference name="head">
            <action method="addCss">
                <name>css/amasty/ambase/default.css</name>
            </action>
        </reference>
        <reference name="root">
            <reference name="notifications">
                <block type="ambase/adminhtml_promo" name="ampromo" as="ampromo" after="messages" template="amasty/ambase/promo.phtml"></block>
            </reference>
            
            <reference name="content">
                <block type="ambase/adminhtml_update" name="amupdate" as="amupdate" template="amasty/ambase/update.phtml"></block>
            </reference>

            <reference name="before_body_end">
                <block type="ambase/adminhtml_logo" name="amlogo" as="amlogo" template="amasty/ambase/logo.phtml"></block>
            </reference>

        </reference>
    </default>

</layout>