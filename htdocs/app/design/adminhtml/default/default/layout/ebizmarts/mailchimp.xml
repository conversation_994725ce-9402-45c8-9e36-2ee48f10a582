<?xml version="1.0"?>
<layout>
    <adminhtml_mailchimperrors_grid>
        <update handle="formkey"/>
        <block type="mailchimp/adminhtml_mailchimperrors_grid"
               name="mailchimp_mailchimperrors.grid" output="toHtml"/>
    </adminhtml_mailchimperrors_grid>
    <adminhtml_mailchimperrors_index>
        <reference name="content">
            <block type="mailchimp/adminhtml_mailchimperrors" name="mailchimp_mailchimperrors.grid.container"/>
        </reference>
    </adminhtml_mailchimperrors_index>
    <adminhtml_mailchimpstores_grid>
        <update handle="formkey"/>
        <block type="mailchimp/adminhtml_mailchimpstores_grid" name="mailchimp_mailchimpstores.grid" output="toHtml"/>
    </adminhtml_mailchimpstores_grid>
    <adminhtml_mailchimpstores_index>
        <reference name="content">
            <block type="mailchimp/adminhtml_mailchimpstores" name="mailchimp_mailchimpstores.grid.container"/>
        </reference>
    </adminhtml_mailchimpstores_index>
    <adminhtml_mergevars_addmergevar>
        <reference name="content">
            <update handle="default"/>
            <remove name="header"/>
            <remove name="menu"/>
            <remove name="footer"/>
            <remove name="notifications"/>
            <remove name="global_notices"/>
            <block type="mailchimp/adminhtml_mergevars_add" name="adminhtml.mailchimp.add"/>
        </reference>
    </adminhtml_mergevars_addmergevar>
    <adminhtml_ecommerce_renderresendecom>
        <reference name="content">
            <update handle="default"/>
            <remove name="header"/>
            <remove name="menu"/>
            <remove name="footer"/>
            <remove name="notifications"/>
            <remove name="global_notices"/>
            <block type="mailchimp/adminhtml_ecommerce_resendecommercedata" name="adminhtml.mailchimp.resend"></block>
        </reference>
    </adminhtml_ecommerce_renderresendecom>
    <adminhtml_system_config_edit>
        <reference name="head">
            <action method="addCss">
                <stylesheet>ebizmarts/mailchimp/css/mailchimp.css</stylesheet>
            </action>
        </reference>
        <reference name="head">
            <action method="addCss">
                <stylesheet>ebizmarts/mandrill/css/mandrill.css</stylesheet>
            </action>
        </reference>
    </adminhtml_system_config_edit>
    <adminhtml_sales_order_view>
        <reference name="order_info">
            <block type="mailchimp/adminhtml_sales_order_view_info_monkey" name="mailchimp.order.info.monkey.block" template="ebizmarts/mailchimp/sales/order/view/monkey.phtml" before="order_history" />
        </reference>
    </adminhtml_sales_order_view>
    <default>
        <reference name="notifications">
            <block type="mailchimp/adminhtml_notifications" name="mailchimp_notifications" template="ebizmarts/mailchimp/notifications.phtml"/>
        </reference>
        <reference name="head">
            <action method="addItem">
                <type>js_css</type>
                <name>prototype/windows/themes/default.css</name>
            </action>
            <action method="addCss">
                <name>lib/prototype/windows/themes/magento.css</name>
            </action>
        </reference>
    </default>
</layout>
