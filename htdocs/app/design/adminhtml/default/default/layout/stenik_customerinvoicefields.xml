<?xml version="1.0"?>
<!--
/**
 * @package Stenik_CustomerInvoiceFields
 * <AUTHOR> Magento Team <<EMAIL>>
 */
-->

<layout>
    <adminhtml_sales_order_view>
        <reference name="stenik.order_info.after">
            <block type="adminhtml/sales_order_view_tab_info" name="stenik_customerinvoicefields.order_info.invoice" template="stenik/customerinvoicefields/order/info/invoice.phtml">
                <block type="core/text_list" name="stenik_customerinvoicefields.order_info.invoice.additional" as="additional"/>
            </block>
        </reference>
    </adminhtml_sales_order_view>
    <adminhtml_sales_order_invoice_new>
        <reference name="stenik.order_info.after">
            <block type="adminhtml/sales_order_invoice_create_form" name="stenik_customerinvoicefields.order_info.invoice" template="stenik/customerinvoicefields/order/info/invoice.phtml">
                <block type="core/text_list" name="stenik_customerinvoicefields.order_info.invoice.additional" as="additional"/>
            </block>
        </reference>
    </adminhtml_sales_order_invoice_new>
    <adminhtml_sales_order_invoice_view>
        <reference name="stenik.order_info.after">
            <block type="adminhtml/sales_order_invoice_create_form" name="stenik_customerinvoicefields.order_info.invoice" template="stenik/customerinvoicefields/order/info/invoice.phtml">
                <block type="core/text_list" name="stenik_customerinvoicefields.order_info.invoice.additional" as="additional"/>
            </block>
        </reference>
    </adminhtml_sales_order_invoice_view>
</layout>