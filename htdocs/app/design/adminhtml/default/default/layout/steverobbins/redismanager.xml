<?xml version="1.0"?>
<!--
/**
 * Redis Management Module
 * 
 * @category   Steverobbins
 * @package    Stevero<PERSON>ins_Redismanager
 * <AUTHOR> <<EMAIL>>
 * @copyright  Copyright (c) 2014 <PERSON> (https://github.com/steverobbins)
 * @license    http://creativecommons.org/licenses/by/3.0/deed.en_US Creative Commons Attribution 3.0 Unported License
 */
-->
<layout version="0.1.0">
    <adminhtml_redismanager_index>
        <reference name="head">
            <action method="addCss"><stylesheet>steverobbins/redismanager/redismanager.css</stylesheet></action>
        </reference>
        <reference name="content">
            <block type="redismanager/adminhtml_manager" name="redismanager.manager" template="steverobbins/redismanager/manager.phtml">
                <block type="redismanager/adminhtml_manager" name="redismanager.grid" template="steverobbins/redismanager/grid.phtml" />
            </block>
        </reference>
    </adminhtml_redismanager_index>
    <adminhtml_redismanager_grid>
        <block type="redismanager/adminhtml_manager" name="root" output="toHtml" template="steverobbins/redismanager/grid.phtml" />
    </adminhtml_redismanager_grid>
</layout>