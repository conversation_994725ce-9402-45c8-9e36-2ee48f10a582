<?xml version="1.0"?>
<layout>
    <adminhtml_catalog_category_edit>
        <reference name="head">
            <action method="addJs">
                <name><![CDATA[pfg/olx/lib/jquery/jquery-3.4.1.min.js]]></name>
            </action>
            <action method="addJs">
                <name><![CDATA[pfg/olx/lib/jquery/noconflict.js]]></name>
            </action>
            <action method="addJs">
                <name><![CDATA[pfg/olx/adminhtml/location_dependency.js]]></name>
            </action>
        </reference>
    </adminhtml_catalog_category_edit>
    <adminhtml_catalog_product_edit>
        <reference name="head">
            <action method="addJs">
                <name><![CDATA[pfg/olx/lib/jquery/jquery-3.4.1.min.js]]></name>
            </action>
            <action method="addJs">
                <name><![CDATA[pfg/olx/lib/jquery/noconflict.js]]></name>
            </action>
            <action method="addJs">
                <name><![CDATA[pfg/olx/adminhtml/location_dependency.js]]></name>
            </action>
        </reference>
    </adminhtml_catalog_product_edit>
    <adminhtml_system_config_edit>
        <reference name="head">
            <action method="addCss">
                <name><![CDATA[pfg/olx/bootstrap.css]]></name>
            </action>
            <action method="addJs">
                <name><![CDATA[pfg/olx/lib/jquery/jquery-3.4.1.min.js]]></name>
            </action>
            <action method="addJs">
                <name><![CDATA[pfg/olx/lib/jquery/noconflict.js]]></name>
            </action>
            <action method="addJs">
                <name><![CDATA[pfg/olx/lib/jquery/jquery-ui/jquery-ui.min.js]]></name>
            </action>
            <action method="addCss">
                <name><![CDATA[pfg/olx/lib/jquery/jquery-ui/jquery-ui.css]]></name>
            </action>
            <action method="addCss">
                <name><![CDATA[pfg/olx/lib/jquery/jquery-ui/jquery-ui.theme.css]]></name>
            </action>
            <action method="addJs">
                <name><![CDATA[pfg/olx/adminhtml/location_dependency.js]]></name>
            </action>
            <action method="addJs">
                <name><![CDATA[pfg/olx/adminhtml/description_template_editor.js]]></name>
            </action>
            <action method="addJs">
                <name><![CDATA[pfg/olx/lib/bootstrap.bundle.js]]></name>
            </action>
        </reference>
    </adminhtml_system_config_edit>
    <adminhtml_olxmassactions_promote_confirm>
        <reference name="head">
            <action method="addCss">
                <name><![CDATA[pfg/olx/bootstrap.css]]></name>
            </action>
            <action method="addJs">
                <name><![CDATA[pfg/olx/lib/jquery/jquery-3.4.1.min.js]]></name>
            </action>
            <action method="addJs">
                <name><![CDATA[pfg/olx/lib/jquery/noconflict.js]]></name>
            </action>
            <action method="addJs">
                <name><![CDATA[pfg/olx/lib/bootstrap.bundle.js]]></name>
            </action>
            <block type="core/text" name="calendar">
                <action method="setText">
                    <text><![CDATA[<meta name="viewport" content="width=device-width, initial-scale=1">]]></text>
                </action>
            </block>
        </reference>
        <reference name="content">
            <block type="pfgolx/adminhtml_catalog_product_massAction_promoteConfirm" name="pfgolx.massactions.promote.confirm" />
        </reference>
    </adminhtml_olxmassactions_promote_confirm>
    <adminhtml_olxmassactions_delete_confirm>
        <reference name="head">
            <action method="addCss">
                <name><![CDATA[pfg/olx/bootstrap.css]]></name>
            </action>
            <action method="addJs">
                <name><![CDATA[pfg/olx/lib/jquery/jquery-3.4.1.min.js]]></name>
            </action>
            <action method="addJs">
                <name><![CDATA[pfg/olx/lib/jquery/noconflict.js]]></name>
            </action>
            <action method="addJs">
                <name><![CDATA[pfg/olx/lib/bootstrap.bundle.js]]></name>
            </action>
            <block type="core/text" name="calendar">
                <action method="setText">
                    <text><![CDATA[<meta name="viewport" content="width=device-width, initial-scale=1">]]></text>
                </action>
            </block>
        </reference>
        <reference name="content">
            <block type="pfgolx/adminhtml_catalog_product_massAction_deleteConfirm" name="pfgolx.massactions.delete.confirm" />
        </reference>
    </adminhtml_olxmassactions_delete_confirm>
    <adminhtml_olxadpreview_index>
        <reference name="content">
            <block type="pfgolx/adminhtml_system_config_form_olxAdPreview" name="pfgolx.system.config.olx_ad_preview"/>
        </reference>
    </adminhtml_olxadpreview_index>
</layout>