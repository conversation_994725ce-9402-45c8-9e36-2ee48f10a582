<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->

<layout>

<!--
Layout handle for budle products
-->

    <!--<default>
        <reference name="left">
            <action method="setData"><attribute>attributeTabs</attribute><attributeType>bundle/adminhtml_catalog_product_edit_tab_attributes</attributeType></action>
        </reference>
    </default>-->

    <adminhtml_catalog_product_bundle>
        <reference name="product_tabs">
            <action method="addTab"><name>bundle_items</name><block>bundle/adminhtml_catalog_product_edit_tab_bundle</block></action>
            <action method="bindShadowTabs"><first>bundle_items</first><second>customer_options</second></action>
        </reference>
    </adminhtml_catalog_product_bundle>

    <adminhtml_sales_order_view>
        <reference name="order_items">
            <action method="addItemRender"><type>bundle</type><block>bundle/adminhtml_sales_order_view_items_renderer</block><template>bundle/sales/order/view/items/renderer.phtml</template></action>
        </reference>
    </adminhtml_sales_order_view>

    <adminhtml_sales_order_invoice_new>
        <reference name="order_items">
            <action method="addItemRender"><type>bundle</type><block>bundle/adminhtml_sales_order_items_renderer</block><template>bundle/sales/invoice/create/items/renderer.phtml</template></action>
        </reference>
    </adminhtml_sales_order_invoice_new>

    <adminhtml_sales_order_invoice_updateqty>
        <reference name="order_items">
            <action method="addItemRender"><type>bundle</type><block>bundle/adminhtml_sales_order_items_renderer</block><template>bundle/sales/invoice/create/items/renderer.phtml</template></action>
        </reference>
    </adminhtml_sales_order_invoice_updateqty>

    <adminhtml_sales_order_invoice_view>
        <reference name="invoice_items">
            <action method="addItemRender"><type>bundle</type><block>bundle/adminhtml_sales_order_items_renderer</block><template>bundle/sales/invoice/view/items/renderer.phtml</template></action>
        </reference>
    </adminhtml_sales_order_invoice_view>

    <adminhtml_sales_order_shipment_new>
        <reference name="order_items">
            <action method="addItemRender"><type>bundle</type><block>bundle/adminhtml_sales_order_items_renderer</block><template>bundle/sales/shipment/create/items/renderer.phtml</template></action>
        </reference>
    </adminhtml_sales_order_shipment_new>

    <adminhtml_sales_order_shipment_view>
        <reference name="shipment_items">
            <action method="addItemRender"><type>bundle</type><block>bundle/adminhtml_sales_order_items_renderer</block><template>bundle/sales/shipment/view/items/renderer.phtml</template></action>
        </reference>
    </adminhtml_sales_order_shipment_view>

    <adminhtml_sales_order_creditmemo_new>
        <reference name="order_items">
            <action method="addItemRender"><type>bundle</type><block>bundle/adminhtml_sales_order_items_renderer</block><template>bundle/sales/creditmemo/create/items/renderer.phtml</template></action>
        </reference>
    </adminhtml_sales_order_creditmemo_new>

    <adminhtml_sales_order_creditmemo_updateqty>
        <reference name="order_items">
            <action method="addItemRender"><type>bundle</type><block>bundle/adminhtml_sales_order_items_renderer</block><template>bundle/sales/creditmemo/create/items/renderer.phtml</template></action>
        </reference>
    </adminhtml_sales_order_creditmemo_updateqty>

    <adminhtml_sales_order_creditmemo_view>
        <reference name="creditmemo_items">
            <action method="addItemRender"><type>bundle</type><block>bundle/adminhtml_sales_order_items_renderer</block><template>bundle/sales/creditmemo/view/items/renderer.phtml</template></action>
        </reference>
    </adminhtml_sales_order_creditmemo_view>
    
    <adminhtml_customer_wishlist>
        <reference name="customer.wishlist.edit.tab">
            <action method="addProductConfigurationHelper"><type>bundle</type><name>bundle/catalog_product_configuration</name></action>
        </reference>
    </adminhtml_customer_wishlist>

    <PRODUCT_TYPE_bundle>
        <reference name="product.composite.fieldset">
            <block type="bundle/adminhtml_catalog_product_composite_fieldset_bundle" before="product.composite.fieldset.options" name="product.composite.fieldset.bundle" template="bundle/product/composite/fieldset/options/bundle.phtml">
                <action method="addRenderer"><type>select</type><block>bundle/adminhtml_catalog_product_composite_fieldset_options_type_select</block></action>
                <action method="addRenderer"><type>multi</type><block>bundle/adminhtml_catalog_product_composite_fieldset_options_type_multi</block></action>
                <action method="addRenderer"><type>radio</type><block>bundle/adminhtml_catalog_product_composite_fieldset_options_type_radio</block></action>
                <action method="addRenderer"><type>checkbox</type><block>bundle/adminhtml_catalog_product_composite_fieldset_options_type_checkbox</block></action>
            </block>
        </reference>
    </PRODUCT_TYPE_bundle>
</layout>
