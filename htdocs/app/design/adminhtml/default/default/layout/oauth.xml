<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<layout>
    <!-- Consumers -->
    <adminhtml_oauth_consumer_index>
        <reference name="content">
            <block type="oauth/adminhtml_oauth_consumer" name="oauth_consumer"/>
        </reference>
    </adminhtml_oauth_consumer_index>

    <adminhtml_oauth_consumer_grid>
        <remove name="root"/>
        <block type="oauth/adminhtml_oauth_consumer_grid" name="oauth_consumer.grid" output="toHtml"/>
    </adminhtml_oauth_consumer_grid>

    <adminhtml_oauth_consumer_new>
        <reference name="content">
            <block type="oauth/adminhtml_oauth_consumer_edit" name="oauth_consumer.edit">
                <block type="oauth/adminhtml_oauth_consumer_edit_form" name="form"/>
            </block>
        </reference>
    </adminhtml_oauth_consumer_new>

    <adminhtml_oauth_consumer_edit>
        <reference name="content">
            <block type="oauth/adminhtml_oauth_consumer_edit" name="oauth_consumer.edit">
                <block type="oauth/adminhtml_oauth_consumer_edit_form" name="form"/>
            </block>
        </reference>
    </adminhtml_oauth_consumer_edit>
    <!-- EOF Consumers -->

    <!-- My Applications -->
    <adminhtml_oauth_admin_token_index>
        <reference name="content">
            <block type="oauth/adminhtml_oauth_admin_token" name="oauth_admin_token"/>
        </reference>
    </adminhtml_oauth_admin_token_index>

    <adminhtml_oauth_admin_token_grid>
        <remove name="root"/>
        <block type="oauth/adminhtml_oauth_admin_token_grid" name="oauth_admin_token.grid" output="toHtml"/>
    </adminhtml_oauth_admin_token_grid>
    <!-- EOF My Applications -->



    <oauth_root_handle>
        <reference name="root">
            <action method="setTemplate">
                <template>empty.phtml</template>
            </action>
        </reference>
        <reference name="head">
            <action method="removeItem"><type>js</type><name>scriptaculous/controls.js</name></action>
            <action method="removeItem"><type>js</type><name>prototype/window.js</name></action>
            <action method="removeItem"><type>js</type><name>scriptaculous/builder.js</name></action>
            <action method="removeItem"><type>js</type><name>scriptaculous/dragdrop.js</name></action>
            <action method="removeItem"><type>js</type><name>scriptaculous/controls.js</name></action>
            <action method="removeItem"><type>js</type><name>scriptaculous/slider.js</name></action>
            <action method="removeItem"><type>js</type><name>lib/ccard.js</name></action>
            <action method="removeItem"><type>js</type><name>varien/js.js</name></action>
            <action method="removeItem"><type>js</type><name>mage/adminhtml/hash.js</name></action>
            <action method="removeItem"><type>js</type><name>mage/adminhtml/events.js</name></action>
            <action method="removeItem"><type>js</type><name>mage/adminhtml/loader.js</name></action>
            <action method="removeItem"><type>js</type><name>mage/adminhtml/grid.js</name></action>
            <action method="removeItem"><type>js</type><name>mage/adminhtml/tabs.js</name></action>
            <action method="removeItem"><type>js</type><name>mage/adminhtml/accordion.js</name></action>
<!--            <action method="removeItem"><type>js</type><name>mage/adminhtml/tools.js</name></action>-->
            <action method="removeItem"><type>js</type><name>mage/adminhtml/uploader.js</name></action>
            <action method="removeItem"><type>js</type><name>mage/adminhtml/product.js</name></action>
            <action method="removeItem"><type>js</type><name>mage/adminhtml/rules.js</name></action>
            <action method="removeItem"><type>js</type><name>mage/adminhtml/wysiwyg/tiny_mce/setup.js</name></action>
            <action method="removeItem"><type>js</type><name>lib/ds-sleight.js</name></action>
            <action method="removeItem"><type>js</type><name>varien/iehover-fix.js</name></action>

            <action method="removeItem"><type>css</type><name>print.css</name></action>
            <action method="removeItem"><type>js_css</type><name>calendar/calendar-win2k-1.css</name></action>
            <action method="removeItem"><type>js_css</type><name>extjs/resources/css/ext-all.css</name></action>
            <action method="removeItem"><type>js_css</type><name>extjs/resources/css/ytheme-magento.css</name></action>
            <action method="removeItem"><type>skin_css</type><name>menu.css</name></action>

            <action method="removeItem"><type>js</type><name>calendar/calendar.js</name></action>
            <action method="removeItem"><type>js</type><name>calendar/calendar-setup.js</name></action>
            <action method="removeItem"><type>js</type><name>extjs/fix-defer-before.js</name></action>
            <action method="removeItem"><type>js</type><name>extjs/ext-tree.js</name></action>
            <action method="removeItem"><type>js</type><name>extjs/fix-defer.js</name></action>
            <action method="removeItem"><type>js</type><name>extjs/ext-tree-checkbox.js</name></action>
        </reference>
    </oauth_root_handle>

    <oauth_root_handle_simple>
        <remove name="js_cookie" />
        <block type="adminhtml/page" name="root" output="toHtml" template="empty.phtml">
            <block type="adminhtml/page_head" name="head" as="head" template="oauth/authorize/head-simple.phtml">
                <action method="addCss"><name>oauth-simple.css</name></action>
            </block>
            <block type="core/text_list" name="content"/>
        </block>
    </oauth_root_handle_simple>

    <!-- Authorize -->
    <!--Handle for simple pages-->
    <adminhtml_oauth_authorize_index translate="label">
        <update handle="oauth_root_handle"/>
        <label>OAuth authorization for admin</label>
        <reference name="content">
            <block type="oauth/adminhtml_oauth_authorize" name="oauth.authorize.form" template="oauth/authorize/form/login.phtml"/>
            <block type="oauth/adminhtml_oauth_authorize_button" name="oauth.authorize.button" template="oauth/authorize/button.phtml" />
        </reference>
    </adminhtml_oauth_authorize_index>

    <adminhtml_oauth_authorize_simple translate="label">
        <update handle="oauth_root_handle_simple"/>
        <label>OAuth authorization simple for admin</label>
        <reference name="content">
            <block type="oauth/adminhtml_oauth_authorize" name="oauth.authorize.form" template="oauth/authorize/form/login-simple.phtml"/>
            <block type="oauth/adminhtml_oauth_authorize_button" name="oauth.authorize.button" template="oauth/authorize/button-simple.phtml" />
        </reference>
    </adminhtml_oauth_authorize_simple>
    <!-- EOF Authorize -->

    <!-- Confirm Authorization -->
    <adminhtml_oauth_authorize_confirm translate="label">
        <update handle="oauth_root_handle"/>
        <label>Confirm token authorization for admin</label>
        <reference name="content">
            <block type="core/template" name="oauth.authorize.confirm" template="oauth/authorize/confirm.phtml" />
        </reference>
    </adminhtml_oauth_authorize_confirm>

    <adminhtml_oauth_authorize_confirmsimple translate="label">
        <update handle="oauth_root_handle_simple"/>
        <label>Confirm token authorization Pop Up for admin</label>
        <reference name="content">
            <block type="core/template" name="oauth.authorize.confirm" template="oauth/authorize/confirm-simple.phtml" />
        </reference>
    </adminhtml_oauth_authorize_confirmsimple>
    <!-- EOF Confirm Authorization -->

    <!-- Reject Authorization -->
    <adminhtml_oauth_authorize_reject translate="label">
        <update handle="oauth_root_handle"/>
        <label>Reject token authorization for admin</label>
        <reference name="content">
            <block type="core/template" name="oauth.authorize.reject" template="oauth/authorize/reject.phtml" />
        </reference>
    </adminhtml_oauth_authorize_reject>

    <adminhtml_oauth_authorize_rejectsimple translate="label">
        <update handle="oauth_root_handle_simple"/>
        <label>Reject token authorization Pop Up for admin</label>
        <reference name="content">
            <block type="core/template" name="oauth.authorize.reject" template="oauth/authorize/reject-simple.phtml" />
        </reference>
    </adminhtml_oauth_authorize_rejectsimple>
    <!-- EOF Reject Authorization -->

    <adminhtml_oauth_authorizedtokens_index>
        <reference name="content">
            <block type="oauth/adminhtml_oauth_authorizedTokens" name="oauth_authorizedTokens"/>
        </reference>
    </adminhtml_oauth_authorizedtokens_index>

    <adminhtml_oauth_authorizedtokens_grid>
        <remove name="root"/>
        <block type="oauth/adminhtml_oauth_authorizedTokens_grid" name="oauth_authorizedTokens.grid" output="toHtml"/>
    </adminhtml_oauth_authorizedtokens_grid>
</layout>
