<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<?php
/**
 * Template for displaying product price in different places (products grid, product view page etc)
 *
 * @see Mage_Adminhtml_Block_Catalog_Product_Price
 */
?>
<?php
    /** @var $_coreHelper Mage_Core_Helper_Data */
    $_coreHelper        = $this->helper('core');
    /** @var $_weeeHelper Mage_Weee_Helper_Data */
    $_weeeHelper        = $this->helper('weee');
    /** @var $_taxHelper Mage_Tax_Helper_Data */
    $_taxHelper         = $this->helper('tax');

    $_product           = $this->getProduct();
    $_id                = $_product->getId();
    $_storeId           = $_product->getStoreId();
    $_website           = Mage::app()->getStore($_storeId)->getWebsite();

    $_weeeSeparator     = '';
    $_simplePricesTax   = ($_taxHelper->displayPriceIncludingTax() || $_taxHelper->displayBothPrices());
    $_minimalPriceValue = $_product->getMinimalPrice();
    $_minimalPrice      = $_taxHelper->getPrice($_product, $_minimalPriceValue, $_simplePricesTax);
?>


<?php
$_exclTax = $_taxHelper->getPrice($_product, $_minimalPriceValue, $includingTax = null);
$_inclTax = $_taxHelper->getPrice($_product, $_minimalPriceValue, $includingTax = true);
?>
<?php $_weeeTaxAmount = $_weeeHelper->getAmount($_product, null, null, $_website); ?>
<?php if ($_weeeHelper->typeOfDisplay($_product, array(1,2,4))): ?>
    <?php $_weeeTaxAmount = $_weeeHelper->getAmount($_product, null, null, $_website); ?>
    <?php $_weeeTaxAttributes = $_weeeHelper->getProductWeeeAttributesForRenderer($_product, null, null, $_website); ?>
<?php endif; ?>

<div class="price-box">
<?php $_price = $_taxHelper->getPrice($_product, $_product->getPrice()) ?>
<?php $_regularPrice = $_taxHelper->getPrice($_product, $_product->getPrice(), $_simplePricesTax) ?>
<?php $_finalPrice = $_taxHelper->getPrice($_product, $_product->getFinalPrice()) ?>
<?php $_finalPriceInclTax = $_taxHelper->getPrice($_product, $_product->getFinalPrice(), true) ?>
<?php $_weeeDisplayType = $_weeeHelper->getPriceDisplayType(); ?>
<?php if ($_finalPrice == $_price): ?>
    <?php if ($_taxHelper->displayBothPrices()): ?>
        <?php if ($_weeeTaxAmount && $_weeeHelper->typeOfDisplay($_product, 0)): // including ?>
            <span class="price-excluding-tax">
                <span class="label"><?php echo $this->helper('tax')->__('Excl. Tax:') ?></span>
                <span class="price" id="price-excluding-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->currencyByStore($_price+$_weeeTaxAmount, $_storeId, true, false) ?>
                </span>
            </span>
            <span class="price-including-tax">
                <span class="label"><?php echo $this->helper('tax')->__('Incl. Tax:') ?></span>
                <span class="price" id="price-including-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->currencyByStore($_finalPriceInclTax+$_weeeTaxAmount,true,false) ?>
                </span>
            </span>
        <?php elseif ($_weeeTaxAmount && $_weeeHelper->typeOfDisplay($_product, 1)): // incl. + weee ?>
            <span class="price-excluding-tax">
                <span class="label"><?php echo $this->helper('tax')->__('Excl. Tax:') ?></span>
                <span class="price" id="price-excluding-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->currencyByStore($_price+$_weeeTaxAmount, $_storeId, true, false) ?>
                </span>
            </span>
            <span class="price-including-tax">
                <span class="label"><?php echo $this->helper('tax')->__('Incl. Tax:') ?></span>
                <span class="price" id="price-including-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->currencyByStore($_finalPriceInclTax+$_weeeTaxAmount, $_storeId, true, false) ?>
                </span>
                <span class="weee">(
                    <?php foreach ($_weeeTaxAttributes as $_weeeTaxAttribute): ?>
                        <?php echo $_weeeSeparator; ?>
                        <?php echo $_weeeTaxAttribute->getName(); ?>: <?php echo $_coreHelper->currencyByStore($_weeeTaxAttribute->getAmount(), $_storeId, true, true); ?>
                        <?php $_weeeSeparator = ' + '; ?>
                    <?php endforeach; ?>
                    )</span>
            </span>
        <?php elseif ($_weeeTaxAmount && $_weeeHelper->typeOfDisplay($_product, 4)): // incl. + weee ?>
            <span class="price-excluding-tax">
                <span class="label"><?php echo $this->helper('tax')->__('Excl. Tax:') ?></span>
                <span class="price" id="price-excluding-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->currencyByStore($_price+$_weeeTaxAmount, $_storeId, true, false) ?>
                </span>
            </span>
            <span class="price-including-tax">
                <span class="label"><?php echo $this->helper('tax')->__('Incl. Tax:') ?></span>
                <span class="price" id="price-including-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->currencyByStore($_finalPriceInclTax+$_weeeTaxAmount, $_storeId, true, false) ?>
                </span>
                <span class="weee">(
                    <?php foreach ($_weeeTaxAttributes as $_weeeTaxAttribute): ?>
                        <?php echo $_weeeSeparator; ?>
                        <?php echo $_weeeTaxAttribute->getName(); ?>: <?php echo $_coreHelper->currencyByStore($_weeeTaxAttribute->getAmount()+$_weeeTaxAttribute->getTaxAmount(), $_storeId, true, true); ?>
                        <?php $_weeeSeparator = ' + '; ?>
                    <?php endforeach; ?>
                    )</span>
            </span>
        <?php elseif ($_weeeTaxAmount && $_weeeHelper->typeOfDisplay($_product, 2)): // excl. + weee + final ?>
            <span class="price-excluding-tax">
                <span class="label"><?php echo $this->helper('tax')->__('Excl. Tax:') ?></span>
                <span class="price" id="price-excluding-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->currencyByStore($_price, $_storeId, true, false) ?>
                </span>
            </span>
            <?php foreach ($_weeeTaxAttributes as $_weeeTaxAttribute): ?>
                <span class="weee">
                    <?php echo $_weeeTaxAttribute->getName(); ?>: <?php echo $_coreHelper->currencyByStore($_weeeTaxAttribute->getAmount(), $_storeId, true, true); ?>
                </span>
            <?php endforeach; ?>
            <span class="price-including-tax">
                <span class="label"><?php echo $this->helper('tax')->__('Incl. Tax:') ?></span>
                <span class="price" id="price-including-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->currencyByStore($_finalPriceInclTax+$_weeeTaxAmount, $_storeId, true, false) ?>
                </span>
            </span>
        <?php else: ?>
            <span class="price-excluding-tax">
                <span class="label"><?php echo $this->helper('tax')->__('Excl. Tax:') ?></span>
                <span class="price" id="price-excluding-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->currencyByStore($_price, $_storeId, true, false) ?>
                </span>
            </span>
            <span class="price-including-tax">
                <span class="label"><?php echo $this->helper('tax')->__('Incl. Tax:') ?></span>
                <span class="price" id="price-including-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->currencyByStore($_finalPriceInclTax, $_storeId, true, false) ?>
                </span>
            </span>
        <?php endif; ?>
    <?php else: ?>
        <?php if ($_weeeTaxAmount && $_weeeHelper->typeOfDisplay($_product, 0)): // including ?>
            <span class="regular-price" id="product-price-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                <?php echo $_coreHelper->currencyByStore($_price+$_weeeTaxAmount, $_storeId, true, true) ?>
            </span>
        <?php elseif ($_weeeTaxAmount && $_weeeHelper->typeOfDisplay($_product, 1)): // incl. + weee ?>
            <span class="regular-price" id="product-price-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                <?php echo $_coreHelper->currencyByStore($_price+$_weeeTaxAmount, $_storeId, true, true) ?>
            </span>
            <span class="weee">(
                <?php foreach ($_weeeTaxAttributes as $_weeeTaxAttribute): ?>
                    <?php echo $_weeeSeparator; ?>
                    <?php echo $_weeeTaxAttribute->getName(); ?>: <?php echo $_coreHelper->currencyByStore($_weeeTaxAttribute->getAmount(), $_storeId, true, true); ?>
                    <?php $_weeeSeparator = ' + '; ?>
                <?php endforeach; ?>
                )</span>
        <?php elseif ($_weeeTaxAmount && $_weeeHelper->typeOfDisplay($_product, 4)): // incl. + weee ?>
            <span class="regular-price" id="product-price-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                <?php echo $_coreHelper->currencyByStore($_price+$_weeeTaxAmount, $_storeId, true, true) ?>
            </span>
            <span class="weee">(
                <?php foreach ($_weeeTaxAttributes as $_weeeTaxAttribute): ?>
                    <?php echo $_weeeSeparator; ?>
                    <?php echo $_weeeTaxAttribute->getName(); ?>: <?php echo $_coreHelper->currencyByStore($_weeeTaxAttribute->getAmount()+$_weeeTaxAttribute->getTaxAmount(), $_storeId, true, true); ?>
                    <?php $_weeeSeparator = ' + '; ?>
                <?php endforeach; ?>
                )</span>
        <?php elseif ($_weeeTaxAmount && $_weeeHelper->typeOfDisplay($_product, 2)): // excl. + weee + final ?>
            <span class="regular-price"><?php echo $_coreHelper->currencyByStore($_price, $_storeId, true, true) ?></span><br />
            <?php foreach ($_weeeTaxAttributes as $_weeeTaxAttribute): ?>
                <span class="weee">
                    <?php echo $_weeeTaxAttribute->getName(); ?>: <?php echo $_coreHelper->currencyByStore($_weeeTaxAttribute->getAmount(), $_storeId, true, true); ?>
                </span>
            <?php endforeach; ?>
            <span class="regular-price" id="product-price-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                <?php echo $_coreHelper->currencyByStore($_price+$_weeeTaxAmount, $_storeId, true, true) ?>
            </span>
        <?php else: ?>
            <span class="regular-price" id="product-price-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                <?php echo $_coreHelper->currencyByStore($_price, $_storeId, true, true) ?>
            </span>
        <?php endif; ?>
    <?php endif; ?>
<?php else: /* if ($_finalPrice == $_price): */ ?>
    <?php $_originalWeeeTaxAmount = $_weeeHelper->getOriginalAmount($_product); ?>

    <?php if ($_weeeTaxAmount && $_weeeHelper->typeOfDisplay($_product, 0)): // including ?>
        <p class="old-price">
            <span class="price-label"><?php echo $this->__('Regular Price:') ?></span>
            <span class="price" id="old-price-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                <?php echo $_coreHelper->currencyByStore($_regularPrice+$_originalWeeeTaxAmount, $_storeId, true, false) ?>
            </span>
        </p>

        <?php if ($_taxHelper->displayBothPrices()): ?>
            <p class="special-price">
                <span class="price-label"><?php echo $this->__('Special Price:') ?></span>
                <span class="price-excluding-tax">
                    <span class="label"><?php echo $this->helper('tax')->__('Excl. Tax:') ?></span>
                    <span class="price" id="price-excluding-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                        <?php echo $_coreHelper->currencyByStore($_finalPrice+$_weeeTaxAmount, $_storeId, true, false) ?>
                    </span>
                </span>
            <span class="price-including-tax">
                <span class="label"><?php echo $this->helper('tax')->__('Incl. Tax:') ?></span>
                <span class="price" id="price-including-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->currencyByStore($_finalPriceInclTax+$_weeeTaxAmount, $_storeId, true, false) ?>
                </span>
            </span>
            </p>
        <?php else: ?>
        <p class="special-price">
            <span class="price-label"><?php echo $this->__('Special Price:') ?></span>
            <span class="price" id="product-price-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                <?php echo $_coreHelper->currencyByStore($_finalPrice+$_weeeTaxAmount, $_storeId, true, false) ?>
            </span>
        </p>
        <?php endif; ?>

    <?php elseif ($_weeeTaxAmount && $_weeeHelper->typeOfDisplay($_product, 1)): // incl. + weee ?>
        <p class="old-price">
            <span class="price-label"><?php echo $this->__('Regular Price:') ?></span>
            <span class="price" id="old-price-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                <?php echo $_coreHelper->currencyByStore($_regularPrice+$_originalWeeeTaxAmount, $_storeId, true, false) ?>
            </span>
        </p>

        <p class="special-price">
            <span class="price-label"><?php echo $this->__('Special Price:') ?></span>
            <span class="price-excluding-tax">
                <span class="label"><?php echo $this->helper('tax')->__('Excl. Tax:') ?></span>
                <span class="price" id="price-excluding-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->currencyByStore($_finalPrice+$_weeeTaxAmount, $_storeId, true, false) ?>
                </span>
            </span>
        <span class="weee">(
            <?php foreach ($_weeeTaxAttributes as $_weeeTaxAttribute): ?>
                <?php echo $_weeeSeparator; ?>
                <?php echo $_weeeTaxAttribute->getName(); ?>: <?php echo $_coreHelper->currencyByStore($_weeeTaxAttribute->getAmount(), $_storeId, true, true); ?>
                <?php $_weeeSeparator = ' + '; ?>
            <?php endforeach; ?>
            )</span>
        <span class="price-including-tax">
            <span class="label"><?php echo $this->helper('tax')->__('Incl. Tax:') ?></span>
            <span class="price" id="price-including-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                <?php echo $_coreHelper->currencyByStore($_finalPriceInclTax+$_weeeTaxAmount, $_storeId, true, false) ?>
            </span>
        </span>
        </p>
    <?php elseif ($_weeeTaxAmount && $_weeeHelper->typeOfDisplay($_product, 4)): // incl. + weee ?>
        <p class="old-price">
            <span class="price-label"><?php echo $this->__('Regular Price:') ?></span>
            <span class="price" id="old-price-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                <?php echo $_coreHelper->currencyByStore($_regularPrice+$_originalWeeeTaxAmount, $_storeId, true, false) ?>
            </span>
        </p>

        <p class="special-price">
            <span class="price-label"><?php echo $this->__('Special Price:') ?></span>
            <span class="price-excluding-tax">
                <span class="label"><?php echo $this->helper('tax')->__('Excl. Tax:') ?></span>
                <span class="price" id="price-excluding-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->currencyByStore($_finalPrice+$_weeeTaxAmount, $_storeId, true, false) ?>
                </span>
            </span>
        <span class="weee">(
            <?php foreach ($_weeeTaxAttributes as $_weeeTaxAttribute): ?>
                <?php echo $_weeeSeparator; ?>
                <?php echo $_weeeTaxAttribute->getName(); ?>: <?php echo $_coreHelper->currencyByStore($_weeeTaxAttribute->getAmount()+$_weeeTaxAttribute->getTaxAmount(), $_storeId, true, true); ?>
                <?php $_weeeSeparator = ' + '; ?>
            <?php endforeach; ?>
            )</span>
        <span class="price-including-tax">
            <span class="label"><?php echo $this->helper('tax')->__('Incl. Tax:') ?></span>
            <span class="price" id="price-including-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                <?php echo $_coreHelper->currencyByStore($_finalPriceInclTax+$_weeeTaxAmount, $_storeId, true, false) ?>
            </span>
        </span>
        </p>
    <?php elseif ($_weeeTaxAmount && $_weeeHelper->typeOfDisplay($_product, 2)): // excl. + weee + final ?>
        <p class="old-price">
            <span class="price-label"><?php echo $this->__('Regular Price:') ?></span>
            <span class="price" id="old-price-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                <?php echo $_coreHelper->currencyByStore($_regularPrice, $_storeId, true, false) ?>
            </span>
        </p>

        <p class="special-price">
            <span class="price-label"><?php echo $this->__('Special Price:') ?></span>
            <span class="price-excluding-tax">
                <span class="label"><?php echo $this->helper('tax')->__('Excl. Tax:') ?></span>
                <span class="price" id="price-excluding-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->currencyByStore($_finalPrice, $_storeId, true, false) ?>
                </span>
            </span>
            <?php foreach ($_weeeTaxAttributes as $_weeeTaxAttribute): ?>
                <span class="weee">
                    <?php echo $_weeeTaxAttribute->getName(); ?>: <?php echo $_coreHelper->currencyByStore($_weeeTaxAttribute->getAmount(), $_storeId, true, true); ?>
                </span>
            <?php endforeach; ?>
            <span class="price-including-tax">
                <span class="label"><?php echo $this->helper('tax')->__('Incl. Tax:') ?></span>
                <span class="price" id="price-including-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                    <?php echo $_coreHelper->currencyByStore($_finalPriceInclTax+$_weeeTaxAmount, $_storeId, true, false) ?>
                </span>
            </span>
        </p>
    <?php else: // excl. ?>
        <p class="old-price">
            <span class="price-label"><?php echo $this->__('Regular Price:') ?></span>
            <span class="price" id="old-price-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                <?php echo $_coreHelper->currencyByStore($_regularPrice, $_storeId, true, false) ?>
            </span>
        </p>

        <?php if ($_taxHelper->displayBothPrices()): ?>
            <p class="special-price">
                <span class="price-label"><?php echo $this->__('Special Price:') ?></span>
                <span class="price-excluding-tax">
                    <span class="label"><?php echo $this->helper('tax')->__('Excl. Tax:') ?></span>
                    <span class="price" id="price-excluding-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                        <?php echo $_coreHelper->currencyByStore($_finalPrice, $_storeId, true, false) ?>
                    </span>
                </span>
                <span class="price-including-tax">
                    <span class="label"><?php echo $this->helper('tax')->__('Incl. Tax:') ?></span>
                    <span class="price" id="price-including-tax-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                        <?php echo $_coreHelper->currencyByStore($_finalPriceInclTax, $_storeId, true, false) ?>
                    </span>
                </span>
            </p>
        <?php else: ?>
        <p class="special-price">
            <span class="price-label"><?php echo $this->__('Special Price:') ?></span>
            <span class="price" id="product-price-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
                <?php echo $_coreHelper->currencyByStore($_finalPrice, $_storeId, true, false) ?>
            </span>
        </p>
        <?php endif; ?>
    <?php endif; ?>

<?php endif; /* if ($_finalPrice == $_price): */ ?>

<?php if ($this->getDisplayMinimalPrice() && $_minimalPriceValue && $_minimalPriceValue < $_product->getFinalPrice()): ?>

    <?php $_minimalPriceDisplayValue = $_minimalPrice; ?>
    <?php if ($_weeeTaxAmount && $_weeeHelper->typeOfDisplay($_product, array(0, 1, 4))): ?>
        <?php $_minimalPriceDisplayValue = $_minimalPrice+$_weeeTaxAmount; ?>
    <?php endif; ?>

    <?php if ($this->getUseLinkForAsLowAs()):?>
    <a href="<?php echo $_product->getProductUrl(); ?>" class="minimal-price-link">
    <?php else:?>
    <span class="minimal-price-link">
    <?php endif?>
        <span class="label"><?php echo $this->__('As low as:') ?></span>
        <span class="price" id="product-minimal-price-<?php echo $_id ?><?php echo $this->getIdSuffix() ?>">
            <?php echo $_coreHelper->currencyByStore($_minimalPriceDisplayValue, $_storeId, true, false) ?>
        </span>
    <?php if ($this->getUseLinkForAsLowAs()):?>
    </a>
    <?php else:?>
    </span>
    <?php endif?>
<?php endif; /* if ($this->getDisplayMinimalPrice() && $_minimalPrice && $_minimalPrice < $_finalPrice): */ ?>
</div>
