<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<div class="grid np">
  <div class="hor-scroll">
    <table cellspacing="0" class="data order-tables">
        <col />
        <col width="1" />
        <col width="1" />
        <col width="1" />
        <col width="1" />
        <col width="1" />
        <col width="1" />
        <col width="1" />
        <thead>
            <tr class="headings">
                <th><?php echo Mage::helper('sales')->__('Product') ?></th>
                <th><?php echo Mage::helper('sales')->__('Price') ?></th>
                <th class="a-center"><?php echo Mage::helper('sales')->__('Qty') ?></th>
                <th><span class="nobr"><?php echo Mage::helper('sales')->__('Qty to Invoice') ?></span></th>
                <th><?php echo Mage::helper('sales')->__('Subtotal') ?></th>
                <th><span class="nobr"><?php echo Mage::helper('sales')->__('Tax Amount') ?></span></th>
                <th><span class="nobr"><?php echo Mage::helper('sales')->__('Discount Amount') ?></span></th>
                <th class="last"><span class="nobr"><?php echo Mage::helper('sales')->__('Row Total') ?></span></th>
            </tr>
        </thead>
        <?php if ($this->canEditQty()): ?>
        <tfoot>
            <tr>
                <td colspan="2">&nbsp;</td>
                <td colspan="3" class="a-center"><?php echo $this->getUpdateButtonHtml() ?></td>
                <td colspan="3">&nbsp;</td>
            </tr>
        </tfoot>
        <?php endif; ?>
        <?php $_items = $this->getInvoice()->getAllItems() ?>
        <?php $_i=0;foreach ($_items as $_item): ?>
        <?php if ($_item->getOrderItem()->getParentItem()) continue; else $_i++; ?>
        <tbody class="<?php echo $_i%2?'even':'odd' ?>">
            <?php echo $this->getItemHtml($_item) ?>
            <?php echo $this->getItemExtraInfoHtml($_item->getOrderItem()) ?>
        </tbody>
        <?php endforeach; ?>
    </table>
  </div>
</div>
<br />
<div class="clear"></div>
<?php echo $this->getChildHtml('order_totalbar') ?>
<div class="clear"></div>

<div class="box-left entry-edit">
    <div class="entry-edit-head"><h4><?php echo $this->__('Invoice History') ?></h4></div>
    <fieldset>
        <div id="history_form" class="order-history-form">
            <span class="field-row">
                <label class="normal" for="invoice_comment_text"><?php echo Mage::helper('sales')->__('Invoice Comments') ?></label>
                <textarea id="invoice_comment_text" name="invoice[comment_text]" rows="3" cols="5" style="height:10em; width:98%;"><?php echo $this->getInvoice()->getCommentText(); ?></textarea>
            </span>
            <div class="clear"></div>
        </div>
    </fieldset>
</div>

<div class="box-right entry-edit" id="invoice_totals">
    <div class="entry-edit-head"><h4><?php echo $this->__('Invoice Totals') ?></h4></div>
    <div class="order-totals">
        <?php echo $this->getChildHtml('invoice_totals') ?>
        <div class="order-totals-bottom">
          <div class="divider"></div>
          <?php if ($this->isCaptureAllowed()): ?>
            <?php if ($this->canCapture()):?>
              <p>
              <!--
                  <label for="invoice_do_capture" class="normal"><?php echo Mage::helper('sales')->__('Capture Amount') ?></label>
                  <input type="checkbox" name="invoice[do_capture]" id="invoice_do_capture" value="1" checked/>
              -->
                  <label for="invoice_do_capture" class="normal"><?php echo Mage::helper('sales')->__('Amount') ?></label>
                  <select name="invoice[capture_case]">
                      <option value="online"><?php echo Mage::helper('sales')->__('Capture Online') ?></option>
                      <option value="offline"><?php echo Mage::helper('sales')->__('Capture Offline') ?></option>
                      <option value="not_capture"><?php echo Mage::helper('sales')->__('Not Capture') ?></option>
                  </select>
              </p>
            <?php elseif ($this->isGatewayUsed()):?>
                <input type="hidden" name="invoice[capture_case]" value="offline"/>
                <p><?php echo Mage::helper('sales')->__('Invoice will be created without communication with payment gateway.') ?></p>
            <?php endif?>
          <?php endif; ?>
          <p>
              <label class="normal" for="notify_customer"><?php echo Mage::helper('sales')->__('Append Comments') ?></label>
              <input id="notify_customer" name="invoice[comment_customer_notify]" value="1" type="checkbox" />
          </p>
          <?php if ($this->canSendInvoiceEmail()): ?>
          <p>
              <label class="normal" for="send_email"><?php echo Mage::helper('sales')->__('Email Copy of Invoice') ?></label>
              <input id="send_email" name="invoice[send_email]" value="1" type="checkbox" />
          </p>
          <?php endif; ?>
          <div class="a-right">
          <?php echo $this->getChildHtml('submit_button') ?>
          </div>
        </div>
    </div>
</div>
<div class="clear"></div>

<table class="order-info-foot" cellpadding="10" cellspacing="0" width="100%">
    <tbody>
        <tr>
            <td class="section">
                <table id="comments_block" cellpadding="0" width="100%">
                    <tbody>
                        <tr>
                            <td style="padding-right:30px; width:50%;">&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </tbody>
                </table>
            </td>
            <td class="document-totals section">&nbsp;</td>
        </tr>
    </tbody>
</table>

<script type="text/javascript">
//<![CDATA[
var submitButtons = $$('.submit-button');
var updateButtons = $$('.update-button');
var enableSubmitButtons = <?php echo (int) !$this->getDisableSubmitButton() ?>;
var fields = $$('.qty-input');

updateButtons.each(function (elem) {elem.disabled=true;elem.addClassName('disabled');});

for(var i=0;i<fields.length;i++){
    fields[i].observe('change', checkButtonsRelation)
    fields[i].baseValue = fields[i].value;
}

function checkButtonsRelation() {
    var hasChanges = false;
    fields.each(function (elem) {
        if (elem.baseValue != elem.value) {
            hasChanges = true;
        }
    }.bind(this));
    if (hasChanges) {
        submitButtons.each(function (elem) {elem.disabled=true;elem.addClassName('disabled');});
        updateButtons.each(function (elem) {elem.disabled=false;elem.removeClassName('disabled');});
    }
    else {
        if (enableSubmitButtons) {
            submitButtons.each(function (elem) {elem.disabled=false;elem.removeClassName('disabled');});
        }
        updateButtons.each(function (elem) {elem.disabled=true;elem.addClassName('disabled');});
    }
}

var sendEmailCheckbox = $('send_email');
if (sendEmailCheckbox) {
    var notifyCustomerCheckbox = $('notify_customer');
    var invoiceCommentText = $('invoice_comment_text');
    Event.observe(sendEmailCheckbox, 'change', bindSendEmail);
    bindSendEmail();
}
function bindSendEmail()
{
    if (sendEmailCheckbox.checked == true) {
        notifyCustomerCheckbox.disabled = false;
        //invoiceCommentText.disabled = false;
    }
    else {
        notifyCustomerCheckbox.disabled = true;
        //invoiceCommentText.disabled = true;
    }
}
//]]>
</script>
