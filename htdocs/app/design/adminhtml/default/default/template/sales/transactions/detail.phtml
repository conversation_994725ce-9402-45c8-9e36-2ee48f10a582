<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<div class="content-header">
    <table cellspacing="0">
        <tr>
            <td style="<?php echo $this->getHeaderWidth() ?>"><?php echo $this->getHeaderHtml() ?></td>
            <td class="form-buttons"><?php echo $this->getButtonsHtml() ?></td>
        </tr>
    </table>
</div>

<div class="entry-edit">
    <div class="entry-edit">
        <div class="entry-edit-head">
            <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__('Transaction Data'); ?></h4>
        </div>
        <div id="log_details_fieldset" class="log-details">
            <table cellspacing="0" class="log-info table">
                <col width="25%" />
                <col />
                <tbody>
                    <tr>
                        <th><?php echo $this->__('Transaction ID'); ?></th>
                        <td><?php echo $this->getTxnIdHtml() ?></td>
                    </tr>
                    <tr>
                        <th><?php echo $this->__('Parent Transaction ID'); ?></th>
                        <td>
                            <?php if ($this->getParentTxnIdHtml()): ?>
                                <a href="<?php echo $this->getParentTxnIdUrlHtml() ?>">
                                    <?php echo $this->getParentTxnIdHtml(); ?>
                                </a>
                            <?php else :?>
                                <?php echo $this->__('N/A'); ?>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <th><?php echo $this->__('Order ID'); ?></th>
                        <td>
                            <a href="<?php echo $this->getOrderIdUrlHtml(); ?>">
                                <?php echo $this->getOrderIncrementIdHtml() ?>
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <th><?php echo $this->__('Transaction Type'); ?></th>
                        <td><?php echo $this->getTxnTypeHtml() ?></td>
                    </tr>
                    <tr>
                        <th><?php echo $this->__('Is Closed'); ?></th>
                        <td><?php echo $this->getIsClosedHtml(); ?></td>
                    </tr>
                    <tr>
                        <th><?php echo $this->__('Created At'); ?></th>
                        <td><?php echo $this->getCreatedAtHtml(); ?></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="entry-edit">
    <div class="entry-edit">
        <div class="entry-edit-head">
            <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__('Child Transactions'); ?></h4>
        </div>
        <div class="log-details-grid">
            <?php echo $this->getChildHtml('child_grid') ?>
        </div>
    </div>
</div>

<div class="entry-edit">
    <div class="entry-edit">
        <div class="entry-edit-head">
            <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__('Transaction Details'); ?></h4>
        </div>
        <div class="log-details-grid">
            <?php echo $this->getChildHtml('detail_grid') ?>
        </div>
    </div>
</div>
