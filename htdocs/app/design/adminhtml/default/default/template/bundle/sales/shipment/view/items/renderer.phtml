<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * @see Mage_Bundle_Block_Adminhtml_Sales_Order_Items_Renderer
 */
?>

<?php $_item = $this->getItem() ?>
<?php $items = array_merge(array($_item->getOrderItem()), $_item->getOrderItem()->getChildrenItems()) ?>
<?php $shipItems = $this->getChilds($_item) ?>
<?php $_count = count ($items) ?>
<?php $_index = 0 ?>

<?php $_prevOptionId = '' ?>

<?php if($this->getOrderOptions() || $_item->getDescription()): ?>
    <?php $_showlastRow = true ?>
<?php else: ?>
    <?php $_showlastRow = false ?>
<?php endif; ?>

<?php foreach ($items as $_item): ?>
    <?php $this->setPriceDataObject($_item) ?>
    <?php if ($_item->getParentItem()): ?>
        <?php $attributes = $this->getSelectionAttributes($_item) ?>
        <?php if ($_prevOptionId != $attributes['option_id']): ?>
        <tr>
            <td><div class="option-label"><?php echo $this->escapeHtml($attributes['option_label']); ?></div></td>
            <td class="last">&nbsp;</td>
        </tr>
        <?php $_prevOptionId = $attributes['option_id'] ?>
        <?php endif; ?>
    <?php endif; ?>
    <tr<?php echo (++$_index==$_count && !$_showlastRow)?' class="border"':'' ?>>
        <?php if (!$_item->getParentItem()): ?>
        <td>
            <h5 class="title"><?php echo $this->escapeHtml($_item->getName()) ?></h5>
            <div>
                <strong><?php echo $this->helper('sales')->__('SKU') ?>:</strong>
                <?php echo implode('<br />', Mage::helper('catalog')->splitSku($this->escapeHtml($_item->getSku()))); ?>
            </div>
        </td>
        <?php else: ?>
        <td><div class="option-value"><?php echo $this->getValueHtml($_item)?></div></td>
        <?php endif; ?>
        <td class="a-center last">
            <?php if (($this->isShipmentSeparately() && $_item->getParentItem()) || (!$this->isShipmentSeparately() && !$_item->getParentItem())): ?>
                <?php if (isset($shipItems[$_item->getId()])): ?>
                    <?php echo $shipItems[$_item->getId()]->getQty()*1 ?>
                <?php elseif ($_item->getIsVirtual()): ?>
                    <?php echo $this->__('N/A') ?>
                <?php else: ?>
                    0
                <?php endif; ?>
            <?php else: ?>
                &nbsp;
            <?php endif; ?>
        </td>
    </tr>
<?php endforeach; ?>
<?php if($_showlastRow): ?>
    <tr class="border">
        <td>
            <?php if ($this->getOrderOptions($_item->getOrderItem())): ?>
                <dl class="item-options">
                <?php foreach ($this->getOrderOptions($_item->getOrderItem()) as $option): ?>
                    <dt><?php echo $this->escapeHtml($option['label']) ?></dt>
                    <dd>
                    <?php if (isset($option['custom_view']) && $option['custom_view']): ?>
                        <?php echo $option['value'];?>
                    <?php else: ?>
                        <?php echo Mage::helper('core/string')->truncate($option['value'], 55, '', $_remainder);?>
                        <?php if ($_remainder):?>
                            ... <span id="<?php echo $_id = 'id' . uniqid()?>"><?php echo $_remainder ?></span>
                            <script type="text/javascript">
                            $('<?php echo $_id ?>').hide();
                            $('<?php echo $_id ?>').up().observe('mouseover', function(){$('<?php echo $_id ?>').show();});
                            $('<?php echo $_id ?>').up().observe('mouseout',  function(){$('<?php echo $_id ?>').hide();});
                            </script>
                        <?php endif;?>
                    <?php endif;?>
                    </dd>
                <?php endforeach; ?>
                </dl>
            <?php endif; ?>
            <?php echo $this->escapeHtml($_item->getDescription()) ?>
        </td>
        <td class="last">&nbsp;</td>
    </tr>
<?php endif; ?>
