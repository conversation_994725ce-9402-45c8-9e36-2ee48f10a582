<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/** @var $previewModel Mage_XmlConnect_Model_Preview_Iphone */
$previewModel = Mage::helper('xmlconnect')->getPreviewModel();

$categoryItemTintColor = $previewModel->getCategoryItemTintColor();
?>
<link rel="stylesheet" type="text/css" href="<?php echo $previewModel->getPreviewCssUrl('mobile-home.css'); ?>" media="all" />
<div class="main-frame">
<div class="main-block" style="background: <?php echo $previewModel->getData('conf/body/backgroundColor'); ?>;">
    <div class="top-header" style="background:#000;">
        <div class="volume" style="background:url('<?php echo $previewModel->getPreviewImagesUrl('1.gif'); ?>') bottom left no-repeat;"></div>
        <div class="header-sign-1"><?php echo $this->__("Carrier"); ?></div>
        <div class="antenna" style="background:url('<?php echo $previewModel->getPreviewImagesUrl('3.gif'); ?>') bottom right no-repeat;"></div>
        <div class="time"><?php if($previewModel->getData('conf/Time')): ?><?php echo $previewModel->getData('conf/Time'); ?><?php else: ?><?php echo $this->__("10:40 AM"); ?><?php endif; ?></div>
        <div class="battery" style="background:url('<?php echo $previewModel->getPreviewImagesUrl('2.gif'); ?>') bottom right no-repeat;"></div>
    </div>
    <div class="main-header"  style="background:<?php echo $previewModel->getData('conf/navigationBar/tintColor'); ?>;color:#f3f3f3;">
        <div class="gradient">
            <table class="header-buttons">
                <tr>
                    <td class="info"><img src="<?php echo $previewModel->getPreviewImagesUrl('info.png') ?>" alt="" width="20" height="20" /></td>
                    <td class="logo-small"><div>
                            <img src="<?php echo $previewModel->getData('conf/navigationBar/icon') ? $previewModel->getData('conf/navigationBar/icon') : $previewModel->getDesignPreviewImageUrl($previewModel->getInterfaceImagesPaths('conf/navigationBar/icon'));?>" alt="" />
            <span class="sh-title">
                <span class="sh-title1" style="font:bold <?php echo $previewModel->getData('conf/fonts/Title1/size'); ?>px/44px <?php echo $previewModel->getData('conf/fonts/Title1/name'); ?>;">
                <span class="sh-title2" style="color: <?php echo $previewModel->getData('conf/fonts/Title1/color'); ?>;"><?php echo $this->__("Home"); ?></span>
                <?php echo $this->__("Home"); ?>
                </span>
            </span>
                    </div></td>
                    <td class="login-btn">
                        <div class="login-left"></div>
                        <div class="login-body"><span><?php echo $this->__("Log In"); ?></span></div>
                        <div class="login-right"></div>
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <div class="big-logo" style="background:<?php echo $previewModel->getData('conf/body/backgroundColor'); ?>;">
        <img src="<?php echo $previewModel->getData('conf/body/bannerImage') ? $previewModel->getData('conf/body/bannerImage') : $previewModel->getDesignPreviewImageUrl($previewModel->getInterfaceImagesPaths('conf/body/bannerImage')); ?>" />
    </div>
    <div class="catalog" style="background:<?php echo $previewModel->getData('conf/body/scrollBackgroundColor'); ?>">
        <div class="item" style="background:<?php echo $previewModel->getData('conf/categoryItem/backgroundColor'); ?>">
            <img src="<?php echo $previewModel->getPreviewImagesUrl('men.png') ?>" width="80" height="80" alt="" />
            <div class="item-text" style="font:bold <?php echo $previewModel->getData('conf/fonts/Title9/size'); ?>px <?php echo $previewModel->getData('conf/fonts/Title9/name'); ?>;color:<?php echo $previewModel->getData('conf/fonts/Title9/color'); ?>;<?php echo $categoryItemTintColor; ?>"><?php echo $this->__("80x80 px"); ?></div>
        </div>
        <div class="item" style="background:<?php echo $previewModel->getData('conf/categoryItem/backgroundColor'); ?>">
            <img src="<?php echo $previewModel->getPreviewImagesUrl('men.png') ?>" width="80" height="80" alt="" />
            <div class="item-text" style="font:bold <?php echo $previewModel->getData('conf/fonts/Title9/size'); ?>px <?php echo $previewModel->getData('conf/fonts/Title9/name'); ?>;color:<?php echo $previewModel->getData('conf/fonts/Title9/color'); ?>;<?php echo $categoryItemTintColor; ?>"><?php echo $this->__("80x80 px"); ?></div>
        </div>
        <div class="item" style="background:<?php echo $this->getData('conf/categoryItem/backgroundColor'); ?>">
            <img src="<?php echo $this->getPreviewImagesUrl('men.png') ?>" width="80" height="80" alt="" />
            <div class="item-text" style="font:bold <?php echo $this->getData('conf/fonts/Title9/size'); ?>px <?php echo $this->getData('conf/fonts/Title9/name'); ?>;color:<?php echo $this->getData('conf/fonts/Title9/color'); ?>;<?php echo $categoryItemTintColor; ?>"><?php echo $this->__("80x80 px"); ?></div>
        </div>
        <div class="item" style="background:<?php echo $this->getData('conf/categoryItem/backgroundColor'); ?>">
            <img src="<?php echo $this->getPreviewImagesUrl('men.png') ?>" width="80" height="80" alt="" />
            <div class="item-text" style="font:bold <?php echo $this->getData('conf/fonts/Title9/size'); ?>px <?php echo $this->getData('conf/fonts/Title9/name'); ?>;color:<?php echo $this->getData('conf/fonts/Title9/color'); ?>;<?php echo $categoryItemTintColor; ?>"><?php echo $this->__("80x80 px"); ?></div>
        </div>
        <!-- div class="item" style="background:<?php echo $this->getData('conf/categoryItem/backgroundColor'); ?>">
            <img src="<?php echo $this->getPreviewImagesUrl('men.png') ?>" width="80" height="80" alt="" />
            <div class="item-text" style="font:bold <?php echo $this->getData('conf/fonts/Title9/size'); ?>px/18px <?php echo $this->getData('conf/fonts/Title9/name'); ?>;color:<?php echo $this->getData('conf/fonts/Title9/color'); ?>; background:<?php echo $this->getData('conf/categoryItem/tintColor'); ?>"><?php echo $this->__("80px x 80px"); ?></div>
        </div -->
    </div>
    <?php echo $this->getChildHtml('tab_items'); ?>
</div>
</div>
<?php if ($this->getJsErrorMessage()) : ?>
<script type="text/javascript">
    alert('<?php echo $this->getJsErrorMessage(); ?>');
</script>
<?php endif; ?>
