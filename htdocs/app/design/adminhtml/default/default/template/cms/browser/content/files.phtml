<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * Directory contents template for Wysiwyg Images
 *
 * @see Mage_Adminhtml_Block_Cms_Wysiwyg_Images_Content_Files
 */
?>
<?php
$_width  = $this->getImagesWidth();
$_height = $this->getImagesHeight();
?>
<?php if ($this->getFilesCount() > 0): ?>
    <?php foreach ($this->getFiles() as $file): ?>
    <div class="filecnt" id="<?php echo $this->getFileId($file) ?>">
        <p class="nm" style="height:<?php echo $_height ?>px;width:<?php echo $_width ?>px;">
        <?php if($this->getFileThumbUrl($file)):?>
            <img src="<?php echo $this->getFileThumbUrl($file) ?>" alt="<?php echo $this->escapeHtml($this->getFileName($file)) ?>"/>
        <?php endif; ?>
        </p>
        <?php if($this->getFileWidth($file)): ?>
            <small><?php echo $this->getFileWidth($file) ?>x<?php echo $this->getFileHeight($file) ?> <?php echo $this->helper('cms')->__('px.') ?></small><br/>
        <?php endif; ?>
        <small><?php echo $this->getFileShortName($file); ?></small>
    </div>
    <?php endforeach; ?>
<?php else: ?>
    <?php echo $this->helper('cms')->__('No files found') ?>
<?php endif; ?>
