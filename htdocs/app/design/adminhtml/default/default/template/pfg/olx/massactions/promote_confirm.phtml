<?php /** @var $this PFG_Olx_Block_Adminhtml_Catalog_Product_MassAction_PromoteConfirm */ ?>
<div class="container">
    <div class="row">
        <div class="col-12 mx-auto">
            <form method="post" action="<?php echo $this->getPromoteExecuteUrl() ?>">
                <?php $productsFromRequest = $this->getProductsFromRequest(); ?>
                <?php $productsWithAdIdCollection = $this->getProductsWithAdId(); ?>
                <h3>
                    <?php if (count($productsFromRequest) == $productsWithAdIdCollection->count()): ?>
                        <?php echo $this->__('All %d products will be promoted. Are you sure you want to do that?', count($productsFromRequest)) ?>
                    <?php else: ?>
                        <?php echo $this->__('From all %d selected products %d will be promoted, Are you sure you want to do that?', count($productsFromRequest), $productsWithAdIdCollection->count()) ?>
                    <?php endif; ?>
                </h3>
                <?php $balanceData = $this->getUserBalanceData(); ?>
                <?php if (is_array($balanceData) && isset($balanceData['total_balance'])): ?>
                    <div>
                        <p><?php echo $this->__('Your current balance is: %.2f', $balanceData['total_balance']); ?></p>
                    </div>
                <?php endif; ?>
                <div class="form-group">
                    <label for="select_ad_promo_type"><?php echo $this->__('Please select the promo type you want to use for your ads:') ?></label>
                    <select name="promote_type" id="select_ad_promo_type" class="form-control">
                        <?php $promoTypesOptions = Mage::getModel('pfgolx/system_config_source_olxPaidFeatures')->getAllOptions(); ?>
                        <?php
                        $promoTypes = Mage::helper('pfgolx/api_paidFeatures')->getFromOlx();
                        $promoTypesFormatted = [];
                        foreach ($promoTypes as $promoType) {
                            $promoTypesFormatted[$promoType['feature_code']] = $promoType;
                        }
                        ?>
                        <?php foreach ($promoTypesOptions as $promoTypeOption): ?>
                            <option value="<?php echo $promoTypeOption['value'] ?>"><?php echo $promoTypeOption['label'] ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <input type="hidden" name="product" value="<?php echo $this->getProductIdsString() ?>" />
                <input type="hidden" name="form_key" value="<?php echo Mage::getSingleton('core/session')->getFormKey() ?>" />
                <input type="submit" class="btn btn-success" name="confirm" value="Confirm" />
            </form>
        </div>
    </div>
</div>
