<?php /** @var $this PFG_Olx_Block_Adminhtml_Queue_ClearQueueConfirm */ ?>
<div class="container">
    <div class="row">
        <div class="col-12 mx-auto">
            <?php $productsCount = $this->getAllQueueProductsCount(); ?>
            <?php if ($productsCount > 0): ?>
            <h1><?php echo $this->__('All %d entries in the queue will be removed. Are you sure you want to continue?', $productsCount) ?></h1>
            <form method="post" action="<?php echo $this->getConfirmClearQueue() ?>">
                <input type="hidden" name="form_key"
                       value="<?php echo Mage::getSingleton('core/session')->getFormKey() ?>"/>
                <input type="submit" class="btn btn-danger" name="confirm"
                       value="<?php echo $this->__('Confirm clear queue') ?>"/>
            </form>
            <?php else: ?>
            <h1><?php echo $this->__('No products found in queue.') ?></h1>
            <?php endif; ?>
        </div>
    </div>
</div>
