<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php $_order=$this->getOrder() ?>
<table cellspacing="0" cellpadding="0" border="0" width="100%" style="border:1px solid #bebcb7; background:#f8f7f5;">
    <thead>
        <tr>
            <th class="a-left" bgcolor="#d9e5ee" style="padding:3px 9px">Item</th>
            <th class="a-center" bgcolor="#d9e5ee" style="padding:3px 9px">Qty</th>
            <th class="a-right" bgcolor="#d9e5ee" style="padding:3px 9px">Subtotal</th>
        </tr>
    </thead>

    <tbody>
<?php $i=0; foreach ($_order->getAllItems() as $_item): $i++ ?>
        <tr <?php echo $i%2?'bgcolor="#eeeded"':'' ?>>
            <td align="left" valign="top" style="padding:3px 9px"><?php echo $_item->getName() ?></td>
            <td align="center" valign="top" style="padding:3px 9px"><?php echo sprintf('%s', $_item->getQtyOrdered()) ?></td>
            <td align="right" valign="top" style="padding:3px 9px">
            <?php if ($this->helper('tax')->displayCartPriceExclTax() || $this->helper('tax')->displayCartBothPrices()): ?>
                <?php if ($this->helper('tax')->displayCartBothPrices()): ?>
                    <span class="label"><?php echo $this->__('Excl. Tax'); ?>:</span>
                <?php endif; ?>
                <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'email', $_order->getStore())): ?>
                    <?php echo $_order->formatPrice($_item->getRowTotal()+$_item->getWeeeTaxAppliedRowAmount()+$_item->getWeeeTaxRowDisposition()); ?>
                <?php else: ?>
                    <?php echo $_order->formatPrice($_item->getRowTotal()) ?>
                <?php endif; ?>

                <?php if (Mage::helper('weee')->getApplied($_item)): ?>
                    <br />
                    <?php if (Mage::helper('weee')->typeOfDisplay($_item, 1, 'email', $_order->getStore())): ?>
                        <small>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $_order->formatPrice($tax['row_amount'],true,true); ?></span><br />
                        <?php endforeach; ?>
                        </small>
                    <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 2, 'email', $_order->getStore())): ?>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="nobr"><small><?php echo $tax['title']; ?>: <?php echo $_order->formatPrice($tax['row_amount'],true,true); ?></small></span><br />
                        <?php endforeach; ?>
                    <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 4, 'email', $_order->getStore())): ?>
                        <small>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $_order->formatPrice($tax['row_amount'],true,true); ?></span><br />
                        <?php endforeach; ?>
                        </small>
                    <?php endif; ?>

                    <?php if (Mage::helper('weee')->typeOfDisplay($_item, 2, 'email', $_order->getStore())): ?>
                        <br />
                        <span class="nobr"><?php echo Mage::helper('weee')->__('Total'); ?>:<br /> <?php echo $_order->formatPrice($_item->getCalculationPrice()+$_item->getWeeeTaxAppliedRowAmount()+$_item->getWeeeTaxRowDisposition()); ?></span>
                    <?php endif; ?>
                <?php endif; ?>
            <?php endif; ?>


            <?php if ($this->helper('tax')->displayCartPriceInclTax() || $this->helper('tax')->displayCartBothPrices()): ?>
                <?php if ($this->helper('tax')->displayCartBothPrices()): ?>
                    <br /><span class="label"><?php echo $this->__('Incl. Tax'); ?>:</span>
                <?php endif; ?>
                <?php $_incl = $this->helper('checkout')->getSubtotalInclTax($_item); ?>
                    <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'email', $_order->getStore())): ?>
                        <?php echo $_order->formatPrice($_incl + Mage::helper('weee')->getRowWeeeTaxInclTax($_item)); ?>
                    <?php else: ?>
                        <?php echo $_order->formatPrice($_incl-$_item->getWeeeTaxRowDisposition()) ?>
                    <?php endif; ?>
                <?php if (Mage::helper('weee')->getApplied($_item)): ?>
                    <br />
                    <?php if (Mage::helper('weee')->typeOfDisplay($_item, 1, 'email', $_order->getStore())): ?>
                        <small>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $_order->formatPrice($tax['row_amount_incl_tax'], true, true); ?></span><br />
                        <?php endforeach; ?>
                        </small>
                    <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 2, 'email', $_order->getStore())): ?>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="nobr"><small><?php echo $tax['title']; ?>: <?php echo $_order->formatPrice($tax['row_amount_incl_tax'], true, true); ?></small></span><br />
                        <?php endforeach; ?>
                    <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 4, 'email', $_order->getStore())): ?>
                        <small>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $_order->formatPrice($tax['row_amount_incl_tax'], true, true); ?></span><br />
                        <?php endforeach; ?>
                        </small>
                    <?php endif; ?>
                    <?php if (Mage::helper('weee')->typeOfDisplay($_item, 2, 'email', $_order->getStore())): ?>
                        <span class="nobr"><?php echo Mage::helper('weee')->__('Total incl. tax'); ?>:<br /> <?php echo $_order->formatPrice($_incl + Mage::helper('weee')->getRowWeeeTaxInclTax($_item)); ?></span>
                    <?php endif; ?>
                <?php endif; ?>
            <?php endif; ?>
            </td>
        </tr>
<?php endforeach ?>
    </tbody>

    <tfoot>
        <tr>
            <td colspan="2" align="right" style="padding:3px 9px"><?php echo Mage::helper('sales')->__('Subtotal') ?></td>
            <td align="right" style="padding:3px 9px"><?php echo $_order->formatPrice($_order->getSubtotal()) ?></td>
        </tr>
        <?php if ($_order->getDiscountAmount()): ?>
            <tr>
                <td colspan="2" align="right" style="padding:3px 9px"><?php echo Mage::helper('sales')->__('Discount') ?></td>
                <td align="right" style="padding:3px 9px"><?php echo $_order->formatPrice($_order->getDiscountAmount()) ?></td>
            </tr>
        <?php endif; ?>
        <?php if ($_order->getShippingAmount()): ?>
            <tr>
                <td colspan="2" align="right" style="padding:3px 9px"><?php echo Mage::helper('sales')->__('Shipping Amount') ?></td>
                <td align="right" style="padding:3px 9px"><?php echo $_order->formatPrice($_order->getShippingAmount()) ?></td>
            </tr>
        <?php endif; ?>
        <?php if ($_order->getTaxAmount()): ?>
            <tr>
                <td colspan="2" align="right" style="padding:3px 9px"><?php echo Mage::helper('sales')->__('Tax Amount') ?></td>
                <td align="right" style="padding:3px 9px"><?php echo $_order->formatPrice($_order->getTaxAmount()) ?></td>
            </tr>
        <?php endif; ?>
        <tr bgcolor="#DEE5E8">
            <td colspan="2" align="right" style="padding:3px 9px"><strong><big><?php echo Mage::helper('sales')->__('Grand Total') ?></big></strong></td>
            <td align="right" style="padding:6px 9px"><strong><big><?php echo $_order->formatPrice($_order->getGrandTotal()) ?></big></strong></td>
        </tr>
    </tfoot>

</table>
