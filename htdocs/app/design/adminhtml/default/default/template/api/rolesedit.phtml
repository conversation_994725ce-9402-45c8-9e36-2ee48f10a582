<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<div class="entry-edit">
    <div class="entry-edit-head">
        <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__('Roles Resources') ?></h4>
    </div>
    <input type="hidden" name="resource" id="role_resources" value="">
    <fieldset id="role_resources">

        <span class="field-row">
            <label for="all"><?php echo $this->__('Resource Access') ?></label>
            <select id="all" name="all" onchange="$('resources_container').toggle()" class="select">
                <option value="0" <?php echo ($this->getEverythingAllowed()?'':'selected="selected"'); ?>><?php echo $this->__('Custom') ?></option>
                <option value="1" <?php echo ($this->getEverythingAllowed()?'selected="selected"':''); ?>><?php echo $this->__('All') ?></option>
            </select>
        </span>

        <span class="field-row" id="resources_container">
            <label><?php echo $this->__('Resources') ?></label>
            <div class="f-left">
                <div class="tree x-tree" id="resource-tree"></div>
            </div>
        </span>

    </fieldset>
</div>
<!-- Draw Resources Tree -->
<script type="text/javascript">
<?php if($this->getEverythingAllowed()): ?>
    $('resources_container').hide();
<?php endif; ?>
Ext.EventManager.onDocumentReady(function() {
    var tree = new Ext.tree.TreePanel('resource-tree', {
        animate:false,
        loader: false,
        enableDD:false,
        containerScroll: true,
        rootUIProvider: Ext.tree.CheckboxNodeUI,
        selModel: new Ext.tree.CheckNodeMultiSelectionModel(),
        rootVisible: false
    });

    tree.on('check', checkHandler, tree);

    // set the root node
    var root = new Ext.tree.TreeNode({
        text: 'root',
        draggable:false,
        checked:'false',
        id:'__root__',
        uiProvider: Ext.tree.CheckboxNodeUI
    });

    tree.setRootNode(root);
    bildResourcesTree(root, <?php echo $this->getResTreeJson() ?>);
    tree.addListener('click', resourceClick.createDelegate(this));

    // render the tree
    tree.render();
    // root.expand();
    tree.expandAll();

    $('role_resources').value = tree.getChecked().join(',');
});

function resourceClick(node, e){
    node.getUI().check(!node.getUI().checked());
    varienElementMethods.setHasChanges(Event.element(e), e);
};

function bildResourcesTree(parent, config){
    if (!config) return null;

    if (parent && config && config.length){
        for (var i = 0; i < config.length; i++){
            config[i].uiProvider = Ext.tree.CheckboxNodeUI;
            var node = new Ext.tree.TreeNode(config[i]);
            parent.appendChild(node);
            if(config[i].children){
                bildResourcesTree(node, config[i].children);
            }
        }
    }
}

function checkHandler(node)
{
    if ( node.attributes.checked && node.parentNode ) {
        var n = node.parentNode;
        this.removeListener('check', checkHandler);
        do {
            if (!n || n.attributes.id == 'admin' || n.attributes.id == '__root__') {
                break;
            } else {
                n.ui.check(true);
            }
        } while (n = n.parentNode );
        this.on('check', checkHandler);
    }
    if ( !node.isLeaf() && node.hasChildNodes() ) {
        this.removeListener('check', checkHandler);
        processChildren(node, node.attributes.checked);
        this.on('check', checkHandler);
    }
    $('role_resources').value = this.getChecked().join(',');
}

function processChildren(node, state)
{
    if ( !node.hasChildNodes() ) return false;
    for(var i = 0; i < node.childNodes.length; i++ ) {
        node.childNodes[i].ui.check(state);
        if ( node.childNodes[i].hasChildNodes() ) {
            processChildren(node.childNodes[i], state);
        }
    }
    return true;
}
</script>
