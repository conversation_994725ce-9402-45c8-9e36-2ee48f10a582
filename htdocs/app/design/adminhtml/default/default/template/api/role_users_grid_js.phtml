<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<script type="text/javascript">
<!--
<?php $myBlock = $this->getLayout()->getBlock('roleUsersGrid'); ?>
<?php if( is_object($myBlock) && $myBlock->getJsObjectName() ): ?>
    var checkBoxes = $H({});
    var warning = false;
    var inRoleUsers = $H(<?php echo $myBlock->_getUsers(true) ?>);
    if (inRoleUsers.size() > 0) warning = true;
    $('in_role_user').value = inRoleUsers.toQueryString();

    function registerUserRole(grid, element, checked){
        if(checked){
            inRoleUsers.set(element.value, 0);
        } else {
            inRoleUsers.unset(element.value);
        }
        $('in_role_user').value = inRoleUsers.toQueryString();
        grid.reloadParams = {'in_role_user[]':inRoleUsers.keys()};
    }

    function roleUsersRowClick(grid, event){
        var trElement = Event.findElement(event, 'tr');
        var isInput   = Event.element(event).tagName == 'INPUT';
        if(trElement){
            var checkbox = Element.getElementsBySelector(trElement, 'input');
            if(checkbox[0]){
                var checked = isInput ? checkbox[0].checked : !checkbox[0].checked;
                if (warning && checkBoxes.size() > 0) {
                    if ( !confirm("<?php echo Mage::helper('core')->jsQuoteEscape($this->__('Warning!\r\nThis action will remove this user from already assigned role\r\nAre you sure?')) ?>") ) {
                        checkbox[0].checked = false;
                        checkBoxes.each(function(elem) {
                            if (elem.value.status == 1) {
                                elem.value.object.checked = true;
                            }
                        });
                        return false;
                    }
                    warning = false;
                }
                <?php echo $myBlock->getJsObjectName() ?>.setCheckboxChecked(checkbox[0], checked);
            }
        }
    }

    function roleUsersRowInit(grid, row){
        var checkbox = $(row).getElementsByClassName('checkbox')[0];
        if (checkbox) {
            checkBoxes.set(checkbox.value, {'status' : ((checkbox.checked) ? 1 : 0), 'object' : checkbox});
        }
    }

    function myhandler(obj)
    {
        if (checkBoxes.size() > 0) {
            if ( !confirm("<?php echo Mage::helper('core')->jsQuoteEscape($this->__('Warning!\r\nThis action will remove those users from already assigned roles\r\nAre you sure?')) ?>") ) {
                obj.checked = false;
                checkBoxes.each(function(elem) {
                    if (elem.value.status == 1) {
                        elem.value.object.checked = true;
                    }
                });
                return false;
            }
            warning = false;
        }
        checkBoxes.each(function(elem) {
            <?php echo $myBlock->getJsObjectName() ?>.setCheckboxChecked(elem.value.object, obj.checked);
        });
    }

<?php echo $myBlock->getJsObjectName() ?>.rowClickCallback = roleUsersRowClick;
<?php echo $myBlock->getJsObjectName() ?>.initRowCallback = roleUsersRowInit;
<?php echo $myBlock->getJsObjectName() ?>.checkboxCheckCallback = registerUserRole;
<?php echo $myBlock->getJsObjectName() ?>.checkCheckboxes = myhandler;
<?php echo $myBlock->getJsObjectName() ?>.rows.each(function(row){roleUsersRowInit(<?php echo $myBlock->getJsObjectName() ?>, row)});
    $('in_role_user_old').value = $('in_role_user').value;
<?php endif; ?>
//-->
</script>
