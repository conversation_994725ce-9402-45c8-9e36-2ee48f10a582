"A user with the same user name or email already exists.","A user with the same user name or email already exists."
"Access denied.","Access denied."
"Api Key confirmation must be same as Api Key.","Api Key confirmation must be same as Api Key."
"Api Key must be at least of %d characters.","Api Key must be at least of %d characters."
"Api Key must include both numeric and alphabetic characters.","Api Key must include both numeric and alphabetic characters."
"Can not find webservice adapter.","Can not find webservice adapter."
"Client Session Timeout (sec.)","Client Session Timeout (sec.)"
"Default Response Charset","Default Response Charset"
"Email","Email"
"Enable WSDL Cache","Enable WSDL Cache"
"First Name is required field.","First Name is required field."
"General Settings","General Settings"
"Invalid webservice adapter specified.","Invalid webservice adapter specified."
"Invalid webservice handler specified.","Invalid webservice handler specified."
"Last Name is required field.","Last Name is required field."
"Magento Core API","Magento Core API"
"Magento Core API Section","Magento Core API Section"
"Please enter a valid email.","Please enter a valid email."
"SOAP/XML-RPC - Roles","SOAP/XML-RPC - Roles"
"SOAP/XML-RPC - Users","SOAP/XML-RPC - Users"
"Unable to login.","Unable to login."
"User Name","User Name"
"User Name is required field.","User Name is required field."
"WS-I Compliance","WS-I Compliance"
"Web Services","Web Services"
"Your account has been deactivated.","Your account has been deactivated."
