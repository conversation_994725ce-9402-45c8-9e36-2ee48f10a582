<?xml version="1.0"?>
<config>
    <modules>
        <PFG_Olx>
            <version>0.1.2</version>
        </PFG_Olx>
    </modules>
    <global>
        <models>
            <pfgolx>
                <class>PFG_Olx_Model</class>
                <resourceModel>pfgolx_resource</resourceModel>
            </pfgolx>
            <pfgolx_resource>
                <class>PFG_Olx_Model_Resource</class>
                <entities>
                    <product_queue>
                        <table>pfg_olx_product_sync_queue</table>
                    </product_queue>
                    <product_queue_history>
                        <table>pfg_olx_product_sync_queue_history</table>
                    </product_queue_history>
                    <product_statistics>
                        <table>pfg_olx_product_statistics</table>
                    </product_statistics>
                </entities>
            </pfgolx_resource>
        </models>
        <resources>
            <pfgolx_setup>
                <setup>
                    <module>PFG_Olx</module>
                    <class>Mage_Catalog_Model_Resource_Setup</class>
                </setup>
                <connection>
                    <use>core_setup</use>
                </connection>
            </pfgolx_setup>
            <pfgolx_write>
                <connection>
                    <use>core_write</use>
                </connection>
            </pfgolx_write>
            <pfgolx_read>
                <connection>
                    <use>core_read</use>
                </connection>
            </pfgolx_read>

        </resources>
        <helpers>
            <pfgolx>
                <class>PFG_Olx_Helper</class>
            </pfgolx>
        </helpers>
        <blocks>
            <pfgolx>
                <class>PFG_Olx_Block</class>
            </pfgolx>
        </blocks>
        <events>
            <catalog_product_save_after>
                <observers>
                    <pfg_olx_global_product_save_after>
                        <type>singleton</type>
                        <class>pfgolx/observers_productObserver</class>
                        <method>queueForSyncOnSave</method>
                    </pfg_olx_global_product_save_after>
                </observers>
            </catalog_product_save_after>
            <cataloginventory_stock_item_save_after>
                <observers>
                    <pfg_olx_global_iventory_save_after>
                        <type>singleton</type>
                        <class>pfgolx/observers_productObserver</class>
                        <method>queueForSyncOnInventorySave</method>
                    </pfg_olx_global_iventory_save_after>
                </observers>
            </cataloginventory_stock_item_save_after>
            <cataloginventory_stock_item_load_after>
                <observers>
                    <pfg_olx_global_iventory_load_after>
                        <type>singleton</type>
                        <class>pfgolx/observers_productObserver</class>
                        <method>storeCurrentIsInStockAfterLoad</method>
                    </pfg_olx_global_iventory_load_after>
                </observers>
            </cataloginventory_stock_item_load_after>
        </events>
        <cache>
            <types>
                <pfgolx_cache translate="label,description" module="pfgolx">
                    <label>PFG OLX Cache</label>
                    <description>Cached values for the PFG OLX API</description>
                    <tags>PFG_OLX_CACHE</tags>
                </pfgolx_cache>
            </types>
        </cache>
    </global>

    <adminhtml>
        <layout>
            <updates>
                <general>
                    <file>pfg/olx/general.xml</file>
                </general>
                <customtabs>
                    <file>pfg/olx/custom_tabs.xml</file>
                </customtabs>
                <dashboard>
                    <file>pfg/olx/dashboard.xml</file>
                </dashboard>
                <queue>
                    <file>pfg/olx/queue.xml</file>
                </queue>
            </updates>
        </layout>

        <events>
            <adminhtml_block_html_before>
                <observers>
                    <pfg_olx_add_grid_columns>
                        <type>singleton</type>
                        <class>pfgolx/observers_adminhtml_productObserver</class>
                        <method>addProductGridColumns</method>
                    </pfg_olx_add_grid_columns>
                </observers>
            </adminhtml_block_html_before>
            <adminhtml_catalog_product_grid_prepare_massaction>
                <observers>
                    <pfg_olx_add_products_mass_actions>
                        <class>pfgolx/observers_adminhtml_productObserver</class>
                        <method>addProductsMassActions</method>
                    </pfg_olx_add_products_mass_actions>
                </observers>
            </adminhtml_catalog_product_grid_prepare_massaction>
            <eav_collection_abstract_load_before>
                <observers>
                    <pfg_olx_add_grid_columns_to_collection>
                        <type>singleton</type>
                        <class>pfgolx/observers_adminhtml_productObserver</class>
                        <method>addProductGridColumnsToCollection</method>
                    </pfg_olx_add_grid_columns_to_collection>
                </observers>
            </eav_collection_abstract_load_before>
            <catalog_category_prepare_save>
                <observers>
                    <pfg_olx_category_prepare_save>
                        <type>singleton</type>
                        <class>pfgolx/observers_adminhtml_categoryObserver</class>
                        <method>adminhtmlCategoryPrepareDataForSave</method>
                    </pfg_olx_category_prepare_save>
                </observers>
            </catalog_category_prepare_save>
            <catalog_product_prepare_save>
                <observers>
                    <pfg_olx_product_prepare_save>
                        <type>singleton</type>
                        <class>pfgolx/observers_adminhtml_productObserver</class>
                        <method>adminhtmlProductPrepareDataForSave</method>
                    </pfg_olx_product_prepare_save>
                </observers>
            </catalog_product_prepare_save>
            <catalog_product_attribute_update_before>
                <observers>
                    <pfg_olx_attribute_update_before>
                        <type>singleton</type>
                        <class>pfgolx/observers_adminhtml_productObserver</class>
                        <method>adminhtmlAttributesSaveBefore</method>
                    </pfg_olx_attribute_update_before>
                </observers>
            </catalog_product_attribute_update_before>
        </events>
    </adminhtml>

    <admin>
        <routers>
            <adminhtml>
                <args>
                    <modules>
                        <pfg_olx before="Mage_Adminhtml">PFG_Olx_Adminhtml</pfg_olx>
                    </modules>
                </args>
            </adminhtml>
        </routers>
    </admin>

    <crontab>
        <jobs>
            <pfg_olx_update_ads_in_limited>
                <schedule>
                    <cron_expr><![CDATA[*/5 * * * *]]></cron_expr>
                </schedule>
                <run>
                    <model>pfgolx/cron_ads::updateAdsInLimitedStatus</model>
                </run>
            </pfg_olx_update_ads_in_limited>
            <pfg_olx_update_all_ads_statuses>
                <schedule>
                    <cron_expr><![CDATA[*/5 * * * *]]></cron_expr>
                </schedule>
                <run>
                    <model>pfgolx/cron_ads::updateAllAdsStatuses</model>
                </run>
            </pfg_olx_update_all_ads_statuses>
            <pfg_olx_update_deleted_ads>
                <schedule>
                    <cron_expr><![CDATA[*/30 * * * *]]></cron_expr>
                </schedule>
                <run>
                    <model>pfgolx/cron_ads::updateDeletedAds</model>
                </run>
            </pfg_olx_update_deleted_ads>
            <pfg_olx_sync_all_products>
                <schedule>
                    <cron_expr><![CDATA[0 5 * * *]]></cron_expr>
                </schedule>
                <run>
                    <model>pfgolx/cron_ads::syncAllProducts</model>
                </run>
            </pfg_olx_sync_all_products>
            <pfg_olx_process_queue_table>
                <schedule>
                    <cron_expr><![CDATA[*/5 * * * *]]></cron_expr>
                </schedule>
                <run>
                    <model>pfgolx/cron_ads::processQueueTable</model>
                </run>
            </pfg_olx_process_queue_table>
        </jobs>
    </crontab>

    <default>
        <pfg_olx>
            <service>
                <api_access_token><![CDATA[]]></api_access_token>
                <api_access_token_expire><![CDATA[]]></api_access_token_expire>
                <api_access_token_save_time><![CDATA[]]></api_access_token_save_time>
                <client_is_connected>0</client_is_connected>
            </service>
            <general>
                <active>1</active>
                <sync_method><![CDATA[on_save]]></sync_method>
                <enable_logging>0</enable_logging>
            </general>
            <pfg_auth_config>
                <pfg_api_url><![CDATA[https://olxapi.pfgbase.com/]]></pfg_api_url>
                <pfg_api_client_id><![CDATA[]]></pfg_api_client_id>
                <pfg_api_secret><![CDATA[]]></pfg_api_secret>
            </pfg_auth_config>
            <products_and_ads_settings>
                <default_sync_status><![CDATA[0]]></default_sync_status>
                <default_sync_category><![CDATA[]]></default_sync_category>
                <olx_category_attributes><![CDATA[]]></olx_category_attributes>
                <configurables_handling><![CDATA[sync_parent]]></configurables_handling>
                <sync_by_visibility><![CDATA[2,4]]></sync_by_visibility>
                <publish_if_no_images><![CDATA[0]]></publish_if_no_images>
                <publish_if_qty_zero><![CDATA[0]]></publish_if_qty_zero>
                <out_of_stock_products_handling><![CDATA[out_of_stock_deactivate]]></out_of_stock_products_handling>
                <number_of_images_to_upload><![CDATA[6]]></number_of_images_to_upload>
                <olx_currency><![CDATA[]]></olx_currency>
                <should_use_price_increase><![CDATA[0]]></should_use_price_increase>
                <price_increase_percentage><![CDATA[]]></price_increase_percentage>
                <olx_price_negotiable><![CDATA[]]></olx_price_negotiable>
                <olx_trade_acceptable><![CDATA[]]></olx_trade_acceptable>
                <ad_description_attributes_overwrite><![CDATA[]]></ad_description_attributes_overwrite>
                <olx_ad_type><![CDATA[]]></olx_ad_type>
                <olx_contact_name><![CDATA[]]></olx_contact_name>
                <olx_contact_phone><![CDATA[]]></olx_contact_phone>
                <olx_contact_region><![CDATA[]]></olx_contact_region>
                <olx_contact_city><![CDATA[]]></olx_contact_city>
                <olx_contact_district><![CDATA[]]></olx_contact_district>
            </products_and_ads_settings>
            <advanced_settings>
                <ad_title_template><![CDATA[%%ATTR_NAME%%]]></ad_title_template>
                <ad_description_template><![CDATA[
%%ATTR_DESCRIPTION%%
%%CONFIGURABLE_PRODUCTS_OPTIONS%%
%%ATTRIBUTES_GROUPED%%]]></ad_description_template>
            </advanced_settings>
            <system_settings>
                <cron_queue_max_time>180</cron_queue_max_time>
                <update_statuses_max_time>180</update_statuses_max_time>
                <api_statuses_update_method>attribute_update</api_statuses_update_method>
            </system_settings>
        </pfg_olx>
    </default>
</config>
