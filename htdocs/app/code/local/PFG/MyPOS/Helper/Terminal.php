<?php

class PFG_MyPOS_Helper_Terminal extends PFG_MyPOS_Helper_Data
{
    public function getCurrencyCode()
    {
        return (string)$this->getConfig('currency');
    }

    public function getTerminalId()
    {
        return (string)$this->getConfig('client_number');
    }

    public function getKeyIndex()
    {
        return (string)$this->getConfig('key_index');
    }

    public function getPrivateKeyFilePath($absolute = true)
    {
        // Return the private key content directly
        return $this->getConfig('store_private_key');
    }

    public function getPrivateKeyPassword()
    {
        return (string)$this->getConfig('private_key_password');
    }

    public function getCertificateFilePath($absolute = true)
    {
        // Return the certificate content directly
        return $this->getConfig('mypos_public_certificate');
    }

    public function getPublicKeyFilePath($absolute = true)
    {
        $publicKeyFilePath = Mage::getBaseDir('var') . DS
            . $this->getConfig('public_key_path');
        if (!file_exists($publicKeyFilePath)) {
            Mage::throwException($this->__('Public key file does not exist.'));
        }

        return $publicKeyFilePath;
    }
}
