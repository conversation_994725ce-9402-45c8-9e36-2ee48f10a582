<?php

class PFG_MyPOS_Helper_Request extends PFG_MyPOS_Helper_Data
{
    public function isDevEnvironment()
    {
        return (bool)((int)Mage::helper('pfg_mypos')->getConfig('sandbox_mode'));
    }

    public function isProductionEnvironment()
    {
        return !$this->isDevEnvironment();
    }

    public function getApiUrl()
    {
        return Mage::helper('pfg_mypos/mypos')->getApiUrl();
    }
}
