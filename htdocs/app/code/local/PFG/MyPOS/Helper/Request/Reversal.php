<?php

class PFG_MyPOS_Helper_Request_Reversal extends PFG_MyPOS_Helper_Request_Sale
{

    /**
     * @param $order Mage_Sales_Model_Order
     * @throws Mage_Core_Exception
     */
    public function getPaymentTransactionId($order)
    {
        if (!is_object($order) || !$order->getId()) {
            Mage::throwException($this->__('Invalid order provided. Please contact administrator.'));
        }

        $payment = $order->getPayment();
        Mage::log('Payment Method: ' . $payment->getMethod(), Zend_Log::INFO, 'payment.log', true);
        if (!is_object($payment)) {
            Mage::throwException($this->__('Could not get payment from order'));
        }

        $transactionId = $payment->getLastTransId();
        if (!$transactionId) {
            Mage::throwException($this->__('Missing transaction ID in payment'));
        }

        return $transactionId;
    }

    /**
     * @param $order Mage_Sales_Model_Order
     * @throws Mage_Core_Exception
     */
    public function getPaymentInternalReferenceId($order)
    {
        return $this->getPaymentAdditionalData($order, '');
    }

    /**
     * @param $order Mage_Sales_Model_Order
     * @param string $additionalDataKey
     * @return mixed
     * @throws Mage_Core_Exception
     */
    public function getPaymentAdditionalData($order, $additionalDataKey = '')
    {
        if (!is_object($order) || !$order->getId()) {
            Mage::throwException($this->__('Invalid order provided. Please contact administrator.'));
        }

        $payment = $order->getPayment();
        Mage::log('Payment Method: ' . $payment->getMethod(), Zend_Log::INFO, 'payment.log', true);
        if (!is_object($payment)) {
            Mage::throwException($this->__('Could not get payment from order'));
        }

        if ($additionalDataKey) {
            return $payment->getAdditionalInformation($additionalDataKey);
        }

        return $payment->getAdditionalInformation();
    }
}