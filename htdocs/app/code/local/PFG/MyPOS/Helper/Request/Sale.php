<?php

class PFG_MyPOS_Helper_Request_Sale extends PFG_MyPOS_Helper_Data
{
    public function checkOrderPaymentStatus()
    {

    }


    /**
     * @param $order Mage_Sales_Model_Order
     * @throws Mage_Core_Exception
     */
    public function getPaymentTransactionId($order)
    {
        if (!is_object($order) || !$order->getId()) {
            Mage::throwException($this->__('Invalid order provided. Please contact administrator.'));
        }

        $payment = $order->getPayment();
        // Use error_log instead of Mage::log to ensure the message is logged
        error_log('Payment Method: ' . $payment->getMethod(), 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');
        // Also log using Mage::log for comparison
        Mage::log('Payment Method (Mage::log): ' . $payment->getMethod(), Zend_Log::INFO, 'system.log', true);
        if (!is_object($payment)) {
            Mage::throwException($this->__('Could not get payment from order'));
        }

        $transactionId = $payment->getLastTransId();
        if (!$transactionId) {
            Mage::throwException($this->__('Missing transaction ID in payment'));
        }

        return $transactionId;
    }
    /**
     * @param $order Mage_Sales_Model_Order
     * @return float
     * @throws Mage_Core_Exception
     */
    public function getAmount($order)
    {
        if (!is_object($order) || !$order->getId()) {
            Mage::throwException($this->__('Invalid order provided. Please contact administrator.'));
        }

        $orderCurrencyCode = $order->getOrderCurrencyCode();
        $terminalCurrencyCode = Mage::helper('pfg_mypos/terminal')->getCurrencyCode();
        $amount = $order->getBaseGrandTotal() - $order->getBaseShippingAmount();
        if ($orderCurrencyCode != $terminalCurrencyCode) {
            if ($order->getBaseCurrencyCode() == $terminalCurrencyCode) {
                $amount = $order->getBaseGrandTotal() - $order->getBaseShippingAmount();
            } else {
                try {
                    $amount = Mage::helper('directory')->currencyConvert($amount, $orderCurrencyCode, $terminalCurrencyCode);
                } catch (Exception $e) {
                    $this->getLogger()->logException($e);
                    $this->getLogger()->log(
                        $this->__(
                            'Exception occurred while converting currency from %s to %s. See exception log for more details',
                            $orderCurrencyCode, $terminalCurrencyCode
                        ),
                        Zend_Log::ERR
                    );
                    $amount = $amount ? $amount : $order->getBaseGrandTotal() - $order->getBaseShippingAmount();
                }
            }

        }

        return round($amount, 2);
    }

    /**
     * @param $order Mage_Sales_Model_Order
     * @return mixed
     */
    public function getOrderId($order)
    {
        $orderId = $order->getId();
        $orderId = str_pad($orderId, 6, '0', STR_PAD_LEFT); // pad the order ID to 6 symbols
        return mb_substr($orderId, -6); // get the last 6 characters from the order
    }

    /**
     * @param $order Mage_Sales_Model_Order
     * @return mixed
     */
    public function getCurrency($order)
    {
        return (string)$order->getOrderCurrencyCode();
    }

    /**
     * @param $order Mage_Sales_Model_Order
     * @return string
     */
    public function getDescription($order)
    {
        $description = $this->getConfig('description');
        $description = str_ireplace('%%increment_id%%', $order->getIncrementId(), $description);

        return $description;
    }

    public function getMerchantName()
    {
        return (string)Mage::getStoreConfig('general/store_information/name');
    }

    public function getMerchantUrl()
    {
        return Mage::getBaseUrl();
    }

    public function getMerchantId()
    {
        return $this->getConfig('store_id');
    }

    public function getTerminalId()
    {
        return Mage::helper('pfg_mypos/terminal')->getTerminalId();
    }

    public function getStoreEmail()
    {
        return (string)Mage::getStoreConfig('trans_email/ident_general/email');
    }

    public function getCountry()
    {
        return (string)Mage::getStoreConfig('general/country/default');
    }

    public function getMerchantTimezone()
    {
        $localTimezone = new DateTimeZone((string)Mage::getStoreConfig('general/locale/timezone'));
        $utcTime = new DateTime('now', new DateTimeZone('UTC'));

        $offset = $localTimezone->getOffset($utcTime) / 3600;
        return $offset > 0 ? ('+' . $offset) : (string)$offset;
    }

    public function getTimestamp()
    {
        return time();
    }

    public function getNonce()
    {
        return strtoupper(bin2hex(openssl_random_pseudo_bytes(16)));
    }

    /**
     * @param $order Mage_Sales_Model_Order
     * @return string
     */
    public function getOrderIdentifier($order)
    {
        return $order->getIncrementId();
    }
}
