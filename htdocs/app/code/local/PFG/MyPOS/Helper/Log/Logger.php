<?php

class PFG_MyPOS_Helper_Log_Logger extends PFG_MyPOS_Helper_Data
{
    protected $forceLog;
    protected $logFileName;
    protected $exceptionsLogFileName;

    public function log($message, $type = Zend_Log::DEBUG)
    {
        Mage::log($message, $type, $this->getLogFileName(), $this->shouldForceLog());
    }

    public function logException(Exception $exception)
    {
        Mage::log($exception->__toString(), Zend_Log::ERR, $this->getExceptionsFileName(), $this->shouldForceLog());
        $this->log($this->__('An exception has occurred: %s. See exceptions file for more info', $exception->getMessage()), Zend_Log::ERR);
    }

    protected function shouldForceLog()
    {
        // @todo - add setting in system settings for the log file name
        if (is_null($this->forceLog)) {
            $this->forceLog = (bool)((int)$this->getAdvancedConfig('force_log'));
        }

        return $this->forceLog;
    }

    protected function getLogFileName()
    {
        // @todo - add setting in system settings for the log file name
        if (is_null($this->logFileName)) {
            $this->logFileName = (string)$this->getAdvancedConfig('log_file_name');
        }

        return  $this->logFileName;
    }

    protected function getExceptionsFileName()
    {
        // @todo - add setting in system settings for the log file name
        if (is_null($this->exceptionsLogFileName)) {
            $this->exceptionsLogFileName = (string)$this->getAdvancedConfig('exceptions_file_name');
        }

        return  $this->exceptionsLogFileName;
    }
}