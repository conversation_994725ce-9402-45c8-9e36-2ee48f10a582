<?php

class PFG_MyPOS_Helper_Data extends Mage_Core_Helper_Abstract
{
    protected $logger;

    /**
     * @param $path
     * @return mixed
     */
    public function getConfig($path)
    {
        return Mage::getStoreConfig('payment/pfg_mypos/' . $path);
    }

    /**
     * @param $path
     * @return mixed
     */
    public function getAdvancedConfig($path)
    {
        return Mage::getStoreConfig('pfg_mypos/advanced/' . $path);
    }

    /**
     * @return Mage_Core_Helper_Abstract|PFG_MyPOS_Helper_Log_Logger|null
     */
    public function getLogger()
    {
        if (is_null($this->logger)) {
            $this->logger = Mage::helper('pfg_mypos/log_logger');
        }

        return $this->logger;
    }
}