<?php

class PFG_MyPOS_Helper_Sales_Order extends PFG_MyPOS_Helper_Data
{
    /**
     * @param $order Mage_Sales_Model_Order|int
     * @param $myposResponse <PERSON>g<PERSON><PERSON><PERSON><PERSON>\MyPOS\Response\Response
     * @return false|Mage_Core_Model_Abstract|Mage_Sales_Model_Order|mixed
     * @throws Mage_Core_Exception
     */
    public function processOrderSuccess($order, $myposResponse)
    {
        if (!is_object($order) || !$order->getId()) {
            $order = Mage::getModel('sales/order')->load($order);
        }

        if (!is_object($order) || !$order->getId()) {
            Mage::throwException($this->__('Could not load order for success processing.'));
        }

        try {
            // change the status to order_status_after_payment
            // generate and add comment
            $comment = $this->__('Transaction completed successfully: %s', $myposResponse->retrievalReferenceNumber) . PHP_EOL . '<br />';
            $comment .= $this->__('Time of payment: %s', date('Y-m-d H:i:s', strtotime($myposResponse->timestamp))) . PHP_EOL . '<br />';
            $comment .= $this->__('Amount captured: %.2f %s', $myposResponse->amount, $myposResponse->currency) . PHP_EOL . '<br />';
            $comment .= $this->__('MyPOS message was: %s', $myposResponse->statusMessage) . PHP_EOL . '<br />';
            $order->addStatusHistoryComment($comment, Mage::helper('pfg_mypos/sales')->getOrderStatusAfterPayment());
            $order->sendNewOrderEmail()
                ->setIsCustomerNotified(true);
            $order->save();

            // create the payment
            $paymentMessage = $myposResponse->statusMessage . ' (' . $myposResponse->responseCode . ')';
            $paymentAmount = (float)$myposResponse->amount;
            $paymentAmount = number_format($paymentAmount, 2);

            $payment = $order->getPayment();
            Mage::log('Payment Method: ' . $payment->getMethod(), Zend_Log::INFO, 'payment.log', true);
            if ($payment) {
                $payment->setTransactionId($myposResponse->retrievalReferenceNumber);
                $payment->setPreparedMessage($paymentMessage);
                $payment->setIsTransactionClosed(1); // always closed
                $order->save();
                $payment->registerCaptureNotification($paymentAmount);
                $order->save();
            }

            // prepare the checkout session for the success page
            $onepageCheckoutSession = Mage::getSingleton('checkout/type_onepage')->getCheckout();
            $onepageCheckoutSession->setLastSuccessQuoteId($order->getQuoteId());
            $onepageCheckoutSession->setLastQuoteId($order->getQuoteId());
            $onepageCheckoutSession->setLastOrderId($order->getId());
        } catch (Exception $e) {
            Mage::helper('pfg_mypos')->getLogger()->logException($e);
            Mage::getSingleton('customer/session')->addError($this->__('An error has occurred. Please - contact administrator.'));
            return false;
        }

        return $order;
    }

    public function processOrderFailed($order, $myposResponse, $responseCode)
    {
        if (!is_object($order) || !$order->getId()) {
            $order = Mage::getModel('sales/order')->load($order);
        }

        if (!is_object($order) || !$order->getId()) {
            Mage::throwException($this->__('Could not load order for fail processing.'));
        }

        // first log the message
        $this->getLogger()->log($this->__(
            'Response code for order %s was "%s"(%s)',
            $order->getId(),
            $myposResponse->responseCodeDescription(),
            $responseCode
        ));

        $order = Mage::getModel('sales/order')->load($order->getId());
        $order->cancel();
        $order->addStatusHistoryComment($this->__('Order canceled. MyPOS message was: %s', $myposResponse->responseCodeDescription()));
        $order->save();

        return $order;
    }
}