<?php

// Load IPC autoloader
require_once Mage::getModuleDir('local', 'PFG_MyPOS') . DS . 'IPC' . DS . 'autoload.php';

use Mypos\IPC\Purchase;
use Mypos\IPC\Refund;
use Mypos\IPC\GetTxnStatus;
use Mypos\IPC\ResponseWrapper;
use Mypos\IPC\Config;
use Mypos\IPC\Card;
use Mypos\IPC\Customer;
use Mypos\IPC\Cart;

class PFG_MyPOS_Helper_MyPOS extends PFG_MyPOS_Helper_Data
{
    public function __construct()
    {
        parent::__construct();
        error_log('MyPOS payment method constructor called', 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');
    }

    protected $myposSign;
    protected $salesRequest;
    protected $refundRequest;
    protected $authorizationRequest;

    /**
     * @param $order
     * @return Purchase
     * @throws Mage_Core_Exception
     */
    public function getSalesRequest($order)
    {
        if (!is_object($order) || !$order->getId()) {
            Mage::throwException($this->__('Invalid order provided. Please contact administrator.'));
        }

        if (is_null($this->salesRequest)) {
            $salesRequestHelper = Mage::helper('pfg_mypos/request_sale');

            // Create config
            $config = new Config();
            $config->setPrivateKey($this->getMyPOSSign()->getPrivateKey());
            $config->setKeyIndex($this->getMyPOSSign()->getKeyIndex());
            $config->setSid($salesRequestHelper->getMerchantId());
            $config->setWallet($salesRequestHelper->getTerminalId());

            // Create customer
            $customer = new Customer();
            $customer->setEmail($salesRequestHelper->getStoreEmail());

            // Create cart
            $cart = new Cart();
            $cart->setOrderId($salesRequestHelper->getOrderId($order));
            $cart->setAmount($salesRequestHelper->getAmount($order));
            $cart->setCurrency($salesRequestHelper->getCurrency($order));
            $cart->setDescription($salesRequestHelper->getDescription($order));

            // Create purchase
            $this->salesRequest = new Purchase($config);
            $this->salesRequest->setUrlOk(Mage::getUrl('pfg_mypos/response/process'));
            $this->salesRequest->setUrlCancel(Mage::getUrl('pfg_mypos/response/fail'));
            $this->salesRequest->setUrlNotify(Mage::getUrl('pfg_mypos/response/process'));
            $this->salesRequest->setCustomer($customer);
            $this->salesRequest->setCart($cart);
        }

        return $this->salesRequest;
    }

    /**
     * @param $order Mage_Sales_Model_Order
     * @param $amount float
     * @return Refund
     * @throws Mage_Core_Exception
     */
    public function getRefundRequest($order, $amount)
    {
        if (!is_object($order) || !$order->getId()) {
            Mage::throwException($this->__('Invalid order provided. Please contact administrator.'));
        }

        if (is_null($this->refundRequest)) {
            $salesRequestHelper = Mage::helper('pfg_mypos/request_sale');

            // Create config
            $config = new Config();
            $config->setPrivateKey($this->getMyPOSSign()->getPrivateKey());
            $config->setKeyIndex($this->getMyPOSSign()->getKeyIndex());
            $config->setSid($salesRequestHelper->getMerchantId());
            $config->setWallet($salesRequestHelper->getTerminalId());

            // Create cart
            $cart = new Cart();
            $cart->setOrderId($salesRequestHelper->getOrderId($order));
            $cart->setAmount($amount);
            $cart->setCurrency($salesRequestHelper->getCurrency($order));

            // Create refund
            $this->refundRequest = new Refund($config);
            $this->refundRequest->setCart($cart);
        }

        return $this->refundRequest;
    }

    public function getSalesRequestForm($order)
    {
        $salesRequest = $this->getSalesRequest($order);
        return $salesRequest->process();
    }

    /**
     * @param $orderId
     * @return GetTxnStatus
     */
    public function getStatusCheckRequest($orderId)
    {
        if (is_object($orderId)) {
            $orderId = $orderId->getId();
        }

        // Create config
        $config = new Config();
        $config->setPrivateKey($this->getMyPOSSign()->getPrivateKey());
        $config->setKeyIndex($this->getMyPOSSign()->getKeyIndex());
        $config->setSid(Mage::helper('pfg_mypos/request_sale')->getMerchantId());
        $config->setWallet(Mage::helper('pfg_mypos/terminal')->getTerminalId());

        // Create cart
        $cart = new Cart();
        $cart->setOrderId($orderId);

        // Create status check request
        $request = new GetTxnStatus($config);
        $request->setCart($cart);

        return $request;
    }

    public function getStatusCheckResponseResult($responseData, $ignoreResponseVerification = false)
    {
        $response = null;
        $isResponseVerified = false;
        try {
            // Create config for verification
            $config = new Config();
            $config->setPrivateKey($this->getMyPOSSign()->getPrivateKey());
            $config->setKeyIndex($this->getMyPOSSign()->getKeyIndex());

            // Create response object
            $response = new ResponseWrapper($config);
            $response->setResponse($responseData);
            $isResponseVerified = $response->validate();
        } catch (Exception $e) {
            $isResponseVerified = false;
            $this->getLogger()->logException($e);
            $this->getLogger()->log(
                $this->__(
                    'Exception occurred while trying to process MyPOS response. Message was: %s.See exception log for more details',
                    $e->getMessage()
                ),
                Zend_Log::ERR
            );
        }

        if (!$isResponseVerified && !$ignoreResponseVerification) {
            Mage::throwException($this->__('Could not verify MyPOS response.'));
        }

        return is_object($response) ? $response->getStatusCode() : '';
    }

    public function getApiUrl()
    {
        // Get the URL from the Config object
        return $this->getMyPOSSign()->getIpcURL();
    }

    /**
     * @param $order Mage_Sales_Model_Order
     * @param $request MyPOSRequest
     * @return MyPOSRequest
     */
    protected function setSaleFieldsByOrder($order, $request)
    {
        $salesRequestHelper = Mage::helper('pfg_mypos/request_sale');
        $request->setCurrency($salesRequestHelper->getCurrency($order))
            ->setOrder($salesRequestHelper->getOrderId($order))
            ->setDescription($salesRequestHelper->getDescription($order))
            ->setMerchantName($salesRequestHelper->getMerchantName())
            ->setMerchantUrl($salesRequestHelper->getMerchantUrl())
            ->setMerchant($salesRequestHelper->getMerchantId())
            ->setTerminal($salesRequestHelper->getTerminalId())
            ->setEmail($salesRequestHelper->getStoreEmail())
            ->setCountry($salesRequestHelper->getCountry())
            ->setMerchantTimezone($salesRequestHelper->getMerchantTimezone())
            ->setTimestamp($salesRequestHelper->getTimestamp())
            ->setNonce($salesRequestHelper->getNonce())
            ->setOrderIdentifier($salesRequestHelper->getOrderIdentifier($order))
            ->setAddendum('AD,TD'); // @todo - what's this? make it use method;

        return $request;
    }

    protected function setReversalFieldsByOrder($order, $request)
    {
        $reversalRequestHelper = Mage::helper('pfg_mypos/request_reversal');
        $request = $this->setSaleFieldsByOrder($order, $request);
        $myposOrder = Mage::getModel('pfg_mypos/mypos_orders')->load($order->getId(), 'order_id');
        if (!is_object($myposOrder) || !$myposOrder->getId()) {
            Mage::throwException($this->__('Missing mypos order for order %s', $order->getIncrementId()));
        }
        $myposOrderData = Mage::helper('core')->jsonDecode($myposOrder->getData('mypos_response'));
        if (!is_array($myposOrderData) || !isset($myposOrderData['INT_REF'])) {
            Mage::throwException($this->__('Missing mypos internal reference for order %s', $order->getIncrementId()));
        }

        $request->setRetrievalReferenceNumber($reversalRequestHelper->getPaymentTransactionId($order))
            ->setInternalReference($myposOrderData['INT_REF']);

        return $request;
    }

    public function getMyPOSSign()
    {
        if (is_null($this->myposSign)) {
            $terminalHelper = Mage::helper('pfg_mypos/terminal');
            $this->myposSign = new Config();
            $this->myposSign->setPrivateKey($terminalHelper->getPrivateKeyFilePath());
            $this->myposSign->setKeyIndex($terminalHelper->getKeyIndex());
            $this->myposSign->setIsSandbox(Mage::helper('pfg_mypos/request')->isDevEnvironment());
        }

        return $this->myposSign;
    }

    public function getPaymentFailedCmsPage()
    {
        return $this->getConfig('payment_failed_cms_page');
    }
}
