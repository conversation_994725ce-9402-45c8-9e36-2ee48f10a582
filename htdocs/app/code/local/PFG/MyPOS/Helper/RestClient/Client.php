<?php

require_once Mage::getModuleDir('local', 'PFG_MyPOS') . DS . 'lib' . DS . 'vendor' . DS . 'autoload.php';

class PFG_MyPOS_Helper_RestClient_Client extends PFG_MyPOS_Helper_Data
{
    protected $client;

    public function getClient($initiateNew = false)
    {
        if (!$this->client || $initiateNew) {
            $this->client = $this->initiateClient();
        }

        return $this->client;
    }

    /**
     * @param array $formData
     * @param bool $parseContent
     * @return array|\Psr\Http\Message\ResponseInterface
     */
    public function postToMyPOS($formData, $parseContent = true)
    {
        $client = $this->getClient();
        $response = $client->post('', [
            'form_params' => $formData,
        ]);

        $statusCode = $response->getStatusCode();
        if ($statusCode !== 200) {
            Mage::throwException($this->__('Wrong status returned from MyPOS: %s', $statusCode));
        }

        if ($parseContent) {
            return $this->parseContent($response->getBody());
        }

        return $response;
    }

    public function parseContent($content)
    {
        return $this->parseJsonContent($content);
    }

    protected function initiateClient()
    {
        $baseUri = Mage::helper('pfg_mypos/request')->getApiUrl();

        return new \GuzzleHttp\Client([
            'base_uri' => $baseUri,
            'timeout'  => 20.0,
        ]);
    }

    /**
     * @param $content
     * @return array
     */
    protected function parseJsonContent($content)
    {
        try {
            return Mage::helper('core')->jsonDecode($content);
        } catch (Zend_Json_Exception $jsonException) {
            $this->getLogger()->log($this->__('There was an exception while encoding the JSON response.'));
            $this->getLogger()->logException($jsonException);
        } catch (Exception $exception) {
            $this->getLogger()->logException($exception);
        }

        return [];
    }
}