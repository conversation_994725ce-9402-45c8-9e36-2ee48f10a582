<?php

// Load IPC autoloader
require_once Mage::getModuleDir('local', 'PFG_MyPOS') . DS . 'IPC' . DS . 'autoload.php';

use Mypos\IPC\ResponseWrapper as MyPOSResponse;

class PFG_MyPOS_Adminhtml_Pfg_MyPOSController extends Mage_Adminhtml_Controller_Action
{
    public function checkPaymentStatusAction()
    {
        $orderId = $this->getRequest()->getParam('id', false);
        if (!$orderId) {
            Mage::getSingleton('adminhtml/session')->addError($this->__('Missing order ID in request.'));
            return $this->_redirectReferer();
        }

        $MyPOSHelper = Mage::helper('pfg_mypos/mypos');
        $restClientHelper = Mage::helper('pfg_mypos/restClient_client');
        $MyPOSSalesHelper = Mage::helper('pfg_mypos/sales');

        $statusCheckRequest = $MyPOSHelper->getStatusCheckRequest($orderId);
        $statusCheckResult = $restClientHelper->postToMyPOS($statusCheckRequest->toPostData());
        $MyPOSResponse = MyPOSResponse::withPost($statusCheckResult);
//            $responseCode = $MyPOSHelper->getStatusCheckResponseResult($statusCheckResult); // @fixme - uncomment me
        $responseCode = $MyPOSHelper->getStatusCheckResponseResult($statusCheckResult, true); // @fixme - delete me
        $order = Mage::getModel('sales/order')->load($orderId);
        if ($responseCode == '00') {
            Mage::helper('pfg_mypos/sales_order')->processOrderSuccess($order, $MyPOSResponse);
            Mage::getSingleton('adminhtml/session')->addSuccess($this->__('Order paid. See comments for more information.'));
        } else {
            if (in_array($responseCode, MyPOSResponse::RESPONSE_CODES_FAILURE)) {
                Mage::helper('pfg_mypos/sales_order')->processOrderFailed($order, $MyPOSResponse, $responseCode);
                Mage::getSingleton('adminhtml/session')->addError($this->__('Payment canceled. See comments for more information.'));
            } else {
                Mage::getSingleton('adminhtml/session')->addError($this->__('Order payment is still processing. See comments section for more information.'));
            }
            // @todo - if hold -> show status in order screen
        }

        return $this->_redirectReferer();
    }
}
