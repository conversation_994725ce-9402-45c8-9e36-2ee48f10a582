<?php

// Load IPC autoloader
require_once Mage::getModuleDir('local', 'PFG_MyPOS') . DS . 'IPC' . DS . 'autoload.php';

use Mypos\IPC\ResponseWrapper as MyPOSResponse;

class PFG_MyPOS_ResponseController extends Mage_Core_Controller_Front_Action
{
    public function processAction()
    {
        $request = $this->getRequest();
        if (!$request->isPost()) {
            Mage::getSingleton('customer/session')->addError($this->__('Invalid request from MyPOS. Please contact administrators'));
            return $this->_redirect('/');
        }

        $orderId = (int)$request->getPost('ORDER', false);
        if (!$orderId) {
            Mage::getSingleton('customer/session')->addError($this->__('Invalid request from MyPOS - missing order ID. Please contact administrators'));
            return $this->_redirect('/');
        }

        $postData = $request->getPost();
        $MyPOSHelper = Mage::helper('pfg_mypos/mypos');
        // Maybe remove later
        Mage::helper('pfg_mypos')->getLogger()->log('#####################################');
        Mage::helper('pfg_mypos')->getLogger()->log('### Logging response data: START ###');
        Mage::helper('pfg_mypos')->getLogger()->log('#####################################');
        Mage::helper('pfg_mypos')->getLogger()->log($postData);
        Mage::helper('pfg_mypos')->getLogger()->log('##################################');
        Mage::helper('pfg_mypos')->getLogger()->log('### Logging response data: END ###');
        Mage::helper('pfg_mypos')->getLogger()->log('##################################');
        try {
            // Create config for response
            $config = new Mypos\IPC\Config();
            $config->setPrivateKey($MyPOSHelper->getMyPOSSign()->getPrivateKey());
            $config->setKeyIndex($MyPOSHelper->getMyPOSSign()->getKeyIndex());

            // Create response object
            $MyPOSResponse = new MyPOSResponse($config);
            $MyPOSResponse->setResponse($postData);

            $MyPOSOrder = Mage::getModel('pfg_mypos/mypos_orders');;
            $MyPOSOrder->setData('order_id', $orderId)
                ->setData('MyPOS_response', Mage::helper('core')->jsonEncode($postData))
                ->setData('created_at', date('Y-m-d H:i:s', time()));
            $MyPOSOrder->save();

            $responseCode = $MyPOSResponse->getStatusCode();
            if ($responseCode == '00') {
                $result = Mage::helper('pfg_mypos/sales_order')->processOrderSuccess($orderId, $MyPOSResponse);
                return $this->_redirect('checkout/success');
            } else {
                // Check if response code indicates failure
                if ($responseCode != '00' && $responseCode != '') {
                    Mage::helper('pfg_mypos/sales_order')->processOrderFailed($orderId, $MyPOSResponse, $responseCode);
                }

                return $this->_redirect('checkout/paymentError');
            }
        } catch (Exception $e) {
            Mage::helper('pfg_mypos')->getLogger()->logException($e);
            return $this->_redirect('checkout/paymentError');
        }

    }

    public function failAction()
    {
        $this->loadLayout();
        $this->renderLayout();
    }

    protected function getCheckout()
    {
        return Mage::getSingleton('checkout/session');
    }
}
