<?php
/* @var $installer Mage_Core_Model_Resource_Setup */
$installer = $this;
$installer->startSetup();

// Migrate existing configuration values from old field names to new field names
$oldToNewMapping = array(
    'payment/pfg_mypos/merchant_id' => 'payment/pfg_mypos/store_id',
    'payment/pfg_mypos/terminal_id' => 'payment/pfg_mypos/client_number',
    'payment/pfg_mypos/private_key_path' => 'payment/pfg_mypos/store_private_key',
    'payment/pfg_mypos/certificate_path' => 'payment/pfg_mypos/mypos_public_certificate'
);

// Get the connection
$connection = $installer->getConnection();

// Get the config data table name
$configDataTable = $installer->getTable('core/config_data');

// Migrate each configuration value
foreach ($oldToNewMapping as $oldPath => $newPath) {
    // Get the old value
    $select = $connection->select()
        ->from($configDataTable, array('scope', 'scope_id', 'value'))
        ->where('path = ?', $oldPath);
    
    $oldValues = $connection->fetchAll($select);
    
    // Insert the old value into the new path
    foreach ($oldValues as $oldValue) {
        $connection->insertOnDuplicate(
            $configDataTable,
            array(
                'scope' => $oldValue['scope'],
                'scope_id' => $oldValue['scope_id'],
                'path' => $newPath,
                'value' => $oldValue['value']
            ),
            array('value')
        );
    }
}

// Add default value for the new URL field
$installer->setConfigData('payment/pfg_mypos/url', '1'); // Default to test URL

$installer->endSetup();