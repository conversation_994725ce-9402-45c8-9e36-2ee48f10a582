<?php
/* @var $installer Mage_Core_Model_Resource_Setup */
$installer = $this;
$installer->startSetup();

// Configuration settings for MyPOS
$settings = array(
    'payment/pfg_mypos/active' => '1',
    'payment/pfg_mypos/title' => 'Credit Card Payment via MyPOS',
    'payment/pfg_mypos/instructions' => 'You will be redirected to MyPOS secure payment page.',
    'payment/pfg_mypos/description' => 'Payment for your order from our store',
    'payment/pfg_mypos/new_order_status' => 'pending',
    'payment/pfg_mypos/order_status_after_payment' => 'processing',
    'payment/pfg_mypos/payment_failed_cms_page' => '',
    'payment/pfg_mypos/merchant_id' => '',
    'payment/pfg_mypos/terminal_id' => '',
    'payment/pfg_mypos/currency' => 'BGN',
    'payment/pfg_mypos/language' => 'EN',
    'payment/pfg_mypos/private_key_path' => '',
    'payment/pfg_mypos/private_key_password' => '',
    'payment/pfg_mypos/certificate_path' => '',
    'payment/pfg_mypos/public_key_path' => '',
    'payment/pfg_mypos/sandbox_mode' => '1',
    'payment/pfg_mypos/sort_order' => '100',
    'pfg_mypos/advanced/force_log' => '0',
    'pfg_mypos/advanced/log_file_name' => 'pfg_mypos.log',
    'pfg_mypos/advanced/exceptions_file_name' => 'pfg_mypos_exceptions.log'
);

// Add all configuration settings
foreach ($settings as $path => $value) {
    $installer->setConfigData($path, $value);
}

// Create MyPOS orders table if it doesn't exist
if (!$installer->getConnection()->isTableExists($installer->getTable('pfg_mypos/pfg_mypos_orders'))) {
    $table = $installer->getConnection()
        ->newTable($installer->getTable('pfg_mypos/pfg_mypos_orders'))
        ->addColumn('id', Varien_Db_Ddl_Table::TYPE_INTEGER, null, array(
            'identity' => true,
            'unsigned' => true,
            'nullable' => false,
            'primary'  => true,
        ), 'ID')
        ->addColumn('order_id', Varien_Db_Ddl_Table::TYPE_INTEGER, null, array(
            'unsigned' => true,
            'nullable' => false,
        ), 'Order ID')
        ->addColumn('store_id', Varien_Db_Ddl_Table::TYPE_SMALLINT, null, array(
            'unsigned' => true,
            'nullable' => false,
            'default'  => '0',
        ), 'Store ID')
        ->addColumn('transaction_id', Varien_Db_Ddl_Table::TYPE_TEXT, 255, array(
            'nullable' => true,
        ), 'MyPOS Transaction ID')
        ->addColumn('status', Varien_Db_Ddl_Table::TYPE_TEXT, 32, array(
            'nullable' => false,
            'default'  => 'pending',
        ), 'Transaction Status')
        ->addColumn('amount', Varien_Db_Ddl_Table::TYPE_DECIMAL, '12,4', array(
            'nullable' => false,
            'default'  => '0.0000',
        ), 'Transaction Amount')
        ->addColumn('currency', Varien_Db_Ddl_Table::TYPE_TEXT, 3, array(
            'nullable' => false,
            'default'  => 'BGN',
        ), 'Currency')
        ->addColumn('created_at', Varien_Db_Ddl_Table::TYPE_TIMESTAMP, null, array(
            'nullable' => false,
            'default'  => Varien_Db_Ddl_Table::TIMESTAMP_INIT,
        ), 'Created At')
        ->addColumn('updated_at', Varien_Db_Ddl_Table::TYPE_TIMESTAMP, null, array(
            'nullable' => false,
            'default'  => Varien_Db_Ddl_Table::TIMESTAMP_INIT_UPDATE,
        ), 'Updated At')
        ->addColumn('response_code', Varien_Db_Ddl_Table::TYPE_TEXT, 32, array(
            'nullable' => true,
        ), 'MyPOS Response Code')
        ->addColumn('response_message', Varien_Db_Ddl_Table::TYPE_TEXT, 255, array(
            'nullable' => true,
        ), 'MyPOS Response Message')
        ->addColumn('additional_info', Varien_Db_Ddl_Table::TYPE_TEXT, null, array(
            'nullable' => true,
        ), 'Additional Information')
        ->addIndex(
            $installer->getIdxName('pfg_mypos/pfg_mypos_orders', array('order_id')),
            array('order_id')
        )
        ->addIndex(
            $installer->getIdxName('pfg_mypos/pfg_mypos_orders', array('transaction_id')),
            array('transaction_id')
        )
        ->addIndex(
            $installer->getIdxName('pfg_mypos/pfg_mypos_orders', array('store_id')),
            array('store_id')
        )
        ->addForeignKey(
            $installer->getFkName('pfg_mypos/pfg_mypos_orders', 'order_id', 'sales/order', 'entity_id'),
            'order_id',
            $installer->getTable('sales/order'),
            'entity_id',
            Varien_Db_Ddl_Table::ACTION_CASCADE,
            Varien_Db_Ddl_Table::ACTION_CASCADE
        )
        ->addForeignKey(
            $installer->getFkName('pfg_mypos/pfg_mypos_orders', 'store_id', 'core/store', 'store_id'),
            'store_id',
            $installer->getTable('core/store'),
            'store_id',
            Varien_Db_Ddl_Table::ACTION_CASCADE,
            Varien_Db_Ddl_Table::ACTION_CASCADE
        )
        ->setComment('PFG MyPOS Orders Table');

    $installer->getConnection()->createTable($table);
}

$installer->endSetup();