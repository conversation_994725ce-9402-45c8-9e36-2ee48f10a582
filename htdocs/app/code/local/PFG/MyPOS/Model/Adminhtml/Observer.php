<?php

class PFG_MyPOS_Model_Adminhtml_Observer extends Mage_Core_Model_Abstract
{
    public function addCheckPaymentStatusButton($observer)
    {
        $block = $observer->getEvent()->getData('block');
        $myposHelper = Mage::helper('pfg_mypos');
        if ($block instanceof Mage_Adminhtml_Block_Sales_Order_View && $block->getId() == 'sales_order_view' && $block->getOrderId()) {
            if ($block->getOrder()->getPayment()->getMethod() == PFG_MyPOS_Model_Payment_MyPOS::PAYMENT_METHOD_CODE) {
                $url = Mage::helper('adminhtml')->getUrl('adminhtml/pfg_mypos/checkPaymentStatus', array('id' => $block->getOrderId()));

                $block->addButton(
                    'pfg_mypos_check_payment_status',
                    array(
                        'label' => Mage::helper('pfg_mypos')->__('Check MyPOS Payment Status'),
                        'onclick' => sprintf(
                            "confirmSetLocation('%s', '%s')",
                            str_replace("'", "\'", $myposHelper->escapeHtml($myposHelper->__('Are you sure you want to refresh MY_POS payment status?'))),
                            $myposHelper->escapeHtml($url)
                        ),
                        'class' => 'custom-window-open-btn',
                        'id' => 'pfg_mypos_check_payment_status',
                    )
                );
            }
        }
    }
}