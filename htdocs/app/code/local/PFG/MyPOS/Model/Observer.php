<?php

class PFG_MyPOS_Model_Observer
{
    public function myposChangeOrderStatus(Varien_Event_Observer $observer)
    {
        /**
         * @var $order Mage_Sales_Model_Order
         */
        $order = $observer->getEvent()->getOrder();
        $paymentMethod = $order->getPayment()->getMethod();
        if ($paymentMethod == 'pfg_mypos') {
            $order->setStatus(Mage::getStoreConfig('payment/pfg_mypos/new_order_status'));
            $order->setState(Mage::getStoreConfig('payment/pfg_mypos/new_order_status'));
            $order->save();
        }
    }
}