<?php

// Load IPC autoloader
require_once Mage::getModuleDir('local', 'PFG_MyPOS') . DS . 'IPC' . DS . 'autoload.php';

use Mypos\IPC\ResponseWrapper as MyPOSResponse;

class PFG_MyPOS_Model_Payment_MyPOS extends Mage_Payment_Model_Method_Abstract
{
    const PAYMENT_METHOD_CODE = 'pfg_mypos';

    protected $_code = self::PAYMENT_METHOD_CODE;

    protected $_canCapture = true;

    protected $_canRefund = true;

    protected $_canRefundInvoicePartial = true;

    protected $_formBlockType = 'pfg_mypos/checkout_form';

    public function getOrderPlaceRedirectUrl()
    {
        return Mage::getUrl('pfg_mypos/request/redirect');
    }

    /**
     * Override canUseForCountry to add logging
     *
     * @param string $country
     * @return bool
     */
    public function canUseForCountry($country)
    {
        // Get allowed countries from config
        $allowSpecific = $this->getConfigData('allowspecific');
        error_log('Allow specific countries: ' . ($allowSpecific ? 'Yes' : 'No'), 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');

        if (!$allowSpecific) {
            error_log('All countries allowed', 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');
            return true;
        }

        $specificCountries = $this->getConfigData('specificcountry');
        error_log('Specific countries: ' . $specificCountries, 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');
        error_log('Current country: ' . $country, 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');

        $specificCountries = explode(',', $specificCountries);
        $result = in_array($country, $specificCountries);
        error_log('Country is allowed: ' . ($result ? 'Yes' : 'No'), 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');

        return $result;
    }

    /**
     * Override canUseForCurrency to add logging
     *
     * @param string $currencyCode
     * @return bool
     */
    public function canUseForCurrency($currencyCode)
    {
        error_log('Checking if currency is allowed: ' . $currencyCode, 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');

        // Get allowed currencies from config
        $allowedCurrencies = $this->getConfigData('currency');
        error_log('Allowed currencies: ' . $allowedCurrencies, 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');

        if (!$allowedCurrencies) {
            $allowedCurrencies = Mage::getStoreConfig('payment/pfg_mypos/currency');
            error_log('Allowed currencies from store config: ' . $allowedCurrencies, 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');
        }

        $allowedCurrencies = explode(',', $allowedCurrencies);
        $result = in_array($currencyCode, $allowedCurrencies);
        error_log('Currency is allowed: ' . ($result ? 'Yes' : 'No'), 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');

        return $result;
    }

    public function getInstructions()
    {
        return trim($this->getConfigData('instructions'));
    }

    /**
     * Override isAvailable to add logging
     *
     * @param Mage_Sales_Model_Quote|null $quote
     * @return bool
     */
    public function isAvailable($quote = null)
    {
        // Log that we're checking availability
        error_log('Checking availability of payment method: ' . $this->getCode(), 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');

        // Check if the method is active in config
        $isActive = (bool)(int)$this->getConfigData('active', $quote ? $quote->getStoreId() : null);
        error_log('Payment method active in config: ' . ($isActive ? 'Yes' : 'No'), 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');

        if (!$isActive) {
            return false;
        }

        // Create a result object for the event
        $checkResult = new StdClass;
        $checkResult->isAvailable = $isActive;
        $checkResult->isDeniedInConfig = !$isActive;

        // Dispatch the event
        Mage::dispatchEvent('payment_method_is_active', array(
            'result'          => $checkResult,
            'method_instance' => $this,
            'quote'           => $quote,
        ));

        error_log('Payment method available after event: ' . ($checkResult->isAvailable ? 'Yes' : 'No'), 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');

        // If still available and we have a quote, check if applicable to quote
        if ($checkResult->isAvailable && $quote) {
            $isApplicable = $this->isApplicableToQuote($quote, self::CHECK_RECURRING_PROFILES);
            error_log('Payment method applicable to quote: ' . ($isApplicable ? 'Yes' : 'No'), 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');
            $checkResult->isAvailable = $isApplicable;
        }

        error_log('Final availability of payment method: ' . ($checkResult->isAvailable ? 'Yes' : 'No'), 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');
        return $checkResult->isAvailable;
    }

    /**
     * Override isApplicableToQuote to add logging
     *
     * @param Mage_Sales_Model_Quote $quote
     * @param int|null $checksBitMask
     * @return bool
     */
    public function isApplicableToQuote($quote, $checksBitMask)
    {
        error_log('Checking if payment method is applicable to quote', 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');

        if ($checksBitMask & self::CHECK_USE_FOR_COUNTRY) {
            $canUseForCountry = $this->canUseForCountry($quote->getBillingAddress()->getCountry());
            error_log('Can use for country: ' . ($canUseForCountry ? 'Yes' : 'No'), 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');
            if (!$canUseForCountry) {
                return false;
            }
        }

        if ($checksBitMask & self::CHECK_USE_FOR_CURRENCY) {
            $canUseForCurrency = $this->canUseForCurrency($quote->getStore()->getBaseCurrencyCode());
            error_log('Can use for currency: ' . ($canUseForCurrency ? 'Yes' : 'No'), 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');
            if (!$canUseForCurrency) {
                return false;
            }
        }

        if ($checksBitMask & self::CHECK_USE_CHECKOUT) {
            $canUseCheckout = $this->canUseCheckout();
            error_log('Can use checkout: ' . ($canUseCheckout ? 'Yes' : 'No'), 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');
            if (!$canUseCheckout) {
                return false;
            }
        }

        if ($checksBitMask & self::CHECK_USE_FOR_MULTISHIPPING) {
            $canUseForMultishipping = $this->canUseForMultishipping();
            error_log('Can use for multishipping: ' . ($canUseForMultishipping ? 'Yes' : 'No'), 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');
            if (!$canUseForMultishipping) {
                return false;
            }
        }

        if ($checksBitMask & self::CHECK_USE_INTERNAL) {
            $canUseInternal = $this->canUseInternal();
            error_log('Can use internal: ' . ($canUseInternal ? 'Yes' : 'No'), 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');
            if (!$canUseInternal) {
                return false;
            }
        }

        if ($checksBitMask & self::CHECK_ORDER_TOTAL_MIN_MAX) {
            $total = $quote->getBaseGrandTotal();
            $minTotal = $this->getConfigData('min_order_total');
            $maxTotal = $this->getConfigData('max_order_total');
            error_log('Order total: ' . $total . ', Min: ' . $minTotal . ', Max: ' . $maxTotal, 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');
            if (!empty($minTotal) && $total < $minTotal || !empty($maxTotal) && $total > $maxTotal) {
                error_log('Order total not within min/max limits', 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');
                return false;
            }
        }

        if ($checksBitMask & self::CHECK_RECURRING_PROFILES) {
            $canManageRecurringProfiles = $this->canManageRecurringProfiles();
            $hasRecurringItems = $quote->hasRecurringItems();
            error_log('Can manage recurring profiles: ' . ($canManageRecurringProfiles ? 'Yes' : 'No') . ', Has recurring items: ' . ($hasRecurringItems ? 'Yes' : 'No'), 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');
            if (!$canManageRecurringProfiles && $hasRecurringItems) {
                return false;
            }
        }

        if ($checksBitMask & self::CHECK_ZERO_TOTAL) {
            $total = $quote->getBaseSubtotal() + $quote->getShippingAddress()->getBaseShippingAmount();
            error_log('Zero total check: Total = ' . $total, 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');
            if ($total < 0.0001 && $this->getCode() != 'free'
                && !($this->canManageRecurringProfiles() && $quote->hasRecurringItems())
            ) {
                error_log('Zero total not allowed for this payment method', 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');
                return false;
            }
        }

        error_log('Payment method is applicable to quote', 3, Mage::getBaseDir('var') . DS . 'log' . DS . 'payment.log');
        return true;
    }

    public function refund(Varien_Object $payment, $amount)
    {
        $myposHelper = Mage::helper('pfg_mypos/mypos');
        $order = $payment->getOrder();
        if (!is_object($order) || !$order->getId()) {
            Mage::throwException($myposHelper->__('Could not get order from payment.'));
        }

        $refundRequest = $myposHelper->getRefundRequest($order, $amount);
        $refundResult = $refundRequest->process();

        // Create config for response
        $config = new Mypos\IPC\Config();
        $config->setPrivateKey($myposHelper->getMyPOSSign()->getPrivateKey());
        $config->setKeyIndex($myposHelper->getMyPOSSign()->getKeyIndex());

        // Create response object
        $myposResponse = new MyPOSResponse($config);
        $myposResponse->setResponse($refundResult);

        $responseCode = $myposResponse->getStatusCode();
        if ($responseCode != '00') {
            Mage::throwException($myposResponse->getStatusMessage());
        }

        return $this;
    }
}
