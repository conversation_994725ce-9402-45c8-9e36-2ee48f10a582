<?php

// Load IPC autoloader
require_once Mage::getModuleDir('local', 'PFG_MyPOS') . DS . 'IPC' . DS . 'autoload.php';

use Mypos\IPC\ResponseWrapper as MyPOSResponse;

class PFG_MyPOS_Model_Cron extends Mage_Core_Model_Abstract
{
    public function checkOrdersPaymentStatuses()
    {
        Mage::log('start order processing', null, "pfg_mypos_cron.log", true);
        $myposHelper = Mage::helper('pfg_mypos/mypos');
        $myposSalesHelper = Mage::helper('pfg_mypos/sales');
        $newOrderStatus = Mage::getModel('sales/order')->getCollection()
            ->addAttributeToFilter('status', $myposSalesHelper->getNewOrderStatusCode());
        $newOrderStatus->getSelect()
            ->joinInner(
                ['payments_table' => Mage::getSingleton('core/resource')->getTableName('sales/order_payment')],
                '(main_table.entity_id = payments_table.entity_id)',
                ['method']
            );
        $newOrderStatus->getSelect()->where('payments_table.method = ?', PFG_MyPOS_Model_Payment_MyPOS::PAYMENT_METHOD_CODE);

        $ordersToCheck = Mage::getSingleton('core/resource')->getConnection('core_read')
            ->fetchAll($newOrderStatus->getSelect());

        Mage::log('ordersToCheck gotten', null, "pfg_mypos_cron.log", true);
        if (!is_array($ordersToCheck) || empty($ordersToCheck)) {
            $myposHelper->getLogger()->log($myposHelper->__('No orders to process'));
            return;
        }

        $restClientHelper = Mage::helper('pfg_mypos/restClient_client');
        foreach ($ordersToCheck as $singleOrderToCheck) {
            $orderId = isset($singleOrderToCheck['entity_id']) ? $singleOrderToCheck['entity_id'] : false;
            if (!$orderId) {
                Mage::log("Order ID: $orderId skipped", null, "pfg_mypos_cron.log", true);
                continue;
            }

            Mage::log("process: $orderId", null, "pfg_mypos_cron.log", true);
            $statusCheckRequest = $myposHelper->getStatusCheckRequest($orderId);
            /** @var \Psr\Http\Message\ResponseInterface $statusCheckResult */
            $statusCheckResult = $restClientHelper->postToMyPOS($statusCheckRequest->toPostData());
            Mage::log("order: $orderId responded", null, "pfg_mypos_cron.log", true);
            $myposResponse = MyPOSResponse::withPost($statusCheckResult);
//            $responseCode = $myposHelper->getStatusCheckResponseResult($statusCheckResult); // @fixme - uncomment me
            $responseCode = $myposHelper->getStatusCheckResponseResult($statusCheckResult, true); // @fixme - delete me
            Mage::log("order: $orderId response processed", null, "pfg_mypos_cron.log", true);
            if ($responseCode == '00') {
                Mage::log("Order Order ID: $orderId response: $responseCode", null, "pfg_mypos_cron.log", true);
                Mage::helper('pfg_mypos/sales_order')->processOrderSuccess($orderId, $myposResponse);
            } else {
                if (in_array($responseCode, MyPOSResponse::RESPONSE_CODES_FAILURE)) {
                    Mage::helper('pfg_mypos/sales_order')->processOrderFailed($orderId, $myposResponse, $responseCode);
                } else {
                    Mage::log("Order Order ID: $orderId response: $responseCode", null, "pfg_mypos_cron.log", true);
                    Mage::log($myposResponse->getStatusMessage(), null, "pfg_mypos_cron.log", true);
                }
                // @todo - if hold -> show status in order screen
            }
        }
        Mage::log(".......................end .......................", null, "pfg_mypos_cron.log", true);
    }
}
