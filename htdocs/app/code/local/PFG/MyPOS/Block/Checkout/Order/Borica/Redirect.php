<?php

class pfg_mypos_Block_Checkout_Order_MyPOS_Redirect extends Mage_Core_Block_Template
{
    protected function _construct()
    {
        parent::_construct();
        $this->setTemplate('pfg/mypos/checkout/order/mypos/redirect.phtml');
    }

    protected function getOrder()
    {
        $order = Mage::registry('pfg_mypos_current_order');
        if (!is_object($order) || !$order->getId()) {
            $order = Mage::getSingleton('checkout/session')->getLastRealOrder();
            if (!is_object($order) || !$order->getId()) {
                $orderId = Mage::getSingleton('checkout/session')->getLastOrderId();
                $order = Mage::getModel('sales/order')->load($orderId);
            }
        }

        if (!is_object($order) || !$order->getId()) {
            Mage::throwException($this->__('Could not get order to process. Please contact administrator.'));
        }

        try {
            Mage::register('pfg_mypos_current_order', $order, true);
        } catch (Exception $e) {
            // do nothing
        }

        return $order;
    }

    public function getFormFields()
    {
        $salesRequest = Mage::helper('pfg_mypos/mypos')->getSalesRequest($this->getOrder());
        if (!is_object($salesRequest)) {
            Mage::throwException($this->__('Could not generate sales request. Please contact administrator.'));
        }

        $postData = $salesRequest->toPostData();

        Mage::helper('pfg_mypos')->getLogger()->log('######################################');
        Mage::helper('pfg_mypos')->getLogger()->log('### Logging data to be sent: START ###');
        Mage::helper('pfg_mypos')->getLogger()->log('######################################');
        Mage::helper('pfg_mypos')->getLogger()->log("SSL ERROR: " . openssl_error_string());
        Mage::helper('pfg_mypos')->getLogger()->log($postData);
        Mage::helper('pfg_mypos')->getLogger()->log('####################################');
        Mage::helper('pfg_mypos')->getLogger()->log('### Logging data to be sent: END ###');
        Mage::helper('pfg_mypos')->getLogger()->log('####################################');

        return $postData;
    }

    public function getFormHtml()
    {
        return Mage::helper('pfg_mypos/mypos')->getSalesRequestForm($this->getOrder());
    }
}