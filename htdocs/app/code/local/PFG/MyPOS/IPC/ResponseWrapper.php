<?php

namespace Mypos\IPC;

/**
 * Wrapper for the Response class to provide the setResponse method
 */
class ResponseWrapper
{
    /**
     * Response codes that indicate failure
     */
    const RESPONSE_CODES_FAILURE = ['01', '04', '05', '06', '07', '12', '14', '15', '33', '41', '43', '51', '54', '57', '58', '76', '77', '78', '91', '94', '96', 'N7', 'N8'];

    /**
     * Static method to create a ResponseWrapper instance from POST data
     *
     * @param array $postData
     * @return ResponseWrapper
     */
    public static function withPost($postData)
    {
        $config = new Config();
        $instance = new self($config);
        $instance->setResponse($postData);
        return $instance;
    }
    /**
     * @var Config
     */
    private $config;

    /**
     * @var Response
     */
    private $response;

    /**
     * Constructor
     *
     * @param Config $config
     */
    public function __construct(Config $config)
    {
        $this->config = $config;
    }

    /**
     * Set the response data
     *
     * @param array|string $data
     * @return $this
     */
    public function setResponse($data)
    {
        try {
            $this->response = new Response($this->config, $data, Defines::COMMUNICATION_FORMAT_POST);
        } catch (\Exception $e) {
            // Log the exception
            error_log('Error creating Response: ' . $e->getMessage(), 3, dirname(dirname(dirname(dirname(dirname(dirname(__FILE__)))))) . '/var/log/payment.log');
        }
        return $this;
    }

    /**
     * Get the status code
     *
     * @return string
     */
    public function getStatusCode()
    {
        if (!$this->response) {
            return '';
        }

        try {
            return $this->response->getStatus();
        } catch (\Exception $e) {
            error_log('Error getting status: ' . $e->getMessage(), 3, dirname(dirname(dirname(dirname(dirname(dirname(__FILE__)))))) . '/var/log/payment.log');
            return '';
        }
    }

    /**
     * Get the status message
     *
     * @return string
     */
    public function getStatusMessage()
    {
        if (!$this->response) {
            return '';
        }

        try {
            return $this->response->getStatusMsg();
        } catch (\Exception $e) {
            error_log('Error getting status message: ' . $e->getMessage(), 3, dirname(dirname(dirname(dirname(dirname(dirname(__FILE__)))))) . '/var/log/payment.log');
            return '';
        }
    }

    /**
     * Validate the response
     *
     * @return bool
     */
    public function validate()
    {
        if (!$this->response) {
            return false;
        }

        try {
            return $this->response->isSignatureCorrect();
        } catch (\Exception $e) {
            error_log('Error validating signature: ' . $e->getMessage(), 3, dirname(dirname(dirname(dirname(dirname(dirname(__FILE__)))))) . '/var/log/payment.log');
            return false;
        }
    }

    /**
     * Get the response data
     *
     * @return array
     */
    public function getData()
    {
        if (!$this->response) {
            return [];
        }

        try {
            return $this->response->getData();
        } catch (\Exception $e) {
            error_log('Error getting data: ' . $e->getMessage(), 3, dirname(dirname(dirname(dirname(dirname(dirname(__FILE__)))))) . '/var/log/payment.log');
            return [];
        }
    }
}
