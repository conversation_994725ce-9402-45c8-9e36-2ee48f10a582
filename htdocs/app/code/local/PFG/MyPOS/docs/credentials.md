# Adding MyPOS IPC Credentials

This document provides instructions on how to add your MyPOS IPC credentials to the Magento configuration.

## Required Credentials

For the MyPOS IPC integration, you need the following credentials:

1. **Client Number (Merchant ID)** - This is your unique merchant identifier provided by MyPOS
2. **Client Secret/Code (Terminal ID)** - This is your terminal identifier provided by MyPOS
3. **Key Index** - This is a numeric value provided by MyPOS for the IPC integration

## Adding Credentials in Magento Admin Panel

Follow these steps to add your credentials:

1. Log in to your Magento Admin Panel
2. Go to **System > Configuration**
3. In the left sidebar, click on **Payment Methods**
4. Find and expand the **PFG MyPOS** section
5. Enter your credentials in the following fields:
   - **Merchant ID**: Enter your Client Number here
   - **Terminal ID**: Enter your Client Secret/Code here
   - **Key Index**: Enter the Key Index value provided by MyPOS
6. Make sure the **Enabled** field is set to **Yes**
7. Configure other settings as needed (e.g., sandbox mode, currency, etc.)
8. Click **Save Config** to save your changes

## Verifying the Configuration

After saving your configuration, you can verify that the credentials are correctly set up by:

1. Making a test purchase on your website
2. Checking the MyPOS transaction logs in your MyPOS merchant account
3. Verifying that the transaction appears correctly in both systems

If you encounter any issues, please check that:
- All credentials are entered correctly
- The private key and certificate files are properly uploaded and configured
- The sandbox mode setting matches your intended environment (development or production)

## Additional Information

The MyPOS IPC integration uses these credentials to authenticate your requests to the MyPOS payment gateway. The credentials are used in the following way:

- **Merchant ID (Client Number)**: Used as the SID parameter in API requests
- **Terminal ID (Client Secret/Code)**: Used as the wallet parameter in API requests
- **Key Index**: Used for signing API requests

For more information about the MyPOS IPC integration, please refer to the MyPOS documentation or contact MyPOS support.