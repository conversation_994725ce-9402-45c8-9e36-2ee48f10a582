<?xml version="1.0"?>
<config>
<tabs>
		<bitware translate="label" module="bitware_speedengineDeferJS">
            <label> Bitware Extensions</label>
            <sort_order>100</sort_order>
        </bitware>
</tabs>
    <sections>
		<bitware translate="label" module="bitware_speedengineDeferJS">
            <label>Extension Options</label>
            <tab>bitware</tab>
            <sort_order>1000</sort_order>
            <show_in_default>1</show_in_default>
            <show_in_website>1</show_in_website>
            <show_in_store>1</show_in_store>
			
				<groups>
				<bitware_group translate="label" module="bitware_speedengineDeferJS">
                    <label>Extension Options</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>1000</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
 
                    <fields>
                        
                        <bitware_enabled translate="label">
                            <label>Move JavaScript to Footer</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </bitware_enabled>
                        <bitware_excluded_blocks translate="label comment">
                            <label>Bitware Excluded Blocks</label>
                            <frontend_type>textarea</frontend_type>
                            <comment><![CDATA[Here enter block names separated with a comma. All JS from these blocks will stay untouched and will not be moved to footer.<br/><strong>Note:</strong> JS libraries are moved to footer, so please be careful when you are excluding JS, which has dependencies.]]></comment>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </bitware_excluded_blocks>
                        <bitware_excluded_links translate="label comment">
                            <label>Bitware Excluded Files</label>
                            <frontend_type>textarea</frontend_type>
                            <comment><![CDATA[Here enter script(js) files separated with a comma. All includes for these files will stay untouched and will not be moved to footer.]]></comment>
                            <sort_order>31</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </bitware_excluded_links>
                    </fields>
                </bitware_group>
				</groups>
			
		</bitware>
    </sections>
</config>
