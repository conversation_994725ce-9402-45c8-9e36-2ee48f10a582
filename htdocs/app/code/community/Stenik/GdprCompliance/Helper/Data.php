<?php
/**
 * @package Stenik_GdprCompliance
 * <AUTHOR> Magento Team <<EMAIL>>
 */

class Stenik_GdprCompliance_Helper_Data extends Mage_Core_Helper_Abstract
{

    const XML_PATH_GENERAL_ENABLED                           = 'stenik_gdprcompliance/general/enabled';
    const XML_PATH_GENERAL_DASHBOARD_TITLE                   = 'stenik_gdprcompliance/general/dashboard_title';
    const XML_PATH_GENERAL_DASHBOARD_CONTENT                 = 'stenik_gdprcompliance/general/dashboard_content';
    const XML_PATH_GENERAL_REDIRECT_TO_REVIEW                = 'stenik_gdprcompliance/general/redirect_to_review';
    const XML_PATH_GENERAL_REDIRECT_NOTICE                   = 'stenik_gdprcompliance/general/redirect_notice';
    const XML_PATH_GENERAL_CUSTOMER_ACCOUNT_NAVIGATION_TITLE = 'stenik_gdprcompliance/general/customer_account_navigation_title';
    const XML_PATH_GENERAL_REVIEW_TITLE                      = 'stenik_gdprcompliance/general/review_title';
    const XML_PATH_GENERAL_REVIEW_CONTENT                    = 'stenik_gdprcompliance/general/review_content';

    const XML_PATH_GENERAL_TERMS_CMS_PAGE_ID                 = 'stenik_gdprcompliance/general/terms_cms_page_id';
    const XML_PATH_GENERAL_PRIVACY_CMS_PAGE_ID               = 'stenik_gdprcompliance/general/privacy_policy_cms_page_id';


    /**
     * [isEnabled description]
     *
     * @param  [type]  $storeId [description]
     * @return boolean          [description]
     */
    public function isEnabled($storeId = null)
    {
        return Mage::getStoreConfigFlag(self::XML_PATH_GENERAL_ENABLED, $storeId);
    }

    /**
     * [getDashboardTitle description]
     *
     * @param  [type] $storeId [description]
     * @return [type]          [description]
     */
    public function getDashboardTitle($storeId = null)
    {
        return Mage::getStoreConfig(self::XML_PATH_GENERAL_DASHBOARD_TITLE, $storeId);
    }

    /**
     * [getDashboardContent description]
     *
     * @param  [type] $storeId [description]
     * @return [type]          [description]
     */
    public function getDashboardContent($storeId = null)
    {
        return Mage::getStoreConfig(self::XML_PATH_GENERAL_DASHBOARD_CONTENT, $storeId);
    }

    /**
     * [isEnabled description]
     *
     * @param  [type]  $storeId [description]
     * @return boolean          [description]
     */
    public function getRedirectToReview($storeId = null)
    {
        return Mage::getStoreConfigFlag(self::XML_PATH_GENERAL_REDIRECT_TO_REVIEW, $storeId);
    }

    /**
     * [getRedirectNotice description]
     *
     * @param  [type] $storeId [description]
     * @return [type]          [description]
     */
    public function getRedirectNotice($storeId = null)
    {
        return Mage::getStoreConfig(self::XML_PATH_GENERAL_REDIRECT_NOTICE, $storeId);
    }

    /**
     * [getReviewTitle description]
     *
     * @param  [type] $storeId [description]
     * @return [type]          [description]
     */
    public function getReviewTitle($storeId = null)
    {
        return Mage::getStoreConfig(self::XML_PATH_GENERAL_REVIEW_TITLE, $storeId);
    }

    /**
     * [getReviewContent description]
     *
     * @param  [type] $storeId [description]
     * @return [type]          [description]
     */
    public function getReviewContent($storeId = null)
    {
        return Mage::getStoreConfig(self::XML_PATH_GENERAL_REVIEW_CONTENT, $storeId);
    }

    /**
     * [getCustomerNavigationTitle description]
     *
     * @param  [type] $storeId [description]
     * @return [type]          [description]
     */
    public function getCustomerAccountNavigationTitle($storeId = null)
    {
        return Mage::getStoreConfig(self::XML_PATH_GENERAL_CUSTOMER_ACCOUNT_NAVIGATION_TITLE, $storeId);
    }

    /**
     * Check if user has given consent with GDPR Rules
     *
     * @param  Mage_Customer_Model_Customer $customer [description]
     * @return boolean                                [description]
     */
    public function hasConsent(Mage_Customer_Model_Customer $customer)
    {
        // Terms and Privacy check
        if (Mage::helper('stenik_gdprcompliance/agreement_termsAndPrivacy')->isEnabled()) {
            if ($customer->getGdprTermsAndPrivacy() != 1) {
                return false;
            }
        }

        // Privacy Policy check
        if (Mage::helper('stenik_gdprcompliance/agreement_privacyPolicy')->isEnabled()) {
            if ($customer->getGdprPrivacyPolicy() != 1) {
                return false;
            }
        }

        // Age Check
        if (Mage::helper('stenik_gdprcompliance/agreement_age')->isEnabled()) {
            if ($customer->getGdprAge() != 1) {
                return false;
            }
        }

        return true;
    }

    /**
     * Log action entry into history table
     *
     * @param  Mage_Customer_Model_Customer $customer        [description]
     * @param  int                          $agreementTypeId [description]
     * @param  string                       $agreement       [description]
     * @param  string                       $action          [description]
     * @return boolean                                       [description]
     */
    public function logHistory(Mage_Customer_Model_Customer $customer, $agreementTypeId, $agreement, $action)
    {
        $history = Mage::getModel('stenik_gdprcompliance/history');

        $history->setCustomerId($customer->getId());
        $history->setEmail($customer->getEmail());
        $history->setAgreementTypeId($agreementTypeId);
        $history->setAgreement($agreement);
        $history->setAction($action);
        $history->setIpAddress(Mage::helper('core/http')->getRemoteAddr());
        $history->setUserAgent(Mage::helper('core/http')->getHttpUserAgent());
        $history->save();
        return true;
    }

    /**
     * [getTermsPageId description]
     *
     * @param  [type] $storeId [description]
     * @return [type]          [description]
     */
    public function getTermsPageId($storeId = null)
    {
        return Mage::getStoreConfig(self::XML_PATH_GENERAL_TERMS_CMS_PAGE_ID, $storeId);
    }

    /**
     * [getTermsPageLink description]
     *
     * @param  [type] $storeId [description]
     * @return [type]          [description]
     */
    public function getTermsPageLink($storeId = null)
    {
        return Mage::helper('cms/page')->getPageUrl($this->getTermsPageId($storeId));
    }

    /**
     * [getTermsPageTitle description]
     *
     * @param  [type] $storeId [description]
     * @return [type]          [description]
     */
    public function getTermsPageTitle($storeId = null)
    {
        return $this->getPageTitle($this->getTermsPageId($storeId), $storeId);
    }

    /**
     * [getTermsPageContent description]
     *
     * @param  [type] $storeId [description]
     * @return [type]          [description]
     */
    public function getTermsPageContent($storeId = null)
    {
        return $this->getPageContent($this->getTermsPageId($storeId), $storeId);
    }

    /**
     * [getPrivacyPolicyPageId description]
     *
     * @param  [type] $storeId [description]
     * @return [type]          [description]
     */
    public function getPrivacyPolicyPageId($storeId = null)
    {
        return Mage::getStoreConfig(self::XML_PATH_GENERAL_PRIVACY_CMS_PAGE_ID, $storeId);
    }

    /**
     * [getPrivacyPolicyPageLink description]
     *
     * @param  [type] $storeId [description]
     * @return [type]          [description]
     */
    public function getPrivacyPolicyPageLink($storeId = null)
    {
        return Mage::helper('cms/page')->getPageUrl($this->getPrivacyPolicyPageId($storeId));
    }

    /**
     * [getPrivacyPolicyPageTitle description]
     *
     * @param  [type] $storeId [description]
     * @return [type]          [description]
     */
    public function getPrivacyPolicyPageTitle($storeId = null)
    {
        return $this->getPageTitle($this->getPrivacyPolicyPageId($storeId), $storeId);
    }

    /**
     * [getPrivacyPolicyPageContent description]
     *
     * @param  [type] $storeId [description]
     * @return [type]          [description]
     */
    public function getPrivacyPolicyPageContent($storeId = null)
    {
        return $this->getPageContent($this->getPrivacyPolicyPageId($storeId), $storeId);
    }

    /**
     * [getTermsPageContent description]
     *
     * @param  [type] $storeId [description]
     * @return [type]          [description]
     */
    public function getPageTitle($id, $storeId = null)
    {
        $page = Mage::getModel('cms/page');
        if ($storeId) {
            $page->setStoreId($storeId);
        } else {
            $page->setStoreId(Mage::app()->getStore()->getId());
        }
        $page->load($id);
        $html = '';
        if ($page && $page->getId()) {
            return $page->getTitle();
        }
        return $html;
    }

    /**
     * [getPageContent description]
     *
     * @param  [type] $storeId [description]
     * @return [type]          [description]
     */
    public function getPageContent($id, $storeId = null)
    {
        $page = Mage::getModel('cms/page');
        if ($storeId) {
            $page->setStoreId($storeId);
        } else {
            $page->setStoreId(Mage::app()->getStore()->getId());
        }
        $page->load($id);
        $html = '';
        if ($page && $page->getId()) {
            $helper = Mage::helper('cms');
            $processor = $helper->getPageTemplateProcessor();
            $html = $processor->filter($page->getContent());
        }
        return $html;
    }

    /**
     * Get the redirect URL.
     *
     * @return string
     */
    public function getUrl()
    {
        $referer = $this->_getRefererUrl();

        if (! empty($referer)) {
            return $referer;
        }

        if ($this->_session->hasLastUrl()) {
            return $this->_session->getLastUrl();
        }

        return $this->getRequestUri();
    }

    /**
     * Identify referer url via all accepted methods:
     *  - HTTP_REFERER
     *  - Regular
     *  - base64-encoded request param
     *
     * @return string
     */
    protected function _getRefererUrl()
    {
        $request = Mage::app()->getRequest();

        $refererUrl = $request->getServer('HTTP_REFERER');
        if ($url = $request->getParam(Mage_Core_Controller_Front_Action::PARAM_NAME_REFERER_URL)) {
            $refererUrl = $url;
        }
        if ($url = $request->getParam(Mage_Core_Controller_Front_Action::PARAM_NAME_BASE64_URL)) {
            $refererUrl = Mage::helper('core')->urlDecode($url);
        }
        if ($url = $request->getParam(Mage_Core_Controller_Front_Action::PARAM_NAME_URL_ENCODED)) {
            $refererUrl = Mage::helper('core')->urlDecode($url);
        }

        return $refererUrl;
    }
}
