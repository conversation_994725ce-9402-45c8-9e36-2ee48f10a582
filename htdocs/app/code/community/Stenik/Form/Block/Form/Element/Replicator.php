<?php
/**
 * Form element - replicator
 *
 * @package Stenik_Form
 * <AUTHOR> Magento Team <<EMAIL>>
 */

class Stenik_Form_Block_Form_Element_Replicator extends Varien_Data_Form_Element_Link
{
    /**
     * Init.
     *
     * @return void
     */
    protected function _construct()
    {
        parent::_construct();
        $this->setHref('javascript:;');
        $this->setOnclick($this->_escape(sprintf('return Stenik.replicate(%s, %s)',
            json_encode($this->getTargetElement()),
            json_encode(array('add_remove_replication_link' => (bool) (int) $this->getAddRemoveReplicationLink()))
        )));
    }

    /**
     * Retrieve value
     *
     * @return string|null
     */
    public function getValue($index = null)
    {
        return $this->getLabel();
    }

    /**
     * Retrieve html attributes
     *
     * @return array
     */
    public function getHtmlAttributes()
    {
        $htmlAttributes = parent::getHtmlAttributes();
        $htmlAttributes = array_merge($htmlAttributes, array('placeholder')); // Add
        return $htmlAttributes;
    }
}