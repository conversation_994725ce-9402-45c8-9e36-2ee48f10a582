<?php
/**
 * @package  Stenik_Speedy
 * <AUTHOR> Magento Team <<EMAIL>>
 */

class Stenik_Speedy_Block_Adminhtml_Sales_Order_View_Tab_ShippingLabel_List
    extends Mage_Adminhtml_Block_Widget_Grid_Container
    implements Mage_Adminhtml_Block_Widget_Tab_Interface
{
    protected $_controller = 'adminhtml_sales_order_view_tab_shippingLabel_list';
    protected $_blockGroup = 'stenik_speedy';

    /**
     * Init.
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_headerText = $this->getTabTitle();
    }

    protected function _prepareLayout()
    {
        $this->removeButton('add');
        return parent::_prepareLayout();
    }

    /**
     * Retrieve order model instance
     *
     * @return Mage_Sales_Model_Order
     */
    public function getOrder()
    {
        return Mage::registry('current_order');
    }

    /**
     * Retrieve grid url
     *
     * @return string
     */
    public function getGridUrl()
    {
        return $this->getUrl('adminhtml/stenik_speedy_sales_order_view_shippinglabel/listGrid', array('_current' => true));
    }

    /**
     * Get Tab Label
     *
     * @return string
     */
    public function getTabLabel()
    {
        return Mage::helper('stenik_speedy')->__('Speedy Shipping Labels');
    }

    /**
     * Get Tab Title
     *
     * @return string
     */
    public function getTabTitle()
    {
        return Mage::helper('stenik_speedy')->__('Speedy Shipping Labels');
    }

    /**
     * Get Class
     *
     * @return string
     */
    public function getClass()
    {
        return $this->getTabClass();
    }

    /**
     * Can Show Tab
     *
     * @return boolean
     */
    public function canShowTab()
    {
        $order = $this->getOrder();

        if (!$order) {
            return false;
        }

        return true;
    }

    /**
     * Is Hidden
     *
     * @return boolean
     */
    public function isHidden()
    {
        return false;
    }
}