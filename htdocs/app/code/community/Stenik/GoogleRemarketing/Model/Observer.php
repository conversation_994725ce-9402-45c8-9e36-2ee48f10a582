<?php
/**
 * Observer
 *
 * @package Stenik_GoogleRemarketing
 * <AUTHOR> Magento Team <<EMAIL>>
 */

class Stenik_GoogleRemarketing_Model_Observer extends Mage_Core_Model_Observer
{
    /**
     * Collect meta data for facebook
     *
     * @event stenik_googleremarketing_collect_page_data
     *
     * @param Varien_Event_Observer $observer
     * @return void
     */
    public function collectPageData(Varien_Event_Observer $observer)
    {
        $dataObject = $observer->getEvent()->getDataObject();

        if (!$dataObject || $dataObject->getIsPageDataCollected()) {
            return;
        }

        $pageIdentifier = Mage::app()->getFrontController()->getAction()->getFullActionName();
        /* Collect homepage's page data */
        if (!$dataObject->getIsPageDataCollected()) {
            if ($pageIdentifier == 'cms_index_index') {
                $dataObject->setPageType(Stenik_GoogleRemarketing_Helper_Data::REMARKETING_PAGE_TYPE_HOME);

                    $dataObject->setIsPageDataCollected(true);
            }
        }

        /* Collect searchresults's page data */
        if (!$dataObject->getIsPageDataCollected()) {
            if ($pageIdentifier == 'catalogsearch_result_index') {
                $dataObject->setPageType(Stenik_GoogleRemarketing_Helper_Data::REMARKETING_PAGE_TYPE_SEARCHRESULTS);

                    $dataObject->setIsPageDataCollected(true);
            }
        }


        /* Collect product's page data */
        if (!$dataObject->getIsPageDataCollected()) {
            if (Mage::registry('current_product') instanceof Mage_Catalog_Model_Product) {
                $_product = Mage::registry('current_product');

                $dataObject->setItemId($_product->getSku())
                    ->setPageType(Stenik_GoogleRemarketing_Helper_Data::REMARKETING_PAGE_TYPE_OFFERDETAIL)
                    ->setTotalValue($_product->getFinalPrice());

                    $dataObject->setIsPageDataCollected(true);
            }
        }

        /* Collect cart/checkout page data */
        if (!$dataObject->getIsPageDataCollected()) {
            if ($pageIdentifier == 'checkout_cart_index'
                || $pageIdentifier == 'opc_index_index'
                || $pageIdentifier == 'checkout_index_index') {
                $cart = Mage::getModel('checkout/cart')->getQuote();

                $items = array();
                foreach ($cart->getAllVisibleItems() as $_item) {
                    if (!in_array($_item->getProduct()->getData('sku'), $items)) {
                        $items[] = $_item->getProduct()->getData('sku');
                    }
                }

                $dataObject->setPageType(Stenik_GoogleRemarketing_Helper_Data::REMARKETING_PAGE_TYPE_CONVERSIONINTENT);

                if (!empty($items)) {
                    if (count($items) > 1) {
                        $formattedSkus = '';
                        foreach ($items as $itemSku) {
                            if ($formattedSkus != '') {
                                $formattedSkus .= ', ';
                            }

                            $formattedSkus .= '"' . $itemSku . '"';
                        }

                        $dataObject->setItemId('[' . $formattedSkus . ']');
                    } else {
                        $dataObject->setItemId('"' . $items[0] . '"');
                    }
                }

                if ($cart->getGrandTotal()) {
                    $dataObject->setTotalValue($cart->getGrandTotal());
                }

                $dataObject->setIsPageDataCollected(true);
            }
        }

        /* Collect success page data */
        if (!$dataObject->getIsPageDataCollected()) {
            if ($pageIdentifier == 'checkout_onepage_success') {
                $_order = Mage::getModel('sales/order')->loadByIncrementId(Mage::getSingleton('checkout/session')->getLastRealOrderId());

                $items = array();
                foreach ($_order->getAllVisibleItems() as $_item) {
                    if (!in_array($_item->getProduct()->getData('sku'), $items)) {
                        $items[] = $_item->getProduct()->getData('sku');
                    }
                }

                $dataObject->setPageType(Stenik_GoogleRemarketing_Helper_Data::REMARKETING_PAGE_TYPE_CONVERSION);

                if (!empty($items)) {
                    if (count($items) > 1) {
                        $formattedSkus = '';
                        foreach ($items as $itemSku) {
                            if ($formattedSkus != '') {
                                $formattedSkus .= ', ';
                            }

                            $formattedSkus .= '"' . $itemSku . '"';
                        }

                        $dataObject->setItemId('[' . $formattedSkus . ']');
                    } else {
                        $dataObject->setItemId('"' . $items[0] . '"');
                    }
                }

                $dataObject->setTotalValue($_order->getBaseGrandTotal() - $_order->getShippingAmount() - $_order->getBaseTaxAmount());

                $dataObject->setIsPageDataCollected(true);
            }
        }

        /* Collect others page data */
        if (!$dataObject->getIsPageDataCollected()) {
            $dataObject->setPageType(Stenik_GoogleRemarketing_Helper_Data::REMARKETING_PAGE_TYPE_OTHER);

            $dataObject->setIsPageDataCollected(true);
        }
    }
}
