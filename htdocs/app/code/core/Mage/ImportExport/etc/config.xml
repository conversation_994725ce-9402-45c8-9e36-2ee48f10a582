<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_ImportExport
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<config>
    <modules>
        <Mage_ImportExport>
            <version>1.6.0.2</version>
        </Mage_ImportExport>
    </modules>
    <global>
        <models>
            <importexport>
                <class>Mage_ImportExport_Model</class>
                <resourceModel>importexport_resource</resourceModel>
            </importexport>
            <importexport_resource>
                <class>Mage_ImportExport_Model_Resource</class>
                <entities>
                    <importdata>
                        <table>importexport_importdata</table>
                    </importdata>
                </entities>
            </importexport_resource>
        </models>
        <blocks>
            <importexport>
                <class>Mage_ImportExport_Block</class>
            </importexport>
        </blocks>
        <helpers>
            <importexport>
                <class>Mage_ImportExport_Helper</class>
            </importexport>
        </helpers>
        <resources>
            <importexport_setup>
                <setup>
                    <module>Mage_ImportExport</module>
                    <class>Mage_ImportExport_Model_Resource_Setup</class>
                </setup>
            </importexport_setup>
        </resources>
        <importexport module="importexport">
            <import_entities>
                <catalog_product translate="label">
                    <model_token>importexport/import_entity_product</model_token>
                    <label>Products</label>
                </catalog_product>
                <customer translate="label">
                    <model_token>importexport/import_entity_customer</model_token>
                    <label>Customers</label>
                </customer>
            </import_entities>
            <export_entities>
                <catalog_product translate="label">
                    <model_token>importexport/export_entity_product</model_token>
                    <label>Products</label>
                </catalog_product>
                <customer translate="label">
                    <model_token>importexport/export_entity_customer</model_token>
                    <label>Customers</label>
                </customer>
            </export_entities>
            <export_file_formats>
                <csv translate="label">
                    <model_token>importexport/export_adapter_csv</model_token>
                    <label>CSV</label>
                </csv>
            </export_file_formats>
            <import_product_types>
                <simple>importexport/import_entity_product_type_simple</simple>
                <configurable>importexport/import_entity_product_type_configurable</configurable>
                <virtual>importexport/import_entity_product_type_simple</virtual>
                <grouped>importexport/import_entity_product_type_grouped</grouped>
            </import_product_types>
            <export_product_types>
                <simple>importexport/export_entity_product_type_simple</simple>
                <configurable>importexport/export_entity_product_type_configurable</configurable>
                <virtual>importexport/export_entity_product_type_simple</virtual>
                <grouped>importexport/export_entity_product_type_grouped</grouped>
            </export_product_types>
            <import>
                <catalog_product>
                    <attributes>
                        <url_key>
                            <backend_model>importexport/product_attribute_backend_urlkey</backend_model>
                        </url_key>
                    </attributes>
                </catalog_product>
            </import>
        </importexport>
    </global>
    <admin>
        <routers>
            <adminhtml>
                <args>
                    <modules>
                        <importexport before="Mage_Adminhtml">Mage_ImportExport_Adminhtml</importexport>
                    </modules>
                </args>
            </adminhtml>
        </routers>
    </admin>
    <adminhtml>
        <layout>
            <updates>
                <importexport>
                    <file>importexport.xml</file>
                </importexport>
            </updates>
        </layout>
    </adminhtml>
    <default>
        <system>
            <export_csv>
                <escaping>1</escaping>
            </export_csv>
            <import_csv>
                <configurable_page_size>1000</configurable_page_size>
            </import_csv>
        </system>
        <general>
            <file>
                <importexport_local_valid_paths>
                    <available>
                        <export_xml>var/export/*/*.xml</export_xml>
                        <export_csv>var/export/*/*.csv</export_csv>
                        <import_xml>var/import/*/*.xml</import_xml>
                        <import_csv>var/import/*/*.csv</import_csv>
                    </available>
                </importexport_local_valid_paths>
                <bunch_size>100</bunch_size>
            </file>
        </general>
    </default>
</config>
