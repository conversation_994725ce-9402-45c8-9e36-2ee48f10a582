<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_Sales
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<config>
    <api>
        <resources>
            <sales_order translate="title" module="sales">
                <model>sales/order_api</model>
                <title>Order API</title>
                <acl>sales/order</acl>
                <methods>
                    <list translate="title" module="sales">
                        <title>Retrieve list of orders by filters</title>
                        <method>items</method>
                        <acl>sales/order/info</acl>
                    </list>
                    <info translate="title" module="sales">
                        <title>Retrieve order information</title>
                        <acl>sales/order/info</acl>
                    </info>
                    <addComment translate="title" module="sales">
                        <title>Add comment to order</title>
                        <acl>sales/order/change</acl>
                    </addComment>
                    <hold translate="title" module="sales">
                        <title>Hold order</title>
                        <acl>sales/order/change</acl>
                    </hold>
                    <unhold translate="title" module="sales">
                        <title>Unhold order</title>
                        <acl>sales/order/change</acl>
                    </unhold>
                    <cancel translate="title" module="sales">
                        <title>Cancel order</title>
                        <acl>sales/order/change</acl>
                    </cancel>
                </methods>
                <faults module="sales">
                    <not_exists>
                        <code>100</code>
                        <message>Requested order not exists.</message>
                    </not_exists>
                    <filters_invalid>
                        <code>101</code>
                        <message>Invalid filters given. Details in error message.</message>
                    </filters_invalid>
                    <data_invalid>
                        <code>102</code>
                        <message>Invalid data given. Details in error message.</message>
                    </data_invalid>
                    <status_not_changed>
                        <code>103</code>
                        <message>Order status not changed. Details in error message.</message>
                    </status_not_changed>
                </faults>
            </sales_order>
            <sales_order_shipment>
                <title>Shipment API</title>
                <model>sales/order_shipment_api</model>
                <acl>sales/order/shipment</acl>
                <methods>
                    <list translate="title" module="sales">
                        <title>Retrieve list of shipments by filters</title>
                        <method>items</method>
                        <acl>sales/order/shipment/info</acl>
                    </list>
                    <info translate="title" module="sales">
                        <title>Retrieve shipment information</title>
                        <acl>sales/order/shipment/info</acl>
                    </info>
                    <sendInfo translate="title" module="sales">
                        <title>Send shipment info</title>
                        <acl>sales/order/shipment/send</acl>
                    </sendInfo>
                    <create translate="title" module="sales">
                        <title>Create new shipment for order</title>
                        <acl>sales/order/shipment/create</acl>
                    </create>
                    <addComment translate="title" module="sales">
                        <title>Add new comment to shipment</title>
                        <acl>sales/order/shipment/comment</acl>
                    </addComment>
                    <addTrack translate="title" module="sales">
                        <title>Add new tracking number</title>
                        <acl>sales/order/shipment/track</acl>
                    </addTrack>
                    <removeTrack translate="title" module="sales">
                        <title>Remove tracking number</title>
                        <acl>sales/order/shipment/track</acl>
                    </removeTrack>
                    <getCarriers>
                        <title>Retrieve list of allowed carriers for order</title>
                    </getCarriers>
                </methods>
                <faults module="sales">
                    <not_exists>
                        <code>100</code>
                        <message>Requested shipment not exists.</message>
                    </not_exists>
                    <filters_invalid>
                        <code>101</code>
                        <message>Invalid filters given. Details in error message.</message>
                    </filters_invalid>
                    <data_invalid>
                        <code>102</code>
                        <message>Invalid data given. Details in error message.</message>
                    </data_invalid>
                    <order_not_exists>
                        <code>103</code>
                        <message>Requested order not exists.</message>
                    </order_not_exists>
                    <track_not_exists>
                        <code>104</code>
                        <message>Requested tracking not exists.</message>
                    </track_not_exists>
                    <track_not_deleted>
                        <code>105</code>
                        <message>Tracking not deleted. Details in error message.</message>
                    </track_not_deleted>
                </faults>
            </sales_order_shipment>
            <sales_order_invoice>
                <title>Invoice API</title>
                <model>sales/order_invoice_api</model>
                <acl>sales/order/invoice</acl>
                <methods>
                    <list translate="title" module="sales">
                        <title>Retrieve list of invoices by filters</title>
                        <method>items</method>
                        <acl>sales/order/invoice/info</acl>
                    </list>
                    <info translate="title" module="sales">
                        <title>Retrieve invoice information</title>
                        <acl>sales/order/invoice/info</acl>
                    </info>
                    <create translate="title" module="sales">
                        <title>Create new invoice for order</title>
                        <acl>sales/order/invoice/create</acl>
                    </create>
                    <addComment translate="title" module="sales">
                        <title>Add new comment to shipment</title>
                        <acl>sales/order/invoice/comment</acl>
                    </addComment>
                    <capture translate="title" module="sales">
                        <title>Capture invoice</title>
                        <acl>sales/order/invoice/capture</acl>
                    </capture>
                    <void translate="title" module="sales">
                        <title>Void invoice</title>
                        <acl>sales/order/invoice/void</acl>
                    </void>
                    <cancel translate="title" module="sales">
                        <title>Cancel invoice</title>
                        <acl>sales/order/invoice/cancel</acl>
                    </cancel>
                </methods>
                <faults module="sales">
                    <not_exists>
                        <code>100</code>
                        <message>Requested invoice does not exist.</message>
                    </not_exists>
                    <invalid_filter>
                        <code>101</code>
                        <message>Invalid filter given. Details in error message.</message>
                    </invalid_filter>
                    <data_invalid>
                        <code>102</code>
                        <message>Invalid data given. Details in error message.</message>
                    </data_invalid>
                    <order_not_exists>
                        <code>103</code>
                        <message>Requested order not exists.</message>
                    </order_not_exists>
                    <status_not_changed>
                        <code>104</code>
                        <message>Invoice status not changed</message>
                    </status_not_changed>
                </faults>
           </sales_order_invoice>
            <sales_order_creditmemo>
                <title>Credit memo API</title>
                <model>sales/order_creditmemo_api</model>
                <acl>sales/order/creditmemo</acl>
                <methods>
                    <list translate="title" module="sales">
                        <title>Retrieve list of credit memos by filters</title>
                        <method>items</method>
                        <acl>sales/order/creditmemo/list</acl>
                    </list>
                    <info translate="title" module="sales">
                        <title>Retrieve credit memo information</title>
                        <acl>sales/order/creditmemo/info</acl>
                    </info>
                    <create translate="title" module="sales">
                        <title>Create new credit memo for order</title>
                        <acl>sales/order/creditmemo/create</acl>
                    </create>
                    <addComment translate="title" module="sales">
                        <title>Add new comment to credit memo</title>
                        <acl>sales/order/creditmemo/comment</acl>
                    </addComment>
                    <cancel translate="title" module="sales">
                        <title>Cancel credit memo</title>
                        <acl>sales/order/creditmemo/cancel</acl>
                    </cancel>
                </methods>
                <faults module="sales">
                    <not_exists>
                        <code>100</code>
                        <message>Requested credit memo does not exist</message>
                    </not_exists>
                    <invalid_filter>
                        <code>101</code>
                        <message>Invalid filter given. Details in error message</message>
                    </invalid_filter>
                    <data_invalid>
                        <code>102</code>
                        <message>Invalid data given. Details in error message</message>
                    </data_invalid>
                    <order_not_exists>
                        <code>103</code>
                        <message>Requested order does not exist</message>
                    </order_not_exists>
                    <status_not_changed>
                        <code>104</code>
                        <message>Credit memo status not changed</message>
                    </status_not_changed>
                    <cannot_refund_to_storecredit>
                        <code>105</code>
                        <message>Money can not be refunded to the store credit account as order was created by guest</message>
                    </cannot_refund_to_storecredit>
                    <cannot_create_creditmemo>
                        <code>106</code>
                        <message>Credit memo for requested order can not be created.</message>
                    </cannot_create_creditmemo>
                </faults>
            </sales_order_creditmemo>
        </resources>
        <resources_alias>
            <order>sales_order</order>
            <order_shipment>sales_order_shipment</order_shipment>
            <order_invoice>sales_order_invoice</order_invoice>
            <order_creditmemo>sales_order_creditmemo</order_creditmemo>
        </resources_alias>
        <v2>
            <resources_function_prefix>
                <order>salesOrder</order>
                <order_shipment>salesOrderShipment</order_shipment>
                <order_invoice>salesOrderInvoice</order_invoice>
                <order_creditmemo>salesOrderCreditmemo</order_creditmemo>
            </resources_function_prefix>
        </v2>
        <rest>
            <mapping>
                <sales_order>
                    <delete>
                        <method>cancel</method>
                    </delete>
                    <post>
                        <resource>cart</resource>
                        <method>order</method>
                    </post>
                </sales_order>
               <sales_order_invoice>
                   <delete>
                       <method>cancel</method>
                   </delete>
               </sales_order_invoice>
                <sales_order_creditmemo>
                    <delete>
                        <method>cancel</method>
                    </delete>
                </sales_order_creditmemo>
            </mapping>
        </rest>
        <acl>
            <resources>
                <sales translate="title" module="sales">
                    <title>Sales</title>
                    <sort_order>2</sort_order>
                    <order translate="title" module="sales">
                        <title>Order</title>
                        <change translate="title" module="sales">
                            <title>Change status, add comments</title>
                        </change>
                        <info translate="title" module="sales">
                            <title>Retrieve orders info</title>
                        </info>
                        <shipment translate="title" module="sales">
                            <title>Order shipments</title>
                            <create translate="title" module="sales">
                                <title>Create</title>
                            </create>
                            <comment translate="title" module="sales">
                                <title>Comments</title>
                            </comment>
                            <track translate="title" module="sales">
                                <title>Tracking</title>
                            </track>
                            <info translate="title" module="sales">
                                <title>Retrieve shipment info</title>
                            </info>
                            <send translate="title" module="sales">
                                <title>Send shipment info</title>
                            </send>
                        </shipment>
                        <invoice translate="title" module="sales">
                            <title>Order invoice</title>
                            <create translate="title" module="sales">
                                <title>Create</title>
                            </create>
                            <comment translate="title" module="sales">
                                <title>Comments</title>
                            </comment>
                            <capture translate="title" module="sales">
                                <title>Capture</title>
                            </capture>
                            <void translate="title" module="sales">
                                <title>Void</title>
                            </void>
                            <cancel translate="title" module="sales">
                                <title>Cancel</title>
                            </cancel>
                            <info translate="title" module="sales">
                                <title>Retrieve invoice info</title>
                            </info>
                        </invoice>
                        <creditmemo translate="title" module="sales">
                            <title>Order credit memo</title>
                            <create translate="title" module="sales">
                                <title>Create</title>
                            </create>
                            <comment translate="title" module="sales">
                                <title>Comments</title>
                            </comment>
                            <cancel translate="title" module="sales">
                                <title>Cancel</title>
                            </cancel>
                            <info translate="title" module="sales">
                                <title>Retrieve credit memo info</title>
                            </info>
                            <list translate="title" module="sales">
                                <title>Retrieve credit memo list</title>
                            </list>
                        </creditmemo>
                    </order>
                </sales>
            </resources>
        </acl>
    </api>
</config>
