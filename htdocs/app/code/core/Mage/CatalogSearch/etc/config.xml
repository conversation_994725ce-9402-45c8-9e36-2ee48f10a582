<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_CatalogSearch
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<config>
    <modules>
        <Mage_CatalogSearch>
            <version>1.8.2.0</version>
        </Mage_CatalogSearch>
    </modules>
    <global>
        <catalogsearch_fulltext>
            <model>catalogsearch/fulltext</model>
        </catalogsearch_fulltext>
        <models>
            <catalogsearch>
                <class>Mage_CatalogSearch_Model</class>
                <resourceModel>catalogsearch_resource</resourceModel>
            </catalogsearch>
            <catalogsearch_resource>
                <class>Mage_CatalogSearch_Model_Resource</class>
                <deprecatedNode>catalogsearch_mysql4</deprecatedNode>
                <entities>
                    <search_query>
                        <table>catalogsearch_query</table>
                    </search_query>
                    <result>
                        <table>catalogsearch_result</table>
                    </result>
                    <fulltext>
                        <table>catalogsearch_fulltext</table>
                    </fulltext>
                </entities>
            </catalogsearch_resource>
        </models>
        <helpers>
            <catalogsearch>
                <class>Mage_CatalogSearch_Helper</class>
            </catalogsearch>
        </helpers>
        <resources>
            <catalogsearch_setup>
                <setup>
                    <module>Mage_CatalogSearch</module>
                </setup>
            </catalogsearch_setup>
        </resources>
        <blocks>
            <catalogsearch>
                <class>Mage_CatalogSearch_Block</class>
            </catalogsearch>
        </blocks>
        <index>
            <indexer>
                <catalogsearch_fulltext>
                    <model>catalogsearch/indexer_fulltext</model>
                </catalogsearch_fulltext>
            </indexer>
        </index>
    </global>
    <frontend>
        <routers>
            <catalogsearch>
                <use>standard</use>
                <args>
                    <module>Mage_CatalogSearch</module>
                    <frontName>catalogsearch</frontName>
                </args>
            </catalogsearch>
        </routers>
        <translate>
            <modules>
                <Mage_CatalogSearch>
                    <files>
                        <default>Mage_CatalogSearch.csv</default>
                    </files>
                </Mage_CatalogSearch>
            </modules>
        </translate>
        <layout>
            <updates>
                <catalogsearch>
                    <file>catalogsearch.xml</file>
                </catalogsearch>
            </updates>
        </layout>
    </frontend>
    <adminhtml>
        <translate>
            <modules>
                <Mage_CatalogSearch>
                    <files>
                        <default>Mage_CatalogSearch.csv</default>
                    </files>
                </Mage_CatalogSearch>
            </modules>
        </translate>
        <layout>
            <updates>
                <catalogsearch>
                    <file>search.xml</file>
                </catalogsearch>
            </updates>
        </layout>
    </adminhtml>
    <default>
        <catalog>
            <seo>
                <search_terms>1</search_terms>
                <site_map>1</site_map>
            </seo>
            <search>
                <min_query_length>1</min_query_length>
                <max_query_length>128</max_query_length>
                <max_query_words>10</max_query_words>
                <search_type>1</search_type>
                <use_layered_navigation_count>2000</use_layered_navigation_count>
                <show_autocomplete_results_count>1</show_autocomplete_results_count>
            </search>
        </catalog>
    </default>
</config>
