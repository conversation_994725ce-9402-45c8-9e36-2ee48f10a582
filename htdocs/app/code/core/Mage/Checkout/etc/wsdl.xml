<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns:typens="urn:{{var wsdl.name}}" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
             xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
             xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
             xmlns="http://schemas.xmlsoap.org/wsdl/"
             name="{{var wsdl.name}}" targetNamespace="urn:{{var wsdl.name}}">
    <types>
        <schema xmlns="http://www.w3.org/2001/XMLSchema" targetNamespace="urn:Magento">
            <complexType name="shoppingCartAddressEntity">
                <all>
                    <element name="address_id" type="xsd:string" minOccurs="0"/>
                    <element name="created_at" type="xsd:string" minOccurs="0"/>
                    <element name="updated_at" type="xsd:string" minOccurs="0"/>
                    <element name="customer_id" type="xsd:string" minOccurs="0"/>
                    <element name="save_in_address_book" type="xsd:int" minOccurs="0"/>
                    <element name="customer_address_id" type="xsd:string" minOccurs="0"/>
                    <element name="address_type" type="xsd:string" minOccurs="0"/>
                    <element name="email" type="xsd:string" minOccurs="0"/>
                    <element name="prefix" type="xsd:string" minOccurs="0"/>
                    <element name="firstname" type="xsd:string" minOccurs="0"/>
                    <element name="middlename" type="xsd:string" minOccurs="0"/>
                    <element name="lastname" type="xsd:string" minOccurs="0"/>
                    <element name="suffix" type="xsd:string" minOccurs="0"/>
                    <element name="company" type="xsd:string" minOccurs="0"/>
                    <element name="street" type="xsd:string" minOccurs="0"/>
                    <element name="city" type="xsd:string" minOccurs="0"/>
                    <element name="region" type="xsd:string" minOccurs="0"/>
                    <element name="region_id" type="xsd:string" minOccurs="0"/>
                    <element name="postcode" type="xsd:string" minOccurs="0"/>
                    <element name="country_id" type="xsd:string" minOccurs="0"/>
                    <element name="telephone" type="xsd:string" minOccurs="0"/>
                    <element name="fax" type="xsd:string" minOccurs="0"/>
                    <element name="same_as_billing" type="xsd:int" minOccurs="0"/>
                    <element name="free_shipping" type="xsd:int" minOccurs="0"/>
                    <element name="shipping_method" type="xsd:string" minOccurs="0"/>
                    <element name="shipping_description" type="xsd:string" minOccurs="0"/>
                    <element name="weight" type="xsd:double" minOccurs="0"/>
                    <element name="fax" type="xsd:string" minOccurs="0"/>
                </all>
            </complexType>
            <complexType name="shoppingCartItemEntity">
                <all>
                    <element name="item_id" type="xsd:string" minOccurs="0"/>
                    <element name="created_at" type="xsd:string" minOccurs="0"/>
                    <element name="updated_at" type="xsd:string" minOccurs="0"/>
                    <element name="product_id" type="xsd:string" minOccurs="0"/>
                    <element name="store_id" type="xsd:string" minOccurs="0"/>
                    <element name="parent_item_id" type="xsd:string" minOccurs="0"/>
                    <element name="is_virtual" type="xsd:int" minOccurs="0"/>
                    <element name="sku" type="xsd:string" minOccurs="0"/>
                    <element name="name" type="xsd:string" minOccurs="0"/>
                    <element name="description" type="xsd:string" minOccurs="0"/>
                    <element name="applied_rule_ids" type="xsd:string" minOccurs="0"/>
                    <element name="additional_data" type="xsd:string" minOccurs="0"/>
                    <element name="free_shipping" type="xsd:string" minOccurs="0"/>
                    <element name="is_qty_decimal" type="xsd:string" minOccurs="0"/>
                    <element name="no_discount" type="xsd:string" minOccurs="0"/>
                    <element name="weight" type="xsd:double" minOccurs="0"/>
                    <element name="qty" type="xsd:double" minOccurs="0"/>
                    <element name="price" type="xsd:double" minOccurs="0"/>
                    <element name="base_price" type="xsd:double" minOccurs="0"/>
                    <element name="custom_price" type="xsd:double" minOccurs="0"/>
                    <element name="discount_percent" type="xsd:double" minOccurs="0"/>
                    <element name="discount_amount" type="xsd:double" minOccurs="0"/>
                    <element name="base_discount_amount" type="xsd:double" minOccurs="0"/>
                    <element name="tax_percent" type="xsd:double" minOccurs="0"/>
                    <element name="tax_amount" type="xsd:double" minOccurs="0"/>
                    <element name="base_tax_amount" type="xsd:double" minOccurs="0"/>
                    <element name="row_total" type="xsd:double" minOccurs="0"/>
                    <element name="base_row_total" type="xsd:double" minOccurs="0"/>
                    <element name="row_total_with_discount" type="xsd:double" minOccurs="0"/>
                    <element name="row_weight" type="xsd:double" minOccurs="0"/>
                    <element name="product_type" type="xsd:string" minOccurs="0"/>
                    <element name="base_tax_before_discount" type="xsd:double" minOccurs="0"/>
                    <element name="tax_before_discount" type="xsd:double" minOccurs="0"/>
                    <element name="original_custom_price" type="xsd:double" minOccurs="0"/>
                    <element name="base_cost" type="xsd:double" minOccurs="0"/>
                    <element name="price_incl_tax" type="xsd:double" minOccurs="0"/>
                    <element name="base_price_incl_tax" type="xsd:double" minOccurs="0"/>
                    <element name="row_total_incl_tax" type="xsd:double" minOccurs="0"/>
                    <element name="base_row_total_incl_tax" type="xsd:double" minOccurs="0"/>
                    <element name="gift_message_id" type="xsd:string" minOccurs="0"/>
                    <element name="gift_message" type="xsd:string" minOccurs="0"/>
                    <element name="gift_message_available" type="xsd:string" minOccurs="0"/>
                    <element name="weee_tax_applied" type="xsd:double" minOccurs="0"/>
                    <element name="weee_tax_applied_amount" type="xsd:double" minOccurs="0"/>
                    <element name="weee_tax_applied_row_amount" type="xsd:double" minOccurs="0"/>
                    <element name="base_weee_tax_applied_amount" type="xsd:double" minOccurs="0"/>
                    <element name="base_weee_tax_applied_row_amount" type="xsd:double" minOccurs="0"/>
                    <element name="weee_tax_disposition" type="xsd:double" minOccurs="0"/>
                    <element name="weee_tax_row_disposition" type="xsd:double" minOccurs="0"/>
                    <element name="base_weee_tax_disposition" type="xsd:double" minOccurs="0"/>
                    <element name="base_weee_tax_row_disposition" type="xsd:double" minOccurs="0"/>
                    <element name="tax_class_id" type="xsd:string" minOccurs="0"/>
                </all>
            </complexType>
            <complexType name="shoppingCartItemEntityArray">
                <complexContent>
                    <restriction base="soapenc:Array">
                        <attribute ref="soapenc:arrayType" wsdl:arrayType="typens:shoppingCartItemEntity[]"/>
                    </restriction>
                </complexContent>
            </complexType>
            <complexType name="shoppingCartPaymentEntity">
                <all>
                    <element name="payment_id" type="xsd:string" minOccurs="0"/>
                    <element name="created_at" type="xsd:string" minOccurs="0"/>
                    <element name="updated_at" type="xsd:string" minOccurs="0"/>
                    <element name="method" type="xsd:string" minOccurs="0"/>
                    <element name="cc_type" type="xsd:string" minOccurs="0"/>
                    <element name="cc_number_enc" type="xsd:string" minOccurs="0"/>
                    <element name="cc_last4" type="xsd:string" minOccurs="0"/>
                    <element name="cc_cid_enc" type="xsd:string" minOccurs="0"/>
                    <element name="cc_owner" type="xsd:string" minOccurs="0"/>
                    <element name="cc_exp_month" type="xsd:string" minOccurs="0"/>
                    <element name="cc_exp_year" type="xsd:string" minOccurs="0"/>
                    <element name="cc_ss_owner" type="xsd:string" minOccurs="0"/>
                    <element name="cc_ss_start_month" type="xsd:string" minOccurs="0"/>
                    <element name="cc_ss_start_year" type="xsd:string" minOccurs="0"/>
                    <element name="cc_ss_issue" type="xsd:string" minOccurs="0"/>
                    <element name="po_number" type="xsd:string" minOccurs="0"/>
                    <element name="additional_data" type="xsd:string" minOccurs="0"/>
                    <element name="additional_information" type="xsd:string" minOccurs="0"/>
                </all>
            </complexType>
            <complexType name="shoppingCartInfoEntity">
                <all>
                    <element name="store_id" type="xsd:string" minOccurs="0"/>
                    <element name="created_at" type="xsd:string" minOccurs="0"/>
                    <element name="updated_at" type="xsd:string" minOccurs="0"/>
                    <element name="converted_at" type="xsd:string" minOccurs="0"/>
                    <element name="quote_id" type="xsd:int" minOccurs="0"/>
                    <element name="is_active" type="xsd:int" minOccurs="0"/>
                    <element name="is_virtual" type="xsd:int" minOccurs="0"/>
                    <element name="is_multi_shipping" type="xsd:int" minOccurs="0"/>
                    <element name="items_count" type="xsd:double" minOccurs="0"/>
                    <element name="items_qty" type="xsd:double" minOccurs="0"/>
                    <element name="orig_order_id" type="xsd:string" minOccurs="0"/>
                    <element name="store_to_base_rate" type="xsd:string" minOccurs="0"/>
                    <element name="store_to_quote_rate" type="xsd:string" minOccurs="0"/>
                    <element name="base_currency_code" type="xsd:string" minOccurs="0"/>
                    <element name="store_currency_code" type="xsd:string" minOccurs="0"/>
                    <element name="quote_currency_code" type="xsd:string" minOccurs="0"/>
                    <element name="grand_total" type="xsd:string" minOccurs="0"/>
                    <element name="base_grand_total" type="xsd:string" minOccurs="0"/>
                    <element name="checkout_method" type="xsd:string" minOccurs="0"/>
                    <element name="customer_id" type="xsd:string" minOccurs="0"/>
                    <element name="customer_tax_class_id" type="xsd:string" minOccurs="0"/>
                    <element name="customer_group_id" type="xsd:int" minOccurs="0"/>
                    <element name="customer_email" type="xsd:string" minOccurs="0"/>
                    <element name="customer_prefix" type="xsd:string" minOccurs="0"/>
                    <element name="customer_firstname" type="xsd:string" minOccurs="0"/>
                    <element name="customer_middlename" type="xsd:string" minOccurs="0"/>
                    <element name="customer_lastname" type="xsd:string" minOccurs="0"/>
                    <element name="customer_suffix" type="xsd:string" minOccurs="0"/>
                    <element name="customer_note" type="xsd:string" minOccurs="0"/>
                    <element name="customer_note_notify" type="xsd:string" minOccurs="0"/>
                    <element name="customer_is_guest" type="xsd:string" minOccurs="0"/>
                    <element name="applied_rule_ids" type="xsd:string" minOccurs="0"/>
                    <element name="reserved_order_id" type="xsd:string" minOccurs="0"/>
                    <element name="password_hash" type="xsd:string" minOccurs="0"/>
                    <element name="coupon_code" type="xsd:string" minOccurs="0"/>
                    <element name="global_currency_code" type="xsd:string" minOccurs="0"/>
                    <element name="base_to_global_rate" type="xsd:double" minOccurs="0"/>
                    <element name="base_to_quote_rate" type="xsd:double" minOccurs="0"/>
                    <element name="customer_taxvat" type="xsd:string" minOccurs="0"/>
                    <element name="customer_gender" type="xsd:string" minOccurs="0"/>
                    <element name="subtotal" type="xsd:double" minOccurs="0"/>
                    <element name="base_subtotal" type="xsd:double" minOccurs="0"/>
                    <element name="subtotal_with_discount" type="xsd:double" minOccurs="0"/>
                    <element name="base_subtotal_with_discount" type="xsd:double" minOccurs="0"/>
                    <element name="ext_shipping_info" type="xsd:string" minOccurs="0"/>
                    <element name="gift_message_id" type="xsd:string" minOccurs="0"/>
                    <element name="gift_message" type="xsd:string" minOccurs="0"/>
                    <element name="customer_balance_amount_used" type="xsd:double" minOccurs="0"/>
                    <element name="base_customer_balance_amount_used" type="xsd:double" minOccurs="0"/>
                    <element name="use_customer_balance" type="xsd:string" minOccurs="0"/>
                    <element name="gift_cards_amount" type="xsd:string" minOccurs="0"/>
                    <element name="base_gift_cards_amount" type="xsd:string" minOccurs="0"/>
                    <element name="gift_cards_amount_used" type="xsd:string" minOccurs="0"/>
                    <element name="use_reward_points" type="xsd:string" minOccurs="0"/>
                    <element name="reward_points_balance" type="xsd:string" minOccurs="0"/>
                    <element name="base_reward_currency_amount" type="xsd:string" minOccurs="0"/>
                    <element name="reward_currency_amount" type="xsd:string" minOccurs="0"/>
                    <element name="shipping_address" type="typens:shoppingCartAddressEntity" minOccurs="0"/>
                    <element name="billing_address" type="typens:shoppingCartAddressEntity" minOccurs="0"/>
                    <element name="items" type="typens:shoppingCartItemEntityArray" minOccurs="0"/>
                    <element name="payment" type="typens:shoppingCartPaymentEntity" minOccurs="0"/>
                </all>
            </complexType>
            <complexType name="shoppingCartTotalsEntity">
                <all>
                    <element name="title" type="xsd:string" minOccurs="0"/>
                    <element name="amount" type="xsd:double" minOccurs="0"/>
                </all>
            </complexType>
            <complexType name="shoppingCartTotalsEntityArray">
                <complexContent>
                    <restriction base="soapenc:Array">
                        <attribute ref="soapenc:arrayType" wsdl:arrayType="typens:shoppingCartTotalsEntity[]"/>
                    </restriction>
                </complexContent>
            </complexType>
            <complexType name="shoppingCartLicenseEntity">
                <all>
                    <element name="agreement_id" type="xsd:string" minOccurs="0"/>
                    <element name="name" type="xsd:string" minOccurs="0"/>
                    <element name="content" type="xsd:string" minOccurs="0"/>
                    <element name="is_active" type="xsd:int" minOccurs="0"/>
                    <element name="is_html" type="xsd:int" minOccurs="0"/>
                </all>
            </complexType>
            <complexType name="shoppingCartLicenseEntityArray">
                <complexContent>
                    <restriction base="soapenc:Array">
                        <attribute ref="soapenc:arrayType" wsdl:arrayType="typens:shoppingCartLicenseEntity[]"/>
                    </restriction>
                </complexContent>
            </complexType>

            <complexType name="shoppingCartProductEntity">
                <all>
                    <element name="product_id" type="xsd:string" minOccurs="0"/>
                    <element name="sku" type="xsd:string" minOccurs="0"/>
                    <element name="qty" type="xsd:double" minOccurs="0"/>
                    <element name="options" type="typens:associativeArray" minOccurs="0"/>
                    <element name="bundle_option" type="typens:associativeArray" minOccurs="0"/>
                    <element name="bundle_option_qty" type="typens:associativeArray" minOccurs="0"/>
                    <element name="links" type="typens:ArrayOfString" minOccurs="0"/>
                </all>
            </complexType>
            <complexType name="shoppingCartProductEntityArray">
                <complexContent>
                    <restriction base="soapenc:Array">
                        <attribute ref="soapenc:arrayType" wsdl:arrayType="typens:shoppingCartProductEntity[]"/>
                    </restriction>
                </complexContent>
            </complexType>
            <complexType name="shoppingCartProductResponseEntityArray">
                <complexContent>
                    <restriction base="soapenc:Array">
                        <attribute ref="soapenc:arrayType" wsdl:arrayType="typens:catalogProductEntity[]"/>
                    </restriction>
                </complexContent>
            </complexType>
            <complexType name="shoppingCartCustomerEntity">
                <all>
                    <element name="mode" type="xsd:string" minOccurs="0"/>
                    <element name="customer_id" type="xsd:int" minOccurs="0"/>
                    <element name="email" type="xsd:string" minOccurs="0"/>
                    <element name="firstname" type="xsd:string" minOccurs="0"/>
                    <element name="lastname" type="xsd:string" minOccurs="0"/>
                    <element name="password" type="xsd:string" minOccurs="0"/>
                    <element name="confirmation" type="xsd:string" minOccurs="0"/>
                    <element name="website_id" type="xsd:int" minOccurs="0"/>
                    <element name="store_id" type="xsd:int" minOccurs="0"/>
                    <element name="group_id" type="xsd:int" minOccurs="0"/>
                </all>
            </complexType>
            <complexType name="shoppingCartCustomerAddressEntity">
                <all>
                    <element name="mode" type="xsd:string" minOccurs="0"/>
                    <element name="address_id" type="xsd:string" minOccurs="0"/>
                    <element name="firstname" type="xsd:string" minOccurs="0"/>
                    <element name="lastname" type="xsd:string" minOccurs="0"/>
                    <element name="company" type="xsd:string" minOccurs="0"/>
                    <element name="street" type="xsd:string" minOccurs="0"/>
                    <element name="city" type="xsd:string" minOccurs="0"/>
                    <element name="region" type="xsd:string" minOccurs="0"/>
                    <element name="region_id" type="xsd:string" minOccurs="0"/>
                    <element name="postcode" type="xsd:string" minOccurs="0"/>
                    <element name="country_id" type="xsd:string" minOccurs="0"/>
                    <element name="telephone" type="xsd:string" minOccurs="0"/>
                    <element name="fax" type="xsd:string" minOccurs="0"/>
                    <element name="is_default_billing" type="xsd:int" minOccurs="0"/>
                    <element name="is_default_shipping" type="xsd:int" minOccurs="0"/>
                </all>
            </complexType>
            <complexType name="shoppingCartCustomerAddressEntityArray">
                <complexContent>
                    <restriction base="soapenc:Array">
                        <attribute ref="soapenc:arrayType" wsdl:arrayType="typens:shoppingCartCustomerAddressEntity[]"/>
                    </restriction>
                </complexContent>
            </complexType>
            <complexType name="shoppingCartShippingMethodEntity">
                <all>
                    <element name="code" type="xsd:string" minOccurs="0"/>
                    <element name="carrier" type="xsd:string" minOccurs="0"/>
                    <element name="carrier_title" type="xsd:string" minOccurs="0"/>
                    <element name="method" type="xsd:string" minOccurs="0"/>
                    <element name="method_title" type="xsd:string" minOccurs="0"/>
                    <element name="method_description" type="xsd:string" minOccurs="0"/>
                    <element name="price" type="xsd:double" minOccurs="0"/>
                </all>
            </complexType>
            <complexType name="shoppingCartShippingMethodEntityArray">
                <complexContent>
                    <restriction base="soapenc:Array">
                        <attribute ref="soapenc:arrayType" wsdl:arrayType="typens:shoppingCartShippingMethodEntity[]"/>
                    </restriction>
                </complexContent>
            </complexType>

            <complexType name="shoppingCartPaymentMethodEntity">
                <all>
                    <element name="po_number" type="xsd:string" minOccurs="0"/>
                    <element name="method" type="xsd:string" minOccurs="0"/>
                    <element name="cc_cid" type="xsd:string" minOccurs="0"/>
                    <element name="cc_owner" type="xsd:string" minOccurs="0"/>
                    <element name="cc_number" type="xsd:string" minOccurs="0"/>
                    <element name="cc_type" type="xsd:string" minOccurs="0"/>
                    <element name="cc_exp_year" type="xsd:string" minOccurs="0"/>
                    <element name="cc_exp_month" type="xsd:string" minOccurs="0"/>
                </all>
            </complexType>
            <complexType name="shoppingCartPaymentMethodResponseEntity">
                <all>
                    <element name="code" type="xsd:string"/>
                    <element name="title" type="xsd:string"/>
                    <element name="cc_types" type="typens:associativeArray"/>
                </all>
            </complexType>
            <complexType name="shoppingCartPaymentMethodResponseEntityArray">
                <complexContent>
                    <restriction base="soapenc:Array">
                        <attribute ref="soapenc:arrayType" wsdl:arrayType="typens:shoppingCartPaymentMethodResponseEntity[]"/>
                    </restriction>
                </complexContent>
            </complexType>
        </schema>
    </types>
    <message name="shoppingCartCreateRequest">
        <part name="sessionId" type="xsd:string"/>
        <part name="storeId" type="xsd:string"/>
    </message>
    <message name="shoppingCartCreateResponse">
        <part name="quoteId" type="xsd:int"/>
    </message>
    <message name="shoppingCartOrderRequest">
        <part name="sessionId" type="xsd:string"/>
        <part name="quoteId" type="xsd:int"/>
        <part name="storeId" type="xsd:string"/>
        <part name="licenses" type="typens:ArrayOfString"/>
    </message>
    <message name="shoppingCartOrderResponse">
        <part name="result" type="xsd:string"/>
    </message>
    <message name="shoppingCartInfoRequest">
        <part name="sessionId" type="xsd:string"/>
        <part name="quoteId" type="xsd:int"/>
        <part name="storeId" type="xsd:string"/>
    </message>
    <message name="shoppingCartInfoResponse">
        <part name="result" type="typens:shoppingCartInfoEntity"/>
    </message>
    <message name="shoppingCartTotalsRequest">
        <part name="sessionId" type="xsd:string"/>
        <part name="quoteId" type="xsd:int"/>
        <part name="storeId" type="xsd:string"/>
    </message>
    <message name="shoppingCartTotalsResponse">
        <part name="result" type="typens:shoppingCartTotalsEntityArray"/>
    </message>
    <message name="shoppingCartLicenseRequest">
        <part name="sessionId" type="xsd:string"/>
        <part name="quoteId" type="xsd:int"/>
        <part name="storeId" type="xsd:string"/>
    </message>
    <message name="shoppingCartLicenseResponse">
        <part name="result" type="typens:shoppingCartLicenseEntityArray"/>
    </message>
    <message name="shoppingCartProductAddRequest">
        <part name="sessionId" type="xsd:string"/>
        <part name="quoteId" type="xsd:int"/>
        <part name="products" type="typens:shoppingCartProductEntityArray"/>
        <part name="storeId" type="xsd:string"/>
    </message>
    <message name="shoppingCartProductAddResponse">
        <part name="result" type="xsd:boolean"/>
    </message>
    <message name="shoppingCartProductUpdateRequest">
        <part name="sessionId" type="xsd:string"/>
        <part name="quoteId" type="xsd:int"/>
        <part name="products" type="typens:shoppingCartProductEntityArray"/>
        <part name="storeId" type="xsd:string"/>
    </message>
    <message name="shoppingCartProductUpdateResponse">
        <part name="result" type="xsd:boolean"/>
    </message>
    <message name="shoppingCartProductRemoveRequest">
        <part name="sessionId" type="xsd:string"/>
        <part name="quoteId" type="xsd:int"/>
        <part name="products" type="typens:shoppingCartProductEntityArray"/>
        <part name="storeId" type="xsd:string"/>
    </message>
    <message name="shoppingCartProductRemoveResponse">
        <part name="result" type="xsd:boolean"/>
    </message>
    <message name="shoppingCartProductListRequest">
        <part name="sessionId" type="xsd:string"/>
        <part name="quoteId" type="xsd:int"/>
        <part name="storeId" type="xsd:string"/>
    </message>
    <message name="shoppingCartProductListResponse">
        <part name="result" type="typens:shoppingCartProductResponseEntityArray"/>
    </message>
    <message name="shoppingCartProductMoveToCustomerQuoteRequest">
        <part name="sessionId" type="xsd:string"/>
        <part name="quoteId" type="xsd:int"/>
        <part name="products" type="typens:shoppingCartProductEntityArray"/>
        <part name="storeId" type="xsd:string"/>
    </message>
    <message name="shoppingCartProductMoveToCustomerQuoteResponse">
        <part name="result" type="xsd:boolean"/>
    </message>
    <message name="shoppingCartCustomerSetRequest">
        <part name="sessionId" type="xsd:string"/>
        <part name="quoteId" type="xsd:int"/>
        <part name="customer" type="typens:shoppingCartCustomerEntity"/>
        <part name="storeId" type="xsd:string"/>
    </message>
    <message name="shoppingCartCustomerSetResponse">
        <part name="result" type="xsd:boolean"/>
    </message>
    <message name="shoppingCartCustomerAddressesRequest">
        <part name="sessionId" type="xsd:string"/>
        <part name="quoteId" type="xsd:int"/>
        <part name="customer" type="typens:shoppingCartCustomerAddressEntityArray"/>
        <part name="storeId" type="xsd:string"/>
    </message>
    <message name="shoppingCartCustomerAddressesResponse">
        <part name="result" type="xsd:boolean"/>
    </message>
    <message name="shoppingCartShippingMethodRequest">
        <part name="sessionId" type="xsd:string"/>
        <part name="quoteId" type="xsd:int"/>
        <part name="method" type="xsd:string"/>
        <part name="storeId" type="xsd:string"/>
    </message>
    <message name="shoppingCartShippingMethodResponse">
        <part name="result" type="xsd:boolean"/>
    </message>
    <message name="shoppingCartShippingListRequest">
        <part name="sessionId" type="xsd:string"/>
        <part name="quoteId" type="xsd:int"/>
        <part name="storeId" type="xsd:string"/>
    </message>
    <message name="shoppingCartShippingListResponse">
        <part name="result" type="typens:shoppingCartShippingMethodEntityArray"/>
    </message>
    <message name="shoppingCartPaymentMethodRequest">
        <part name="sessionId" type="xsd:string"/>
        <part name="quoteId" type="xsd:int"/>
        <part name="method" type="typens:shoppingCartPaymentMethodEntity"/>
        <part name="storeId" type="xsd:string"/>
    </message>
    <message name="shoppingCartPaymentMethodResponse">
        <part name="result" type="xsd:boolean"/>
    </message>
    <message name="shoppingCartPaymentListRequest">
        <part name="sessionId" type="xsd:string"/>
        <part name="quoteId" type="xsd:int"/>
        <part name="store" type="xsd:string"/>
    </message>
    <message name="shoppingCartPaymentListResponse">
        <part name="result" type="typens:shoppingCartPaymentMethodResponseEntityArray"/>
    </message>
    <message name="shoppingCartCouponAddRequest">
        <part name="sessionId" type="xsd:string"/>
        <part name="quoteId" type="xsd:int"/>
        <part name="couponCode" type="xsd:string"/>
        <part name="storeId" type="xsd:string"/>
    </message>
    <message name="shoppingCartCouponAddResponse">
        <part name="result" type="xsd:boolean"/>
    </message>
    <message name="shoppingCartCouponRemoveRequest">
        <part name="sessionId" type="xsd:string"/>
        <part name="quoteId" type="xsd:int"/>
        <part name="storeId" type="xsd:string"/>
    </message>
    <message name="shoppingCartCouponRemoveResponse">
        <part name="result" type="xsd:boolean"/>
    </message>
    <portType name="{{var wsdl.handler}}PortType">
        <operation name="shoppingCartCreate">
            <documentation>Create shopping cart</documentation>
            <input message="typens:shoppingCartCreateRequest"/>
            <output message="typens:shoppingCartCreateResponse"/>
        </operation>
        <operation name="shoppingCartInfo">
            <documentation>Retrieve information about shopping cart</documentation>
            <input message="typens:shoppingCartInfoRequest"/>
            <output message="typens:shoppingCartInfoResponse"/>
        </operation>
        <operation name="shoppingCartOrder">
            <documentation>Create an order from shopping cart</documentation>
            <input message="typens:shoppingCartOrderRequest"/>
            <output message="typens:shoppingCartOrderResponse"/>
        </operation>
        <operation name="shoppingCartTotals">
            <documentation>Get total prices for shopping cart</documentation>
            <input message="typens:shoppingCartTotalsRequest"/>
            <output message="typens:shoppingCartTotalsResponse"/>
        </operation>
        <operation name="shoppingCartLicense">
            <documentation>Get terms and conditions</documentation>
            <input message="typens:shoppingCartLicenseRequest"/>
            <output message="typens:shoppingCartLicenseResponse"/>
        </operation>
        <operation name="shoppingCartProductAdd">
            <documentation>Add product(s) to shopping cart</documentation>
            <input message="typens:shoppingCartProductAddRequest"/>
            <output message="typens:shoppingCartProductAddResponse"/>
        </operation>
        <operation name="shoppingCartProductUpdate">
            <documentation>Update product(s) quantities in shopping cart</documentation>
            <input message="typens:shoppingCartProductUpdateRequest"/>
            <output message="typens:shoppingCartProductUpdateResponse"/>
        </operation>
        <operation name="shoppingCartProductRemove">
            <documentation>Remove product(s) from shopping cart</documentation>
            <input message="typens:shoppingCartProductRemoveRequest"/>
            <output message="typens:shoppingCartProductRemoveResponse"/>
        </operation>
        <operation name="shoppingCartProductList">
            <documentation>Get list of products in shopping cart</documentation>
            <input message="typens:shoppingCartProductListRequest"/>
            <output message="typens:shoppingCartProductListResponse"/>
        </operation>
        <operation name="shoppingCartProductMoveToCustomerQuote">
            <documentation>Move product(s) to customer quote</documentation>
            <input message="typens:shoppingCartProductMoveToCustomerQuoteRequest"/>
            <output message="typens:shoppingCartProductMoveToCustomerQuoteResponse"/>
        </operation>
        <operation name="shoppingCartCustomerSet">
            <documentation>Set customer for shopping cart</documentation>
            <input message="typens:shoppingCartCustomerSetRequest"/>
            <output message="typens:shoppingCartCustomerSetResponse"/>
        </operation>
        <operation name="shoppingCartCustomerAddresses">
            <documentation>Set customer's addresses in shopping cart</documentation>
            <input message="typens:shoppingCartCustomerAddressesRequest"/>
            <output message="typens:shoppingCartCustomerAddressesResponse"/>
        </operation>
        <operation name="shoppingCartShippingMethod">
            <documentation>Set shipping method</documentation>
            <input message="typens:shoppingCartShippingMethodRequest"/>
            <output message="typens:shoppingCartShippingMethodResponse"/>
        </operation>
        <operation name="shoppingCartShippingList">
            <documentation>Get list of available shipping methods</documentation>
            <input message="typens:shoppingCartShippingListRequest"/>
            <output message="typens:shoppingCartShippingListResponse"/>
        </operation>
        <operation name="shoppingCartPaymentMethod">
            <documentation>Set payment method</documentation>
            <input message="typens:shoppingCartPaymentMethodRequest"/>
            <output message="typens:shoppingCartPaymentMethodResponse"/>
        </operation>
        <operation name="shoppingCartPaymentList">
            <documentation>Get list of available payment methods</documentation>
            <input message="typens:shoppingCartPaymentListRequest"/>
            <output message="typens:shoppingCartPaymentListResponse"/>
        </operation>
        <operation name="shoppingCartCouponAdd">
            <documentation>Add coupon code for shopping cart</documentation>
            <input message="typens:shoppingCartCouponAddRequest"/>
            <output message="typens:shoppingCartCouponAddResponse"/>
        </operation>
        <operation name="shoppingCartCouponRemove">
            <documentation>Remove coupon code from shopping cart</documentation>
            <input message="typens:shoppingCartCouponRemoveRequest"/>
            <output message="typens:shoppingCartCouponRemoveResponse"/>
        </operation>
    </portType>
    <binding name="{{var wsdl.handler}}Binding" type="typens:{{var wsdl.handler}}PortType">
        <soap:binding style="rpc" transport="http://schemas.xmlsoap.org/soap/http"/>
        <operation name="shoppingCartCreate">
            <soap:operation soapAction="urn:{{var wsdl.handler}}Action"/>
            <input>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </input>
            <output>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </output>
        </operation>
        <operation name="shoppingCartInfo">
            <soap:operation soapAction="urn:{{var wsdl.handler}}Action"/>
            <input>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </input>
            <output>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </output>
        </operation>
        <operation name="shoppingCartTotals">
            <soap:operation soapAction="urn:{{var wsdl.handler}}Action"/>
            <input>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </input>
            <output>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </output>
        </operation>
        <operation name="shoppingCartOrder">
            <soap:operation soapAction="urn:{{var wsdl.handler}}Action"/>
            <input>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </input>
            <output>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </output>
        </operation>
        <operation name="shoppingCartLicense">
            <soap:operation soapAction="urn:{{var wsdl.handler}}Action"/>
            <input>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </input>
            <output>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </output>
        </operation>
        <operation name="shoppingCartProductAdd">
            <soap:operation soapAction="urn:{{var wsdl.handler}}Action"/>
            <input>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </input>
            <output>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </output>
        </operation>
        <operation name="shoppingCartProductUpdate">
            <soap:operation soapAction="urn:{{var wsdl.handler}}Action"/>
            <input>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </input>
            <output>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </output>
        </operation>
        <operation name="shoppingCartProductRemove">
            <soap:operation soapAction="urn:{{var wsdl.handler}}Action"/>
            <input>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </input>
            <output>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </output>
        </operation>
        <operation name="shoppingCartProductList">
            <soap:operation soapAction="urn:{{var wsdl.handler}}Action"/>
            <input>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </input>
            <output>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </output>
        </operation>
        <operation name="shoppingCartProductMoveToCustomerQuote">
            <soap:operation soapAction="urn:{{var wsdl.handler}}Action"/>
            <input>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </input>
            <output>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </output>
        </operation>
        <operation name="shoppingCartCustomerSet">
            <soap:operation soapAction="urn:{{var wsdl.handler}}Action"/>
            <input>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </input>
            <output>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </output>
        </operation>
        <operation name="shoppingCartCustomerAddresses">
            <soap:operation soapAction="urn:{{var wsdl.handler}}Action"/>
            <input>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </input>
            <output>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </output>
        </operation>
        <operation name="shoppingCartShippingMethod">
            <soap:operation soapAction="urn:{{var wsdl.handler}}Action"/>
            <input>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </input>
            <output>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </output>
        </operation>
        <operation name="shoppingCartShippingList">
            <soap:operation soapAction="urn:{{var wsdl.handler}}Action"/>
            <input>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </input>
            <output>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </output>
        </operation>
        <operation name="shoppingCartPaymentMethod">
            <soap:operation soapAction="urn:{{var wsdl.handler}}Action"/>
            <input>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </input>
            <output>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </output>
        </operation>
        <operation name="shoppingCartPaymentList">
            <soap:operation soapAction="urn:{{var wsdl.handler}}Action"/>
            <input>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </input>
            <output>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </output>
        </operation>
        <operation name="shoppingCartCouponAdd">
            <soap:operation soapAction="urn:{{var wsdl.handler}}Action"/>
            <input>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </input>
            <output>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </output>
        </operation>
        <operation name="shoppingCartCouponRemove">
            <soap:operation soapAction="urn:{{var wsdl.handler}}Action"/>
            <input>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </input>
            <output>
                <soap:body namespace="urn:{{var wsdl.name}}" use="encoded"
                           encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
            </output>
        </operation>
    </binding>
</definitions>
