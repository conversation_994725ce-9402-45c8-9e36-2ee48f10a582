/*
 * qTip2 - Pretty powerful tooltips - v3.0.3
 * http://qtip2.com
 *
 * Copyright (c) 2017 
 * Released under the MIT licenses
 * http://jquery.org/license
 *
 * Date: Wed Jan 4 2017 06:45 EST-0500
 * Plugins: None
 * Styles: core basic
 */
.qtip{
	position: absolute;
	left: -28000px;
	top: -28000px;
	display: none;

	max-width: 280px;
	min-width: 50px;

	font-size: 10.5px;
	line-height: 12px;

	direction: ltr;

	box-shadow: none;
	padding: 0;
}

	.qtip-content{
		position: relative;
		padding: 5px 9px;
		overflow: hidden;

		text-align: left;
		word-wrap: break-word;
	}

	.qtip-titlebar{
		position: relative;
		padding: 5px 35px 5px 10px;
		overflow: hidden;

		border-width: 0 0 1px;
		font-weight: bold;
	}

	.qtip-titlebar + .qtip-content{ border-top-width: 0 !important; }

	/* Default close button class */
	.qtip-close{
		position: absolute;
		right: -9px; top: -9px;
		z-index: 11; /* Overlap .qtip-tip */

		cursor: pointer;
		outline: medium none;

		border: 1px solid transparent;
	}

		.qtip-titlebar .qtip-close{
			right: 4px; top: 50%;
			margin-top: -9px;
		}

		* html .qtip-titlebar .qtip-close{ top: 16px; } /* IE fix */

		.qtip-titlebar .ui-icon,
		.qtip-icon .ui-icon{
			display: block;
			text-indent: -1000em;
			direction: ltr;
		}

		.qtip-icon, .qtip-icon .ui-icon{
			-moz-border-radius: 3px;
			-webkit-border-radius: 3px;
			border-radius: 3px;
			text-decoration: none;
		}

			.qtip-icon .ui-icon{
				width: 18px;
				height: 14px;

				line-height: 14px;
				text-align: center;
				text-indent: 0;
				font: normal bold 10px/13px Tahoma,sans-serif;

				color: inherit;
				background: transparent none no-repeat -100em -100em;
			}

/* Applied to 'focused' tooltips e.g. most recently displayed/interacted with */
.qtip-focus{}

/* Applied on hover of tooltips i.e. added/removed on mouseenter/mouseleave respectively */
.qtip-hover{}

/* Default tooltip style */
.qtip-default{
	border: 1px solid #F1D031;

	background-color: #FFFFA3;
	color: #555;
}

	.qtip-default .qtip-titlebar{
		background-color: #FFEF93;
	}

	.qtip-default .qtip-icon{
		border-color: #CCC;
		background: #F1F1F1;
		color: #777;
	}

	.qtip-default .qtip-titlebar .qtip-close{
		border-color: #AAA;
		color: #111;
	}


/*! Light tooltip style */
.qtip-light{
	background-color: white;
	border-color: #E2E2E2;
	color: #454545;
}

	.qtip-light .qtip-titlebar{
		background-color: #f1f1f1;
	}


/*! Dark tooltip style */
.qtip-dark{
	background-color: #505050;
	border-color: #303030;
	color: #f3f3f3;
}

	.qtip-dark .qtip-titlebar{
		background-color: #404040;
	}

	.qtip-dark .qtip-icon{
		border-color: #444;
	}

	.qtip-dark .qtip-titlebar .ui-state-hover{
		border-color: #303030;
	}


/*! Cream tooltip style */
.qtip-cream{
	background-color: #FBF7AA;
	border-color: #F9E98E;
	color: #A27D35;
}

	.qtip-cream .qtip-titlebar{
		background-color: #F0DE7D;
	}

	.qtip-cream .qtip-close .qtip-icon{
		background-position: -82px 0;
	}


/*! Red tooltip style */
.qtip-red{
	background-color: #F78B83;
	border-color: #D95252;
	color: #912323;
}

	.qtip-red .qtip-titlebar{
		background-color: #F06D65;
	}

	.qtip-red .qtip-close .qtip-icon{
		background-position: -102px 0;
	}

	.qtip-red .qtip-icon{
		border-color: #D95252;
	}

	.qtip-red .qtip-titlebar .ui-state-hover{
		border-color: #D95252;
	}


/*! Green tooltip style */
.qtip-green{
	background-color: #CAED9E;
	border-color: #90D93F;
	color: #3F6219;
}

	.qtip-green .qtip-titlebar{
		background-color: #B0DE78;
	}

	.qtip-green .qtip-close .qtip-icon{
		background-position: -42px 0;
	}


/*! Blue tooltip style */
.qtip-blue{
	background-color: #E5F6FE;
	border-color: #ADD9ED;
	color: #5E99BD;
}

	.qtip-blue .qtip-titlebar{
		background-color: #D0E9F5;
	}

	.qtip-blue .qtip-close .qtip-icon{
		background-position: -2px 0;
	}
