<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="rubikmedium" horiz-adv-x="1171" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="466" />
<glyph unicode="&#xfb01;" horiz-adv-x="1261" d="M35 895v121q0 20 14 34.5t35 14.5h166v82q0 186 100.5 277.5t302.5 91.5h424q23 0 36 -13.5t13 -36.5v-120q0 -20 -14 -35t-35 -15h-413q-80 0 -113 -38.5t-33 -120.5v-72h559q23 0 36 -13.5t13 -35.5v-967q0 -20 -13 -34.5t-36 -14.5h-170q-20 0 -34.5 14.5t-14.5 34.5 v797h-340v-797q0 -20 -13 -34.5t-36 -14.5h-170q-20 0 -34.5 14.5t-14.5 34.5v797h-166q-20 0 -34.5 14t-14.5 35z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1343" d="M35 895v121q0 20 14 34.5t35 14.5h166v82q0 186 100.5 277.5t302.5 91.5h123q23 0 36 -13.5t13 -36.5v-120q0 -20 -14 -35t-35 -15h-112q-80 0 -113 -38.5t-33 -120.5v-72h420v401q0 20 14.5 35t34.5 15h170q23 0 36 -13.5t13 -36.5v-1417q0 -20 -13 -34.5t-36 -14.5 h-170q-20 0 -34.5 14.5t-14.5 34.5v797h-420v-797q0 -20 -13 -34.5t-36 -14.5h-170q-20 0 -34.5 14.5t-14.5 34.5v797h-166q-20 0 -34.5 14t-14.5 35z" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="466" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode=" "  horiz-adv-x="466" />
<glyph unicode="&#x09;" horiz-adv-x="466" />
<glyph unicode="&#xa0;" horiz-adv-x="466" />
<glyph unicode="!" horiz-adv-x="550" d="M129 49v195q0 23 14.5 36t34.5 13h193q23 0 37 -14.5t14 -34.5v-195q0 -20 -14.5 -34.5t-36.5 -14.5h-193q-20 0 -34.5 14.5t-14.5 34.5zM141 449v935q0 23 14.5 36.5t34.5 13.5h170q20 0 35 -14.5t15 -35.5v-935q0 -20 -14.5 -35t-35.5 -15h-170q-20 0 -34.5 14.5 t-14.5 35.5z" />
<glyph unicode="&#x22;" horiz-adv-x="849" d="M98 1399q-2 25 10.5 40t36.5 15h183q23 0 36 -14.5t11 -40.5l-41 -393q-8 -51 -51 -52h-93q-43 1 -51 52zM475 1399q-2 25 10.5 40t34.5 15h185q23 0 35 -14.5t10 -40.5l-41 -393q-4 -51 -50 -52h-92q-43 1 -51 52z" />
<glyph unicode="#" horiz-adv-x="1415" d="M96 391v119q0 20 13.5 33.5t33.5 13.5h205l45 307h-196q-20 0 -33.5 13.5t-13.5 33.5v119q0 20 13 33.5t34 13.5h229l33 221q4 18 16 30.5t31 12.5h121q18 0 30.5 -12t12.5 -31l-33 -221h254l33 221q4 18 16 30.5t31 12.5h121q18 0 30.5 -12t12.5 -31l-33 -221h170 q20 0 33.5 -13t13.5 -34v-119q0 -20 -13.5 -33.5t-33.5 -13.5h-201l-47 -307h195q20 0 33.5 -13.5t13.5 -33.5v-119q0 -20 -13.5 -33.5t-33.5 -13.5h-226l-32 -221q-4 -18 -16.5 -30.5t-33.5 -12.5h-120q-18 0 -32 12t-11 31l34 221h-254l-32 -221q-4 -18 -16.5 -30.5 t-33.5 -12.5h-120q-19 0 -32 12t-11 31l34 221h-174q-20 0 -33.5 13.5t-13.5 33.5zM559 557h254l45 307h-252z" />
<glyph unicode="$" horiz-adv-x="1318" d="M74 377q0 16 13 29.5t32 13.5h184q25 0 38 -9.5t26 -29.5q23 -70 95.5 -117t199.5 -47q141 0 213.5 48t72.5 132q0 55 -37 92t-111.5 65t-219.5 67q-242 57 -354.5 151t-112.5 270q0 160 115.5 270.5t316.5 135.5v141q0 23 14 36t35 13h127q23 0 36 -13t13 -36v-145 q133 -18 228.5 -78.5t145.5 -140.5t54 -156q0 -18 -12 -30.5t-31 -12.5h-194q-43 0 -60 37q-12 66 -80.5 110t-171.5 44q-113 0 -177 -43.5t-64 -124.5q0 -55 31.5 -92t102 -66t201.5 -62q182 -41 290 -92t159 -129t51 -198q0 -178 -128 -289t-345 -133v-144 q0 -23 -13.5 -36t-35.5 -13h-127q-21 0 -35 13.5t-14 35.5v144q-147 14 -253 71.5t-160 140.5t-58 177z" />
<glyph unicode="%" horiz-adv-x="1607" d="M86 1071q0 61 2 98q8 127 86 201t219 74t220 -74t87 -201q4 -74 5 -98q0 -23 -5 -92q-8 -123 -89 -194.5t-218 -71.5q-135 0 -216 71.5t-89 194.5q-2 35 -2 92zM182 41q0 12 8 22l1010 1332q16 20 30.5 29.5t41.5 9.5h108q20 0 31.5 -11.5t11.5 -29.5q0 -12 -8 -23 l-1009 -1331q-16 -20 -31 -29.5t-39 -9.5h-109q-20 0 -32.5 11.5t-12.5 29.5zM272 1075q0 -51 2 -86q4 -51 32 -87t87 -36q60 0 87.5 35t33.5 88q4 70 4 86q0 23 -4 84q-4 53 -32.5 89t-88.5 36q-57 0 -85.5 -36t-33.5 -89q-2 -31 -2 -84zM905 354q0 59 2 97q8 127 86 200.5 t219 73.5q143 0 221 -73.5t87 -200.5q4 -74 4 -97t-4 -92q-8 -125 -89 -199.5t-219 -74.5q-135 0 -216 74.5t-89 199.5q-2 35 -2 92zM1092 358q0 -53 2 -88q4 -51 31.5 -87t86.5 -36q62 0 89.5 36t31.5 87q4 70 4 88q0 20 -4 82q-4 53 -32.5 89t-88.5 36q-57 0 -85.5 -35.5 t-32.5 -89.5q-2 -31 -2 -82z" />
<glyph unicode="&#x26;" horiz-adv-x="1472" d="M102 391q0 127 74 220.5t209 170.5q-86 92 -124 167t-38 155q0 94 49.5 174t143.5 128t223 48q123 0 214 -46t140 -126t49 -174q0 -121 -75.5 -207t-221.5 -168l293 -289q72 125 107 271q2 16 13 25.5t30 9.5h151q18 0 30.5 -12.5t12.5 -28.5q-2 -80 -57 -200t-131 -220 l219 -221q14 -14 14 -31q0 -16 -10 -26.5t-28 -10.5h-201q-33 0 -53 20l-113 109q-168 -150 -420 -149q-149 0 -262 51t-175.5 144t-62.5 216zM362 401q0 -94 71 -150t169 -56q156 0 260 104l-325 319q-174 -94 -175 -217zM475 1102q0 -47 26.5 -94t98.5 -119 q100 53 151.5 100t51.5 113t-47 107.5t-117 41.5q-68 0 -116 -41t-48 -108z" />
<glyph unicode="'" horiz-adv-x="473" d="M98 1399q-2 25 10.5 40t36.5 15h183q23 0 36 -14.5t11 -40.5l-41 -393q-8 -51 -51 -52h-93q-43 1 -51 52z" />
<glyph unicode="(" horiz-adv-x="741" d="M100 633q0 502 133.5 729t411.5 227q20 0 34.5 -14t14.5 -35v-125q0 -20 -14 -34.5t-33 -14.5q-113 -4 -176 -82t-91 -235.5t-28 -415.5t28 -417t91.5 -236.5t175.5 -81.5q18 0 32.5 -14.5t14.5 -35.5v-124q0 -20 -14 -35t-35 -15q-278 0 -411.5 228.5t-133.5 730.5z" />
<glyph unicode=")" horiz-adv-x="741" d="M49 -152q0 20 14.5 35t30.5 15q113 4 177.5 81.5t92 236.5t27.5 417t-27.5 415.5t-92 235.5t-177.5 82q-16 0 -30.5 14.5t-14.5 34.5v125q0 20 14.5 34.5t34.5 14.5q276 0 409.5 -228t133.5 -728q0 -502 -133 -730.5t-410 -228.5q-20 0 -34.5 14.5t-14.5 35.5v124z" />
<glyph unicode="*" horiz-adv-x="911" d="M98 1182l47 143q4 16 21.5 24.5t34.5 4.5q6 0 16 -8l160 -95l-39 183l-2 16q0 16 13 30.5t32 14.5h149q18 0 31.5 -14.5t13.5 -30.5l-2 -16l-41 -183l162 95l17 8q4 2 10 2q16 0 29.5 -8.5t15.5 -22.5l49 -143l2 -13q0 -14 -9 -27.5t-24 -17.5l-14 -4l-188 -18l141 -121 q12 -12 12 -14q8 -12 8 -25q0 -25 -18 -37l-121 -90q-12 -6 -26 -6q-27 0 -37 16q-2 2 -4 5.5t-7 9.5l-73 174l-76 -174q-4 -6 -6 -9.5t-4 -5.5q-10 -16 -37 -16q-14 0 -27 6l-119 90q-18 12 -18 37q0 12 8 25l10 14l142 123l-187 16l-16 4q-16 4 -24.5 21.5t-4.5 36.5z" />
<glyph unicode="+" horiz-adv-x="1257" d="M78 565v111q0 20 14 34.5t35 14.5h391v387q0 23 14.5 36t34.5 13h123q23 0 36 -13t13 -36v-387h391q20 0 35 -14.5t15 -34.5v-111q0 -20 -13.5 -34.5t-36.5 -14.5h-391v-399q0 -20 -13 -34.5t-36 -14.5h-123q-20 0 -34.5 14t-14.5 35v399h-391q-20 0 -34.5 14.5 t-14.5 34.5z" />
<glyph unicode="," horiz-adv-x="542" d="M86 -111l53 351q12 66 68 65h190q14 0 25.5 -11t11.5 -26q0 -10 -6 -28l-133 -340q-20 -53 -68 -54h-106q-17 0 -27 12.5t-8 30.5z" />
<glyph unicode="-" horiz-adv-x="983" d="M127 545v139q0 20 14.5 34.5t34.5 14.5h631q23 0 36 -13t13 -36v-139q0 -20 -14.5 -34.5t-34.5 -14.5h-631q-20 0 -34.5 14t-14.5 35z" />
<glyph unicode="." horiz-adv-x="550" d="M123 49v207q0 20 14.5 34.5t34.5 14.5h207q20 0 34.5 -14.5t14.5 -34.5v-207q0 -20 -13.5 -34.5t-35.5 -14.5h-207q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="/" horiz-adv-x="1042" d="M51 -129q0 12 4 23l697 1673q8 16 24.5 29.5t40.5 13.5h133q18 0 30.5 -12.5t12.5 -30.5q0 -12 -4 -23l-700 -1673q-6 -16 -21.5 -29.5t-42.5 -13.5h-133q-16 0 -28.5 12.5t-12.5 30.5z" />
<glyph unicode="0" horiz-adv-x="1349" d="M117 717q0 135 2 192q10 242 147 393.5t408 151.5q270 0 407.5 -151.5t147.5 -393.5q4 -115 4 -192q0 -78 -4 -189q-12 -246 -146.5 -397t-408.5 -151q-275 0 -409 151.5t-146 396.5q-2 55 -2 189zM414 721q0 -123 2 -182q4 -154 67.5 -237t190.5 -83t190.5 83t67.5 237 q2 59 2 182t-2 178q-4 150 -67.5 234t-190.5 86q-127 -2 -190.5 -86t-67.5 -234q-2 -55 -2 -178z" />
<glyph unicode="1" horiz-adv-x="950" d="M41 1022q0 25 22 41l459 356q23 14 51 15h185q20 0 34.5 -14.5t14.5 -35.5v-1335q0 -20 -14.5 -34.5t-34.5 -14.5h-191q-20 0 -34.5 14.5t-14.5 34.5v1057l-309 -238q-12 -10 -31 -10q-25 0 -39 21l-88 114q-10 14 -10 29z" />
<glyph unicode="2" horiz-adv-x="1269" d="M92 49v107q0 57 51 96l320 317q145 115 224 187.5t118 134t39 121.5q0 96 -51.5 151.5t-155.5 55.5q-102 0 -160.5 -60.5t-73.5 -157.5q-6 -25 -23.5 -36t-37.5 -11h-184q-18 0 -30.5 12.5t-12.5 30.5q4 121 66.5 226.5t179 170t274.5 64.5q164 0 278.5 -56t172 -154.5 t57.5 -223.5q0 -137 -77 -248.5t-239 -238.5l-284 -289h579q23 0 37.5 -14.5t14.5 -36.5v-148q0 -23 -14.5 -36t-37.5 -13h-981q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="3" horiz-adv-x="1292" d="M76 360q0 18 12 30.5t31 12.5h188q45 0 62 -41q45 -145 270 -145q125 0 195.5 57.5t70.5 163.5t-64.5 154.5t-189.5 48.5h-211q-20 0 -34.5 14.5t-14.5 34.5v88q0 39 27 62l364 356h-583q-23 0 -37.5 14.5t-14.5 34.5v137q0 23 14.5 37.5t37.5 14.5h880q23 0 37 -14.5 t14 -37.5v-125q0 -35 -26 -57l-352 -362l24 -2q197 -16 312.5 -119t115.5 -293q0 -135 -72.5 -235.5t-200.5 -154.5t-292 -54q-182 0 -308 57t-188.5 144t-66.5 179z" />
<glyph unicode="4" horiz-adv-x="1337" d="M61 360v142q0 33 23 61l604 836q10 16 30.5 25.5t45.5 9.5h225q23 0 36 -13.5t13 -36.5v-829h185q23 0 36 -13.5t13 -35.5v-146q0 -20 -13.5 -34.5t-35.5 -14.5h-185v-262q0 -20 -13 -34.5t-36 -14.5h-180q-21 0 -35 14.5t-14 34.5v262h-649q-20 0 -35 14.5t-15 34.5z M350 547h416v588z" />
<glyph unicode="5" horiz-adv-x="1271" d="M80 389v4q0 16 13 27.5t30 11.5h194q47 0 60 -41q29 -90 95.5 -133t156.5 -43q115 0 189.5 69.5t74.5 192.5q0 111 -75 179.5t-189 68.5q-61 0 -100.5 -16.5t-75.5 -44.5q-4 -2 -18.5 -12.5t-26 -14.5t-23.5 -4h-192q-18 0 -32 13t-14 32l70 698q2 27 19.5 42.5 t42.5 15.5h770q23 0 36 -13.5t13 -36.5v-139q0 -20 -13.5 -34.5t-35.5 -14.5h-592l-33 -332q47 31 108.5 50.5t159.5 19.5q133 0 247 -56.5t182.5 -162t68.5 -244.5q0 -150 -69.5 -261.5t-196.5 -172t-295 -60.5q-170 0 -294 57.5t-187.5 152t-67.5 202.5z" />
<glyph unicode="6" horiz-adv-x="1286" d="M82 483q0 180 160 398l372 512q16 20 30.5 30.5t41.5 10.5h195q18 0 29.5 -13.5t11.5 -31.5q0 -14 -9 -27l-286 -395q33 8 86 8q137 -2 252.5 -66.5t184.5 -175t69 -246.5q0 -139 -69 -254.5t-197 -184t-298 -68.5q-172 0 -302 65.5t-200.5 180t-70.5 257.5zM379 483 q0 -125 79 -195.5t193 -70.5q115 0 195 70.5t80 195.5q0 123 -79 194t-196 71q-115 0 -193.5 -71t-78.5 -194z" />
<glyph unicode="7" horiz-adv-x="1116" d="M59 1237v145q0 23 13.5 37.5t36.5 14.5h901q23 0 37 -14.5t14 -37.5v-129q0 -43 -21 -88l-487 -1116q-12 -23 -26.5 -36t-39.5 -13h-190q-18 0 -30.5 13.5t-12.5 31.5q0 6 4 18l492 1125h-641q-20 0 -35 14.5t-15 34.5z" />
<glyph unicode="8" horiz-adv-x="1345" d="M102 422q0 115 56.5 204t152.5 136q-76 47 -116.5 121.5t-40.5 171.5q0 180 136 289.5t382 109.5t381 -108.5t135 -288.5q0 -96 -40 -171t-116 -124q98 -47 154.5 -136t56.5 -206q0 -129 -67.5 -228.5t-196.5 -155.5t-307 -56t-306 56t-196 156.5t-68 229.5zM391 420 q0 -102 80 -163.5t201 -61.5t201.5 61t80.5 164q0 102 -80.5 163.5t-201.5 61.5t-201 -60.5t-80 -164.5zM426 1044q0 -88 69.5 -140t176.5 -52q106 2 177 54t71 138q0 88 -70 141.5t-178 53.5q-106 0 -176 -53t-70 -142z" />
<glyph unicode="9" horiz-adv-x="1265" d="M68 956q0 129 63.5 243t188 184.5t301.5 70.5q178 0 305 -66.5t191.5 -178t64.5 -244.5q0 -84 -27 -158t-60.5 -128t-95.5 -140l-358 -498q-16 -20 -30.5 -30.5t-39.5 -10.5h-196q-19 0 -31 13.5t-12 31.5q0 12 10 27l301 413q-35 -8 -86 -8q-135 4 -246.5 68.5t-177 172 t-65.5 238.5zM360 956q0 -117 75 -187.5t188 -70.5q112 0 188 71t76 187q0 119 -76 190t-188 71q-111 0 -187 -71t-76 -190z" />
<glyph unicode=":" horiz-adv-x="563" d="M129 49v197q0 23 14.5 37t34.5 14h205q23 0 37 -14.5t14 -36.5v-197q0 -20 -14 -34.5t-37 -14.5h-205q-20 0 -34.5 14.5t-14.5 34.5zM129 766v197q0 23 14.5 37t34.5 14h205q23 0 37 -14.5t14 -36.5v-197q0 -20 -14 -34.5t-37 -14.5h-205q-20 0 -34.5 14t-14.5 35z" />
<glyph unicode=";" horiz-adv-x="587" d="M104 -111l56 351q4 29 20.5 47t44.5 18h191q14 0 25.5 -11t11.5 -26q0 -10 -7 -28l-131 -340q-12 -25 -26.5 -39.5t-42.5 -14.5h-107q-16 0 -26.5 12.5t-8.5 30.5zM156 766v197q0 23 14 37t35 14h205q23 0 37 -14.5t14 -36.5v-197q0 -20 -14.5 -34.5t-36.5 -14.5h-205 q-21 0 -35 14t-14 35z" />
<glyph unicode="&#x3c;" horiz-adv-x="1034" d="M68 694v49q0 29 13 49.5t46 43.5l694 473q23 18 41 18t29.5 -11t11.5 -32v-158q0 -27 -12 -42t-39 -33l-496 -332l496 -332q27 -18 39 -33.5t12 -40.5v-159q0 -18 -12 -29.5t-29 -11.5q-21 0 -41 16l-694 475q-31 20 -45 41t-14 49z" />
<glyph unicode="=" horiz-adv-x="1163" d="M135 301v115q0 23 14.5 36t34.5 13h795q20 0 34.5 -14.5t14.5 -34.5v-115q0 -20 -14.5 -34.5t-34.5 -14.5h-795q-20 0 -34.5 14.5t-14.5 34.5zM135 813v115q0 23 14.5 36t34.5 13h795q20 0 34.5 -14.5t14.5 -34.5v-115q0 -20 -14.5 -34.5t-34.5 -14.5h-795 q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x3e;" horiz-adv-x="1032" d="M131 154v159q0 25 12.5 40.5t38.5 33.5l496 332l-496 332q-27 18 -39 33.5t-12 41.5v158q0 20 12.5 31.5t28.5 11.5q18 0 41 -18l694 -473q33 -23 46.5 -43.5t13.5 -49.5v-49q0 -29 -14.5 -49t-45.5 -41l-694 -475q-20 -16 -41 -16q-16 0 -28.5 11t-12.5 30z" />
<glyph unicode="?" horiz-adv-x="1159" d="M61 1024q4 113 69 211t185.5 158.5t284.5 60.5q170 0 279.5 -54t160 -138t50.5 -175q0 -102 -46.5 -177.5t-136.5 -174.5q-66 -70 -101.5 -120t-47.5 -107q-8 -51 -10 -70q-10 -27 -22.5 -39t-35.5 -12h-203q-18 0 -30.5 12.5t-12.5 28.5q0 43 2 66q10 96 61.5 169.5 t141.5 163.5q70 70 103.5 115t38.5 94q8 84 -54.5 133.5t-150.5 49.5q-199 0 -240 -199q-14 -47 -59 -47h-178q-20 0 -34 14t-14 37zM432 49v182q0 20 14.5 35t34.5 15h203q20 0 35.5 -14.5t15.5 -35.5v-182q0 -20 -14 -34.5t-37 -14.5h-203q-20 0 -34.5 14.5t-14.5 34.5z " />
<glyph unicode="@" horiz-adv-x="1718" d="M106 559q0 102 5 172q20 186 108 330.5t247 228.5t378 84q727 0 764 -643q4 -82 4 -133q0 -76 -4 -129q-8 -147 -90 -227t-205 -80q-90 0 -150.5 34.5t-89.5 75.5q-33 -55 -91 -93t-171 -38q-154 0 -243 110.5t-89 307.5t88 308.5t240 111.5q74 0 123 -25.5t84 -70.5v30 q0 23 14 36.5t35 13.5h82q23 0 36 -13.5t13 -36.5v-426q0 -76 31.5 -114.5t87.5 -38.5q63 0 90 47t27 106q4 61 4 129q0 31 -4 105q-16 246 -150.5 362.5t-435.5 116.5q-250 0 -388.5 -126t-162.5 -353q-6 -51 -6 -162q0 -109 6 -168q23 -229 165 -351t402 -122 q127 0 209 17.5t124 41t93 60.5q18 14 28.5 21.5t28.5 7.5h115q20 0 34.5 -14.5t14.5 -35.5q0 -10 -12 -30q-217 -242 -635 -242q-344 0 -531.5 169t-217.5 468q-4 78 -5 178zM659 559q0 -113 44.5 -179.5t132.5 -66.5q94 0 136 61.5t42 184.5t-42 185.5t-136 62.5 q-176 0 -177 -248z" />
<glyph unicode="A" horiz-adv-x="1421" d="M29 43l4 18l491 1321q20 51 74 52h225q53 0 74 -52l492 -1321l4 -18q0 -18 -13.5 -30.5t-29.5 -12.5h-183q-43 0 -59 39l-94 248h-606l-95 -248q-16 -39 -59 -39h-182q-19 0 -31 12.5t-12 30.5zM479 524h463l-231 631z" />
<glyph unicode="B" horiz-adv-x="1406" d="M160 49v1333q0 23 13 37.5t36 14.5h594q240 0 358.5 -105.5t118.5 -288.5q0 -106 -52 -181t-122 -107q86 -39 145.5 -127t59.5 -201q0 -190 -129 -307t-361 -117h-612q-21 0 -35 14.5t-14 34.5zM449 219h337q106 0 166 58.5t60 148.5t-60.5 148.5t-165.5 58.5h-337v-414z M449 846h317q104 0 160.5 51t56.5 137t-55.5 133t-161.5 47h-317v-368z" />
<glyph unicode="C" horiz-adv-x="1402" d="M109 715q0 127 2 184q10 262 165.5 408.5t436.5 146.5q182 0 317 -62.5t210 -171t79 -243.5v-4q0 -16 -13.5 -27.5t-29.5 -11.5h-199q-25 0 -37 11t-20 40q-31 127 -106.5 179.5t-200.5 52.5q-293 0 -305 -328q-2 -55 -2 -170t2 -174q12 -328 305 -328q123 0 199.5 52.5 t107.5 179.5q8 29 20.5 40t36.5 11h199q18 0 31.5 -12.5t11.5 -30.5q-4 -135 -79 -244t-210 -171t-317 -62q-283 0 -437.5 144t-164.5 411q-2 55 -2 180z" />
<glyph unicode="D" horiz-adv-x="1439" d="M160 49v1333q0 23 13 37.5t36 14.5h500q604 0 616 -557q4 -119 4 -160q0 -39 -4 -158q-8 -287 -154.5 -423t-451.5 -136h-510q-21 0 -35 14.5t-14 34.5zM449 238h260q170 0 245.5 75.5t79.5 251.5q4 123 4 154q0 33 -4 151q-4 168 -84 247t-252 79h-249v-958z" />
<glyph unicode="E" horiz-adv-x="1275" d="M160 49v1333q0 23 13 37.5t36 14.5h903q23 0 37 -14.5t14 -37.5v-137q0 -23 -14 -36t-37 -13h-672v-358h627q23 0 37 -14.5t14 -37.5v-129q0 -23 -14 -36t-37 -13h-627v-370h688q23 0 37.5 -13.5t14.5 -36.5v-139q0 -23 -14.5 -36t-37.5 -13h-919q-21 0 -35 14.5 t-14 34.5z" />
<glyph unicode="F" horiz-adv-x="1232" d="M160 49v1333q0 23 13 37.5t36 14.5h891q23 0 37 -14.5t14 -37.5v-147q0 -20 -14.5 -34.5t-36.5 -14.5h-656v-385h615q23 0 37 -14.5t14 -34.5v-148q0 -20 -14.5 -34.5t-36.5 -14.5h-615v-506q0 -20 -14 -34.5t-37 -14.5h-184q-21 0 -35 14.5t-14 34.5z" />
<glyph unicode="G" horiz-adv-x="1435" d="M111 721q0 131 2 190q8 254 167.5 398.5t438.5 144.5q188 0 324.5 -64.5t206 -159.5t73.5 -188q0 -18 -12.5 -30.5t-32.5 -12.5h-215q-20 0 -30.5 8.5t-18.5 26.5q-29 76 -100.5 129.5t-194.5 53.5q-143 0 -224 -77t-85 -239q-2 -59 -2 -180q0 -119 2 -180 q4 -166 86 -245t225 -79q145 0 233 78t88 233v64h-249q-20 0 -35 14.5t-15 34.5v113q0 20 14.5 34.5t35.5 14.5h491q23 0 37 -14.5t14 -34.5v-217q0 -170 -75.5 -296t-215 -193.5t-325.5 -67.5q-283 0 -439.5 145t-166.5 405q-2 59 -2 191z" />
<glyph unicode="H" horiz-adv-x="1495" d="M160 49v1333q0 23 13 37.5t36 14.5h188q23 0 37.5 -14.5t14.5 -37.5v-526h598v526q0 23 14 37.5t35 14.5h190q23 0 36 -14.5t13 -37.5v-1333q0 -20 -13 -34.5t-36 -14.5h-190q-20 0 -34.5 14.5t-14.5 34.5v547h-598v-547q0 -20 -14.5 -34.5t-37.5 -14.5h-188 q-21 0 -35 14.5t-14 34.5z" />
<glyph unicode="I" horiz-adv-x="614" d="M160 49v1335q0 23 14 36.5t35 13.5h197q23 0 36 -13.5t13 -36.5v-1335q0 -20 -13.5 -34.5t-35.5 -14.5h-197q-21 0 -35 14.5t-14 34.5z" />
<glyph unicode="J" horiz-adv-x="1337" d="M61 432q0 18 12.5 31.5t32.5 13.5h199q47 0 60 -51q16 -106 86.5 -157.5t179.5 -51.5q127 0 195.5 83t68.5 232v656h-694q-20 0 -34.5 14.5t-14.5 36.5v145q0 23 14 36.5t35 13.5h940q23 0 36 -14.5t13 -37.5v-858q0 -170 -69.5 -292.5t-197.5 -187t-300 -64.5 q-152 0 -278 51t-202.5 153.5t-81.5 247.5z" />
<glyph unicode="K" horiz-adv-x="1296" d="M160 49v1333q0 23 13 37.5t36 14.5h184q23 0 37 -14.5t14 -37.5v-479l457 494q31 37 80 37h209q16 0 29.5 -12.5t13.5 -30.5q0 -14 -10 -25l-557 -618l598 -678q8 -14 8 -27q0 -18 -12.5 -30.5t-28.5 -12.5h-215q-43 0 -61.5 17.5t-20.5 19.5l-490 541v-529 q0 -20 -14 -34.5t-37 -14.5h-184q-21 0 -35 14.5t-14 34.5z" />
<glyph unicode="L" horiz-adv-x="1189" d="M160 49v1335q0 23 14 36.5t35 13.5h192q23 0 36.5 -13.5t13.5 -36.5v-1138h649q23 0 37 -14.5t14 -34.5v-148q0 -20 -14.5 -34.5t-36.5 -14.5h-891q-21 0 -35 14.5t-14 34.5z" />
<glyph unicode="M" horiz-adv-x="1662" d="M160 49v1333q0 23 13 37.5t36 14.5h166q39 0 61 -39l395 -733l396 733q23 39 61 39h164q23 0 37 -14.5t14 -37.5v-1333q0 -23 -14 -36t-37 -13h-176q-20 0 -34.5 14.5t-14.5 34.5v881l-285 -541q-23 -45 -68 -45h-86q-43 0 -69 45l-285 541v-881q0 -20 -13 -34.5 t-36 -14.5h-176q-21 0 -35 14.5t-14 34.5z" />
<glyph unicode="N" horiz-adv-x="1447" d="M160 49v1333q0 23 13 37.5t36 14.5h160q39 0 59 -35l586 -910v893q0 23 13 37.5t36 14.5h176q23 0 37 -14.5t14 -37.5v-1331q0 -23 -14 -37t-35 -14h-162q-41 0 -61 35l-584 889v-875q0 -20 -13 -34.5t-36 -14.5h-176q-21 0 -35 14.5t-14 34.5z" />
<glyph unicode="O" horiz-adv-x="1423" d="M111 715q0 117 2 176q8 272 167.5 417.5t432.5 145.5q270 0 431 -145.5t169 -417.5q4 -119 4 -176q0 -55 -4 -174q-8 -279 -165 -420t-435 -141q-281 0 -436.5 141t-163.5 420q-2 59 -2 174zM408 717q0 -104 2 -166q4 -168 84.5 -251t218.5 -83q137 0 217 82t86 252 q4 123 4 166q0 47 -4 166q-6 168 -88 251t-215 83q-135 0 -217 -83t-86 -251q-2 -59 -2 -166z" />
<glyph unicode="P" horiz-adv-x="1351" d="M160 49v1333q0 23 13 37.5t36 14.5h549q248 0 389 -117t141 -340q0 -221 -141 -335t-389 -114h-301v-479q0 -23 -14.5 -36t-36.5 -13h-197q-21 0 -35 14.5t-14 34.5zM453 758h295q119 0 181 56t62 165q0 106 -60.5 165.5t-182.5 59.5h-295v-446z" />
<glyph unicode="Q" horiz-adv-x="1423" d="M109 715q0 117 2 176q8 272 168.5 417.5t431.5 145.5q272 0 432 -145.5t168 -417.5q4 -119 4 -176q0 -55 -4 -174q-8 -270 -148 -406l144 -205q6 -12 6 -20v-4q0 -16 -12.5 -27.5t-28.5 -11.5h-193q-26 0 -43.5 13.5t-31.5 33.5l-76 102q-90 -36 -210 -36h-7 q-279 0 -435.5 141t-164.5 420q-2 59 -2 174zM406 717q0 -104 2 -166q6 -170 85.5 -252t217.5 -82q137 0 218 83t85 251q4 123 4 166q0 47 -4 166q-4 168 -86 251t-217 83t-216 -83t-87 -251q-2 -59 -2 -166z" />
<glyph unicode="R" horiz-adv-x="1380" d="M160 49v1333q0 23 13 37.5t36 14.5h530q252 0 394.5 -117t142.5 -332q0 -150 -71.5 -251t-200.5 -146l296 -522q6 -12 7 -23q0 -18 -13.5 -30.5t-29.5 -12.5h-189q-33 0 -51 15.5t-33 39.5l-262 486h-280v-492q0 -20 -14.5 -34.5t-37.5 -14.5h-188q-21 0 -35 14.5 t-14 34.5zM449 774h284q123 0 184.5 54.5t61.5 158.5t-61.5 160.5t-184.5 56.5h-284v-430z" />
<glyph unicode="S" horiz-adv-x="1316" d="M74 377q0 16 12 29.5t31 13.5h186q23 0 37 -10.5t27 -28.5q20 -70 92.5 -117t199.5 -47q143 0 216 48t73 132q0 55 -37 92t-111.5 65t-221.5 67q-242 57 -354.5 151t-112.5 270q0 119 65.5 212.5t187 146.5t283.5 53q170 0 294 -59.5t188.5 -148.5t68.5 -177 q0 -16 -12 -29.5t-31 -13.5h-194q-45 0 -62 37q-10 66 -79.5 110t-172.5 44q-113 0 -177 -43.5t-64 -124.5q0 -55 32.5 -92t102 -65t202.5 -63q182 -41 290 -92t159 -129t51 -198q0 -133 -72.5 -230.5t-204.5 -148.5t-307 -51q-184 0 -315 55t-198.5 145t-71.5 197z" />
<glyph unicode="T" horiz-adv-x="1241" d="M47 1229v153q0 23 13.5 37.5t35.5 14.5h1047q23 0 37 -14.5t14 -37.5v-153q0 -23 -14.5 -37t-36.5 -14h-377v-1129q0 -20 -13.5 -34.5t-35.5 -14.5h-193q-20 0 -34.5 14.5t-14.5 34.5v1129h-379q-20 0 -34.5 14t-14.5 37z" />
<glyph unicode="U" horiz-adv-x="1462" d="M147 549v833q0 23 13.5 37.5t36.5 14.5h188q23 0 37 -14.5t14 -37.5v-833q0 -162 77 -244t218 -82t218 82t77 244v833q0 23 14.5 37.5t36.5 14.5h189q23 0 36 -14.5t13 -37.5v-833q0 -287 -152.5 -428t-429.5 -141q-278 0 -432 141t-154 428z" />
<glyph unicode="V" horiz-adv-x="1382" d="M57 1391q0 18 12.5 30.5t30.5 12.5h183q27 0 43 -14.5t22 -30.5l342 -1049l344 1049q4 16 20.5 30.5t43.5 14.5h184q16 0 29.5 -12.5t13.5 -30.5l-4 -19l-436 -1315q-20 -57 -80 -57h-227q-62 0 -80 57l-439 1315q-2 6 -2 19z" />
<glyph unicode="W" horiz-adv-x="1671" d="M78 1391q0 18 12 30.5t31 12.5h184q27 0 39 -9.5t16 -31.5l170 -922l183 594q20 51 67 51h113q27 0 43 -15.5t22 -35.5l185 -594l168 922q6 41 57 41h184q16 0 28.5 -12.5t12.5 -30.5q0 -10 -2 -17l-241 -1313q-4 -29 -23.5 -45t-50.5 -16h-139q-29 0 -47.5 15.5 t-24.5 35.5l-229 680l-230 -680q-16 -51 -71 -51h-140q-59 0 -73 61l-242 1313z" />
<glyph unicode="X" horiz-adv-x="1357" d="M35 43q0 8 8 25l457 663l-428 635q-8 12 -9 25q0 18 13.5 30.5t29.5 12.5h216q37 0 63 -39l299 -439l301 439q23 39 64 39h202q16 0 29.5 -12.5t13.5 -30.5q0 -8 -8 -25l-428 -635l459 -663q6 -12 6 -25q0 -18 -12.5 -30.5t-30.5 -12.5h-219q-37 0 -64 37l-323 461 l-320 -461q-27 -37 -63 -37h-213q-17 0 -30 12.5t-13 30.5z" />
<glyph unicode="Y" horiz-adv-x="1361" d="M45 1391q0 18 13.5 30.5t29.5 12.5h184q39 0 64 -39l344 -594l346 594q8 14 23.5 26.5t37.5 12.5h185q18 0 30.5 -12.5t12.5 -30.5q0 -12 -6 -23l-484 -858v-461q0 -23 -14 -36t-37 -13h-188q-21 0 -35 14.5t-14 34.5v461l-486 858q-6 18 -6 23z" />
<glyph unicode="Z" horiz-adv-x="1284" d="M80 51v146q0 27 9 43t22 32l710 916h-671q-20 0 -35 14.5t-15 36.5v143q0 23 13.5 37.5t36.5 14.5h980q23 0 37.5 -14.5t14.5 -37.5v-143q0 -41 -27 -72l-694 -921h694q23 0 36 -13.5t13 -35.5v-148q0 -20 -13 -34.5t-36 -14.5h-1026q-23 0 -36 14.5t-13 36.5z" />
<glyph unicode="[" horiz-adv-x="729" d="M137 -276v1816q0 23 14.5 36t34.5 13h439q23 0 36 -13t13 -36v-127q0 -20 -13.5 -34.5t-35.5 -14.5h-236v-1448h236q20 0 34.5 -14.5t14.5 -34.5v-143q0 -23 -13.5 -36.5t-35.5 -13.5h-439q-20 0 -34.5 13.5t-14.5 36.5z" />
<glyph unicode="\" horiz-adv-x="1042" d="M49 1567q0 18 12.5 30.5t28.5 12.5h135q25 0 41.5 -13.5t22.5 -29.5l698 -1673q4 -10 4 -23q0 -18 -12 -30.5t-31 -12.5h-131q-26 0 -41.5 13.5t-21.5 29.5l-701 1673q-4 10 -4 23z" />
<glyph unicode="]" horiz-adv-x="729" d="M53 -133q0 20 14.5 34.5t34.5 14.5h236v1448h-236q-20 0 -34.5 14.5t-14.5 34.5v127q0 20 14.5 34.5t34.5 14.5h441q23 0 36 -13t13 -36v-1816q0 -23 -13.5 -36.5t-35.5 -13.5h-441q-20 0 -34.5 14.5t-14.5 35.5v143z" />
<glyph unicode="^" horiz-adv-x="919" d="M115 1221q0 18 18 36l199 205q20 20 34.5 26.5t36.5 6.5h113q20 0 35.5 -6t34.5 -27l200 -205q18 -18 19 -36q0 -27 -31 -27h-76q-39 0 -65 14l-174 109l-174 -109q-23 -14 -64 -14h-76q-30 0 -30 27z" />
<glyph unicode="_" horiz-adv-x="1517" d="M109 -68v138q0 23 14 36t35 13h1202q23 0 37 -14.5t14 -34.5v-138q0 -20 -14.5 -34.5t-36.5 -14.5h-1202q-21 0 -35 13.5t-14 35.5z" />
<glyph unicode="`" horiz-adv-x="755" d="M129 1479q0 18 10.5 29.5t28.5 11.5h207q27 0 42 -8.5t34 -30.5l167 -203q10 -10 11 -27q0 -33 -33 -32h-129q-23 0 -38 6t-34 20l-256 209q-10 10 -10 25z" />
<glyph unicode="a" d="M66 293q0 139 112.5 225t308.5 117l281 41v43q0 88 -45 133t-143 45q-68 0 -109 -24.5t-66.5 -50t-33.5 -34.5q-8 -25 -31 -24h-160q-18 0 -31.5 12t-13.5 33q2 51 50 115.5t148.5 112.5t248.5 48q242 0 352 -108.5t110 -284.5v-643q0 -20 -13 -34.5t-36 -14.5h-170 q-20 0 -34.5 14.5t-14.5 34.5v80q-45 -66 -127 -107.5t-205 -41.5q-104 0 -191 41.5t-137 113.5t-50 158zM384 211.5q54 -35.5 130 -35.5q113 0 184.5 74t71.5 213v41l-205 -33q-235 -37 -235 -157q0 -67 54 -102.5z" />
<glyph unicode="b" horiz-adv-x="1257" d="M137 49v1356q0 23 14.5 36t34.5 13h179q23 0 36 -13.5t13 -35.5v-455q115 135 313 135q207 0 318.5 -134t117.5 -351q2 -25 2 -68q0 -41 -2 -67q-8 -221 -118.5 -353t-317.5 -132q-213 0 -326 145v-76q0 -20 -14 -34.5t-37 -14.5h-164q-20 0 -34.5 14.5t-14.5 34.5z M412 539q0 -53 2 -78q4 -109 61 -184.5t174 -75.5q220 0 234 270q2 20 2 61t-2 62q-14 270 -234 270q-113 0 -172 -71.5t-63 -174.5q-2 -25 -2 -79z" />
<glyph unicode="c" d="M92 532l2 82q8 221 142.5 346t359.5 125q162 0 273.5 -57t166 -140t58.5 -159q2 -20 -13.5 -34.5t-36.5 -14.5h-182q-23 0 -33 9t-20 34q-31 78 -81 112.5t-128 34.5q-104 0 -162.5 -65.5t-62.5 -200.5l-2 -76l2 -67q12 -266 225 -266q80 0 129 33.5t80 113.5 q8 23 19.5 33t33.5 10h182q20 0 36 -14.5t14 -34.5q-4 -72 -57.5 -155t-164 -142t-276.5 -59q-225 0 -359.5 124.5t-142.5 346.5z" />
<glyph unicode="d" horiz-adv-x="1257" d="M90 532l2 68q8 217 120 351t318 134q197 0 314 -135v455q0 23 14 36t35 13h178q23 0 36 -13.5t13 -35.5v-1356q0 -20 -13 -34.5t-36 -14.5h-166q-20 0 -34.5 14.5t-14.5 34.5v76q-113 -145 -326 -145q-206 0 -317 132t-121 353zM373 532l2 -61q14 -270 233 -270 q117 0 173.5 74.5t62.5 185.5q2 25 2 78q0 55 -2 79q-4 102 -63.5 174t-172.5 72q-219 0 -233 -270z" />
<glyph unicode="e" horiz-adv-x="1183" d="M88 535q0 256 134 403t372 147q244 0 376 -147t132 -393v-43q0 -20 -14.5 -34.5t-36.5 -14.5h-680v-17q4 -115 62.5 -189.5t160.5 -74.5q117 0 190 94q18 23 28.5 28t35.5 5h180q18 0 31.5 -10t13.5 -29q0 -49 -58.5 -115.5t-167 -115.5t-251.5 -49q-229 0 -363.5 131 t-142.5 372zM371 627h448v4q0 121 -60.5 193.5t-164.5 72.5t-163.5 -72.5t-59.5 -193.5v-4z" />
<glyph unicode="f" horiz-adv-x="849" d="M35 895v121q0 20 14 34.5t35 14.5h166v82q0 186 100.5 277.5t302.5 91.5h142q20 0 34.5 -14.5t14.5 -35.5v-120q0 -20 -14.5 -35t-34.5 -15h-131q-80 0 -113 -38.5t-33 -120.5v-72h256q20 0 34.5 -14.5t14.5 -34.5v-121q0 -20 -14 -34.5t-35 -14.5h-256v-797 q0 -20 -13 -34.5t-36 -14.5h-170q-20 0 -34.5 14.5t-14.5 34.5v797h-166q-20 0 -34.5 14t-14.5 35z" />
<glyph unicode="g" horiz-adv-x="1261" d="M92 545l2 55q8 219 116.5 352t319.5 133q109 0 191 -40.5t135 -110.5v80q0 23 15.5 37t35.5 14h168q20 0 34.5 -14.5t14.5 -36.5l2 -987q0 -227 -127 -352.5t-383 -125.5q-182 0 -294.5 60.5t-159.5 138.5t-47 133q0 20 14 33.5t35 13.5h182q18 0 31.5 -9t21.5 -34 q23 -53 68 -91t139 -38q123 0 180.5 54.5t57.5 189.5v139q-111 -131 -314 -131q-213 0 -320.5 129t-115.5 352zM373 545q0 -319 235 -320q115 0 172.5 74t63.5 180q2 16 2 66q0 49 -2 65q-6 106 -63.5 180t-172.5 74q-221 0 -233 -270z" />
<glyph unicode="h" horiz-adv-x="1290" d="M137 49v1356q0 23 14.5 36t34.5 13h187q23 0 36 -13.5t13 -35.5v-467q123 147 330 147q188 0 300.5 -123.5t112.5 -336.5v-576q0 -20 -13 -34.5t-36 -14.5h-186q-23 0 -37 14.5t-14 34.5v563q0 119 -58.5 185.5t-167.5 66.5q-104 0 -167.5 -67.5t-63.5 -184.5v-563 q0 -20 -13.5 -34.5t-35.5 -14.5h-187q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="i" horiz-adv-x="542" d="M127 1288v150q0 23 14.5 37t34.5 14h189q23 0 37 -14.5t14 -36.5v-150q0 -20 -15.5 -34.5t-35.5 -14.5h-189q-20 0 -34.5 14.5t-14.5 34.5zM135 49v967q0 23 14.5 36t34.5 13h174q20 0 35 -14.5t15 -34.5v-967q0 -20 -14.5 -34.5t-35.5 -14.5h-174q-20 0 -34.5 14.5 t-14.5 34.5z" />
<glyph unicode="j" horiz-adv-x="573" d="M-90 -219q0 20 13.5 34.5t35.5 14.5h53q86 0 116 44t30 130v1012q0 23 14 36t35 13h180q23 0 36 -13.5t13 -35.5v-1018q0 -184 -100 -285.5t-297 -101.5h-80q-22 0 -35.5 13.5t-13.5 35.5v121zM147 1288v150q0 20 14.5 35.5t35.5 15.5h198q20 0 34.5 -14.5t14.5 -36.5 v-150q0 -20 -14 -34.5t-35 -14.5h-198q-21 0 -35.5 14.5t-14.5 34.5z" />
<glyph unicode="k" horiz-adv-x="1130" d="M137 49v1356q0 23 14.5 36t34.5 13h170q20 0 35 -14.5t15 -34.5v-700l339 325q6 4 19.5 15.5t26 15.5t30.5 4h195q20 0 32.5 -12.5t12.5 -32.5q0 -18 -21 -37l-417 -395l469 -508q18 -18 18 -35q0 -20 -12.5 -32.5t-30.5 -12.5h-199q-29 0 -42 7t-33 28l-387 411v-397 q0 -20 -14.5 -34.5t-35.5 -14.5h-170q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="l" horiz-adv-x="544" d="M137 49v1356q0 23 14.5 36t34.5 13h172q23 0 36.5 -13.5t13.5 -35.5v-1356q0 -20 -13.5 -34.5t-36.5 -14.5h-172q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="m" horiz-adv-x="1849" d="M135 49v967q0 23 14.5 36t34.5 13h162q23 0 36 -13.5t13 -35.5v-70q111 139 297 139q227 0 318 -182q49 82 139 132t194 50q168 0 274.5 -114.5t106.5 -333.5v-588q0 -20 -13 -34.5t-36 -14.5h-170q-20 0 -34.5 14.5t-14.5 34.5v572q0 129 -54 186t-142 57 q-80 0 -136.5 -58t-56.5 -185v-572q0 -20 -13.5 -34.5t-35.5 -14.5h-172q-20 0 -34.5 14.5t-14.5 34.5v572q0 127 -55.5 185t-141.5 58q-80 0 -136 -58t-56 -185v-572q0 -20 -13.5 -34.5t-36.5 -14.5h-174q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="n" horiz-adv-x="1280" d="M135 49v967q0 23 14.5 36t34.5 13h170q23 0 36 -13.5t13 -35.5v-84q124 153 339 153h3q188 0 299 -122.5t111 -337.5v-576q0 -20 -13.5 -34.5t-35.5 -14.5h-184q-20 0 -35 14.5t-15 34.5v563q0 119 -58 185.5t-167 66.5q-104 0 -167.5 -67.5t-63.5 -184.5v-563 q0 -20 -13.5 -34.5t-35.5 -14.5h-183q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="o" horiz-adv-x="1214" d="M92 532l2 91q10 215 143.5 338.5t370.5 123.5q236 0 369 -123.5t143 -338.5q2 -25 2 -91t-2 -90q-10 -217 -141 -339.5t-371 -122.5q-241 0 -372.5 122.5t-141.5 339.5zM375 532l2 -79q4 -131 63.5 -199t167.5 -68q109 0 167.5 68t62.5 199q2 20 2 79t-2 80 q-4 131 -63.5 200t-166.5 69q-109 0 -168 -69t-63 -200z" />
<glyph unicode="p" horiz-adv-x="1257" d="M137 -340v1356q0 23 14.5 36t34.5 13h164q23 0 37 -14.5t14 -34.5v-82q108 151 323 151h3q207 0 317.5 -130t118.5 -349q2 -25 2 -74t-2 -73q-6 -215 -117.5 -347t-318.5 -132q-205 0 -313 145v-465q0 -23 -13.5 -36t-35.5 -13h-179q-20 0 -34.5 13.5t-14.5 35.5z M412 526q0 -53 2 -80q4 -102 63.5 -173.5t171.5 -71.5q220 0 234 270q2 20 2 61t-2 62q-14 270 -234 270q-117 0 -174 -75.5t-61 -184.5q-2 -25 -2 -78z" />
<glyph unicode="q" horiz-adv-x="1257" d="M92 532l2 74q8 219 119 349t317 130q215 0 328 -151v82q0 23 14.5 36t34.5 13h166q23 0 36 -13.5t13 -35.5v-1356q0 -23 -13 -36t-36 -13h-178q-21 0 -35 13.5t-14 35.5v465q-109 -145 -316 -145q-205 0 -316.5 132t-119.5 347zM375 532q0 -41 2 -61q14 -270 231 -270 q113 0 173.5 71.5t64.5 173.5q2 27 2 80t-2 78q-6 111 -63.5 185.5t-174.5 74.5q-217 0 -231 -270q-2 -20 -2 -62z" />
<glyph unicode="r" horiz-adv-x="858" d="M135 49v965q0 23 14.5 37t34.5 14h168q23 0 37 -14.5t14 -36.5v-84q102 135 295 135h86q23 0 36.5 -13.5t13.5 -35.5v-150q0 -20 -13.5 -34.5t-36.5 -14.5h-161q-96 0 -151.5 -55t-55.5 -152v-561q0 -20 -14.5 -34.5t-34.5 -14.5h-183q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="s" horiz-adv-x="1091" d="M76 244q0 20 13 32.5t32 12.5h168q16 0 28 -17q10 -8 46 -38.5t82.5 -48t101.5 -17.5q82 0 133 31.5t51 91.5q0 41 -23.5 66.5t-84 47t-181.5 48.5q-174 37 -257 112.5t-83 200.5q0 82 49.5 154.5t146.5 118.5t232 46q139 0 239.5 -44t153 -105.5t52.5 -108.5 q0 -18 -13.5 -31.5t-31.5 -13.5h-154q-23 0 -35 17q-14 10 -45.5 37.5t-70.5 44t-95 16.5q-76 0 -116.5 -33t-40.5 -86q0 -37 19.5 -61.5t78.5 -46t178 -43.5q369 -72 369 -322q0 -145 -126 -235t-351 -90q-156 0 -260.5 47t-154.5 109.5t-50 107.5z" />
<glyph unicode="t" horiz-adv-x="882" d="M33 895v121q0 20 14 34.5t35 14.5h162v340q0 23 14 36t35 13h170q23 0 36 -13.5t13 -35.5v-340h256q20 0 34.5 -14.5t14.5 -34.5v-121q0 -20 -14.5 -34.5t-34.5 -14.5h-256v-440q0 -88 30.5 -133.5t100.5 -45.5h143q23 0 36.5 -13t13.5 -36v-129q0 -20 -13.5 -34.5 t-36.5 -14.5h-168q-186 0 -280 97.5t-94 285.5v463h-162q-20 0 -34.5 14t-14.5 35z" />
<glyph unicode="u" horiz-adv-x="1273" d="M125 440v576q0 23 14.5 36t34.5 13h184q20 0 35 -14.5t15 -34.5v-563q0 -252 219 -252q106 0 168.5 67.5t62.5 184.5v563q0 23 14.5 36t34.5 13h183q23 0 36 -13.5t13 -35.5v-967q0 -20 -13.5 -34.5t-35.5 -14.5h-170q-20 0 -35 14.5t-15 34.5v84q-112 -153 -337 -153h-3 q-188 0 -296.5 122.5t-108.5 337.5z" />
<glyph unicode="v" horiz-adv-x="1177" d="M51 1020q0 18 13.5 31.5t29.5 13.5h170q41 0 55 -39l271 -715l270 715q4 14 17.5 26.5t35.5 12.5h170q18 0 30.5 -13.5t12.5 -31.5q0 -12 -2 -19l-383 -950q-23 -51 -73 -51h-156q-31 0 -47 13.5t-27 37.5l-385 950q-2 6 -2 19z" />
<glyph unicode="w" horiz-adv-x="1681" d="M63 1020q0 18 12.5 31.5t30.5 13.5h156q20 0 35.5 -12.5t19.5 -26.5l199 -674l211 668q6 18 21.5 31.5t39.5 13.5h105q25 0 40 -13.5t19 -31.5l213 -668l199 674q4 14 18.5 26.5t36.5 12.5h154q18 0 31.5 -13.5t13.5 -31.5l-4 -19l-293 -950q-8 -25 -23.5 -38t-42.5 -13 h-135q-53 0 -67 51l-211 643l-213 -643q-16 -51 -70 -51h-133q-52 0 -68 51l-292 950q-2 6 -3 19z" />
<glyph unicode="x" horiz-adv-x="1157" d="M45 45q0 18 12 35l351 467l-324 438q-12 16 -12 35q0 18 13 31.5t32 13.5h180q23 0 34 -9t23 -26l230 -305l227 305q2 2 15.5 18.5t41.5 16.5h174q18 0 30.5 -13.5t12.5 -29.5q0 -20 -12 -37l-328 -440l353 -465q12 -16 12 -35q0 -18 -13.5 -31.5t-31.5 -13.5h-188 q-33 0 -56 33l-246 323l-247 -323q-23 -33 -56 -33h-182q-18 0 -31.5 13.5t-13.5 31.5z" />
<glyph unicode="y" horiz-adv-x="1177" d="M53 1020q2 18 14.5 31.5t30.5 13.5h170q37 0 54 -39l272 -668l276 668q20 39 58 39h166q18 0 30.5 -12.5t12.5 -28.5q0 -14 -9 -33l-579 -1341q-16 -39 -57 -39h-164q-17 0 -30 12t-13 29q0 14 8 33l162 378l-394 928q-8 18 -8 29z" />
<glyph unicode="z" horiz-adv-x="1089" d="M82 49v119q0 18 7 32.5t22 35.5l522 612h-486q-20 0 -34.5 15.5t-14.5 35.5v117q0 20 14.5 34.5t34.5 14.5h781q20 0 34.5 -14.5t14.5 -34.5v-127q0 -16 -9.5 -32.5t-19.5 -31.5l-512 -608h533q20 0 34.5 -14.5t14.5 -36.5v-117q0 -20 -13.5 -34.5t-35.5 -14.5h-838 q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="{" horiz-adv-x="815" d="M39 604v58q0 29 12 47t41 32l23 13q59 35 84.5 92t25.5 151q0 293 121 441.5t361 150.5h12q18 0 31.5 -12t13.5 -33v-133q0 -18 -12.5 -31.5t-30.5 -13.5h-8q-92 -6 -143.5 -47t-72 -126t-20.5 -230q0 -123 -40 -208t-111 -122q72 -35 111.5 -122t39.5 -208 q0 -145 20.5 -230t72 -127t141.5 -46h10q18 -2 30.5 -14.5t12.5 -30.5v-136q0 -18 -13.5 -31.5t-31.5 -13.5h-10q-242 2 -363 151.5t-121 442.5q0 94 -26.5 152.5t-85.5 93.5l-21 10q-29 14 -41 32.5t-12 47.5z" />
<glyph unicode="|" horiz-adv-x="509" d="M139 -377v2163q0 23 14.5 36t34.5 13h136q20 0 34.5 -14.5t14.5 -34.5v-2163q0 -20 -14.5 -34.5t-34.5 -14.5h-136q-20 0 -34.5 13.5t-14.5 35.5z" />
<glyph unicode="}" horiz-adv-x="815" d="M51 -145q0 18 12.5 30.5t30.5 14.5h8q92 4 143.5 45t72 126t20.5 230q0 125 40 209t111 121q-72 37 -111.5 124t-39.5 208q0 145 -20.5 230t-71.5 126t-142 45l-10 2q-16 0 -29.5 13.5t-13.5 31.5v133q0 20 13.5 32.5t31.5 12.5h10q242 -2 363 -151.5t121 -442.5 q0 -94 26.5 -152.5t85.5 -90.5l21 -11q29 -16 41 -34.5t12 -44.5v-60q0 -27 -12 -45t-41 -35l-23 -10q-57 -35 -83.5 -93.5t-26.5 -152.5q0 -586 -481 -592h-13q-18 0 -31.5 13.5t-13.5 31.5v136z" />
<glyph unicode="~" horiz-adv-x="1140" d="M135 516v119q0 51 66.5 93t158.5 42q63 0 113.5 -10t112.5 -31q59 -18 102 -27.5t96 -9.5q49 0 80 15.5t72 44.5q16 10 33 12q39 0 39 -51v-121q0 -51 -67 -92t-159 -41q-59 0 -105 9t-114 30q-57 18 -103 28.5t-102 10.5q-49 0 -79.5 -15.5t-71.5 -44.5q-20 -12 -33 -12 q-39 0 -39 51z" />
<glyph unicode="&#xa1;" horiz-adv-x="536" d="M123 817v195q0 20 14.5 34.5t36.5 14.5h193q22 0 35.5 -13.5t13.5 -35.5v-195q0 -20 -13.5 -34.5t-35.5 -14.5h-193q-23 0 -37 14.5t-14 34.5zM135 -324v936q0 20 14.5 35t34.5 15h170q23 0 36 -13.5t13 -36.5v-936q0 -23 -13 -36t-36 -13h-170q-20 0 -34.5 14.5 t-14.5 34.5z" />
<glyph unicode="&#xa2;" horiz-adv-x="1185" d="M96 532l2 82q6 188 104.5 308t266.5 149v129q0 23 14.5 36t34.5 13h146q23 0 36 -13t13 -36v-127q131 -20 218 -83.5t127 -137.5t40 -127q0 -18 -15.5 -31.5t-33.5 -13.5h-183q-23 0 -33 9t-20 34q-31 78 -85 112.5t-132 34.5q-96 0 -154.5 -66.5t-62.5 -199.5l-2 -76 l2 -67q6 -135 63.5 -200.5t153.5 -65.5q80 0 133 34.5t84 112.5q8 23 19.5 33t33.5 10h183q18 0 33.5 -13.5t15.5 -31.5q0 -53 -40 -127t-126 -137.5t-219 -83.5v-148q0 -23 -13.5 -36t-35.5 -13h-146q-20 0 -34.5 13.5t-14.5 35.5v150q-168 29 -266.5 148.5t-104.5 308.5z " />
<glyph unicode="&#xa3;" horiz-adv-x="1433" d="M94 39q4 131 66.5 218t165.5 120q-2 57 -13.5 126.5t-13.5 86.5h-156q-20 0 -34.5 14t-14.5 35v115q0 23 14.5 36t34.5 13h115q-6 55 -6 123q0 246 136 388t392 142q164 0 286 -59.5t187.5 -166t69.5 -243.5q0 -18 -12.5 -30.5t-30.5 -12.5h-197q-45 0 -61 49 q-47 225 -242 226q-115 0 -172 -79t-57 -216q0 -84 4 -121h350q20 0 34.5 -14.5t14.5 -34.5v-115q0 -20 -14 -34.5t-35 -14.5h-334q16 -143 19 -217q51 -10 89 -28.5t91 -53.5t91 -52.5t85 -17.5q72 0 110 37t58 107q4 12 13.5 21t23.5 9h143q18 0 28.5 -11t10.5 -28 q-2 -117 -51 -199.5t-128 -126.5t-173 -44q-88 0 -153.5 26.5t-149.5 75.5q-70 41 -111 58.5t-90 17.5q-66 0 -98.5 -33t-48.5 -98q-4 -14 -12.5 -23.5t-26.5 -9.5h-158q-16 0 -27.5 11.5t-11.5 27.5z" />
<glyph unicode="&#xa4;" horiz-adv-x="1230" d="M119 244q0 23 14 35l90 90q-72 111 -71 245q0 131 71 246l-90 90q-14 14 -14 35q0 20 14 35l78 78q14 14 35 14q20 0 35 -14l90 -92q115 74 245 73q129 0 244 -73l92 92q14 14 35 14q20 0 33 -14l80 -78q14 -14 14 -35q0 -20 -14 -35l-92 -90q72 -115 71 -246 q0 -135 -71 -245l92 -90q14 -12 14 -35q0 -20 -14 -35l-80 -78q-14 -14 -33 -14q-16 0 -35 14l-92 92q-109 -74 -244 -73q-137 0 -245 73l-90 -92q-18 -14 -35 -14q-16 0 -35 14l-78 78q-14 14 -14 35zM360 614q0 -106 75 -181t181 -75q107 0 181.5 75t74.5 181t-74.5 181 t-181.5 75q-106 0 -181 -74.5t-75 -181.5z" />
<glyph unicode="&#xa5;" horiz-adv-x="1339" d="M63 1391q0 18 12.5 30.5t28.5 12.5h189q39 0 63 -39l314 -559l315 559q25 39 64 39h186q16 0 29.5 -12.5t13.5 -30.5q0 -5 -6 -23l-379 -688h199q23 0 37 -14.5t14 -34.5v-119q0 -20 -15.5 -34.5t-35.5 -14.5h-277v-72h277q23 0 37 -14.5t14 -34.5v-117 q0 -20 -14.5 -35.5t-36.5 -15.5h-277v-125q0 -23 -14.5 -36t-36.5 -13h-189q-23 0 -37 14.5t-14 34.5v125h-276q-20 0 -34.5 15.5t-14.5 35.5v117q0 20 14 34.5t35 14.5h276v72h-276q-20 0 -34.5 14.5t-14.5 34.5v119q0 20 14 34.5t35 14.5h198l-376 688q-6 10 -7 23z" />
<glyph unicode="&#xa6;" horiz-adv-x="522" d="M143 49v627q0 20 14.5 34.5t35.5 14.5h135q23 0 36 -13.5t13 -35.5v-627q0 -20 -13.5 -34.5t-35.5 -14.5h-135q-21 0 -35.5 14.5t-14.5 34.5zM143 1022v627q0 20 14.5 34.5t35.5 14.5h135q23 0 36 -13.5t13 -35.5v-627q0 -20 -13.5 -34.5t-35.5 -14.5h-135 q-21 0 -35.5 14t-14.5 35z" />
<glyph unicode="&#xa7;" horiz-adv-x="1204" d="M78 645q0 162 151 275q-45 61 -45 165q0 98 49.5 182.5t147.5 135.5t235 51q141 0 244 -46t155 -113.5t56 -133.5v-4q0 -18 -12 -28.5t-31 -10.5h-164q-18 0 -30.5 8.5t-22.5 28.5q-23 45 -63.5 72.5t-129.5 27.5q-82 0 -123.5 -40.5t-41.5 -106.5q0 -47 19.5 -75.5 t74.5 -52.5t164 -52q154 -43 243.5 -96.5t130.5 -128t41 -181.5q0 -160 -151 -274q45 -61 45 -166q0 -98 -50 -182t-147.5 -135.5t-234.5 -51.5q-142 0 -244 46t-155.5 115t-57.5 134v4q0 18 13.5 28.5t31.5 10.5h162q18 0 30.5 -8t22.5 -27q20 -47 62.5 -75.5t130.5 -28.5 q82 0 125 41t43 106q0 47 -19.5 76t-75 52.5t-163.5 52.5q-154 43 -244 96t-131 128t-41 181zM328 655q0 -47 19.5 -80.5t73.5 -63.5t157 -60q86 -25 198 -68q51 23 76 53.5t25 75.5q0 72 -51.5 116t-196.5 87q-127 39 -201 69q-100 -43 -100 -129z" />
<glyph unicode="&#xa8;" horiz-adv-x="882" d="M127 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM514 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5 t-12.5 33.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1673" d="M98 717q0 201 99.5 369.5t268.5 268t370 99.5t369.5 -99.5t268 -268t99.5 -369.5t-99.5 -370t-268.5 -268t-369 -99q-201 0 -370 99t-268.5 268t-99.5 370zM256 717q0 -160 78 -295t211 -215t291 -80q159 0 292.5 80t211 215t77.5 295t-77.5 295t-211 215t-292.5 80 q-158 0 -291 -80t-211 -215t-78 -295zM492 719l2 74q6 141 97 231t245 90q111 0 188.5 -36t115.5 -91t40 -110v-5q0 -18 -13.5 -29.5t-31.5 -11.5h-103q-20 0 -30.5 11.5t-18.5 29.5q-16 43 -51 62.5t-96 19.5q-141 0 -152 -166l-2 -69l2 -70q10 -166 152 -166q61 0 96 19.5 t51 62.5q8 20 18.5 30.5t30.5 10.5h103q20 0 33.5 -12t11.5 -33q-2 -55 -40 -110.5t-116 -91t-188 -35.5q-154 0 -245 90t-97 231z" />
<glyph unicode="&#xaa;" horiz-adv-x="737" d="M88 1038q0 78 61.5 126t169.5 65l121 18q0 29 -13 47.5t-60 18.5q-39 0 -70 -23q-6 -2 -17.5 -8t-25.5 -6h-94q-14 0 -24.5 7t-10.5 20q0 25 29.5 58.5t86 58t132.5 24.5q117 0 181 -59.5t64 -163.5v-293q0 -16 -11 -27.5t-27 -11.5h-97q-16 0 -27.5 11t-11.5 28v20 q-18 -31 -63 -50t-96 -19q-88 0 -142.5 45t-54.5 114zM260 1049q0 -18 18.5 -29.5t47.5 -11.5q59 0 86.5 33.5t27.5 91.5l-80 -15q-57 -10 -78.5 -27.5t-21.5 -41.5z" />
<glyph unicode="&#xab;" horiz-adv-x="1259" d="M74 637v74q4 39 32 67l410 398q23 18 41 18t30.5 -12.5t12.5 -30.5v-141q0 -29 -8 -45.5t-29 -36.5l-262 -254l262 -254q20 -20 28.5 -36.5t8.5 -45.5v-141q0 -18 -12.5 -30.5t-30.5 -12.5t-41 18l-410 399q-29 27 -32 66zM618 637v74q4 39 33 67l410 398q23 18 41 18 t30.5 -12.5t12.5 -30.5v-141q0 -29 -8.5 -45.5t-28.5 -36.5l-262 -254l262 -254q20 -20 28.5 -36.5t8.5 -45.5v-141q0 -18 -12.5 -30.5t-30.5 -12.5t-41 18l-410 399q-29 27 -33 66z" />
<glyph unicode="&#xac;" horiz-adv-x="1175" d="M127 551v117q0 23 14.5 36t34.5 13h813q23 0 37 -14.5t14 -34.5v-371q0 -20 -15 -34.5t-36 -14.5h-119q-20 0 -34.5 14t-14.5 35v205h-645q-20 0 -34.5 14t-14.5 35z" />
<glyph unicode="&#xad;" horiz-adv-x="512" />
<glyph unicode="&#xae;" horiz-adv-x="1673" d="M98 717q0 201 99.5 369.5t268.5 268t370 99.5t369.5 -99.5t268 -268t99.5 -369.5t-99.5 -370t-268.5 -268t-369 -99q-201 0 -370 99t-268.5 268t-99.5 370zM256 717q0 -160 78 -295t211 -215t291 -80q159 0 292.5 80t211 215t77.5 295t-77.5 295t-211 215t-292.5 80 q-158 0 -291 -80t-211 -215t-78 -295zM545 389v660q0 20 12 32.5t33 12.5h276q129 0 214 -59.5t85 -186.5q0 -78 -35.5 -131t-97.5 -80l121 -219q8 -12 8 -29q0 -18 -11 -31.5t-34 -13.5h-90q-37 0 -59 39l-113 219h-125v-213q0 -20 -12 -32.5t-33 -12.5h-94q-21 0 -33 12.5 t-12 32.5zM729 756h135q113 0 113 92q0 41 -26.5 67.5t-86.5 26.5h-135v-186z" />
<glyph unicode="&#xaf;" horiz-adv-x="862" d="M127 1296v107q0 20 12.5 32.5t32.5 12.5h516q20 0 33.5 -12.5t13.5 -32.5v-107q0 -20 -13 -32.5t-34 -12.5h-516q-20 0 -32.5 12.5t-12.5 32.5z" />
<glyph unicode="&#xb0;" horiz-adv-x="819" d="M80 1135q0 90 44 162.5t119.5 114.5t166.5 42q90 0 165.5 -42t119.5 -114.5t44 -162.5t-44 -163t-119.5 -115t-165.5 -42q-91 0 -166.5 42t-119.5 114.5t-44 163.5zM254 1135q0 -70 43 -113t113 -43q69 0 112 43t43 113t-43 112.5t-112 42.5q-70 0 -113 -43t-43 -112z " />
<glyph unicode="&#xb1;" horiz-adv-x="1155" d="M115 797v110q0 20 14 34.5t37 14.5h301v299q0 20 14.5 35t34.5 15h125q23 0 36 -13.5t13 -36.5v-299h301q23 0 36 -13t13 -36v-110q0 -20 -13 -34.5t-36 -14.5h-301v-310q0 -20 -13 -34.5t-36 -14.5h-125q-20 0 -34.5 14.5t-14.5 34.5v310h-301q-23 0 -37 14t-14 35z M137 49v111q0 23 14.5 36t34.5 13h785q23 0 36 -13.5t13 -35.5v-111q0 -20 -13.5 -34.5t-35.5 -14.5h-785q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#xb2;" horiz-adv-x="714" d="M57 762v57q0 16 10.5 31.5t38.5 38.5l103 74q143 104 190 149t47 98q0 37 -25.5 60.5t-68.5 23.5q-49 0 -67.5 -18.5t-34.5 -48.5q-12 -23 -22.5 -30t-26.5 -7h-90q-37 0 -37 35q0 59 38 109t102.5 80t137.5 30q125 0 200 -64.5t75 -162.5q0 -96 -62.5 -166t-222.5 -179 h270q20 0 32.5 -12t12.5 -33v-65q0 -20 -12 -32.5t-33 -12.5h-510q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#xb3;" horiz-adv-x="712" d="M49 885v4q0 14 12.5 24.5t22.5 10.5h68q29 0 61 -17q10 -6 24.5 -19.5t38 -21.5t64.5 -8q53 0 87 21.5t34 56.5q0 41 -29 57.5t-84 16.5h-90q-18 0 -30.5 12t-12.5 33v45q0 23 23 45l141 129h-250q-20 0 -32.5 12t-12.5 33v70q0 20 12.5 32.5t32.5 12.5h440 q20 0 32.5 -12.5t12.5 -32.5v-62q0 -23 -6 -36t-24 -29l-138 -115q98 -12 150.5 -67.5t52.5 -145.5q0 -104 -82 -165.5t-221 -61.5q-143 0 -217 54t-80 124z" />
<glyph unicode="&#xb4;" horiz-adv-x="755" d="M127 1251q0 16 10 27l168 203q18 23 33.5 31t42.5 8h207q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-256 -209q-18 -14 -33.5 -20t-37.5 -6h-129q-33 -1 -33 32z" />
<glyph unicode="&#xb5;" horiz-adv-x="1296" d="M135 -360v1376q0 20 14.5 34.5t36.5 14.5h187q23 0 36 -13.5t13 -35.5v-563q0 -113 68.5 -182.5t160.5 -69.5t161 69.5t69 182.5v563q0 23 14 36t35 13h182q23 0 36 -13.5t13 -35.5v-967q0 -20 -13 -34.5t-36 -14.5h-170q-20 0 -34.5 14.5t-14.5 34.5v84 q-45 -61 -107.5 -96t-152.5 -35q-66 0 -116 20.5t-95 59.5v-442q0 -20 -14.5 -35t-34.5 -15h-187q-20 0 -35.5 14.5t-15.5 35.5z" />
<glyph unicode="&#xb6;" horiz-adv-x="1191" d="M57 1055q0 102 51.5 189t138.5 138.5t191 51.5h557q20 0 34.5 -14.5t14.5 -35.5v-1540q0 -20 -14 -34.5t-35 -14.5h-125q-20 0 -34.5 13.5t-14.5 35.5v1393h-162v-1393q0 -23 -13 -36t-36 -13h-123q-20 0 -34.5 13.5t-14.5 35.5v830q-104 0 -191 51t-138.5 138t-51.5 192 z" />
<glyph unicode="&#xb7;" horiz-adv-x="675" d="M98 657q0 98 71 169t169 71t169 -70.5t71 -169.5q0 -98 -71 -168.5t-169 -70.5t-169 70.5t-71 168.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="622" d="M127 -365q0 8 10 19l33 31q10 10 20 10t41 -17.5t68 -17.5q33 0 54.5 19.5t21.5 50.5q0 29 -21.5 47t-54.5 18q-20 0 -50 -9t-40 -9q-14 0 -27 12l-37 39q-8 10 -8 22q0 16 13 41l71 164h127l-82 -164q23 14 70 15q72 0 117 -48t45 -126t-52.5 -130.5t-142.5 -52.5 q-70 0 -123 29t-53 57z" />
<glyph unicode="&#xb9;" horiz-adv-x="530" d="M27 1225q0 20 18 35l211 159q20 14 47 15h92q20 0 32.5 -12.5t12.5 -32.5v-627q0 -20 -12 -32.5t-33 -12.5h-96q-20 0 -32.5 12t-12.5 33v455l-113 -84q-10 -8 -26 -9q-20 0 -37 19l-41 53q-10 14 -10 29z" />
<glyph unicode="&#xba;" horiz-adv-x="729" d="M92 1161l2 56q6 111 80 169t193 58t191.5 -58.5t78.5 -168.5q2 -10 2 -56q0 -45 -2 -55q-6 -111 -75.5 -169t-194.5 -58q-127 0 -197 57t-76 170zM274 1161l2 -45q2 -51 24 -74.5t67 -23.5q43 0 64.5 23.5t23.5 74.5q4 20 4 45t-4 45q-4 98 -88 99q-86 0 -91 -99z" />
<glyph unicode="&#xbb;" horiz-adv-x="1259" d="M115 197v141q0 29 8 45t29 37l262 254l-262 254q-20 20 -28.5 36.5t-8.5 45.5v141q0 18 12 30.5t31 12.5q18 0 41 -18l409 -398q29 -29 33 -67v-74q-4 -39 -33 -66l-409 -399q-20 -18 -41 -18q-19 0 -31 12t-12 31zM659 197v141q0 29 8.5 45t28.5 37l262 254l-262 254 q-20 20 -28.5 36.5t-8.5 45.5v141q0 18 12.5 30.5t30.5 12.5q20 0 41 -18l410 -398q29 -29 33 -67v-74q-4 -39 -33 -66l-410 -399q-20 -18 -41 -18q-18 0 -30.5 12t-12.5 31z" />
<glyph unicode="&#xbc;" horiz-adv-x="1527" d="M27 1225q0 20 18 35l211 159q20 14 47 15h92q20 0 32.5 -12.5t12.5 -32.5v-627q0 -20 -12 -32.5t-33 -12.5h-96q-20 0 -32.5 12t-12.5 33v455l-113 -84q-10 -8 -26 -9q-20 0 -37 19l-41 53q-10 14 -10 29zM86 41q0 12 8 22l1009 1332q16 20 30.5 29.5t39.5 9.5h84 q20 0 32.5 -11.5t12.5 -29.5q0 -12 -8 -23l-1012 -1331q-16 -20 -30.5 -29.5t-38.5 -9.5h-84q-21 0 -32 11.5t-11 29.5zM793 172v74q0 29 25 61l272 371q27 37 61 37h125q20 0 32.5 -12.5t12.5 -32.5v-381h72q20 0 32.5 -12.5t12.5 -32.5v-72q0 -20 -12 -32.5t-33 -12.5h-72 v-84q0 -20 -12 -32.5t-33 -12.5h-90q-20 0 -32.5 12t-12.5 33v84h-303q-20 0 -32.5 12t-12.5 33zM988 285h157v223z" />
<glyph unicode="&#xbd;" horiz-adv-x="1538" d="M27 1225q0 20 18 35l211 159q20 14 47 15h92q20 0 32.5 -12.5t12.5 -32.5v-627q0 -20 -12 -32.5t-33 -12.5h-96q-20 0 -32.5 12t-12.5 33v455l-113 -84q-10 -8 -26 -9q-20 0 -37 19l-41 53q-10 14 -10 29zM86 41q0 12 8 22l1009 1332q16 20 30.5 29.5t39.5 9.5h84 q20 0 32.5 -11.5t12.5 -29.5q0 -12 -8 -23l-1012 -1331q-16 -20 -30.5 -29.5t-38.5 -9.5h-84q-21 0 -32 11.5t-11 29.5zM839 45v57q0 16 10.5 31.5t38.5 38.5l103 74q143 104 190 149t47 98q0 37 -25.5 60.5t-68.5 23.5q-49 0 -67.5 -18t-34.5 -49q-12 -23 -22.5 -30 t-26.5 -7h-90q-37 0 -37 35q0 59 38 109t102.5 80t137.5 30q125 0 200 -64.5t75 -162.5q0 -96 -62.5 -166t-222.5 -179h270q20 0 32.5 -12t12.5 -33v-65q0 -20 -12 -32.5t-33 -12.5h-510q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#xbe;" horiz-adv-x="1677" d="M49 885v4q0 14 12.5 24.5t22.5 10.5h68q29 0 61 -17q10 -6 24.5 -19.5t38 -21.5t64.5 -8q53 0 87 21.5t34 56.5q0 41 -29 57.5t-84 16.5h-90q-18 0 -30.5 12t-12.5 33v45q0 23 23 45l141 129h-250q-20 0 -32.5 12t-12.5 33v70q0 20 12.5 32.5t32.5 12.5h440 q20 0 32.5 -12.5t12.5 -32.5v-62q0 -23 -6 -36t-24 -29l-138 -115q98 -12 150.5 -67.5t52.5 -145.5q0 -104 -82 -165.5t-221 -61.5q-143 0 -217 54t-80 124zM269 41q0 12 8 22l1009 1332q16 20 30.5 29.5t39.5 9.5h84q20 0 32.5 -11.5t12.5 -29.5q0 -12 -8 -23l-1012 -1331 q-16 -20 -30.5 -29.5t-38.5 -9.5h-84q-21 0 -32 11.5t-11 29.5zM981 170v74q0 29 25 61l272 371q27 37 61 37h125q20 0 32.5 -12.5t12.5 -32.5v-381h72q20 0 32.5 -12.5t12.5 -32.5v-72q0 -20 -12 -32.5t-33 -12.5h-72v-84q0 -20 -12 -32.5t-33 -12.5h-90q-20 0 -32.5 12 t-12.5 33v84h-303q-20 0 -32.5 12t-12.5 33zM1176 283h157v223z" />
<glyph unicode="&#xbf;" horiz-adv-x="1146" d="M63 -25q0 102 46.5 178t136.5 175q66 70 101.5 120t47.5 107q8 51 11 70q10 27 22 39t35 12h203q18 0 30.5 -12.5t12.5 -28.5q0 -43 -2 -66q-10 -96 -61.5 -169.5t-141.5 -163.5q-70 -70 -103.5 -115t-38.5 -94q-8 -84 54.5 -133.5t150.5 -49.5q199 0 240 199 q14 47 59 47h178q20 0 34 -14.5t14 -36.5q-4 -113 -69 -211t-185.5 -158.5t-284.5 -60.5q-170 0 -279.5 54t-160 138t-50.5 174zM418 831v183q0 20 14 34.5t37 14.5h203q20 0 34.5 -14.5t14.5 -34.5v-183q0 -20 -14.5 -34.5t-34.5 -14.5h-203q-20 0 -35.5 14.5t-15.5 34.5z " />
<glyph unicode="&#xc0;" horiz-adv-x="1421" d="M29 43l4 18l491 1321q20 51 74 52h225q53 0 74 -52l492 -1321l4 -18q0 -18 -13.5 -30.5t-29.5 -12.5h-183q-43 0 -59 39l-94 248h-606l-95 -248q-16 -39 -59 -39h-182q-19 0 -31 12.5t-12 30.5zM332 1796q0 18 10.5 29.5t28.5 11.5h223q27 0 41.5 -8t34.5 -31l166 -203 q10 -10 10 -26q0 -33 -33 -33h-143q-22 0 -37.5 6t-34.5 21l-256 209q-10 10 -10 24zM479 524h463l-231 631z" />
<glyph unicode="&#xc1;" horiz-adv-x="1421" d="M29 43l4 18l491 1321q20 51 74 52h225q53 0 74 -52l492 -1321l4 -18q0 -18 -13.5 -30.5t-29.5 -12.5h-183q-43 0 -59 39l-94 248h-606l-95 -248q-16 -39 -59 -39h-182q-19 0 -31 12.5t-12 30.5zM479 524h463l-231 631zM584 1569q0 16 10 26l168 203q18 23 33.5 31t42.5 8 h221q18 0 28.5 -11t10.5 -30q0 -14 -10 -24l-256 -209q-18 -14 -34 -20.5t-38 -6.5h-143q-33 0 -33 33z" />
<glyph unicode="&#xc2;" horiz-adv-x="1421" d="M29 43l4 18l491 1321q20 51 74 52h225q53 0 74 -52l492 -1321l4 -18q0 -18 -13.5 -30.5t-29.5 -12.5h-183q-43 0 -59 39l-94 248h-606l-95 -248q-16 -39 -59 -39h-182q-19 0 -31 12.5t-12 30.5zM361 1563q0 16 18 34l201 207q18 20 33.5 26.5t35.5 6.5h123q20 0 34.5 -6 t35.5 -27l198 -207q18 -18 19 -34q0 -27 -29 -27h-82q-37 0 -63 14l-174 109l-174 -109q-27 -14 -64 -14h-82q-30 0 -30 27zM479 524h463l-231 631z" />
<glyph unicode="&#xc3;" horiz-adv-x="1421" d="M29 43l4 18l491 1321q20 51 74 52h225q53 0 74 -52l492 -1321l4 -18q0 -18 -13.5 -30.5t-29.5 -12.5h-183q-43 0 -59 39l-94 248h-606l-95 -248q-16 -39 -59 -39h-182q-19 0 -31 12.5t-12 30.5zM388 1571q0 47 22.5 100t68.5 90t113 37q39 0 67 -10t66 -31 q29 -16 46.5 -23.5t37.5 -7.5q18 0 28.5 8.5t23.5 24.5q10 16 18 22.5t23 6.5h94q16 0 26.5 -10.5t10.5 -24.5q0 -47 -23.5 -100.5t-71 -90t-112.5 -36.5q-39 0 -66.5 10t-66.5 31q-29 16 -46.5 23t-37.5 7q-18 0 -28.5 -7t-22.5 -25q-16 -29 -39 -29h-97q-14 0 -24 10 t-10 25zM479 524h463l-231 631z" />
<glyph unicode="&#xc4;" horiz-adv-x="1421" d="M29 43l4 18l491 1321q20 51 74 52h225q53 0 74 -52l492 -1321l4 -18q0 -18 -13.5 -30.5t-29.5 -12.5h-183q-43 0 -59 39l-94 248h-606l-95 -248q-16 -39 -59 -39h-182q-19 0 -31 12.5t-12 30.5zM365 1581v152q0 20 12 33.5t33 13.5h151q20 0 34 -13.5t14 -33.5v-152 q0 -20 -13.5 -32.5t-34.5 -12.5h-151q-21 0 -33 12.5t-12 32.5zM479 524h463l-231 631zM813 1581v152q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13.5t13.5 -33.5v-152q0 -20 -13.5 -32.5t-33.5 -12.5h-152q-20 0 -32.5 12.5t-12.5 32.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1421" d="M29 43l4 18l491 1321q20 51 74 52h225q53 0 74 -52l492 -1321l4 -18q0 -18 -13.5 -30.5t-29.5 -12.5h-183q-43 0 -59 39l-94 248h-606l-95 -248q-16 -39 -59 -39h-182q-19 0 -31 12.5t-12 30.5zM479 524h463l-231 631zM531 1686q0 74 52 123t128 49t128 -49.5t52 -122.5 q0 -74 -52 -122t-128 -48t-128 48t-52 122zM649 1686q0 -27 17.5 -44.5t44.5 -17.5q26 0 43.5 17.5t17.5 44.5t-17.5 44t-43.5 17q-27 0 -44.5 -17.5t-17.5 -43.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1955" d="M10 43q0 6 4 23q61 176 180 433t449 883q20 51 70 52h1079q23 0 37 -14.5t14 -37.5v-137q0 -23 -14 -36t-37 -13h-647v-358h602q23 0 37 -14.5t14 -37.5v-129q0 -23 -14.5 -36t-36.5 -13h-602v-370h663q23 0 37.5 -13.5t14.5 -36.5v-139q0 -23 -14.5 -36t-37.5 -13h-895 q-20 0 -34.5 14.5t-14.5 34.5v238h-438q-117 -211 -129 -248q-18 -39 -59 -39h-187q-16 0 -28.5 12.5t-12.5 30.5zM510 524h354v664h-26z" />
<glyph unicode="&#xc7;" horiz-adv-x="1402" d="M109 715q0 127 2 184q10 262 165.5 408.5t436.5 146.5q182 0 317 -62.5t210 -171t79 -243.5v-4q0 -16 -13.5 -27.5t-29.5 -11.5h-199q-25 0 -37 11t-20 40q-31 127 -106.5 179.5t-200.5 52.5q-293 0 -305 -328q-2 -55 -2 -170t2 -174q12 -328 305 -328q123 0 199.5 52.5 t107.5 179.5q8 29 20.5 40t36.5 11h199q18 0 31.5 -12.5t11.5 -30.5q-4 -135 -79 -244t-210 -171t-317 -62l-43 -89q23 14 71 15q72 0 116 -48t44 -126t-51 -130.5t-141 -52.5q-70 0 -123.5 29t-53.5 57q0 10 9 19l34 31q10 10 21 10q8 0 39 -17.5t70 -17.5q33 0 54 19.5 t21 50.5q0 29 -21.5 47t-53.5 18q-20 0 -50 -9t-41 -9q-16 0 -28 12l-37 39q-8 10 -8 22q0 16 12 41l41 97q-227 29 -350 170t-133 377q-2 55 -2 180z" />
<glyph unicode="&#xc8;" horiz-adv-x="1275" d="M160 49v1333q0 23 13 37.5t36 14.5h903q23 0 37 -14.5t14 -37.5v-137q0 -23 -14 -36t-37 -13h-672v-358h627q23 0 37 -14.5t14 -37.5v-129q0 -23 -14 -36t-37 -13h-627v-370h688q23 0 37.5 -13.5t14.5 -36.5v-139q0 -23 -14.5 -36t-37.5 -13h-919q-21 0 -35 14.5 t-14 34.5zM260 1796q0 18 10.5 29.5t28.5 11.5h223q27 0 41.5 -8t34.5 -31l166 -203q10 -10 10 -26q0 -33 -33 -33h-143q-22 0 -37.5 6t-34.5 21l-256 209q-10 10 -10 24z" />
<glyph unicode="&#xc9;" horiz-adv-x="1275" d="M160 49v1333q0 23 13 37.5t36 14.5h903q23 0 37 -14.5t14 -37.5v-137q0 -23 -14 -36t-37 -13h-672v-358h627q23 0 37 -14.5t14 -37.5v-129q0 -23 -14 -36t-37 -13h-627v-370h688q23 0 37.5 -13.5t14.5 -36.5v-139q0 -23 -14.5 -36t-37.5 -13h-919q-21 0 -35 14.5 t-14 34.5zM512 1569q0 16 10 26l168 203q18 23 33.5 31t42.5 8h221q18 0 28.5 -11t10.5 -30q0 -14 -10 -24l-256 -209q-18 -14 -34 -20.5t-38 -6.5h-143q-33 0 -33 33z" />
<glyph unicode="&#xca;" horiz-adv-x="1275" d="M160 49v1333q0 23 13 37.5t36 14.5h903q23 0 37 -14.5t14 -37.5v-137q0 -23 -14 -36t-37 -13h-672v-358h627q23 0 37 -14.5t14 -37.5v-129q0 -23 -14 -36t-37 -13h-627v-370h688q23 0 37.5 -13.5t14.5 -36.5v-139q0 -23 -14.5 -36t-37.5 -13h-919q-21 0 -35 14.5 t-14 34.5zM289 1563q0 16 18 34l201 207q18 20 33.5 26.5t35.5 6.5h123q20 0 34.5 -6t35.5 -27l198 -207q18 -18 19 -34q0 -27 -29 -27h-82q-37 0 -63 14l-174 109l-174 -109q-27 -14 -64 -14h-82q-30 0 -30 27z" />
<glyph unicode="&#xcb;" horiz-adv-x="1275" d="M160 49v1333q0 23 13 37.5t36 14.5h903q23 0 37 -14.5t14 -37.5v-137q0 -23 -14 -36t-37 -13h-672v-358h627q23 0 37 -14.5t14 -37.5v-129q0 -23 -14 -36t-37 -13h-627v-370h688q23 0 37.5 -13.5t14.5 -36.5v-139q0 -23 -14.5 -36t-37.5 -13h-919q-21 0 -35 14.5 t-14 34.5zM293 1581v152q0 20 12 33.5t33 13.5h151q20 0 34 -13.5t14 -33.5v-152q0 -20 -13.5 -32.5t-34.5 -12.5h-151q-21 0 -33 12.5t-12 32.5zM741 1581v152q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13.5t13.5 -33.5v-152q0 -20 -13.5 -32.5t-33.5 -12.5h-152 q-20 0 -32.5 12.5t-12.5 32.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="614" d="M-72 1796q0 18 10.5 29.5t28.5 11.5h223q27 0 41.5 -8t34.5 -31l166 -203q10 -10 10 -26q0 -33 -33 -33h-143q-22 0 -37.5 6t-34.5 21l-256 209q-10 10 -10 24zM160 49v1335q0 23 14 36.5t35 13.5h197q23 0 36 -13.5t13 -36.5v-1335q0 -20 -13.5 -34.5t-35.5 -14.5h-197 q-21 0 -35 14.5t-14 34.5z" />
<glyph unicode="&#xcd;" horiz-adv-x="614" d="M160 49v1335q0 23 14 36.5t35 13.5h197q23 0 36 -13.5t13 -36.5v-1335q0 -20 -13.5 -34.5t-35.5 -14.5h-197q-21 0 -35 14.5t-14 34.5zM180 1569q0 16 10 26l168 203q18 23 33.5 31t42.5 8h221q18 0 28.5 -11t10.5 -30q0 -14 -10 -24l-256 -209q-18 -14 -34 -20.5 t-38 -6.5h-143q-33 0 -33 33z" />
<glyph unicode="&#xce;" horiz-adv-x="614" d="M-43 1563q0 16 18 34l201 207q18 20 33.5 26.5t35.5 6.5h123q20 0 34.5 -6t35.5 -27l198 -207q18 -18 19 -34q0 -27 -29 -27h-82q-37 0 -63 14l-174 109l-174 -109q-27 -14 -64 -14h-82q-30 0 -30 27zM160 49v1335q0 23 14 36.5t35 13.5h197q23 0 36 -13.5t13 -36.5 v-1335q0 -20 -13.5 -34.5t-35.5 -14.5h-197q-21 0 -35 14.5t-14 34.5z" />
<glyph unicode="&#xcf;" horiz-adv-x="614" d="M-39 1581v152q0 20 12 33.5t33 13.5h151q20 0 34 -13.5t14 -33.5v-152q0 -20 -13.5 -32.5t-34.5 -12.5h-151q-21 0 -33 12.5t-12 32.5zM160 49v1335q0 23 14 36.5t35 13.5h197q23 0 36 -13.5t13 -36.5v-1335q0 -20 -13.5 -34.5t-35.5 -14.5h-197q-21 0 -35 14.5t-14 34.5 zM409 1581v152q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13.5t13.5 -33.5v-152q0 -20 -13.5 -32.5t-33.5 -12.5h-152q-20 0 -32.5 12.5t-12.5 32.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1470" d="M31 668v106q0 20 14 34.5t35 14.5h106v559q0 23 14.5 37.5t35.5 14.5h501q305 0 460 -142.5t157 -414.5q2 -57 2 -158q0 -102 -2 -160q-2 -285 -152.5 -422t-458.5 -137h-507q-21 0 -35.5 14.5t-14.5 34.5v569h-106q-20 0 -34.5 14.5t-14.5 35.5zM475 238h262 q170 0 244 75.5t76 251.5q2 57 2 154q0 94 -2 151q-2 170 -79 248t-247 78h-256v-373h227q20 0 35 -14t15 -35v-106q0 -20 -13.5 -35t-36.5 -15h-227v-380z" />
<glyph unicode="&#xd1;" horiz-adv-x="1447" d="M160 49v1333q0 23 13 37.5t36 14.5h160q39 0 59 -35l586 -910v893q0 23 13 37.5t36 14.5h176q23 0 37 -14.5t14 -37.5v-1331q0 -23 -14 -37t-35 -14h-162q-41 0 -61 35l-584 889v-875q0 -20 -13 -34.5t-36 -14.5h-176q-21 0 -35 14.5t-14 34.5zM402 1571q0 47 22.5 100 t68.5 90t113 37q39 0 67 -10t66 -31q29 -16 46.5 -23.5t37.5 -7.5q18 0 28.5 8.5t23.5 24.5q10 16 18 22.5t23 6.5h94q16 0 26.5 -10.5t10.5 -24.5q0 -47 -23.5 -100.5t-71 -90t-112.5 -36.5q-39 0 -66.5 10t-66.5 31q-29 16 -46.5 23t-37.5 7q-18 0 -28.5 -7t-22.5 -25 q-16 -29 -39 -29h-97q-14 0 -24 10t-10 25z" />
<glyph unicode="&#xd2;" horiz-adv-x="1423" d="M111 715q0 117 2 176q8 272 167.5 417.5t432.5 145.5q270 0 431 -145.5t169 -417.5q4 -119 4 -176q0 -55 -4 -174q-8 -279 -165 -420t-435 -141q-281 0 -436.5 141t-163.5 420q-2 59 -2 174zM334 1796q0 18 10.5 29.5t28.5 11.5h223q27 0 41.5 -8t34.5 -31l166 -203 q10 -10 10 -26q0 -33 -33 -33h-143q-22 0 -37.5 6t-34.5 21l-256 209q-10 10 -10 24zM408 717q0 -104 2 -166q4 -168 84.5 -251t218.5 -83q137 0 217 82t86 252q4 123 4 166q0 47 -4 166q-6 168 -88 251t-215 83q-135 0 -217 -83t-86 -251q-2 -59 -2 -166z" />
<glyph unicode="&#xd3;" horiz-adv-x="1423" d="M111 715q0 117 2 176q8 272 167.5 417.5t432.5 145.5q270 0 431 -145.5t169 -417.5q4 -119 4 -176q0 -55 -4 -174q-8 -279 -165 -420t-435 -141q-281 0 -436.5 141t-163.5 420q-2 59 -2 174zM408 717q0 -104 2 -166q4 -168 84.5 -251t218.5 -83q137 0 217 82t86 252 q4 123 4 166q0 47 -4 166q-6 168 -88 251t-215 83q-135 0 -217 -83t-86 -251q-2 -59 -2 -166zM586 1569q0 16 10 26l168 203q18 23 33.5 31t42.5 8h221q18 0 28.5 -11t10.5 -30q0 -14 -10 -24l-256 -209q-18 -14 -34 -20.5t-38 -6.5h-143q-33 0 -33 33z" />
<glyph unicode="&#xd4;" horiz-adv-x="1423" d="M111 715q0 117 2 176q8 272 167.5 417.5t432.5 145.5q270 0 431 -145.5t169 -417.5q4 -119 4 -176q0 -55 -4 -174q-8 -279 -165 -420t-435 -141q-281 0 -436.5 141t-163.5 420q-2 59 -2 174zM363 1563q0 16 18 34l201 207q18 20 33.5 26.5t35.5 6.5h123q20 0 34.5 -6 t35.5 -27l198 -207q18 -18 19 -34q0 -27 -29 -27h-82q-37 0 -63 14l-174 109l-174 -109q-27 -14 -64 -14h-82q-30 0 -30 27zM408 717q0 -104 2 -166q4 -168 84.5 -251t218.5 -83q137 0 217 82t86 252q4 123 4 166q0 47 -4 166q-6 168 -88 251t-215 83q-135 0 -217 -83 t-86 -251q-2 -59 -2 -166z" />
<glyph unicode="&#xd5;" horiz-adv-x="1423" d="M111 715q0 117 2 176q8 272 167.5 417.5t432.5 145.5q270 0 431 -145.5t169 -417.5q4 -119 4 -176q0 -55 -4 -174q-8 -279 -165 -420t-435 -141q-281 0 -436.5 141t-163.5 420q-2 59 -2 174zM390 1571q0 47 22.5 100t68.5 90t113 37q39 0 67 -10t66 -31 q29 -16 46.5 -23.5t37.5 -7.5q18 0 28.5 8.5t23.5 24.5q10 16 18 22.5t23 6.5h94q16 0 26.5 -10.5t10.5 -24.5q0 -47 -23.5 -100.5t-71 -90t-112.5 -36.5q-39 0 -66.5 10t-66.5 31q-29 16 -46.5 23t-37.5 7q-18 0 -28.5 -7t-22.5 -25q-16 -29 -39 -29h-97q-14 0 -24 10 t-10 25zM408 717q0 -104 2 -166q4 -168 84.5 -251t218.5 -83q137 0 217 82t86 252q4 123 4 166q0 47 -4 166q-6 168 -88 251t-215 83q-135 0 -217 -83t-86 -251q-2 -59 -2 -166z" />
<glyph unicode="&#xd6;" horiz-adv-x="1423" d="M111 715q0 117 2 176q8 272 167.5 417.5t432.5 145.5q270 0 431 -145.5t169 -417.5q4 -119 4 -176q0 -55 -4 -174q-8 -279 -165 -420t-435 -141q-281 0 -436.5 141t-163.5 420q-2 59 -2 174zM367 1581v152q0 20 12 33.5t33 13.5h151q20 0 34 -13.5t14 -33.5v-152 q0 -20 -13.5 -32.5t-34.5 -12.5h-151q-21 0 -33 12.5t-12 32.5zM408 717q0 -104 2 -166q4 -168 84.5 -251t218.5 -83q137 0 217 82t86 252q4 123 4 166q0 47 -4 166q-6 168 -88 251t-215 83q-135 0 -217 -83t-86 -251q-2 -59 -2 -166zM815 1581v152q0 20 12.5 33.5 t32.5 13.5h152q20 0 33.5 -13.5t13.5 -33.5v-152q0 -20 -13.5 -32.5t-33.5 -12.5h-152q-20 0 -32.5 12.5t-12.5 32.5z" />
<glyph unicode="&#xd7;" horiz-adv-x="1191" d="M109 360q0 16 14 35l287 285l-287 289q-14 18 -14 35q0 16 14 34l117 117q14 14 34 14t35 -14l287 -289l287 287q16 16 35 16q18 0 34 -16l117 -117q14 -12 14 -35q0 -20 -14 -34l-287 -287l287 -285q14 -14 14 -35q0 -20 -14 -34l-117 -117q-18 -14 -34 -14t-35 14 l-287 287l-287 -287q-14 -14 -35 -14q-20 0 -34 14l-117 117q-14 18 -14 34z" />
<glyph unicode="&#xd8;" horiz-adv-x="1480" d="M23 401v115q0 25 10 37t35 22l73 27l-2 119q0 51 4 170q8 272 169 417.5t431 145.5q221 0 370 -100.5t200 -292.5l80 31q16 6 24 6q18 0 29.5 -10.5t11.5 -28.5v-117q0 -25 -10 -36t-35 -21l-67 -25l2 -135q0 -61 -5 -184q-8 -279 -163.5 -420t-436.5 -141 q-239 0 -386.5 103t-190.5 314l-78 -28q-16 -6 -25 -7q-18 0 -29 10.5t-11 28.5zM438 721l600 231q-18 133 -96 199t-199 66q-135 0 -216 -83t-87 -251q-4 -115 -2 -162zM442 506q16 -147 95 -218t206 -71q138 0 218.5 83t85.5 251q4 135 2 192z" />
<glyph unicode="&#xd9;" horiz-adv-x="1462" d="M147 549v833q0 23 13.5 37.5t36.5 14.5h188q23 0 37 -14.5t14 -37.5v-833q0 -162 77 -244t218 -82t218 82t77 244v833q0 23 14.5 37.5t36.5 14.5h189q23 0 36 -14.5t13 -37.5v-833q0 -287 -152.5 -428t-429.5 -141q-278 0 -432 141t-154 428zM352 1796q0 18 10.5 29.5 t28.5 11.5h223q27 0 41.5 -8t34.5 -31l166 -203q10 -10 10 -26q0 -33 -33 -33h-143q-22 0 -37.5 6t-34.5 21l-256 209q-10 10 -10 24z" />
<glyph unicode="&#xda;" horiz-adv-x="1462" d="M147 549v833q0 23 13.5 37.5t36.5 14.5h188q23 0 37 -14.5t14 -37.5v-833q0 -162 77 -244t218 -82t218 82t77 244v833q0 23 14.5 37.5t36.5 14.5h189q23 0 36 -14.5t13 -37.5v-833q0 -287 -152.5 -428t-429.5 -141q-278 0 -432 141t-154 428zM604 1569q0 16 10 26 l168 203q18 23 33.5 31t42.5 8h221q18 0 28.5 -11t10.5 -30q0 -14 -10 -24l-256 -209q-18 -14 -34 -20.5t-38 -6.5h-143q-33 0 -33 33z" />
<glyph unicode="&#xdb;" horiz-adv-x="1462" d="M147 549v833q0 23 13.5 37.5t36.5 14.5h188q23 0 37 -14.5t14 -37.5v-833q0 -162 77 -244t218 -82t218 82t77 244v833q0 23 14.5 37.5t36.5 14.5h189q23 0 36 -14.5t13 -37.5v-833q0 -287 -152.5 -428t-429.5 -141q-278 0 -432 141t-154 428zM381 1563q0 16 18 34 l201 207q18 20 33.5 26.5t35.5 6.5h123q20 0 34.5 -6t35.5 -27l198 -207q18 -18 19 -34q0 -27 -29 -27h-82q-37 0 -63 14l-174 109l-174 -109q-27 -14 -64 -14h-82q-30 0 -30 27z" />
<glyph unicode="&#xdc;" horiz-adv-x="1462" d="M147 549v833q0 23 13.5 37.5t36.5 14.5h188q23 0 37 -14.5t14 -37.5v-833q0 -162 77 -244t218 -82t218 82t77 244v833q0 23 14.5 37.5t36.5 14.5h189q23 0 36 -14.5t13 -37.5v-833q0 -287 -152.5 -428t-429.5 -141q-278 0 -432 141t-154 428zM385 1581v152q0 20 12 33.5 t33 13.5h151q20 0 34 -13.5t14 -33.5v-152q0 -20 -13.5 -32.5t-34.5 -12.5h-151q-21 0 -33 12.5t-12 32.5zM833 1581v152q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13.5t13.5 -33.5v-152q0 -20 -13.5 -32.5t-33.5 -12.5h-152q-20 0 -32.5 12.5t-12.5 32.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1361" d="M45 1391q0 18 13.5 30.5t29.5 12.5h184q39 0 64 -39l344 -594l346 594q8 14 23.5 26.5t37.5 12.5h185q18 0 30.5 -12.5t12.5 -30.5q0 -12 -6 -23l-484 -858v-461q0 -23 -14 -36t-37 -13h-188q-21 0 -35 14.5t-14 34.5v461l-486 858q-6 18 -6 23zM553 1569q0 16 10 26 l168 203q18 23 33.5 31t42.5 8h221q18 0 28.5 -11t10.5 -30q0 -14 -10 -24l-256 -209q-18 -14 -34 -20.5t-38 -6.5h-143q-33 0 -33 33z" />
<glyph unicode="&#xde;" horiz-adv-x="1357" d="M160 49v1335q0 23 14 36.5t35 13.5h199q23 0 36 -13.5t13 -36.5v-206h297q250 0 392 -116t142 -339t-141 -337t-393 -114h-297v-223q0 -23 -14.5 -36t-36.5 -13h-197q-21 0 -35 14.5t-14 34.5zM453 502h295q117 0 180 56t63 165q0 106 -60.5 165.5t-182.5 59.5h-295v-446 z" />
<glyph unicode="&#xdf;" horiz-adv-x="1261" d="M137 49v936q0 207 122 338t370 131q166 0 275.5 -56.5t160.5 -146.5t51 -192q0 -104 -44 -176t-116 -113q219 -82 220 -344q0 -104 -53.5 -201.5t-166 -161t-278.5 -63.5h-119q-25 0 -40 13.5t-15 35.5v115q0 23 15.5 36t39.5 13h80q119 0 186.5 62.5t67.5 158.5 q0 98 -67.5 161.5t-190.5 63.5h-80q-25 0 -38 13.5t-13 36.5v100q0 49 51 49h70q111 2 169 51t58 140q0 84 -61.5 138t-161.5 54q-102 0 -161.5 -64.5t-59.5 -191.5v-936q0 -20 -13.5 -34.5t-36.5 -14.5h-172q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#xe0;" d="M66 293q0 139 112.5 225t308.5 117l281 41v43q0 88 -45 133t-143 45q-68 0 -109 -24.5t-66.5 -50t-33.5 -34.5q-8 -25 -31 -24h-160q-18 0 -31.5 12t-13.5 33q2 51 50 115.5t148.5 112.5t248.5 48q242 0 352 -108.5t110 -284.5v-643q0 -20 -13 -34.5t-36 -14.5h-170 q-20 0 -34.5 14.5t-14.5 34.5v80q-45 -66 -127 -107.5t-205 -41.5q-104 0 -191 41.5t-137 113.5t-50 158zM207 1479q0 18 10.5 29.5t28.5 11.5h207q27 0 42 -8.5t34 -30.5l167 -203q10 -10 11 -27q0 -33 -33 -32h-129q-23 0 -38 6t-34 20l-256 209q-10 10 -10 25zM330 313 q0 -66 54 -101.5t130 -35.5q113 0 184.5 74t71.5 213v41l-205 -33q-236 -37 -235 -158z" />
<glyph unicode="&#xe1;" d="M66 293q0 139 112.5 225t308.5 117l281 41v43q0 88 -45 133t-143 45q-68 0 -109 -24.5t-66.5 -50t-33.5 -34.5q-8 -25 -31 -24h-160q-18 0 -31.5 12t-13.5 33q2 51 50 115.5t148.5 112.5t248.5 48q242 0 352 -108.5t110 -284.5v-643q0 -20 -13 -34.5t-36 -14.5h-170 q-20 0 -34.5 14.5t-14.5 34.5v80q-45 -66 -127 -107.5t-205 -41.5q-104 0 -191 41.5t-137 113.5t-50 158zM330 313q0 -66 54 -101.5t130 -35.5q113 0 184.5 74t71.5 213v41l-205 -33q-236 -37 -235 -158zM438 1251q0 16 10 27l168 203q18 23 33.5 31t42.5 8h207 q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-256 -209q-18 -14 -33.5 -20t-37.5 -6h-129q-33 -1 -33 32z" />
<glyph unicode="&#xe2;" d="M66 293q0 139 112.5 225t308.5 117l281 41v43q0 88 -45 133t-143 45q-68 0 -109 -24.5t-66.5 -50t-33.5 -34.5q-8 -25 -31 -24h-160q-18 0 -31.5 12t-13.5 33q2 51 50 115.5t148.5 112.5t248.5 48q242 0 352 -108.5t110 -284.5v-643q0 -20 -13 -34.5t-36 -14.5h-170 q-20 0 -34.5 14.5t-14.5 34.5v80q-45 -66 -127 -107.5t-205 -41.5q-104 0 -191 41.5t-137 113.5t-50 158zM229 1245q0 16 18 35l199 207q20 20 34.5 26.5t35.5 6.5h114q20 0 36 -6.5t34 -26.5l201 -207q18 -18 18 -35q0 -27 -31 -26h-77q-37 0 -64 14l-174 108l-174 -108 q-27 -14 -64 -14h-75q-31 -1 -31 26zM330 313q0 -66 54 -101.5t130 -35.5q113 0 184.5 74t71.5 213v41l-205 -33q-236 -37 -235 -158z" />
<glyph unicode="&#xe3;" d="M66 293q0 139 112.5 225t308.5 117l281 41v43q0 88 -45 133t-143 45q-68 0 -109 -24.5t-66.5 -50t-33.5 -34.5q-8 -25 -31 -24h-160q-18 0 -31.5 12t-13.5 33q2 51 50 115.5t148.5 112.5t248.5 48q242 0 352 -108.5t110 -284.5v-643q0 -20 -13 -34.5t-36 -14.5h-170 q-20 0 -34.5 14.5t-14.5 34.5v80q-45 -66 -127 -107.5t-205 -41.5q-104 0 -191 41.5t-137 113.5t-50 158zM260 1253q0 47 21.5 98.5t66.5 87.5t113 36q39 0 65.5 -10.5t63.5 -30.5q27 -16 43 -23.5t37 -7.5q18 0 29.5 8t21.5 25q10 14 18 21t23 7h94q16 0 26.5 -10t10.5 -24 q0 -47 -22.5 -98.5t-68.5 -87.5t-112 -36q-39 0 -65.5 10.5t-63.5 30.5q-27 16 -43 23.5t-37 7.5q-18 0 -28.5 -8t-22.5 -25q-16 -29 -39 -28h-96q-14 0 -24.5 10t-10.5 24zM330 313q0 -66 54 -101.5t130 -35.5q113 0 184.5 74t71.5 213v41l-205 -33q-236 -37 -235 -158z " />
<glyph unicode="&#xe4;" d="M66 293q0 139 112.5 225t308.5 117l281 41v43q0 88 -45 133t-143 45q-68 0 -109 -24.5t-66.5 -50t-33.5 -34.5q-8 -25 -31 -24h-160q-18 0 -31.5 12t-13.5 33q2 51 50 115.5t148.5 112.5t248.5 48q242 0 352 -108.5t110 -284.5v-643q0 -20 -13 -34.5t-36 -14.5h-170 q-20 0 -34.5 14.5t-14.5 34.5v80q-45 -66 -127 -107.5t-205 -41.5q-104 0 -191 41.5t-137 113.5t-50 158zM266 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM330 313q0 -66 54 -101.5 t130 -35.5q113 0 184.5 74t71.5 213v41l-205 -33q-236 -37 -235 -158zM653 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5z" />
<glyph unicode="&#xe5;" d="M66 293q0 139 112.5 225t308.5 117l281 41v43q0 88 -45 133t-143 45q-68 0 -109 -24.5t-66.5 -50t-33.5 -34.5q-8 -25 -31 -24h-160q-18 0 -31.5 12t-13.5 33q2 51 50 115.5t148.5 112.5t248.5 48q242 0 352 -108.5t110 -284.5v-643q0 -20 -13 -34.5t-36 -14.5h-170 q-20 0 -34.5 14.5t-14.5 34.5v80q-45 -66 -127 -107.5t-205 -41.5q-104 0 -191 41.5t-137 113.5t-50 158zM330 313q0 -66 54 -101.5t130 -35.5q113 0 184.5 74t71.5 213v41l-205 -33q-236 -37 -235 -158zM393 1378q0 74 52 123t128 49t128 -49t52 -123t-52 -122t-128 -48 t-128 48.5t-52 121.5zM512 1378q0 -27 17.5 -44t43.5 -17q27 0 44.5 17.5t17.5 43.5q0 27 -17.5 44.5t-44.5 17.5t-44 -17.5t-17 -44.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1851" d="M66 293q0 139 113.5 227t309.5 119l279 39v41q0 88 -44 133t-144 45q-68 0 -109 -24.5t-66.5 -50t-33.5 -34.5q-8 -25 -31 -24h-160q-18 0 -31.5 12t-13.5 33q0 49 49 114.5t150.5 113.5t247.5 48q252 0 346 -137q131 137 340 137q182 0 293.5 -79.5t158.5 -201.5 t47 -259v-43q0 -20 -14 -34.5t-37 -14.5h-667v-17q4 -133 65.5 -198.5t151.5 -65.5q57 0 106 23.5t84 70.5q16 20 28.5 26.5t35.5 6.5h176q18 0 30.5 -11.5t12.5 -31.5q0 -45 -55.5 -111.5t-163 -115.5t-252.5 -49q-260 0 -394 184q-135 -184 -411 -184q-119 0 -209 40.5 t-139 112.5t-49 160zM332 313q0 -66 53 -101.5t131 -35.5q113 0 184.5 74t71.5 213v41l-205 -33q-236 -37 -235 -158zM1049 627h436v8q0 121 -55.5 191.5t-161.5 70.5q-90 0 -152.5 -67.5t-66.5 -194.5v-8z" />
<glyph unicode="&#xe7;" d="M92 532l2 82q8 221 142.5 346t359.5 125q162 0 273.5 -57t166 -140t58.5 -159q2 -20 -13.5 -34.5t-36.5 -14.5h-182q-23 0 -33 9t-20 34q-31 78 -81 112.5t-128 34.5q-104 0 -162.5 -65.5t-62.5 -200.5l-2 -76l2 -67q12 -266 225 -266q80 0 129 33.5t80 113.5 q8 23 19.5 33t33.5 10h182q20 0 36 -14.5t14 -34.5q-4 -72 -56.5 -154t-163 -142t-272.5 -60l-43 -89q23 14 72 15q72 0 116 -48t44 -126t-51.5 -130.5t-141.5 -52.5q-70 0 -123 29t-53 57q0 10 8 19l35 31q10 10 20 10q8 0 39 -17.5t70 -17.5q33 0 54.5 19.5t21.5 50.5 q0 29 -21.5 47t-54.5 18q-20 0 -50 -9t-40 -9q-14 0 -27 12l-39 39q-8 10 -8 22t14 41l41 99q-178 29 -281.5 148.5t-109.5 312.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="1183" d="M88 535q0 256 134 403t372 147q244 0 376 -147t132 -393v-43q0 -20 -14.5 -34.5t-36.5 -14.5h-680v-17q4 -115 62.5 -189.5t160.5 -74.5q117 0 190 94q18 23 28.5 28t35.5 5h180q18 0 31.5 -10t13.5 -29q0 -49 -58.5 -115.5t-167 -115.5t-251.5 -49q-229 0 -363.5 131 t-142.5 372zM221 1479q0 18 10.5 29.5t28.5 11.5h207q27 0 42 -8.5t34 -30.5l167 -203q10 -10 11 -27q0 -33 -33 -32h-129q-23 0 -38 6t-34 20l-256 209q-10 10 -10 25zM371 627h448v4q0 121 -60.5 193.5t-164.5 72.5t-163.5 -72.5t-59.5 -193.5v-4z" />
<glyph unicode="&#xe9;" horiz-adv-x="1183" d="M88 535q0 256 134 403t372 147q244 0 376 -147t132 -393v-43q0 -20 -14.5 -34.5t-36.5 -14.5h-680v-17q4 -115 62.5 -189.5t160.5 -74.5q117 0 190 94q18 23 28.5 28t35.5 5h180q18 0 31.5 -10t13.5 -29q0 -49 -58.5 -115.5t-167 -115.5t-251.5 -49q-229 0 -363.5 131 t-142.5 372zM371 627h448v4q0 121 -60.5 193.5t-164.5 72.5t-163.5 -72.5t-59.5 -193.5v-4zM475 1251q0 16 10 27l168 203q18 23 33.5 31t42.5 8h207q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-256 -209q-18 -14 -33.5 -20t-37.5 -6h-129q-33 -1 -33 32z" />
<glyph unicode="&#xea;" horiz-adv-x="1183" d="M88 535q0 256 134 403t372 147q244 0 376 -147t132 -393v-43q0 -20 -14.5 -34.5t-36.5 -14.5h-680v-17q4 -115 62.5 -189.5t160.5 -74.5q117 0 190 94q18 23 28.5 28t35.5 5h180q18 0 31.5 -10t13.5 -29q0 -49 -58.5 -115.5t-167 -115.5t-251.5 -49q-229 0 -363.5 131 t-142.5 372zM250 1245q0 16 18 35l199 207q20 20 34.5 26.5t35.5 6.5h114q20 0 36 -6.5t34 -26.5l201 -207q18 -18 18 -35q0 -27 -31 -26h-77q-37 0 -64 14l-174 108l-174 -108q-27 -14 -64 -14h-75q-31 -1 -31 26zM371 627h448v4q0 121 -60.5 193.5t-164.5 72.5 t-163.5 -72.5t-59.5 -193.5v-4z" />
<glyph unicode="&#xeb;" horiz-adv-x="1183" d="M88 535q0 256 134 403t372 147q244 0 376 -147t132 -393v-43q0 -20 -14.5 -34.5t-36.5 -14.5h-680v-17q4 -115 62.5 -189.5t160.5 -74.5q117 0 190 94q18 23 28.5 28t35.5 5h180q18 0 31.5 -10t13.5 -29q0 -49 -58.5 -115.5t-167 -115.5t-251.5 -49q-229 0 -363.5 131 t-142.5 372zM281 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM371 627h448v4q0 121 -60.5 193.5t-164.5 72.5t-163.5 -72.5t-59.5 -193.5v-4zM668 1303v151q0 20 12.5 33.5t32.5 13.5 h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5z" />
<glyph unicode="&#xec;" horiz-adv-x="542" d="M-107 1479q0 18 10.5 29.5t28.5 11.5h207q27 0 42 -8.5t34 -30.5l167 -203q10 -10 11 -27q0 -33 -33 -32h-129q-23 0 -38 6t-34 20l-256 209q-10 10 -10 25zM135 49v967q0 23 14.5 36t34.5 13h174q20 0 35 -14.5t15 -34.5v-967q0 -20 -14.5 -34.5t-35.5 -14.5h-174 q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#xed;" horiz-adv-x="542" d="M135 49v967q0 23 14.5 36t34.5 13h174q20 0 35 -14.5t15 -34.5v-967q0 -20 -14.5 -34.5t-35.5 -14.5h-174q-20 0 -34.5 14.5t-14.5 34.5zM150 1251q0 16 10 27l168 203q18 23 33.5 31t42.5 8h207q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-256 -209q-18 -14 -33.5 -20 t-37.5 -6h-129q-33 -1 -33 32z" />
<glyph unicode="&#xee;" horiz-adv-x="542" d="M-74 1245q0 16 18 35l199 207q20 20 34.5 26.5t35.5 6.5h114q20 0 36 -6.5t34 -26.5l201 -207q18 -18 18 -35q0 -27 -31 -26h-77q-37 0 -64 14l-174 108l-174 -108q-27 -14 -64 -14h-75q-31 -1 -31 26zM135 49v967q0 23 14.5 36t34.5 13h174q20 0 35 -14.5t15 -34.5v-967 q0 -20 -14.5 -34.5t-35.5 -14.5h-174q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#xef;" horiz-adv-x="542" d="M-45 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM135 49v967q0 23 14.5 36t34.5 13h174q20 0 35 -14.5t15 -34.5v-967q0 -20 -14.5 -34.5t-35.5 -14.5h-174q-20 0 -34.5 14.5 t-14.5 34.5zM342 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1202" d="M86 473q0 150 62.5 260.5t178 170t269.5 59.5q100 0 170 -48q-39 111 -104 193l-216 -84q-16 -6 -24 -6q-18 0 -29.5 10t-11.5 29v47q0 25 10 37t35 22l137 54q-74 70 -155 131q-33 25 -33 43t12 30.5t31 12.5h184q55 0 88 -27l35 -31q53 -45 76 -69l188 73q16 6 25 6 q18 0 29.5 -10t11.5 -28v-48q0 -25 -10.5 -36t-34.5 -21l-109 -43q94 -121 145.5 -283.5t59.5 -394.5v-57q-6 -225 -140.5 -355t-369.5 -130q-240 0 -375 132t-135 361zM369 444q4 -117 60 -187.5t167 -70.5t168 72t59 188v50q-2 119 -58 190.5t-169 71.5t-169 -72t-58 -190 v-52z" />
<glyph unicode="&#xf1;" horiz-adv-x="1280" d="M135 49v967q0 23 14.5 36t34.5 13h170q23 0 36 -13.5t13 -35.5v-84q125 154 342 153q188 0 299 -122.5t111 -337.5v-576q0 -20 -13.5 -34.5t-35.5 -14.5h-184q-20 0 -35 14.5t-15 34.5v563q0 119 -58 185.5t-167 66.5q-104 0 -167.5 -67.5t-63.5 -184.5v-563 q0 -20 -13.5 -34.5t-35.5 -14.5h-183q-20 0 -34.5 14.5t-14.5 34.5zM330 1253q0 47 21.5 98.5t66.5 87.5t113 36q39 0 65.5 -10.5t63.5 -30.5q27 -16 43 -23.5t37 -7.5q18 0 29.5 8t21.5 25q10 14 18 21t23 7h94q16 0 26.5 -10t10.5 -24q0 -47 -22.5 -98.5t-68.5 -87.5 t-112 -36q-39 0 -65.5 10.5t-63.5 30.5q-27 16 -43 23.5t-37 7.5q-18 0 -28.5 -8t-22.5 -25q-16 -29 -39 -28h-96q-14 0 -24.5 10t-10.5 24z" />
<glyph unicode="&#xf2;" horiz-adv-x="1214" d="M92 532l2 91q10 215 143.5 338.5t370.5 123.5q236 0 369 -123.5t143 -338.5q2 -25 2 -91t-2 -90q-10 -217 -141 -339.5t-371 -122.5q-241 0 -372.5 122.5t-141.5 339.5zM231 1479q0 18 10.5 29.5t28.5 11.5h207q27 0 42 -8.5t34 -30.5l167 -203q10 -10 11 -27 q0 -33 -33 -32h-129q-23 0 -38 6t-34 20l-256 209q-10 10 -10 25zM375 532l2 -79q4 -131 63.5 -199t167.5 -68q109 0 167.5 68t62.5 199q2 20 2 79t-2 80q-4 131 -63.5 200t-166.5 69q-109 0 -168 -69t-63 -200z" />
<glyph unicode="&#xf3;" horiz-adv-x="1214" d="M92 532l2 91q10 215 143.5 338.5t370.5 123.5q236 0 369 -123.5t143 -338.5q2 -25 2 -91t-2 -90q-10 -217 -141 -339.5t-371 -122.5q-241 0 -372.5 122.5t-141.5 339.5zM375 532l2 -79q4 -131 63.5 -199t167.5 -68q109 0 167.5 68t62.5 199q2 20 2 79t-2 80 q-4 131 -63.5 200t-166.5 69q-109 0 -168 -69t-63 -200zM483 1251q0 16 10 27l168 203q18 23 33.5 31t42.5 8h207q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-256 -209q-18 -14 -33.5 -20t-37.5 -6h-129q-33 -1 -33 32z" />
<glyph unicode="&#xf4;" horiz-adv-x="1214" d="M92 532l2 91q10 215 143.5 338.5t370.5 123.5q236 0 369 -123.5t143 -338.5q2 -25 2 -91t-2 -90q-10 -217 -141 -339.5t-371 -122.5q-241 0 -372.5 122.5t-141.5 339.5zM262 1245q0 16 18 35l199 207q20 20 34.5 26.5t35.5 6.5h114q20 0 36 -6.5t34 -26.5l201 -207 q18 -18 18 -35q0 -27 -31 -26h-77q-37 0 -64 14l-174 108l-174 -108q-27 -14 -64 -14h-75q-31 -1 -31 26zM375 532l2 -79q4 -131 63.5 -199t167.5 -68q109 0 167.5 68t62.5 199q2 20 2 79t-2 80q-4 131 -63.5 200t-166.5 69q-109 0 -168 -69t-63 -200z" />
<glyph unicode="&#xf5;" horiz-adv-x="1214" d="M92 532l2 91q10 215 143.5 338.5t370.5 123.5q236 0 369 -123.5t143 -338.5q2 -25 2 -91t-2 -90q-10 -217 -141 -339.5t-371 -122.5q-241 0 -372.5 122.5t-141.5 339.5zM293 1253q0 47 21.5 98.5t66.5 87.5t113 36q39 0 65.5 -10.5t63.5 -30.5q27 -16 43 -23.5t37 -7.5 q18 0 29.5 8t21.5 25q10 14 18 21t23 7h94q16 0 26.5 -10t10.5 -24q0 -47 -22.5 -98.5t-68.5 -87.5t-112 -36q-39 0 -65.5 10.5t-63.5 30.5q-27 16 -43 23.5t-37 7.5q-18 0 -28.5 -8t-22.5 -25q-16 -29 -39 -28h-96q-14 0 -24.5 10t-10.5 24zM375 532l2 -79 q4 -131 63.5 -199t167.5 -68q109 0 167.5 68t62.5 199q2 20 2 79t-2 80q-4 131 -63.5 200t-166.5 69q-109 0 -168 -69t-63 -200z" />
<glyph unicode="&#xf6;" horiz-adv-x="1214" d="M92 532l2 91q10 215 143.5 338.5t370.5 123.5q236 0 369 -123.5t143 -338.5q2 -25 2 -91t-2 -90q-10 -217 -141 -339.5t-371 -122.5q-241 0 -372.5 122.5t-141.5 339.5zM293 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33 t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM375 532l2 -79q4 -131 63.5 -199t167.5 -68q109 0 167.5 68t62.5 199q2 20 2 79t-2 80q-4 131 -63.5 200t-166.5 69q-109 0 -168 -69t-63 -200zM680 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151 q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5z" />
<glyph unicode="&#xf7;" horiz-adv-x="1116" d="M113 557v117q0 23 14 36t35 13h792q23 0 36.5 -13.5t13.5 -35.5v-117q0 -20 -14.5 -34.5t-35.5 -14.5h-792q-21 0 -35 14.5t-14 34.5zM426 166v153q0 23 14.5 37.5t34.5 14.5h164q23 0 37 -14.5t14 -37.5v-153q0 -20 -14 -34.5t-37 -14.5h-164q-20 0 -34.5 14t-14.5 35z M426 911v154q0 23 14.5 37t34.5 14h164q23 0 37 -14.5t14 -36.5v-154q0 -20 -14 -34.5t-37 -14.5h-164q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="1277" d="M25 264v80q0 25 10 37t35 22l59 23q-4 78 -4 117l2 80q10 205 139 333.5t375 128.5q184 0 300 -74.5t169 -203.5l78 31q16 6 24 6q18 0 29.5 -11.5t11.5 -29.5v-80q0 -25 -10 -36t-35 -21l-57 -23q4 -31 4 -113q0 -63 -2 -88q-10 -207 -136 -334.5t-376 -127.5 q-188 0 -306 74.5t-169 205.5l-76 -29q-16 -6 -24 -6q-18 0 -29.5 10.5t-11.5 28.5zM410 535l450 174q-20 88 -78.5 130t-140.5 42q-102 0 -163.5 -61.5t-67.5 -190.5v-94zM420 358q41 -172 221 -172q219 0 231 260v89z" />
<glyph unicode="&#xf9;" horiz-adv-x="1273" d="M125 440v576q0 23 14.5 36t34.5 13h184q20 0 35 -14.5t15 -34.5v-563q0 -252 219 -252q106 0 168.5 67.5t62.5 184.5v563q0 23 14.5 36t34.5 13h183q23 0 36 -13.5t13 -35.5v-967q0 -20 -13.5 -34.5t-35.5 -14.5h-170q-20 0 -35 14.5t-15 34.5v84q-113 -154 -340 -153 q-188 0 -296.5 122.5t-108.5 337.5zM254 1479q0 18 10.5 29.5t28.5 11.5h207q27 0 42 -8.5t34 -30.5l167 -203q10 -10 11 -27q0 -33 -33 -32h-129q-23 0 -38 6t-34 20l-256 209q-10 10 -10 25z" />
<glyph unicode="&#xfa;" horiz-adv-x="1273" d="M125 440v576q0 23 14.5 36t34.5 13h184q20 0 35 -14.5t15 -34.5v-563q0 -252 219 -252q106 0 168.5 67.5t62.5 184.5v563q0 23 14.5 36t34.5 13h183q23 0 36 -13.5t13 -35.5v-967q0 -20 -13.5 -34.5t-35.5 -14.5h-170q-20 0 -35 14.5t-15 34.5v84q-113 -154 -340 -153 q-188 0 -296.5 122.5t-108.5 337.5zM508 1251q0 16 10 27l168 203q18 23 33.5 31t42.5 8h207q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-256 -209q-18 -14 -33.5 -20t-37.5 -6h-129q-33 -1 -33 32z" />
<glyph unicode="&#xfb;" horiz-adv-x="1273" d="M125 440v576q0 23 14.5 36t34.5 13h184q20 0 35 -14.5t15 -34.5v-563q0 -252 219 -252q106 0 168.5 67.5t62.5 184.5v563q0 23 14.5 36t34.5 13h183q23 0 36 -13.5t13 -35.5v-967q0 -20 -13.5 -34.5t-35.5 -14.5h-170q-20 0 -35 14.5t-15 34.5v84q-113 -154 -340 -153 q-188 0 -296.5 122.5t-108.5 337.5zM287 1245q0 16 18 35l199 207q20 20 34.5 26.5t35.5 6.5h114q20 0 36 -6.5t34 -26.5l201 -207q18 -18 18 -35q0 -27 -31 -26h-77q-37 0 -64 14l-174 108l-174 -108q-27 -14 -64 -14h-75q-31 -1 -31 26z" />
<glyph unicode="&#xfc;" horiz-adv-x="1273" d="M125 440v576q0 23 14.5 36t34.5 13h184q20 0 35 -14.5t15 -34.5v-563q0 -252 219 -252q106 0 168.5 67.5t62.5 184.5v563q0 23 14.5 36t34.5 13h183q23 0 36 -13.5t13 -35.5v-967q0 -20 -13.5 -34.5t-35.5 -14.5h-170q-20 0 -35 14.5t-15 34.5v84q-113 -154 -340 -153 q-188 0 -296.5 122.5t-108.5 337.5zM317 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM704 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33 t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="1177" d="M53 1020q2 18 14.5 31.5t30.5 13.5h170q37 0 54 -39l272 -668l276 668q20 39 58 39h166q18 0 30.5 -12.5t12.5 -28.5q0 -14 -9 -33l-579 -1341q-16 -39 -57 -39h-164q-17 0 -30 12t-13 29q0 14 8 33l162 378l-394 928q-8 18 -8 29zM473 1251q0 16 10 27l168 203 q18 23 33.5 31t42.5 8h207q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-256 -209q-18 -14 -33.5 -20t-37.5 -6h-129q-33 -1 -33 32z" />
<glyph unicode="&#xfe;" horiz-adv-x="1259" d="M137 -340v1745q0 23 14.5 36t34.5 13h179q23 0 36 -13.5t13 -35.5v-463q53 70 126.5 106.5t188.5 36.5q207 0 317.5 -130t118.5 -349q2 -25 2 -74t-2 -73q-8 -215 -119.5 -347t-316.5 -132q-207 0 -315 145v-465q0 -23 -13.5 -36t-35.5 -13h-179q-20 0 -34.5 13.5 t-14.5 35.5zM412 526l2 -80q4 -102 64.5 -173.5t172.5 -71.5q218 0 232 270q2 20 2 61t-2 62q-14 270 -232 270q-117 0 -174 -74.5t-63 -185.5z" />
<glyph unicode="&#xff;" horiz-adv-x="1177" d="M53 1020q2 18 14.5 31.5t30.5 13.5h170q37 0 54 -39l272 -668l276 668q20 39 58 39h166q18 0 30.5 -12.5t12.5 -28.5q0 -14 -9 -33l-579 -1341q-16 -39 -57 -39h-164q-17 0 -30 12t-13 29q0 14 8 33l162 378l-394 928q-8 18 -8 29zM279 1303v151q0 20 12.5 33.5 t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM666 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5z" />
<glyph unicode="&#x100;" horiz-adv-x="1421" d="M29 43l4 18l491 1321q20 51 74 52h225q53 0 74 -52l492 -1321l4 -18q0 -18 -13.5 -30.5t-29.5 -12.5h-183q-43 0 -59 39l-94 248h-606l-95 -248q-16 -39 -59 -39h-182q-19 0 -31 12.5t-12 30.5zM385 1581v117q0 20 12 32.5t33 12.5h559q20 0 33.5 -12.5t13.5 -32.5v-117 q0 -20 -13.5 -32.5t-33.5 -12.5h-559q-21 0 -33 12.5t-12 32.5zM479 524h463l-231 631z" />
<glyph unicode="&#x101;" d="M66 293q0 139 112.5 225t308.5 117l281 41v43q0 88 -45 133t-143 45q-68 0 -109 -24.5t-66.5 -50t-33.5 -34.5q-8 -25 -31 -24h-160q-18 0 -31.5 12t-13.5 33q2 51 50 115.5t148.5 112.5t248.5 48q242 0 352 -108.5t110 -284.5v-643q0 -20 -13 -34.5t-36 -14.5h-170 q-20 0 -34.5 14.5t-14.5 34.5v80q-45 -66 -127 -107.5t-205 -41.5q-104 0 -191 41.5t-137 113.5t-50 158zM272 1296v107q0 20 12.5 32.5t32.5 12.5h516q20 0 33.5 -12.5t13.5 -32.5v-107q0 -20 -13 -32.5t-34 -12.5h-516q-20 0 -32.5 12.5t-12.5 32.5zM330 313 q0 -66 54 -101.5t130 -35.5q113 0 184.5 74t71.5 213v41l-205 -33q-236 -37 -235 -158z" />
<glyph unicode="&#x102;" horiz-adv-x="1421" d="M29 43l4 18l491 1321q20 51 74 52h225q53 0 74 -52l492 -1321l4 -18q0 -18 -13.5 -30.5t-29.5 -12.5h-183q-43 0 -59 39l-94 248h-606l-95 -248q-16 -39 -59 -39h-182q-19 0 -31 12.5t-12 30.5zM422 1792q0 16 10 27.5t28 11.5h103q18 0 28.5 -11.5t10.5 -27.5 q0 -55 24.5 -91t83.5 -36q57 0 82 36t25 91q0 16 10 27.5t29 11.5h102q18 0 28.5 -11.5t10.5 -27.5q0 -121 -69.5 -196.5t-217.5 -75.5q-149 0 -218.5 75.5t-69.5 196.5zM479 524h463l-231 631z" />
<glyph unicode="&#x103;" d="M66 293q0 139 112.5 225t308.5 117l281 41v43q0 88 -45 133t-143 45q-68 0 -109 -24.5t-66.5 -50t-33.5 -34.5q-8 -25 -31 -24h-160q-18 0 -31.5 12t-13.5 33q2 51 50 115.5t148.5 112.5t248.5 48q242 0 352 -108.5t110 -284.5v-643q0 -20 -13 -34.5t-36 -14.5h-170 q-20 0 -34.5 14.5t-14.5 34.5v80q-45 -66 -127 -107.5t-205 -41.5q-104 0 -191 41.5t-137 113.5t-50 158zM299 1481q0 16 10.5 27.5t28.5 11.5h98q18 0 28.5 -11.5t10.5 -27.5q0 -127 101 -127q102 0 102 127q0 16 10 27.5t29 11.5h98q18 0 28.5 -11.5t10.5 -27.5 q0 -119 -68.5 -196t-209.5 -77q-140 0 -208.5 77t-68.5 196zM330 313q0 -66 54 -101.5t130 -35.5q113 0 184.5 74t71.5 213v41l-205 -33q-236 -37 -235 -158z" />
<glyph unicode="&#x104;" horiz-adv-x="1421" d="M29 43l4 18l491 1321q20 51 74 52h225q53 0 74 -52l492 -1321l4 -18q0 -18 -13.5 -30.5t-29.5 -12.5h-62q-59 -2 -94 -41t-35 -98q0 -61 36 -100.5t99 -39.5h27q20 0 34.5 -14t14.5 -35v-73q0 -20 -14.5 -35t-34.5 -15h-35q-147 0 -226 85t-79 227q0 78 35 132t96 73 q-18 6 -27 30l-71 191h-606l-95 -248q-16 -39 -59 -39h-182q-18 0 -30.5 12.5t-12.5 30.5zM479 524h463l-231 631z" />
<glyph unicode="&#x105;" d="M66 293q0 139 112.5 225t308.5 117l281 41v43q0 88 -45 133t-143 45q-68 0 -109 -24.5t-66.5 -50t-33.5 -34.5q-8 -25 -31 -24h-160q-18 0 -31.5 12t-13.5 33q2 51 50 115.5t148.5 112.5t248.5 48q242 0 352 -108.5t110 -284.5v-643q0 -16 -10 -29.5t-28 -17.5l-17 -2 q-117 -35 -117 -150q0 -59 28 -94t81 -35h14q23 0 36 -14t13 -35v-73q0 -23 -13 -36.5t-36 -13.5h-22q-131 0 -206 82t-75 219q0 61 31 112.5t72 78.5q-18 8 -19 27v61q-45 -66 -127 -107.5t-205 -41.5q-104 0 -191 41.5t-137 113.5t-50 158zM330 313q0 -66 54 -101.5 t130 -35.5q113 0 184.5 74t71.5 213v41l-205 -33q-236 -37 -235 -158z" />
<glyph unicode="&#x106;" horiz-adv-x="1402" d="M109 715q0 127 2 184q10 262 165.5 408.5t436.5 146.5q182 0 317 -62.5t210 -171t79 -243.5v-4q0 -16 -13.5 -27.5t-29.5 -11.5h-199q-25 0 -37 11t-20 40q-31 127 -106.5 179.5t-200.5 52.5q-293 0 -305 -328q-2 -55 -2 -170t2 -174q12 -328 305 -328q123 0 199.5 52.5 t107.5 179.5q8 29 20.5 40t36.5 11h199q18 0 31.5 -12.5t11.5 -30.5q-4 -135 -79 -244t-210 -171t-317 -62q-283 0 -437.5 144t-164.5 411q-2 55 -2 180zM576 1569q0 16 10 26l168 203q18 23 33.5 31t42.5 8h221q18 0 28.5 -11t10.5 -30q0 -14 -10 -24l-256 -209 q-18 -14 -34 -20.5t-38 -6.5h-143q-33 0 -33 33z" />
<glyph unicode="&#x107;" d="M92 532l2 82q8 221 142.5 346t359.5 125q162 0 273.5 -57t166 -140t58.5 -159q2 -20 -13.5 -34.5t-36.5 -14.5h-182q-23 0 -33 9t-20 34q-31 78 -81 112.5t-128 34.5q-104 0 -162.5 -65.5t-62.5 -200.5l-2 -76l2 -67q12 -266 225 -266q80 0 129 33.5t80 113.5 q8 23 19.5 33t33.5 10h182q20 0 36 -14.5t14 -34.5q-4 -72 -57.5 -155t-164 -142t-276.5 -59q-225 0 -359.5 124.5t-142.5 346.5zM465 1251q0 16 10 27l168 203q18 23 33.5 31t42.5 8h207q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-256 -209q-18 -14 -33.5 -20t-37.5 -6 h-129q-33 -1 -33 32z" />
<glyph unicode="&#x108;" horiz-adv-x="1402" d="M109 715q0 127 2 184q10 262 165.5 408.5t436.5 146.5q182 0 317 -62.5t210 -171t79 -243.5v-4q0 -16 -13.5 -27.5t-29.5 -11.5h-199q-25 0 -37 11t-20 40q-31 127 -106.5 179.5t-200.5 52.5q-293 0 -305 -328q-2 -55 -2 -170t2 -174q12 -328 305 -328q123 0 199.5 52.5 t107.5 179.5q8 29 20.5 40t36.5 11h199q18 0 31.5 -12.5t11.5 -30.5q-4 -135 -79 -244t-210 -171t-317 -62q-283 0 -437.5 144t-164.5 411q-2 55 -2 180zM353 1563q0 16 18 34l201 207q18 20 33.5 26.5t35.5 6.5h123q20 0 34.5 -6t35.5 -27l198 -207q18 -18 19 -34 q0 -27 -29 -27h-82q-37 0 -63 14l-174 109l-174 -109q-27 -14 -64 -14h-82q-30 0 -30 27z" />
<glyph unicode="&#x109;" d="M92 532l2 82q8 221 142.5 346t359.5 125q162 0 273.5 -57t166 -140t58.5 -159q2 -20 -13.5 -34.5t-36.5 -14.5h-182q-23 0 -33 9t-20 34q-31 78 -81 112.5t-128 34.5q-104 0 -162.5 -65.5t-62.5 -200.5l-2 -76l2 -67q12 -266 225 -266q80 0 129 33.5t80 113.5 q8 23 19.5 33t33.5 10h182q20 0 36 -14.5t14 -34.5q-4 -72 -57.5 -155t-164 -142t-276.5 -59q-225 0 -359.5 124.5t-142.5 346.5zM250 1257q0 16 18 35l199 207q20 20 34.5 26.5t35.5 6.5h114q20 0 36 -6.5t34 -26.5l201 -207q18 -18 18 -35q0 -27 -31 -26h-77q-37 0 -64 14 l-174 108l-174 -108q-27 -14 -64 -14h-75q-31 -1 -31 26z" />
<glyph unicode="&#x10a;" horiz-adv-x="1402" d="M109 715q0 127 2 184q10 262 165.5 408.5t436.5 146.5q182 0 317 -62.5t210 -171t79 -243.5v-4q0 -16 -13.5 -27.5t-29.5 -11.5h-199q-25 0 -37 11t-20 40q-31 127 -106.5 179.5t-200.5 52.5q-293 0 -305 -328q-2 -55 -2 -170t2 -174q12 -328 305 -328q123 0 199.5 52.5 t107.5 179.5q8 29 20.5 40t36.5 11h199q18 0 31.5 -12.5t11.5 -30.5q-4 -135 -79 -244t-210 -171t-317 -62q-283 0 -437.5 144t-164.5 411q-2 55 -2 180zM570 1581v174q0 20 12 33.5t33 13.5h174q20 0 33.5 -13t13.5 -34v-174q0 -20 -13.5 -32.5t-33.5 -12.5h-174 q-21 0 -33 12.5t-12 32.5z" />
<glyph unicode="&#x10b;" d="M92 532l2 82q8 221 142.5 346t359.5 125q162 0 273.5 -57t166 -140t58.5 -159q2 -20 -13.5 -34.5t-36.5 -14.5h-182q-23 0 -33 9t-20 34q-31 78 -81 112.5t-128 34.5q-104 0 -162.5 -65.5t-62.5 -200.5l-2 -76l2 -67q12 -266 225 -266q80 0 129 33.5t80 113.5 q8 23 19.5 33t33.5 10h182q20 0 36 -14.5t14 -34.5q-4 -72 -57.5 -155t-164 -142t-276.5 -59q-225 0 -359.5 124.5t-142.5 346.5zM465 1290v170q0 20 12.5 33.5t32.5 13.5h170q20 0 33.5 -13t13.5 -34v-170q0 -20 -13.5 -32.5t-33.5 -12.5h-170q-20 0 -32.5 12.5t-12.5 32.5 z" />
<glyph unicode="&#x10c;" horiz-adv-x="1402" d="M109 715q0 127 2 184q10 262 165.5 408.5t436.5 146.5q182 0 317 -62.5t210 -171t79 -243.5v-4q0 -16 -13.5 -27.5t-29.5 -11.5h-199q-25 0 -37 11t-20 40q-31 127 -106.5 179.5t-200.5 52.5q-293 0 -305 -328q-2 -55 -2 -170t2 -174q12 -328 305 -328q123 0 199.5 52.5 t107.5 179.5q8 29 20.5 40t36.5 11h199q18 0 31.5 -12.5t11.5 -30.5q-4 -135 -79 -244t-210 -171t-317 -62q-283 0 -437.5 144t-164.5 411q-2 55 -2 180zM353 1810q0 27 30 27h82q37 0 64 -14l174 -109l174 109q27 14 63 14h82q29 0 29 -27q0 -18 -19 -34l-198 -207 q-20 -20 -34.5 -26.5t-35.5 -6.5h-123q-20 0 -35.5 6t-33.5 27l-201 207q-18 16 -18 34z" />
<glyph unicode="&#x10d;" d="M92 532l2 82q8 221 142.5 346t359.5 125q162 0 273.5 -57t166 -140t58.5 -159q2 -20 -13.5 -34.5t-36.5 -14.5h-182q-23 0 -33 9t-20 34q-31 78 -81 112.5t-128 34.5q-104 0 -162.5 -65.5t-62.5 -200.5l-2 -76l2 -67q12 -266 225 -266q80 0 129 33.5t80 113.5 q8 23 19.5 33t33.5 10h182q20 0 36 -14.5t14 -34.5q-4 -72 -57.5 -155t-164 -142t-276.5 -59q-225 0 -359.5 124.5t-142.5 346.5zM250 1493q0 27 31 27h75q37 0 64 -15l174 -108l174 108q27 14 64 15h77q31 0 31 -27q0 -18 -18 -35l-201 -207q-18 -20 -33.5 -26t-36.5 -6 h-114q-21 0 -35 6t-35 26l-199 207q-18 16 -18 35z" />
<glyph unicode="&#x10e;" horiz-adv-x="1439" d="M160 49v1333q0 23 13 37.5t36 14.5h500q604 0 616 -557q4 -119 4 -160q0 -39 -4 -158q-8 -287 -154.5 -423t-451.5 -136h-510q-21 0 -35 14.5t-14 34.5zM357 1810q0 27 30 27h82q37 0 64 -14l174 -109l174 109q27 14 63 14h82q29 0 29 -27q0 -18 -19 -34l-198 -207 q-20 -20 -34.5 -26.5t-35.5 -6.5h-123q-20 0 -35.5 6t-33.5 27l-201 207q-18 16 -18 34zM449 238h260q170 0 245.5 75.5t79.5 251.5q4 123 4 154q0 33 -4 151q-4 168 -84 247t-252 79h-249v-958z" />
<glyph unicode="&#x10f;" horiz-adv-x="1257" d="M90 532l2 68q8 217 120 351t318 134q197 0 314 -135v455q0 23 14 36t35 13h178q23 0 36 -13.5t13 -35.5v-1356q0 -20 -13 -34.5t-36 -14.5h-166q-20 0 -34.5 14.5t-14.5 34.5v76q-113 -145 -326 -145q-206 0 -317 132t-121 353zM373 532l2 -61q14 -270 233 -270 q117 0 173.5 74.5t62.5 185.5q2 25 2 78q0 55 -2 79q-4 102 -63.5 174t-172.5 72q-219 0 -233 -270zM1229 1136l39 252q4 29 20 47.5t45 18.5h174q14 0 25.5 -11.5t11.5 -27.5q0 -12 -6 -27l-94 -244q-12 -25 -26.5 -39t-41.5 -14h-113q-16 0 -26 13.5t-8 31.5z" />
<glyph unicode="&#x110;" horiz-adv-x="1468" d="M31 670v104q0 23 14 36t35 13h106v559q0 23 14.5 37.5t35.5 14.5h501q600 0 617 -557q2 -59 2 -160q0 -98 -2 -158q-10 -287 -157.5 -423t-453.5 -136h-507q-21 0 -35.5 14.5t-14.5 34.5v569h-106q-20 0 -34.5 14.5t-14.5 37.5zM475 238h262q170 0 245 75.5t79 251.5 q4 123 4 154q0 33 -4 151q-4 170 -82 248t-248 78h-256v-373h227q23 0 36.5 -13t13.5 -36v-104q0 -23 -14.5 -37.5t-35.5 -14.5h-227v-380z" />
<glyph unicode="&#x111;" horiz-adv-x="1257" d="M92 532l2 68q6 209 112.5 347t323.5 138q199 0 316 -135v199h-96q-20 0 -35 14.5t-15 36.5v60q0 23 14.5 36t35.5 13h96v96q0 23 14 36t35 13h178q23 0 36 -13.5t13 -35.5v-96h95q23 0 36 -13.5t13 -35.5v-60q0 -23 -14.5 -37t-34.5 -14h-95v-1100q0 -20 -13 -34.5 t-36 -14.5h-166q-20 0 -34.5 14.5t-14.5 34.5v82q-115 -152 -328 -151q-217 0 -322.5 136t-113.5 349zM375 532q0 -41 2 -61q6 -123 59 -196.5t172 -73.5q117 0 174.5 74.5t63.5 185.5q2 25 2 78q0 55 -2 79q-4 102 -64.5 174t-173.5 72q-119 0 -172 -73.5t-59 -196.5 q-2 -20 -2 -62z" />
<glyph unicode="&#x112;" horiz-adv-x="1275" d="M160 49v1333q0 23 13 37.5t36 14.5h903q23 0 37 -14.5t14 -37.5v-137q0 -23 -14 -36t-37 -13h-672v-358h627q23 0 37 -14.5t14 -37.5v-129q0 -23 -14 -36t-37 -13h-627v-370h688q23 0 37.5 -13.5t14.5 -36.5v-139q0 -23 -14.5 -36t-37.5 -13h-919q-21 0 -35 14.5 t-14 34.5zM314 1581v117q0 20 12 32.5t33 12.5h559q20 0 33.5 -12.5t13.5 -32.5v-117q0 -20 -13.5 -32.5t-33.5 -12.5h-559q-21 0 -33 12.5t-12 32.5z" />
<glyph unicode="&#x113;" horiz-adv-x="1183" d="M88 535q0 256 134 403t372 147q244 0 376 -147t132 -393v-43q0 -20 -14.5 -34.5t-36.5 -14.5h-680v-17q4 -115 62.5 -189.5t160.5 -74.5q117 0 190 94q18 23 28.5 28t35.5 5h180q18 0 31.5 -10t13.5 -29q0 -49 -58.5 -115.5t-167 -115.5t-251.5 -49q-229 0 -363.5 131 t-142.5 372zM293 1296v107q0 20 12.5 32.5t32.5 12.5h516q20 0 33.5 -12.5t13.5 -32.5v-107q0 -20 -13 -32.5t-34 -12.5h-516q-20 0 -32.5 12.5t-12.5 32.5zM371 627h448v4q0 121 -60.5 193.5t-164.5 72.5t-163.5 -72.5t-59.5 -193.5v-4z" />
<glyph unicode="&#x114;" horiz-adv-x="1275" d="M160 49v1333q0 23 13 37.5t36 14.5h903q23 0 37 -14.5t14 -37.5v-137q0 -23 -14 -36t-37 -13h-672v-358h627q23 0 37 -14.5t14 -37.5v-129q0 -23 -14 -36t-37 -13h-627v-370h688q23 0 37.5 -13.5t14.5 -36.5v-139q0 -23 -14.5 -36t-37.5 -13h-919q-21 0 -35 14.5 t-14 34.5zM351 1792q0 16 10 27.5t28 11.5h103q18 0 28.5 -11.5t10.5 -27.5q0 -55 24.5 -91t83.5 -36q57 0 82 36t25 91q0 16 10 27.5t29 11.5h102q18 0 28.5 -11.5t10.5 -27.5q0 -121 -69.5 -196.5t-217.5 -75.5q-149 0 -218.5 75.5t-69.5 196.5z" />
<glyph unicode="&#x115;" horiz-adv-x="1183" d="M88 535q0 256 134 403t372 147q244 0 376 -147t132 -393v-43q0 -20 -14.5 -34.5t-36.5 -14.5h-680v-17q4 -115 62.5 -189.5t160.5 -74.5q117 0 190 94q18 23 28.5 28t35.5 5h180q18 0 31.5 -10t13.5 -29q0 -49 -58.5 -115.5t-167 -115.5t-251.5 -49q-229 0 -363.5 131 t-142.5 372zM317 1481q0 16 10.5 27.5t28.5 11.5h98q18 0 28.5 -11.5t10.5 -27.5q0 -127 101 -127q102 0 102 127q0 16 10 27.5t29 11.5h98q18 0 28.5 -11.5t10.5 -27.5q0 -119 -68.5 -196t-209.5 -77q-140 0 -208.5 77t-68.5 196zM371 627h448v4q0 121 -60.5 193.5 t-164.5 72.5t-163.5 -72.5t-59.5 -193.5v-4z" />
<glyph unicode="&#x116;" horiz-adv-x="1275" d="M160 49v1333q0 23 13 37.5t36 14.5h903q23 0 37 -14.5t14 -37.5v-137q0 -23 -14 -36t-37 -13h-672v-358h627q23 0 37 -14.5t14 -37.5v-129q0 -23 -14 -36t-37 -13h-627v-370h688q23 0 37.5 -13.5t14.5 -36.5v-139q0 -23 -14.5 -36t-37.5 -13h-919q-21 0 -35 14.5 t-14 34.5zM506 1581v174q0 20 12 33.5t33 13.5h174q20 0 33.5 -13t13.5 -34v-174q0 -20 -13.5 -32.5t-33.5 -12.5h-174q-21 0 -33 12.5t-12 32.5z" />
<glyph unicode="&#x117;" horiz-adv-x="1183" d="M88 535q0 256 134 403t372 147q244 0 376 -147t132 -393v-43q0 -20 -14.5 -34.5t-36.5 -14.5h-680v-17q4 -115 62.5 -189.5t160.5 -74.5q117 0 190 94q18 23 28.5 28t35.5 5h180q18 0 31.5 -10t13.5 -29q0 -49 -58.5 -115.5t-167 -115.5t-251.5 -49q-229 0 -363.5 131 t-142.5 372zM371 627h448v4q0 121 -60.5 193.5t-164.5 72.5t-163.5 -72.5t-59.5 -193.5v-4zM465 1290v170q0 20 12.5 33.5t32.5 13.5h170q20 0 33.5 -13t13.5 -34v-170q0 -20 -13.5 -32.5t-33.5 -12.5h-170q-20 0 -32.5 12.5t-12.5 32.5z" />
<glyph unicode="&#x118;" horiz-adv-x="1275" d="M160 49v1333q0 23 13 37.5t36 14.5h903q23 0 37 -14.5t14 -37.5v-137q0 -23 -14 -36t-37 -13h-672v-358h627q23 0 37 -14.5t14 -37.5v-129q0 -23 -14 -36t-37 -13h-627v-370h688q23 0 37.5 -13.5t14.5 -36.5v-139q0 -23 -14.5 -36t-37.5 -13h-32q-59 -2 -92 -41t-33 -98 q0 -61 34.5 -100.5t98.5 -39.5h26q23 0 36.5 -14t13.5 -35v-73q0 -23 -13.5 -36.5t-36.5 -13.5h-34q-148 0 -226.5 85t-78.5 227q0 76 28 139h-610q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x119;" horiz-adv-x="1185" d="M90 483v101q10 233 145.5 367t360.5 134q244 0 376 -147t132 -393v-43q0 -20 -14.5 -34.5t-36.5 -14.5h-680v-17q4 -113 62.5 -188.5t158.5 -75.5q119 0 192 94q18 23 28.5 28t35.5 5h180q18 0 30.5 -10t12.5 -29q0 -41 -44 -98.5t-128 -106.5t-194 -67 q-129 -23 -129 -127q0 -61 35.5 -100.5t99.5 -39.5h24q23 0 37 -14t14 -35v-73q0 -20 -14 -35t-37 -15h-32q-148 0 -227 85t-79 227q0 84 27 139q-154 43 -241 167t-95 316zM371 627h448v4q0 121 -60.5 193.5t-164.5 72.5t-163.5 -72.5t-59.5 -193.5v-4z" />
<glyph unicode="&#x11a;" horiz-adv-x="1275" d="M160 49v1333q0 23 13 37.5t36 14.5h903q23 0 37 -14.5t14 -37.5v-137q0 -23 -14 -36t-37 -13h-672v-358h627q23 0 37 -14.5t14 -37.5v-129q0 -23 -14 -36t-37 -13h-627v-370h688q23 0 37.5 -13.5t14.5 -36.5v-139q0 -23 -14.5 -36t-37.5 -13h-919q-21 0 -35 14.5 t-14 34.5zM289 1810q0 27 30 27h82q37 0 64 -14l174 -109l174 109q27 14 63 14h82q29 0 29 -27q0 -18 -19 -34l-198 -207q-20 -20 -34.5 -26.5t-35.5 -6.5h-123q-20 0 -35.5 6t-33.5 27l-201 207q-18 16 -18 34z" />
<glyph unicode="&#x11b;" horiz-adv-x="1183" d="M88 535q0 256 134 403t372 147q244 0 376 -147t132 -393v-43q0 -20 -14.5 -34.5t-36.5 -14.5h-680v-17q4 -115 62.5 -189.5t160.5 -74.5q117 0 190 94q18 23 28.5 28t35.5 5h180q18 0 31.5 -10t13.5 -29q0 -49 -58.5 -115.5t-167 -115.5t-251.5 -49q-229 0 -363.5 131 t-142.5 372zM250 1493q0 27 31 27h75q37 0 64 -15l174 -108l174 108q27 14 64 15h77q31 0 31 -27q0 -18 -18 -35l-201 -207q-18 -20 -33.5 -26t-36.5 -6h-114q-21 0 -35 6t-35 26l-199 207q-18 16 -18 35zM371 627h448v4q0 121 -60.5 193.5t-164.5 72.5t-163.5 -72.5 t-59.5 -193.5v-4z" />
<glyph unicode="&#x11c;" horiz-adv-x="1435" d="M111 721q0 131 2 190q8 254 167.5 398.5t438.5 144.5q188 0 324.5 -64.5t206 -159.5t73.5 -188q0 -18 -12.5 -30.5t-32.5 -12.5h-215q-20 0 -30.5 8.5t-18.5 26.5q-29 76 -100.5 129.5t-194.5 53.5q-143 0 -224 -77t-85 -239q-2 -59 -2 -180q0 -119 2 -180 q4 -166 86 -245t225 -79q145 0 233 78t88 233v64h-249q-20 0 -35 14.5t-15 34.5v113q0 20 14.5 34.5t35.5 14.5h491q23 0 37 -14.5t14 -34.5v-217q0 -170 -75.5 -296t-215 -193.5t-325.5 -67.5q-283 0 -439.5 145t-166.5 405q-2 59 -2 191zM377 1563q0 16 18 34l201 207 q18 20 33.5 26.5t35.5 6.5h123q20 0 34.5 -6t35.5 -27l198 -207q18 -18 19 -34q0 -27 -29 -27h-82q-37 0 -63 14l-174 109l-174 -109q-27 -14 -64 -14h-82q-30 0 -30 27z" />
<glyph unicode="&#x11d;" horiz-adv-x="1261" d="M92 545l2 55q8 219 116.5 352t319.5 133q109 0 191 -40.5t135 -110.5v80q0 23 15.5 37t35.5 14h168q20 0 34.5 -14.5t14.5 -36.5l2 -987q0 -227 -127 -352.5t-383 -125.5q-182 0 -294.5 60.5t-159.5 138.5t-47 133q0 20 14 33.5t35 13.5h182q18 0 31.5 -9t21.5 -34 q23 -53 68 -91t139 -38q123 0 180.5 54.5t57.5 189.5v139q-111 -131 -314 -131q-213 0 -320.5 129t-115.5 352zM264 1245q0 16 18 35l199 207q20 20 34.5 26.5t35.5 6.5h114q20 0 36 -6.5t34 -26.5l201 -207q18 -18 18 -35q0 -27 -31 -26h-77q-37 0 -64 14l-174 108 l-174 -108q-27 -14 -64 -14h-75q-31 -1 -31 26zM373 545q0 -319 235 -320q115 0 172.5 74t63.5 180q2 16 2 66q0 49 -2 65q-6 106 -63.5 180t-172.5 74q-221 0 -233 -270z" />
<glyph unicode="&#x11e;" horiz-adv-x="1435" d="M111 721q0 131 2 190q8 254 167.5 398.5t438.5 144.5q188 0 324.5 -64.5t206 -159.5t73.5 -188q0 -18 -12.5 -30.5t-32.5 -12.5h-215q-20 0 -30.5 8.5t-18.5 26.5q-29 76 -100.5 129.5t-194.5 53.5q-143 0 -224 -77t-85 -239q-2 -59 -2 -180q0 -119 2 -180 q4 -166 86 -245t225 -79q145 0 233 78t88 233v64h-249q-20 0 -35 14.5t-15 34.5v113q0 20 14.5 34.5t35.5 14.5h491q23 0 37 -14.5t14 -34.5v-217q0 -170 -75.5 -296t-215 -193.5t-325.5 -67.5q-283 0 -439.5 145t-166.5 405q-2 59 -2 191zM439 1792q0 16 10 27.5t28 11.5 h103q18 0 28.5 -11.5t10.5 -27.5q0 -55 24.5 -91t83.5 -36q57 0 82 36t25 91q0 16 10 27.5t29 11.5h102q18 0 28.5 -11.5t10.5 -27.5q0 -121 -69.5 -196.5t-217.5 -75.5q-149 0 -218.5 75.5t-69.5 196.5z" />
<glyph unicode="&#x11f;" horiz-adv-x="1261" d="M92 545l2 55q8 219 116.5 352t319.5 133q109 0 191 -40.5t135 -110.5v80q0 23 15.5 37t35.5 14h168q20 0 34.5 -14.5t14.5 -36.5l2 -987q0 -227 -127 -352.5t-383 -125.5q-182 0 -294.5 60.5t-159.5 138.5t-47 133q0 20 14 33.5t35 13.5h182q18 0 31.5 -9t21.5 -34 q23 -53 68 -91t139 -38q123 0 180.5 54.5t57.5 189.5v139q-111 -131 -314 -131q-213 0 -320.5 129t-115.5 352zM332 1481q0 16 10.5 27.5t28.5 11.5h98q18 0 28.5 -11.5t10.5 -27.5q0 -127 101 -127q102 0 102 127q0 16 10 27.5t29 11.5h98q18 0 28.5 -11.5t10.5 -27.5 q0 -119 -68.5 -196t-209.5 -77q-140 0 -208.5 77t-68.5 196zM373 545q0 -319 235 -320q115 0 172.5 74t63.5 180q2 16 2 66q0 49 -2 65q-6 106 -63.5 180t-172.5 74q-221 0 -233 -270z" />
<glyph unicode="&#x120;" horiz-adv-x="1435" d="M111 721q0 131 2 190q8 254 167.5 398.5t438.5 144.5q188 0 324.5 -64.5t206 -159.5t73.5 -188q0 -18 -12.5 -30.5t-32.5 -12.5h-215q-20 0 -30.5 8.5t-18.5 26.5q-29 76 -100.5 129.5t-194.5 53.5q-143 0 -224 -77t-85 -239q-2 -59 -2 -180q0 -119 2 -180 q4 -166 86 -245t225 -79q145 0 233 78t88 233v64h-249q-20 0 -35 14.5t-15 34.5v113q0 20 14.5 34.5t35.5 14.5h491q23 0 37 -14.5t14 -34.5v-217q0 -170 -75.5 -296t-215 -193.5t-325.5 -67.5q-283 0 -439.5 145t-166.5 405q-2 59 -2 191zM594 1581v174q0 20 12 33.5 t33 13.5h174q20 0 33.5 -13t13.5 -34v-174q0 -20 -13.5 -32.5t-33.5 -12.5h-174q-21 0 -33 12.5t-12 32.5z" />
<glyph unicode="&#x121;" horiz-adv-x="1261" d="M92 545l2 55q8 219 116.5 352t319.5 133q109 0 191 -40.5t135 -110.5v80q0 23 15.5 37t35.5 14h168q20 0 34.5 -14.5t14.5 -36.5l2 -987q0 -227 -127 -352.5t-383 -125.5q-182 0 -294.5 60.5t-159.5 138.5t-47 133q0 20 14 33.5t35 13.5h182q18 0 31.5 -9t21.5 -34 q23 -53 68 -91t139 -38q123 0 180.5 54.5t57.5 189.5v139q-111 -131 -314 -131q-213 0 -320.5 129t-115.5 352zM373 545q0 -319 235 -320q115 0 172.5 74t63.5 180q2 16 2 66q0 49 -2 65q-6 106 -63.5 180t-172.5 74q-221 0 -233 -270zM479 1290v170q0 20 12.5 33.5 t32.5 13.5h170q20 0 33.5 -13t13.5 -34v-170q0 -20 -13.5 -32.5t-33.5 -12.5h-170q-20 0 -32.5 12.5t-12.5 32.5z" />
<glyph unicode="&#x122;" horiz-adv-x="1435" d="M111 721q0 131 2 190q8 254 167.5 398.5t438.5 144.5q188 0 324.5 -64.5t206 -159.5t73.5 -188q0 -18 -12.5 -30.5t-32.5 -12.5h-215q-20 0 -30.5 8.5t-18.5 26.5q-29 76 -100.5 129.5t-194.5 53.5q-143 0 -224 -77t-85 -239q-2 -59 -2 -180q0 -119 2 -180 q4 -166 86 -245t225 -79q145 0 233 78t88 233v64h-249q-20 0 -35 14.5t-15 34.5v113q0 20 14.5 34.5t35.5 14.5h491q23 0 37 -14.5t14 -34.5v-217q0 -170 -75.5 -296t-215 -193.5t-325.5 -67.5q-283 0 -439.5 145t-166.5 405q-2 59 -2 191zM544 -420l39 252q4 29 20.5 47.5 t45.5 18.5h184q14 0 25.5 -11.5t11.5 -27.5q0 -12 -6 -27l-94 -244q-12 -25 -27.5 -39t-42.5 -14h-119q-18 0 -28.5 13.5t-8.5 31.5z" />
<glyph unicode="&#x123;" horiz-adv-x="1261" d="M92 545l2 55q8 219 116.5 352t319.5 133q109 0 191 -40.5t135 -110.5v80q0 23 15.5 37t35.5 14h168q20 0 34.5 -14.5t14.5 -36.5l2 -987q0 -227 -127 -352.5t-383 -125.5q-182 0 -294.5 60.5t-159.5 138.5t-47 133q0 20 14 33.5t35 13.5h182q18 0 31.5 -9t21.5 -34 q23 -53 68 -91t139 -38q123 0 180.5 54.5t57.5 189.5v139q-111 -131 -314 -131q-213 0 -320.5 129t-115.5 352zM373 545q0 -319 235 -320q115 0 172.5 74t63.5 180q2 16 2 66q0 49 -2 65q-6 106 -63.5 180t-172.5 74q-221 0 -233 -270zM492 1257q0 12 6 27l94 244 q12 25 26.5 39t40.5 14h113q16 0 26.5 -13.5t8.5 -31.5l-39 -252q-4 -29 -20.5 -47t-45.5 -18h-174q-14 0 -25 11t-11 27z" />
<glyph unicode="&#x124;" horiz-adv-x="1495" d="M160 49v1333q0 23 13 37.5t36 14.5h188q23 0 37.5 -14.5t14.5 -37.5v-526h598v526q0 23 14 37.5t35 14.5h190q23 0 36 -14.5t13 -37.5v-1333q0 -20 -13 -34.5t-36 -14.5h-190q-20 0 -34.5 14.5t-14.5 34.5v547h-598v-547q0 -20 -14.5 -34.5t-37.5 -14.5h-188 q-21 0 -35 14.5t-14 34.5zM398 1563q0 16 18 34l201 207q18 20 33.5 26.5t35.5 6.5h123q20 0 34.5 -6t35.5 -27l198 -207q18 -18 19 -34q0 -27 -29 -27h-82q-37 0 -63 14l-174 109l-174 -109q-27 -14 -64 -14h-82q-30 0 -30 27z" />
<glyph unicode="&#x125;" horiz-adv-x="1290" d="M137 49v1356q0 23 14.5 36t34.5 13h187q23 0 36 -13.5t13 -35.5v-467q123 147 330 147q188 0 300.5 -123.5t112.5 -336.5v-576q0 -20 -13 -34.5t-36 -14.5h-186q-23 0 -37 14.5t-14 34.5v563q0 119 -58.5 185.5t-167.5 66.5q-104 0 -167.5 -67.5t-63.5 -184.5v-563 q0 -20 -13.5 -34.5t-35.5 -14.5h-187q-20 0 -34.5 14.5t-14.5 34.5zM305 1562q0 16 18 35l199 207q20 20 34.5 26.5t35.5 6.5h114q20 0 36 -6.5t34 -26.5l201 -207q18 -18 18 -35q0 -27 -31 -26h-77q-37 0 -64 14l-174 108l-174 -108q-27 -14 -64 -14h-75q-31 -1 -31 26z " />
<glyph unicode="&#x126;" horiz-adv-x="1495" d="M78 1106v76q0 23 14 36t35 13h33v151q0 23 13 37.5t36 14.5h188q23 0 37.5 -14.5t14.5 -37.5v-151h598v151q0 23 14 37.5t35 14.5h190q23 0 36 -14.5t13 -37.5v-151h43q23 0 36 -13.5t13 -35.5v-76q0 -20 -14 -34.5t-35 -14.5h-43v-1008q0 -20 -13 -34.5t-36 -14.5h-190 q-20 0 -34.5 14.5t-14.5 34.5v547h-598v-547q0 -20 -14.5 -34.5t-37.5 -14.5h-188q-20 0 -34.5 14.5t-14.5 34.5v1008h-33q-20 0 -34.5 14t-14.5 35zM449 856h598v201h-598v-201z" />
<glyph unicode="&#x127;" horiz-adv-x="1290" d="M-4 1190v61q0 23 14.5 36t34.5 13h90v105q0 20 14.5 34.5t36.5 14.5h187q23 0 36 -13.5t13 -35.5v-105h90q20 0 34.5 -14t14.5 -35v-61q0 -20 -14.5 -34.5t-34.5 -14.5h-90v-203q121 147 330 147q188 0 300.5 -123.5t112.5 -336.5v-574q0 -23 -14 -37t-35 -14h-188 q-20 0 -34.5 14.5t-14.5 36.5v561q0 119 -58.5 185.5t-167.5 66.5q-106 0 -168.5 -67.5t-62.5 -184.5v-563q0 -20 -13.5 -34.5t-35.5 -14.5h-187q-22 0 -36.5 14.5t-14.5 34.5v1092h-90q-20 0 -34.5 14t-14.5 35z" />
<glyph unicode="&#x128;" horiz-adv-x="614" d="M-16 1571q0 47 22.5 100t68.5 90t113 37q39 0 67 -10t66 -31q29 -16 46.5 -23.5t37.5 -7.5q18 0 28.5 8.5t23.5 24.5q10 16 18 22.5t23 6.5h94q16 0 26.5 -10.5t10.5 -24.5q0 -47 -23.5 -100.5t-71 -90t-112.5 -36.5q-39 0 -66.5 10t-66.5 31q-29 16 -46.5 23t-37.5 7 q-18 0 -28.5 -7t-22.5 -25q-16 -29 -39 -29h-97q-14 0 -24 10t-10 25zM160 49v1335q0 23 14 36.5t35 13.5h197q23 0 36 -13.5t13 -36.5v-1335q0 -20 -13.5 -34.5t-35.5 -14.5h-197q-21 0 -35 14.5t-14 34.5z" />
<glyph unicode="&#x129;" horiz-adv-x="542" d="M-45 1253q0 47 21.5 98.5t66.5 87.5t113 36q39 0 65.5 -10.5t63.5 -30.5q27 -16 43 -23.5t37 -7.5q18 0 29.5 8t21.5 25q10 14 18 21t23 7h94q16 0 26.5 -10t10.5 -24q0 -47 -22.5 -98.5t-68.5 -87.5t-112 -36q-39 0 -65.5 10.5t-63.5 30.5q-27 16 -43 23.5t-37 7.5 q-18 0 -28.5 -8t-22.5 -25q-16 -29 -39 -28h-96q-14 0 -24.5 10t-10.5 24zM135 49v967q0 23 14.5 36t34.5 13h174q20 0 35 -14.5t15 -34.5v-967q0 -20 -14.5 -34.5t-35.5 -14.5h-174q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x12a;" horiz-adv-x="614" d="M-18 1581v117q0 20 12 32.5t33 12.5h559q20 0 33.5 -12.5t13.5 -32.5v-117q0 -20 -13.5 -32.5t-33.5 -12.5h-559q-21 0 -33 12.5t-12 32.5zM160 49v1335q0 23 14 36.5t35 13.5h197q23 0 36 -13.5t13 -36.5v-1335q0 -20 -13.5 -34.5t-35.5 -14.5h-197q-21 0 -35 14.5 t-14 34.5z" />
<glyph unicode="&#x12b;" horiz-adv-x="542" d="M-33 1296v107q0 20 12.5 32.5t32.5 12.5h516q20 0 33.5 -12.5t13.5 -32.5v-107q0 -20 -13 -32.5t-34 -12.5h-516q-20 0 -32.5 12.5t-12.5 32.5zM135 49v967q0 23 14.5 36t34.5 13h174q20 0 35 -14.5t15 -34.5v-967q0 -20 -14.5 -34.5t-35.5 -14.5h-174q-20 0 -34.5 14.5 t-14.5 34.5z" />
<glyph unicode="&#x12c;" horiz-adv-x="614" d="M19 1792q0 16 10 27.5t28 11.5h103q18 0 28.5 -11.5t10.5 -27.5q0 -55 24.5 -91t83.5 -36q57 0 82 36t25 91q0 16 10 27.5t29 11.5h102q18 0 28.5 -11.5t10.5 -27.5q0 -121 -69.5 -196.5t-217.5 -75.5q-149 0 -218.5 75.5t-69.5 196.5zM160 49v1335q0 23 14 36.5t35 13.5 h197q23 0 36 -13.5t13 -36.5v-1335q0 -20 -13.5 -34.5t-35.5 -14.5h-197q-21 0 -35 14.5t-14 34.5z" />
<glyph unicode="&#x12d;" horiz-adv-x="542" d="M-8 1481q0 16 10.5 27.5t28.5 11.5h98q18 0 28.5 -11.5t10.5 -27.5q0 -127 101 -127q102 0 102 127q0 16 10 27.5t29 11.5h98q18 0 28.5 -11.5t10.5 -27.5q0 -119 -68.5 -196t-209.5 -77q-140 0 -208.5 77t-68.5 196zM135 49v967q0 23 14.5 36t34.5 13h174q20 0 35 -14.5 t15 -34.5v-967q0 -20 -14.5 -34.5t-35.5 -14.5h-174q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x12e;" horiz-adv-x="634" d="M88 -141q0 80 36 134t99 73l-18 10q-10 4 -17.5 13t-7.5 22v1273q0 20 14.5 35t34.5 15h197q23 0 37 -14.5t14 -35.5v-1335q0 -20 -14 -34.5t-37 -14.5h-25q-63 0 -99 -40t-36 -101q0 -59 36 -99.5t99 -40.5h25q23 0 37 -14t14 -35v-73q0 -20 -14 -35t-37 -15h-33 q-147 0 -226 85t-79 227z" />
<glyph unicode="&#x12f;" horiz-adv-x="567" d="M63 -133q0 76 33 127t92 69l-4 7q-10 2 -17 10t-7 20v916q0 23 14 36t35 13h174q23 0 36 -13.5t13 -35.5v-967q0 -20 -13 -34.5t-36 -14.5h-14q-63 0 -98 -36t-35 -97q0 -59 34.5 -94t98.5 -35h26q20 0 34.5 -14.5t14.5 -36.5v-64q0 -23 -13 -36t-36 -13h-35 q-141 0 -219 80t-78 213zM152 1288v150q0 23 14 37t35 14h190q20 0 34.5 -15.5t14.5 -35.5v-150q0 -20 -14 -34.5t-35 -14.5h-190q-21 0 -35 14.5t-14 34.5z" />
<glyph unicode="&#x130;" horiz-adv-x="614" d="M160 49v1335q0 23 14 36.5t35 13.5h197q23 0 36 -13.5t13 -36.5v-1335q0 -20 -13.5 -34.5t-35.5 -14.5h-197q-21 0 -35 14.5t-14 34.5zM174 1581v174q0 20 12 33.5t33 13.5h174q20 0 33.5 -13t13.5 -34v-174q0 -20 -13.5 -32.5t-33.5 -12.5h-174q-21 0 -33 12.5t-12 32.5 z" />
<glyph unicode="&#x131;" horiz-adv-x="542" d="M135 49v967q0 23 14.5 36t34.5 13h174q20 0 35 -14.5t15 -34.5v-967q0 -20 -14.5 -34.5t-35.5 -14.5h-174q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x132;" horiz-adv-x="1392" d="M160 49v1335q0 23 14 36.5t35 13.5h197q23 0 36 -13.5t13 -36.5v-1335q0 -20 -13.5 -34.5t-35.5 -14.5h-197q-21 0 -35 14.5t-14 34.5zM618 29v155q0 23 14.5 37.5t35.5 14.5q139 0 209.5 83.5t70.5 241.5v823q0 20 14.5 35t36.5 15h195q23 0 37 -14.5t14 -37.5v-858 q0 -270 -149.5 -407t-427.5 -137q-21 0 -35.5 14t-14.5 35z" />
<glyph unicode="&#x133;" horiz-adv-x="1116" d="M127 1288v150q0 23 14.5 37t34.5 14h189q23 0 37 -14.5t14 -36.5v-150q0 -20 -15.5 -34.5t-35.5 -14.5h-189q-20 0 -34.5 14.5t-14.5 34.5zM135 49v967q0 23 14.5 36t34.5 13h174q20 0 35 -14.5t15 -34.5v-967q0 -20 -14.5 -34.5t-35.5 -14.5h-174q-20 0 -34.5 14.5 t-14.5 34.5zM453 -219q0 20 13.5 34.5t35.5 14.5h53q86 0 116 44t30 130v1012q0 23 14 36t35 13h180q23 0 36 -13.5t13 -35.5v-1018q0 -184 -100 -285.5t-297 -101.5h-80q-22 0 -35.5 13.5t-13.5 35.5v121zM690 1288v150q0 20 14.5 35.5t35.5 15.5h198q20 0 34.5 -14.5 t14.5 -36.5v-150q0 -20 -14 -34.5t-35 -14.5h-198q-21 0 -35.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x134;" horiz-adv-x="1337" d="M61 432q0 18 12.5 31.5t32.5 13.5h199q47 0 60 -51q16 -106 86.5 -157.5t179.5 -51.5q127 0 195.5 83t68.5 232v656h-694q-20 0 -34.5 14.5t-14.5 36.5v145q0 23 14 36.5t35 13.5h940q23 0 36 -14.5t13 -37.5v-858q0 -170 -69.5 -292.5t-197.5 -187t-300 -64.5 q-152 0 -278 51t-202.5 153.5t-81.5 247.5zM330 1563q0 16 18 34l201 207q18 20 33.5 26.5t35.5 6.5h123q20 0 34.5 -6t35.5 -27l198 -207q18 -18 19 -34q0 -27 -29 -27h-82q-37 0 -63 14l-174 109l-174 -109q-27 -14 -64 -14h-82q-30 0 -30 27z" />
<glyph unicode="&#x135;" horiz-adv-x="733" d="M-2 1245q0 16 18 35l199 207q20 20 34.5 26.5t35.5 6.5h114q20 0 36 -6.5t34 -26.5l201 -207q18 -18 18 -35q0 -27 -31 -26h-77q-37 0 -64 14l-174 108l-174 -108q-27 -14 -64 -14h-75q-31 -1 -31 26zM31 -190q0 20 14 34.5t35 14.5h92q84 0 113.5 45t29.5 129v807h-221 q-20 0 -34.5 14t-14.5 35v127q0 23 14.5 36t34.5 13h453q23 0 36 -13.5t13 -35.5v-983q0 -195 -100.5 -308.5t-296.5 -113.5h-119q-21 0 -35 13.5t-14 35.5v150z" />
<glyph unicode="&#x136;" horiz-adv-x="1296" d="M160 49v1333q0 23 13 37.5t36 14.5h184q23 0 37 -14.5t14 -37.5v-479l457 494q31 37 80 37h209q16 0 29.5 -12.5t13.5 -30.5q0 -14 -10 -25l-557 -618l598 -678q8 -14 8 -27q0 -18 -12.5 -30.5t-28.5 -12.5h-215q-43 0 -61.5 17.5t-20.5 19.5l-490 541v-529 q0 -20 -14 -34.5t-37 -14.5h-184q-21 0 -35 14.5t-14 34.5zM481 -420l39 252q4 29 20.5 47.5t45.5 18.5h184q14 0 25.5 -11.5t11.5 -27.5q0 -12 -6 -27l-94 -244q-12 -25 -27.5 -39t-42.5 -14h-119q-18 0 -28.5 13.5t-8.5 31.5z" />
<glyph unicode="&#x137;" horiz-adv-x="1130" d="M137 49v1356q0 23 14.5 36t34.5 13h170q20 0 35 -14.5t15 -34.5v-700l339 325q6 4 19.5 15.5t26 15.5t30.5 4h195q20 0 32.5 -12.5t12.5 -32.5q0 -18 -21 -37l-417 -395l469 -508q18 -18 18 -35q0 -20 -12.5 -32.5t-30.5 -12.5h-199q-29 0 -42 7t-33 28l-387 411v-397 q0 -20 -14.5 -34.5t-35.5 -14.5h-170q-20 0 -34.5 14.5t-14.5 34.5zM373 -420l39 252q4 29 20 47.5t45 18.5h174q14 0 25.5 -11.5t11.5 -27.5q0 -12 -6 -27l-94 -244q-12 -25 -26.5 -39t-41.5 -14h-113q-16 0 -26 13.5t-8 31.5z" />
<glyph unicode="&#x138;" horiz-adv-x="1132" d="M135 49v967q0 23 14.5 36t34.5 13h170q23 0 36 -13.5t13 -35.5v-367l361 381q4 4 17.5 15.5t25.5 15.5t31 4h184q18 0 30.5 -12.5t12.5 -32.5q0 -18 -18 -37l-420 -444l465 -459q18 -18 18 -35q0 -20 -12.5 -32.5t-32.5 -12.5h-201q-25 0 -37 6t-23 15.5t-16 13.5 l-385 379v-365q0 -20 -13 -34.5t-36 -14.5h-170q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x139;" horiz-adv-x="1189" d="M160 49v1335q0 23 14 36.5t35 13.5h192q23 0 36.5 -13.5t13.5 -36.5v-1138h649q23 0 37 -14.5t14 -34.5v-148q0 -20 -14.5 -34.5t-36.5 -14.5h-891q-21 0 -35 14.5t-14 34.5zM170 1569q0 16 10 26l168 203q18 23 33.5 31t42.5 8h221q18 0 28.5 -11t10.5 -30 q0 -14 -10 -24l-256 -209q-18 -14 -34 -20.5t-38 -6.5h-143q-33 0 -33 33z" />
<glyph unicode="&#x13a;" horiz-adv-x="544" d="M137 49v1356q0 23 14.5 36t34.5 13h172q23 0 36.5 -13.5t13.5 -35.5v-1356q0 -20 -13.5 -34.5t-36.5 -14.5h-172q-20 0 -34.5 14.5t-14.5 34.5zM172 1568q0 16 10 27l168 203q18 23 33.5 31t42.5 8h207q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-256 -209 q-18 -14 -33.5 -20t-37.5 -6h-129q-33 -1 -33 32z" />
<glyph unicode="&#x13b;" horiz-adv-x="1189" d="M160 49v1335q0 23 14 36.5t35 13.5h192q23 0 36.5 -13.5t13.5 -36.5v-1138h649q23 0 37 -14.5t14 -34.5v-148q0 -20 -14.5 -34.5t-36.5 -14.5h-891q-21 0 -35 14.5t-14 34.5zM442 -420l39 252q4 29 20.5 47.5t45.5 18.5h184q14 0 25.5 -11.5t11.5 -27.5q0 -12 -6 -27 l-94 -244q-12 -25 -27.5 -39t-42.5 -14h-119q-18 0 -28.5 13.5t-8.5 31.5z" />
<glyph unicode="&#x13c;" horiz-adv-x="544" d="M86 -420l39 252q4 29 20 47.5t45 18.5h174q14 0 25.5 -11.5t11.5 -27.5q0 -12 -6 -27l-94 -244q-12 -25 -26.5 -39t-41.5 -14h-113q-16 0 -26 13.5t-8 31.5zM137 49v1356q0 23 14.5 36t34.5 13h172q23 0 36.5 -13.5t13.5 -35.5v-1356q0 -20 -13.5 -34.5t-36.5 -14.5h-172 q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x13d;" horiz-adv-x="1189" d="M160 49v1335q0 23 14 36.5t35 13.5h192q23 0 36.5 -13.5t13.5 -36.5v-1138h649q23 0 37 -14.5t14 -34.5v-148q0 -20 -14.5 -34.5t-36.5 -14.5h-891q-21 0 -35 14.5t-14 34.5zM626 1136l39 252q4 29 20.5 47.5t45.5 18.5h184q14 0 25.5 -11.5t11.5 -27.5q0 -12 -6 -27 l-94 -244q-12 -25 -27.5 -39t-42.5 -14h-119q-18 0 -28.5 13.5t-8.5 31.5z" />
<glyph unicode="&#x13e;" horiz-adv-x="702" d="M137 49v1356q0 23 14.5 36t34.5 13h172q23 0 36.5 -13.5t13.5 -35.5v-1356q0 -20 -13.5 -34.5t-36.5 -14.5h-172q-20 0 -34.5 14.5t-14.5 34.5zM516 1136l39 252q4 29 20 47.5t45 18.5h174q14 0 25.5 -11.5t11.5 -27.5q0 -12 -6 -27l-94 -244q-12 -25 -26.5 -39 t-41.5 -14h-113q-16 0 -26 13.5t-8 31.5z" />
<glyph unicode="&#x13f;" horiz-adv-x="1189" d="M160 49v1335q0 23 14 36.5t35 13.5h192q23 0 36.5 -13.5t13.5 -36.5v-1138h649q23 0 37 -14.5t14 -34.5v-148q0 -20 -14.5 -34.5t-36.5 -14.5h-891q-21 0 -35 14.5t-14 34.5zM647 684v170q0 20 12.5 33.5t32.5 13.5h170q20 0 33.5 -13t13.5 -34v-170q0 -20 -13.5 -32.5 t-33.5 -12.5h-170q-20 0 -32.5 12.5t-12.5 32.5z" />
<glyph unicode="&#x140;" horiz-adv-x="737" d="M137 49v1356q0 23 14.5 36t34.5 13h172q23 0 36.5 -13.5t13.5 -35.5v-1356q0 -20 -13.5 -34.5t-36.5 -14.5h-172q-20 0 -34.5 14.5t-14.5 34.5zM512 651v170q0 20 12.5 33.5t32.5 13.5h170q20 0 33.5 -13t13.5 -34v-170q0 -20 -13.5 -32.5t-33.5 -12.5h-170 q-20 0 -32.5 12.5t-12.5 32.5z" />
<glyph unicode="&#x141;" horiz-adv-x="1206" d="M25 543v114q0 25 10 36.5t35 21.5l106 41v628q0 23 14.5 36.5t34.5 13.5h193q23 0 36 -13.5t13 -36.5v-514l297 115q16 6 27 6q16 0 27 -10t11 -29v-114q0 -25 -10 -36t-35 -22l-317 -125v-409h651q20 0 34.5 -14.5t14.5 -34.5v-148q0 -20 -14 -34.5t-35 -14.5h-893 q-20 0 -34.5 14.5t-14.5 34.5v492l-86 -33q-16 -6 -24 -6q-16 0 -28.5 11t-12.5 30z" />
<glyph unicode="&#x142;" horiz-adv-x="636" d="M49 582v116q0 25 10.5 36t34.5 22l90 32v617q0 23 14.5 36t34.5 13h173q23 0 37 -14.5t14 -34.5v-510l67 27q14 6 27 6q16 0 27.5 -10.5t11.5 -28.5v-115q0 -25 -10.5 -37t-34.5 -22l-88 -35v-631q0 -20 -14.5 -34.5t-36.5 -14.5h-173q-20 0 -34.5 14.5t-14.5 34.5v526 l-69 -26q-14 -6 -25 -6q-18 0 -29.5 10t-11.5 29z" />
<glyph unicode="&#x143;" horiz-adv-x="1447" d="M160 49v1333q0 23 13 37.5t36 14.5h160q39 0 59 -35l586 -910v893q0 23 13 37.5t36 14.5h176q23 0 37 -14.5t14 -37.5v-1331q0 -23 -14 -37t-35 -14h-162q-41 0 -61 35l-584 889v-875q0 -20 -13 -34.5t-36 -14.5h-176q-21 0 -35 14.5t-14 34.5zM598 1569q0 16 10 26 l168 203q18 23 33.5 31t42.5 8h221q18 0 28.5 -11t10.5 -30q0 -14 -10 -24l-256 -209q-18 -14 -34 -20.5t-38 -6.5h-143q-33 0 -33 33z" />
<glyph unicode="&#x144;" horiz-adv-x="1280" d="M135 49v967q0 23 14.5 36t34.5 13h170q23 0 36 -13.5t13 -35.5v-84q125 154 342 153q188 0 299 -122.5t111 -337.5v-576q0 -20 -13.5 -34.5t-35.5 -14.5h-184q-20 0 -35 14.5t-15 34.5v563q0 119 -58 185.5t-167 66.5q-104 0 -167.5 -67.5t-63.5 -184.5v-563 q0 -20 -13.5 -34.5t-35.5 -14.5h-183q-20 0 -34.5 14.5t-14.5 34.5zM518 1251q0 16 10 27l168 203q18 23 33.5 31t42.5 8h207q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-256 -209q-18 -14 -33.5 -20t-37.5 -6h-129q-33 -1 -33 32z" />
<glyph unicode="&#x145;" horiz-adv-x="1447" d="M160 49v1333q0 23 13 37.5t36 14.5h160q39 0 59 -35l586 -910v893q0 23 13 37.5t36 14.5h176q23 0 37 -14.5t14 -37.5v-1331q0 -23 -14 -37t-35 -14h-162q-41 0 -61 35l-584 889v-875q0 -20 -13 -34.5t-36 -14.5h-176q-21 0 -35 14.5t-14 34.5zM540 -420l39 252 q4 29 20.5 47.5t45.5 18.5h184q14 0 25.5 -11.5t11.5 -27.5q0 -12 -6 -27l-94 -244q-12 -25 -27.5 -39t-42.5 -14h-119q-18 0 -28.5 13.5t-8.5 31.5z" />
<glyph unicode="&#x146;" horiz-adv-x="1280" d="M135 49v967q0 23 14.5 36t34.5 13h170q23 0 36 -13.5t13 -35.5v-84q125 154 342 153q188 0 299 -122.5t111 -337.5v-576q0 -20 -13.5 -34.5t-35.5 -14.5h-184q-20 0 -35 14.5t-15 34.5v563q0 119 -58 185.5t-167 66.5q-104 0 -167.5 -67.5t-63.5 -184.5v-563 q0 -20 -13.5 -34.5t-35.5 -14.5h-183q-20 0 -34.5 14.5t-14.5 34.5zM469 -420l39 252q4 29 20 47.5t45 18.5h174q14 0 25.5 -11.5t11.5 -27.5q0 -12 -6 -27l-94 -244q-12 -25 -26.5 -39t-41.5 -14h-113q-16 0 -26 13.5t-8 31.5z" />
<glyph unicode="&#x147;" horiz-adv-x="1447" d="M160 49v1333q0 23 13 37.5t36 14.5h160q39 0 59 -35l586 -910v893q0 23 13 37.5t36 14.5h176q23 0 37 -14.5t14 -37.5v-1331q0 -23 -14 -37t-35 -14h-162q-41 0 -61 35l-584 889v-875q0 -20 -13 -34.5t-36 -14.5h-176q-21 0 -35 14.5t-14 34.5zM375 1810q0 27 30 27h82 q37 0 64 -14l174 -109l174 109q27 14 63 14h82q29 0 29 -27q0 -18 -19 -34l-198 -207q-20 -20 -34.5 -26.5t-35.5 -6.5h-123q-20 0 -35.5 6t-33.5 27l-201 207q-18 16 -18 34z" />
<glyph unicode="&#x148;" horiz-adv-x="1280" d="M135 49v967q0 23 14.5 36t34.5 13h170q23 0 36 -13.5t13 -35.5v-84q125 154 342 153q188 0 299 -122.5t111 -337.5v-576q0 -20 -13.5 -34.5t-35.5 -14.5h-184q-20 0 -35 14.5t-15 34.5v563q0 119 -58 185.5t-167 66.5q-104 0 -167.5 -67.5t-63.5 -184.5v-563 q0 -20 -13.5 -34.5t-35.5 -14.5h-183q-20 0 -34.5 14.5t-14.5 34.5zM299 1493q0 27 31 27h75q37 0 64 -15l174 -108l174 108q27 14 64 15h77q31 0 31 -27q0 -18 -18 -35l-201 -207q-18 -20 -33.5 -26t-36.5 -6h-114q-21 0 -35 6t-35 26l-199 207q-18 16 -18 35z" />
<glyph unicode="&#x149;" horiz-adv-x="1280" d="M135 49v967q0 23 14.5 36t34.5 13h170q23 0 36 -13.5t13 -35.5v-84q125 154 342 153q188 0 299 -122.5t111 -337.5v-576q0 -20 -13.5 -34.5t-35.5 -14.5h-184q-20 0 -35 14.5t-15 34.5v563q0 119 -58 185.5t-167 66.5q-104 0 -167.5 -67.5t-63.5 -184.5v-563 q0 -20 -13.5 -34.5t-35.5 -14.5h-183q-20 0 -34.5 14.5t-14.5 34.5zM178 1247l39 252q4 29 20 47.5t45 18.5h174q14 0 25.5 -11.5t11.5 -27.5q0 -12 -6 -27l-94 -244q-12 -25 -26.5 -39t-41.5 -14h-113q-16 0 -26 13.5t-8 31.5z" />
<glyph unicode="&#x14a;" horiz-adv-x="1447" d="M160 49v1333q0 23 13 37.5t36 14.5h160q39 0 59 -35l586 -910v893q0 23 13 37.5t36 14.5h176q23 0 37 -14.5t14 -37.5v-1427q0 -199 -116.5 -307.5t-342.5 -108.5h-18q-20 0 -34.5 13.5t-14.5 35.5v148q0 20 14.5 34.5t34.5 14.5h39q90 0 126 47t36 131v80l-580 881v-875 q0 -20 -13 -34.5t-36 -14.5h-176q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x14b;" horiz-adv-x="1280" d="M135 49v967q0 23 14.5 36t34.5 13h170q23 0 36 -13.5t13 -35.5v-84q125 154 342 153q188 0 299 -122.5t111 -337.5v-652q0 -209 -115.5 -321.5t-339.5 -112.5h-30q-21 0 -35 14.5t-14 34.5v144q0 20 14 34.5t35 14.5h39q94 0 128.5 49t34.5 152v630q0 119 -58 185.5 t-167 66.5q-104 0 -167.5 -67.5t-63.5 -184.5v-563q0 -20 -13.5 -34.5t-35.5 -14.5h-183q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x14c;" horiz-adv-x="1423" d="M111 715q0 117 2 176q8 272 167.5 417.5t432.5 145.5q270 0 431 -145.5t169 -417.5q4 -119 4 -176q0 -55 -4 -174q-8 -279 -165 -420t-435 -141q-281 0 -436.5 141t-163.5 420q-2 59 -2 174zM387 1581v117q0 20 12 32.5t33 12.5h559q20 0 33.5 -12.5t13.5 -32.5v-117 q0 -20 -13.5 -32.5t-33.5 -12.5h-559q-21 0 -33 12.5t-12 32.5zM408 717q0 -104 2 -166q4 -168 84.5 -251t218.5 -83q137 0 217 82t86 252q4 123 4 166q0 47 -4 166q-6 168 -88 251t-215 83q-135 0 -217 -83t-86 -251q-2 -59 -2 -166z" />
<glyph unicode="&#x14d;" horiz-adv-x="1214" d="M92 532l2 91q10 215 143.5 338.5t370.5 123.5q236 0 369 -123.5t143 -338.5q2 -25 2 -91t-2 -90q-10 -217 -141 -339.5t-371 -122.5q-241 0 -372.5 122.5t-141.5 339.5zM305 1296v107q0 20 12.5 32.5t32.5 12.5h516q20 0 33.5 -12.5t13.5 -32.5v-107q0 -20 -13 -32.5 t-34 -12.5h-516q-20 0 -32.5 12.5t-12.5 32.5zM375 532l2 -79q4 -131 63.5 -199t167.5 -68q109 0 167.5 68t62.5 199q2 20 2 79t-2 80q-4 131 -63.5 200t-166.5 69q-109 0 -168 -69t-63 -200z" />
<glyph unicode="&#x14e;" horiz-adv-x="1423" d="M111 715q0 117 2 176q8 272 167.5 417.5t432.5 145.5q270 0 431 -145.5t169 -417.5q4 -119 4 -176q0 -55 -4 -174q-8 -279 -165 -420t-435 -141q-281 0 -436.5 141t-163.5 420q-2 59 -2 174zM408 717q0 -104 2 -166q4 -168 84.5 -251t218.5 -83q137 0 217 82t86 252 q4 123 4 166q0 47 -4 166q-6 168 -88 251t-215 83q-135 0 -217 -83t-86 -251q-2 -59 -2 -166zM424 1792q0 16 10 27.5t28 11.5h103q18 0 28.5 -11.5t10.5 -27.5q0 -55 24.5 -91t83.5 -36q57 0 82 36t25 91q0 16 10 27.5t29 11.5h102q18 0 28.5 -11.5t10.5 -27.5 q0 -121 -69.5 -196.5t-217.5 -75.5q-149 0 -218.5 75.5t-69.5 196.5z" />
<glyph unicode="&#x14f;" horiz-adv-x="1214" d="M92 532l2 91q10 215 143.5 338.5t370.5 123.5q236 0 369 -123.5t143 -338.5q2 -25 2 -91t-2 -90q-10 -217 -141 -339.5t-371 -122.5q-241 0 -372.5 122.5t-141.5 339.5zM330 1481q0 16 10.5 27.5t28.5 11.5h98q18 0 28.5 -11.5t10.5 -27.5q0 -127 101 -127q102 0 102 127 q0 16 10 27.5t29 11.5h98q18 0 28.5 -11.5t10.5 -27.5q0 -119 -68.5 -196t-209.5 -77q-140 0 -208.5 77t-68.5 196zM375 532l2 -79q4 -131 63.5 -199t167.5 -68q109 0 167.5 68t62.5 199q2 20 2 79t-2 80q-4 131 -63.5 200t-166.5 69q-109 0 -168 -69t-63 -200z" />
<glyph unicode="&#x150;" horiz-adv-x="1423" d="M111 715q0 117 2 176q8 272 167.5 417.5t432.5 145.5q270 0 431 -145.5t169 -417.5q4 -119 4 -176q0 -55 -4 -174q-8 -279 -165 -420t-435 -141q-281 0 -436.5 141t-163.5 420q-2 59 -2 174zM408 717q0 -104 2 -166q4 -168 84.5 -251t218.5 -83q137 0 217 82t86 252 q4 123 4 166q0 47 -4 166q-6 168 -88 251t-215 83q-135 0 -217 -83t-86 -251q-2 -59 -2 -166zM428 1569q0 10 10 30l88 199q10 20 27.5 29.5t48.5 9.5h182q18 0 29.5 -10t11.5 -29q0 -16 -12 -26l-182 -207q-25 -29 -66 -29h-104q-33 0 -33 33zM813 1569q0 10 10 30l90 199 q10 20 28 29.5t48 9.5h180q18 0 29.5 -10t11.5 -29q0 -16 -12 -26l-182 -207q-25 -29 -64 -29h-106q-33 0 -33 33z" />
<glyph unicode="&#x151;" horiz-adv-x="1214" d="M92 532l2 91q10 215 143.5 338.5t370.5 123.5q236 0 369 -123.5t143 -338.5q2 -25 2 -91t-2 -90q-10 -217 -141 -339.5t-371 -122.5q-241 0 -372.5 122.5t-141.5 339.5zM303 1251q0 10 10 31l88 199q10 20 27.5 29.5t48.5 9.5h168q18 0 29.5 -10.5t11.5 -28.5 q0 -14 -12 -27l-183 -207q-25 -29 -65 -28h-90q-33 -1 -33 32zM375 532l2 -79q4 -131 63.5 -199t167.5 -68q109 0 167.5 68t62.5 199q2 20 2 79t-2 80q-4 131 -63.5 200t-166.5 69q-109 0 -168 -69t-63 -200zM692 1251q0 10 10 31l88 199q10 20 28 29.5t48 9.5h168 q18 0 29.5 -10.5t11.5 -28.5q0 -14 -12 -27l-182 -207q-25 -29 -66 -28h-90q-33 -1 -33 32z" />
<glyph unicode="&#x152;" horiz-adv-x="1931" d="M111 717l2 160q16 557 618 557h1036q23 0 36.5 -14.5t13.5 -37.5v-137q0 -20 -13.5 -34.5t-36.5 -14.5h-608v-358h563q23 0 36.5 -14.5t13.5 -37.5v-129q0 -20 -13.5 -34.5t-36.5 -14.5h-563v-370h625q23 0 36 -13.5t13 -36.5v-139q0 -20 -13.5 -34.5t-35.5 -14.5h-1063 q-305 0 -451.5 136t-156.5 423zM403 719q0 -92 3 -154q4 -176 79.5 -251.5t245.5 -75.5h148v958h-138q-172 0 -251.5 -79t-83.5 -247q-2 -59 -3 -151z" />
<glyph unicode="&#x153;" horiz-adv-x="1933" d="M94 532q0 66 2 91q10 215 143.5 338.5t368.5 123.5q125 0 221.5 -43t157.5 -120q131 164 361 163q244 0 375.5 -147t131.5 -393v-43q0 -20 -14 -34.5t-37 -14.5h-680v-17q4 -115 62.5 -189.5t161.5 -74.5q111 0 184 94q18 23 28.5 28t34.5 5h181q18 0 31.5 -10t13.5 -29 q0 -49 -57.5 -115.5t-165 -115.5t-248.5 -49q-117 0 -209 41.5t-156 117.5q-59 -76 -154.5 -117.5t-222.5 -41.5q-241 0 -371.5 123.5t-140.5 338.5q-2 25 -2 90zM375 532l2 -79q6 -131 64.5 -199t166.5 -68q109 0 167.5 68t64.5 199q2 20 2 79t-2 80q-6 131 -65.5 200 t-166.5 69q-106 0 -165.5 -69t-65.5 -200zM1124 627h449v4q0 121 -60.5 193.5t-164.5 72.5t-164 -72.5t-60 -193.5v-4z" />
<glyph unicode="&#x154;" horiz-adv-x="1380" d="M160 49v1333q0 23 13 37.5t36 14.5h530q252 0 394.5 -117t142.5 -332q0 -150 -71.5 -251t-200.5 -146l296 -522q6 -12 7 -23q0 -18 -13.5 -30.5t-29.5 -12.5h-189q-33 0 -51 15.5t-33 39.5l-262 486h-280v-492q0 -20 -14.5 -34.5t-37.5 -14.5h-188q-21 0 -35 14.5 t-14 34.5zM449 774h284q123 0 184.5 54.5t61.5 158.5t-61.5 160.5t-184.5 56.5h-284v-430zM522 1569q0 16 10 26l168 203q18 23 33.5 31t42.5 8h221q18 0 28.5 -11t10.5 -30q0 -14 -10 -24l-256 -209q-18 -14 -34 -20.5t-38 -6.5h-143q-33 0 -33 33z" />
<glyph unicode="&#x155;" horiz-adv-x="858" d="M135 49v965q0 23 14.5 37t34.5 14h168q23 0 37 -14.5t14 -36.5v-84q102 135 295 135h86q23 0 36.5 -13.5t13.5 -35.5v-150q0 -20 -13.5 -34.5t-36.5 -14.5h-161q-96 0 -151.5 -55t-55.5 -152v-561q0 -20 -14.5 -34.5t-34.5 -14.5h-183q-20 0 -34.5 14.5t-14.5 34.5z M342 1251q0 16 10 27l168 203q18 23 33.5 31t42.5 8h207q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-256 -209q-18 -14 -33.5 -20t-37.5 -6h-129q-33 -1 -33 32z" />
<glyph unicode="&#x156;" horiz-adv-x="1380" d="M160 49v1333q0 23 13 37.5t36 14.5h530q252 0 394.5 -117t142.5 -332q0 -150 -71.5 -251t-200.5 -146l296 -522q6 -12 7 -23q0 -18 -13.5 -30.5t-29.5 -12.5h-189q-33 0 -51 15.5t-33 39.5l-262 486h-280v-492q0 -20 -14.5 -34.5t-37.5 -14.5h-188q-21 0 -35 14.5 t-14 34.5zM449 774h284q123 0 184.5 54.5t61.5 158.5t-61.5 160.5t-184.5 56.5h-284v-430zM510 -420l39 252q4 29 20.5 47.5t45.5 18.5h184q14 0 25.5 -11.5t11.5 -27.5q0 -12 -6 -27l-94 -244q-12 -25 -27.5 -39t-42.5 -14h-119q-18 0 -28.5 13.5t-8.5 31.5z" />
<glyph unicode="&#x157;" horiz-adv-x="858" d="M80 -420l39 252q4 29 20 47.5t45 18.5h174q14 0 25.5 -11.5t11.5 -27.5q0 -12 -6 -27l-94 -244q-12 -25 -26.5 -39t-41.5 -14h-113q-16 0 -26 13.5t-8 31.5zM135 49v965q0 23 14.5 37t34.5 14h168q23 0 37 -14.5t14 -36.5v-84q102 135 295 135h86q23 0 36.5 -13.5 t13.5 -35.5v-150q0 -20 -13.5 -34.5t-36.5 -14.5h-161q-96 0 -151.5 -55t-55.5 -152v-561q0 -20 -14.5 -34.5t-34.5 -14.5h-183q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x158;" horiz-adv-x="1380" d="M160 49v1333q0 23 13 37.5t36 14.5h530q252 0 394.5 -117t142.5 -332q0 -150 -71.5 -251t-200.5 -146l296 -522q6 -12 7 -23q0 -18 -13.5 -30.5t-29.5 -12.5h-189q-33 0 -51 15.5t-33 39.5l-262 486h-280v-492q0 -20 -14.5 -34.5t-37.5 -14.5h-188q-21 0 -35 14.5 t-14 34.5zM299 1810q0 27 30 27h82q37 0 64 -14l174 -109l174 109q27 14 63 14h82q29 0 29 -27q0 -18 -19 -34l-198 -207q-20 -20 -34.5 -26.5t-35.5 -6.5h-123q-20 0 -35.5 6t-33.5 27l-201 207q-18 16 -18 34zM449 774h284q123 0 184.5 54.5t61.5 158.5t-61.5 160.5 t-184.5 56.5h-284v-430z" />
<glyph unicode="&#x159;" horiz-adv-x="858" d="M113 1493q0 27 31 27h75q37 0 64 -15l174 -108l174 108q27 14 64 15h77q31 0 31 -27q0 -18 -18 -35l-201 -207q-18 -20 -33.5 -26t-36.5 -6h-114q-21 0 -35 6t-35 26l-199 207q-18 16 -18 35zM135 49v965q0 23 14.5 37t34.5 14h168q23 0 37 -14.5t14 -36.5v-84 q102 135 295 135h86q23 0 36.5 -13.5t13.5 -35.5v-150q0 -20 -13.5 -34.5t-36.5 -14.5h-161q-96 0 -151.5 -55t-55.5 -152v-561q0 -20 -14.5 -34.5t-34.5 -14.5h-183q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x15a;" horiz-adv-x="1316" d="M74 377q0 16 12 29.5t31 13.5h186q23 0 37 -10.5t27 -28.5q20 -70 92.5 -117t199.5 -47q143 0 216 48t73 132q0 55 -37 92t-111.5 65t-221.5 67q-242 57 -354.5 151t-112.5 270q0 119 65.5 212.5t187 146.5t283.5 53q170 0 294 -59.5t188.5 -148.5t68.5 -177 q0 -16 -12 -29.5t-31 -13.5h-194q-45 0 -62 37q-10 66 -79.5 110t-172.5 44q-113 0 -177 -43.5t-64 -124.5q0 -55 32.5 -92t102 -65t202.5 -63q182 -41 290 -92t159 -129t51 -198q0 -133 -72.5 -230.5t-204.5 -148.5t-307 -51q-184 0 -315 55t-198.5 145t-71.5 197z M533 1569q0 16 10 26l168 203q18 23 33.5 31t42.5 8h221q18 0 28.5 -11t10.5 -30q0 -14 -10 -24l-256 -209q-18 -14 -34 -20.5t-38 -6.5h-143q-33 0 -33 33z" />
<glyph unicode="&#x15b;" horiz-adv-x="1091" d="M76 244q0 20 13 32.5t32 12.5h168q16 0 28 -17q10 -8 46 -38.5t82.5 -48t101.5 -17.5q82 0 133 31.5t51 91.5q0 41 -23.5 66.5t-84 47t-181.5 48.5q-174 37 -257 112.5t-83 200.5q0 82 49.5 154.5t146.5 118.5t232 46q139 0 239.5 -44t153 -105.5t52.5 -108.5 q0 -18 -13.5 -31.5t-31.5 -13.5h-154q-23 0 -35 17q-14 10 -45.5 37.5t-70.5 44t-95 16.5q-76 0 -116.5 -33t-40.5 -86q0 -37 19.5 -61.5t78.5 -46t178 -43.5q369 -72 369 -322q0 -145 -126 -235t-351 -90q-156 0 -260.5 47t-154.5 109.5t-50 107.5zM401 1251q0 16 10 27 l168 203q18 23 33.5 31t42.5 8h207q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-256 -209q-18 -14 -33.5 -20t-37.5 -6h-129q-33 -1 -33 32z" />
<glyph unicode="&#x15c;" horiz-adv-x="1316" d="M74 377q0 16 12 29.5t31 13.5h186q23 0 37 -10.5t27 -28.5q20 -70 92.5 -117t199.5 -47q143 0 216 48t73 132q0 55 -37 92t-111.5 65t-221.5 67q-242 57 -354.5 151t-112.5 270q0 119 65.5 212.5t187 146.5t283.5 53q170 0 294 -59.5t188.5 -148.5t68.5 -177 q0 -16 -12 -29.5t-31 -13.5h-194q-45 0 -62 37q-10 66 -79.5 110t-172.5 44q-113 0 -177 -43.5t-64 -124.5q0 -55 32.5 -92t102 -65t202.5 -63q182 -41 290 -92t159 -129t51 -198q0 -133 -72.5 -230.5t-204.5 -148.5t-307 -51q-184 0 -315 55t-198.5 145t-71.5 197z M310 1563q0 16 18 34l201 207q18 20 33.5 26.5t35.5 6.5h123q20 0 34.5 -6t35.5 -27l198 -207q18 -18 19 -34q0 -27 -29 -27h-82q-37 0 -63 14l-174 109l-174 -109q-27 -14 -64 -14h-82q-30 0 -30 27z" />
<glyph unicode="&#x15d;" horiz-adv-x="1091" d="M76 244q0 20 13 32.5t32 12.5h168q16 0 28 -17q10 -8 46 -38.5t82.5 -48t101.5 -17.5q82 0 133 31.5t51 91.5q0 41 -23.5 66.5t-84 47t-181.5 48.5q-174 37 -257 112.5t-83 200.5q0 82 49.5 154.5t146.5 118.5t232 46q139 0 239.5 -44t153 -105.5t52.5 -108.5 q0 -18 -13.5 -31.5t-31.5 -13.5h-154q-23 0 -35 17q-14 10 -45.5 37.5t-70.5 44t-95 16.5q-76 0 -116.5 -33t-40.5 -86q0 -37 19.5 -61.5t78.5 -46t178 -43.5q369 -72 369 -322q0 -145 -126 -235t-351 -90q-156 0 -260.5 47t-154.5 109.5t-50 107.5zM201 1245q0 16 18 35 l199 207q20 20 34.5 26.5t35.5 6.5h114q20 0 36 -6.5t34 -26.5l201 -207q18 -18 18 -35q0 -27 -31 -26h-77q-37 0 -64 14l-174 108l-174 -108q-27 -14 -64 -14h-75q-31 -1 -31 26z" />
<glyph unicode="&#x15e;" horiz-adv-x="1316" d="M74 377q0 16 12 29.5t31 13.5h186q23 0 37 -10.5t27 -28.5q20 -70 92.5 -117t199.5 -47q143 0 216 48t73 132q0 55 -37 92t-111.5 65t-221.5 67q-242 57 -354.5 151t-112.5 270q0 119 65.5 212.5t187 146.5t283.5 53q170 0 294 -59.5t188.5 -148.5t68.5 -177 q0 -16 -12 -29.5t-31 -13.5h-194q-45 0 -62 37q-10 66 -79.5 110t-172.5 44q-113 0 -177 -43.5t-64 -124.5q0 -55 32.5 -92t102 -65t202.5 -63q182 -41 290 -92t159 -129t51 -198q0 -131 -72.5 -228.5t-203.5 -148.5t-303 -53l-43 -89q23 14 69 15q72 0 116 -48t44 -126 t-51 -130.5t-142 -52.5q-69 0 -122.5 29t-53.5 57q0 10 8 19l35 31q10 10 21 10q10 0 40.5 -17.5t67.5 -17.5q33 0 54.5 19.5t21.5 50.5q0 29 -21.5 47t-54.5 18q-20 0 -50 -9t-40 -9q-14 0 -26 12l-37 39q-8 10 -8 22q0 16 12 41l41 97q-147 16 -254 72.5t-161 139.5 t-56 177z" />
<glyph unicode="&#x15f;" horiz-adv-x="1091" d="M76 240q-2 23 12 36t33 13h160q18 0 34 -17q12 -10 48 -39.5t81 -47t103 -17.5q82 0 133 31.5t51 91.5q0 41 -23.5 66.5t-84 47t-181.5 48.5q-174 37 -257 112.5t-83 200.5q0 82 49.5 154.5t146.5 118.5t232 46q139 0 239.5 -44t153 -105.5t52.5 -108.5 q0 -18 -13.5 -31.5t-31.5 -13.5h-156q-20 0 -33 17q-14 10 -45.5 37.5t-70.5 44t-95 16.5q-76 0 -116.5 -33t-40.5 -86q0 -37 19.5 -61.5t78.5 -46t178 -43.5q369 -72 369 -322q0 -143 -124 -233t-349 -92l-41 -89q20 14 69 15q72 0 116 -48t44 -126t-51 -130.5t-141 -52.5 q-70 0 -123 29t-53 57q0 10 8 19l35 31q10 10 20 10q8 0 39 -17.5t70 -17.5q33 0 54 19.5t21 50.5q0 29 -21.5 47t-53.5 18q-20 0 -50 -9t-41 -9q-14 0 -26 12l-37 39q-8 10 -8 22q0 16 12 41l41 97q-115 16 -194.5 61t-117.5 98.5t-40 92.5z" />
<glyph unicode="&#x160;" horiz-adv-x="1316" d="M74 377q0 16 12 29.5t31 13.5h186q23 0 37 -10.5t27 -28.5q20 -70 92.5 -117t199.5 -47q143 0 216 48t73 132q0 55 -37 92t-111.5 65t-221.5 67q-242 57 -354.5 151t-112.5 270q0 119 65.5 212.5t187 146.5t283.5 53q170 0 294 -59.5t188.5 -148.5t68.5 -177 q0 -16 -12 -29.5t-31 -13.5h-194q-45 0 -62 37q-10 66 -79.5 110t-172.5 44q-113 0 -177 -43.5t-64 -124.5q0 -55 32.5 -92t102 -65t202.5 -63q182 -41 290 -92t159 -129t51 -198q0 -133 -72.5 -230.5t-204.5 -148.5t-307 -51q-184 0 -315 55t-198.5 145t-71.5 197z M310 1810q0 27 30 27h82q37 0 64 -14l174 -109l174 109q27 14 63 14h82q29 0 29 -27q0 -18 -19 -34l-198 -207q-20 -20 -34.5 -26.5t-35.5 -6.5h-123q-20 0 -35.5 6t-33.5 27l-201 207q-18 16 -18 34z" />
<glyph unicode="&#x161;" horiz-adv-x="1091" d="M76 244q0 20 13 32.5t32 12.5h168q16 0 28 -17q10 -8 46 -38.5t82.5 -48t101.5 -17.5q82 0 133 31.5t51 91.5q0 41 -23.5 66.5t-84 47t-181.5 48.5q-174 37 -257 112.5t-83 200.5q0 82 49.5 154.5t146.5 118.5t232 46q139 0 239.5 -44t153 -105.5t52.5 -108.5 q0 -18 -13.5 -31.5t-31.5 -13.5h-154q-23 0 -35 17q-14 10 -45.5 37.5t-70.5 44t-95 16.5q-76 0 -116.5 -33t-40.5 -86q0 -37 19.5 -61.5t78.5 -46t178 -43.5q369 -72 369 -322q0 -145 -126 -235t-351 -90q-156 0 -260.5 47t-154.5 109.5t-50 107.5zM201 1493q0 27 31 27h75 q37 0 64 -15l174 -108l174 108q27 14 64 15h77q31 0 31 -27q0 -18 -18 -35l-201 -207q-18 -20 -33.5 -26t-36.5 -6h-114q-21 0 -35 6t-35 26l-199 207q-18 16 -18 35z" />
<glyph unicode="&#x164;" horiz-adv-x="1241" d="M47 1229v153q0 23 13.5 37.5t35.5 14.5h1047q23 0 37 -14.5t14 -37.5v-153q0 -23 -14.5 -37t-36.5 -14h-377v-1129q0 -20 -13.5 -34.5t-35.5 -14.5h-193q-20 0 -34.5 14.5t-14.5 34.5v1129h-379q-20 0 -34.5 14t-14.5 37zM271 1810q0 27 30 27h82q37 0 64 -14l174 -109 l174 109q27 14 63 14h82q29 0 29 -27q0 -18 -19 -34l-198 -207q-20 -20 -34.5 -26.5t-35.5 -6.5h-123q-20 0 -35.5 6t-33.5 27l-201 207q-18 16 -18 34z" />
<glyph unicode="&#x165;" horiz-adv-x="835" d="M33 895v121q0 20 14 34.5t35 14.5h162v340q0 23 14 36t35 13h170q23 0 36 -13.5t13 -35.5v-340h256q20 0 34.5 -14.5t14.5 -34.5v-121q0 -20 -14.5 -34.5t-34.5 -14.5h-256v-440q0 -88 30.5 -133.5t100.5 -45.5h143q23 0 36.5 -13t13.5 -36v-129q0 -20 -13.5 -34.5 t-36.5 -14.5h-168q-186 0 -280 97.5t-94 285.5v463h-162q-20 0 -34.5 14t-14.5 35zM668 1294l39 252q4 29 20 47.5t45 18.5h174q14 0 25.5 -11.5t11.5 -27.5q0 -12 -6 -27l-94 -244q-12 -25 -26.5 -39t-41.5 -14h-113q-16 0 -26 13.5t-8 31.5z" />
<glyph unicode="&#x166;" horiz-adv-x="1259" d="M55 1229v153q0 23 14.5 37.5t36.5 14.5h1047q23 0 37 -14.5t14 -37.5v-153q0 -20 -14 -35.5t-37 -15.5h-377v-361h217q23 0 36 -14.5t13 -36.5v-78q0 -20 -13 -34.5t-36 -14.5h-217v-590q0 -23 -14 -36t-37 -13h-193q-20 0 -34.5 14.5t-14.5 34.5v590h-217 q-20 0 -34.5 14.5t-14.5 34.5v78q0 23 13.5 37t35.5 14h217v361h-377q-20 0 -35.5 15t-15.5 36z" />
<glyph unicode="&#x167;" horiz-adv-x="892" d="M41 639v59q0 23 14.5 36.5t34.5 13.5h160v157h-160q-20 0 -34.5 14.5t-14.5 36.5v60q0 23 14.5 36t34.5 13h160v340q0 23 14.5 36t34.5 13h170q23 0 36 -13.5t13 -35.5v-340h256q23 0 36 -13.5t13 -35.5v-60q0 -23 -14 -37t-35 -14h-256v-157h256q23 0 36 -13.5t13 -36.5 v-59q0 -20 -13 -34.5t-36 -14.5h-256v-184q0 -88 31 -133.5t102 -45.5h142q23 0 36 -13t13 -36v-129q0 -20 -13.5 -34.5t-35.5 -14.5h-166q-187 0 -282 97.5t-95 285.5v207h-160q-20 0 -34.5 14t-14.5 35z" />
<glyph unicode="&#x168;" horiz-adv-x="1462" d="M147 549v833q0 23 13.5 37.5t36.5 14.5h188q23 0 37 -14.5t14 -37.5v-833q0 -162 77 -244t218 -82t218 82t77 244v833q0 23 14.5 37.5t36.5 14.5h189q23 0 36 -14.5t13 -37.5v-833q0 -287 -152.5 -428t-429.5 -141q-278 0 -432 141t-154 428zM408 1571q0 47 22.5 100 t68.5 90t113 37q39 0 67 -10t66 -31q29 -16 46.5 -23.5t37.5 -7.5q18 0 28.5 8.5t23.5 24.5q10 16 18 22.5t23 6.5h94q16 0 26.5 -10.5t10.5 -24.5q0 -47 -23.5 -100.5t-71 -90t-112.5 -36.5q-39 0 -66.5 10t-66.5 31q-29 16 -46.5 23t-37.5 7q-18 0 -28.5 -7t-22.5 -25 q-16 -29 -39 -29h-97q-14 0 -24 10t-10 25z" />
<glyph unicode="&#x169;" horiz-adv-x="1273" d="M125 440v576q0 23 14.5 36t34.5 13h184q20 0 35 -14.5t15 -34.5v-563q0 -252 219 -252q106 0 168.5 67.5t62.5 184.5v563q0 23 14.5 36t34.5 13h183q23 0 36 -13.5t13 -35.5v-967q0 -20 -13.5 -34.5t-35.5 -14.5h-170q-20 0 -35 14.5t-15 34.5v84q-113 -154 -340 -153 q-188 0 -296.5 122.5t-108.5 337.5zM317 1253q0 47 21.5 98.5t66.5 87.5t113 36q39 0 65.5 -10.5t63.5 -30.5q27 -16 43 -23.5t37 -7.5q18 0 29.5 8t21.5 25q10 14 18 21t23 7h94q16 0 26.5 -10t10.5 -24q0 -47 -22.5 -98.5t-68.5 -87.5t-112 -36q-39 0 -65.5 10.5 t-63.5 30.5q-27 16 -43 23.5t-37 7.5q-18 0 -28.5 -8t-22.5 -25q-16 -29 -39 -28h-96q-14 0 -24.5 10t-10.5 24z" />
<glyph unicode="&#x16a;" horiz-adv-x="1462" d="M147 549v833q0 23 13.5 37.5t36.5 14.5h188q23 0 37 -14.5t14 -37.5v-833q0 -162 77 -244t218 -82t218 82t77 244v833q0 23 14.5 37.5t36.5 14.5h189q23 0 36 -14.5t13 -37.5v-833q0 -287 -152.5 -428t-429.5 -141q-278 0 -432 141t-154 428zM406 1581v117q0 20 12 32.5 t33 12.5h559q20 0 33.5 -12.5t13.5 -32.5v-117q0 -20 -13.5 -32.5t-33.5 -12.5h-559q-21 0 -33 12.5t-12 32.5z" />
<glyph unicode="&#x16b;" horiz-adv-x="1273" d="M125 440v576q0 23 14.5 36t34.5 13h184q20 0 35 -14.5t15 -34.5v-563q0 -252 219 -252q106 0 168.5 67.5t62.5 184.5v563q0 23 14.5 36t34.5 13h183q23 0 36 -13.5t13 -35.5v-967q0 -20 -13.5 -34.5t-35.5 -14.5h-170q-20 0 -35 14.5t-15 34.5v84q-113 -154 -340 -153 q-188 0 -296.5 122.5t-108.5 337.5zM330 1296v107q0 20 12.5 32.5t32.5 12.5h516q20 0 33.5 -12.5t13.5 -32.5v-107q0 -20 -13 -32.5t-34 -12.5h-516q-20 0 -32.5 12.5t-12.5 32.5z" />
<glyph unicode="&#x16c;" horiz-adv-x="1462" d="M147 549v833q0 23 13.5 37.5t36.5 14.5h188q23 0 37 -14.5t14 -37.5v-833q0 -162 77 -244t218 -82t218 82t77 244v833q0 23 14.5 37.5t36.5 14.5h189q23 0 36 -14.5t13 -37.5v-833q0 -287 -152.5 -428t-429.5 -141q-278 0 -432 141t-154 428zM443 1792q0 16 10 27.5 t28 11.5h103q18 0 28.5 -11.5t10.5 -27.5q0 -55 24.5 -91t83.5 -36q57 0 82 36t25 91q0 16 10 27.5t29 11.5h102q18 0 28.5 -11.5t10.5 -27.5q0 -121 -69.5 -196.5t-217.5 -75.5q-149 0 -218.5 75.5t-69.5 196.5z" />
<glyph unicode="&#x16d;" horiz-adv-x="1273" d="M125 440v576q0 23 14.5 36t34.5 13h184q20 0 35 -14.5t15 -34.5v-563q0 -252 219 -252q106 0 168.5 67.5t62.5 184.5v563q0 23 14.5 36t34.5 13h183q23 0 36 -13.5t13 -35.5v-967q0 -20 -13.5 -34.5t-35.5 -14.5h-170q-20 0 -35 14.5t-15 34.5v84q-113 -154 -340 -153 q-188 0 -296.5 122.5t-108.5 337.5zM354 1481q0 16 10.5 27.5t28.5 11.5h98q18 0 28.5 -11.5t10.5 -27.5q0 -127 101 -127q102 0 102 127q0 16 10 27.5t29 11.5h98q18 0 28.5 -11.5t10.5 -27.5q0 -119 -68.5 -196t-209.5 -77q-140 0 -208.5 77t-68.5 196z" />
<glyph unicode="&#x16e;" horiz-adv-x="1462" d="M147 549v833q0 23 13.5 37.5t36.5 14.5h188q23 0 37 -14.5t14 -37.5v-833q0 -162 77 -244t218 -82t218 82t77 244v833q0 23 14.5 37.5t36.5 14.5h189q23 0 36 -14.5t13 -37.5v-833q0 -287 -152.5 -428t-429.5 -141q-278 0 -432 141t-154 428zM551 1686q0 74 52 123 t128 49t128 -49.5t52 -122.5q0 -74 -52 -122t-128 -48t-128 48t-52 122zM669 1686q0 -27 17.5 -44.5t44.5 -17.5q26 0 43.5 17.5t17.5 44.5t-17.5 44t-43.5 17q-27 0 -44.5 -17.5t-17.5 -43.5z" />
<glyph unicode="&#x16f;" horiz-adv-x="1273" d="M125 440v576q0 23 14.5 36t34.5 13h184q20 0 35 -14.5t15 -34.5v-563q0 -252 219 -252q106 0 168.5 67.5t62.5 184.5v563q0 23 14.5 36t34.5 13h183q23 0 36 -13.5t13 -35.5v-967q0 -20 -13.5 -34.5t-35.5 -14.5h-170q-20 0 -35 14.5t-15 34.5v84q-113 -154 -340 -153 q-188 0 -296.5 122.5t-108.5 337.5zM451 1378q0 74 52 123t128 49t128 -49t52 -123t-52 -122t-128 -48t-128 48.5t-52 121.5zM570 1378q0 -27 17.5 -44t43.5 -17q27 0 44.5 17.5t17.5 43.5q0 27 -17.5 44.5t-44.5 17.5t-44 -17.5t-17 -44.5z" />
<glyph unicode="&#x170;" horiz-adv-x="1462" d="M147 549v833q0 23 13.5 37.5t36.5 14.5h188q23 0 37 -14.5t14 -37.5v-833q0 -162 77 -244t218 -82t218 82t77 244v833q0 23 14.5 37.5t36.5 14.5h189q23 0 36 -14.5t13 -37.5v-833q0 -287 -152.5 -428t-429.5 -141q-278 0 -432 141t-154 428zM446 1569q0 10 10 30l88 199 q10 20 27.5 29.5t48.5 9.5h182q18 0 29.5 -10t11.5 -29q0 -16 -12 -26l-182 -207q-25 -29 -66 -29h-104q-33 0 -33 33zM831 1569q0 10 10 30l90 199q10 20 28 29.5t48 9.5h180q18 0 29.5 -10t11.5 -29q0 -16 -12 -26l-182 -207q-25 -29 -64 -29h-106q-33 0 -33 33z" />
<glyph unicode="&#x171;" horiz-adv-x="1273" d="M125 440v576q0 23 14.5 36t34.5 13h184q20 0 35 -14.5t15 -34.5v-563q0 -252 219 -252q106 0 168.5 67.5t62.5 184.5v563q0 23 14.5 36t34.5 13h183q23 0 36 -13.5t13 -35.5v-967q0 -20 -13.5 -34.5t-35.5 -14.5h-170q-20 0 -35 14.5t-15 34.5v84q-113 -154 -340 -153 q-188 0 -296.5 122.5t-108.5 337.5zM322 1251q0 10 10 31l88 199q10 20 27.5 29.5t48.5 9.5h168q18 0 29.5 -10.5t11.5 -28.5q0 -14 -12 -27l-183 -207q-25 -29 -65 -28h-90q-33 -1 -33 32zM711 1251q0 10 10 31l88 199q10 20 28 29.5t48 9.5h168q18 0 29.5 -10.5 t11.5 -28.5q0 -14 -12 -27l-182 -207q-25 -29 -66 -28h-90q-33 -1 -33 32z" />
<glyph unicode="&#x172;" horiz-adv-x="1462" d="M147 549v833q0 23 13.5 37.5t36.5 14.5h188q23 0 37 -14.5t14 -37.5v-833q0 -162 77 -244t218 -82t218 82t77 244v833q0 23 14.5 37.5t36.5 14.5h189q23 0 36 -14.5t13 -37.5v-833q0 -246 -115 -384t-326 -175q-61 -12 -90.5 -43t-29.5 -86q0 -61 34.5 -100.5t98.5 -39.5 h26q23 0 36.5 -14t13.5 -35v-73q0 -23 -13.5 -36.5t-36.5 -13.5h-34q-148 0 -227 85t-79 227q0 74 23 129q-215 31 -332 170t-117 389z" />
<glyph unicode="&#x173;" horiz-adv-x="1273" d="M125 440v576q0 23 14.5 36t34.5 13h184q20 0 35 -14.5t15 -34.5v-563q0 -252 219 -252q106 0 168.5 67.5t62.5 184.5v563q0 23 14.5 36t34.5 13h183q23 0 36 -13.5t13 -35.5v-967q0 -18 -11.5 -31.5t-29.5 -17.5l-19 -2q-115 -35 -114 -148q0 -59 28.5 -94t81.5 -35h15 q23 0 36 -14t13 -35v-73q0 -23 -13.5 -36.5t-35.5 -13.5h-23q-131 0 -206 82t-75 219q0 61 31 110.5t72 80.5q-18 10 -19 33v59q-113 -154 -340 -153q-188 0 -296.5 122.5t-108.5 337.5z" />
<glyph unicode="&#x174;" horiz-adv-x="1671" d="M78 1391q0 18 12 30.5t31 12.5h184q27 0 39 -9.5t16 -31.5l170 -922l183 594q20 51 67 51h113q27 0 43 -15.5t22 -35.5l185 -594l168 922q6 41 57 41h184q16 0 28.5 -12.5t12.5 -30.5q0 -10 -2 -17l-241 -1313q-4 -29 -23.5 -45t-50.5 -16h-139q-29 0 -47.5 15.5 t-24.5 35.5l-229 680l-230 -680q-16 -51 -71 -51h-140q-59 0 -73 61l-242 1313zM486 1563q0 16 18 34l201 207q18 20 33.5 26.5t35.5 6.5h123q20 0 34.5 -6t35.5 -27l198 -207q18 -18 19 -34q0 -27 -29 -27h-82q-37 0 -63 14l-174 109l-174 -109q-27 -14 -64 -14h-82 q-30 0 -30 27z" />
<glyph unicode="&#x175;" horiz-adv-x="1681" d="M63 1020q0 18 12.5 31.5t30.5 13.5h156q20 0 35.5 -12.5t19.5 -26.5l199 -674l211 668q6 18 21.5 31.5t39.5 13.5h105q25 0 40 -13.5t19 -31.5l213 -668l199 674q4 14 18.5 26.5t36.5 12.5h154q18 0 31.5 -13.5t13.5 -31.5l-4 -19l-293 -950q-8 -25 -23.5 -38t-42.5 -13 h-135q-53 0 -67 51l-211 643l-213 -643q-16 -51 -70 -51h-133q-52 0 -68 51l-292 950q-2 6 -3 19zM496 1245q0 16 18 35l199 207q20 20 34.5 26.5t35.5 6.5h114q20 0 36 -6.5t34 -26.5l201 -207q18 -18 18 -35q0 -27 -31 -26h-77q-37 0 -64 14l-174 108l-174 -108 q-27 -14 -64 -14h-75q-31 -1 -31 26z" />
<glyph unicode="&#x176;" horiz-adv-x="1361" d="M45 1391q0 18 13.5 30.5t29.5 12.5h184q39 0 64 -39l344 -594l346 594q8 14 23.5 26.5t37.5 12.5h185q18 0 30.5 -12.5t12.5 -30.5q0 -12 -6 -23l-484 -858v-461q0 -23 -14 -36t-37 -13h-188q-21 0 -35 14.5t-14 34.5v461l-486 858q-6 18 -6 23zM330 1563q0 16 18 34 l201 207q18 20 33.5 26.5t35.5 6.5h123q20 0 34.5 -6t35.5 -27l198 -207q18 -18 19 -34q0 -27 -29 -27h-82q-37 0 -63 14l-174 109l-174 -109q-27 -14 -64 -14h-82q-30 0 -30 27z" />
<glyph unicode="&#x177;" horiz-adv-x="1177" d="M53 1020q2 18 14.5 31.5t30.5 13.5h170q37 0 54 -39l272 -668l276 668q20 39 58 39h166q18 0 30.5 -12.5t12.5 -28.5q0 -14 -9 -33l-579 -1341q-16 -39 -57 -39h-164q-17 0 -30 12t-13 29q0 14 8 33l162 378l-394 928q-8 18 -8 29zM248 1245q0 16 18 35l199 207 q20 20 34.5 26.5t35.5 6.5h114q20 0 36 -6.5t34 -26.5l201 -207q18 -18 18 -35q0 -27 -31 -26h-77q-37 0 -64 14l-174 108l-174 -108q-27 -14 -64 -14h-75q-31 -1 -31 26z" />
<glyph unicode="&#x178;" horiz-adv-x="1361" d="M45 1391q0 18 13.5 30.5t29.5 12.5h184q39 0 64 -39l344 -594l346 594q8 14 23.5 26.5t37.5 12.5h185q18 0 30.5 -12.5t12.5 -30.5q0 -12 -6 -23l-484 -858v-461q0 -23 -14 -36t-37 -13h-188q-21 0 -35 14.5t-14 34.5v461l-486 858q-6 18 -6 23zM334 1581v152 q0 20 12 33.5t33 13.5h151q20 0 34 -13.5t14 -33.5v-152q0 -20 -13.5 -32.5t-34.5 -12.5h-151q-21 0 -33 12.5t-12 32.5zM782 1581v152q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13.5t13.5 -33.5v-152q0 -20 -13.5 -32.5t-33.5 -12.5h-152q-20 0 -32.5 12.5t-12.5 32.5z " />
<glyph unicode="&#x179;" horiz-adv-x="1284" d="M80 51v146q0 27 9 43t22 32l710 916h-671q-20 0 -35 14.5t-15 36.5v143q0 23 13.5 37.5t36.5 14.5h980q23 0 37.5 -14.5t14.5 -37.5v-143q0 -41 -27 -72l-694 -921h694q23 0 36 -13.5t13 -35.5v-148q0 -20 -13 -34.5t-36 -14.5h-1026q-23 0 -36 14.5t-13 36.5zM496 1569 q0 16 10 26l168 203q18 23 33.5 31t42.5 8h221q18 0 28.5 -11t10.5 -30q0 -14 -10 -24l-256 -209q-18 -14 -34 -20.5t-38 -6.5h-143q-33 0 -33 33z" />
<glyph unicode="&#x17a;" horiz-adv-x="1089" d="M82 49v119q0 18 7 32.5t22 35.5l522 612h-486q-20 0 -34.5 15.5t-14.5 35.5v117q0 20 14.5 34.5t34.5 14.5h781q20 0 34.5 -14.5t14.5 -34.5v-127q0 -16 -9.5 -32.5t-19.5 -31.5l-512 -608h533q20 0 34.5 -14.5t14.5 -36.5v-117q0 -20 -13.5 -34.5t-35.5 -14.5h-838 q-20 0 -34.5 14.5t-14.5 34.5zM428 1251q0 16 10 27l168 203q18 23 33.5 31t42.5 8h207q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-256 -209q-18 -14 -33.5 -20t-37.5 -6h-129q-33 -1 -33 32z" />
<glyph unicode="&#x17b;" horiz-adv-x="1284" d="M80 51v146q0 27 9 43t22 32l710 916h-671q-20 0 -35 14.5t-15 36.5v143q0 23 13.5 37.5t36.5 14.5h980q23 0 37.5 -14.5t14.5 -37.5v-143q0 -41 -27 -72l-694 -921h694q23 0 36 -13.5t13 -35.5v-148q0 -20 -13 -34.5t-36 -14.5h-1026q-23 0 -36 14.5t-13 36.5zM490 1581 v174q0 20 12 33.5t33 13.5h174q20 0 33.5 -13t13.5 -34v-174q0 -20 -13.5 -32.5t-33.5 -12.5h-174q-21 0 -33 12.5t-12 32.5z" />
<glyph unicode="&#x17c;" horiz-adv-x="1089" d="M82 49v119q0 18 7 32.5t22 35.5l522 612h-486q-20 0 -34.5 15.5t-14.5 35.5v117q0 20 14.5 34.5t34.5 14.5h781q20 0 34.5 -14.5t14.5 -34.5v-127q0 -16 -9.5 -32.5t-19.5 -31.5l-512 -608h533q20 0 34.5 -14.5t14.5 -36.5v-117q0 -20 -13.5 -34.5t-35.5 -14.5h-838 q-20 0 -34.5 14.5t-14.5 34.5zM420 1290v170q0 20 12.5 33.5t32.5 13.5h170q20 0 33.5 -13t13.5 -34v-170q0 -20 -13.5 -32.5t-33.5 -12.5h-170q-20 0 -32.5 12.5t-12.5 32.5z" />
<glyph unicode="&#x17d;" horiz-adv-x="1284" d="M80 51v146q0 27 9 43t22 32l710 916h-671q-20 0 -35 14.5t-15 36.5v143q0 23 13.5 37.5t36.5 14.5h980q23 0 37.5 -14.5t14.5 -37.5v-143q0 -41 -27 -72l-694 -921h694q23 0 36 -13.5t13 -35.5v-148q0 -20 -13 -34.5t-36 -14.5h-1026q-23 0 -36 14.5t-13 36.5zM273 1810 q0 27 30 27h82q37 0 64 -14l174 -109l174 109q27 14 63 14h82q29 0 29 -27q0 -18 -19 -34l-198 -207q-20 -20 -34.5 -26.5t-35.5 -6.5h-123q-20 0 -35.5 6t-33.5 27l-201 207q-18 16 -18 34z" />
<glyph unicode="&#x17e;" horiz-adv-x="1089" d="M82 49v119q0 18 7 32.5t22 35.5l522 612h-486q-20 0 -34.5 15.5t-14.5 35.5v117q0 20 14.5 34.5t34.5 14.5h781q20 0 34.5 -14.5t14.5 -34.5v-127q0 -16 -9.5 -32.5t-19.5 -31.5l-512 -608h533q20 0 34.5 -14.5t14.5 -36.5v-117q0 -20 -13.5 -34.5t-35.5 -14.5h-838 q-20 0 -34.5 14.5t-14.5 34.5zM205 1493q0 27 31 27h75q37 0 64 -15l174 -108l174 108q27 14 64 15h77q31 0 31 -27q0 -18 -18 -35l-201 -207q-18 -20 -33.5 -26t-36.5 -6h-114q-21 0 -35 6t-35 26l-199 207q-18 16 -18 35z" />
<glyph unicode="&#x17f;" horiz-adv-x="655" d="M37 895v121q0 23 14.5 36t34.5 13h166v76q0 190 99.5 282.5t301.5 92.5h142q23 0 36 -13.5t13 -36.5v-120q0 -20 -14.5 -35t-34.5 -15h-131q-86 0 -115 -39.5t-29 -119.5v-1088q0 -20 -14 -34.5t-37 -14.5h-168q-20 0 -34.5 14.5t-14.5 34.5v797h-166q-20 0 -34.5 14 t-14.5 35z" />
<glyph unicode="&#x192;" horiz-adv-x="917" d="M20 -268q0 20 14.5 34.5t37.5 14.5h41q96 0 131 49t35 152v864h-166q-20 0 -35 14t-15 35v121q0 23 14.5 36t35.5 13h166v82q0 369 401 369h141q23 0 36 -13.5t13 -36.5v-120q0 -20 -14 -35t-35 -15h-131q-80 0 -111.5 -38.5t-31.5 -120.5v-72h254q23 0 36 -13.5 t13 -35.5v-121q0 -20 -13.5 -34.5t-35.5 -14.5h-254v-873q0 -213 -103.5 -323.5t-330.5 -110.5h-41q-23 0 -37.5 14.5t-14.5 34.5v144z" />
<glyph unicode="&#x1fc;" horiz-adv-x="1955" d="M10 43q0 6 4 23q61 176 180 433t449 883q20 51 70 52h1079q23 0 37 -14.5t14 -37.5v-137q0 -23 -14 -36t-37 -13h-647v-358h602q23 0 37 -14.5t14 -37.5v-129q0 -23 -14.5 -36t-36.5 -13h-602v-370h663q23 0 37.5 -13.5t14.5 -36.5v-139q0 -23 -14.5 -36t-37.5 -13h-895 q-20 0 -34.5 14.5t-14.5 34.5v238h-438q-117 -211 -129 -248q-18 -39 -59 -39h-187q-16 0 -28.5 12.5t-12.5 30.5zM510 524h354v664h-26zM852 1569q0 16 10 26l168 203q18 23 33.5 31t42.5 8h221q18 0 28.5 -11t10.5 -30q0 -14 -10 -24l-256 -209q-18 -14 -34 -20.5 t-38 -6.5h-143q-33 0 -33 33z" />
<glyph unicode="&#x1fd;" horiz-adv-x="1851" d="M66 293q0 139 113.5 227t309.5 119l279 39v41q0 88 -44 133t-144 45q-68 0 -109 -24.5t-66.5 -50t-33.5 -34.5q-8 -25 -31 -24h-160q-18 0 -31.5 12t-13.5 33q0 49 49 114.5t150.5 113.5t247.5 48q252 0 346 -137q131 137 340 137q182 0 293.5 -79.5t158.5 -201.5 t47 -259v-43q0 -20 -14 -34.5t-37 -14.5h-667v-17q4 -133 65.5 -198.5t151.5 -65.5q57 0 106 23.5t84 70.5q16 20 28.5 26.5t35.5 6.5h176q18 0 30.5 -11.5t12.5 -31.5q0 -45 -55.5 -111.5t-163 -115.5t-252.5 -49q-260 0 -394 184q-135 -184 -411 -184q-119 0 -209 40.5 t-139 112.5t-49 160zM332 313q0 -66 53 -101.5t131 -35.5q113 0 184.5 74t71.5 213v41l-205 -33q-236 -37 -235 -158zM809 1251q0 16 10 27l168 203q18 23 33.5 31t42.5 8h207q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-256 -209q-18 -14 -33.5 -20t-37.5 -6h-129 q-33 -1 -33 32zM1049 627h436v8q0 121 -55.5 191.5t-161.5 70.5q-90 0 -152.5 -67.5t-66.5 -194.5v-8z" />
<glyph unicode="&#x1fe;" horiz-adv-x="1480" d="M23 401v115q0 25 10 37t35 22l73 27l-2 119q0 51 4 170q8 272 169 417.5t431 145.5q221 0 370 -100.5t200 -292.5l80 31q16 6 24 6q18 0 29.5 -10.5t11.5 -28.5v-117q0 -25 -10 -36t-35 -21l-67 -25l2 -135q0 -61 -5 -184q-8 -279 -163.5 -420t-436.5 -141 q-239 0 -386.5 103t-190.5 314l-78 -28q-16 -6 -25 -7q-18 0 -29 10.5t-11 28.5zM438 721l600 231q-18 133 -96 199t-199 66q-135 0 -216 -83t-87 -251q-4 -115 -2 -162zM442 506q16 -147 95 -218t206 -71q138 0 218.5 83t85.5 251q4 135 2 192zM613 1569q0 16 10 26 l168 203q18 23 33.5 31t42.5 8h221q18 0 28.5 -11t10.5 -30q0 -14 -10 -24l-256 -209q-18 -14 -34 -20.5t-38 -6.5h-143q-33 0 -33 33z" />
<glyph unicode="&#x1ff;" horiz-adv-x="1277" d="M25 264v80q0 25 10 37t35 22l59 23q-4 78 -4 117l2 80q10 205 139 333.5t375 128.5q184 0 300 -74.5t169 -203.5l78 31q16 6 24 6q18 0 29.5 -11.5t11.5 -29.5v-80q0 -25 -10 -36t-35 -21l-57 -23q4 -31 4 -113q0 -63 -2 -88q-10 -207 -136 -334.5t-376 -127.5 q-188 0 -306 74.5t-169 205.5l-76 -29q-16 -6 -24 -6q-18 0 -29.5 10.5t-11.5 28.5zM410 535l450 174q-20 88 -78.5 130t-140.5 42q-102 0 -163.5 -61.5t-67.5 -190.5v-94zM420 358q41 -172 221 -172q219 0 231 260v89zM510 1251q0 16 10 27l168 203q18 23 33.5 31t42.5 8 h207q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-256 -209q-18 -14 -33.5 -20t-37.5 -6h-129q-33 -1 -33 32z" />
<glyph unicode="&#x218;" horiz-adv-x="1316" d="M74 377q0 16 12 29.5t31 13.5h186q23 0 37 -10.5t27 -28.5q20 -70 92.5 -117t199.5 -47q143 0 216 48t73 132q0 55 -37 92t-111.5 65t-221.5 67q-242 57 -354.5 151t-112.5 270q0 119 65.5 212.5t187 146.5t283.5 53q170 0 294 -59.5t188.5 -148.5t68.5 -177 q0 -16 -12 -29.5t-31 -13.5h-194q-45 0 -62 37q-10 66 -79.5 110t-172.5 44q-113 0 -177 -43.5t-64 -124.5q0 -55 32.5 -92t102 -65t202.5 -63q182 -41 290 -92t159 -129t51 -198q0 -133 -72.5 -230.5t-204.5 -148.5t-307 -51q-184 0 -315 55t-198.5 145t-71.5 197z M469 -420l39 252q4 29 20.5 47.5t45.5 18.5h184q14 0 25.5 -11.5t11.5 -27.5q0 -12 -6 -27l-94 -244q-12 -25 -27.5 -39t-42.5 -14h-119q-18 0 -28.5 13.5t-8.5 31.5z" />
<glyph unicode="&#x219;" horiz-adv-x="1091" d="M76 244q0 20 13 32.5t32 12.5h168q16 0 28 -17q10 -8 46 -38.5t82.5 -48t101.5 -17.5q82 0 133 31.5t51 91.5q0 41 -23.5 66.5t-84 47t-181.5 48.5q-174 37 -257 112.5t-83 200.5q0 82 49.5 154.5t146.5 118.5t232 46q139 0 239.5 -44t153 -105.5t52.5 -108.5 q0 -18 -13.5 -31.5t-31.5 -13.5h-154q-23 0 -35 17q-14 10 -45.5 37.5t-70.5 44t-95 16.5q-76 0 -116.5 -33t-40.5 -86q0 -37 19.5 -61.5t78.5 -46t178 -43.5q369 -72 369 -322q0 -145 -126 -235t-351 -90q-156 0 -260.5 47t-154.5 109.5t-50 107.5zM363 -420l39 252 q4 29 20 47.5t45 18.5h174q14 0 25.5 -11.5t11.5 -27.5q0 -12 -6 -27l-94 -244q-12 -25 -26.5 -39t-41.5 -14h-113q-16 0 -26 13.5t-8 31.5z" />
<glyph unicode="&#x21a;" horiz-adv-x="1241" d="M47 1229v153q0 23 13.5 37.5t35.5 14.5h1047q23 0 37 -14.5t14 -37.5v-153q0 -23 -14.5 -37t-36.5 -14h-377v-1129q0 -20 -13.5 -34.5t-35.5 -14.5h-193q-20 0 -34.5 14.5t-14.5 34.5v1129h-379q-20 0 -34.5 14t-14.5 37zM428 -420l39 252q4 29 20.5 47.5t45.5 18.5h184 q14 0 25.5 -11.5t11.5 -27.5q0 -12 -6 -27l-94 -244q-12 -25 -27.5 -39t-42.5 -14h-119q-18 0 -28.5 13.5t-8.5 31.5z" />
<glyph unicode="&#x21b;" horiz-adv-x="882" d="M33 895v121q0 20 14 34.5t35 14.5h162v340q0 23 14 36t35 13h170q23 0 36 -13.5t13 -35.5v-340h256q20 0 34.5 -14.5t14.5 -34.5v-121q0 -20 -14.5 -34.5t-34.5 -14.5h-256v-440q0 -88 30.5 -133.5t100.5 -45.5h143q23 0 36.5 -13t13.5 -36v-129q0 -20 -13.5 -34.5 t-36.5 -14.5h-168q-186 0 -280 97.5t-94 285.5v463h-162q-20 0 -34.5 14t-14.5 35zM254 -420l39 252q4 29 20 47.5t45 18.5h174q14 0 25.5 -11.5t11.5 -27.5q0 -12 -6 -27l-94 -244q-12 -25 -26.5 -39t-41.5 -14h-113q-16 0 -26 13.5t-8 31.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="942" d="M127 1245q0 16 18 35l199 207q20 20 34.5 26.5t35.5 6.5h114q20 0 36 -6.5t34 -26.5l201 -207q18 -18 18 -35q0 -27 -31 -26h-77q-37 0 -64 14l-174 108l-174 -108q-27 -14 -64 -14h-75q-31 -1 -31 26z" />
<glyph unicode="&#x2da;" horiz-adv-x="614" d="M127 1378q0 74 52 123t128 49t128 -49t52 -123t-52 -122t-128 -48t-128 48.5t-52 121.5zM246 1378q0 -27 17.5 -44t43.5 -17q27 0 44.5 17.5t17.5 43.5q0 27 -17.5 44.5t-44.5 17.5t-44 -17.5t-17 -44.5z" />
<glyph unicode="&#x2dc;" horiz-adv-x="884" d="M127 1253q0 47 21.5 98.5t66.5 87.5t113 36q39 0 65.5 -10.5t63.5 -30.5q27 -16 43 -23.5t37 -7.5q18 0 29.5 8t21.5 25q10 14 18 21t23 7h94q16 0 26.5 -10t10.5 -24q0 -47 -22.5 -98.5t-68.5 -87.5t-112 -36q-39 0 -65.5 10.5t-63.5 30.5q-27 16 -43 23.5t-37 7.5 q-18 0 -28.5 -8t-22.5 -25q-16 -29 -39 -28h-96q-14 0 -24.5 10t-10.5 24z" />
<glyph unicode="&#x400;" horiz-adv-x="1275" d="M160 49v1333q0 23 13 37.5t36 14.5h903q23 0 37 -14.5t14 -37.5v-137q0 -23 -14 -36t-37 -13h-672v-358h627q23 0 37 -14.5t14 -37.5v-129q0 -23 -14 -36t-37 -13h-627v-370h688q23 0 37.5 -13.5t14.5 -36.5v-139q0 -23 -14.5 -36t-37.5 -13h-919q-21 0 -35 14.5 t-14 34.5zM301 1796q0 18 10.5 29.5t28.5 11.5h223q27 0 41.5 -8t34.5 -31l166 -203q10 -10 10 -26q0 -33 -33 -33h-143q-22 0 -37.5 6t-34.5 21l-256 209q-10 10 -10 24z" />
<glyph unicode="&#x401;" horiz-adv-x="1275" d="M160 49v1333q0 23 13 37.5t36 14.5h903q23 0 37 -14.5t14 -37.5v-137q0 -23 -14 -36t-37 -13h-672v-358h627q23 0 37 -14.5t14 -37.5v-129q0 -23 -14 -36t-37 -13h-627v-370h688q23 0 37.5 -13.5t14.5 -36.5v-139q0 -23 -14.5 -36t-37.5 -13h-919q-21 0 -35 14.5 t-14 34.5zM364 1672v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM751 1672v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152 q-20 0 -32.5 12.5t-12.5 33.5z" />
<glyph unicode="&#x402;" horiz-adv-x="1523" d="M47 1257v129q0 20 12.5 34t32.5 14h1002q23 0 35 -12.5t12 -35.5v-129q0 -20 -13.5 -33.5t-33.5 -13.5h-449v-350q43 49 140.5 76t197.5 27q467 0 467 -449v-68q0 -213 -138 -329.5t-409 -116.5h-22q-20 0 -35 14.5t-15 34.5v127q0 23 14.5 36t35.5 13h22 q141 0 199.5 58.5t58.5 162.5v43q0 106 -58.5 156.5t-182.5 50.5q-115 0 -195 -39t-80 -131v-477q0 -20 -13.5 -34.5t-35.5 -14.5h-188q-21 0 -35.5 14.5t-14.5 34.5v1161h-266q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x403;" horiz-adv-x="1114" d="M160 49v1333q0 23 13 37.5t36 14.5h838q23 0 37 -14.5t14 -37.5v-145q0 -23 -14.5 -36t-36.5 -13h-598v-1139q0 -20 -14.5 -34.5t-37.5 -14.5h-188q-21 0 -35 14.5t-14 34.5zM549 1569q0 16 10 26l168 203q18 23 33.5 31t42.5 8h221q18 0 28.5 -11t10.5 -30 q0 -14 -10 -24l-256 -209q-18 -14 -34 -20.5t-38 -6.5h-143q-33 0 -33 33z" />
<glyph unicode="&#x404;" horiz-adv-x="1361" d="M102 717l2 141q10 303 168 449.5t424 146.5q240 0 393.5 -101.5t174.5 -279.5v-4q0 -16 -13.5 -27.5t-31.5 -11.5h-193q-23 0 -33 8.5t-20 30.5q-20 66 -88 107t-183 41q-301 0 -301 -353v-35h447q20 0 34.5 -15t14.5 -38v-119q0 -23 -14.5 -37t-34.5 -14h-447v-39 q0 -166 79 -259t222 -93q115 0 183.5 41t91.5 106q10 23 21.5 31t33.5 8h191q18 0 32.5 -12t12.5 -31q-20 -178 -178 -278t-394 -100q-268 0 -424.5 145t-167.5 450z" />
<glyph unicode="&#x405;" horiz-adv-x="1316" d="M74 377q0 16 12 29.5t31 13.5h186q23 0 37 -10.5t27 -28.5q20 -70 92.5 -117t199.5 -47q143 0 216 48t73 132q0 55 -37 92t-111.5 65t-221.5 67q-242 57 -354.5 151t-112.5 270q0 119 65.5 212.5t187 146.5t283.5 53q170 0 294 -59.5t188.5 -148.5t68.5 -177 q0 -16 -12 -29.5t-31 -13.5h-194q-45 0 -62 37q-10 66 -79.5 110t-172.5 44q-113 0 -177 -43.5t-64 -124.5q0 -55 32.5 -92t102 -65t202.5 -63q182 -41 290 -92t159 -129t51 -198q0 -133 -72.5 -230.5t-204.5 -148.5t-307 -51q-184 0 -315 55t-198.5 145t-71.5 197z" />
<glyph unicode="&#x406;" horiz-adv-x="614" d="M160 49v1335q0 23 14 36.5t35 13.5h197q23 0 36 -13.5t13 -36.5v-1335q0 -20 -13.5 -34.5t-35.5 -14.5h-197q-21 0 -35 14.5t-14 34.5z" />
<glyph unicode="&#x407;" horiz-adv-x="614" d="M-8 1672v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM160 49v1335q0 23 14 36.5t35 13.5h197q23 0 36 -13.5t13 -36.5v-1335q0 -20 -13.5 -34.5t-35.5 -14.5h-197q-21 0 -35 14.5 t-14 34.5zM379 1672v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5z" />
<glyph unicode="&#x408;" horiz-adv-x="1337" d="M61 432q0 18 12.5 31.5t32.5 13.5h199q47 0 60 -51q16 -106 86.5 -157.5t179.5 -51.5q127 0 195.5 83t68.5 232v656h-694q-20 0 -34.5 14.5t-14.5 36.5v145q0 23 14 36.5t35 13.5h940q23 0 36 -14.5t13 -37.5v-858q0 -170 -69.5 -292.5t-197.5 -187t-300 -64.5 q-152 0 -278 51t-202.5 153.5t-81.5 247.5z" />
<glyph unicode="&#x409;" horiz-adv-x="2121" d="M53 51v158q0 20 13.5 32.5t33.5 16.5q121 18 179.5 189t58.5 509v428q0 23 14.5 36.5t34.5 13.5h875q23 0 36 -13.5t13 -36.5v-466h227q246 0 379 -116t133 -337q0 -133 -58.5 -238.5t-174 -166t-279.5 -60.5h-467q-20 0 -34.5 14.5t-14.5 34.5v1125h-406v-254 q0 -338 -46 -534t-156.5 -287t-309.5 -97q-20 -2 -35.5 13.5t-15.5 35.5zM1311 231h215q106 0 166.5 64.5t60.5 171.5q0 109 -58.5 165t-168.5 56h-215v-457z" />
<glyph unicode="&#x40a;" horiz-adv-x="2123" d="M160 49v1333q0 23 13 37.5t36 14.5h188q23 0 37.5 -14.5t14.5 -37.5v-526h575v526q0 23 13.5 37.5t35.5 14.5h191q20 0 34.5 -14.5t14.5 -35.5v-466h225q246 0 379 -115t133 -338q0 -133 -58.5 -238.5t-174 -166t-279.5 -60.5h-465q-20 0 -34.5 14.5t-14.5 34.5v547h-575 v-547q0 -20 -14.5 -34.5t-37.5 -14.5h-188q-21 0 -35 14.5t-14 34.5zM1313 231h213q106 0 166.5 64.5t60.5 171.5q0 109 -57.5 165t-169.5 56h-213v-457z" />
<glyph unicode="&#x40b;" horiz-adv-x="1554" d="M47 1257v129q0 20 12.5 34t32.5 14h1002q23 0 35 -12.5t12 -35.5v-129q0 -20 -13.5 -33.5t-33.5 -13.5h-449v-368q45 51 141.5 77.5t196.5 26.5q467 0 467 -450v-447q0 -20 -13.5 -34.5t-35.5 -14.5h-189q-23 0 -37 13.5t-14 35.5v424q0 104 -58.5 155.5t-182.5 51.5 q-109 0 -192 -37t-83 -133v-461q0 -20 -13.5 -34.5t-35.5 -14.5h-188q-21 0 -35.5 14.5t-14.5 34.5v1161h-266q-20 0 -32.5 13.5t-12.5 33.5z" />
<glyph unicode="&#x40c;" horiz-adv-x="1376" d="M160 49v1333q0 23 13 37.5t36 14.5h188q23 0 37.5 -14.5t14.5 -37.5v-526l176 -2l329 543q23 37 74 37h209q20 0 31.5 -12.5t11.5 -30.5q0 -10 -10 -25l-385 -625l424 -671q6 -9 6 -23q0 -18 -13.5 -32.5t-33.5 -14.5h-215q-41 0 -62 35l-350 561h-192v-547 q0 -20 -14.5 -34.5t-37.5 -14.5h-188q-20 0 -34.5 14.5t-14.5 34.5zM539 1569q0 16 10 26l168 203q18 23 33.5 31t42.5 8h221q18 0 28.5 -11t10.5 -30q0 -14 -10 -24l-256 -209q-18 -14 -34 -20.5t-38 -6.5h-143q-33 0 -33 33z" />
<glyph unicode="&#x40d;" horiz-adv-x="1558" d="M160 51v1331q0 23 13 37.5t36 14.5h190q20 0 35 -14.5t15 -37.5v-936l669 953q18 35 60 35h172q23 0 36 -14.5t13 -37.5v-1333q0 -20 -13.5 -34.5t-35.5 -14.5h-189q-23 0 -37 14.5t-14 34.5v916l-668 -930q-20 -35 -59 -35h-174q-23 0 -36 14.5t-13 36.5zM401 1796 q0 18 10.5 29.5t28.5 11.5h223q27 0 41.5 -8t34.5 -31l166 -203q10 -10 10 -26q0 -33 -33 -33h-143q-22 0 -37.5 6t-34.5 21l-256 209q-10 10 -10 24z" />
<glyph unicode="&#x40e;" horiz-adv-x="1271" d="M23 1389q0 18 13 31.5t32 13.5h204q35 0 54 -39l360 -672l293 672q14 39 57 39h193q18 0 30.5 -12.5t12.5 -28.5q0 -20 -8 -35l-412 -940q-72 -164 -131 -250t-144 -127t-223 -41h-96q-20 0 -34.5 14.5t-14.5 34.5v156q0 20 14.5 34.5t34.5 14.5h76q70 0 118 37t93 123 l-518 954q-4 16 -4 21zM315 1792q0 16 10 27.5t29 11.5h119q18 0 28.5 -11.5t10.5 -27.5q0 -117 145 -117q143 0 143 117q0 16 10.5 27.5t28.5 11.5h119q18 0 28.5 -11.5t10.5 -27.5q0 -123 -81 -199.5t-259 -76.5t-260 76.5t-82 199.5z" />
<glyph unicode="&#x40f;" horiz-adv-x="1495" d="M160 49v1335q0 23 14 36.5t35 13.5h188q23 0 37.5 -14.5t14.5 -35.5v-1124h598v1124q0 20 14 35t35 15h190q23 0 36 -13.5t13 -36.5v-1335q0 -20 -13 -34.5t-36 -14.5h-395v-238q0 -23 -13.5 -36t-35.5 -13h-191q-20 0 -34.5 14.5t-14.5 34.5v238h-393q-20 0 -34.5 14.5 t-14.5 34.5z" />
<glyph unicode="&#x410;" horiz-adv-x="1421" d="M29 43l4 18l491 1321q20 51 74 52h225q53 0 74 -52l492 -1321l4 -18q0 -18 -13.5 -30.5t-29.5 -12.5h-183q-43 0 -59 39l-94 248h-606l-95 -248q-16 -39 -59 -39h-182q-19 0 -31 12.5t-12 30.5zM479 524h463l-231 631z" />
<glyph unicode="&#x411;" horiz-adv-x="1329" d="M160 49v1333q0 23 13 37.5t36 14.5h938q23 0 37 -14.5t14 -37.5v-145q0 -23 -14.5 -36t-36.5 -13h-698v-326h342q231 0 363 -114.5t132 -311.5q0 -117 -57.5 -216t-169 -159.5t-268.5 -60.5h-582q-21 0 -35 14.5t-14 34.5zM449 219h313q104 0 165.5 63.5t61.5 157.5 q0 92 -58.5 150.5t-168.5 58.5h-313v-430z" />
<glyph unicode="&#x412;" horiz-adv-x="1406" d="M160 49v1333q0 23 13 37.5t36 14.5h594q240 0 358.5 -105.5t118.5 -288.5q0 -106 -52 -181t-122 -107q86 -39 145.5 -127t59.5 -201q0 -190 -129 -307t-361 -117h-612q-21 0 -35 14.5t-14 34.5zM449 219h337q106 0 166 58.5t60 148.5t-60.5 148.5t-165.5 58.5h-337v-414z M449 846h317q104 0 160.5 51t56.5 137t-55.5 133t-161.5 47h-317v-368z" />
<glyph unicode="&#x413;" horiz-adv-x="1114" d="M160 49v1333q0 23 13 37.5t36 14.5h838q23 0 37 -14.5t14 -37.5v-145q0 -23 -14.5 -36t-36.5 -13h-598v-1139q0 -20 -14.5 -34.5t-37.5 -14.5h-188q-21 0 -35 14.5t-14 34.5z" />
<glyph unicode="&#x414;" horiz-adv-x="1527" d="M53 211q0 20 14.5 34.5t34.5 14.5h41q96 0 140.5 176t44.5 520v428q0 20 14 35t35 15h885q23 0 37 -14.5t14 -37.5v-1122h98q20 0 35.5 -14.5t15.5 -34.5v-447q0 -20 -14 -34.5t-37 -14.5h-188q-20 0 -34.5 13.5t-14.5 35.5v236h-832v-236q0 -23 -13.5 -36t-35.5 -13 h-191q-20 0 -34.5 13.5t-14.5 35.5v447zM496 260h528v914h-416v-254q0 -481 -112 -660z" />
<glyph unicode="&#x415;" horiz-adv-x="1275" d="M160 49v1333q0 23 13 37.5t36 14.5h903q23 0 37 -14.5t14 -37.5v-137q0 -23 -14 -36t-37 -13h-672v-358h627q23 0 37 -14.5t14 -37.5v-129q0 -23 -14 -36t-37 -13h-627v-370h688q23 0 37.5 -13.5t14.5 -36.5v-139q0 -23 -14.5 -36t-37.5 -13h-919q-21 0 -35 14.5 t-14 34.5z" />
<glyph unicode="&#x416;" horiz-adv-x="2113" d="M61 47q0 14 7 23l424 671l-386 625q-10 14 -10 25q0 18 11.5 30.5t31.5 12.5h207q51 0 72 -37l330 -543l167 2v528q0 20 14.5 35t37.5 15h180q23 0 36 -13.5t13 -36.5v-528l166 -2l330 543q23 37 73 37h207q20 0 31.5 -12.5t11.5 -30.5q0 -14 -8 -25l-387 -625l426 -671 q4 -7 4 -23q0 -18 -13.5 -32.5t-31.5 -14.5h-215q-25 0 -38 9t-26 26l-350 561h-180v-547q0 -20 -13.5 -34.5t-35.5 -14.5h-180q-23 0 -37.5 14.5t-14.5 34.5v547h-180l-350 -561q-20 -35 -61 -35h-215q-20 0 -34 14.5t-14 32.5z" />
<glyph unicode="&#x417;" horiz-adv-x="1325" d="M78 360v5q0 16 12 26t31 10h186q23 0 38 -9t24 -32q25 -68 93 -106.5t179 -38.5q127 0 209 55.5t82 149.5q0 98 -72 150.5t-201 52.5h-149q-20 0 -34.5 15t-14.5 36v121q0 23 14 38t35 15h143q109 0 172.5 46t63.5 140q0 82 -67.5 132.5t-174.5 50.5q-109 0 -177.5 -38 t-84.5 -110q-8 -20 -20.5 -29.5t-34.5 -9.5h-193q-18 0 -31.5 12.5t-11.5 30.5q14 166 153.5 273.5t403.5 107.5q164 0 285 -54t184.5 -145.5t63.5 -197.5q0 -92 -38 -173t-126 -130q106 -53 157.5 -144.5t51.5 -199.5q0 -127 -72 -224.5t-203 -151.5t-307 -54 q-180 0 -305 53t-189.5 139t-74.5 188z" />
<glyph unicode="&#x418;" horiz-adv-x="1558" d="M160 51v1331q0 23 13 37.5t36 14.5h190q20 0 35 -14.5t15 -37.5v-936l669 953q18 35 60 35h172q23 0 36 -14.5t13 -37.5v-1333q0 -20 -13.5 -34.5t-35.5 -14.5h-189q-23 0 -37 14.5t-14 34.5v916l-668 -930q-20 -35 -59 -35h-174q-23 0 -36 14.5t-13 36.5z" />
<glyph unicode="&#x419;" horiz-adv-x="1558" d="M160 51v1331q0 23 13 37.5t36 14.5h190q20 0 35 -14.5t15 -37.5v-936l669 953q18 35 60 35h172q23 0 36 -14.5t13 -37.5v-1333q0 -20 -13.5 -34.5t-35.5 -14.5h-189q-23 0 -37 14.5t-14 34.5v916l-668 -930q-20 -35 -59 -35h-174q-23 0 -36 14.5t-13 36.5zM438 1792 q0 16 10 27.5t29 11.5h119q18 0 28.5 -11.5t10.5 -27.5q0 -117 145 -117q143 0 143 117q0 16 10.5 27.5t28.5 11.5h119q18 0 28.5 -11.5t10.5 -27.5q0 -123 -81 -199.5t-259 -76.5t-260 76.5t-82 199.5z" />
<glyph unicode="&#x41a;" horiz-adv-x="1376" d="M160 49v1333q0 23 13 37.5t36 14.5h188q23 0 37.5 -14.5t14.5 -37.5v-526l176 -2l329 543q23 37 74 37h209q20 0 31.5 -12.5t11.5 -30.5q0 -10 -10 -25l-385 -625l424 -671q6 -9 6 -23q0 -18 -13.5 -32.5t-33.5 -14.5h-215q-41 0 -62 35l-350 561h-192v-547 q0 -20 -14.5 -34.5t-37.5 -14.5h-188q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x41b;" horiz-adv-x="1505" d="M53 51v158q0 20 13.5 32.5t33.5 16.5q121 18 179.5 189t58.5 509v428q0 23 14.5 36.5t34.5 13.5h909q23 0 37.5 -14.5t14.5 -37.5v-1333q0 -20 -14.5 -34.5t-37.5 -14.5h-188q-21 0 -35 14.5t-14 34.5v1125h-443v-254q0 -338 -46 -534t-156.5 -287t-309.5 -97 q-20 -2 -35.5 13.5t-15.5 35.5z" />
<glyph unicode="&#x41c;" horiz-adv-x="1662" d="M160 49v1333q0 23 13 37.5t36 14.5h166q39 0 61 -39l395 -733l396 733q23 39 61 39h164q23 0 37 -14.5t14 -37.5v-1333q0 -23 -14 -36t-37 -13h-176q-20 0 -34.5 14.5t-14.5 34.5v881l-285 -541q-23 -45 -68 -45h-86q-43 0 -69 45l-285 541v-881q0 -20 -13 -34.5 t-36 -14.5h-176q-21 0 -35 14.5t-14 34.5z" />
<glyph unicode="&#x41d;" horiz-adv-x="1495" d="M160 49v1333q0 23 13 37.5t36 14.5h188q23 0 37.5 -14.5t14.5 -37.5v-526h598v526q0 23 14 37.5t35 14.5h190q23 0 36 -14.5t13 -37.5v-1333q0 -20 -13 -34.5t-36 -14.5h-190q-20 0 -34.5 14.5t-14.5 34.5v547h-598v-547q0 -20 -14.5 -34.5t-37.5 -14.5h-188 q-21 0 -35 14.5t-14 34.5z" />
<glyph unicode="&#x41e;" horiz-adv-x="1423" d="M111 715q0 117 2 176q8 272 167.5 417.5t432.5 145.5q270 0 431 -145.5t169 -417.5q4 -119 4 -176q0 -55 -4 -174q-8 -279 -165 -420t-435 -141q-281 0 -436.5 141t-163.5 420q-2 59 -2 174zM408 717q0 -104 2 -166q4 -168 84.5 -251t218.5 -83q137 0 217 82t86 252 q4 123 4 166q0 47 -4 166q-6 168 -88 251t-215 83q-135 0 -217 -83t-86 -251q-2 -59 -2 -166z" />
<glyph unicode="&#x41f;" horiz-adv-x="1495" d="M160 49v1333q0 23 13 37.5t36 14.5h1077q23 0 36 -14.5t13 -37.5v-1333q0 -20 -13 -34.5t-36 -14.5h-190q-20 0 -34.5 14.5t-14.5 34.5v1125h-598v-1125q0 -20 -14.5 -34.5t-37.5 -14.5h-188q-21 0 -35 14.5t-14 34.5z" />
<glyph unicode="&#x420;" horiz-adv-x="1351" d="M160 49v1333q0 23 13 37.5t36 14.5h549q248 0 389 -117t141 -340q0 -221 -141 -335t-389 -114h-301v-479q0 -23 -14.5 -36t-36.5 -13h-197q-21 0 -35 14.5t-14 34.5zM453 758h295q119 0 181 56t62 165q0 106 -60.5 165.5t-182.5 59.5h-295v-446z" />
<glyph unicode="&#x421;" horiz-adv-x="1402" d="M109 715q0 127 2 184q10 262 165.5 408.5t436.5 146.5q182 0 317 -62.5t210 -171t79 -243.5v-4q0 -16 -13.5 -27.5t-29.5 -11.5h-199q-25 0 -37 11t-20 40q-31 127 -106.5 179.5t-200.5 52.5q-293 0 -305 -328q-2 -55 -2 -170t2 -174q12 -328 305 -328q123 0 199.5 52.5 t107.5 179.5q8 29 20.5 40t36.5 11h199q18 0 31.5 -12.5t11.5 -30.5q-4 -135 -79 -244t-210 -171t-317 -62q-283 0 -437.5 144t-164.5 411q-2 55 -2 180z" />
<glyph unicode="&#x422;" horiz-adv-x="1241" d="M47 1229v153q0 23 13.5 37.5t35.5 14.5h1047q23 0 37 -14.5t14 -37.5v-153q0 -23 -14.5 -37t-36.5 -14h-377v-1129q0 -20 -13.5 -34.5t-35.5 -14.5h-193q-20 0 -34.5 14.5t-14.5 34.5v1129h-379q-20 0 -34.5 14t-14.5 37z" />
<glyph unicode="&#x423;" horiz-adv-x="1271" d="M23 1389q0 18 13 31.5t32 13.5h204q35 0 54 -39l360 -672l293 672q14 39 57 39h193q18 0 30.5 -12.5t12.5 -28.5q0 -20 -8 -35l-412 -940q-72 -164 -131 -250t-144 -127t-223 -41h-96q-20 0 -34.5 14.5t-14.5 34.5v156q0 20 14.5 34.5t34.5 14.5h76q70 0 118 37t93 123 l-518 954q-4 16 -4 21z" />
<glyph unicode="&#x424;" horiz-adv-x="1828" d="M109 745l2 74q14 262 188 401.5t471 139.5v127q0 23 14.5 36t34.5 13h191q23 0 36 -13.5t13 -35.5v-127q297 0 471 -139.5t188 -401.5q2 -33 2 -84q0 -39 -2 -63q-43 -537 -659 -537v-151q0 -23 -13.5 -36.5t-35.5 -13.5h-191q-20 0 -34.5 13.5t-14.5 36.5v151 q-614 0 -659 537zM406 748l2 -66q18 -309 364 -309v747q-170 0 -260 -74.5t-104 -234.5zM1057 373q350 0 364 309q2 20 2 76q0 39 -2 53q-14 160 -105 234.5t-259 74.5v-747z" />
<glyph unicode="&#x425;" horiz-adv-x="1357" d="M35 43q0 8 8 25l457 663l-428 635q-8 12 -9 25q0 18 13.5 30.5t29.5 12.5h216q37 0 63 -39l299 -439l301 439q23 39 64 39h202q16 0 29.5 -12.5t13.5 -30.5q0 -8 -8 -25l-428 -635l459 -663q6 -12 6 -25q0 -18 -12.5 -30.5t-30.5 -12.5h-219q-37 0 -64 37l-323 461 l-320 -461q-27 -37 -63 -37h-213q-17 0 -30 12.5t-13 30.5z" />
<glyph unicode="&#x426;" horiz-adv-x="1552" d="M160 51v1333q0 23 14 36.5t35 13.5h188q23 0 37.5 -14.5t14.5 -35.5v-1124h598v1124q0 20 14 35t35 15h190q23 0 36 -13.5t13 -36.5v-1124h99q23 0 37 -14.5t14 -34.5v-449q0 -23 -14.5 -36t-36.5 -13h-189q-20 0 -34.5 13.5t-14.5 35.5v238h-987q-23 0 -36 14.5 t-13 36.5z" />
<glyph unicode="&#x427;" horiz-adv-x="1388" d="M104 924v460q0 23 14.5 36.5t35.5 13.5h188q23 0 37 -13.5t14 -36.5v-446q0 -121 61.5 -176t204.5 -55q102 0 187.5 37.5t85.5 125.5v514q0 23 14.5 36.5t34.5 13.5h188q23 0 37.5 -13.5t14.5 -36.5v-1335q0 -23 -14.5 -36t-37.5 -13h-188q-20 0 -34.5 14.5t-14.5 34.5 v481q-45 -45 -137.5 -67.5t-190.5 -22.5q-254 0 -378 121t-122 363z" />
<glyph unicode="&#x428;" horiz-adv-x="1992" d="M160 49v1335q0 23 14 36.5t35 13.5h188q23 0 37.5 -14.5t14.5 -35.5v-1124h403v1124q0 20 14.5 35t36.5 15h189q23 0 36 -13.5t13 -36.5v-1124h405v1124q0 23 14.5 36.5t34.5 13.5h189q23 0 37 -14.5t14 -35.5v-1335q0 -20 -14.5 -34.5t-36.5 -14.5h-1575q-21 0 -35 14.5 t-14 34.5z" />
<glyph unicode="&#x429;" horiz-adv-x="2050" d="M160 49v1335q0 23 14 36.5t35 13.5h188q23 0 37.5 -14.5t14.5 -35.5v-1124h403v1124q0 20 14.5 35t36.5 15h189q23 0 36 -13.5t13 -36.5v-1124h405v1124q0 23 14.5 36.5t34.5 13.5h189q23 0 37 -14.5t14 -35.5v-1124h98q20 0 34.5 -14.5t14.5 -34.5v-449q0 -23 -13 -36 t-36 -13h-190q-21 0 -35 14.5t-14 34.5v238h-1485q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x42a;" horiz-adv-x="1517" d="M47 1245v137q0 23 13.5 37.5t35.5 14.5h537q23 0 36 -13.5t13 -36.5v-466h250q246 0 379 -115t133 -338q0 -133 -58.5 -238.5t-174 -166t-279.5 -60.5h-490q-20 0 -34.5 14.5t-14.5 34.5v1147h-297q-20 0 -34.5 14.5t-14.5 34.5zM682 231h238q106 0 166.5 64.5 t60.5 171.5q0 109 -57.5 165t-169.5 56h-238v-457z" />
<glyph unicode="&#x42b;" horiz-adv-x="1814" d="M160 49v1335q0 23 14 36.5t35 13.5h188q23 0 37.5 -14.5t14.5 -35.5v-466h225q246 0 379 -115t133 -338q0 -133 -58.5 -238.5t-174 -166t-279.5 -60.5h-465q-21 0 -35 14.5t-14 34.5zM449 231h213q106 0 166.5 64.5t60.5 171.5q0 109 -57.5 165t-169.5 56h-213v-457z M1362 49v1335q0 23 14.5 36.5t34.5 13.5h197q23 0 36 -13.5t13 -36.5v-1335q0 -20 -13.5 -34.5t-35.5 -14.5h-197q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x42c;" horiz-adv-x="1294" d="M160 49v1335q0 23 14 36.5t35 13.5h188q23 0 37.5 -14.5t14.5 -35.5v-466h262q246 0 379 -116t133 -337q0 -133 -58.5 -238.5t-174.5 -166t-279 -60.5h-502q-21 0 -35 14.5t-14 34.5zM449 231h249q106 0 167 64.5t61 171.5q0 109 -58.5 165t-169.5 56h-249v-457z" />
<glyph unicode="&#x42d;" horiz-adv-x="1366" d="M94 358v4q0 16 13.5 27.5t29.5 11.5h191q23 0 34 -8t21 -31q23 -66 92.5 -106.5t181.5 -40.5q143 0 222 93t79 259v39h-446q-20 0 -34.5 15.5t-14.5 35.5v119q0 23 14.5 38t34.5 15h446v35q0 352 -301 353q-113 0 -181 -41t-89 -107q-10 -23 -20.5 -31t-32.5 -8h-193 q-18 0 -31.5 12.5t-11.5 30.5q20 180 175 280.5t391 100.5q266 0 424.5 -146.5t166.5 -449.5q4 -111 5 -141q0 -31 -5 -142q-8 -305 -164.5 -450t-426.5 -145q-156 0 -280 46t-201 131t-89 201z" />
<glyph unicode="&#x42e;" horiz-adv-x="2039" d="M160 49v1335q0 23 14 36.5t35 13.5h188q23 0 37.5 -14.5t14.5 -35.5v-528h278l2 64q20 242 169 388t429 146q289 0 439.5 -154.5t160.5 -408.5q4 -119 4 -176q0 -55 -4 -174q-10 -260 -159.5 -410.5t-440.5 -150.5q-285 0 -433.5 145t-164.5 395q-4 53 -4 76h-276v-547 q0 -20 -14.5 -34.5t-37.5 -14.5h-188q-20 0 -34.5 14.5t-14.5 34.5zM1022 717q0 -111 2 -172q6 -168 86 -248t217 -80t217 80t86 248q4 123 4 172q0 53 -4 172q-6 168 -87 248t-216 80t-216 -80t-87 -248q-2 -59 -2 -172z" />
<glyph unicode="&#x42f;" horiz-adv-x="1396" d="M137 43q0 10 6 23l297 485q-129 49 -200.5 156.5t-71.5 263.5q0 129 58.5 234.5t174 167t279.5 61.5h508q22 0 35.5 -13.5t13.5 -36.5v-1335q0 -20 -13.5 -34.5t-35.5 -14.5h-189q-23 0 -37 13.5t-14 35.5v467h-225l-272 -461q-12 -27 -31 -41t-51 -14h-189 q-16 0 -29.5 12.5t-13.5 30.5zM465 969q0 -217 225 -217h258v452h-258q-106 0 -165.5 -64.5t-59.5 -170.5z" />
<glyph unicode="&#x430;" d="M66 293q0 139 112.5 225t308.5 117l281 41v43q0 88 -45 133t-143 45q-68 0 -109 -24.5t-66.5 -50t-33.5 -34.5q-8 -25 -31 -24h-160q-18 0 -31.5 12t-13.5 33q2 51 50 115.5t148.5 112.5t248.5 48q242 0 352 -108.5t110 -284.5v-643q0 -20 -13 -34.5t-36 -14.5h-170 q-20 0 -34.5 14.5t-14.5 34.5v80q-45 -66 -127 -107.5t-205 -41.5q-104 0 -191 41.5t-137 113.5t-50 158zM330 313q0 -66 54 -101.5t130 -35.5q113 0 184.5 74t71.5 213v41l-205 -33q-236 -37 -235 -158z" />
<glyph unicode="&#x431;" horiz-adv-x="1224" d="M102 627l2 123q6 291 90.5 436t244 211.5t517.5 152.5l13 2q16 0 29.5 -11t15.5 -28l26 -137l2 -12q0 -16 -10 -28.5t-26 -16.5q-41 -8 -62 -14q-248 -53 -353.5 -91t-160.5 -115t-63 -235q49 61 130 98t171 37q215 0 333.5 -118.5t128.5 -323.5q5 -45 5 -72t-5 -63 q-10 -205 -141 -323.5t-371 -118.5q-254 0 -378.5 132t-135.5 392zM385 485l2 -53q4 -129 63.5 -195.5t167.5 -66.5q109 0 167.5 66.5t62.5 195.5q2 14 2 53q0 43 -2 62q-4 127 -60.5 194.5t-154.5 67.5q-111 0 -176.5 -68.5t-69.5 -193.5z" />
<glyph unicode="&#x432;" horiz-adv-x="1198" d="M135 49v967q0 23 14.5 36t34.5 13h455q213 0 320.5 -78t107.5 -217q0 -82 -30.5 -130t-92.5 -85q72 -33 112 -98.5t40 -147.5q0 -150 -111 -229.5t-323 -79.5h-478q-20 0 -34.5 14.5t-14.5 34.5zM399 186h246q84 0 131 31t47 94q0 68 -44 97.5t-134 29.5h-246v-252z M399 631h236q82 0 129 34.5t47 98.5q0 61 -45 88t-131 27h-236v-248z" />
<glyph unicode="&#x433;" horiz-adv-x="913" d="M135 49v967q0 23 14.5 36t34.5 13h650q23 0 36 -13.5t13 -35.5v-121q0 -20 -13.5 -34.5t-35.5 -14.5h-426v-797q0 -20 -14.5 -34.5t-35.5 -14.5h-174q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x434;" horiz-adv-x="1288" d="M45 170q0 23 14.5 36t34.5 13h17q80 0 116.5 127t36.5 393v277q0 23 14.5 36t34.5 13h734q23 0 36 -13.5t13 -35.5v-795h92q23 0 36 -13t13 -36v-348q0 -23 -13.5 -36t-35.5 -13h-174q-20 0 -34.5 13t-14.5 36v176h-648v-176q0 -23 -13 -36t-36 -13h-174q-20 0 -34.5 13 t-14.5 36v346zM430 219l393 2v625h-299v-137q0 -352 -94 -490z" />
<glyph unicode="&#x435;" horiz-adv-x="1183" d="M88 535q0 256 134 403t372 147q244 0 376 -147t132 -393v-43q0 -20 -14.5 -34.5t-36.5 -14.5h-680v-17q4 -115 62.5 -189.5t160.5 -74.5q117 0 190 94q18 23 28.5 28t35.5 5h180q18 0 31.5 -10t13.5 -29q0 -49 -58.5 -115.5t-167 -115.5t-251.5 -49q-229 0 -363.5 131 t-142.5 372zM371 627h448v4q0 121 -60.5 193.5t-164.5 72.5t-163.5 -72.5t-59.5 -193.5v-4z" />
<glyph unicode="&#x436;" horiz-adv-x="1759" d="M43 37q0 10 8 26l340 490l-295 448q-8 10 -8 27q0 16 11.5 26.5t31.5 10.5h186q20 0 32.5 -8t25.5 -25l243 -381h130v365q0 23 14 36t35 13h170q20 0 34.5 -14.5t14.5 -34.5v-365h123l247 381q20 33 56 33h186q20 0 32.5 -10.5t12.5 -26.5q0 -12 -10 -27l-295 -448 l340 -490q8 -10 8 -26t-11 -26.5t-32 -10.5h-211q-31 0 -57 31l-266 401h-123v-383q0 -20 -14.5 -34.5t-34.5 -14.5h-170q-20 0 -34.5 14.5t-14.5 34.5v383h-125l-269 -401q-25 -31 -57 -31h-211q-18 0 -30.5 10t-12.5 27z" />
<glyph unicode="&#x437;" horiz-adv-x="1097" d="M80 285v2q0 14 11 24.5t28 10.5h170q37 0 53 -35q18 -57 71.5 -83t141.5 -26t142.5 35t54.5 104q0 137 -166 138h-113q-20 0 -32.5 13t-12.5 32v88q0 18 12.5 31.5t32.5 13.5h105q74 0 110.5 27.5t36.5 87.5q0 59 -44 98t-136 39t-134 -29t-63 -92q-16 -35 -53 -35h-164 q-16 0 -28.5 11.5t-10.5 25.5q4 82 52.5 154.5t148.5 118.5t252 46q227 0 334.5 -88t107.5 -213q0 -80 -29.5 -133t-99.5 -88q164 -61 164 -268q0 -150 -129 -232.5t-348 -82.5q-164 0 -265.5 49t-147.5 117.5t-52 138.5z" />
<glyph unicode="&#x438;" horiz-adv-x="1277" d="M135 47v969q0 23 14.5 36t34.5 13h174q20 0 35 -14.5t15 -34.5v-654l485 670q25 33 57 33h158q18 0 32.5 -13.5t14.5 -31.5v-971q0 -20 -13.5 -34.5t-35.5 -14.5h-174q-20 0 -34.5 14.5t-14.5 34.5v653l-486 -669q-23 -33 -57 -33h-158q-20 0 -33.5 13.5t-13.5 33.5z" />
<glyph unicode="&#x439;" horiz-adv-x="1277" d="M135 47v969q0 23 14.5 36t34.5 13h174q20 0 35 -14.5t15 -34.5v-654l485 670q25 33 57 33h158q18 0 32.5 -13.5t14.5 -31.5v-971q0 -20 -13.5 -34.5t-35.5 -14.5h-174q-20 0 -34.5 14.5t-14.5 34.5v653l-486 -669q-23 -33 -57 -33h-158q-20 0 -33.5 13.5t-13.5 33.5z M323 1481q0 16 10.5 27.5t28.5 11.5h119q18 0 28.5 -11.5t10.5 -27.5q0 -119 137 -119q139 0 140 119q0 16 10 27.5t28 11.5h119q18 0 28.5 -11.5t10.5 -27.5q0 -123 -80 -200t-256 -77q-174 0 -254 77t-80 200z" />
<glyph unicode="&#x43a;" horiz-adv-x="1173" d="M135 49v967q0 23 14.5 36t34.5 13h170q23 0 36 -13.5t13 -35.5v-365h152l244 381q12 16 24.5 24.5t30.5 8.5h188q20 0 32.5 -10.5t12.5 -26.5q0 -6 -10 -27l-291 -450l334 -488q10 -14 10 -26q0 -16 -12 -26.5t-31 -10.5h-210q-20 0 -33 7t-25 24l-266 399h-150v-381 q0 -20 -13 -34.5t-36 -14.5h-170q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x43b;" horiz-adv-x="1236" d="M51 51v131q0 20 13.5 32.5t33.5 14.5q90 6 129 125t39 385v277q0 23 14.5 36t34.5 13h732q23 0 36 -13.5t13 -35.5v-967q0 -20 -13.5 -34.5t-35.5 -14.5h-175q-20 0 -34.5 14.5t-14.5 34.5v797h-297v-137q0 -266 -41 -419t-132 -221.5t-251 -68.5q-20 0 -35.5 15.5 t-15.5 35.5z" />
<glyph unicode="&#x43c;" horiz-adv-x="1441" d="M135 43v983q0 14 12.5 26.5t28.5 12.5h174q35 0 53 -35l314 -569l313 569q18 35 55 35h175q16 0 28 -11.5t12 -27.5v-983q0 -18 -12 -30.5t-31 -12.5h-182q-18 0 -30.5 12.5t-12.5 30.5v567l-217 -397q-14 -25 -27.5 -37t-35.5 -12h-68q-23 0 -36 12t-30 37l-215 389 v-559q0 -18 -12 -30.5t-33 -12.5h-180q-18 0 -30.5 12.5t-12.5 30.5z" />
<glyph unicode="&#x43d;" horiz-adv-x="1277" d="M135 49v967q0 23 14.5 36t34.5 13h174q20 0 35 -14.5t15 -34.5v-365h456v365q0 20 14.5 34.5t34.5 14.5h174q23 0 36.5 -13.5t13.5 -35.5v-967q0 -20 -13.5 -34.5t-36.5 -14.5h-174q-20 0 -34.5 14.5t-14.5 34.5v383h-456v-383q0 -20 -14.5 -34.5t-35.5 -14.5h-174 q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x43e;" horiz-adv-x="1214" d="M92 532l2 91q10 215 143.5 338.5t370.5 123.5q236 0 369 -123.5t143 -338.5q2 -25 2 -91t-2 -90q-10 -217 -141 -339.5t-371 -122.5q-241 0 -372.5 122.5t-141.5 339.5zM375 532l2 -79q4 -131 63.5 -199t167.5 -68q109 0 167.5 68t62.5 199q2 20 2 79t-2 80 q-4 131 -63.5 200t-166.5 69q-109 0 -168 -69t-63 -200z" />
<glyph unicode="&#x43f;" horiz-adv-x="1253" d="M135 49v967q0 23 14.5 36t34.5 13h879q23 0 36 -13.5t13 -35.5v-967q0 -20 -13.5 -34.5t-35.5 -14.5h-172q-20 0 -34.5 14.5t-14.5 34.5v797h-434v-797q0 -20 -14.5 -34.5t-35.5 -14.5h-174q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x440;" horiz-adv-x="1257" d="M137 -340v1356q0 23 14.5 36t34.5 13h164q23 0 37 -14.5t14 -34.5v-82q109 152 326 151q207 0 317.5 -130t118.5 -349q2 -25 2 -74t-2 -73q-6 -215 -117.5 -347t-318.5 -132q-205 0 -313 145v-465q0 -23 -13.5 -36t-35.5 -13h-179q-20 0 -34.5 13.5t-14.5 35.5zM412 526 q0 -53 2 -80q4 -102 63.5 -173.5t171.5 -71.5q220 0 234 270q2 20 2 61t-2 62q-14 270 -234 270q-117 0 -174 -75.5t-61 -184.5q-2 -25 -2 -78z" />
<glyph unicode="&#x441;" d="M92 532l2 82q8 221 142.5 346t359.5 125q162 0 273.5 -57t166 -140t58.5 -159q2 -20 -13.5 -34.5t-36.5 -14.5h-182q-23 0 -33 9t-20 34q-31 78 -81 112.5t-128 34.5q-104 0 -162.5 -65.5t-62.5 -200.5l-2 -76l2 -67q12 -266 225 -266q80 0 129 33.5t80 113.5 q8 23 19.5 33t33.5 10h182q20 0 36 -14.5t14 -34.5q-4 -72 -57.5 -155t-164 -142t-276.5 -59q-225 0 -359.5 124.5t-142.5 346.5z" />
<glyph unicode="&#x442;" horiz-adv-x="1087" d="M29 895v121q0 23 14 36t35 13h932q23 0 36 -13.5t13 -35.5v-121q0 -20 -13.5 -34.5t-35.5 -14.5h-332v-797q0 -20 -13.5 -34.5t-35.5 -14.5h-170q-21 0 -35 14.5t-14 34.5v797h-332q-20 0 -34.5 14t-14.5 35z" />
<glyph unicode="&#x443;" d="M51 1020q0 18 13.5 31.5t31.5 13.5h166q39 0 55 -39l271 -670l278 670q14 39 56 39h159q17 0 30 -12.5t13 -28.5q0 -18 -8 -33l-389 -930q-63 -150 -114.5 -240.5t-127 -150t-182.5 -59.5h-92q-20 0 -34.5 14.5t-14.5 34.5v119q0 20 14 34.5t35 14.5h63q59 0 90 35 t76 139l21 51l-402 938q-8 18 -8 29z" />
<glyph unicode="&#x444;" horiz-adv-x="1564" d="M92 532l2 78q12 213 151.5 334t401.5 121v340q0 23 14.5 36t34.5 13h172q23 0 36.5 -13.5t13.5 -35.5v-340q262 0 401 -121t151 -334q2 -25 3 -78q0 -53 -3 -77q-12 -215 -148 -335t-404 -120v-340q0 -23 -13.5 -36t-36.5 -13h-172q-20 0 -34.5 13.5t-14.5 35.5v340 q-268 0 -404.5 120t-148.5 335zM362 532q0 -170 67 -247.5t220 -77.5v651q-141 0 -209.5 -65.5t-74.5 -200.5zM918 207q139 0 206.5 64.5t75.5 201.5q2 18 2 59t-2 60q-6 135 -74.5 200.5t-207.5 65.5v-651z" />
<glyph unicode="&#x445;" horiz-adv-x="1157" d="M45 45q0 18 12 35l351 467l-324 438q-12 16 -12 35q0 18 13 31.5t32 13.5h180q23 0 34 -9t23 -26l230 -305l227 305q2 2 15.5 18.5t41.5 16.5h174q18 0 30.5 -13.5t12.5 -29.5q0 -20 -12 -37l-328 -440l353 -465q12 -16 12 -35q0 -18 -13.5 -31.5t-31.5 -13.5h-188 q-33 0 -56 33l-246 323l-247 -323q-23 -33 -56 -33h-182q-18 0 -31.5 13.5t-13.5 31.5z" />
<glyph unicode="&#x446;" horiz-adv-x="1306" d="M135 49v967q0 23 14.5 36t34.5 13h174q20 0 35 -14.5t15 -34.5v-797h434v797q0 23 14 36t35 13h172q23 0 36 -13.5t13 -35.5v-797h92q23 0 36 -13t13 -36v-371q0 -20 -14 -34.5t-35 -14.5h-172q-20 0 -34.5 14.5t-14.5 34.5v201h-799q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x447;" horiz-adv-x="1185" d="M76 686v330q0 23 14 36t35 13h178q23 0 36 -13.5t13 -35.5v-316q0 -82 43 -116.5t142 -34.5q223 0 223 119v348q0 23 14 36t35 13h186q23 0 36 -13.5t13 -35.5v-967q0 -20 -13 -34.5t-36 -14.5h-186q-21 0 -35 14.5t-14 34.5v348q-100 -88 -295 -88q-193 0 -291 91.5 t-98 285.5z" />
<glyph unicode="&#x448;" horiz-adv-x="1658" d="M135 49v967q0 23 14.5 36t34.5 13h164q23 0 37 -14.5t14 -34.5v-797h301v797q0 20 14.5 34.5t35.5 14.5h153q20 0 34.5 -14.5t14.5 -34.5v-797h301v797q0 20 14.5 34.5t37.5 14.5h163q23 0 36.5 -13.5t13.5 -35.5v-967q0 -20 -13.5 -34.5t-36.5 -14.5h-1284 q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x449;" horiz-adv-x="1710" d="M135 49v967q0 23 14.5 36t34.5 13h164q23 0 37 -14.5t14 -34.5v-797h301v797q0 20 14.5 34.5t35.5 14.5h153q20 0 34.5 -14.5t14.5 -34.5v-797h301v797q0 20 14.5 34.5t37.5 14.5h163q23 0 36.5 -13.5t13.5 -35.5v-797h92q23 0 36 -13t13 -36v-371q0 -20 -14.5 -34.5 t-34.5 -14.5h-172q-21 0 -35 14.5t-14 34.5v201h-1205q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x44a;" horiz-adv-x="1253" d="M29 895v121q0 23 14 36t35 13h471q23 0 36 -13.5t13 -35.5v-287h188q205 0 310.5 -97t105.5 -274q0 -178 -110.5 -268t-323.5 -90h-393q-21 0 -35 14.5t-14 34.5v797h-248q-20 0 -34.5 14t-14.5 35zM594 197h168q102 0 148 36.5t46 124.5t-47 131t-147 43h-168v-335z" />
<glyph unicode="&#x44b;" horiz-adv-x="1572" d="M135 49v967q0 23 14.5 36t34.5 13h174q23 0 36.5 -13.5t13.5 -35.5v-287h200q203 0 309.5 -98.5t106.5 -272.5q0 -178 -110.5 -268t-323.5 -90h-406q-20 0 -34.5 14.5t-14.5 34.5zM403 197h181q102 0 148 36.5t46 124.5t-47 131t-147 43h-181v-335zM1161 49v967 q0 23 14.5 36t34.5 13h174q20 0 35 -14.5t15 -34.5v-967q0 -20 -14.5 -34.5t-35.5 -14.5h-174q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x44c;" horiz-adv-x="1087" d="M135 49v967q0 23 14.5 36t34.5 13h174q23 0 36.5 -13.5t13.5 -35.5v-287h210q205 0 311.5 -98.5t106.5 -272.5q0 -178 -110.5 -268t-323.5 -90h-418q-20 0 -34.5 14.5t-14.5 34.5zM403 197h193q102 0 147 36.5t45 124.5t-46 131t-146 43h-193v-335z" />
<glyph unicode="&#x44d;" horiz-adv-x="1179" d="M94 305v6q0 14 12.5 24.5t26.5 10.5h166q20 0 30.5 -7t22.5 -28q31 -72 82 -102.5t133 -30.5q221 0 228 254v12h-344q-18 0 -30.5 13.5t-12.5 31.5v89q0 18 12 31.5t31 13.5h344v10q-2 125 -62.5 188.5t-165.5 63.5q-82 0 -134 -31t-83 -102q-10 -20 -20.5 -27.5 t-32.5 -7.5h-160q-18 0 -30.5 11t-10.5 30q6 78 55.5 151.5t153 124.5t262.5 51q236 0 369 -120.5t143 -343.5q2 -25 2 -89q0 -63 -2 -88q-10 -225 -140 -344.5t-372 -119.5q-161 0 -265.5 50t-153 123.5t-54.5 151.5z" />
<glyph unicode="&#x44e;" horiz-adv-x="1712" d="M135 49v967q0 23 14.5 36t34.5 13h174q20 0 35 -14.5t15 -34.5v-365h186q18 203 150.5 318.5t361.5 115.5q236 0 369 -123.5t143 -338.5q2 -25 2 -91t-2 -90q-10 -217 -141 -339.5t-371 -122.5q-238 0 -369 120.5t-145 331.5h-184v-383q0 -20 -14.5 -34.5t-35.5 -14.5 h-174q-20 0 -34.5 14.5t-14.5 34.5zM872 532l2 -79q4 -131 63.5 -199t168.5 -68t167 68t62 199q2 20 2 79t-2 80q-4 131 -63.5 200t-165.5 69q-109 0 -168 -69t-64 -200z" />
<glyph unicode="&#x44f;" horiz-adv-x="1179" d="M113 41q0 18 12 33l215 336q-96 41 -145.5 120.5t-49.5 188.5q0 172 107.5 259t314.5 87h422q23 0 36 -13.5t13 -35.5v-967q0 -20 -13 -34.5t-36 -14.5h-178q-20 0 -34.5 14.5t-14.5 34.5v311h-182l-199 -329q-18 -31 -51 -31h-176q-16 0 -28.5 12.5t-12.5 28.5zM391 719 q0 -76 43 -119t127 -43h205v311h-193q-96 0 -139 -33.5t-43 -115.5z" />
<glyph unicode="&#x450;" horiz-adv-x="1183" d="M88 535q0 256 134 403t372 147q244 0 376 -147t132 -393v-43q0 -20 -14.5 -34.5t-36.5 -14.5h-680v-17q4 -115 62.5 -189.5t160.5 -74.5q117 0 190 94q18 23 28.5 28t35.5 5h180q18 0 31.5 -10t13.5 -29q0 -49 -58.5 -115.5t-167 -115.5t-251.5 -49q-229 0 -363.5 131 t-142.5 372zM209 1479q0 18 10.5 29.5t28.5 11.5h207q27 0 42 -8.5t34 -30.5l168 -203q10 -10 10 -27q0 -33 -33 -32h-129q-22 0 -37.5 6t-33.5 20l-256 209q-10 10 -11 25zM371 627h448v4q0 121 -60.5 193.5t-164.5 72.5t-163.5 -72.5t-59.5 -193.5v-4z" />
<glyph unicode="&#x451;" horiz-adv-x="1183" d="M88 535q0 256 134 403t372 147q244 0 376 -147t132 -393v-43q0 -20 -14.5 -34.5t-36.5 -14.5h-680v-17q4 -115 62.5 -189.5t160.5 -74.5q117 0 190 94q18 23 28.5 28t35.5 5h180q18 0 31.5 -10t13.5 -29q0 -49 -58.5 -115.5t-167 -115.5t-251.5 -49q-229 0 -363.5 131 t-142.5 372zM284 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM371 627h448v4q0 121 -60.5 193.5t-164.5 72.5t-163.5 -72.5t-59.5 -193.5v-4zM671 1303v151q0 20 12.5 33.5t32.5 13.5 h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5z" />
<glyph unicode="&#x452;" horiz-adv-x="1288" d="M12 1155v59q0 20 14.5 35t34.5 15h93v141q0 23 14 36t35 13h186q23 0 36 -13.5t13 -35.5v-141h305q23 0 36.5 -13.5t13.5 -36.5v-59q0 -20 -13.5 -34.5t-36.5 -14.5h-305v-264q123 147 332 147q197 0 305.5 -128t108.5 -335v-336q0 -270 -126 -424.5t-388 -154.5h-47 q-21 0 -35.5 13.5t-14.5 35.5v121q0 20 14.5 34.5t35.5 14.5h22q152 0 202 92t50 273v319q0 119 -58.5 185.5t-166.5 66.5q-106 0 -170 -66.5t-64 -185.5v-465q0 -20 -13 -34.5t-36 -14.5h-186q-20 0 -34.5 14.5t-14.5 34.5v1057h-93q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x453;" horiz-adv-x="913" d="M135 49v967q0 23 14.5 36t34.5 13h650q23 0 36 -13.5t13 -35.5v-121q0 -20 -13.5 -34.5t-35.5 -14.5h-426v-797q0 -20 -14.5 -34.5t-35.5 -14.5h-174q-20 0 -34.5 14.5t-14.5 34.5zM415 1251q0 16 11 27l167 203q18 23 34 31t42 8h207q18 0 28.5 -11.5t10.5 -29.5 q0 -14 -10 -25l-256 -209q-18 -14 -33.5 -20t-38.5 -6h-129q-33 -1 -33 32z" />
<glyph unicode="&#x454;" horiz-adv-x="1179" d="M100 444v177q10 223 142.5 343.5t369.5 120.5q152 0 255.5 -47t155.5 -121.5t60 -158.5v-4q0 -16 -12 -26.5t-29 -10.5h-159q-18 0 -27.5 6t-19.5 22q-37 76 -89.5 108t-134.5 32q-102 0 -163.5 -62.5t-63.5 -189.5v-10h344q18 0 30.5 -13.5t12.5 -31.5v-89 q0 -20 -12 -32.5t-31 -12.5h-344v-12q6 -254 227 -254q84 0 135.5 31t88.5 108q8 16 17 22.5t28 6.5h166q16 0 28 -12t10 -29q-14 -135 -130.5 -230t-342.5 -95q-241 0 -371 119.5t-141 344.5z" />
<glyph unicode="&#x455;" horiz-adv-x="1091" d="M76 244q0 20 13 32.5t32 12.5h168q16 0 28 -17q10 -8 46 -38.5t82.5 -48t101.5 -17.5q82 0 133 31.5t51 91.5q0 41 -23.5 66.5t-84 47t-181.5 48.5q-174 37 -257 112.5t-83 200.5q0 82 49.5 154.5t146.5 118.5t232 46q139 0 239.5 -44t153 -105.5t52.5 -108.5 q0 -18 -13.5 -31.5t-31.5 -13.5h-154q-23 0 -35 17q-14 10 -45.5 37.5t-70.5 44t-95 16.5q-76 0 -116.5 -33t-40.5 -86q0 -37 19.5 -61.5t78.5 -46t178 -43.5q369 -72 369 -322q0 -145 -126 -235t-351 -90q-156 0 -260.5 47t-154.5 109.5t-50 107.5z" />
<glyph unicode="&#x456;" horiz-adv-x="542" d="M127 1288v150q0 23 14.5 37t34.5 14h189q23 0 37 -14.5t14 -36.5v-150q0 -20 -15.5 -34.5t-35.5 -14.5h-189q-20 0 -34.5 14.5t-14.5 34.5zM135 49v967q0 23 14.5 36t34.5 13h174q20 0 35 -14.5t15 -34.5v-967q0 -20 -14.5 -34.5t-35.5 -14.5h-174q-20 0 -34.5 14.5 t-14.5 34.5z" />
<glyph unicode="&#x457;" horiz-adv-x="542" d="M-43 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM135 49v967q0 23 14.5 36t34.5 13h174q20 0 35 -14.5t15 -34.5v-967q0 -20 -14.5 -34.5t-35.5 -14.5h-174q-20 0 -34.5 14.5 t-14.5 34.5zM344 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5z" />
<glyph unicode="&#x458;" horiz-adv-x="573" d="M-90 -219q0 20 13.5 34.5t35.5 14.5h53q86 0 116 44t30 130v1012q0 23 14 36t35 13h180q23 0 36 -13.5t13 -35.5v-1018q0 -184 -100 -285.5t-297 -101.5h-80q-22 0 -35.5 13.5t-13.5 35.5v121zM147 1288v150q0 20 14.5 35.5t35.5 15.5h198q20 0 34.5 -14.5t14.5 -36.5 v-150q0 -20 -14 -34.5t-35 -14.5h-198q-21 0 -35.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x459;" horiz-adv-x="1693" d="M51 51v131q0 39 47 49q63 10 102.5 60.5t59 158t19.5 289.5v277q0 23 14 36t35 13h696q23 0 36 -13.5t13 -35.5v-287h152q205 0 311 -98.5t106 -272.5q0 -178 -110.5 -268t-323.5 -90h-358q-21 0 -35 14.5t-14 34.5v797h-262v-137q0 -266 -42 -417t-136.5 -218.5 t-258.5 -71.5q-20 -2 -35.5 12.5t-15.5 36.5zM1069 197h133q102 0 147.5 36.5t45.5 124.5t-46 131t-147 43h-133v-335z" />
<glyph unicode="&#x45a;" horiz-adv-x="1708" d="M135 49v967q0 23 14.5 36t34.5 13h174q20 0 35 -14.5t15 -34.5v-365h409v365q0 23 14.5 36t34.5 13h174q20 0 35 -14.5t15 -34.5v-287h151q205 0 310.5 -97t105.5 -274q0 -178 -110.5 -268t-323.5 -90h-357q-20 0 -34.5 14.5t-14.5 34.5v383h-409v-383q0 -20 -14.5 -34.5 t-35.5 -14.5h-174q-20 0 -34.5 14.5t-14.5 34.5zM1085 197h132q102 0 148 36.5t46 124.5t-47 131t-147 43h-132v-335z" />
<glyph unicode="&#x45b;" horiz-adv-x="1337" d="M14 1155v59q0 23 14.5 36.5t34.5 13.5h93v141q0 23 14 36t35 13h186q23 0 36 -13.5t13 -35.5v-141h305q23 0 36.5 -13.5t13.5 -36.5v-59q0 -20 -13.5 -34.5t-36.5 -14.5h-305v-264q123 147 330 147q197 0 305.5 -128t108.5 -335v-477q0 -20 -13.5 -34.5t-35.5 -14.5h-187 q-23 0 -37 14.5t-14 34.5v465q0 119 -58.5 185.5t-166.5 66.5q-106 0 -169 -66.5t-63 -185.5v-465q0 -20 -13 -34.5t-36 -14.5h-186q-21 0 -35 14.5t-14 34.5v1057h-93q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x45c;" horiz-adv-x="1173" d="M135 49v967q0 23 14.5 36t34.5 13h170q23 0 36 -13.5t13 -35.5v-365h152l244 381q12 16 24.5 24.5t30.5 8.5h188q20 0 32.5 -10.5t12.5 -26.5q0 -6 -10 -27l-291 -450l334 -488q10 -14 10 -26q0 -16 -12 -26.5t-31 -10.5h-210q-20 0 -33 7t-25 24l-266 399h-150v-381 q0 -20 -13 -34.5t-36 -14.5h-170q-20 0 -34.5 14.5t-14.5 34.5zM471 1251q0 16 11 27l167 203q18 23 34 31t42 8h207q18 0 28.5 -11.5t10.5 -29.5q0 -14 -10 -25l-256 -209q-18 -14 -33.5 -20t-38.5 -6h-129q-33 -1 -33 32z" />
<glyph unicode="&#x45d;" horiz-adv-x="1277" d="M135 47v969q0 23 14.5 36t34.5 13h174q20 0 35 -14.5t15 -34.5v-654l485 670q25 33 57 33h158q18 0 32.5 -13.5t14.5 -31.5v-971q0 -20 -13.5 -34.5t-35.5 -14.5h-174q-20 0 -34.5 14.5t-14.5 34.5v653l-486 -669q-23 -33 -57 -33h-158q-20 0 -33.5 13.5t-13.5 33.5z M266 1479q0 18 10.5 29.5t28.5 11.5h207q27 0 42 -8.5t34 -30.5l168 -203q10 -10 10 -27q0 -33 -33 -32h-129q-22 0 -37.5 6t-33.5 20l-256 209q-10 10 -11 25z" />
<glyph unicode="&#x45e;" d="M51 1020q0 18 13.5 31.5t31.5 13.5h166q39 0 55 -39l271 -670l278 670q14 39 56 39h159q17 0 30 -12.5t13 -28.5q0 -18 -8 -33l-389 -930q-63 -150 -114.5 -240.5t-127 -150t-182.5 -59.5h-92q-20 0 -34.5 14.5t-14.5 34.5v119q0 20 14 34.5t35 14.5h63q59 0 90 35 t76 139l21 51l-402 938q-8 18 -8 29zM258 1481q0 16 10.5 27.5t28.5 11.5h119q18 0 28.5 -11.5t10.5 -27.5q0 -119 137 -119q139 0 140 119q0 16 10 27.5t28 11.5h119q18 0 28.5 -11.5t10.5 -27.5q0 -123 -80 -200t-256 -77q-174 0 -254 77t-80 200z" />
<glyph unicode="&#x45f;" horiz-adv-x="1253" d="M135 49v967q0 23 14.5 36t34.5 13h174q20 0 35 -14.5t15 -34.5v-797h434v797q0 23 14 36t35 13h172q23 0 36 -13.5t13 -35.5v-967q0 -20 -13.5 -34.5t-35.5 -14.5h-303v-201q0 -20 -15.5 -34.5t-35.5 -14.5h-172q-21 0 -35.5 14.5t-14.5 34.5v201h-303q-20 0 -34.5 14.5 t-14.5 34.5z" />
<glyph unicode="&#x462;" horiz-adv-x="1431" d="M47 1122v84q0 23 12.5 35t32.5 12h203v140q0 20 14.5 34.5t36.5 14.5h189q23 0 36 -13.5t13 -35.5v-140h368q23 0 35 -12t12 -35v-84q0 -20 -13 -32.5t-34 -12.5h-368v-159h262q246 0 379 -115t133 -338q0 -133 -58.5 -238.5t-174 -166t-279.5 -60.5h-500q-23 0 -37 14.5 t-14 34.5v1028h-203q-20 0 -32.5 12.5t-12.5 32.5zM584 231h252q106 0 165.5 64.5t59.5 171.5q0 221 -225 221h-252v-457z" />
<glyph unicode="&#x463;" horiz-adv-x="1122" d="M43 989v62q0 20 14.5 34.5t34.5 14.5h103v305q0 23 14 36t35 13h174q23 0 36 -13.5t13 -35.5v-305h262q20 0 34.5 -14.5t14.5 -34.5v-62q0 -20 -13 -34.5t-36 -14.5h-262v-211h188q203 0 309.5 -98.5t106.5 -272.5q0 -178 -110.5 -268t-323.5 -90h-393q-20 0 -34.5 14.5 t-14.5 34.5v891h-103q-20 0 -34.5 14.5t-14.5 34.5zM463 197h168q102 0 148 36.5t46 124.5t-47 131t-147 43h-168v-335z" />
<glyph unicode="&#x46a;" horiz-adv-x="1875" d="M61 49q0 8 5 21l163 456q51 145 136.5 207t218.5 66h69l-489 565q-8 10 -8 27q0 16 12 29.5t31 13.5h1480q18 0 30.5 -13.5t12.5 -29.5t-10 -29l-500 -563h72q137 -4 224 -65.5t139 -207.5l163 -456q4 -12 5 -21q0 -20 -12.5 -34.5t-33.5 -14.5h-196q-47 0 -57 35 l-140 395q-27 76 -63.5 105.5t-108.5 29.5h-121v-516q0 -20 -14 -34.5t-35 -14.5h-190q-20 0 -34.5 14.5t-14.5 34.5v516h-123q-70 0 -108 -30.5t-64 -104.5l-140 -395q-10 -35 -57 -35h-197q-20 0 -32.5 14.5t-12.5 34.5zM606 1208l328 -399l334 399h-662z" />
<glyph unicode="&#x46b;" horiz-adv-x="1697" d="M12 37q0 12 11 26l217 338q68 104 137 143.5t155 39.5h72l-317 405q-10 12 -11 31q0 20 13.5 32.5t29.5 12.5h1055q18 0 30.5 -12.5t12.5 -30.5t-10 -29l-313 -409h61q92 0 163 -38t136 -138l221 -345q8 -10 8 -26t-11 -26.5t-32 -10.5h-208q-20 0 -33 8t-25 23l-198 325 q-27 45 -58 60.5t-80 15.5h-55v-383q0 -20 -13.5 -34.5t-35.5 -14.5h-170q-20 0 -34.5 14.5t-14.5 34.5v383h-56q-49 0 -80.5 -16.5t-58.5 -59.5l-196 -325q-25 -31 -58 -31h-211q-18 0 -30.5 10t-12.5 27zM616 891l234 -309l231 309h-465z" />
<glyph unicode="&#x472;" horiz-adv-x="1423" d="M111 715q0 117 2 176q8 272 167.5 417.5t432.5 145.5q270 0 431 -145.5t169 -417.5q4 -119 4 -176q0 -55 -4 -174q-8 -279 -165 -420t-435 -141q-281 0 -436.5 141t-163.5 420q-2 59 -2 174zM408 635l2 -84q4 -168 84.5 -251t218.5 -83q137 0 217 82t86 252l2 84h-610z M408 811h610l-2 72q-6 168 -88 251t-215 83q-135 0 -217 -83t-86 -251z" />
<glyph unicode="&#x473;" horiz-adv-x="1214" d="M92 532l2 91q10 215 143.5 338.5t370.5 123.5q236 0 369 -123.5t143 -338.5q2 -25 2 -91t-2 -90q-10 -217 -141 -339.5t-371 -122.5q-242 0 -373 122.5t-141 339.5zM377 436q4 -131 63.5 -199.5t167.5 -68.5q109 0 167.5 68.5t62.5 199.5v27h-461v-27zM377 602h461v27 q-4 131 -63.5 199.5t-166.5 68.5q-108 0 -167.5 -68.5t-63.5 -199.5v-27z" />
<glyph unicode="&#x474;" horiz-adv-x="1396" d="M59 1372q-6 23 5.5 42.5t35.5 19.5h183q47 0 67 -52l342 -1044l219 711q63 211 155.5 304t244.5 93h47q39 0 39 -51v-170q0 -20 -13.5 -34.5t-31.5 -14.5h-25q-55 0 -91 -46.5t-73 -164.5l-278 -908q-20 -57 -80 -57h-227q-60 0 -80 57z" />
<glyph unicode="&#x475;" horiz-adv-x="1191" d="M51 1020q0 18 13.5 31.5t29.5 13.5h170q41 0 55 -39l267 -715l188 508q49 133 110.5 188.5t182.5 57.5h57q23 0 36.5 -14.5t13.5 -36.5v-117q0 -23 -13.5 -37t-34.5 -14q-55 0 -90 -33t-65 -115l-242 -647q-23 -51 -74 -51h-147q-31 0 -47.5 13.5t-26.5 37.5l-381 950 q-2 6 -2 19z" />
<glyph unicode="&#x490;" horiz-adv-x="1114" d="M160 49v1333q0 23 13 37.5t36 14.5h600v237q0 23 14.5 36t34.5 13h189q22 0 36.5 -13t14.5 -36v-434q0 -23 -14.5 -36t-36.5 -13h-598v-1139q0 -20 -14.5 -34.5t-37.5 -14.5h-188q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x491;" horiz-adv-x="913" d="M135 49v967q0 23 14.5 36t34.5 13h428v201q0 20 14.5 34.5t35.5 14.5h172q20 0 34.5 -14.5t14.5 -34.5v-371q0 -20 -13.5 -34.5t-35.5 -14.5h-426v-797q0 -20 -14.5 -34.5t-35.5 -14.5h-174q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x492;" horiz-adv-x="1138" d="M4 662v75q0 23 14.5 36t34.5 13h107v596q0 23 13 37.5t36 14.5h838q22 0 36.5 -14.5t14.5 -37.5v-145q0 -23 -14.5 -36t-36.5 -13h-607v-402h260q23 0 37.5 -14t14.5 -35v-75q0 -20 -15.5 -35t-36.5 -15h-260v-563q0 -20 -14 -34.5t-37 -14.5h-180q-20 0 -34.5 14.5 t-14.5 34.5v563h-107q-20 0 -34.5 14.5t-14.5 35.5z" />
<glyph unicode="&#x493;" horiz-adv-x="913" d="M-8 477v76q0 23 14 36t35 13h94v414q0 23 14.5 36t34.5 13h650q22 0 35.5 -13.5t13.5 -35.5v-121q0 -20 -13.5 -34.5t-35.5 -14.5h-426v-244h213q20 0 34.5 -14.5t14.5 -34.5v-76q0 -20 -14.5 -34.5t-34.5 -14.5h-213v-379q0 -20 -14.5 -34.5t-35.5 -14.5h-174 q-20 0 -34.5 14.5t-14.5 34.5v379h-94q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x494;" horiz-adv-x="1320" d="M160 49v1333q0 23 13 37.5t36 14.5h838q23 0 37 -14.5t14 -37.5v-145q0 -23 -14.5 -36t-36.5 -13h-598v-414q55 51 140 79t191 28q234 0 350.5 -114t116.5 -337v-149q0 -213 -137 -330t-410 -117h-14q-20 0 -34.5 14.5t-14.5 34.5v127q0 20 14.5 34.5t34.5 14.5h14 q143 0 200.5 58.5t57.5 163.5v125q0 117 -57 171t-184 54q-115 0 -191.5 -44t-76.5 -159v-379q0 -20 -14.5 -34.5t-37.5 -14.5h-188q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x495;" horiz-adv-x="1110" d="M135 49v967q0 23 14.5 36t34.5 13h650q23 0 36 -13.5t13 -35.5v-121q0 -20 -13.5 -34.5t-35.5 -14.5h-445v-297q111 82 283 82q190 0 295.5 -128t105.5 -335v-49q0 -238 -125 -373t-387 -135h-33q-20 0 -34.5 13.5t-14.5 35.5v121q0 20 14.5 34.5t34.5 14.5h23 q104 0 159.5 37t74 99.5t18.5 156.5v33q0 121 -56.5 186.5t-164.5 65.5q-193 0 -193 -109v-250q0 -20 -13.5 -34.5t-35.5 -14.5h-156q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x496;" horiz-adv-x="2166" d="M61 47q0 14 7 23l424 671l-386 625q-10 14 -10 25q0 18 11.5 30.5t31.5 12.5h207q51 0 72 -37l330 -543l167 2v528q0 20 14.5 35t37.5 15h180q23 0 36 -13.5t13 -36.5v-528l166 -2l330 543q23 37 73 37h207q20 0 31.5 -12.5t11.5 -30.5q0 -14 -8 -25l-387 -625l305 -481 h162q20 0 35.5 -14.5t15.5 -34.5v-449q0 -20 -14.5 -34.5t-36.5 -14.5h-189q-20 0 -34.5 13.5t-14.5 35.5v238h-71q-31 4 -52 35l-350 561h-180v-547q0 -20 -13.5 -34.5t-35.5 -14.5h-180q-23 0 -37.5 14.5t-14.5 34.5v547h-180l-350 -561q-20 -35 -61 -35h-215 q-20 0 -34 14.5t-14 32.5z" />
<glyph unicode="&#x497;" horiz-adv-x="1794" d="M43 37q0 10 8 26l340 490l-295 448q-8 10 -8 27q0 16 11.5 26.5t31.5 10.5h186q20 0 32.5 -8t25.5 -25l243 -381h130v365q0 23 14 36t35 13h170q20 0 34.5 -14.5t14.5 -34.5v-365h123l247 381q20 33 56 33h186q20 0 32.5 -10.5t12.5 -26.5q0 -12 -10 -27l-295 -448 l231 -334h130q20 0 34.5 -14.5t14.5 -34.5v-371q0 -20 -14.5 -34.5t-34.5 -14.5h-175q-20 0 -34.5 14.5t-14.5 34.5v201h-67l-2 6q-18 8 -31 25l-266 401h-123v-383q0 -20 -14.5 -34.5t-34.5 -14.5h-170q-20 0 -34.5 14.5t-14.5 34.5v383h-125l-269 -401q-25 -31 -57 -31 h-211q-18 0 -30.5 10t-12.5 27z" />
<glyph unicode="&#x498;" horiz-adv-x="1325" d="M78 360v5q0 16 12 26t31 10h186q23 0 38 -9t24 -32q25 -68 93 -106.5t179 -38.5q127 0 209 55.5t82 149.5q0 98 -72 150.5t-201 52.5h-149q-20 0 -34.5 15t-14.5 36v121q0 23 14 38t35 15h143q109 0 172.5 46t63.5 140q0 82 -67.5 132.5t-174.5 50.5q-109 0 -177.5 -38 t-84.5 -110q-8 -20 -20.5 -29.5t-34.5 -9.5h-193q-18 0 -31.5 12.5t-11.5 30.5q14 166 153.5 273.5t403.5 107.5q164 0 285 -54t184.5 -145.5t63.5 -197.5q0 -92 -38 -173t-126 -130q106 -53 157.5 -144.5t51.5 -199.5q0 -162 -111.5 -271.5t-306.5 -144.5v-232 q0 -23 -13.5 -36t-35.5 -13h-189q-23 0 -37 14.5t-14 34.5v226q-207 25 -318.5 129t-125.5 243z" />
<glyph unicode="&#x499;" horiz-adv-x="1097" d="M80 285v2q0 14 11 24.5t28 10.5h170q37 0 53 -35q18 -57 71.5 -83t141.5 -26t142.5 35t54.5 104q0 137 -166 138h-113q-20 0 -32.5 13t-12.5 32v88q0 18 12.5 31.5t32.5 13.5h105q74 0 110.5 27.5t36.5 87.5q0 59 -44 98t-136 39t-134 -29t-63 -92q-16 -35 -53 -35h-164 q-16 0 -28.5 11.5t-10.5 25.5q4 82 52.5 154.5t148.5 118.5t252 46q227 0 334.5 -88t107.5 -213q0 -80 -29.5 -133t-99.5 -88q164 -61 164 -268q0 -117 -82 -193.5t-225 -105.5v-197q0 -20 -14.5 -34.5t-34.5 -14.5h-172q-20 0 -36 14.5t-16 34.5v187q-182 23 -268 111.5 t-94 187.5z" />
<glyph unicode="&#x49a;" horiz-adv-x="1439" d="M160 49v1333q0 23 13 37.5t36 14.5h188q23 0 37.5 -14.5t14.5 -37.5v-526l176 -2l329 543q23 37 74 37h209q20 0 31.5 -12.5t11.5 -30.5q0 -10 -10 -25l-385 -625l303 -481h174q20 0 35.5 -14.5t15.5 -34.5v-449q0 -20 -14.5 -34.5t-36.5 -14.5h-188q-20 0 -35 13.5 t-15 35.5v238h-71q-41 0 -62 35l-350 561h-192v-547q0 -20 -14.5 -34.5t-37.5 -14.5h-188q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x49b;" horiz-adv-x="1232" d="M135 49v967q0 23 14.5 36t34.5 13h170q23 0 36 -13.5t13 -35.5v-365h152l244 381q12 16 24.5 24.5t30.5 8.5h188q20 0 32.5 -10.5t12.5 -26.5q0 -6 -10 -27l-291 -450l228 -332h155q21 0 35.5 -14.5t14.5 -34.5v-371q0 -20 -14.5 -34.5t-35.5 -14.5h-174 q-20 0 -34.5 14.5t-14.5 34.5v201h-69q-20 0 -33 7t-25 24l-266 399h-150v-381q0 -20 -13 -34.5t-36 -14.5h-170q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x49c;" horiz-adv-x="1443" d="M160 49v1333q0 23 13 37.5t36 14.5h188q23 0 37.5 -14.5t14.5 -37.5v-553h110v181q0 23 14.5 36t34.5 13h15q23 0 36 -13.5t13 -35.5v-183h6l346 570q20 37 74 37h207q20 0 31.5 -12.5t11.5 -30.5q0 -8 -9 -25l-387 -625l424 -671q6 -9 6 -23q0 -18 -13 -32.5t-34 -14.5 h-215q-41 0 -61 35l-367 586h-20v-181q0 -20 -14.5 -34.5t-34.5 -14.5h-15q-20 0 -34.5 14.5t-14.5 34.5v181h-110v-572q0 -20 -14.5 -34.5t-37.5 -14.5h-188q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x49d;" horiz-adv-x="1249" d="M135 49v967q0 23 14.5 36t34.5 13h170q23 0 36 -13.5t13 -35.5v-381h91v151q0 23 14 36.5t35 13.5h14q23 0 36 -13.5t13 -36.5v-151h15l256 397q20 33 55 33h188q18 0 30.5 -10.5t12.5 -26.5q0 -12 -10 -27l-291 -450l336 -488q8 -10 8 -26t-11 -26.5t-32 -10.5h-209 q-37 0 -57 31l-279 415h-12v-163q0 -20 -13 -35t-36 -15h-14q-20 0 -34.5 14.5t-14.5 35.5v163h-91v-397q0 -20 -13 -34.5t-36 -14.5h-170q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x4a0;" horiz-adv-x="1691" d="M47 1229v153q0 23 13.5 37.5t35.5 14.5h617q23 0 37 -14.5t14 -37.5v-526l174 -2l332 543q23 37 73 37h209q18 0 29.5 -12.5t11.5 -30.5q0 -14 -8 -25l-387 -625l426 -671q4 -7 4 -23q0 -18 -13 -32.5t-32 -14.5h-215q-39 0 -63 35l-355 561h-186v-547q0 -20 -14.5 -34.5 t-36.5 -14.5h-189q-20 0 -34.5 14.5t-14.5 34.5v1129h-379q-20 0 -34.5 14t-14.5 37z" />
<glyph unicode="&#x4a1;" horiz-adv-x="1423" d="M29 895v121q0 23 14 36t35 13h526q23 0 36 -13.5t13 -35.5v-365h148l250 381q20 33 55 33h188q18 0 30.5 -10.5t12.5 -26.5q0 -12 -10 -27l-293 -448l338 -490q8 -10 8 -26t-11 -26.5t-32 -10.5h-209q-35 0 -59 31l-268 399h-148v-381q0 -20 -13 -34.5t-36 -14.5h-168 q-23 0 -37 14.5t-14 34.5v797h-307q-20 0 -34.5 14t-14.5 35z" />
<glyph unicode="&#x4a2;" horiz-adv-x="1552" d="M160 49v1333q0 23 13 37.5t36 14.5h188q23 0 37.5 -14.5t14.5 -37.5v-526h598v526q0 23 14 37.5t35 14.5h190q23 0 36 -14.5t13 -37.5v-1122h99q20 0 35.5 -14.5t15.5 -34.5v-449q0 -20 -14.5 -34.5t-36.5 -14.5h-189q-20 0 -34.5 13.5t-14.5 35.5v238h-100 q-20 0 -34.5 14.5t-14.5 34.5v547h-598v-547q0 -20 -14.5 -34.5t-37.5 -14.5h-188q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x4a3;" horiz-adv-x="1329" d="M135 49v967q0 23 14.5 36t34.5 13h174q20 0 35 -14.5t15 -34.5v-365h456v365q0 20 14.5 34.5t34.5 14.5h174q23 0 36.5 -13.5t13.5 -35.5v-797h92q20 0 34.5 -14.5t14.5 -34.5v-371q0 -20 -14.5 -34.5t-34.5 -14.5h-174q-20 0 -34.5 14.5t-14.5 34.5v201h-93 q-20 0 -34.5 14.5t-14.5 34.5v383h-456v-383q0 -20 -14.5 -34.5t-35.5 -14.5h-174q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x4a4;" horiz-adv-x="1912" d="M160 49v1333q0 23 13 37.5t36 14.5h188q23 0 37.5 -14.5t14.5 -37.5v-526h598v526q0 23 14 37.5t35 14.5h749q23 0 36 -14.5t13 -37.5v-145q0 -20 -13 -34.5t-36 -14.5h-510v-1139q0 -20 -13 -34.5t-36 -14.5h-190q-20 0 -34.5 14.5t-14.5 34.5v547h-598v-547 q0 -20 -14.5 -34.5t-37.5 -14.5h-188q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x4a5;" horiz-adv-x="1564" d="M135 49v967q0 23 14.5 36t34.5 13h174q20 0 35 -14.5t15 -34.5v-365h456v365q0 20 14.5 34.5t34.5 14.5h574q22 0 35.5 -13.5t13.5 -35.5v-121q0 -20 -13.5 -34.5t-35.5 -14.5h-350v-797q0 -20 -13.5 -34.5t-36.5 -14.5h-174q-20 0 -34.5 14.5t-14.5 34.5v383h-456v-383 q0 -20 -14.5 -34.5t-35.5 -14.5h-174q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x4aa;" horiz-adv-x="1402" d="M109 715q0 127 2 184q10 262 165.5 408.5t436.5 146.5q182 0 317 -62.5t210 -171t79 -243.5v-4q0 -16 -13.5 -27.5t-29.5 -11.5h-199q-25 0 -37 11t-20 40q-31 127 -106.5 179.5t-200.5 52.5q-293 0 -305 -328q-2 -55 -2 -170t2 -174q12 -328 305 -328q123 0 199.5 52.5 t107.5 179.5q8 29 20.5 40t36.5 11h199q18 0 31.5 -12.5t11.5 -30.5q-4 -178 -126 -304t-333 -161v-230q0 -23 -14.5 -36t-36.5 -13h-188q-20 0 -35 13.5t-15 35.5v228q-217 35 -333.5 175t-126.5 370q-2 55 -2 180z" />
<glyph unicode="&#x4ab;" d="M92 532l2 82q8 221 142.5 346t359.5 125q162 0 273.5 -57t166 -140t58.5 -159q2 -20 -13.5 -34.5t-36.5 -14.5h-182q-23 0 -33 9t-20 34q-31 78 -81 112.5t-128 34.5q-104 0 -162.5 -65.5t-62.5 -200.5l-2 -76l2 -67q12 -266 225 -266q80 0 129 33.5t80 113.5 q8 23 19.5 33t33.5 10h182q21 0 36.5 -14.5t13.5 -34.5q-4 -59 -42 -129t-117 -129.5t-196 -83.5v-195q0 -20 -14 -34.5t-35 -14.5h-174q-20 0 -34.5 14.5t-14.5 34.5v193q-170 33 -268.5 151.5t-104.5 307.5z" />
<glyph unicode="&#x4ae;" horiz-adv-x="1335" d="M33 1391q0 18 12 30.5t31 12.5h184q39 0 64 -39l344 -594l344 594q25 39 63 39h185q16 0 29.5 -12.5t13.5 -30.5q0 -4 -7 -23l-483 -858v-461q0 -20 -14.5 -34.5t-36.5 -14.5h-191q-20 0 -34.5 14.5t-14.5 34.5v461l-483 858q-6 10 -6 23z" />
<glyph unicode="&#x4af;" horiz-adv-x="1136" d="M31 1020q0 18 12 31.5t31 13.5h170q41 0 55 -39l268 -715l271 715q14 39 55 39h170q16 0 29.5 -13.5t13.5 -31.5q0 -13 -2 -19l-397 -981v-360q0 -20 -14.5 -34.5t-37.5 -14.5h-178q-20 0 -34.5 14.5t-14.5 34.5v365l-395 976q-2 6 -2 19z" />
<glyph unicode="&#x4b0;" horiz-adv-x="1361" d="M33 1391q0 18 12 30.5t31 12.5h184q39 0 64 -39l344 -594l344 594q25 39 63 39h185q16 0 29.5 -12.5t13.5 -30.5q0 -4 -7 -23l-483 -858v-461q0 -20 -14.5 -34.5t-36.5 -14.5h-191q-20 0 -34.5 14.5t-14.5 34.5v461l-483 858q-6 10 -6 23zM635 487q4 0 4 -2v2h-4z M635 524h4v4q0 -4 -4 -4zM670 485q0 2 4 2h-4v-2zM670 524h4q-4 0 -4 4v-4z" />
<glyph unicode="&#x4b1;" horiz-adv-x="1136" d="M31 1020q0 18 12 31.5t31 13.5h170q41 0 55 -39l268 -715l271 715q14 39 55 39h170q16 0 29.5 -13.5t13.5 -31.5q0 -13 -2 -19l-379 -933h131q20 0 34.5 -14.5t14.5 -35.5v-75q0 -20 -14 -34.5t-35 -14.5h-149v-234q0 -20 -14.5 -34.5t-37.5 -14.5h-178q-20 0 -34.5 14.5 t-14.5 34.5v234h-152q-20 0 -34.5 14t-14.5 35v75q0 23 14.5 36.5t34.5 13.5h136l-379 933q-2 6 -2 19z" />
<glyph unicode="&#x4b6;" horiz-adv-x="1437" d="M104 924v460q0 23 14.5 36.5t35.5 13.5h188q23 0 37 -13.5t14 -36.5v-446q0 -121 61.5 -176t204.5 -55q102 0 187.5 37.5t85.5 125.5v514q0 23 14.5 36.5t34.5 13.5h188q23 0 37.5 -13.5t14.5 -36.5v-1124h100q20 0 35.5 -14.5t15.5 -34.5v-449q0 -20 -14.5 -34.5 t-36.5 -14.5h-188q-20 0 -35 13.5t-15 35.5v238h-102q-20 0 -34.5 14.5t-14.5 34.5v481q-45 -45 -137.5 -67.5t-190.5 -22.5q-254 0 -378 121t-122 363z" />
<glyph unicode="&#x4b7;" horiz-adv-x="1224" d="M76 686v330q0 23 14 36t35 13h178q23 0 36 -13.5t13 -35.5v-316q0 -82 43 -116.5t142 -34.5q223 0 223 119v348q0 23 14 36t35 13h186q23 0 36 -13.5t13 -35.5v-797h80q21 0 35.5 -14.5t14.5 -34.5v-371q0 -20 -14.5 -34.5t-35.5 -14.5h-174q-20 0 -34.5 14.5t-14.5 34.5 v201h-92q-20 0 -34.5 14.5t-14.5 34.5v348q-100 -88 -295 -88q-193 0 -291 91.5t-98 285.5z" />
<glyph unicode="&#x4b8;" horiz-adv-x="1413" d="M104 924v460q0 23 14.5 36.5t35.5 13.5h188q23 0 37 -13.5t14 -36.5v-479q0 -115 58.5 -171t189.5 -62v198q0 23 14.5 36.5t34.5 13.5h15q23 0 37 -14.5t14 -35.5v-192q86 10 144 49t58 111v546q0 23 14.5 36.5t35.5 13.5h188q23 0 37 -14.5t14 -35.5v-1335 q0 -20 -14 -34.5t-37 -14.5h-188q-20 0 -35 14.5t-15 34.5v481q-61 -59 -202 -79v-158q0 -20 -14.5 -34.5t-36.5 -14.5h-15q-20 0 -34.5 14t-14.5 35v147h-20q-266 0 -391.5 120t-125.5 364z" />
<glyph unicode="&#x4b9;" horiz-adv-x="1224" d="M76 686v330q0 23 14 36t35 13h178q23 0 36 -13.5t13 -35.5v-324q0 -156 174 -162v152q0 23 14.5 36t34.5 13h15q23 0 36 -13t13 -36v-145q160 20 160 122v357q0 23 14 36t35 13h186q23 0 36 -13.5t13 -35.5v-967q0 -20 -13 -34.5t-36 -14.5h-186q-20 0 -34.5 14.5 t-14.5 34.5v365q-66 -51 -160 -72v-129q0 -20 -13.5 -34.5t-35.5 -14.5h-15q-20 0 -34.5 14.5t-14.5 34.5v115q-18 -2 -57 -2q-184 0 -288.5 90t-104.5 270z" />
<glyph unicode="&#x4ba;" horiz-adv-x="1388" d="M168 49v1335q0 23 14.5 36.5t36.5 13.5h189q22 0 35.5 -13.5t13.5 -36.5v-481q45 45 137 67.5t190 22.5q254 0 378 -120.5t122 -362.5v-461q0 -20 -13.5 -34.5t-35.5 -14.5h-188q-23 0 -37.5 13.5t-14.5 35.5v447q0 121 -61.5 176t-204.5 55q-102 0 -187 -38t-85 -126 v-514q0 -20 -13.5 -34.5t-35.5 -14.5h-189q-23 0 -37 13.5t-14 35.5z" />
<glyph unicode="&#x4bb;" horiz-adv-x="1290" d="M137 49v1356q0 23 14.5 36t34.5 13h187q23 0 36 -13.5t13 -35.5v-467q123 147 330 147q188 0 300.5 -123.5t112.5 -336.5v-576q0 -20 -13 -34.5t-36 -14.5h-186q-23 0 -37 14.5t-14 34.5v563q0 119 -58.5 185.5t-167.5 66.5q-104 0 -167.5 -67.5t-63.5 -184.5v-563 q0 -20 -13.5 -34.5t-35.5 -14.5h-187q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x4c0;" horiz-adv-x="614" d="M160 49v1335q0 23 14 36.5t35 13.5h197q23 0 36 -13.5t13 -36.5v-1335q0 -20 -13.5 -34.5t-35.5 -14.5h-197q-21 0 -35 14.5t-14 34.5z" />
<glyph unicode="&#x4c1;" horiz-adv-x="2113" d="M61 47q0 14 7 23l424 671l-386 625q-10 14 -10 25q0 18 11.5 30.5t31.5 12.5h207q51 0 72 -37l330 -543l167 2v528q0 20 14.5 35t37.5 15h180q23 0 36 -13.5t13 -36.5v-528l166 -2l330 543q23 37 73 37h207q20 0 31.5 -12.5t11.5 -30.5q0 -14 -8 -25l-387 -625l426 -671 q4 -7 4 -23q0 -18 -13.5 -32.5t-31.5 -14.5h-215q-25 0 -38 9t-26 26l-350 561h-180v-547q0 -20 -13.5 -34.5t-35.5 -14.5h-180q-23 0 -37.5 14.5t-14.5 34.5v547h-180l-350 -561q-20 -35 -61 -35h-215q-20 0 -34 14.5t-14 32.5zM715 1792q0 16 10 27.5t29 11.5h119 q18 0 28.5 -11.5t10.5 -27.5q0 -117 145 -117q143 0 143 117q0 16 10.5 27.5t28.5 11.5h119q18 0 28.5 -11.5t10.5 -27.5q0 -123 -81 -199.5t-259 -76.5t-260 76.5t-82 199.5z" />
<glyph unicode="&#x4c2;" horiz-adv-x="1759" d="M43 37q0 10 8 26l340 490l-295 448q-8 10 -8 27q0 16 11.5 26.5t31.5 10.5h186q20 0 32.5 -8t25.5 -25l243 -381h130v365q0 23 14 36t35 13h170q20 0 34.5 -14.5t14.5 -34.5v-365h123l247 381q20 33 56 33h186q20 0 32.5 -10.5t12.5 -26.5q0 -12 -10 -27l-295 -448 l340 -490q8 -10 8 -26t-11 -26.5t-32 -10.5h-211q-31 0 -57 31l-266 401h-123v-383q0 -20 -14.5 -34.5t-34.5 -14.5h-170q-20 0 -34.5 14.5t-14.5 34.5v383h-125l-269 -401q-25 -31 -57 -31h-211q-18 0 -30.5 10t-12.5 27zM546 1481q0 16 10.5 27.5t28.5 11.5h119 q18 0 28.5 -11.5t10.5 -27.5q0 -119 137 -119q139 0 140 119q0 16 10 27.5t28 11.5h119q18 0 28.5 -11.5t10.5 -27.5q0 -123 -80 -200t-256 -77q-174 0 -254 77t-80 200z" />
<glyph unicode="&#x4cb;" horiz-adv-x="1388" d="M104 924v460q0 23 14.5 36.5t35.5 13.5h188q23 0 37 -13.5t14 -36.5v-446q0 -121 61.5 -176t204.5 -55q102 0 187.5 37.5t85.5 125.5v514q0 23 14.5 36.5t34.5 13.5h188q23 0 37.5 -13.5t14.5 -36.5v-1335q0 -23 -14.5 -36t-37.5 -13h-98v-238q0 -23 -13.5 -36t-35.5 -13 h-188q-20 0 -35 13.5t-15 35.5v363q0 23 14.5 36t35.5 13h98v356q-45 -45 -137.5 -67.5t-190.5 -22.5q-254 0 -378 121t-122 363z" />
<glyph unicode="&#x4cc;" horiz-adv-x="1165" d="M76 686v330q0 23 14 36t35 13h178q23 0 36 -13.5t13 -35.5v-316q0 -82 43 -116.5t142 -34.5q223 0 223 119v348q0 23 14 36t35 13h186q23 0 36 -13.5t13 -35.5v-967q0 -20 -13 -34.5t-36 -14.5h-112v-201q0 -20 -14.5 -34.5t-34.5 -14.5h-172q-20 0 -35 14.5t-15 34.5 v312q0 20 14.5 34.5t35.5 14.5h98v237q-100 -88 -295 -88q-193 0 -291 91.5t-98 285.5z" />
<glyph unicode="&#x4cf;" horiz-adv-x="544" d="M137 49v1356q0 23 14.5 36t34.5 13h172q23 0 36.5 -13.5t13.5 -35.5v-1356q0 -20 -13.5 -34.5t-36.5 -14.5h-172q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x4d0;" horiz-adv-x="1421" d="M29 43l4 18l491 1321q20 51 74 52h225q53 0 74 -52l492 -1321l4 -18q0 -18 -13.5 -30.5t-29.5 -12.5h-183q-43 0 -59 39l-94 248h-606l-95 -248q-16 -39 -59 -39h-182q-19 0 -31 12.5t-12 30.5zM369 1792q0 16 10 27.5t29 11.5h119q18 0 28.5 -11.5t10.5 -27.5 q0 -117 145 -117q143 0 143 117q0 16 10.5 27.5t28.5 11.5h119q18 0 28.5 -11.5t10.5 -27.5q0 -123 -81 -199.5t-259 -76.5t-260 76.5t-82 199.5zM479 524h463l-231 631z" />
<glyph unicode="&#x4d1;" d="M66 293q0 139 112.5 225t308.5 117l281 41v43q0 88 -45 133t-143 45q-68 0 -109 -24.5t-66.5 -50t-33.5 -34.5q-8 -25 -31 -24h-160q-18 0 -31.5 12t-13.5 33q2 51 50 115.5t148.5 112.5t248.5 48q242 0 352 -108.5t110 -284.5v-643q0 -20 -13 -34.5t-36 -14.5h-170 q-20 0 -34.5 14.5t-14.5 34.5v80q-45 -66 -127 -107.5t-205 -41.5q-104 0 -191 41.5t-137 113.5t-50 158zM270 1481q0 16 10.5 27.5t28.5 11.5h119q18 0 28.5 -11.5t10.5 -27.5q0 -119 137 -119q139 0 140 119q0 16 10 27.5t28 11.5h119q18 0 28.5 -11.5t10.5 -27.5 q0 -123 -80 -200t-256 -77q-174 0 -254 77t-80 200zM330 313q0 -66 54 -101.5t130 -35.5q113 0 184.5 74t71.5 213v41l-205 -33q-236 -37 -235 -158z" />
<glyph unicode="&#x4d2;" horiz-adv-x="1421" d="M29 43l4 18l491 1321q20 51 74 52h225q53 0 74 -52l492 -1321l4 -18q0 -18 -13.5 -30.5t-29.5 -12.5h-183q-43 0 -59 39l-94 248h-606l-95 -248q-16 -39 -59 -39h-182q-19 0 -31 12.5t-12 30.5zM395 1672v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151 q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM479 524h463l-231 631zM782 1672v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5z" />
<glyph unicode="&#x4d3;" d="M66 293q0 139 112.5 225t308.5 117l281 41v43q0 88 -45 133t-143 45q-68 0 -109 -24.5t-66.5 -50t-33.5 -34.5q-8 -25 -31 -24h-160q-18 0 -31.5 12t-13.5 33q2 51 50 115.5t148.5 112.5t248.5 48q242 0 352 -108.5t110 -284.5v-643q0 -20 -13 -34.5t-36 -14.5h-170 q-20 0 -34.5 14.5t-14.5 34.5v80q-45 -66 -127 -107.5t-205 -41.5q-104 0 -191 41.5t-137 113.5t-50 158zM288 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM330 313q0 -66 54 -101.5 t130 -35.5q113 0 184.5 74t71.5 213v41l-205 -33q-236 -37 -235 -158zM675 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5z" />
<glyph unicode="&#x4d4;" horiz-adv-x="1955" d="M10 43q0 6 4 23q61 176 180 433t449 883q20 51 70 52h1079q23 0 37 -14.5t14 -37.5v-137q0 -23 -14 -36t-37 -13h-647v-358h602q23 0 37 -14.5t14 -37.5v-129q0 -23 -14.5 -36t-36.5 -13h-602v-370h663q23 0 37.5 -13.5t14.5 -36.5v-139q0 -23 -14.5 -36t-37.5 -13h-895 q-20 0 -34.5 14.5t-14.5 34.5v238h-438q-117 -211 -129 -248q-18 -39 -59 -39h-187q-16 0 -28.5 12.5t-12.5 30.5zM510 524h354v664h-26z" />
<glyph unicode="&#x4d5;" horiz-adv-x="1851" d="M66 293q0 139 113.5 227t309.5 119l279 39v41q0 88 -44 133t-144 45q-68 0 -109 -24.5t-66.5 -50t-33.5 -34.5q-8 -25 -31 -24h-160q-18 0 -31.5 12t-13.5 33q0 49 49 114.5t150.5 113.5t247.5 48q252 0 346 -137q131 137 340 137q182 0 293.5 -79.5t158.5 -201.5 t47 -259v-43q0 -20 -14 -34.5t-37 -14.5h-667v-17q4 -133 65.5 -198.5t151.5 -65.5q57 0 106 23.5t84 70.5q16 20 28.5 26.5t35.5 6.5h176q18 0 30.5 -11.5t12.5 -31.5q0 -45 -55.5 -111.5t-163 -115.5t-252.5 -49q-260 0 -394 184q-135 -184 -411 -184q-119 0 -209 40.5 t-139 112.5t-49 160zM332 313q0 -66 53 -101.5t131 -35.5q113 0 184.5 74t71.5 213v41l-205 -33q-236 -37 -235 -158zM1049 627h436v8q0 121 -55.5 191.5t-161.5 70.5q-90 0 -152.5 -67.5t-66.5 -194.5v-8z" />
<glyph unicode="&#x4d6;" horiz-adv-x="1275" d="M160 49v1333q0 23 13 37.5t36 14.5h903q23 0 37 -14.5t14 -37.5v-137q0 -23 -14 -36t-37 -13h-672v-358h627q23 0 37 -14.5t14 -37.5v-129q0 -23 -14 -36t-37 -13h-627v-370h688q23 0 37.5 -13.5t14.5 -36.5v-139q0 -23 -14.5 -36t-37.5 -13h-919q-21 0 -35 14.5 t-14 34.5zM338 1792q0 16 10 27.5t29 11.5h119q18 0 28.5 -11.5t10.5 -27.5q0 -117 145 -117q143 0 143 117q0 16 10.5 27.5t28.5 11.5h119q18 0 28.5 -11.5t10.5 -27.5q0 -123 -81 -199.5t-259 -76.5t-260 76.5t-82 199.5z" />
<glyph unicode="&#x4d7;" horiz-adv-x="1183" d="M88 535q0 256 134 403t372 147q244 0 376 -147t132 -393v-43q0 -20 -14.5 -34.5t-36.5 -14.5h-680v-17q4 -115 62.5 -189.5t160.5 -74.5q117 0 190 94q18 23 28.5 28t35.5 5h180q18 0 31.5 -10t13.5 -29q0 -49 -58.5 -115.5t-167 -115.5t-251.5 -49q-229 0 -363.5 131 t-142.5 372zM266 1481q0 16 10.5 27.5t28.5 11.5h119q18 0 28.5 -11.5t10.5 -27.5q0 -119 137 -119q139 0 140 119q0 16 10 27.5t28 11.5h119q18 0 28.5 -11.5t10.5 -27.5q0 -123 -80 -200t-256 -77q-174 0 -254 77t-80 200zM371 627h448v4q0 121 -60.5 193.5t-164.5 72.5 t-163.5 -72.5t-59.5 -193.5v-4z" />
<glyph unicode="&#x4d8;" horiz-adv-x="1415" d="M109 692v45q0 29 20.5 47.5t48.5 18.5h832l-2 86q-12 328 -306 328q-115 0 -184 -41t-96 -146q-8 -27 -20.5 -38t-36.5 -11h-191q-18 0 -31.5 12.5t-11.5 30.5q4 113 73.5 211t197.5 158.5t300 60.5q281 0 436.5 -146.5t166.5 -408.5q4 -114 4 -184t-4 -180 q-10 -264 -164 -409.5t-430 -145.5q-285 0 -443.5 186t-158.5 526zM408 600q0 -190 76.5 -286.5t226.5 -96.5q285 0 297 328l2 86h-602v-31z" />
<glyph unicode="&#x4d9;" horiz-adv-x="1183" d="M82 520v43q0 20 14.5 34.5t36.5 14.5h680v17q-4 115 -62.5 189.5t-160.5 74.5q-117 0 -191 -94q-18 -23 -28.5 -28t-34.5 -5h-180q-18 0 -31.5 10t-13.5 29q0 49 58 115.5t166.5 115.5t252.5 49q229 0 363 -131t143 -372q2 -12 2 -52q0 -256 -134.5 -403t-371.5 -147 q-244 0 -376 147t-132 393zM365 434q0 -121 60 -193.5t165 -72.5q104 0 163.5 72.5t59.5 193.5v4h-448v-4z" />
<glyph unicode="&#x4dc;" horiz-adv-x="2113" d="M61 47q0 14 7 23l424 671l-386 625q-10 14 -10 25q0 18 11.5 30.5t31.5 12.5h207q51 0 72 -37l330 -543l167 2v528q0 20 14.5 35t37.5 15h180q23 0 36 -13.5t13 -36.5v-528l166 -2l330 543q23 37 73 37h207q20 0 31.5 -12.5t11.5 -30.5q0 -14 -8 -25l-387 -625l426 -671 q4 -7 4 -23q0 -18 -13.5 -32.5t-31.5 -14.5h-215q-25 0 -38 9t-26 26l-350 561h-180v-547q0 -20 -13.5 -34.5t-35.5 -14.5h-180q-23 0 -37.5 14.5t-14.5 34.5v547h-180l-350 -561q-20 -35 -61 -35h-215q-20 0 -34 14.5t-14 32.5zM741 1672v151q0 20 12.5 33.5t32.5 13.5h152 q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM1128 1672v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5z" />
<glyph unicode="&#x4dd;" horiz-adv-x="1759" d="M43 37q0 10 8 26l340 490l-295 448q-8 10 -8 27q0 16 11.5 26.5t31.5 10.5h186q20 0 32.5 -8t25.5 -25l243 -381h130v365q0 23 14 36t35 13h170q20 0 34.5 -14.5t14.5 -34.5v-365h123l247 381q20 33 56 33h186q20 0 32.5 -10.5t12.5 -26.5q0 -12 -10 -27l-295 -448 l340 -490q8 -10 8 -26t-11 -26.5t-32 -10.5h-211q-31 0 -57 31l-266 401h-123v-383q0 -20 -14.5 -34.5t-34.5 -14.5h-170q-20 0 -34.5 14.5t-14.5 34.5v383h-125l-269 -401q-25 -31 -57 -31h-211q-18 0 -30.5 10t-12.5 27zM565 1303v151q0 20 12.5 33.5t32.5 13.5h152 q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM952 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5z" />
<glyph unicode="&#x4de;" horiz-adv-x="1325" d="M78 360v5q0 16 12 26t31 10h186q23 0 38 -9t24 -32q25 -68 93 -106.5t179 -38.5q127 0 209 55.5t82 149.5q0 98 -72 150.5t-201 52.5h-149q-20 0 -34.5 15t-14.5 36v121q0 23 14 38t35 15h143q109 0 172.5 46t63.5 140q0 82 -67.5 132.5t-174.5 50.5q-109 0 -177.5 -38 t-84.5 -110q-8 -20 -20.5 -29.5t-34.5 -9.5h-193q-18 0 -31.5 12.5t-11.5 30.5q14 166 153.5 273.5t403.5 107.5q164 0 285 -54t184.5 -145.5t63.5 -197.5q0 -92 -38 -173t-126 -130q106 -53 157.5 -144.5t51.5 -199.5q0 -127 -72 -224.5t-203 -151.5t-307 -54 q-180 0 -305 53t-189.5 139t-74.5 188zM336 1672v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM723 1672v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33 t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5z" />
<glyph unicode="&#x4df;" horiz-adv-x="1097" d="M80 285v2q0 14 11 24.5t28 10.5h170q37 0 53 -35q18 -57 71.5 -83t141.5 -26t142.5 35t54.5 104q0 137 -166 138h-113q-20 0 -32.5 13t-12.5 32v88q0 18 12.5 31.5t32.5 13.5h105q74 0 110.5 27.5t36.5 87.5q0 59 -44 98t-136 39t-134 -29t-63 -92q-16 -35 -53 -35h-164 q-16 0 -28.5 11.5t-10.5 25.5q4 82 52.5 154.5t148.5 118.5t252 46q227 0 334.5 -88t107.5 -213q0 -80 -29.5 -133t-99.5 -88q164 -61 164 -268q0 -150 -129 -232.5t-348 -82.5q-164 0 -265.5 49t-147.5 117.5t-52 138.5zM241 1303v151q0 20 12.5 33.5t32.5 13.5h152 q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM628 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5z" />
<glyph unicode="&#x4e2;" horiz-adv-x="1558" d="M160 51v1331q0 23 13 37.5t36 14.5h190q20 0 35 -14.5t15 -37.5v-936l669 953q18 35 60 35h172q23 0 36 -14.5t13 -37.5v-1333q0 -20 -13.5 -34.5t-35.5 -14.5h-189q-23 0 -37 14.5t-14 34.5v916l-668 -930q-20 -35 -59 -35h-174q-23 0 -36 14.5t-13 36.5zM475 1665v107 q0 20 12 32.5t33 12.5h516q20 0 33.5 -12.5t13.5 -32.5v-107q0 -20 -13.5 -32.5t-33.5 -12.5h-516q-21 0 -33 12.5t-12 32.5z" />
<glyph unicode="&#x4e3;" horiz-adv-x="1277" d="M135 47v969q0 23 14.5 36t34.5 13h174q20 0 35 -14.5t15 -34.5v-654l485 670q25 33 57 33h158q18 0 32.5 -13.5t14.5 -31.5v-971q0 -20 -13.5 -34.5t-35.5 -14.5h-174q-20 0 -34.5 14.5t-14.5 34.5v653l-486 -669q-23 -33 -57 -33h-158q-20 0 -33.5 13.5t-13.5 33.5z M352 1296v107q0 20 12 32.5t33 12.5h516q20 0 33.5 -12.5t13.5 -32.5v-107q0 -20 -13.5 -32.5t-33.5 -12.5h-516q-21 0 -33 12.5t-12 32.5z" />
<glyph unicode="&#x4e4;" horiz-adv-x="1558" d="M160 51v1331q0 23 13 37.5t36 14.5h190q20 0 35 -14.5t15 -37.5v-936l669 953q18 35 60 35h172q23 0 36 -14.5t13 -37.5v-1333q0 -20 -13.5 -34.5t-35.5 -14.5h-189q-23 0 -37 14.5t-14 34.5v916l-668 -930q-20 -35 -59 -35h-174q-23 0 -36 14.5t-13 36.5zM465 1672v151 q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM852 1672v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5z" />
<glyph unicode="&#x4e5;" horiz-adv-x="1277" d="M135 47v969q0 23 14.5 36t34.5 13h174q20 0 35 -14.5t15 -34.5v-654l485 670q25 33 57 33h158q18 0 32.5 -13.5t14.5 -31.5v-971q0 -20 -13.5 -34.5t-35.5 -14.5h-174q-20 0 -34.5 14.5t-14.5 34.5v653l-486 -669q-23 -33 -57 -33h-158q-20 0 -33.5 13.5t-13.5 33.5z M342 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM729 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5 t-12.5 33.5z" />
<glyph unicode="&#x4e6;" horiz-adv-x="1423" d="M111 715q0 117 2 176q8 272 167.5 417.5t432.5 145.5q270 0 431 -145.5t169 -417.5q4 -119 4 -176q0 -55 -4 -174q-8 -279 -165 -420t-435 -141q-281 0 -436.5 141t-163.5 420q-2 59 -2 174zM397 1672v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151 q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM408 717q0 -104 2 -166q4 -168 84.5 -251t218.5 -83q137 0 217 82t86 252q4 123 4 166q0 47 -4 166q-6 168 -88 251t-215 83q-135 0 -217 -83t-86 -251q-2 -59 -2 -166zM784 1672v151q0 20 12.5 33.5 t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5z" />
<glyph unicode="&#x4e7;" horiz-adv-x="1214" d="M92 532l2 91q10 215 143.5 338.5t370.5 123.5q236 0 369 -123.5t143 -338.5q2 -25 2 -91t-2 -90q-10 -217 -141 -339.5t-371 -122.5q-241 0 -372.5 122.5t-141.5 339.5zM293 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33 t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM375 532l2 -79q4 -131 63.5 -199t167.5 -68q109 0 167.5 68t62.5 199q2 20 2 79t-2 80q-4 131 -63.5 200t-166.5 69q-109 0 -168 -69t-63 -200zM680 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151 q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5z" />
<glyph unicode="&#x4e8;" horiz-adv-x="1423" d="M111 715q0 117 2 176q8 272 167.5 417.5t432.5 145.5q270 0 431 -145.5t169 -417.5q4 -119 4 -176q0 -55 -4 -174q-8 -279 -165 -420t-435 -141q-281 0 -436.5 141t-163.5 420q-2 59 -2 174zM408 631l2 -80q4 -168 84.5 -251t218.5 -83q137 0 217 82t86 252l2 80h-610z M408 803h610l-2 80q-6 168 -88 251t-215 83q-135 0 -217 -83t-86 -251z" />
<glyph unicode="&#x4e9;" horiz-adv-x="1214" d="M92 532l2 91q10 215 143.5 338.5t370.5 123.5q236 0 369 -123.5t143 -338.5q2 -25 2 -91t-2 -90q-10 -217 -141 -339.5t-371 -122.5q-242 0 -373 122.5t-141 339.5zM377 436q4 -131 63.5 -199.5t167.5 -68.5q109 0 167.5 68.5t62.5 199.5v27h-461v-27zM377 602h461v27 q-4 131 -63.5 199.5t-166.5 68.5q-108 0 -167.5 -68.5t-63.5 -199.5v-27z" />
<glyph unicode="&#x4ee;" horiz-adv-x="1271" d="M23 1389q0 18 13 31.5t32 13.5h204q35 0 54 -39l360 -672l293 672q14 39 57 39h193q18 0 30.5 -12.5t12.5 -28.5q0 -20 -8 -35l-412 -940q-72 -164 -131 -250t-144 -127t-223 -41h-96q-20 0 -34.5 14.5t-14.5 34.5v156q0 20 14.5 34.5t34.5 14.5h76q70 0 118 37t93 123 l-518 954q-4 16 -4 21zM352 1665v107q0 20 12 32.5t33 12.5h516q20 0 33.5 -12.5t13.5 -32.5v-107q0 -20 -13.5 -32.5t-33.5 -12.5h-516q-21 0 -33 12.5t-12 32.5z" />
<glyph unicode="&#x4ef;" d="M51 1020q0 18 13.5 31.5t31.5 13.5h166q39 0 55 -39l271 -670l278 670q14 39 56 39h159q17 0 30 -12.5t13 -28.5q0 -18 -8 -33l-389 -930q-63 -150 -114.5 -240.5t-127 -150t-182.5 -59.5h-92q-20 0 -34.5 14.5t-14.5 34.5v119q0 20 14 34.5t35 14.5h63q59 0 90 35 t76 139l21 51l-402 938q-8 18 -8 29zM287 1296v107q0 20 12 32.5t33 12.5h516q20 0 33.5 -12.5t13.5 -32.5v-107q0 -20 -13.5 -32.5t-33.5 -12.5h-516q-21 0 -33 12.5t-12 32.5z" />
<glyph unicode="&#x4f0;" horiz-adv-x="1271" d="M23 1389q0 18 13 31.5t32 13.5h204q35 0 54 -39l360 -672l293 672q14 39 57 39h193q18 0 30.5 -12.5t12.5 -28.5q0 -20 -8 -35l-412 -940q-72 -164 -131 -250t-144 -127t-223 -41h-96q-20 0 -34.5 14.5t-14.5 34.5v156q0 20 14.5 34.5t34.5 14.5h76q70 0 118 37t93 123 l-518 954q-4 16 -4 21zM342 1672v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM729 1672v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152 q-20 0 -32.5 12.5t-12.5 33.5z" />
<glyph unicode="&#x4f1;" d="M51 1020q0 18 13.5 31.5t31.5 13.5h166q39 0 55 -39l271 -670l278 670q14 39 56 39h159q17 0 30 -12.5t13 -28.5q0 -18 -8 -33l-389 -930q-63 -150 -114.5 -240.5t-127 -150t-182.5 -59.5h-92q-20 0 -34.5 14.5t-14.5 34.5v119q0 20 14 34.5t35 14.5h63q59 0 90 35 t76 139l21 51l-402 938q-8 18 -8 29zM276 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM663 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33 t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5z" />
<glyph unicode="&#x4f2;" horiz-adv-x="1271" d="M23 1389q0 18 13 31.5t32 13.5h204q35 0 54 -39l360 -672l293 672q14 39 57 39h193q18 0 30.5 -12.5t12.5 -28.5q0 -20 -8 -35l-412 -940q-72 -164 -131 -250t-144 -127t-223 -41h-96q-20 0 -34.5 14.5t-14.5 34.5v156q0 20 14.5 34.5t34.5 14.5h76q70 0 118 37t93 123 l-518 954q-4 16 -4 21zM391 1620q0 10 10 31l88 199q10 20 27.5 29.5t48.5 9.5h168q18 0 29.5 -10.5t11.5 -28.5q0 -14 -12 -27l-183 -207q-25 -29 -65 -28h-90q-33 -1 -33 32zM780 1620q0 10 10 31l88 199q10 20 27.5 29.5t48.5 9.5h168q18 0 29.5 -10.5t11.5 -28.5 q0 -14 -12 -27l-183 -207q-25 -29 -65 -28h-90q-33 -1 -33 32z" />
<glyph unicode="&#x4f3;" d="M51 1020q0 18 13.5 31.5t31.5 13.5h166q39 0 55 -39l271 -670l278 670q14 39 56 39h159q17 0 30 -12.5t13 -28.5q0 -18 -8 -33l-389 -930q-63 -150 -114.5 -240.5t-127 -150t-182.5 -59.5h-92q-20 0 -34.5 14.5t-14.5 34.5v119q0 20 14 34.5t35 14.5h63q59 0 90 35 t76 139l21 51l-402 938q-8 18 -8 29zM326 1251q0 10 10 31l88 199q10 20 27.5 29.5t48.5 9.5h168q18 0 29.5 -10.5t11.5 -28.5q0 -14 -12 -27l-183 -207q-25 -29 -65 -28h-90q-33 -1 -33 32zM715 1251q0 10 10 31l88 199q10 20 27.5 29.5t48.5 9.5h168q18 0 29.5 -10.5 t11.5 -28.5q0 -14 -12 -27l-183 -207q-25 -29 -65 -28h-90q-33 -1 -33 32z" />
<glyph unicode="&#x4f4;" horiz-adv-x="1388" d="M104 924v460q0 23 14.5 36.5t35.5 13.5h188q23 0 37 -13.5t14 -36.5v-446q0 -121 61.5 -176t204.5 -55q102 0 187.5 37.5t85.5 125.5v514q0 23 14.5 36.5t34.5 13.5h188q23 0 37.5 -13.5t14.5 -36.5v-1335q0 -23 -14.5 -36t-37.5 -13h-188q-20 0 -34.5 14.5t-14.5 34.5 v481q-45 -45 -137.5 -67.5t-190.5 -22.5q-254 0 -378 121t-122 363zM331 1672v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM718 1672v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13 t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5z" />
<glyph unicode="&#x4f5;" horiz-adv-x="1185" d="M76 686v330q0 23 14 36t35 13h178q23 0 36 -13.5t13 -35.5v-316q0 -82 43 -116.5t142 -34.5q223 0 223 119v348q0 23 14 36t35 13h186q23 0 36 -13.5t13 -35.5v-967q0 -20 -13 -34.5t-36 -14.5h-186q-21 0 -35 14.5t-14 34.5v348q-100 -88 -295 -88q-193 0 -291 91.5 t-98 285.5zM245 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM632 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152 q-20 0 -32.5 12.5t-12.5 33.5z" />
<glyph unicode="&#x4f6;" horiz-adv-x="1114" d="M160 49v1333q0 23 13 37.5t36 14.5h838q22 0 36.5 -14.5t14.5 -37.5v-145q0 -23 -14.5 -36t-36.5 -13h-598v-928h98q20 0 35.5 -14.5t15.5 -34.5v-449q0 -20 -14.5 -34.5t-36.5 -14.5h-189q-20 0 -34.5 13.5t-14.5 35.5v238h-100q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x4f7;" horiz-adv-x="913" d="M135 49v967q0 23 14.5 36t34.5 13h650q22 0 35.5 -13.5t13.5 -35.5v-121q0 -20 -13.5 -34.5t-35.5 -14.5h-426v-627h92q20 0 34.5 -14.5t14.5 -34.5v-371q0 -20 -14.5 -34.5t-34.5 -14.5h-174q-20 0 -35 14.5t-15 34.5v201h-92q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x4f8;" horiz-adv-x="1814" d="M160 49v1335q0 23 14 36.5t35 13.5h188q23 0 37.5 -14.5t14.5 -35.5v-466h225q246 0 379 -115t133 -338q0 -133 -58.5 -238.5t-174 -166t-279.5 -60.5h-465q-21 0 -35 14.5t-14 34.5zM449 231h213q106 0 166.5 64.5t60.5 171.5q0 109 -57.5 165t-169.5 56h-213v-457z M594 1672v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM981 1672v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5 t-12.5 33.5zM1362 49v1335q0 23 14.5 36.5t34.5 13.5h197q23 0 36 -13.5t13 -36.5v-1335q0 -20 -13.5 -34.5t-35.5 -14.5h-197q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x4f9;" horiz-adv-x="1572" d="M135 49v967q0 23 14.5 36t34.5 13h174q23 0 36.5 -13.5t13.5 -35.5v-287h200q203 0 309.5 -98.5t106.5 -272.5q0 -178 -110.5 -268t-323.5 -90h-406q-20 0 -34.5 14.5t-14.5 34.5zM403 197h181q102 0 148 36.5t46 124.5t-47 131t-147 43h-181v-335zM483 1303v151 q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM870 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5z M1161 49v967q0 23 14.5 36t34.5 13h174q20 0 35 -14.5t15 -34.5v-967q0 -20 -14.5 -34.5t-35.5 -14.5h-174q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x1e80;" horiz-adv-x="1671" d="M78 1391q0 18 12 30.5t31 12.5h184q27 0 39 -9.5t16 -31.5l170 -922l183 594q20 51 67 51h113q27 0 43 -15.5t22 -35.5l185 -594l168 922q6 41 57 41h184q16 0 28.5 -12.5t12.5 -30.5q0 -10 -2 -17l-241 -1313q-4 -29 -23.5 -45t-50.5 -16h-139q-29 0 -47.5 15.5 t-24.5 35.5l-229 680l-230 -680q-16 -51 -71 -51h-140q-59 0 -73 61l-242 1313zM457 1796q0 18 10.5 29.5t28.5 11.5h223q27 0 41.5 -8t34.5 -31l166 -203q10 -10 10 -26q0 -33 -33 -33h-143q-22 0 -37.5 6t-34.5 21l-256 209q-10 10 -10 24z" />
<glyph unicode="&#x1e81;" horiz-adv-x="1681" d="M63 1020q0 18 12.5 31.5t30.5 13.5h156q20 0 35.5 -12.5t19.5 -26.5l199 -674l211 668q6 18 21.5 31.5t39.5 13.5h105q25 0 40 -13.5t19 -31.5l213 -668l199 674q4 14 18.5 26.5t36.5 12.5h154q18 0 31.5 -13.5t13.5 -31.5l-4 -19l-293 -950q-8 -25 -23.5 -38t-42.5 -13 h-135q-53 0 -67 51l-211 643l-213 -643q-16 -51 -70 -51h-133q-52 0 -68 51l-292 950q-2 6 -3 19zM463 1479q0 18 10.5 29.5t28.5 11.5h207q27 0 42 -8.5t34 -30.5l167 -203q10 -10 11 -27q0 -33 -33 -32h-129q-23 0 -38 6t-34 20l-256 209q-10 10 -10 25z" />
<glyph unicode="&#x1e82;" horiz-adv-x="1671" d="M78 1391q0 18 12 30.5t31 12.5h184q27 0 39 -9.5t16 -31.5l170 -922l183 594q20 51 67 51h113q27 0 43 -15.5t22 -35.5l185 -594l168 922q6 41 57 41h184q16 0 28.5 -12.5t12.5 -30.5q0 -10 -2 -17l-241 -1313q-4 -29 -23.5 -45t-50.5 -16h-139q-29 0 -47.5 15.5 t-24.5 35.5l-229 680l-230 -680q-16 -51 -71 -51h-140q-59 0 -73 61l-242 1313zM709 1569q0 16 10 26l168 203q18 23 33.5 31t42.5 8h221q18 0 28.5 -11t10.5 -30q0 -14 -10 -24l-256 -209q-18 -14 -34 -20.5t-38 -6.5h-143q-33 0 -33 33z" />
<glyph unicode="&#x1e83;" horiz-adv-x="1681" d="M63 1020q0 18 12.5 31.5t30.5 13.5h156q20 0 35.5 -12.5t19.5 -26.5l199 -674l211 668q6 18 21.5 31.5t39.5 13.5h105q25 0 40 -13.5t19 -31.5l213 -668l199 674q4 14 18.5 26.5t36.5 12.5h154q18 0 31.5 -13.5t13.5 -31.5l-4 -19l-293 -950q-8 -25 -23.5 -38t-42.5 -13 h-135q-53 0 -67 51l-211 643l-213 -643q-16 -51 -70 -51h-133q-52 0 -68 51l-292 950q-2 6 -3 19zM725 1251q0 16 10 27l168 203q18 23 33.5 31t42.5 8h207q18 0 28.5 -11.5t10.5 -29.5q0 -14 -11 -25l-256 -209q-18 -14 -33.5 -20t-37.5 -6h-129q-33 -1 -33 32z" />
<glyph unicode="&#x1e84;" horiz-adv-x="1671" d="M78 1391q0 18 12 30.5t31 12.5h184q27 0 39 -9.5t16 -31.5l170 -922l183 594q20 51 67 51h113q27 0 43 -15.5t22 -35.5l185 -594l168 922q6 41 57 41h184q16 0 28.5 -12.5t12.5 -30.5q0 -10 -2 -17l-241 -1313q-4 -29 -23.5 -45t-50.5 -16h-139q-29 0 -47.5 15.5 t-24.5 35.5l-229 680l-230 -680q-16 -51 -71 -51h-140q-59 0 -73 61l-242 1313zM490 1581v152q0 20 12 33.5t33 13.5h151q20 0 34 -13.5t14 -33.5v-152q0 -20 -13.5 -32.5t-34.5 -12.5h-151q-21 0 -33 12.5t-12 32.5zM938 1581v152q0 20 12.5 33.5t32.5 13.5h152 q20 0 33.5 -13.5t13.5 -33.5v-152q0 -20 -13.5 -32.5t-33.5 -12.5h-152q-20 0 -32.5 12.5t-12.5 32.5z" />
<glyph unicode="&#x1e85;" horiz-adv-x="1681" d="M63 1020q0 18 12.5 31.5t30.5 13.5h156q20 0 35.5 -12.5t19.5 -26.5l199 -674l211 668q6 18 21.5 31.5t39.5 13.5h105q25 0 40 -13.5t19 -31.5l213 -668l199 674q4 14 18.5 26.5t36.5 12.5h154q18 0 31.5 -13.5t13.5 -31.5l-4 -19l-293 -950q-8 -25 -23.5 -38t-42.5 -13 h-135q-53 0 -67 51l-211 643l-213 -643q-16 -51 -70 -51h-133q-52 0 -68 51l-292 950q-2 6 -3 19zM526 1303v151q0 20 12.5 33.5t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5zM913 1303v151q0 20 12.5 33.5 t32.5 13.5h152q20 0 33.5 -13t13.5 -34v-151q0 -20 -13.5 -33t-33.5 -13h-152q-20 0 -32.5 12.5t-12.5 33.5z" />
<glyph unicode="&#x1ef2;" horiz-adv-x="1361" d="M45 1391q0 18 13.5 30.5t29.5 12.5h184q39 0 64 -39l344 -594l346 594q8 14 23.5 26.5t37.5 12.5h185q18 0 30.5 -12.5t12.5 -30.5q0 -12 -6 -23l-484 -858v-461q0 -23 -14 -36t-37 -13h-188q-21 0 -35 14.5t-14 34.5v461l-486 858q-6 18 -6 23zM301 1796q0 18 10.5 29.5 t28.5 11.5h223q27 0 41.5 -8t34.5 -31l166 -203q10 -10 10 -26q0 -33 -33 -33h-143q-22 0 -37.5 6t-34.5 21l-256 209q-10 10 -10 24z" />
<glyph unicode="&#x1ef3;" horiz-adv-x="1177" d="M53 1020q2 18 14.5 31.5t30.5 13.5h170q37 0 54 -39l272 -668l276 668q20 39 58 39h166q18 0 30.5 -12.5t12.5 -28.5q0 -14 -9 -33l-579 -1341q-16 -39 -57 -39h-164q-17 0 -30 12t-13 29q0 14 8 33l162 378l-394 928q-8 18 -8 29zM217 1479q0 18 10.5 29.5t28.5 11.5 h207q27 0 42 -8.5t34 -30.5l167 -203q10 -10 11 -27q0 -33 -33 -32h-129q-23 0 -38 6t-34 20l-256 209q-10 10 -10 25z" />
<glyph unicode="&#x2000;" horiz-adv-x="944" />
<glyph unicode="&#x2001;" horiz-adv-x="1889" />
<glyph unicode="&#x2002;" horiz-adv-x="944" />
<glyph unicode="&#x2003;" horiz-adv-x="1889" />
<glyph unicode="&#x2004;" horiz-adv-x="629" />
<glyph unicode="&#x2005;" horiz-adv-x="472" />
<glyph unicode="&#x2006;" horiz-adv-x="314" />
<glyph unicode="&#x2007;" horiz-adv-x="314" />
<glyph unicode="&#x2008;" horiz-adv-x="236" />
<glyph unicode="&#x2009;" horiz-adv-x="377" />
<glyph unicode="&#x200a;" horiz-adv-x="104" />
<glyph unicode="&#x2010;" horiz-adv-x="983" d="M127 545v139q0 20 14.5 34.5t34.5 14.5h631q23 0 36 -13t13 -36v-139q0 -20 -14.5 -34.5t-34.5 -14.5h-631q-20 0 -34.5 14t-14.5 35z" />
<glyph unicode="&#x2011;" horiz-adv-x="983" d="M127 545v139q0 20 14.5 34.5t34.5 14.5h631q23 0 36 -13t13 -36v-139q0 -20 -14.5 -34.5t-34.5 -14.5h-631q-20 0 -34.5 14t-14.5 35z" />
<glyph unicode="&#x2012;" horiz-adv-x="983" d="M127 545v139q0 20 14.5 34.5t34.5 14.5h631q23 0 36 -13t13 -36v-139q0 -20 -14.5 -34.5t-34.5 -14.5h-631q-20 0 -34.5 14t-14.5 35z" />
<glyph unicode="&#x2013;" horiz-adv-x="1167" d="M127 545v139q0 20 14.5 34.5t34.5 14.5h815q23 0 36 -13t13 -36v-139q0 -20 -14 -34.5t-35 -14.5h-815q-20 0 -34.5 14t-14.5 35z" />
<glyph unicode="&#x2014;" horiz-adv-x="1556" d="M127 545v139q0 20 14.5 34.5t34.5 14.5h1204q23 0 36.5 -13t13.5 -36v-139q0 -20 -14.5 -34.5t-35.5 -14.5h-1204q-20 0 -34.5 14t-14.5 35z" />
<glyph unicode="&#x2018;" horiz-adv-x="518" d="M84 1032q0 16 4 29l133 340q12 25 26.5 39t41.5 14h106q18 0 28.5 -12t8.5 -33l-55 -348q-4 -29 -20.5 -47.5t-45.5 -18.5h-190q-17 0 -27 11.5t-10 25.5z" />
<glyph unicode="&#x2019;" horiz-adv-x="505" d="M100 1040l56 349q4 29 19 47t46 18h191q14 0 25.5 -11t11.5 -26q0 -12 -7 -28l-133 -342q-20 -51 -67 -52h-107q-18 0 -27.5 12.5t-7.5 32.5z" />
<glyph unicode="&#x201a;" horiz-adv-x="540" d="M86 -109l53 349q4 29 20.5 47t47.5 18h188q16 0 26.5 -11t10.5 -26q0 -16 -4 -28l-133 -340q-12 -25 -26.5 -39.5t-41.5 -14.5h-106q-19 0 -29 12.5t-6 32.5z" />
<glyph unicode="&#x201c;" horiz-adv-x="911" d="M84 1032q0 16 4 29l133 340q12 25 26.5 39t41.5 14h106q18 0 28.5 -12t8.5 -33l-55 -348q-4 -29 -20.5 -47.5t-45.5 -18.5h-190q-17 0 -27 11.5t-10 25.5zM475 1032q0 10 6 29l133 340q20 53 68 53h106q16 0 26.5 -12t8.5 -33l-53 -348q-12 -66 -68 -66h-190 q-14 0 -25.5 11.5t-11.5 25.5z" />
<glyph unicode="&#x201d;" horiz-adv-x="899" d="M100 1040l56 349q4 29 19 47t46 18h191q14 0 25.5 -11t11.5 -26q0 -12 -7 -28l-133 -342q-20 -51 -67 -52h-107q-16 0 -26.5 12.5t-8.5 32.5zM494 1040l53 349q4 29 20.5 47t46.5 18h189q16 0 26.5 -11t10.5 -26q0 -18 -4 -28l-134 -342q-10 -25 -25 -38.5t-42 -13.5 h-107q-18 0 -27 12.5t-7 32.5z" />
<glyph unicode="&#x201e;" horiz-adv-x="933" d="M86 -109l53 349q4 29 20.5 47t47.5 18h188q16 0 26.5 -11t10.5 -26q0 -16 -4 -28l-133 -340q-12 -25 -26.5 -39.5t-41.5 -14.5h-106q-19 0 -29 12.5t-6 32.5zM477 -111l55 351q4 29 19.5 47t46.5 18h190q14 0 25.5 -11t11.5 -26q0 -10 -6 -28l-133 -340q-20 -53 -68 -54 h-106q-16 0 -26.5 12.5t-8.5 30.5z" />
<glyph unicode="&#x2020;" horiz-adv-x="927" d="M63 895v129q0 23 14.5 36t35.5 13h235v311q0 23 14.5 36.5t34.5 13.5h135q20 0 35 -14.5t15 -35.5v-311h235q20 0 34.5 -14.5t14.5 -34.5v-129q0 -20 -14 -34.5t-35 -14.5h-235v-797q0 -20 -14.5 -34.5t-35.5 -14.5h-135q-20 0 -34.5 14.5t-14.5 34.5v797h-235 q-20 0 -35 14t-15 35z" />
<glyph unicode="&#x2021;" horiz-adv-x="1013" d="M106 385v129q0 23 14.5 36t35.5 13h235v334h-235q-20 0 -35 14.5t-15 34.5v129q0 23 14.5 36t35.5 13h235v260q0 23 14.5 36.5t34.5 13.5h135q20 0 35 -14.5t15 -35.5v-260h235q20 0 34.5 -14t14.5 -35v-129q0 -20 -14 -34.5t-35 -14.5h-235v-334h235q20 0 34.5 -14 t14.5 -35v-129q0 -20 -14 -34.5t-35 -14.5h-235v-287q0 -20 -14.5 -34.5t-35.5 -14.5h-135q-20 0 -34.5 14.5t-14.5 34.5v287h-235q-20 0 -35 14.5t-15 34.5z" />
<glyph unicode="&#x2022;" horiz-adv-x="784" d="M154 657q0 98 69.5 169t169.5 71q98 0 168 -70.5t70 -169.5q0 -98 -69.5 -168.5t-168.5 -70.5q-100 0 -169.5 70.5t-69.5 168.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="1396" d="M123 49v189q0 23 14.5 37t34.5 14h188q23 0 37.5 -14.5t14.5 -36.5v-189q0 -20 -14.5 -34.5t-37.5 -14.5h-188q-20 0 -34.5 14.5t-14.5 34.5zM555 49v189q0 23 14.5 37t34.5 14h189q23 0 37 -14.5t14 -36.5v-189q0 -20 -14.5 -34.5t-36.5 -14.5h-189q-20 0 -34.5 14.5 t-14.5 34.5zM987 49v189q0 23 14.5 37t34.5 14h189q20 0 34.5 -14.5t14.5 -36.5v-189q0 -20 -13.5 -34.5t-35.5 -14.5h-189q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="377" />
<glyph unicode="&#x2030;" horiz-adv-x="2293" d="M86 1071q0 61 2 98q8 127 86 201t219 74t220 -74t87 -201q4 -74 5 -98q0 -23 -5 -92q-8 -123 -89 -194.5t-218 -71.5q-135 0 -216 71.5t-89 194.5q-2 35 -2 92zM182 41q0 12 8 22l1010 1332q16 20 30.5 29.5t41.5 9.5h108q20 0 31.5 -11.5t11.5 -29.5q0 -12 -8 -23 l-1009 -1331q-16 -20 -31 -29.5t-39 -9.5h-109q-20 0 -32.5 11.5t-12.5 29.5zM272 1075q0 -51 2 -86q4 -51 32 -87t87 -36q60 0 87.5 35t33.5 88q4 70 4 86q0 23 -4 84q-4 53 -32.5 89t-88.5 36q-57 0 -85.5 -36t-33.5 -89q-2 -31 -2 -84zM905 354q0 59 2 97q8 127 86 200.5 t219 73.5q143 0 221 -73.5t87 -200.5q4 -74 4 -97t-4 -92q-8 -125 -89 -199.5t-219 -74.5q-135 0 -216 74.5t-89 199.5q-2 35 -2 92zM1092 358q0 -53 2 -88q4 -51 31.5 -87t86.5 -36q62 0 89.5 36t31.5 87q4 70 4 88q0 20 -4 82q-4 53 -32.5 89t-88.5 36q-57 0 -85.5 -35.5 t-32.5 -89.5q-2 -31 -2 -82zM1604 354q0 59 2 97q8 127 85.5 200.5t221.5 73.5q141 0 219 -73.5t86 -200.5q4 -74 4 -97t-4 -92q-8 -125 -89 -199.5t-216 -74.5q-137 0 -218 74.5t-89 199.5q-2 35 -2 92zM1790 358q0 -53 2 -88q6 -53 33.5 -88t87.5 -35q59 0 87 36t32 87 q4 70 4 88q0 20 -4 82q-4 53 -33 89t-86 36q-59 0 -88 -35.5t-33 -89.5q-2 -31 -2 -82z" />
<glyph unicode="&#x2039;" horiz-adv-x="714" d="M74 637v74q4 39 32 67l410 398q23 18 41 18t30.5 -12.5t12.5 -30.5v-141q0 -29 -8 -45.5t-29 -36.5l-262 -254l262 -254q20 -20 28.5 -36.5t8.5 -45.5v-141q0 -18 -12.5 -30.5t-30.5 -12.5t-41 18l-410 399q-29 27 -32 66z" />
<glyph unicode="&#x203a;" horiz-adv-x="714" d="M115 197v141q0 29 8 45t29 37l262 254l-262 254q-20 20 -28.5 36.5t-8.5 45.5v141q0 18 12 30.5t31 12.5q18 0 41 -18l409 -398q29 -29 33 -67v-74q-4 -39 -33 -66l-409 -399q-20 -18 -41 -18q-19 0 -31 12t-12 31z" />
<glyph unicode="&#x2044;" horiz-adv-x="327" d="M-444 41q0 12 8 22l1009 1332q16 20 30.5 29.5t39.5 9.5h84q20 0 32.5 -11.5t12.5 -29.5q0 -12 -8 -23l-1012 -1331q-16 -20 -30.5 -29.5t-38.5 -9.5h-84q-21 0 -32 11.5t-11 29.5z" />
<glyph unicode="&#x205f;" horiz-adv-x="472" />
<glyph unicode="&#x20aa;" horiz-adv-x="1853" d="M135 49v1071q0 23 14.5 36t34.5 13h568q276 0 410 -122.5t134 -384.5v-242q0 -20 -13 -34.5t-36 -14.5h-164q-23 0 -37 14t-14 35v239q0 291 -291 291h-342v-901q0 -20 -14 -34.5t-37 -14.5h-164q-20 0 -34.5 14.5t-14.5 34.5zM569 49v703q0 23 14.5 36t34.5 13h164 q23 0 36 -13.5t13 -35.5v-533h345q291 0 290 291v610q0 23 14.5 36t35.5 13h165q20 0 35 -14t15 -35v-612q0 -262 -134.5 -385t-410.5 -123h-568q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x20ac;" horiz-adv-x="1566" d="M78 508v119q0 23 14 36t35 13h127v78h-127q-20 0 -34.5 14t-14.5 35v119q0 23 14 36t35 13h133q27 233 179.5 358t416.5 125q184 0 321.5 -60.5t210 -162t76.5 -221.5v-4q0 -16 -13 -27.5t-30 -11.5h-198q-25 0 -39.5 11t-22.5 40q-31 104 -106.5 151.5t-198.5 47.5 q-254 0 -295 -246h338q23 0 36 -13.5t13 -35.5v-119q0 -20 -14 -34.5t-35 -14.5h-346v-78h346q23 0 36 -13.5t13 -35.5v-119q0 -20 -13 -34.5t-36 -14.5h-338q41 -242 295 -242q123 0 198.5 47t106.5 152q8 29 22.5 40t39.5 11h198q19 0 32 -12.5t11 -30.5 q-4 -121 -76.5 -222.5t-210 -161.5t-321.5 -60q-262 0 -414.5 123.5t-181.5 355.5h-133q-20 0 -34.5 14t-14.5 35z" />
<glyph unicode="&#x20ae;" horiz-adv-x="1241" d="M47 1229v153q0 23 13.5 37.5t35.5 14.5h1047q22 0 36.5 -14.5t14.5 -37.5v-153q0 -23 -14.5 -37t-36.5 -14h-377v-273l260 39q20 2 34.5 -12t14.5 -37v-74q0 -41 -49 -51l-260 -39v-113l260 39q20 4 34.5 -11t14.5 -38v-73q0 -20 -13 -33t-36 -17l-260 -39v-397 q0 -20 -13.5 -34.5t-35.5 -14.5h-193q-20 0 -34.5 14.5t-14.5 34.5v352l-244 -36q-20 -4 -34.5 11t-14.5 38v73q0 20 13.5 32.5t35.5 17.5l244 36v113l-244 -37q-20 -2 -34.5 13.5t-14.5 35.5v76q0 20 13.5 32.5t35.5 16.5l244 37v318h-379q-20 0 -34.5 14t-14.5 37z" />
<glyph unicode="&#x20b4;" horiz-adv-x="1335" d="M82 567v56q0 23 14.5 36t34.5 13h172q51 35 141 71h-313q-20 0 -34.5 14.5t-14.5 35.5v55q0 23 14.5 36t34.5 13h688q49 31 72.5 66.5t23.5 85.5q0 82 -53 126t-145 44q-109 0 -179.5 -43t-88.5 -113q-16 -37 -62 -37h-194q-18 0 -30.5 13.5t-12.5 29.5q0 88 67.5 177 t196.5 148.5t303 59.5q141 0 244.5 -51t159 -141.5t55.5 -200.5q0 -94 -41 -164h69q23 0 36 -13.5t13 -35.5v-55q0 -20 -13 -35t-36 -15h-262q-66 -31 -166 -63l-24 -8h452q23 0 36 -13.5t13 -35.5v-56q0 -20 -13 -34.5t-36 -14.5h-755q-37 -43 -37 -110q0 -90 65.5 -140.5 t181.5 -50.5q133 0 206 47t98 117q20 39 61 39h186q19 0 32 -13.5t13 -29.5q-2 -104 -70.5 -195.5t-200.5 -146.5t-316 -55q-150 0 -266.5 50t-183 144t-66.5 217q0 70 22 127h-43q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x20b8;" horiz-adv-x="1241" d="M47 971v112q0 23 13.5 37.5t35.5 14.5h1047q23 0 37 -14.5t14 -37.5v-112q0 -23 -14.5 -36t-36.5 -13h-377v-873q0 -20 -13.5 -34.5t-35.5 -14.5h-193q-20 0 -34.5 14.5t-14.5 34.5v873h-379q-20 0 -34.5 14t-14.5 35zM47 1307v75q0 23 14.5 37.5t34.5 14.5h1047 q23 0 37 -14.5t14 -37.5v-75q0 -23 -14.5 -36.5t-36.5 -13.5h-1047q-20 0 -34.5 14.5t-14.5 35.5z" />
<glyph unicode="&#x20bd;" horiz-adv-x="1427" d="M31 322v63q0 23 14 36t35 13h156v111h-156q-20 0 -34.5 14t-14.5 35v115q0 23 14 36t35 13h156v624q0 23 13 37.5t36 14.5h549q248 0 389 -117t141 -340q0 -213 -141.5 -322.5t-388.5 -109.5h-302v-111h332q23 0 36 -13t13 -36v-63q0 -20 -13 -35t-36 -15h-332v-223 q0 -20 -14 -34.5t-37 -14.5h-196q-21 0 -35 14.5t-14 34.5v223h-156q-20 0 -34.5 14.5t-14.5 35.5zM528 758h295q119 0 181.5 56t62.5 165q0 104 -60.5 164.5t-183.5 60.5h-295v-446z" />
<glyph unicode="&#x2122;" horiz-adv-x="1310" d="M88 1329v60q0 20 12.5 32.5t32.5 12.5h367q20 0 32.5 -12.5t12.5 -32.5v-60q0 -20 -12.5 -32.5t-32.5 -12.5h-107v-350q0 -20 -12 -32.5t-33 -12.5h-63q-21 0 -33 12t-12 33v350h-107q-20 0 -32.5 12.5t-12.5 32.5zM606 934v455q0 20 12.5 32.5t32.5 12.5h60 q20 0 33.5 -12.5t27.5 -32.5l115 -183l114 183q14 20 27.5 32.5t34.5 12.5h59q20 0 32.5 -12.5t12.5 -32.5v-455q0 -20 -12 -32.5t-33 -12.5h-61q-20 0 -32.5 12t-12.5 33v221l-60 -94q-14 -23 -24 -32t-29 -9h-33q-18 0 -28.5 9t-24.5 32l-59 94v-221q0 -20 -12.5 -32.5 t-32.5 -12.5h-62q-20 0 -32.5 12t-12.5 33z" />
<glyph unicode="&#x2202;" horiz-adv-x="1200" d="M86 473q0 150 61.5 260.5t177 170t269.5 59.5q100 0 170 -48q-45 127 -134 228.5t-224 204.5q-33 25 -33 43t12 30.5t31 12.5h184q55 0 88 -27q131 -102 217 -212t138.5 -267.5t60.5 -380.5v-80q-4 -225 -138.5 -356t-371.5 -131q-240 0 -375 133t-133 360zM367 451 q2 -119 58 -192t169 -73t170 73t57 190v59q-4 113 -61.5 181.5t-165.5 68.5q-109 0 -166 -68.5t-61 -181.5v-57z" />
<glyph unicode="&#x2206;" horiz-adv-x="1368" d="M55 55v58q0 27 15 55l446 1116q29 68 92 68h154q63 0 92 -68l446 -1116q12 -33 13 -55v-58q0 -23 -16.5 -39t-39.5 -16h-1146q-23 0 -39.5 16.5t-16.5 38.5zM340 217h688l-344 893z" />
<glyph unicode="&#x220f;" horiz-adv-x="1314" d="M145 -360v1744q0 23 14.5 36.5t35.5 13.5h925q23 0 36 -13.5t13 -36.5v-1744q0 -23 -13 -36.5t-36 -13.5h-157q-20 0 -35 13.5t-15 36.5v1527h-512v-1527q0 -23 -13 -36.5t-36 -13.5h-157q-21 0 -35.5 13.5t-14.5 36.5z" />
<glyph unicode="&#x2211;" horiz-adv-x="1187" d="M84 -209q0 25 10 41t33 39l641 631v20l-641 631q-23 23 -33 39t-10 41v151q0 23 14.5 36.5t34.5 13.5h889q23 0 36 -13.5t13 -36.5v-167q0 -20 -13.5 -35t-35.5 -15h-547l549 -542q25 -23 36 -39.5t11 -38.5v-70q0 -25 -11 -40t-36 -36l-549 -544h547q23 0 36 -14.5 t13 -35.5v-167q0 -23 -13.5 -36.5t-35.5 -13.5h-889q-20 0 -34.5 13.5t-14.5 36.5v151z" />
<glyph unicode="&#x221a;" horiz-adv-x="1462" d="M43 1020q0 18 13.5 31.5t31.5 13.5h133q41 0 55 -39l240 -715l397 1401q6 18 20.5 33.5t37.5 15.5h411q23 0 36.5 -13t13.5 -36v-106q0 -20 -13.5 -35t-36.5 -15h-272l-444 -1505q-16 -51 -60 -51h-178q-37 0 -53 39l-330 962z" />
<glyph unicode="&#x221e;" horiz-adv-x="1779" d="M106 555q0 121 42 224.5t131.5 167t222.5 63.5q121 0 210 -74t179 -205q90 133 177 206t206 73q133 0 223 -63.5t133 -168t43 -223.5t-43 -223.5t-132 -168t-222 -63.5q-121 0 -210 70t-177 197q-86 -127 -175 -197t-212 -70q-133 0 -222.5 63.5t-131.5 167t-42 224.5z M317 555q0 -119 54.5 -184.5t134.5 -65.5q135 0 260 242q-55 109 -118.5 183.5t-141.5 74.5q-80 0 -134.5 -65.5t-54.5 -184.5zM1012 549q129 -246 260 -246q82 0 136 66.5t54 185.5t-55 184.5t-135 65.5q-135 0 -260 -256z" />
<glyph unicode="&#x222b;" horiz-adv-x="894" d="M35 -301q0 20 14 35.5t35 15.5h90q92 0 127 47t35 137v1176q0 170 94 270.5t277 100.5h104q23 0 36 -13.5t13 -35.5v-111q0 -20 -13 -34.5t-36 -14.5h-88q-86 0 -124 -46t-38 -141v-1167q0 -174 -94 -276.5t-274 -102.5h-109q-21 0 -35 14.5t-14 34.5v111z" />
<glyph unicode="&#x2248;" horiz-adv-x="1142" d="M135 260v119q0 51 66.5 93t158.5 42q63 0 111.5 -10t114.5 -31q63 -18 105 -27.5t95 -9.5q43 0 74 13.5t52.5 28t29.5 18.5q12 10 29 12q39 0 39 -51v-121q0 -51 -66.5 -92t-159.5 -41q-61 0 -105 9t-114 30q-57 18 -103 28.5t-104 10.5q-41 0 -71.5 -14.5t-51 -28 t-26.5 -17.5q-20 -12 -35 -12q-39 0 -39 51zM135 772v119q0 51 66.5 93t158.5 42q63 0 111.5 -10t114.5 -31q63 -18 105 -27.5t95 -9.5q43 0 75 14.5t51.5 28t25.5 17.5q16 10 33 12q39 0 39 -51v-121q0 -51 -66.5 -92t-159.5 -41q-61 0 -105 9t-114 30q-57 18 -103 28.5 t-104 10.5q-47 0 -78.5 -16.5t-70.5 -43.5q-20 -12 -35 -12q-39 0 -39 51z" />
<glyph unicode="&#x2260;" horiz-adv-x="1122" d="M117 301v115q0 23 14 36t35 13h227l125 299h-352q-20 0 -34.5 14.5t-14.5 34.5v115q0 23 14 36t35 13h440l70 168q18 43 63 43h101q18 0 30.5 -12.5t12.5 -30.5q0 -12 -4 -23l-64 -145h143q23 0 36.5 -13.5t13.5 -35.5v-115q0 -20 -13.5 -34.5t-36.5 -14.5h-231 l-125 -299h356q23 0 36.5 -13.5t13.5 -35.5v-115q0 -20 -13.5 -34.5t-36.5 -14.5h-446l-68 -166q-6 -16 -22 -29.5t-41 -13.5h-98q-19 0 -31 12.5t-12 30.5q0 12 6 23l59 143h-139q-20 0 -34.5 14.5t-14.5 34.5z" />
<glyph unicode="&#x2264;" horiz-adv-x="1103" d="M121 49v111q0 23 14.5 36t34.5 13h737q23 0 36 -13.5t13 -35.5v-111q0 -20 -13 -34.5t-36 -14.5h-737q-20 0 -34.5 14.5t-14.5 34.5zM121 823v60q0 31 13 50t46 36l692 327q29 12 43 13q18 0 29.5 -11.5t11.5 -29.5v-121q0 -53 -53 -74l-491 -219l491 -221q53 -20 53 -74 v-121q0 -18 -12 -29.5t-29 -11.5q-18 1 -43 15l-692 327q-33 14 -46 35t-13 49z" />
<glyph unicode="&#x2265;" horiz-adv-x="1103" d="M147 49v111q0 23 14.5 36t35.5 13h739q20 0 34.5 -14.5t14.5 -34.5v-111q0 -20 -14.5 -34.5t-34.5 -14.5h-739q-21 0 -35.5 14.5t-14.5 34.5zM147 438v121q0 53 54 74l491 221l-491 219q-53 20 -54 74v121q0 18 12.5 29.5t30.5 11.5q12 0 41 -13l695 -327q31 -18 45 -37 t14 -49v-60q0 -29 -14.5 -48t-44.5 -36l-695 -327q-25 -14 -41 -15q-18 0 -30.5 11.5t-12.5 29.5z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1064" d="M0 0v1065h1065v-1065h-1065z" />
<glyph unicode="&#xfb03;" horiz-adv-x="1949" d="M35 895v121q0 20 14 34.5t35 14.5h166v82q0 186 100.5 277.5t302.5 91.5h142q20 0 34.5 -14.5t14.5 -35.5v-120q0 -20 -14.5 -35t-34.5 -15h-131q-80 0 -113 -38.5t-33 -120.5v-72h420v82q0 186 100.5 277.5t302.5 91.5h424q23 0 36.5 -13.5t13.5 -36.5v-120 q0 -20 -14.5 -35t-35.5 -15h-413q-80 0 -113 -38.5t-33 -120.5v-72h559q23 0 36.5 -13.5t13.5 -35.5v-967q0 -20 -13.5 -34.5t-36.5 -14.5h-170q-20 0 -34.5 14.5t-14.5 34.5v797h-340v-797q0 -20 -13 -34.5t-36 -14.5h-170q-20 0 -34.5 14.5t-14.5 34.5v797h-420v-797 q0 -20 -13 -34.5t-36 -14.5h-170q-20 0 -34.5 14.5t-14.5 34.5v797h-166q-20 0 -34.5 14t-14.5 35z" />
<glyph unicode="&#xfb04;" horiz-adv-x="2031" d="M35 895v121q0 20 14 34.5t35 14.5h166v82q0 186 100.5 277.5t302.5 91.5h142q20 0 34.5 -14.5t14.5 -35.5v-120q0 -20 -14.5 -35t-34.5 -15h-131q-80 0 -113 -38.5t-33 -120.5v-72h420v82q0 186 100.5 277.5t302.5 91.5h123q23 0 37.5 -14.5t14.5 -35.5v-120 q0 -20 -15.5 -35t-36.5 -15h-112q-80 0 -113 -38.5t-33 -120.5v-72h420v401q0 20 14.5 35t36.5 15h168q23 0 36 -13.5t13 -36.5v-1417q0 -20 -13 -34.5t-36 -14.5h-168q-23 0 -37 14.5t-14 34.5v797h-420v-797q0 -20 -13 -34.5t-36 -14.5h-170q-20 0 -34.5 14.5t-14.5 34.5 v797h-420v-797q0 -20 -13 -34.5t-36 -14.5h-170q-20 0 -34.5 14.5t-14.5 34.5v797h-166q-20 0 -34.5 14t-14.5 35z" />
<hkern u1="&#x20;" u2="&#x135;" k="23" />
<hkern u1="&#x20;" u2="v" k="53" />
<hkern u1="&#x20;" u2="V" k="53" />
<hkern u1="&#x22;" u2="&#x12d;" k="-8" />
<hkern u1="&#x22;" u2="&#x12b;" k="-27" />
<hkern u1="&#x22;" u2="&#x129;" k="-37" />
<hkern u1="&#x22;" u2="&#xef;" k="-43" />
<hkern u1="&#x22;" u2="&#xee;" k="-47" />
<hkern u1="&#x22;" u2="&#xec;" k="-43" />
<hkern u1="&#x23;" u2="&#x37;" k="23" />
<hkern u1="&#x26;" u2="&#x166;" k="68" />
<hkern u1="&#x26;" u2="x" k="-4" />
<hkern u1="&#x26;" u2="v" k="27" />
<hkern u1="&#x26;" u2="X" k="-10" />
<hkern u1="&#x26;" u2="V" k="70" />
<hkern u1="&#x27;" u2="&#x12d;" k="-8" />
<hkern u1="&#x27;" u2="&#x12b;" k="-27" />
<hkern u1="&#x27;" u2="&#x129;" k="-37" />
<hkern u1="&#x27;" u2="&#xef;" k="-43" />
<hkern u1="&#x27;" u2="&#xee;" k="-47" />
<hkern u1="&#x27;" u2="&#xec;" k="-43" />
<hkern u1="&#x28;" u2="&#x12e;" k="-2" />
<hkern u1="&#x28;" u2="&#x12d;" k="-20" />
<hkern u1="&#x28;" u2="&#x12b;" k="-33" />
<hkern u1="&#x28;" u2="&#x12a;" k="-47" />
<hkern u1="&#x28;" u2="&#x129;" k="-47" />
<hkern u1="&#x28;" u2="&#x128;" k="-49" />
<hkern u1="&#x28;" u2="&#x127;" k="-8" />
<hkern u1="&#x28;" u2="&#xf0;" k="82" />
<hkern u1="&#x28;" u2="&#xef;" k="-47" />
<hkern u1="&#x28;" u2="&#xee;" k="-57" />
<hkern u1="&#x28;" u2="&#xec;" k="-125" />
<hkern u1="&#x28;" u2="&#xcf;" k="-70" />
<hkern u1="&#x28;" u2="&#xce;" k="-78" />
<hkern u1="&#x28;" u2="x" k="39" />
<hkern u1="&#x28;" u2="v" k="92" />
<hkern u1="&#x28;" u2="j" k="-94" />
<hkern u1="&#x28;" u2="&#x7b;" k="49" />
<hkern u1="&#x28;" u2="&#x39;" k="31" />
<hkern u1="&#x28;" u2="&#x38;" k="47" />
<hkern u1="&#x28;" u2="&#x36;" k="90" />
<hkern u1="&#x28;" u2="&#x35;" k="29" />
<hkern u1="&#x28;" u2="&#x34;" k="84" />
<hkern u1="&#x28;" u2="&#x32;" k="25" />
<hkern u1="&#x28;" u2="&#x31;" k="68" />
<hkern u1="&#x28;" u2="&#x30;" k="53" />
<hkern u1="&#x28;" u2="&#x28;" k="14" />
<hkern u1="&#x29;" u2="&#x7d;" k="12" />
<hkern u1="&#x29;" u2="]" k="10" />
<hkern u1="&#x29;" u2="&#x29;" k="14" />
<hkern u1="&#x2a;" u2="&#x135;" k="-18" />
<hkern u1="&#x2a;" u2="&#x12b;" k="-25" />
<hkern u1="&#x2a;" u2="&#x129;" k="-37" />
<hkern u1="&#x2a;" u2="&#x127;" k="-20" />
<hkern u1="&#x2a;" u2="&#xf0;" k="68" />
<hkern u1="&#x2a;" u2="&#xef;" k="-37" />
<hkern u1="&#x2a;" u2="&#xee;" k="-70" />
<hkern u1="&#x2b;" u2="&#x37;" k="102" />
<hkern u1="&#x2b;" u2="&#x33;" k="18" />
<hkern u1="&#x2b;" u2="&#x32;" k="45" />
<hkern u1="&#x2b;" u2="&#x31;" k="47" />
<hkern u1="&#x2c;" u2="&#x166;" k="123" />
<hkern u1="&#x2c;" u2="&#x135;" k="27" />
<hkern u1="&#x2d;" u2="&#x166;" k="96" />
<hkern u1="&#x2d;" u2="&#x135;" k="27" />
<hkern u1="&#x2e;" u2="&#x135;" k="27" />
<hkern u1="&#x2f;" u2="&#x135;" k="49" />
<hkern u1="&#x2f;" u2="&#x12d;" k="-20" />
<hkern u1="&#x2f;" u2="&#x12b;" k="-25" />
<hkern u1="&#x2f;" u2="&#x12a;" k="-55" />
<hkern u1="&#x2f;" u2="&#x129;" k="-12" />
<hkern u1="&#x2f;" u2="&#x128;" k="-53" />
<hkern u1="&#x2f;" u2="&#xf0;" k="102" />
<hkern u1="&#x2f;" u2="&#xef;" k="-47" />
<hkern u1="&#x2f;" u2="&#xee;" k="-8" />
<hkern u1="&#x2f;" u2="&#xec;" k="-115" />
<hkern u1="&#x2f;" u2="&#xe8;" k="100" />
<hkern u1="&#x2f;" u2="&#xe0;" k="96" />
<hkern u1="&#x2f;" u2="&#xcf;" k="-78" />
<hkern u1="&#x2f;" u2="&#xce;" k="-80" />
<hkern u1="&#x2f;" u2="x" k="63" />
<hkern u1="&#x2f;" u2="v" k="63" />
<hkern u1="&#x2f;" u2="j" k="12" />
<hkern u1="&#x2f;" u2="&#x39;" k="16" />
<hkern u1="&#x2f;" u2="&#x38;" k="47" />
<hkern u1="&#x2f;" u2="&#x36;" k="111" />
<hkern u1="&#x2f;" u2="&#x35;" k="20" />
<hkern u1="&#x2f;" u2="&#x34;" k="121" />
<hkern u1="&#x2f;" u2="&#x32;" k="37" />
<hkern u1="&#x2f;" u2="&#x31;" k="18" />
<hkern u1="&#x2f;" u2="&#x30;" k="47" />
<hkern u1="&#x2f;" u2="&#x2f;" k="446" />
<hkern u1="&#x30;" u2="X" k="10" />
<hkern u1="&#x30;" u2="V" k="33" />
<hkern u1="&#x30;" u2="&#x7d;" k="47" />
<hkern u1="&#x30;" u2="]" k="45" />
<hkern u1="&#x30;" u2="\" k="45" />
<hkern u1="&#x30;" u2="&#x2f;" k="47" />
<hkern u1="&#x30;" u2="&#x29;" k="53" />
<hkern u1="&#x32;" u2="V" k="31" />
<hkern u1="&#x32;" u2="&#xf7;" k="27" />
<hkern u1="&#x32;" u2="&#xb7;" k="23" />
<hkern u1="&#x32;" u2="&#x7d;" k="16" />
<hkern u1="&#x32;" u2="]" k="18" />
<hkern u1="&#x32;" u2="\" k="37" />
<hkern u1="&#x32;" u2="&#x34;" k="14" />
<hkern u1="&#x32;" u2="&#x2b;" k="23" />
<hkern u1="&#x32;" u2="&#x29;" k="23" />
<hkern u1="&#x33;" u2="V" k="10" />
<hkern u1="&#x33;" u2="\" k="10" />
<hkern u1="&#x33;" u2="&#x31;" k="33" />
<hkern u1="&#x33;" u2="&#x2f;" k="18" />
<hkern u1="&#x34;" u2="V" k="61" />
<hkern u1="&#x34;" u2="&#x7d;" k="51" />
<hkern u1="&#x34;" u2="]" k="45" />
<hkern u1="&#x34;" u2="\" k="70" />
<hkern u1="&#x34;" u2="&#x39;" k="12" />
<hkern u1="&#x34;" u2="&#x37;" k="43" />
<hkern u1="&#x34;" u2="&#x31;" k="47" />
<hkern u1="&#x34;" u2="&#x2f;" k="10" />
<hkern u1="&#x34;" u2="&#x29;" k="55" />
<hkern u1="&#x35;" u2="V" k="10" />
<hkern u1="&#x35;" u2="\" k="10" />
<hkern u1="&#x35;" u2="&#x31;" k="23" />
<hkern u1="&#x35;" u2="&#x2f;" k="16" />
<hkern u1="&#x36;" u2="V" k="70" />
<hkern u1="&#x36;" u2="&#x7d;" k="63" />
<hkern u1="&#x36;" u2="]" k="57" />
<hkern u1="&#x36;" u2="\" k="84" />
<hkern u1="&#x36;" u2="&#x37;" k="68" />
<hkern u1="&#x36;" u2="&#x31;" k="47" />
<hkern u1="&#x36;" u2="&#x2f;" k="16" />
<hkern u1="&#x36;" u2="&#x29;" k="72" />
<hkern u1="&#x37;" u2="&#xf7;" k="57" />
<hkern u1="&#x37;" u2="&#xb7;" k="45" />
<hkern u1="&#x37;" u2="&#xa2;" k="45" />
<hkern u1="&#x37;" u2="&#x3d;" k="18" />
<hkern u1="&#x37;" u2="&#x36;" k="57" />
<hkern u1="&#x37;" u2="&#x34;" k="68" />
<hkern u1="&#x37;" u2="&#x2f;" k="137" />
<hkern u1="&#x37;" u2="&#x2b;" k="57" />
<hkern u1="&#x37;" u2="&#x23;" k="12" />
<hkern u1="&#x38;" u2="V" k="35" />
<hkern u1="&#x38;" u2="&#x7d;" k="43" />
<hkern u1="&#x38;" u2="]" k="41" />
<hkern u1="&#x38;" u2="\" k="49" />
<hkern u1="&#x38;" u2="&#x2f;" k="18" />
<hkern u1="&#x38;" u2="&#x29;" k="49" />
<hkern u1="&#x39;" u2="X" k="25" />
<hkern u1="&#x39;" u2="V" k="27" />
<hkern u1="&#x39;" u2="&#xf7;" k="18" />
<hkern u1="&#x39;" u2="&#xb7;" k="12" />
<hkern u1="&#x39;" u2="&#x7d;" k="33" />
<hkern u1="&#x39;" u2="]" k="33" />
<hkern u1="&#x39;" u2="\" k="27" />
<hkern u1="&#x39;" u2="&#x36;" k="14" />
<hkern u1="&#x39;" u2="&#x34;" k="23" />
<hkern u1="&#x39;" u2="&#x33;" k="23" />
<hkern u1="&#x39;" u2="&#x2f;" k="113" />
<hkern u1="&#x39;" u2="&#x29;" k="39" />
<hkern u1="&#x3a;" u2="&#x166;" k="47" />
<hkern u1="&#x3b;" u2="&#x166;" k="47" />
<hkern u1="&#x3d;" u2="&#x37;" k="33" />
<hkern u1="&#x40;" u2="j" k="-6" />
<hkern u1="&#x40;" u2="X" k="16" />
<hkern u1="&#x40;" u2="V" k="47" />
<hkern u1="A" u2="&#x135;" k="23" />
<hkern u1="B" u2="&#x2122;" k="6" />
<hkern u1="B" u2="&#xee;" k="-8" />
<hkern u1="B" u2="&#x7d;" k="43" />
<hkern u1="B" u2="x" k="6" />
<hkern u1="B" u2="]" k="41" />
<hkern u1="B" u2="\" k="37" />
<hkern u1="B" u2="X" k="43" />
<hkern u1="B" u2="V" k="35" />
<hkern u1="B" u2="&#x2f;" k="29" />
<hkern u1="B" u2="&#x29;" k="51" />
<hkern u1="C" u2="&#xee;" k="-6" />
<hkern u1="E" u2="&#x135;" k="12" />
<hkern u1="E" u2="&#x129;" k="-16" />
<hkern u1="E" u2="&#xef;" k="-18" />
<hkern u1="E" u2="&#xee;" k="-27" />
<hkern u1="E" u2="&#xec;" k="-10" />
<hkern u1="F" u2="&#x17f;" k="25" />
<hkern u1="F" u2="&#x135;" k="27" />
<hkern u1="F" u2="&#x131;" k="18" />
<hkern u1="F" u2="&#x12b;" k="-12" />
<hkern u1="F" u2="&#x129;" k="-27" />
<hkern u1="F" u2="&#xf0;" k="31" />
<hkern u1="F" u2="&#xef;" k="-29" />
<hkern u1="F" u2="&#xee;" k="-37" />
<hkern u1="F" u2="&#xec;" k="-29" />
<hkern u1="F" u2="&#xdf;" k="10" />
<hkern u1="F" u2="x" k="59" />
<hkern u1="F" u2="v" k="27" />
<hkern u1="F" u2="j" k="6" />
<hkern u1="F" u2="X" k="4" />
<hkern u1="F" u2="V" k="10" />
<hkern u1="F" u2="&#x34;" k="12" />
<hkern u1="F" u2="&#x32;" k="12" />
<hkern u1="F" u2="&#x2f;" k="98" />
<hkern u1="F" u2="&#x20;" k="31" />
<hkern u1="H" u2="&#x129;" k="-10" />
<hkern u1="H" u2="&#xef;" k="-10" />
<hkern u1="H" u2="&#xee;" k="-18" />
<hkern u1="I" u2="&#x129;" k="-10" />
<hkern u1="I" u2="&#xef;" k="-10" />
<hkern u1="I" u2="&#xee;" k="-18" />
<hkern u1="J" u2="&#x129;" k="-14" />
<hkern u1="J" u2="&#xef;" k="-16" />
<hkern u1="J" u2="&#xee;" k="-25" />
<hkern u1="J" u2="&#xec;" k="-8" />
<hkern u1="K" u2="&#x135;" k="20" />
<hkern u1="K" u2="&#x12d;" k="-6" />
<hkern u1="K" u2="&#x12b;" k="-25" />
<hkern u1="K" u2="&#x129;" k="-33" />
<hkern u1="K" u2="&#xef;" k="-41" />
<hkern u1="K" u2="&#xee;" k="-27" />
<hkern u1="K" u2="&#xec;" k="-49" />
<hkern u1="L" u2="&#x135;" k="31" />
<hkern u1="M" u2="&#x129;" k="-10" />
<hkern u1="M" u2="&#xef;" k="-10" />
<hkern u1="M" u2="&#xee;" k="-18" />
<hkern u1="N" u2="&#x129;" k="-10" />
<hkern u1="N" u2="&#xef;" k="-10" />
<hkern u1="N" u2="&#xee;" k="-18" />
<hkern u1="P" u2="&#x2122;" k="6" />
<hkern u1="P" u2="&#xf0;" k="39" />
<hkern u1="P" u2="&#xee;" k="-14" />
<hkern u1="P" u2="&#x7d;" k="39" />
<hkern u1="P" u2="]" k="41" />
<hkern u1="P" u2="\" k="16" />
<hkern u1="P" u2="X" k="61" />
<hkern u1="P" u2="V" k="41" />
<hkern u1="P" u2="&#x36;" k="12" />
<hkern u1="P" u2="&#x34;" k="25" />
<hkern u1="P" u2="&#x33;" k="14" />
<hkern u1="P" u2="&#x2f;" k="115" />
<hkern u1="P" u2="&#x29;" k="45" />
<hkern u1="P" u2="&#x26;" k="10" />
<hkern u1="P" u2="&#x20;" k="45" />
<hkern u1="Q" u2="&#x2f;" k="41" />
<hkern u1="T" u2="&#x159;" k="70" />
<hkern u1="T" u2="&#x135;" k="20" />
<hkern u1="T" u2="&#x131;" k="135" />
<hkern u1="T" u2="&#x12d;" k="-6" />
<hkern u1="T" u2="&#x12b;" k="-39" />
<hkern u1="T" u2="&#x129;" k="-41" />
<hkern u1="T" u2="&#x127;" k="-12" />
<hkern u1="T" u2="&#xef;" k="-51" />
<hkern u1="T" u2="&#xee;" k="-51" />
<hkern u1="T" u2="&#xed;" k="45" />
<hkern u1="T" u2="&#xec;" k="-55" />
<hkern u1="T" u2="&#xdf;" k="76" />
<hkern u1="U" u2="&#x129;" k="-14" />
<hkern u1="U" u2="&#xef;" k="-16" />
<hkern u1="U" u2="&#xee;" k="-25" />
<hkern u1="U" u2="&#xec;" k="-8" />
<hkern u1="V" u2="&#x17f;" k="29" />
<hkern u1="V" u2="&#x159;" k="66" />
<hkern u1="V" u2="&#x135;" k="25" />
<hkern u1="V" u2="&#x131;" k="76" />
<hkern u1="V" u2="&#x12d;" k="-8" />
<hkern u1="V" u2="&#x12b;" k="-29" />
<hkern u1="V" u2="&#x129;" k="-37" />
<hkern u1="V" u2="&#xf0;" k="125" />
<hkern u1="V" u2="&#xef;" k="-45" />
<hkern u1="V" u2="&#xee;" k="-39" />
<hkern u1="V" u2="&#xed;" k="29" />
<hkern u1="V" u2="&#xec;" k="-47" />
<hkern u1="V" u2="&#xdf;" k="43" />
<hkern u1="V" u2="&#xae;" k="43" />
<hkern u1="V" u2="&#xa9;" k="43" />
<hkern u1="V" u2="x" k="51" />
<hkern u1="V" u2="v" k="37" />
<hkern u1="V" u2="&#x40;" k="57" />
<hkern u1="V" u2="&#x3f;" k="10" />
<hkern u1="V" u2="&#x39;" k="25" />
<hkern u1="V" u2="&#x38;" k="33" />
<hkern u1="V" u2="&#x36;" k="80" />
<hkern u1="V" u2="&#x35;" k="31" />
<hkern u1="V" u2="&#x34;" k="84" />
<hkern u1="V" u2="&#x32;" k="29" />
<hkern u1="V" u2="&#x31;" k="25" />
<hkern u1="V" u2="&#x30;" k="31" />
<hkern u1="V" u2="&#x2f;" k="147" />
<hkern u1="V" u2="&#x26;" k="57" />
<hkern u1="V" u2="&#x20;" k="53" />
<hkern u1="W" u2="&#x159;" k="8" />
<hkern u1="W" u2="&#x131;" k="23" />
<hkern u1="W" u2="&#x12b;" k="-20" />
<hkern u1="W" u2="&#x129;" k="-29" />
<hkern u1="W" u2="&#xef;" k="-37" />
<hkern u1="W" u2="&#xee;" k="-39" />
<hkern u1="W" u2="&#xec;" k="-27" />
<hkern u1="W" u2="&#xdf;" k="6" />
<hkern u1="X" u2="&#x17f;" k="45" />
<hkern u1="X" u2="&#x135;" k="14" />
<hkern u1="X" u2="&#x12b;" k="-25" />
<hkern u1="X" u2="&#x129;" k="-31" />
<hkern u1="X" u2="&#xf0;" k="49" />
<hkern u1="X" u2="&#xef;" k="-41" />
<hkern u1="X" u2="&#xee;" k="-25" />
<hkern u1="X" u2="&#xec;" k="-47" />
<hkern u1="X" u2="&#xae;" k="45" />
<hkern u1="X" u2="&#xa9;" k="45" />
<hkern u1="X" u2="v" k="90" />
<hkern u1="X" u2="&#x3f;" k="8" />
<hkern u1="X" u2="&#x39;" k="10" />
<hkern u1="X" u2="&#x34;" k="14" />
<hkern u1="X" u2="&#x31;" k="35" />
<hkern u1="X" u2="&#x30;" k="10" />
<hkern u1="Y" u2="&#x15d;" k="72" />
<hkern u1="Y" u2="&#x159;" k="76" />
<hkern u1="Y" u2="&#x135;" k="53" />
<hkern u1="Y" u2="&#x131;" k="139" />
<hkern u1="Y" u2="&#x12d;" k="-20" />
<hkern u1="Y" u2="&#x12b;" k="-39" />
<hkern u1="Y" u2="&#x129;" k="-45" />
<hkern u1="Y" u2="&#x127;" k="-6" />
<hkern u1="Y" u2="&#xef;" k="-55" />
<hkern u1="Y" u2="&#xee;" k="-39" />
<hkern u1="Y" u2="&#xed;" k="57" />
<hkern u1="Y" u2="&#xec;" k="-57" />
<hkern u1="Y" u2="&#xe4;" k="72" />
<hkern u1="Y" u2="&#xe0;" k="76" />
<hkern u1="Y" u2="&#xdf;" k="78" />
<hkern u1="Z" u2="&#x135;" k="12" />
<hkern u1="Z" u2="&#x129;" k="-16" />
<hkern u1="Z" u2="&#xef;" k="-16" />
<hkern u1="Z" u2="&#xee;" k="-27" />
<hkern u1="Z" u2="&#xec;" k="-10" />
<hkern u1="[" u2="&#x12e;" k="2" />
<hkern u1="[" u2="&#x12d;" k="-18" />
<hkern u1="[" u2="&#x12b;" k="-31" />
<hkern u1="[" u2="&#x12a;" k="-43" />
<hkern u1="[" u2="&#x129;" k="-45" />
<hkern u1="[" u2="&#x128;" k="-45" />
<hkern u1="[" u2="&#x127;" k="-6" />
<hkern u1="[" u2="&#xf0;" k="66" />
<hkern u1="[" u2="&#xef;" k="-47" />
<hkern u1="[" u2="&#xee;" k="-55" />
<hkern u1="[" u2="&#xec;" k="-123" />
<hkern u1="[" u2="&#xcf;" k="-66" />
<hkern u1="[" u2="&#xce;" k="-74" />
<hkern u1="[" u2="x" k="33" />
<hkern u1="[" u2="v" k="76" />
<hkern u1="[" u2="j" k="-90" />
<hkern u1="[" u2="&#x7b;" k="41" />
<hkern u1="[" u2="&#x39;" k="25" />
<hkern u1="[" u2="&#x38;" k="39" />
<hkern u1="[" u2="&#x36;" k="68" />
<hkern u1="[" u2="&#x35;" k="25" />
<hkern u1="[" u2="&#x34;" k="61" />
<hkern u1="[" u2="&#x32;" k="23" />
<hkern u1="[" u2="&#x31;" k="59" />
<hkern u1="[" u2="&#x30;" k="45" />
<hkern u1="[" u2="&#x28;" k="10" />
<hkern u1="\" u2="&#x135;" k="12" />
<hkern u1="\" u2="&#xf0;" k="39" />
<hkern u1="\" u2="v" k="109" />
<hkern u1="\" u2="j" k="-61" />
<hkern u1="\" u2="V" k="147" />
<hkern u1="\" u2="\" k="444" />
<hkern u1="\" u2="&#x39;" k="76" />
<hkern u1="\" u2="&#x38;" k="18" />
<hkern u1="\" u2="&#x37;" k="53" />
<hkern u1="\" u2="&#x36;" k="29" />
<hkern u1="\" u2="&#x35;" k="12" />
<hkern u1="\" u2="&#x34;" k="20" />
<hkern u1="\" u2="&#x33;" k="12" />
<hkern u1="\" u2="&#x31;" k="127" />
<hkern u1="\" u2="&#x30;" k="45" />
<hkern u1="a" u2="&#x135;" k="6" />
<hkern u1="b" u2="&#x135;" k="14" />
<hkern u1="d" u2="&#x129;" k="-16" />
<hkern u1="d" u2="&#xef;" k="-16" />
<hkern u1="d" u2="&#xee;" k="-27" />
<hkern u1="d" u2="&#xec;" k="-10" />
<hkern u1="e" u2="&#x135;" k="10" />
<hkern u1="f" u2="&#x12d;" k="-51" />
<hkern u1="f" u2="&#x12b;" k="-41" />
<hkern u1="f" u2="&#x129;" k="-55" />
<hkern u1="f" u2="&#xef;" k="-86" />
<hkern u1="f" u2="&#xee;" k="-63" />
<hkern u1="f" u2="&#xec;" k="-141" />
<hkern u1="g" u2="j" k="-18" />
<hkern u1="h" u2="&#x135;" k="10" />
<hkern u1="i" u2="&#x129;" k="-12" />
<hkern u1="i" u2="&#xef;" k="-14" />
<hkern u1="i" u2="&#xee;" k="-23" />
<hkern u1="i" u2="&#xec;" k="-47" />
<hkern u1="j" u2="&#x129;" k="-12" />
<hkern u1="j" u2="&#xef;" k="-14" />
<hkern u1="j" u2="&#xee;" k="-23" />
<hkern u1="j" u2="&#xec;" k="-47" />
<hkern u1="j" u2="j" k="-16" />
<hkern u1="l" u2="&#x129;" k="-16" />
<hkern u1="l" u2="&#xef;" k="-16" />
<hkern u1="l" u2="&#xee;" k="-27" />
<hkern u1="l" u2="&#xec;" k="-10" />
<hkern u1="m" u2="&#x135;" k="10" />
<hkern u1="n" u2="&#x135;" k="10" />
<hkern u1="o" u2="&#x135;" k="14" />
<hkern u1="p" u2="&#x135;" k="14" />
<hkern u1="s" u2="&#x135;" k="4" />
<hkern u1="v" u2="&#x2122;" k="20" />
<hkern u1="v" u2="&#xf0;" k="57" />
<hkern u1="v" u2="&#x7d;" k="78" />
<hkern u1="v" u2="]" k="76" />
<hkern u1="v" u2="\" k="63" />
<hkern u1="v" u2="X" k="88" />
<hkern u1="v" u2="V" k="37" />
<hkern u1="v" u2="&#x2f;" k="109" />
<hkern u1="v" u2="&#x29;" k="92" />
<hkern u1="v" u2="&#x26;" k="27" />
<hkern u1="v" u2="&#x20;" k="53" />
<hkern u1="x" u2="&#x2122;" k="27" />
<hkern u1="x" u2="&#xf0;" k="43" />
<hkern u1="x" u2="&#x7d;" k="31" />
<hkern u1="x" u2="]" k="33" />
<hkern u1="x" u2="\" k="63" />
<hkern u1="x" u2="V" k="49" />
<hkern u1="x" u2="&#x29;" k="39" />
<hkern u1="&#x7b;" u2="&#x12e;" k="2" />
<hkern u1="&#x7b;" u2="&#x12d;" k="-20" />
<hkern u1="&#x7b;" u2="&#x12b;" k="-31" />
<hkern u1="&#x7b;" u2="&#x12a;" k="-47" />
<hkern u1="&#x7b;" u2="&#x129;" k="-47" />
<hkern u1="&#x7b;" u2="&#x128;" k="-49" />
<hkern u1="&#x7b;" u2="&#x127;" k="-8" />
<hkern u1="&#x7b;" u2="&#xf0;" k="74" />
<hkern u1="&#x7b;" u2="&#xef;" k="-47" />
<hkern u1="&#x7b;" u2="&#xee;" k="-55" />
<hkern u1="&#x7b;" u2="&#xec;" k="-123" />
<hkern u1="&#x7b;" u2="&#xcf;" k="-68" />
<hkern u1="&#x7b;" u2="&#xce;" k="-78" />
<hkern u1="&#x7b;" u2="x" k="31" />
<hkern u1="&#x7b;" u2="v" k="78" />
<hkern u1="&#x7b;" u2="j" k="-92" />
<hkern u1="&#x7b;" u2="&#x7b;" k="47" />
<hkern u1="&#x7b;" u2="&#x39;" k="25" />
<hkern u1="&#x7b;" u2="&#x38;" k="41" />
<hkern u1="&#x7b;" u2="&#x36;" k="78" />
<hkern u1="&#x7b;" u2="&#x35;" k="25" />
<hkern u1="&#x7b;" u2="&#x34;" k="70" />
<hkern u1="&#x7b;" u2="&#x32;" k="18" />
<hkern u1="&#x7b;" u2="&#x31;" k="59" />
<hkern u1="&#x7b;" u2="&#x30;" k="47" />
<hkern u1="&#x7b;" u2="&#x28;" k="12" />
<hkern u1="&#x7c;" u2="&#x12b;" k="-8" />
<hkern u1="&#x7c;" u2="&#x12a;" k="-12" />
<hkern u1="&#x7c;" u2="&#x129;" k="-23" />
<hkern u1="&#x7c;" u2="&#x128;" k="-6" />
<hkern u1="&#x7c;" u2="&#xef;" k="-23" />
<hkern u1="&#x7c;" u2="&#xee;" k="-31" />
<hkern u1="&#x7c;" u2="&#xec;" k="-61" />
<hkern u1="&#x7c;" u2="&#xcf;" k="-18" />
<hkern u1="&#x7c;" u2="&#xce;" k="-18" />
<hkern u1="&#x7c;" u2="&#xcc;" k="-45" />
<hkern u1="&#x7c;" u2="j" k="-37" />
<hkern u1="&#x7d;" u2="&#x7d;" k="47" />
<hkern u1="&#x7d;" u2="]" k="41" />
<hkern u1="&#x7d;" u2="&#x29;" k="49" />
<hkern u1="&#xa1;" u2="&#x166;" k="55" />
<hkern u1="&#xa1;" u2="j" k="-39" />
<hkern u1="&#xa1;" u2="V" k="49" />
<hkern u1="&#xa9;" u2="&#x166;" k="27" />
<hkern u1="&#xa9;" u2="X" k="47" />
<hkern u1="&#xa9;" u2="V" k="45" />
<hkern u1="&#xab;" u2="&#x129;" k="-16" />
<hkern u1="&#xab;" u2="&#xef;" k="-16" />
<hkern u1="&#xab;" u2="&#xee;" k="-27" />
<hkern u1="&#xae;" u2="&#x166;" k="25" />
<hkern u1="&#xae;" u2="X" k="45" />
<hkern u1="&#xae;" u2="V" k="45" />
<hkern u1="&#xb0;" u2="&#x36;" k="53" />
<hkern u1="&#xb0;" u2="&#x34;" k="76" />
<hkern u1="&#xb7;" u2="&#x37;" k="84" />
<hkern u1="&#xb7;" u2="&#x33;" k="31" />
<hkern u1="&#xb7;" u2="&#x32;" k="45" />
<hkern u1="&#xb7;" u2="&#x31;" k="33" />
<hkern u1="&#xbb;" u2="&#x166;" k="78" />
<hkern u1="&#xbf;" u2="&#xf0;" k="31" />
<hkern u1="&#xbf;" u2="v" k="119" />
<hkern u1="&#xbf;" u2="j" k="-49" />
<hkern u1="&#xbf;" u2="V" k="145" />
<hkern u1="&#xc0;" u2="&#x135;" k="23" />
<hkern u1="&#xc1;" u2="&#x135;" k="23" />
<hkern u1="&#xc2;" u2="&#x135;" k="23" />
<hkern u1="&#xc3;" u2="&#x135;" k="23" />
<hkern u1="&#xc4;" u2="&#x135;" k="23" />
<hkern u1="&#xc5;" u2="&#x135;" k="23" />
<hkern u1="&#xc6;" u2="&#x135;" k="12" />
<hkern u1="&#xc6;" u2="&#x129;" k="-16" />
<hkern u1="&#xc6;" u2="&#xef;" k="-18" />
<hkern u1="&#xc6;" u2="&#xee;" k="-27" />
<hkern u1="&#xc6;" u2="&#xec;" k="-10" />
<hkern u1="&#xc7;" u2="&#xee;" k="-6" />
<hkern u1="&#xc8;" u2="&#x135;" k="12" />
<hkern u1="&#xc8;" u2="&#x129;" k="-16" />
<hkern u1="&#xc8;" u2="&#xef;" k="-18" />
<hkern u1="&#xc8;" u2="&#xee;" k="-27" />
<hkern u1="&#xc8;" u2="&#xec;" k="-10" />
<hkern u1="&#xc9;" u2="&#x135;" k="12" />
<hkern u1="&#xc9;" u2="&#x129;" k="-16" />
<hkern u1="&#xc9;" u2="&#xef;" k="-18" />
<hkern u1="&#xc9;" u2="&#xee;" k="-27" />
<hkern u1="&#xc9;" u2="&#xec;" k="-10" />
<hkern u1="&#xca;" u2="&#x135;" k="12" />
<hkern u1="&#xca;" u2="&#x129;" k="-16" />
<hkern u1="&#xca;" u2="&#xef;" k="-18" />
<hkern u1="&#xca;" u2="&#xee;" k="-27" />
<hkern u1="&#xca;" u2="&#xec;" k="-10" />
<hkern u1="&#xcb;" u2="&#x135;" k="12" />
<hkern u1="&#xcb;" u2="&#x129;" k="-16" />
<hkern u1="&#xcb;" u2="&#xef;" k="-18" />
<hkern u1="&#xcb;" u2="&#xee;" k="-27" />
<hkern u1="&#xcb;" u2="&#xec;" k="-10" />
<hkern u1="&#xcc;" u2="&#x129;" k="-10" />
<hkern u1="&#xcc;" u2="&#xef;" k="-10" />
<hkern u1="&#xcc;" u2="&#xee;" k="-18" />
<hkern u1="&#xcd;" u2="&#x129;" k="-10" />
<hkern u1="&#xcd;" u2="&#xef;" k="-10" />
<hkern u1="&#xcd;" u2="&#xee;" k="-18" />
<hkern u1="&#xcd;" u2="&#x7c;" k="-45" />
<hkern u1="&#xce;" u2="&#x129;" k="-10" />
<hkern u1="&#xce;" u2="&#xef;" k="-10" />
<hkern u1="&#xce;" u2="&#xee;" k="-18" />
<hkern u1="&#xce;" u2="&#x7d;" k="-80" />
<hkern u1="&#xce;" u2="&#x7c;" k="-18" />
<hkern u1="&#xce;" u2="]" k="-78" />
<hkern u1="&#xce;" u2="\" k="-86" />
<hkern u1="&#xce;" u2="&#x29;" k="-82" />
<hkern u1="&#xcf;" u2="&#x129;" k="-10" />
<hkern u1="&#xcf;" u2="&#xef;" k="-10" />
<hkern u1="&#xcf;" u2="&#xee;" k="-18" />
<hkern u1="&#xcf;" u2="&#x7d;" k="-74" />
<hkern u1="&#xcf;" u2="&#x7c;" k="-20" />
<hkern u1="&#xcf;" u2="]" k="-70" />
<hkern u1="&#xcf;" u2="\" k="-82" />
<hkern u1="&#xcf;" u2="&#x29;" k="-74" />
<hkern u1="&#xd1;" u2="&#x129;" k="-10" />
<hkern u1="&#xd1;" u2="&#xef;" k="-10" />
<hkern u1="&#xd1;" u2="&#xee;" k="-18" />
<hkern u1="&#xd7;" u2="&#x37;" k="29" />
<hkern u1="&#xd8;" u2="&#x2a;" k="-12" />
<hkern u1="&#xd9;" u2="&#x129;" k="-14" />
<hkern u1="&#xd9;" u2="&#xef;" k="-16" />
<hkern u1="&#xd9;" u2="&#xee;" k="-25" />
<hkern u1="&#xd9;" u2="&#xec;" k="-8" />
<hkern u1="&#xda;" u2="&#x129;" k="-14" />
<hkern u1="&#xda;" u2="&#xef;" k="-16" />
<hkern u1="&#xda;" u2="&#xee;" k="-25" />
<hkern u1="&#xda;" u2="&#xec;" k="-8" />
<hkern u1="&#xdb;" u2="&#x129;" k="-14" />
<hkern u1="&#xdb;" u2="&#xef;" k="-16" />
<hkern u1="&#xdb;" u2="&#xee;" k="-25" />
<hkern u1="&#xdb;" u2="&#xec;" k="-8" />
<hkern u1="&#xdc;" u2="&#x129;" k="-14" />
<hkern u1="&#xdc;" u2="&#xef;" k="-16" />
<hkern u1="&#xdc;" u2="&#xee;" k="-25" />
<hkern u1="&#xdc;" u2="&#xec;" k="-8" />
<hkern u1="&#xdd;" u2="&#x15d;" k="72" />
<hkern u1="&#xdd;" u2="&#x159;" k="76" />
<hkern u1="&#xdd;" u2="&#x135;" k="53" />
<hkern u1="&#xdd;" u2="&#x131;" k="139" />
<hkern u1="&#xdd;" u2="&#x12d;" k="-20" />
<hkern u1="&#xdd;" u2="&#x12b;" k="-39" />
<hkern u1="&#xdd;" u2="&#x129;" k="-45" />
<hkern u1="&#xdd;" u2="&#x127;" k="-6" />
<hkern u1="&#xdd;" u2="&#xef;" k="-55" />
<hkern u1="&#xdd;" u2="&#xee;" k="-39" />
<hkern u1="&#xdd;" u2="&#xed;" k="57" />
<hkern u1="&#xdd;" u2="&#xec;" k="-57" />
<hkern u1="&#xdd;" u2="&#xe4;" k="72" />
<hkern u1="&#xdd;" u2="&#xe0;" k="76" />
<hkern u1="&#xdd;" u2="&#xdf;" k="78" />
<hkern u1="&#xde;" u2="&#x2122;" k="47" />
<hkern u1="&#xde;" u2="&#xba;" k="16" />
<hkern u1="&#xde;" u2="&#x7d;" k="72" />
<hkern u1="&#xde;" u2="x" k="20" />
<hkern u1="&#xde;" u2="v" k="6" />
<hkern u1="&#xde;" u2="]" k="66" />
<hkern u1="&#xde;" u2="\" k="63" />
<hkern u1="&#xde;" u2="X" k="109" />
<hkern u1="&#xde;" u2="V" k="53" />
<hkern u1="&#xde;" u2="&#x2f;" k="66" />
<hkern u1="&#xde;" u2="&#x2a;" k="14" />
<hkern u1="&#xde;" u2="&#x29;" k="80" />
<hkern u1="&#xdf;" u2="&#x2122;" k="31" />
<hkern u1="&#xdf;" u2="&#x17f;" k="6" />
<hkern u1="&#xdf;" u2="&#x7d;" k="49" />
<hkern u1="&#xdf;" u2="x" k="25" />
<hkern u1="&#xdf;" u2="v" k="33" />
<hkern u1="&#xdf;" u2="]" k="47" />
<hkern u1="&#xdf;" u2="\" k="57" />
<hkern u1="&#xdf;" u2="&#x2f;" k="37" />
<hkern u1="&#xdf;" u2="&#x29;" k="57" />
<hkern u1="&#xe0;" u2="&#x135;" k="6" />
<hkern u1="&#xe1;" u2="&#x135;" k="6" />
<hkern u1="&#xe1;" u2="\" k="92" />
<hkern u1="&#xe2;" u2="&#x135;" k="6" />
<hkern u1="&#xe3;" u2="&#x135;" k="6" />
<hkern u1="&#xe4;" u2="&#x135;" k="6" />
<hkern u1="&#xe5;" u2="&#x135;" k="6" />
<hkern u1="&#xe6;" u2="&#x135;" k="10" />
<hkern u1="&#xe8;" u2="&#x135;" k="10" />
<hkern u1="&#xe9;" u2="&#x135;" k="10" />
<hkern u1="&#xe9;" u2="\" k="90" />
<hkern u1="&#xea;" u2="&#x135;" k="10" />
<hkern u1="&#xeb;" u2="&#x135;" k="10" />
<hkern u1="&#xec;" u2="&#x129;" k="-12" />
<hkern u1="&#xec;" u2="&#xef;" k="-14" />
<hkern u1="&#xec;" u2="&#xee;" k="-23" />
<hkern u1="&#xec;" u2="&#xec;" k="-47" />
<hkern u1="&#xed;" u2="&#x2122;" k="-78" />
<hkern u1="&#xed;" u2="&#x201d;" k="-10" />
<hkern u1="&#xed;" u2="&#x2019;" k="-10" />
<hkern u1="&#xed;" u2="&#x17e;" k="-23" />
<hkern u1="&#xed;" u2="&#x161;" k="-27" />
<hkern u1="&#xed;" u2="&#x159;" k="-43" />
<hkern u1="&#xed;" u2="&#x142;" k="-10" />
<hkern u1="&#xed;" u2="&#x140;" k="-10" />
<hkern u1="&#xed;" u2="&#x13e;" k="-10" />
<hkern u1="&#xed;" u2="&#x13c;" k="-10" />
<hkern u1="&#xed;" u2="&#x13a;" k="-10" />
<hkern u1="&#xed;" u2="&#x137;" k="-10" />
<hkern u1="&#xed;" u2="&#x133;" k="-49" />
<hkern u1="&#xed;" u2="&#x131;" k="-49" />
<hkern u1="&#xed;" u2="&#x12f;" k="-49" />
<hkern u1="&#xed;" u2="&#x12d;" k="-49" />
<hkern u1="&#xed;" u2="&#x12b;" k="-49" />
<hkern u1="&#xed;" u2="&#x129;" k="-49" />
<hkern u1="&#xed;" u2="&#x127;" k="-10" />
<hkern u1="&#xed;" u2="&#x125;" k="-10" />
<hkern u1="&#xed;" u2="&#x10d;" k="-6" />
<hkern u1="&#xed;" u2="&#xfe;" k="-10" />
<hkern u1="&#xed;" u2="&#xef;" k="-49" />
<hkern u1="&#xed;" u2="&#xee;" k="-49" />
<hkern u1="&#xed;" u2="&#xec;" k="-49" />
<hkern u1="&#xed;" u2="&#xdf;" k="-10" />
<hkern u1="&#xed;" u2="&#x7d;" k="-123" />
<hkern u1="&#xed;" u2="&#x7c;" k="-61" />
<hkern u1="&#xed;" u2="l" k="-10" />
<hkern u1="&#xed;" u2="k" k="-10" />
<hkern u1="&#xed;" u2="j" k="-37" />
<hkern u1="&#xed;" u2="i" k="-49" />
<hkern u1="&#xed;" u2="h" k="-10" />
<hkern u1="&#xed;" u2="b" k="-10" />
<hkern u1="&#xed;" u2="]" k="-123" />
<hkern u1="&#xed;" u2="\" k="-117" />
<hkern u1="&#xed;" u2="&#x29;" k="-125" />
<hkern u1="&#xed;" u2="&#x27;" k="-43" />
<hkern u1="&#xed;" u2="&#x22;" k="-43" />
<hkern u1="&#xed;" u2="&#x21;" k="-31" />
<hkern u1="&#xee;" u2="&#x2122;" k="-66" />
<hkern u1="&#xee;" u2="&#x203a;" k="-27" />
<hkern u1="&#xee;" u2="&#x201d;" k="-27" />
<hkern u1="&#xee;" u2="&#x201c;" k="-8" />
<hkern u1="&#xee;" u2="&#x2019;" k="-27" />
<hkern u1="&#xee;" u2="&#x2018;" k="-8" />
<hkern u1="&#xee;" u2="&#x142;" k="-25" />
<hkern u1="&#xee;" u2="&#x140;" k="-25" />
<hkern u1="&#xee;" u2="&#x13e;" k="-25" />
<hkern u1="&#xee;" u2="&#x13c;" k="-25" />
<hkern u1="&#xee;" u2="&#x13a;" k="-25" />
<hkern u1="&#xee;" u2="&#x137;" k="-25" />
<hkern u1="&#xee;" u2="&#x133;" k="-20" />
<hkern u1="&#xee;" u2="&#x131;" k="-20" />
<hkern u1="&#xee;" u2="&#x12f;" k="-20" />
<hkern u1="&#xee;" u2="&#x12d;" k="-20" />
<hkern u1="&#xee;" u2="&#x12b;" k="-20" />
<hkern u1="&#xee;" u2="&#x129;" k="-20" />
<hkern u1="&#xee;" u2="&#x127;" k="-25" />
<hkern u1="&#xee;" u2="&#x125;" k="-25" />
<hkern u1="&#xee;" u2="&#xfe;" k="-25" />
<hkern u1="&#xee;" u2="&#xef;" k="-20" />
<hkern u1="&#xee;" u2="&#xee;" k="-20" />
<hkern u1="&#xee;" u2="&#xed;" k="-20" />
<hkern u1="&#xee;" u2="&#xec;" k="-20" />
<hkern u1="&#xee;" u2="&#xdf;" k="-25" />
<hkern u1="&#xee;" u2="&#xbb;" k="-27" />
<hkern u1="&#xee;" u2="&#xba;" k="-51" />
<hkern u1="&#xee;" u2="&#xaa;" k="-37" />
<hkern u1="&#xee;" u2="&#x7d;" k="-55" />
<hkern u1="&#xee;" u2="&#x7c;" k="-31" />
<hkern u1="&#xee;" u2="l" k="-25" />
<hkern u1="&#xee;" u2="k" k="-25" />
<hkern u1="&#xee;" u2="j" k="-10" />
<hkern u1="&#xee;" u2="i" k="-20" />
<hkern u1="&#xee;" u2="h" k="-25" />
<hkern u1="&#xee;" u2="b" k="-25" />
<hkern u1="&#xee;" u2="]" k="-53" />
<hkern u1="&#xee;" u2="\" k="-8" />
<hkern u1="&#xee;" u2="&#x3f;" k="-55" />
<hkern u1="&#xee;" u2="&#x2a;" k="-68" />
<hkern u1="&#xee;" u2="&#x29;" k="-55" />
<hkern u1="&#xee;" u2="&#x27;" k="-45" />
<hkern u1="&#xee;" u2="&#x22;" k="-45" />
<hkern u1="&#xee;" u2="&#x21;" k="-41" />
<hkern u1="&#xef;" u2="&#x2122;" k="-66" />
<hkern u1="&#xef;" u2="&#x203a;" k="-16" />
<hkern u1="&#xef;" u2="&#x201d;" k="-18" />
<hkern u1="&#xef;" u2="&#x2019;" k="-18" />
<hkern u1="&#xef;" u2="&#x142;" k="-16" />
<hkern u1="&#xef;" u2="&#x140;" k="-16" />
<hkern u1="&#xef;" u2="&#x13e;" k="-16" />
<hkern u1="&#xef;" u2="&#x13c;" k="-16" />
<hkern u1="&#xef;" u2="&#x13a;" k="-16" />
<hkern u1="&#xef;" u2="&#x137;" k="-16" />
<hkern u1="&#xef;" u2="&#x133;" k="-14" />
<hkern u1="&#xef;" u2="&#x131;" k="-14" />
<hkern u1="&#xef;" u2="&#x12f;" k="-14" />
<hkern u1="&#xef;" u2="&#x12d;" k="-14" />
<hkern u1="&#xef;" u2="&#x12b;" k="-14" />
<hkern u1="&#xef;" u2="&#x129;" k="-14" />
<hkern u1="&#xef;" u2="&#x127;" k="-16" />
<hkern u1="&#xef;" u2="&#x125;" k="-16" />
<hkern u1="&#xef;" u2="&#xfe;" k="-16" />
<hkern u1="&#xef;" u2="&#xef;" k="-14" />
<hkern u1="&#xef;" u2="&#xee;" k="-14" />
<hkern u1="&#xef;" u2="&#xed;" k="-14" />
<hkern u1="&#xef;" u2="&#xec;" k="-14" />
<hkern u1="&#xef;" u2="&#xdf;" k="-16" />
<hkern u1="&#xef;" u2="&#xbb;" k="-16" />
<hkern u1="&#xef;" u2="&#xba;" k="-43" />
<hkern u1="&#xef;" u2="&#xaa;" k="-31" />
<hkern u1="&#xef;" u2="&#x7d;" k="-47" />
<hkern u1="&#xef;" u2="&#x7c;" k="-23" />
<hkern u1="&#xef;" u2="l" k="-16" />
<hkern u1="&#xef;" u2="k" k="-16" />
<hkern u1="&#xef;" u2="i" k="-14" />
<hkern u1="&#xef;" u2="h" k="-16" />
<hkern u1="&#xef;" u2="b" k="-16" />
<hkern u1="&#xef;" u2="]" k="-47" />
<hkern u1="&#xef;" u2="\" k="-47" />
<hkern u1="&#xef;" u2="&#x3f;" k="-23" />
<hkern u1="&#xef;" u2="&#x2a;" k="-37" />
<hkern u1="&#xef;" u2="&#x29;" k="-47" />
<hkern u1="&#xef;" u2="&#x27;" k="-43" />
<hkern u1="&#xef;" u2="&#x22;" k="-43" />
<hkern u1="&#xef;" u2="&#x21;" k="-35" />
<hkern u1="&#xf0;" u2="&#x2122;" k="10" />
<hkern u1="&#xf0;" u2="&#xba;" k="8" />
<hkern u1="&#xf0;" u2="&#x7d;" k="39" />
<hkern u1="&#xf0;" u2="x" k="29" />
<hkern u1="&#xf0;" u2="v" k="14" />
<hkern u1="&#xf0;" u2="]" k="37" />
<hkern u1="&#xf0;" u2="\" k="20" />
<hkern u1="&#xf0;" u2="&#x2f;" k="43" />
<hkern u1="&#xf0;" u2="&#x2a;" k="8" />
<hkern u1="&#xf0;" u2="&#x29;" k="45" />
<hkern u1="&#xf1;" u2="&#x135;" k="10" />
<hkern u1="&#xf2;" u2="&#x135;" k="14" />
<hkern u1="&#xf3;" u2="&#x135;" k="14" />
<hkern u1="&#xf4;" u2="&#x135;" k="14" />
<hkern u1="&#xf5;" u2="&#x135;" k="14" />
<hkern u1="&#xf6;" u2="&#x135;" k="14" />
<hkern u1="&#xf7;" u2="&#x39;" k="16" />
<hkern u1="&#xf7;" u2="&#x37;" k="94" />
<hkern u1="&#xf7;" u2="&#x33;" k="27" />
<hkern u1="&#xf7;" u2="&#x32;" k="51" />
<hkern u1="&#xf7;" u2="&#x31;" k="55" />
<hkern u1="&#xf8;" u2="&#x135;" k="14" />
<hkern u1="&#xfe;" u2="&#x135;" k="14" />
<hkern u1="&#x100;" u2="&#x135;" k="23" />
<hkern u1="&#x101;" u2="&#x135;" k="6" />
<hkern u1="&#x102;" u2="&#x135;" k="23" />
<hkern u1="&#x103;" u2="&#x135;" k="6" />
<hkern u1="&#x104;" u2="&#x135;" k="23" />
<hkern u1="&#x104;" u2="j" k="-90" />
<hkern u1="&#x105;" u2="&#x135;" k="6" />
<hkern u1="&#x105;" u2="j" k="-45" />
<hkern u1="&#x106;" u2="&#xee;" k="-6" />
<hkern u1="&#x107;" u2="\" k="86" />
<hkern u1="&#x108;" u2="&#xee;" k="-6" />
<hkern u1="&#x10a;" u2="&#xee;" k="-6" />
<hkern u1="&#x10c;" u2="&#xee;" k="-6" />
<hkern u1="&#x10f;" u2="&#x3b;" k="-6" />
<hkern u1="&#x111;" u2="&#x2122;" k="-23" />
<hkern u1="&#x111;" u2="&#x129;" k="-16" />
<hkern u1="&#x111;" u2="&#xef;" k="-16" />
<hkern u1="&#x111;" u2="&#xee;" k="-27" />
<hkern u1="&#x111;" u2="&#xec;" k="-10" />
<hkern u1="&#x111;" u2="&#xba;" k="-10" />
<hkern u1="&#x111;" u2="&#x7d;" k="-10" />
<hkern u1="&#x111;" u2="]" k="-10" />
<hkern u1="&#x111;" u2="&#x3f;" k="-6" />
<hkern u1="&#x111;" u2="&#x2a;" k="-20" />
<hkern u1="&#x111;" u2="&#x29;" k="-10" />
<hkern u1="&#x112;" u2="&#x135;" k="12" />
<hkern u1="&#x112;" u2="&#x129;" k="-16" />
<hkern u1="&#x112;" u2="&#xef;" k="-18" />
<hkern u1="&#x112;" u2="&#xee;" k="-27" />
<hkern u1="&#x112;" u2="&#xec;" k="-10" />
<hkern u1="&#x113;" u2="&#x135;" k="10" />
<hkern u1="&#x114;" u2="&#x135;" k="12" />
<hkern u1="&#x114;" u2="&#x129;" k="-16" />
<hkern u1="&#x114;" u2="&#xef;" k="-18" />
<hkern u1="&#x114;" u2="&#xee;" k="-27" />
<hkern u1="&#x114;" u2="&#xec;" k="-10" />
<hkern u1="&#x115;" u2="&#x135;" k="10" />
<hkern u1="&#x116;" u2="&#x135;" k="12" />
<hkern u1="&#x116;" u2="&#x129;" k="-16" />
<hkern u1="&#x116;" u2="&#xef;" k="-18" />
<hkern u1="&#x116;" u2="&#xee;" k="-27" />
<hkern u1="&#x116;" u2="&#xec;" k="-10" />
<hkern u1="&#x117;" u2="&#x135;" k="10" />
<hkern u1="&#x118;" u2="&#x135;" k="12" />
<hkern u1="&#x118;" u2="&#x129;" k="-16" />
<hkern u1="&#x118;" u2="&#xef;" k="-18" />
<hkern u1="&#x118;" u2="&#xee;" k="-27" />
<hkern u1="&#x118;" u2="&#xec;" k="-10" />
<hkern u1="&#x118;" u2="j" k="-47" />
<hkern u1="&#x119;" u2="&#x135;" k="10" />
<hkern u1="&#x11a;" u2="&#x135;" k="12" />
<hkern u1="&#x11a;" u2="&#x129;" k="-16" />
<hkern u1="&#x11a;" u2="&#xef;" k="-18" />
<hkern u1="&#x11a;" u2="&#xee;" k="-27" />
<hkern u1="&#x11a;" u2="&#xec;" k="-10" />
<hkern u1="&#x11b;" u2="&#x135;" k="10" />
<hkern u1="&#x11d;" u2="j" k="-18" />
<hkern u1="&#x11f;" u2="j" k="-18" />
<hkern u1="&#x121;" u2="j" k="-18" />
<hkern u1="&#x123;" u2="j" k="-18" />
<hkern u1="&#x124;" u2="&#x129;" k="-10" />
<hkern u1="&#x124;" u2="&#xef;" k="-10" />
<hkern u1="&#x124;" u2="&#xee;" k="-18" />
<hkern u1="&#x125;" u2="&#x135;" k="10" />
<hkern u1="&#x126;" u2="&#x129;" k="-10" />
<hkern u1="&#x126;" u2="&#xef;" k="-10" />
<hkern u1="&#x126;" u2="&#xee;" k="-18" />
<hkern u1="&#x127;" u2="&#x135;" k="10" />
<hkern u1="&#x128;" u2="&#x129;" k="-10" />
<hkern u1="&#x128;" u2="&#xef;" k="-10" />
<hkern u1="&#x128;" u2="&#xee;" k="-18" />
<hkern u1="&#x128;" u2="&#x7d;" k="4" />
<hkern u1="&#x128;" u2="&#x29;" k="4" />
<hkern u1="&#x129;" u2="&#x2122;" k="-59" />
<hkern u1="&#x129;" u2="&#x142;" k="-8" />
<hkern u1="&#x129;" u2="&#x140;" k="-8" />
<hkern u1="&#x129;" u2="&#x13e;" k="-8" />
<hkern u1="&#x129;" u2="&#x13c;" k="-8" />
<hkern u1="&#x129;" u2="&#x13a;" k="-8" />
<hkern u1="&#x129;" u2="&#x137;" k="-8" />
<hkern u1="&#x129;" u2="&#x133;" k="-12" />
<hkern u1="&#x129;" u2="&#x131;" k="-12" />
<hkern u1="&#x129;" u2="&#x12f;" k="-12" />
<hkern u1="&#x129;" u2="&#x12d;" k="-12" />
<hkern u1="&#x129;" u2="&#x12b;" k="-12" />
<hkern u1="&#x129;" u2="&#x129;" k="-12" />
<hkern u1="&#x129;" u2="&#x127;" k="-8" />
<hkern u1="&#x129;" u2="&#x125;" k="-8" />
<hkern u1="&#x129;" u2="&#xfe;" k="-8" />
<hkern u1="&#x129;" u2="&#xef;" k="-12" />
<hkern u1="&#x129;" u2="&#xee;" k="-12" />
<hkern u1="&#x129;" u2="&#xed;" k="-12" />
<hkern u1="&#x129;" u2="&#xec;" k="-12" />
<hkern u1="&#x129;" u2="&#xdf;" k="-8" />
<hkern u1="&#x129;" u2="&#xba;" k="-12" />
<hkern u1="&#x129;" u2="&#xaa;" k="-6" />
<hkern u1="&#x129;" u2="&#x7d;" k="-47" />
<hkern u1="&#x129;" u2="&#x7c;" k="-23" />
<hkern u1="&#x129;" u2="l" k="-8" />
<hkern u1="&#x129;" u2="k" k="-8" />
<hkern u1="&#x129;" u2="i" k="-12" />
<hkern u1="&#x129;" u2="h" k="-8" />
<hkern u1="&#x129;" u2="b" k="-8" />
<hkern u1="&#x129;" u2="]" k="-45" />
<hkern u1="&#x129;" u2="\" k="-47" />
<hkern u1="&#x129;" u2="&#x2a;" k="-8" />
<hkern u1="&#x129;" u2="&#x29;" k="-47" />
<hkern u1="&#x129;" u2="&#x27;" k="-35" />
<hkern u1="&#x129;" u2="&#x22;" k="-35" />
<hkern u1="&#x129;" u2="&#x21;" k="-23" />
<hkern u1="&#x12a;" u2="&#x129;" k="-10" />
<hkern u1="&#x12a;" u2="&#xef;" k="-10" />
<hkern u1="&#x12a;" u2="&#xee;" k="-18" />
<hkern u1="&#x12a;" u2="&#x7d;" k="-53" />
<hkern u1="&#x12a;" u2="&#x7c;" k="-12" />
<hkern u1="&#x12a;" u2="]" k="-49" />
<hkern u1="&#x12a;" u2="\" k="-63" />
<hkern u1="&#x12a;" u2="&#x29;" k="-55" />
<hkern u1="&#x12b;" u2="&#x2122;" k="-55" />
<hkern u1="&#x12b;" u2="&#x129;" k="-12" />
<hkern u1="&#x12b;" u2="&#xef;" k="-14" />
<hkern u1="&#x12b;" u2="&#xee;" k="-23" />
<hkern u1="&#x12b;" u2="&#xec;" k="-47" />
<hkern u1="&#x12b;" u2="&#xba;" k="-27" />
<hkern u1="&#x12b;" u2="&#xaa;" k="-16" />
<hkern u1="&#x12b;" u2="&#x7d;" k="-31" />
<hkern u1="&#x12b;" u2="&#x7c;" k="-8" />
<hkern u1="&#x12b;" u2="]" k="-31" />
<hkern u1="&#x12b;" u2="\" k="-25" />
<hkern u1="&#x12b;" u2="&#x3f;" k="-6" />
<hkern u1="&#x12b;" u2="&#x2a;" k="-25" />
<hkern u1="&#x12b;" u2="&#x29;" k="-33" />
<hkern u1="&#x12b;" u2="&#x27;" k="-27" />
<hkern u1="&#x12b;" u2="&#x22;" k="-27" />
<hkern u1="&#x12b;" u2="&#x21;" k="-18" />
<hkern u1="&#x12c;" u2="&#x129;" k="-10" />
<hkern u1="&#x12c;" u2="&#xef;" k="-10" />
<hkern u1="&#x12c;" u2="&#xee;" k="-18" />
<hkern u1="&#x12d;" u2="&#x2122;" k="-12" />
<hkern u1="&#x12d;" u2="&#x129;" k="-12" />
<hkern u1="&#x12d;" u2="&#xef;" k="-14" />
<hkern u1="&#x12d;" u2="&#xee;" k="-23" />
<hkern u1="&#x12d;" u2="&#xec;" k="-47" />
<hkern u1="&#x12d;" u2="&#x7d;" k="-18" />
<hkern u1="&#x12d;" u2="]" k="-18" />
<hkern u1="&#x12d;" u2="\" k="-20" />
<hkern u1="&#x12d;" u2="&#x29;" k="-20" />
<hkern u1="&#x12d;" u2="&#x27;" k="-6" />
<hkern u1="&#x12d;" u2="&#x22;" k="-6" />
<hkern u1="&#x12e;" u2="&#x129;" k="-10" />
<hkern u1="&#x12e;" u2="&#xef;" k="-10" />
<hkern u1="&#x12e;" u2="&#xee;" k="-18" />
<hkern u1="&#x12e;" u2="j" k="-35" />
<hkern u1="&#x12f;" u2="&#x129;" k="-12" />
<hkern u1="&#x12f;" u2="&#xef;" k="-14" />
<hkern u1="&#x12f;" u2="&#xee;" k="-23" />
<hkern u1="&#x12f;" u2="&#xec;" k="-47" />
<hkern u1="&#x12f;" u2="j" k="-41" />
<hkern u1="&#x130;" u2="&#x129;" k="-10" />
<hkern u1="&#x130;" u2="&#xef;" k="-10" />
<hkern u1="&#x130;" u2="&#xee;" k="-18" />
<hkern u1="&#x131;" u2="&#x129;" k="-12" />
<hkern u1="&#x131;" u2="&#xef;" k="-14" />
<hkern u1="&#x131;" u2="&#xee;" k="-23" />
<hkern u1="&#x131;" u2="&#xec;" k="-47" />
<hkern u1="&#x132;" u2="&#x7d;" k="12" />
<hkern u1="&#x132;" u2="]" k="12" />
<hkern u1="&#x132;" u2="X" k="6" />
<hkern u1="&#x132;" u2="&#x2f;" k="47" />
<hkern u1="&#x132;" u2="&#x29;" k="14" />
<hkern u1="&#x133;" u2="&#x129;" k="-12" />
<hkern u1="&#x133;" u2="&#xef;" k="-14" />
<hkern u1="&#x133;" u2="&#xee;" k="-23" />
<hkern u1="&#x133;" u2="&#xec;" k="-47" />
<hkern u1="&#x133;" u2="j" k="-16" />
<hkern u1="&#x134;" u2="&#x129;" k="-14" />
<hkern u1="&#x134;" u2="&#xef;" k="-16" />
<hkern u1="&#x134;" u2="&#xee;" k="-25" />
<hkern u1="&#x134;" u2="&#xec;" k="-8" />
<hkern u1="&#x135;" u2="&#x129;" k="-12" />
<hkern u1="&#x135;" u2="&#xef;" k="-14" />
<hkern u1="&#x135;" u2="&#xee;" k="-23" />
<hkern u1="&#x135;" u2="&#xec;" k="-47" />
<hkern u1="&#x135;" u2="j" k="-6" />
<hkern u1="&#x136;" u2="&#x135;" k="20" />
<hkern u1="&#x136;" u2="&#x12d;" k="-6" />
<hkern u1="&#x136;" u2="&#x12b;" k="-25" />
<hkern u1="&#x136;" u2="&#x129;" k="-33" />
<hkern u1="&#x136;" u2="&#xef;" k="-41" />
<hkern u1="&#x136;" u2="&#xee;" k="-27" />
<hkern u1="&#x136;" u2="&#xec;" k="-49" />
<hkern u1="&#x139;" u2="&#x135;" k="31" />
<hkern u1="&#x13a;" u2="&#x129;" k="-16" />
<hkern u1="&#x13a;" u2="&#xef;" k="-16" />
<hkern u1="&#x13a;" u2="&#xee;" k="-27" />
<hkern u1="&#x13a;" u2="&#xec;" k="-10" />
<hkern u1="&#x13a;" u2="&#x7c;" k="-63" />
<hkern u1="&#x13b;" u2="&#x135;" k="31" />
<hkern u1="&#x13c;" u2="&#x129;" k="-16" />
<hkern u1="&#x13c;" u2="&#xef;" k="-16" />
<hkern u1="&#x13c;" u2="&#xee;" k="-27" />
<hkern u1="&#x13c;" u2="&#xec;" k="-10" />
<hkern u1="&#x13d;" u2="&#x2122;" k="143" />
<hkern u1="&#x13d;" u2="&#x201d;" k="184" />
<hkern u1="&#x13d;" u2="&#x2019;" k="184" />
<hkern u1="&#x13d;" u2="&#x1ef2;" k="131" />
<hkern u1="&#x13d;" u2="&#x1e84;" k="76" />
<hkern u1="&#x13d;" u2="&#x1e82;" k="76" />
<hkern u1="&#x13d;" u2="&#x1e80;" k="76" />
<hkern u1="&#x13d;" u2="&#x21a;" k="162" />
<hkern u1="&#x13d;" u2="&#x178;" k="131" />
<hkern u1="&#x13d;" u2="&#x176;" k="131" />
<hkern u1="&#x13d;" u2="&#x174;" k="76" />
<hkern u1="&#x13d;" u2="&#x166;" k="162" />
<hkern u1="&#x13d;" u2="&#x164;" k="162" />
<hkern u1="&#x13d;" u2="&#x135;" k="31" />
<hkern u1="&#x13d;" u2="&#xdd;" k="131" />
<hkern u1="&#x13d;" u2="&#xba;" k="182" />
<hkern u1="&#x13d;" u2="&#xaa;" k="188" />
<hkern u1="&#x13d;" u2="\" k="135" />
<hkern u1="&#x13d;" u2="Y" k="131" />
<hkern u1="&#x13d;" u2="W" k="76" />
<hkern u1="&#x13d;" u2="V" k="125" />
<hkern u1="&#x13d;" u2="T" k="162" />
<hkern u1="&#x13d;" u2="&#x2a;" k="180" />
<hkern u1="&#x13d;" u2="&#x27;" k="147" />
<hkern u1="&#x13d;" u2="&#x22;" k="147" />
<hkern u1="&#x13e;" u2="&#x161;" k="-10" />
<hkern u1="&#x13e;" u2="&#x10d;" k="8" />
<hkern u1="&#x13e;" u2="&#x3b;" k="-6" />
<hkern u1="&#x13f;" u2="&#x135;" k="31" />
<hkern u1="&#x141;" u2="&#x135;" k="31" />
<hkern u1="&#x142;" u2="&#x129;" k="-16" />
<hkern u1="&#x142;" u2="&#xef;" k="-16" />
<hkern u1="&#x142;" u2="&#xee;" k="-27" />
<hkern u1="&#x142;" u2="&#xec;" k="-10" />
<hkern u1="&#x143;" u2="&#x129;" k="-10" />
<hkern u1="&#x143;" u2="&#xef;" k="-10" />
<hkern u1="&#x143;" u2="&#xee;" k="-18" />
<hkern u1="&#x144;" u2="&#x135;" k="10" />
<hkern u1="&#x145;" u2="&#x129;" k="-10" />
<hkern u1="&#x145;" u2="&#xef;" k="-10" />
<hkern u1="&#x145;" u2="&#xee;" k="-18" />
<hkern u1="&#x146;" u2="&#x135;" k="10" />
<hkern u1="&#x147;" u2="&#x129;" k="-10" />
<hkern u1="&#x147;" u2="&#xef;" k="-10" />
<hkern u1="&#x147;" u2="&#xee;" k="-18" />
<hkern u1="&#x148;" u2="&#x135;" k="10" />
<hkern u1="&#x149;" u2="&#x135;" k="10" />
<hkern u1="&#x14a;" u2="&#x129;" k="-10" />
<hkern u1="&#x14a;" u2="&#xef;" k="-10" />
<hkern u1="&#x14a;" u2="&#xee;" k="-18" />
<hkern u1="&#x14b;" u2="&#x135;" k="10" />
<hkern u1="&#x14d;" u2="&#x135;" k="14" />
<hkern u1="&#x14f;" u2="&#x135;" k="14" />
<hkern u1="&#x151;" u2="&#x2122;" k="61" />
<hkern u1="&#x151;" u2="&#x135;" k="14" />
<hkern u1="&#x151;" u2="&#x7d;" k="43" />
<hkern u1="&#x151;" u2="]" k="39" />
<hkern u1="&#x151;" u2="\" k="53" />
<hkern u1="&#x151;" u2="&#x29;" k="55" />
<hkern u1="&#x152;" u2="&#x135;" k="12" />
<hkern u1="&#x152;" u2="&#x129;" k="-16" />
<hkern u1="&#x152;" u2="&#xef;" k="-18" />
<hkern u1="&#x152;" u2="&#xee;" k="-27" />
<hkern u1="&#x152;" u2="&#xec;" k="-10" />
<hkern u1="&#x153;" u2="&#x135;" k="10" />
<hkern u1="&#x155;" u2="&#x7d;" k="-2" />
<hkern u1="&#x155;" u2="\" k="-16" />
<hkern u1="&#x155;" u2="&#x29;" k="-4" />
<hkern u1="&#x159;" u2="&#x7d;" k="35" />
<hkern u1="&#x159;" u2="]" k="37" />
<hkern u1="&#x159;" u2="\" k="12" />
<hkern u1="&#x159;" u2="&#x29;" k="33" />
<hkern u1="&#x15b;" u2="&#x135;" k="4" />
<hkern u1="&#x15b;" u2="\" k="90" />
<hkern u1="&#x15d;" u2="&#x135;" k="4" />
<hkern u1="&#x15f;" u2="&#x135;" k="4" />
<hkern u1="&#x161;" u2="&#x135;" k="4" />
<hkern u1="&#x164;" u2="&#x159;" k="70" />
<hkern u1="&#x164;" u2="&#x135;" k="20" />
<hkern u1="&#x164;" u2="&#x131;" k="135" />
<hkern u1="&#x164;" u2="&#x12d;" k="-6" />
<hkern u1="&#x164;" u2="&#x12b;" k="-39" />
<hkern u1="&#x164;" u2="&#x129;" k="-41" />
<hkern u1="&#x164;" u2="&#x127;" k="-12" />
<hkern u1="&#x164;" u2="&#xef;" k="-51" />
<hkern u1="&#x164;" u2="&#xee;" k="-51" />
<hkern u1="&#x164;" u2="&#xed;" k="45" />
<hkern u1="&#x164;" u2="&#xec;" k="-55" />
<hkern u1="&#x164;" u2="&#xdf;" k="76" />
<hkern u1="&#x165;" u2="&#x2122;" k="-231" />
<hkern u1="&#x165;" u2="&#x17f;" k="-37" />
<hkern u1="&#x165;" u2="&#x161;" k="-57" />
<hkern u1="&#x165;" u2="&#x11b;" k="-10" />
<hkern u1="&#x165;" u2="&#x10d;" k="-8" />
<hkern u1="&#x165;" u2="&#xed;" k="-90" />
<hkern u1="&#x165;" u2="&#xe4;" k="-39" />
<hkern u1="&#x165;" u2="&#xba;" k="-135" />
<hkern u1="&#x165;" u2="&#xaa;" k="-125" />
<hkern u1="&#x165;" u2="&#x7d;" k="-203" />
<hkern u1="&#x165;" u2="&#x7c;" k="-143" />
<hkern u1="&#x165;" u2="x" k="-35" />
<hkern u1="&#x165;" u2="v" k="-45" />
<hkern u1="&#x165;" u2="j" k="-145" />
<hkern u1="&#x165;" u2="]" k="-201" />
<hkern u1="&#x165;" u2="\" k="-180" />
<hkern u1="&#x165;" u2="&#x3f;" k="-139" />
<hkern u1="&#x165;" u2="&#x3b;" k="-20" />
<hkern u1="&#x165;" u2="&#x2f;" k="33" />
<hkern u1="&#x165;" u2="&#x2a;" k="-160" />
<hkern u1="&#x165;" u2="&#x29;" k="-205" />
<hkern u1="&#x165;" u2="&#x21;" k="-166" />
<hkern u1="&#x165;" u2="&#x20;" k="-16" />
<hkern u1="&#x166;" g2="uniFB02" k="57" />
<hkern u1="&#x166;" g2="uniFB01" k="57" />
<hkern u1="&#x166;" u2="&#x2039;" k="78" />
<hkern u1="&#x166;" u2="&#x2014;" k="96" />
<hkern u1="&#x166;" u2="&#x2013;" k="96" />
<hkern u1="&#x166;" u2="&#x1ef3;" k="92" />
<hkern u1="&#x166;" u2="&#x1e85;" k="80" />
<hkern u1="&#x166;" u2="&#x1e83;" k="80" />
<hkern u1="&#x166;" u2="&#x1e81;" k="80" />
<hkern u1="&#x166;" u2="&#x21b;" k="41" />
<hkern u1="&#x166;" u2="&#x219;" k="133" />
<hkern u1="&#x166;" u2="&#x1ff;" k="141" />
<hkern u1="&#x166;" u2="&#x1fd;" k="158" />
<hkern u1="&#x166;" u2="&#x17f;" k="57" />
<hkern u1="&#x166;" u2="&#x177;" k="92" />
<hkern u1="&#x166;" u2="&#x175;" k="80" />
<hkern u1="&#x166;" u2="&#x173;" k="96" />
<hkern u1="&#x166;" u2="&#x171;" k="96" />
<hkern u1="&#x166;" u2="&#x16f;" k="96" />
<hkern u1="&#x166;" u2="&#x16d;" k="96" />
<hkern u1="&#x166;" u2="&#x16b;" k="96" />
<hkern u1="&#x166;" u2="&#x169;" k="96" />
<hkern u1="&#x166;" u2="&#x167;" k="41" />
<hkern u1="&#x166;" u2="&#x165;" k="41" />
<hkern u1="&#x166;" u2="&#x161;" k="133" />
<hkern u1="&#x166;" u2="&#x15f;" k="133" />
<hkern u1="&#x166;" u2="&#x15d;" k="133" />
<hkern u1="&#x166;" u2="&#x15b;" k="133" />
<hkern u1="&#x166;" u2="&#x159;" k="100" />
<hkern u1="&#x166;" u2="&#x157;" k="100" />
<hkern u1="&#x166;" u2="&#x155;" k="100" />
<hkern u1="&#x166;" u2="&#x153;" k="141" />
<hkern u1="&#x166;" u2="&#x151;" k="141" />
<hkern u1="&#x166;" u2="&#x14f;" k="141" />
<hkern u1="&#x166;" u2="&#x14d;" k="141" />
<hkern u1="&#x166;" u2="&#x14b;" k="100" />
<hkern u1="&#x166;" u2="&#x149;" k="100" />
<hkern u1="&#x166;" u2="&#x148;" k="100" />
<hkern u1="&#x166;" u2="&#x146;" k="100" />
<hkern u1="&#x166;" u2="&#x144;" k="100" />
<hkern u1="&#x166;" u2="&#x142;" k="16" />
<hkern u1="&#x166;" u2="&#x140;" k="16" />
<hkern u1="&#x166;" u2="&#x13e;" k="16" />
<hkern u1="&#x166;" u2="&#x13c;" k="16" />
<hkern u1="&#x166;" u2="&#x13a;" k="16" />
<hkern u1="&#x166;" u2="&#x138;" k="100" />
<hkern u1="&#x166;" u2="&#x137;" k="16" />
<hkern u1="&#x166;" u2="&#x135;" k="20" />
<hkern u1="&#x166;" u2="&#x131;" k="135" />
<hkern u1="&#x166;" u2="&#x12d;" k="-6" />
<hkern u1="&#x166;" u2="&#x12b;" k="-39" />
<hkern u1="&#x166;" u2="&#x129;" k="-41" />
<hkern u1="&#x166;" u2="&#x127;" k="16" />
<hkern u1="&#x166;" u2="&#x125;" k="16" />
<hkern u1="&#x166;" u2="&#x123;" k="135" />
<hkern u1="&#x166;" u2="&#x121;" k="135" />
<hkern u1="&#x166;" u2="&#x11f;" k="135" />
<hkern u1="&#x166;" u2="&#x11d;" k="135" />
<hkern u1="&#x166;" u2="&#x11b;" k="141" />
<hkern u1="&#x166;" u2="&#x119;" k="141" />
<hkern u1="&#x166;" u2="&#x117;" k="141" />
<hkern u1="&#x166;" u2="&#x115;" k="141" />
<hkern u1="&#x166;" u2="&#x113;" k="141" />
<hkern u1="&#x166;" u2="&#x111;" k="137" />
<hkern u1="&#x166;" u2="&#x10f;" k="137" />
<hkern u1="&#x166;" u2="&#x10d;" k="141" />
<hkern u1="&#x166;" u2="&#x10b;" k="141" />
<hkern u1="&#x166;" u2="&#x109;" k="141" />
<hkern u1="&#x166;" u2="&#x107;" k="141" />
<hkern u1="&#x166;" u2="&#x105;" k="158" />
<hkern u1="&#x166;" u2="&#x103;" k="158" />
<hkern u1="&#x166;" u2="&#x101;" k="158" />
<hkern u1="&#x166;" u2="&#xff;" k="92" />
<hkern u1="&#x166;" u2="&#xfe;" k="16" />
<hkern u1="&#x166;" u2="&#xfd;" k="92" />
<hkern u1="&#x166;" u2="&#xfc;" k="96" />
<hkern u1="&#x166;" u2="&#xfb;" k="96" />
<hkern u1="&#x166;" u2="&#xfa;" k="96" />
<hkern u1="&#x166;" u2="&#xf9;" k="96" />
<hkern u1="&#x166;" u2="&#xf8;" k="141" />
<hkern u1="&#x166;" u2="&#xf6;" k="141" />
<hkern u1="&#x166;" u2="&#xf5;" k="141" />
<hkern u1="&#x166;" u2="&#xf4;" k="141" />
<hkern u1="&#x166;" u2="&#xf3;" k="141" />
<hkern u1="&#x166;" u2="&#xf2;" k="141" />
<hkern u1="&#x166;" u2="&#xf1;" k="100" />
<hkern u1="&#x166;" u2="&#xef;" k="-51" />
<hkern u1="&#x166;" u2="&#xee;" k="-51" />
<hkern u1="&#x166;" u2="&#xed;" k="45" />
<hkern u1="&#x166;" u2="&#xec;" k="-55" />
<hkern u1="&#x166;" u2="&#xeb;" k="141" />
<hkern u1="&#x166;" u2="&#xea;" k="141" />
<hkern u1="&#x166;" u2="&#xe9;" k="141" />
<hkern u1="&#x166;" u2="&#xe8;" k="141" />
<hkern u1="&#x166;" u2="&#xe7;" k="141" />
<hkern u1="&#x166;" u2="&#xe6;" k="158" />
<hkern u1="&#x166;" u2="&#xe5;" k="158" />
<hkern u1="&#x166;" u2="&#xe4;" k="158" />
<hkern u1="&#x166;" u2="&#xe3;" k="158" />
<hkern u1="&#x166;" u2="&#xe2;" k="158" />
<hkern u1="&#x166;" u2="&#xe1;" k="158" />
<hkern u1="&#x166;" u2="&#xe0;" k="158" />
<hkern u1="&#x166;" u2="&#xdf;" k="16" />
<hkern u1="&#x166;" u2="&#xae;" k="25" />
<hkern u1="&#x166;" u2="&#xab;" k="78" />
<hkern u1="&#x166;" u2="&#xa9;" k="25" />
<hkern u1="&#x166;" u2="y" k="92" />
<hkern u1="&#x166;" u2="x" k="94" />
<hkern u1="&#x166;" u2="w" k="80" />
<hkern u1="&#x166;" u2="v" k="98" />
<hkern u1="&#x166;" u2="u" k="96" />
<hkern u1="&#x166;" u2="t" k="41" />
<hkern u1="&#x166;" u2="s" k="133" />
<hkern u1="&#x166;" u2="r" k="100" />
<hkern u1="&#x166;" u2="q" k="92" />
<hkern u1="&#x166;" u2="p" k="100" />
<hkern u1="&#x166;" u2="o" k="141" />
<hkern u1="&#x166;" u2="n" k="100" />
<hkern u1="&#x166;" u2="m" k="100" />
<hkern u1="&#x166;" u2="l" k="16" />
<hkern u1="&#x166;" u2="k" k="16" />
<hkern u1="&#x166;" u2="j" k="10" />
<hkern u1="&#x166;" u2="h" k="16" />
<hkern u1="&#x166;" u2="g" k="135" />
<hkern u1="&#x166;" u2="f" k="57" />
<hkern u1="&#x166;" u2="e" k="141" />
<hkern u1="&#x166;" u2="d" k="137" />
<hkern u1="&#x166;" u2="c" k="141" />
<hkern u1="&#x166;" u2="b" k="16" />
<hkern u1="&#x166;" u2="a" k="158" />
<hkern u1="&#x166;" u2="&#x3b;" k="47" />
<hkern u1="&#x166;" u2="&#x3a;" k="47" />
<hkern u1="&#x166;" u2="&#x2d;" k="96" />
<hkern u1="&#x168;" u2="&#x129;" k="-14" />
<hkern u1="&#x168;" u2="&#xef;" k="-16" />
<hkern u1="&#x168;" u2="&#xee;" k="-25" />
<hkern u1="&#x168;" u2="&#xec;" k="-8" />
<hkern u1="&#x16a;" u2="&#x129;" k="-14" />
<hkern u1="&#x16a;" u2="&#xef;" k="-16" />
<hkern u1="&#x16a;" u2="&#xee;" k="-25" />
<hkern u1="&#x16a;" u2="&#xec;" k="-8" />
<hkern u1="&#x16c;" u2="&#x129;" k="-14" />
<hkern u1="&#x16c;" u2="&#xef;" k="-16" />
<hkern u1="&#x16c;" u2="&#xee;" k="-25" />
<hkern u1="&#x16c;" u2="&#xec;" k="-8" />
<hkern u1="&#x16e;" u2="&#x129;" k="-14" />
<hkern u1="&#x16e;" u2="&#xef;" k="-16" />
<hkern u1="&#x16e;" u2="&#xee;" k="-25" />
<hkern u1="&#x16e;" u2="&#xec;" k="-8" />
<hkern u1="&#x170;" u2="&#x129;" k="-14" />
<hkern u1="&#x170;" u2="&#xef;" k="-16" />
<hkern u1="&#x170;" u2="&#xee;" k="-25" />
<hkern u1="&#x170;" u2="&#xec;" k="-8" />
<hkern u1="&#x171;" u2="\" k="49" />
<hkern u1="&#x172;" u2="&#x129;" k="-14" />
<hkern u1="&#x172;" u2="&#xef;" k="-16" />
<hkern u1="&#x172;" u2="&#xee;" k="-25" />
<hkern u1="&#x172;" u2="&#xec;" k="-8" />
<hkern u1="&#x173;" u2="j" k="-41" />
<hkern u1="&#x174;" u2="&#x159;" k="8" />
<hkern u1="&#x174;" u2="&#x131;" k="23" />
<hkern u1="&#x174;" u2="&#x12b;" k="-20" />
<hkern u1="&#x174;" u2="&#x129;" k="-29" />
<hkern u1="&#x174;" u2="&#xef;" k="-37" />
<hkern u1="&#x174;" u2="&#xee;" k="-39" />
<hkern u1="&#x174;" u2="&#xec;" k="-27" />
<hkern u1="&#x174;" u2="&#xdf;" k="6" />
<hkern u1="&#x176;" u2="&#x15d;" k="72" />
<hkern u1="&#x176;" u2="&#x159;" k="76" />
<hkern u1="&#x176;" u2="&#x135;" k="53" />
<hkern u1="&#x176;" u2="&#x131;" k="139" />
<hkern u1="&#x176;" u2="&#x12d;" k="-20" />
<hkern u1="&#x176;" u2="&#x12b;" k="-39" />
<hkern u1="&#x176;" u2="&#x129;" k="-45" />
<hkern u1="&#x176;" u2="&#x127;" k="-6" />
<hkern u1="&#x176;" u2="&#xef;" k="-55" />
<hkern u1="&#x176;" u2="&#xee;" k="-39" />
<hkern u1="&#x176;" u2="&#xed;" k="57" />
<hkern u1="&#x176;" u2="&#xec;" k="-57" />
<hkern u1="&#x176;" u2="&#xe4;" k="72" />
<hkern u1="&#x176;" u2="&#xe0;" k="76" />
<hkern u1="&#x176;" u2="&#xdf;" k="78" />
<hkern u1="&#x178;" u2="&#x15d;" k="72" />
<hkern u1="&#x178;" u2="&#x159;" k="76" />
<hkern u1="&#x178;" u2="&#x135;" k="53" />
<hkern u1="&#x178;" u2="&#x131;" k="139" />
<hkern u1="&#x178;" u2="&#x12d;" k="-20" />
<hkern u1="&#x178;" u2="&#x12b;" k="-39" />
<hkern u1="&#x178;" u2="&#x129;" k="-45" />
<hkern u1="&#x178;" u2="&#x127;" k="-6" />
<hkern u1="&#x178;" u2="&#xef;" k="-55" />
<hkern u1="&#x178;" u2="&#xee;" k="-39" />
<hkern u1="&#x178;" u2="&#xed;" k="57" />
<hkern u1="&#x178;" u2="&#xec;" k="-57" />
<hkern u1="&#x178;" u2="&#xe4;" k="72" />
<hkern u1="&#x178;" u2="&#xe0;" k="76" />
<hkern u1="&#x178;" u2="&#xdf;" k="78" />
<hkern u1="&#x179;" u2="&#x135;" k="12" />
<hkern u1="&#x179;" u2="&#x129;" k="-16" />
<hkern u1="&#x179;" u2="&#xef;" k="-16" />
<hkern u1="&#x179;" u2="&#xee;" k="-27" />
<hkern u1="&#x179;" u2="&#xec;" k="-10" />
<hkern u1="&#x17a;" u2="\" k="55" />
<hkern u1="&#x17b;" u2="&#x135;" k="12" />
<hkern u1="&#x17b;" u2="&#x129;" k="-16" />
<hkern u1="&#x17b;" u2="&#xef;" k="-16" />
<hkern u1="&#x17b;" u2="&#xee;" k="-27" />
<hkern u1="&#x17b;" u2="&#xec;" k="-10" />
<hkern u1="&#x17d;" u2="&#x135;" k="12" />
<hkern u1="&#x17d;" u2="&#x129;" k="-16" />
<hkern u1="&#x17d;" u2="&#xef;" k="-16" />
<hkern u1="&#x17d;" u2="&#xee;" k="-27" />
<hkern u1="&#x17d;" u2="&#xec;" k="-10" />
<hkern u1="&#x17f;" u2="&#x177;" k="-18" />
<hkern u1="&#x17f;" u2="&#x159;" k="-117" />
<hkern u1="&#x17f;" u2="&#x151;" k="-14" />
<hkern u1="&#x17f;" u2="&#x149;" k="-45" />
<hkern u1="&#x17f;" u2="&#x131;" k="-2" />
<hkern u1="&#x17f;" u2="&#x12f;" k="-86" />
<hkern u1="&#x17f;" u2="&#x12d;" k="-246" />
<hkern u1="&#x17f;" u2="&#x12b;" k="-238" />
<hkern u1="&#x17f;" u2="&#x129;" k="-215" />
<hkern u1="&#x17f;" u2="&#x11d;" k="-14" />
<hkern u1="&#x17f;" u2="&#x11b;" k="-10" />
<hkern u1="&#x17f;" u2="&#x10d;" k="-8" />
<hkern u1="&#x17f;" u2="&#x109;" k="-23" />
<hkern u1="&#x17f;" u2="&#xff;" k="-8" />
<hkern u1="&#x17f;" u2="&#xfb;" k="-6" />
<hkern u1="&#x17f;" u2="&#xf9;" k="-29" />
<hkern u1="&#x17f;" u2="&#xf6;" k="-10" />
<hkern u1="&#x17f;" u2="&#xf5;" k="-8" />
<hkern u1="&#x17f;" u2="&#xf4;" k="-18" />
<hkern u1="&#x17f;" u2="&#xf2;" k="-37" />
<hkern u1="&#x17f;" u2="&#xef;" k="-283" />
<hkern u1="&#x17f;" u2="&#xee;" k="-223" />
<hkern u1="&#x17f;" u2="&#xed;" k="-55" />
<hkern u1="&#x17f;" u2="&#xec;" k="-338" />
<hkern u1="&#x17f;" u2="&#xeb;" k="-16" />
<hkern u1="&#x17f;" u2="&#xea;" k="-27" />
<hkern u1="&#x17f;" u2="&#xe8;" k="-43" />
<hkern u1="&#x17f;" u2="&#xe4;" k="-20" />
<hkern u1="&#x17f;" u2="&#xe3;" k="-18" />
<hkern u1="&#x17f;" u2="&#xe2;" k="-29" />
<hkern u1="&#x17f;" u2="&#xe0;" k="-47" />
<hkern u1="&#x17f;" u2="j" k="-90" />
<hkern u1="&#x1fc;" u2="&#x135;" k="12" />
<hkern u1="&#x1fc;" u2="&#x129;" k="-16" />
<hkern u1="&#x1fc;" u2="&#xef;" k="-18" />
<hkern u1="&#x1fc;" u2="&#xee;" k="-27" />
<hkern u1="&#x1fc;" u2="&#xec;" k="-10" />
<hkern u1="&#x1fd;" u2="&#x135;" k="10" />
<hkern u1="&#x1fe;" u2="&#x2a;" k="-12" />
<hkern u1="&#x1ff;" u2="&#x135;" k="14" />
<hkern u1="&#x219;" u2="&#x135;" k="4" />
<hkern u1="&#x21a;" u2="&#x159;" k="70" />
<hkern u1="&#x21a;" u2="&#x135;" k="20" />
<hkern u1="&#x21a;" u2="&#x131;" k="135" />
<hkern u1="&#x21a;" u2="&#x12d;" k="-6" />
<hkern u1="&#x21a;" u2="&#x12b;" k="-39" />
<hkern u1="&#x21a;" u2="&#x129;" k="-41" />
<hkern u1="&#x21a;" u2="&#x127;" k="-12" />
<hkern u1="&#x21a;" u2="&#xef;" k="-51" />
<hkern u1="&#x21a;" u2="&#xee;" k="-51" />
<hkern u1="&#x21a;" u2="&#xed;" k="45" />
<hkern u1="&#x21a;" u2="&#xec;" k="-55" />
<hkern u1="&#x21a;" u2="&#xdf;" k="76" />
<hkern u1="&#x1e80;" u2="&#x159;" k="8" />
<hkern u1="&#x1e80;" u2="&#x131;" k="23" />
<hkern u1="&#x1e80;" u2="&#x12b;" k="-20" />
<hkern u1="&#x1e80;" u2="&#x129;" k="-29" />
<hkern u1="&#x1e80;" u2="&#xef;" k="-37" />
<hkern u1="&#x1e80;" u2="&#xee;" k="-39" />
<hkern u1="&#x1e80;" u2="&#xec;" k="-27" />
<hkern u1="&#x1e80;" u2="&#xdf;" k="6" />
<hkern u1="&#x1e82;" u2="&#x159;" k="8" />
<hkern u1="&#x1e82;" u2="&#x131;" k="23" />
<hkern u1="&#x1e82;" u2="&#x12b;" k="-20" />
<hkern u1="&#x1e82;" u2="&#x129;" k="-29" />
<hkern u1="&#x1e82;" u2="&#xef;" k="-37" />
<hkern u1="&#x1e82;" u2="&#xee;" k="-39" />
<hkern u1="&#x1e82;" u2="&#xec;" k="-27" />
<hkern u1="&#x1e82;" u2="&#xdf;" k="6" />
<hkern u1="&#x1e84;" u2="&#x159;" k="8" />
<hkern u1="&#x1e84;" u2="&#x131;" k="23" />
<hkern u1="&#x1e84;" u2="&#x12b;" k="-20" />
<hkern u1="&#x1e84;" u2="&#x129;" k="-29" />
<hkern u1="&#x1e84;" u2="&#xef;" k="-37" />
<hkern u1="&#x1e84;" u2="&#xee;" k="-39" />
<hkern u1="&#x1e84;" u2="&#xec;" k="-27" />
<hkern u1="&#x1e84;" u2="&#xdf;" k="6" />
<hkern u1="&#x1ef2;" u2="&#x15d;" k="72" />
<hkern u1="&#x1ef2;" u2="&#x159;" k="76" />
<hkern u1="&#x1ef2;" u2="&#x135;" k="53" />
<hkern u1="&#x1ef2;" u2="&#x131;" k="139" />
<hkern u1="&#x1ef2;" u2="&#x12d;" k="-20" />
<hkern u1="&#x1ef2;" u2="&#x12b;" k="-39" />
<hkern u1="&#x1ef2;" u2="&#x129;" k="-45" />
<hkern u1="&#x1ef2;" u2="&#x127;" k="-6" />
<hkern u1="&#x1ef2;" u2="&#xef;" k="-55" />
<hkern u1="&#x1ef2;" u2="&#xee;" k="-39" />
<hkern u1="&#x1ef2;" u2="&#xed;" k="57" />
<hkern u1="&#x1ef2;" u2="&#xec;" k="-57" />
<hkern u1="&#x1ef2;" u2="&#xe4;" k="72" />
<hkern u1="&#x1ef2;" u2="&#xe0;" k="76" />
<hkern u1="&#x1ef2;" u2="&#xdf;" k="78" />
<hkern u1="&#x2013;" u2="&#x166;" k="96" />
<hkern u1="&#x2013;" u2="&#x135;" k="27" />
<hkern u1="&#x2014;" u2="&#x166;" k="96" />
<hkern u1="&#x2014;" u2="&#x135;" k="27" />
<hkern u1="&#x2018;" u2="&#x12d;" k="-14" />
<hkern u1="&#x2018;" u2="&#x12b;" k="-33" />
<hkern u1="&#x2018;" u2="&#x129;" k="-39" />
<hkern u1="&#x2018;" u2="&#xef;" k="-47" />
<hkern u1="&#x2018;" u2="&#xee;" k="-47" />
<hkern u1="&#x2018;" u2="&#xec;" k="-63" />
<hkern u1="&#x2019;" u2="&#x135;" k="8" />
<hkern u1="&#x2019;" u2="&#x12d;" k="-29" />
<hkern u1="&#x2019;" u2="&#x12b;" k="-47" />
<hkern u1="&#x2019;" u2="&#x129;" k="-51" />
<hkern u1="&#x2019;" u2="&#x127;" k="-6" />
<hkern u1="&#x2019;" u2="&#xef;" k="-63" />
<hkern u1="&#x2019;" u2="&#xee;" k="-51" />
<hkern u1="&#x2019;" u2="&#xec;" k="-92" />
<hkern u1="&#x201a;" u2="&#x166;" k="123" />
<hkern u1="&#x201a;" u2="&#x135;" k="27" />
<hkern u1="&#x201c;" u2="&#x12d;" k="-14" />
<hkern u1="&#x201c;" u2="&#x12b;" k="-33" />
<hkern u1="&#x201c;" u2="&#x129;" k="-39" />
<hkern u1="&#x201c;" u2="&#xef;" k="-47" />
<hkern u1="&#x201c;" u2="&#xee;" k="-47" />
<hkern u1="&#x201c;" u2="&#xec;" k="-63" />
<hkern u1="&#x201d;" u2="&#x135;" k="8" />
<hkern u1="&#x201d;" u2="&#x12d;" k="-29" />
<hkern u1="&#x201d;" u2="&#x12b;" k="-47" />
<hkern u1="&#x201d;" u2="&#x129;" k="-51" />
<hkern u1="&#x201d;" u2="&#x127;" k="-6" />
<hkern u1="&#x201d;" u2="&#xef;" k="-63" />
<hkern u1="&#x201d;" u2="&#xee;" k="-51" />
<hkern u1="&#x201d;" u2="&#xec;" k="-92" />
<hkern u1="&#x201e;" u2="&#x166;" k="123" />
<hkern u1="&#x201e;" u2="&#x135;" k="27" />
<hkern u1="&#x2026;" u2="&#x135;" k="27" />
<hkern u1="&#x2039;" u2="&#x129;" k="-16" />
<hkern u1="&#x2039;" u2="&#xef;" k="-16" />
<hkern u1="&#x2039;" u2="&#xee;" k="-27" />
<hkern u1="&#x203a;" u2="&#x166;" k="78" />
<hkern g1="uniFB01" u2="&#x129;" k="-12" />
<hkern g1="uniFB01" u2="&#xef;" k="-14" />
<hkern g1="uniFB01" u2="&#xee;" k="-23" />
<hkern g1="uniFB01" u2="&#xec;" k="-47" />
<hkern g1="uniFB02" u2="&#x129;" k="-16" />
<hkern g1="uniFB02" u2="&#xef;" k="-16" />
<hkern g1="uniFB02" u2="&#xee;" k="-27" />
<hkern g1="uniFB02" u2="&#xec;" k="-47" />
<hkern g1="colon,semicolon" 	g2="quoteright,quotedblright" 	k="23" />
<hkern g1="colon,semicolon" 	g2="quotedbl,quotesingle" 	k="12" />
<hkern g1="guillemotright,guilsinglright" 	g2="quoteright,quotedblright" 	k="57" />
<hkern g1="guillemotright,guilsinglright" 	g2="quotedbl,quotesingle" 	k="59" />
<hkern g1="hyphen,endash,emdash" 	g2="quoteright,quotedblright" 	k="94" />
<hkern g1="hyphen,endash,emdash" 	g2="quotedbl,quotesingle" 	k="100" />
<hkern g1="hyphen,endash,emdash" 	g2="one" 	k="63" />
<hkern g1="hyphen,endash,emdash" 	g2="seven" 	k="109" />
<hkern g1="hyphen,endash,emdash" 	g2="two" 	k="68" />
<hkern g1="hyphen,endash,emdash" 	g2="five" 	k="12" />
<hkern g1="hyphen,endash,emdash" 	g2="nine" 	k="18" />
<hkern g1="hyphen,endash,emdash" 	g2="three" 	k="31" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteright,quotedblright" 	k="197" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quotedbl,quotesingle" 	k="201" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="one" 	k="135" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="seven" 	k="61" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="nine" 	k="86" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="eight" 	k="14" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="four" 	k="23" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteleft,quotedblleft" 	k="199" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="hyphen,endash,emdash" 	k="76" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="six" 	k="16" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="zero" 	k="23" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="211" />
<hkern g1="quoteright,quotedblright" 	g2="hyphen,endash,emdash" 	k="129" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="215" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotleft,guilsinglleft" 	k="90" />
<hkern g1="quoteright,quotedblright" 	g2="ampersand" 	k="39" />
<hkern g1="quoteright,quotedblright" 	g2="at" 	k="53" />
<hkern g1="quoteright,quotedblright" 	g2="copyright" 	k="37" />
<hkern g1="quoteright,quotedblright" 	g2="registered" 	k="37" />
<hkern g1="quoteright,quotedblright" 	g2="slash" 	k="145" />
<hkern g1="quoteright,quotedblright" 	g2="colon,semicolon" 	k="33" />
<hkern g1="quotedbl,quotesingle" 	g2="four" 	k="94" />
<hkern g1="quotedbl,quotesingle" 	g2="hyphen,endash,emdash" 	k="100" />
<hkern g1="quotedbl,quotesingle" 	g2="six" 	k="70" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="201" />
<hkern g1="quotedbl,quotesingle" 	g2="guillemotleft,guilsinglleft" 	k="59" />
<hkern g1="quotedbl,quotesingle" 	g2="ampersand" 	k="35" />
<hkern g1="quotedbl,quotesingle" 	g2="at" 	k="23" />
<hkern g1="quotedbl,quotesingle" 	g2="copyright" 	k="12" />
<hkern g1="quotedbl,quotesingle" 	g2="registered" 	k="12" />
<hkern g1="quotedbl,quotesingle" 	g2="slash" 	k="133" />
<hkern g1="quotedbl,quotesingle" 	g2="colon,semicolon" 	k="12" />
<hkern g1="backslash" 	g2="quoteright,quotedblright" 	k="129" />
<hkern g1="backslash" 	g2="quotedbl,quotesingle" 	k="133" />
<hkern g1="ampersand" 	g2="quotedbl,quotesingle" 	k="68" />
<hkern g1="ampersand" 	g2="quoteright,quotedblright" 	k="59" />
<hkern g1="at" 	g2="quotedbl,quotesingle" 	k="12" />
<hkern g1="at" 	g2="quoteright,quotedblright" 	k="14" />
<hkern g1="copyright" 	g2="quotedbl,quotesingle" 	k="12" />
<hkern g1="copyright" 	g2="quoteright,quotedblright" 	k="16" />
<hkern g1="registered" 	g2="quotedbl,quotesingle" 	k="12" />
<hkern g1="registered" 	g2="quoteright,quotedblright" 	k="16" />
<hkern g1="eight" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="14" />
<hkern g1="five" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="12" />
<hkern g1="four" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="23" />
<hkern g1="four" 	g2="quotedbl,quotesingle" 	k="49" />
<hkern g1="nine" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="119" />
<hkern g1="nine" 	g2="hyphen,endash,emdash" 	k="23" />
<hkern g1="seven" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="125" />
<hkern g1="seven" 	g2="hyphen,endash,emdash" 	k="63" />
<hkern g1="six" 	g2="quotedbl,quotesingle" 	k="63" />
<hkern g1="two" 	g2="hyphen,endash,emdash" 	k="35" />
<hkern g1="zero" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="23" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="J,Jcircumflex" 	k="18" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="29" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="T,Tcaron,Tbar,uni021A" 	k="150" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="57" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="156" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="d,q,dcaron,dcroat" 	k="12" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="f,uniFB01,uniFB02" 	k="45" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="14" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="hyphen,endash,emdash" 	k="43" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="12" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="quoteleft,quotedblleft" 	k="123" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="quoteright,quotedblright" 	k="129" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="quotedbl,quotesingle" 	k="133" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="t,tcaron,tbar,uni021B" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="12" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="47" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="74" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="V" 	k="106" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="asterisk" 	k="123" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="backslash" 	k="143" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="copyright" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="eth" 	k="12" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="longs" 	k="45" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="nine" 	k="55" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="one" 	k="88" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="ordfeminine" 	k="117" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="ordmasculine" 	k="123" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="question" 	k="84" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="registered" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="seven" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="space" 	k="53" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="trademark" 	k="141" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="v" 	k="72" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="zero" 	k="8" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="braceright" 	k="33" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="bracketright" 	k="35" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	g2="parenright" 	k="39" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="39" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="hyphen,endash,emdash" 	k="18" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="V" 	k="37" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="backslash" 	k="31" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="trademark" 	k="8" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="braceright" 	k="41" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="bracketright" 	k="39" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="parenright" 	k="47" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="16" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="AE,AEacute" 	k="12" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="X" 	k="33" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="slash" 	k="31" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="18" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="T,Tcaron,Tbar,uni021A" 	k="27" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="16" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="63" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="V" 	k="43" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="backslash" 	k="49" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="trademark" 	k="29" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="braceright" 	k="53" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="bracketright" 	k="51" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="parenright" 	k="61" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="29" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="AE,AEacute" 	k="29" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="X" 	k="59" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="slash" 	k="47" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="23" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="14" />
<hkern g1="D,Eth,Dcaron,Dcroat" 	g2="x" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="d,q,dcaron,dcroat" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="f,uniFB01,uniFB02" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="guillemotleft,guilsinglleft" 	k="12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="hyphen,endash,emdash" 	k="23" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="t,tcaron,tbar,uni021B" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="27" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="V" 	k="12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="eth" 	k="12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="longs" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="one" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="v" 	k="23" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="four" 	k="12" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="T,Tcaron,Tbar,uni021A" 	k="14" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="14" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="51" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="V" 	k="47" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="backslash" 	k="37" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="trademark" 	k="18" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="braceright" 	k="45" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="bracketright" 	k="41" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="parenright" 	k="51" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="31" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="AE,AEacute" 	k="27" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="X" 	k="51" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="slash" 	k="39" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="23" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="6" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122" 	g2="x" 	k="14" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,uni0145,Ncaron,Eng" 	g2="hyphen,endash,emdash" 	k="10" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,uni0145,Ncaron,Eng" 	g2="eth" 	k="6" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,uni0145,Ncaron,Eng" 	g2="braceright" 	k="12" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,uni0145,Ncaron,Eng" 	g2="bracketright" 	k="12" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,uni0145,Ncaron,Eng" 	g2="parenright" 	k="12" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,uni0145,Ncaron,Eng" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="10" />
<hkern g1="K,uni0136" 	g2="J,Jcircumflex" 	k="18" />
<hkern g1="K,uni0136" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="74" />
<hkern g1="K,uni0136" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="35" />
<hkern g1="K,uni0136" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="6" />
<hkern g1="K,uni0136" 	g2="d,q,dcaron,dcroat" 	k="57" />
<hkern g1="K,uni0136" 	g2="f,uniFB01,uniFB02" 	k="47" />
<hkern g1="K,uni0136" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="63" />
<hkern g1="K,uni0136" 	g2="guillemotleft,guilsinglleft" 	k="102" />
<hkern g1="K,uni0136" 	g2="hyphen,endash,emdash" 	k="104" />
<hkern g1="K,uni0136" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="59" />
<hkern g1="K,uni0136" 	g2="quoteleft,quotedblleft" 	k="16" />
<hkern g1="K,uni0136" 	g2="t,tcaron,tbar,uni021B" 	k="51" />
<hkern g1="K,uni0136" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="43" />
<hkern g1="K,uni0136" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="86" />
<hkern g1="K,uni0136" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="102" />
<hkern g1="K,uni0136" 	g2="copyright" 	k="49" />
<hkern g1="K,uni0136" 	g2="eth" 	k="55" />
<hkern g1="K,uni0136" 	g2="longs" 	k="47" />
<hkern g1="K,uni0136" 	g2="nine" 	k="10" />
<hkern g1="K,uni0136" 	g2="one" 	k="41" />
<hkern g1="K,uni0136" 	g2="registered" 	k="49" />
<hkern g1="K,uni0136" 	g2="space" 	k="14" />
<hkern g1="K,uni0136" 	g2="v" 	k="100" />
<hkern g1="K,uni0136" 	g2="zero" 	k="10" />
<hkern g1="K,uni0136" 	g2="four" 	k="16" />
<hkern g1="K,uni0136" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="14" />
<hkern g1="K,uni0136" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="20" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="18" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="T,Tcaron,Tbar,uni021A" 	k="231" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="31" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="82" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="211" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="d,q,dcaron,dcroat" 	k="16" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="f,uniFB01,uniFB02" 	k="68" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="16" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="guillemotleft,guilsinglleft" 	k="117" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="hyphen,endash,emdash" 	k="100" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="16" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="quoteleft,quotedblleft" 	k="221" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="quoteright,quotedblright" 	k="221" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="quotedbl,quotesingle" 	k="219" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="t,tcaron,tbar,uni021B" 	k="109" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="25" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="115" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="188" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="V" 	k="188" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="asterisk" 	k="236" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="backslash" 	k="166" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="copyright" 	k="41" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="eth" 	k="12" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="longs" 	k="68" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="nine" 	k="76" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="one" 	k="143" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="ordfeminine" 	k="236" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="ordmasculine" 	k="238" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="question" 	k="111" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="registered" 	k="41" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="seven" 	k="43" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="space" 	k="51" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="trademark" 	k="238" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="v" 	k="176" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="zero" 	k="12" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="braceright" 	k="25" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="bracketright" 	k="27" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="parenright" 	k="31" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="four" 	k="29" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="periodcentered" 	k="334" />
<hkern g1="L,Lacute,uni013B,Lcaron,Ldot,Lslash" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="27" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="16" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="63" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="V" 	k="43" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="backslash" 	k="47" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="seven" 	k="12" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="trademark" 	k="27" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="braceright" 	k="51" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="bracketright" 	k="49" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="parenright" 	k="59" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="29" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="AE,AEacute" 	k="29" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="X" 	k="55" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="slash" 	k="45" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="25" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="12" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="x" 	k="8" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="J,Jcircumflex" 	k="8" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="4" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="16" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="18" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="53" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="d,q,dcaron,dcroat" 	k="12" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="12" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="guillemotleft,guilsinglleft" 	k="12" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="hyphen,endash,emdash" 	k="35" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="12" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="V" 	k="45" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="backslash" 	k="27" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="eth" 	k="23" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="trademark" 	k="10" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="braceright" 	k="23" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="bracketright" 	k="25" />
<hkern g1="R,Racute,uni0156,Rcaron" 	g2="parenright" 	k="29" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="T,Tcaron,Tbar,uni021A" 	k="16" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="20" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="55" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="hyphen,endash,emdash" 	k="10" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="4" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="V" 	k="49" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="backslash" 	k="39" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="trademark" 	k="23" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="v" 	k="6" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="braceright" 	k="41" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="bracketright" 	k="41" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="parenright" 	k="49" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="23" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="AE,AEacute" 	k="12" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="X" 	k="39" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="slash" 	k="16" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	g2="x" 	k="14" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="J,Jcircumflex" 	k="8" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="27" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="6" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="d,q,dcaron,dcroat" 	k="182" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="f,uniFB01,uniFB02" 	k="80" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="178" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="guillemotleft,guilsinglleft" 	k="113" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="hyphen,endash,emdash" 	k="125" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="188" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="t,tcaron,tbar,uni021B" 	k="61" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="129" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="113" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="125" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="asterisk" 	k="-6" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="copyright" 	k="39" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="eth" 	k="166" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="longs" 	k="82" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="one" 	k="20" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="registered" 	k="39" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="space" 	k="49" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="v" 	k="117" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="zero" 	k="12" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="150" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="AE,AEacute" 	k="162" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="slash" 	k="139" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="129" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="x" 	k="115" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="four" 	k="113" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="184" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="172" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="guillemotright,guilsinglright" 	k="53" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="b,h,k,germandbls,thorn,hcircumflex,hbar,uni0137" 	k="37" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij" 	k="12" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="l,lacute,uni013C,lcaron,ldot,lslash" 	k="37" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="135" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="z,zacute,zdotaccent,zcaron" 	k="137" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="ampersand" 	k="35" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="j" 	k="25" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="six" 	k="98" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="colon,semicolon" 	k="66" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="at" 	k="53" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="hyphen,endash,emdash" 	k="10" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="6" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="eth" 	k="8" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="braceright" 	k="12" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="bracketright" 	k="12" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="parenright" 	k="14" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="31" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="AE,AEacute" 	k="35" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="X" 	k="6" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="slash" 	k="49" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="35" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="x" 	k="4" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="6" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis,Jcircumflex,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="6" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="J,Jcircumflex" 	k="16" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="16" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="12" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="d,q,dcaron,dcroat" 	k="39" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="39" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="guillemotleft,guilsinglleft" 	k="16" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="hyphen,endash,emdash" 	k="35" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="41" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="18" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="eth" 	k="51" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="space" 	k="25" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="57" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="AE,AEacute" 	k="78" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="slash" 	k="74" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="63" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="x" 	k="6" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="four" 	k="18" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="43" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="31" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="23" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="z,zacute,zdotaccent,zcaron" 	k="8" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="ampersand" 	k="20" />
<hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	g2="six" 	k="23" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="J,Jcircumflex" 	k="37" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="63" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="49" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="d,q,dcaron,dcroat" 	k="172" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="f,uniFB01,uniFB02" 	k="76" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="172" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="guillemotleft,guilsinglleft" 	k="121" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="hyphen,endash,emdash" 	k="131" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="176" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="quoteleft,quotedblleft" 	k="12" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="t,tcaron,tbar,uni021B" 	k="66" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="131" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="104" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="109" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="copyright" 	k="74" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="eth" 	k="184" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="longs" 	k="76" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="nine" 	k="29" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="one" 	k="33" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="question" 	k="20" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="registered" 	k="74" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="space" 	k="59" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="trademark" 	k="-12" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="v" 	k="104" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="zero" 	k="45" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="156" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="AE,AEacute" 	k="184" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="slash" 	k="172" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="158" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="x" 	k="113" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="four" 	k="141" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="203" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="184" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="b,h,k,germandbls,thorn,hcircumflex,hbar,uni0137" 	k="6" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="l,lacute,uni013C,lcaron,ldot,lslash" 	k="6" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="139" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="z,zacute,zdotaccent,zcaron" 	k="125" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="ampersand" 	k="70" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="six" 	k="129" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="colon,semicolon" 	k="76" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="at" 	k="90" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="eight" 	k="43" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="five" 	k="37" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	g2="two" 	k="37" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="12" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="d,q,dcaron,dcroat" 	k="25" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="f,uniFB01,uniFB02" 	k="18" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="29" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="guillemotleft,guilsinglleft" 	k="63" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="hyphen,endash,emdash" 	k="78" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="23" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="t,tcaron,tbar,uni021B" 	k="18" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="25" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="27" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="39" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="copyright" 	k="23" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="eth" 	k="23" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="longs" 	k="18" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="registered" 	k="20" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="v" 	k="39" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="four" 	k="16" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="6" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="8" />
<hkern g1="B" 	g2="T,Tcaron,Tbar,uni021A" 	k="20" />
<hkern g1="B" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="6" />
<hkern g1="B" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="47" />
<hkern g1="B" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="18" />
<hkern g1="B" 	g2="AE,AEacute" 	k="14" />
<hkern g1="B" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="14" />
<hkern g1="F" 	g2="J,Jcircumflex" 	k="8" />
<hkern g1="F" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="10" />
<hkern g1="F" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="10" />
<hkern g1="F" 	g2="d,q,dcaron,dcroat" 	k="20" />
<hkern g1="F" 	g2="f,uniFB01,uniFB02" 	k="25" />
<hkern g1="F" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="20" />
<hkern g1="F" 	g2="hyphen,endash,emdash" 	k="16" />
<hkern g1="F" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="20" />
<hkern g1="F" 	g2="t,tcaron,tbar,uni021B" 	k="18" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="16" />
<hkern g1="F" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="23" />
<hkern g1="F" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="27" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="84" />
<hkern g1="F" 	g2="AE,AEacute" 	k="117" />
<hkern g1="F" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="98" />
<hkern g1="F" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="51" />
<hkern g1="F" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="27" />
<hkern g1="F" 	g2="guillemotright,guilsinglright" 	k="16" />
<hkern g1="F" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="23" />
<hkern g1="F" 	g2="z,zacute,zdotaccent,zcaron" 	k="55" />
<hkern g1="F" 	g2="colon,semicolon" 	k="10" />
<hkern g1="IJ" 	g2="hyphen,endash,emdash" 	k="10" />
<hkern g1="IJ" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="6" />
<hkern g1="IJ" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="29" />
<hkern g1="IJ" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="27" />
<hkern g1="IJ" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="6" />
<hkern g1="IJ" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="6" />
<hkern g1="P" 	g2="J,Jcircumflex" 	k="27" />
<hkern g1="P" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="8" />
<hkern g1="P" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="41" />
<hkern g1="P" 	g2="d,q,dcaron,dcroat" 	k="12" />
<hkern g1="P" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="8" />
<hkern g1="P" 	g2="hyphen,endash,emdash" 	k="23" />
<hkern g1="P" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="14" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="92" />
<hkern g1="P" 	g2="AE,AEacute" 	k="131" />
<hkern g1="P" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="135" />
<hkern g1="P" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="12" />
<hkern g1="P" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="23" />
<hkern g1="Thorn" 	g2="T,Tcaron,Tbar,uni021A" 	k="90" />
<hkern g1="Thorn" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="23" />
<hkern g1="Thorn" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="102" />
<hkern g1="Thorn" 	g2="quoteleft,quotedblleft" 	k="35" />
<hkern g1="Thorn" 	g2="quoteright,quotedblright" 	k="37" />
<hkern g1="Thorn" 	g2="quotedbl,quotesingle" 	k="27" />
<hkern g1="Thorn" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="4" />
<hkern g1="Thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="43" />
<hkern g1="Thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="68" />
<hkern g1="Thorn" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="41" />
<hkern g1="Thorn" 	g2="z,zacute,zdotaccent,zcaron" 	k="6" />
<hkern g1="V" 	g2="J,Jcircumflex" 	k="39" />
<hkern g1="V" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="43" />
<hkern g1="V" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="47" />
<hkern g1="V" 	g2="d,q,dcaron,dcroat" 	k="106" />
<hkern g1="V" 	g2="f,uniFB01,uniFB02" 	k="29" />
<hkern g1="V" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="104" />
<hkern g1="V" 	g2="guillemotleft,guilsinglleft" 	k="66" />
<hkern g1="V" 	g2="hyphen,endash,emdash" 	k="80" />
<hkern g1="V" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="109" />
<hkern g1="V" 	g2="t,tcaron,tbar,uni021B" 	k="23" />
<hkern g1="V" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="70" />
<hkern g1="V" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="35" />
<hkern g1="V" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="41" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="106" />
<hkern g1="V" 	g2="AE,AEacute" 	k="133" />
<hkern g1="V" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="129" />
<hkern g1="V" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="119" />
<hkern g1="V" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="100" />
<hkern g1="V" 	g2="guillemotright,guilsinglright" 	k="25" />
<hkern g1="V" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="76" />
<hkern g1="V" 	g2="z,zacute,zdotaccent,zcaron" 	k="59" />
<hkern g1="V" 	g2="colon,semicolon" 	k="43" />
<hkern g1="X" 	g2="J,Jcircumflex" 	k="20" />
<hkern g1="X" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="57" />
<hkern g1="X" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="35" />
<hkern g1="X" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="6" />
<hkern g1="X" 	g2="d,q,dcaron,dcroat" 	k="49" />
<hkern g1="X" 	g2="f,uniFB01,uniFB02" 	k="45" />
<hkern g1="X" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="59" />
<hkern g1="X" 	g2="guillemotleft,guilsinglleft" 	k="90" />
<hkern g1="X" 	g2="hyphen,endash,emdash" 	k="92" />
<hkern g1="X" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="53" />
<hkern g1="X" 	g2="quoteleft,quotedblleft" 	k="18" />
<hkern g1="X" 	g2="t,tcaron,tbar,uni021B" 	k="51" />
<hkern g1="X" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="41" />
<hkern g1="X" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="72" />
<hkern g1="X" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="94" />
<hkern g1="X" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="8" />
<hkern g1="X" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="18" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="T,Tcaron,Tbar,uni021A" 	k="188" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="37" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="164" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="f,uniFB01,uniFB02" 	k="10" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="quoteright,quotedblright" 	k="45" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="quotedbl,quotesingle" 	k="47" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="t,tcaron,tbar,uni021B" 	k="10" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="10" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="29" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="V" 	k="104" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="asterisk" 	k="39" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="backslash" 	k="106" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="braceright" 	k="47" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="longs" 	k="10" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="ordfeminine" 	k="31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="ordmasculine" 	k="39" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="parenright" 	k="59" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="question" 	k="35" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="trademark" 	k="63" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="v" 	k="27" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="8" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="8" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="quoteleft,quotedblleft" 	k="37" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek" 	g2="bracketright" 	k="39" />
<hkern g1="b,p,thorn" 	g2="T,Tcaron,Tbar,uni021A" 	k="182" />
<hkern g1="b,p,thorn" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="39" />
<hkern g1="b,p,thorn" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="172" />
<hkern g1="b,p,thorn" 	g2="f,uniFB01,uniFB02" 	k="10" />
<hkern g1="b,p,thorn" 	g2="quoteright,quotedblright" 	k="68" />
<hkern g1="b,p,thorn" 	g2="quotedbl,quotesingle" 	k="63" />
<hkern g1="b,p,thorn" 	g2="t,tcaron,tbar,uni021B" 	k="8" />
<hkern g1="b,p,thorn" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="29" />
<hkern g1="b,p,thorn" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="43" />
<hkern g1="b,p,thorn" 	g2="V" 	k="106" />
<hkern g1="b,p,thorn" 	g2="asterisk" 	k="47" />
<hkern g1="b,p,thorn" 	g2="backslash" 	k="109" />
<hkern g1="b,p,thorn" 	g2="braceright" 	k="78" />
<hkern g1="b,p,thorn" 	g2="longs" 	k="10" />
<hkern g1="b,p,thorn" 	g2="ordfeminine" 	k="39" />
<hkern g1="b,p,thorn" 	g2="ordmasculine" 	k="47" />
<hkern g1="b,p,thorn" 	g2="parenright" 	k="88" />
<hkern g1="b,p,thorn" 	g2="question" 	k="39" />
<hkern g1="b,p,thorn" 	g2="trademark" 	k="74" />
<hkern g1="b,p,thorn" 	g2="v" 	k="37" />
<hkern g1="b,p,thorn" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="18" />
<hkern g1="b,p,thorn" 	g2="quoteleft,quotedblleft" 	k="55" />
<hkern g1="b,p,thorn" 	g2="bracketright" 	k="70" />
<hkern g1="b,p,thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="12" />
<hkern g1="b,p,thorn" 	g2="X" 	k="49" />
<hkern g1="b,p,thorn" 	g2="slash" 	k="39" />
<hkern g1="b,p,thorn" 	g2="x" 	k="33" />
<hkern g1="b,p,thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="14" />
<hkern g1="b,p,thorn" 	g2="z,zacute,zdotaccent,zcaron" 	k="8" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="199" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="25" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="184" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="quoteright,quotedblright" 	k="45" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="quotedbl,quotesingle" 	k="47" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="23" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="35" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="V" 	k="94" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="asterisk" 	k="37" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="backslash" 	k="98" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="braceright" 	k="76" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="ordfeminine" 	k="29" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="ordmasculine" 	k="39" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="parenright" 	k="86" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="question" 	k="16" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="trademark" 	k="63" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="v" 	k="31" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="6" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="quoteleft,quotedblleft" 	k="37" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="bracketright" 	k="68" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="X" 	k="33" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="slash" 	k="20" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="x" 	k="16" />
<hkern g1="d,dcroat" 	g2="T,Tcaron,Tbar,uni021A" 	k="37" />
<hkern g1="d,dcroat" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="6" />
<hkern g1="dcaron,lcaron" 	g2="f,uniFB01,uniFB02" 	k="-20" />
<hkern g1="dcaron,lcaron" 	g2="quoteright,quotedblright" 	k="-51" />
<hkern g1="dcaron,lcaron" 	g2="quotedbl,quotesingle" 	k="-111" />
<hkern g1="dcaron,lcaron" 	g2="t,tcaron,tbar,uni021B" 	k="-25" />
<hkern g1="dcaron,lcaron" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="-20" />
<hkern g1="dcaron,lcaron" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="-23" />
<hkern g1="dcaron,lcaron" 	g2="asterisk" 	k="-106" />
<hkern g1="dcaron,lcaron" 	g2="backslash" 	k="-111" />
<hkern g1="dcaron,lcaron" 	g2="braceright" 	k="-133" />
<hkern g1="dcaron,lcaron" 	g2="longs" 	k="-20" />
<hkern g1="dcaron,lcaron" 	g2="ordfeminine" 	k="-51" />
<hkern g1="dcaron,lcaron" 	g2="ordmasculine" 	k="-66" />
<hkern g1="dcaron,lcaron" 	g2="parenright" 	k="-135" />
<hkern g1="dcaron,lcaron" 	g2="question" 	k="-66" />
<hkern g1="dcaron,lcaron" 	g2="trademark" 	k="-176" />
<hkern g1="dcaron,lcaron" 	g2="v" 	k="-27" />
<hkern g1="dcaron,lcaron" 	g2="quoteleft,quotedblleft" 	k="-8" />
<hkern g1="dcaron,lcaron" 	g2="bracketright" 	k="-131" />
<hkern g1="dcaron,lcaron" 	g2="slash" 	k="47" />
<hkern g1="dcaron,lcaron" 	g2="x" 	k="-16" />
<hkern g1="dcaron,lcaron" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="dcaron,lcaron" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="49" />
<hkern g1="dcaron,lcaron" 	g2="colon,semicolon" 	k="-16" />
<hkern g1="dcaron,lcaron" 	g2="d,q,dcaron,dcroat" 	k="43" />
<hkern g1="dcaron,lcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="39" />
<hkern g1="dcaron,lcaron" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="dcaron,lcaron" 	g2="guillemotright,guilsinglright" 	k="-27" />
<hkern g1="dcaron,lcaron" 	g2="b,h,k,germandbls,thorn,hcircumflex,hbar,uni0137" 	k="-113" />
<hkern g1="dcaron,lcaron" 	g2="hyphen,endash,emdash" 	k="41" />
<hkern g1="dcaron,lcaron" 	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij" 	k="-121" />
<hkern g1="dcaron,lcaron" 	g2="l,lacute,uni013C,lcaron,ldot,lslash" 	k="-113" />
<hkern g1="dcaron,lcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="55" />
<hkern g1="dcaron,lcaron" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="37" />
<hkern g1="dcaron,lcaron" 	g2="bar" 	k="-72" />
<hkern g1="dcaron,lcaron" 	g2="exclam" 	k="-94" />
<hkern g1="dcaron,lcaron" 	g2="j" 	k="-100" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="190" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="35" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="201" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="f,uniFB01,uniFB02" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="quoteright,quotedblright" 	k="53" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="quotedbl,quotesingle" 	k="53" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="t,tcaron,tbar,uni021B" 	k="4" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="43" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="V" 	k="113" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="asterisk" 	k="43" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="backslash" 	k="104" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="braceright" 	k="76" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="longs" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="ordfeminine" 	k="37" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="ordmasculine" 	k="45" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="parenright" 	k="86" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="question" 	k="39" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="trademark" 	k="70" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="v" 	k="39" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="12" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="bracketright" 	k="68" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="X" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="slash" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="x" 	k="29" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="z,zacute,zdotaccent,zcaron" 	k="4" />
<hkern g1="f" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="-10" />
<hkern g1="f" 	g2="quotedbl,quotesingle" 	k="-6" />
<hkern g1="f" 	g2="V" 	k="-6" />
<hkern g1="f" 	g2="asterisk" 	k="-6" />
<hkern g1="f" 	g2="braceright" 	k="-18" />
<hkern g1="f" 	g2="ordmasculine" 	k="-8" />
<hkern g1="f" 	g2="parenright" 	k="-23" />
<hkern g1="f" 	g2="trademark" 	k="-12" />
<hkern g1="f" 	g2="bracketright" 	k="-18" />
<hkern g1="f" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="84" />
<hkern g1="f" 	g2="slash" 	k="86" />
<hkern g1="f" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="78" />
<hkern g1="f" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="14" />
<hkern g1="f" 	g2="d,q,dcaron,dcroat" 	k="12" />
<hkern g1="f" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="12" />
<hkern g1="f" 	g2="guillemotleft,guilsinglleft" 	k="16" />
<hkern g1="f" 	g2="hyphen,endash,emdash" 	k="45" />
<hkern g1="f" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="16" />
<hkern g1="f" 	g2="eth" 	k="51" />
<hkern g1="f" 	g2="space" 	k="43" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex,uniFB01" 	g2="T,Tcaron,Tbar,uni021A" 	k="12" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="T,Tcaron,Tbar,uni021A" 	k="115" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="6" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="106" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="quoteright,quotedblright" 	k="12" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="V" 	k="43" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="asterisk" 	k="10" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="backslash" 	k="70" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="braceright" 	k="29" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="parenright" 	k="37" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="trademark" 	k="39" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="4" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="bracketright" 	k="31" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="20" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="d,q,dcaron,dcroat" 	k="47" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="49" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="guillemotleft,guilsinglleft" 	k="45" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="hyphen,endash,emdash" 	k="74" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="49" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="20" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="eth" 	k="55" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="space" 	k="14" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="J,Jcircumflex" 	k="23" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="18" />
<hkern g1="k,uni0137,kgreenlandic" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="4" />
<hkern g1="l,lacute,uni013C,lslash,uniFB02" 	g2="T,Tcaron,Tbar,uni021A" 	k="37" />
<hkern g1="l,lacute,uni013C,lslash,uniFB02" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="6" />
<hkern g1="l,lacute,uni013C,lslash,uniFB02" 	g2="periodcentered" 	k="84" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="T,Tcaron,Tbar,uni021A" 	k="188" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="39" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="170" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="f,uniFB01,uniFB02" 	k="14" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="quoteright,quotedblright" 	k="51" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="quotedbl,quotesingle" 	k="53" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="t,tcaron,tbar,uni021B" 	k="12" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="16" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="37" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="V" 	k="106" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="asterisk" 	k="43" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="backslash" 	k="109" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="braceright" 	k="49" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="longs" 	k="14" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="ordfeminine" 	k="33" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="ordmasculine" 	k="43" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="parenright" 	k="59" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="question" 	k="39" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="trademark" 	k="68" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="v" 	k="33" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="10" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="8" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="quoteleft,quotedblleft" 	k="43" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,uni0146,ncaron,napostrophe,eng" 	g2="bracketright" 	k="39" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="188" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="41" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="176" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="f,uniFB01,uniFB02" 	k="12" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quoteright,quotedblright" 	k="53" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quotedbl,quotesingle" 	k="55" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="t,tcaron,tbar,uni021B" 	k="8" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="31" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="45" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="V" 	k="109" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="asterisk" 	k="45" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="backslash" 	k="109" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="braceright" 	k="80" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="longs" 	k="12" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="ordfeminine" 	k="37" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="ordmasculine" 	k="43" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="parenright" 	k="92" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="question" 	k="41" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="trademark" 	k="70" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="v" 	k="39" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="23" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quoteleft,quotedblleft" 	k="45" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="bracketright" 	k="72" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="12" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="X" 	k="53" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="slash" 	k="39" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="x" 	k="33" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="14" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="z,zacute,zdotaccent,zcaron" 	k="10" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="117" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="94" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="V" 	k="33" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="backslash" 	k="29" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="braceright" 	k="70" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="parenright" 	k="84" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="trademark" 	k="10" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="59" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="bracketright" 	k="68" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="94" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="X" 	k="90" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="slash" 	k="102" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="111" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="16" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="d,q,dcaron,dcroat" 	k="16" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="16" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="guillemotleft,guilsinglleft" 	k="16" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="hyphen,endash,emdash" 	k="59" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="14" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="eth" 	k="51" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="space" 	k="43" />
<hkern g1="r,racute,uni0157,rcaron" 	g2="J,Jcircumflex" 	k="70" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="T,Tcaron,Tbar,uni021A" 	k="178" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="37" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="188" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="f,uniFB01,uniFB02" 	k="4" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="quoteright,quotedblright" 	k="43" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="quotedbl,quotesingle" 	k="45" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="t,tcaron,tbar,uni021B" 	k="4" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="31" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="41" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="V" 	k="109" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="asterisk" 	k="39" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="backslash" 	k="102" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="braceright" 	k="63" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="longs" 	k="4" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="ordfeminine" 	k="27" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="ordmasculine" 	k="37" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="parenright" 	k="78" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="question" 	k="16" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="trademark" 	k="63" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="v" 	k="39" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="6" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="quoteleft,quotedblleft" 	k="35" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="bracketright" 	k="57" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="X" 	k="18" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="slash" 	k="14" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="x" 	k="12" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,uni0219" 	g2="hyphen,endash,emdash" 	k="16" />
<hkern g1="t,tbar,uni021B" 	g2="T,Tcaron,Tbar,uni021A" 	k="100" />
<hkern g1="t,tbar,uni021B" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="94" />
<hkern g1="t,tbar,uni021B" 	g2="V" 	k="37" />
<hkern g1="t,tbar,uni021B" 	g2="backslash" 	k="55" />
<hkern g1="t,tbar,uni021B" 	g2="braceright" 	k="18" />
<hkern g1="t,tbar,uni021B" 	g2="parenright" 	k="25" />
<hkern g1="t,tbar,uni021B" 	g2="trademark" 	k="18" />
<hkern g1="t,tbar,uni021B" 	g2="bracketright" 	k="18" />
<hkern g1="t,tbar,uni021B" 	g2="guillemotleft,guilsinglleft" 	k="12" />
<hkern g1="t,tbar,uni021B" 	g2="hyphen,endash,emdash" 	k="14" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="T,Tcaron,Tbar,uni021A" 	k="135" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="23" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="139" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="quoteright,quotedblright" 	k="12" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="quotedbl,quotesingle" 	k="10" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="V" 	k="76" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="asterisk" 	k="12" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="backslash" 	k="80" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="braceright" 	k="45" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="parenright" 	k="55" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="trademark" 	k="39" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="6" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="8" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="quoteleft,quotedblleft" 	k="12" />
<hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gcircumflex,gbreve,gdotaccent,uni0123,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="bracketright" 	k="35" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="T,Tcaron,Tbar,uni021A" 	k="113" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="104" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="V" 	k="35" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="backslash" 	k="61" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="braceright" 	k="68" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="parenright" 	k="82" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="trademark" 	k="10" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="37" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="bracketright" 	k="68" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="47" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="X" 	k="72" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="slash" 	k="86" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="76" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="z,zacute,zdotaccent,zcaron" 	k="6" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="35" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="d,q,dcaron,dcroat" 	k="29" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="29" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="guillemotleft,guilsinglleft" 	k="12" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="hyphen,endash,emdash" 	k="29" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="31" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="27" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="eth" 	k="41" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="space" 	k="45" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="J,Jcircumflex" 	k="29" />
<hkern g1="w,wcircumflex,wgrave,wacute,wdieresis" 	g2="ampersand" 	k="8" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="T,Tcaron,Tbar,uni021A" 	k="117" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="102" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="V" 	k="37" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="backslash" 	k="59" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="braceright" 	k="74" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="parenright" 	k="88" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="question" 	k="-6" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="trademark" 	k="10" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="57" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="bracketright" 	k="74" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="76" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="X" 	k="90" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="slash" 	k="109" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="109" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="z,zacute,zdotaccent,zcaron" 	k="8" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="43" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="d,q,dcaron,dcroat" 	k="37" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="37" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="guillemotleft,guilsinglleft" 	k="16" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="hyphen,endash,emdash" 	k="43" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="39" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="33" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="eth" 	k="59" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="space" 	k="53" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="J,Jcircumflex" 	k="53" />
<hkern g1="y,yacute,ydieresis,ycircumflex,ygrave" 	g2="ampersand" 	k="27" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="143" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="18" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="137" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="8" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="V" 	k="68" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="backslash" 	k="78" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="braceright" 	k="31" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="parenright" 	k="39" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="trademark" 	k="41" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="v" 	k="6" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="12" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="bracketright" 	k="33" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="d,q,dcaron,dcroat" 	k="8" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="12" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="guillemotleft,guilsinglleft" 	k="23" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="hyphen,endash,emdash" 	k="37" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="12" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="eth" 	k="14" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="J,Jcircumflex" 	k="10" />
<hkern g1="eth" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="16" />
<hkern g1="eth" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="14" />
<hkern g1="eth" 	g2="z,zacute,zdotaccent,zcaron" 	k="4" />
<hkern g1="germandbls" 	g2="f,uniFB01,uniFB02" 	k="6" />
<hkern g1="germandbls" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="germandbls" 	g2="quotedbl,quotesingle" 	k="10" />
<hkern g1="germandbls" 	g2="t,tcaron,tbar,uni021B" 	k="4" />
<hkern g1="germandbls" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="27" />
<hkern g1="germandbls" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="35" />
<hkern g1="germandbls" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="10" />
<hkern g1="germandbls" 	g2="z,zacute,zdotaccent,zcaron" 	k="8" />
<hkern g1="longs" 	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij" 	k="-111" />
<hkern g1="tcaron" 	g2="f,uniFB01,uniFB02" 	k="-37" />
<hkern g1="tcaron" 	g2="quoteright,quotedblright" 	k="-123" />
<hkern g1="tcaron" 	g2="quotedbl,quotesingle" 	k="-182" />
<hkern g1="tcaron" 	g2="t,tcaron,tbar,uni021B" 	k="-63" />
<hkern g1="tcaron" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="-39" />
<hkern g1="tcaron" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="-41" />
<hkern g1="tcaron" 	g2="quoteleft,quotedblleft" 	k="-63" />
<hkern g1="tcaron" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="25" />
<hkern g1="tcaron" 	g2="z,zacute,zdotaccent,zcaron" 	k="-14" />
<hkern g1="tcaron" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="27" />
<hkern g1="tcaron" 	g2="colon,semicolon" 	k="-33" />
<hkern g1="tcaron" 	g2="d,q,dcaron,dcroat" 	k="25" />
<hkern g1="tcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="20" />
<hkern g1="tcaron" 	g2="guillemotleft,guilsinglleft" 	k="10" />
<hkern g1="tcaron" 	g2="guillemotright,guilsinglright" 	k="-78" />
<hkern g1="tcaron" 	g2="b,h,k,germandbls,thorn,hcircumflex,hbar,uni0137" 	k="-156" />
<hkern g1="tcaron" 	g2="hyphen,endash,emdash" 	k="43" />
<hkern g1="tcaron" 	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij" 	k="-166" />
<hkern g1="tcaron" 	g2="l,lacute,uni013C,lcaron,ldot,lslash" 	k="-156" />
<hkern g1="tcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="31" />
<hkern g1="tcaron" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="18" />
<hkern g1="tcaron" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="-16" />
<hkern g1="tcaron" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="-12" />
<hkern g1="v" 	g2="T,Tcaron,Tbar,uni021A" 	k="117" />
<hkern g1="v" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="104" />
<hkern g1="v" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="55" />
<hkern g1="v" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="72" />
<hkern g1="v" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="102" />
<hkern g1="v" 	g2="z,zacute,zdotaccent,zcaron" 	k="8" />
<hkern g1="v" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="45" />
<hkern g1="v" 	g2="d,q,dcaron,dcroat" 	k="37" />
<hkern g1="v" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="37" />
<hkern g1="v" 	g2="guillemotleft,guilsinglleft" 	k="18" />
<hkern g1="v" 	g2="hyphen,endash,emdash" 	k="41" />
<hkern g1="v" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="39" />
<hkern g1="v" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="35" />
<hkern g1="v" 	g2="J,Jcircumflex" 	k="51" />
<hkern g1="x" 	g2="T,Tcaron,Tbar,uni021A" 	k="115" />
<hkern g1="x" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="6" />
<hkern g1="x" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="111" />
<hkern g1="x" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="4" />
<hkern g1="x" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="16" />
<hkern g1="x" 	g2="d,q,dcaron,dcroat" 	k="33" />
<hkern g1="x" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="35" />
<hkern g1="x" 	g2="guillemotleft,guilsinglleft" 	k="35" />
<hkern g1="x" 	g2="hyphen,endash,emdash" 	k="55" />
<hkern g1="x" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="35" />
<hkern g1="x" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="12" />
<hkern g1="x" 	g2="J,Jcircumflex" 	k="25" />
<hkern g1="x" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="8" />
<hkern g1="x" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="4" />
<hkern g1="x" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="4" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="76" />
<hkern g1="colon,semicolon" 	g2="V" 	k="43" />
<hkern g1="colon,semicolon" 	g2="T,Tcaron,Tbar,uni021A" 	k="66" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V" 	k="25" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T,Tcaron,Tbar,uni021A" 	k="53" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="121" />
<hkern g1="guillemotright,guilsinglright" 	g2="V" 	k="66" />
<hkern g1="guillemotright,guilsinglright" 	g2="T,Tcaron,Tbar,uni021A" 	k="113" />
<hkern g1="guillemotright,guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="43" />
<hkern g1="guillemotright,guilsinglright" 	g2="AE,AEacute" 	k="55" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="61" />
<hkern g1="guillemotright,guilsinglright" 	g2="X" 	k="90" />
<hkern g1="guillemotright,guilsinglright" 	g2="x" 	k="35" />
<hkern g1="guillemotright,guilsinglright" 	g2="J,Jcircumflex" 	k="14" />
<hkern g1="guillemotright,guilsinglright" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="12" />
<hkern g1="guillemotright,guilsinglright" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="16" />
<hkern g1="guillemotright,guilsinglright" 	g2="f,uniFB01,uniFB02" 	k="12" />
<hkern g1="guillemotright,guilsinglright" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="12" />
<hkern g1="guillemotright,guilsinglright" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="18" />
<hkern g1="guillemotright,guilsinglright" 	g2="z,zacute,zdotaccent,zcaron" 	k="23" />
<hkern g1="guillemotright,guilsinglright" 	g2="j" 	k="12" />
<hkern g1="guillemotright,guilsinglright" 	g2="v" 	k="18" />
<hkern g1="hyphen,endash,emdash" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="131" />
<hkern g1="hyphen,endash,emdash" 	g2="V" 	k="80" />
<hkern g1="hyphen,endash,emdash" 	g2="T,Tcaron,Tbar,uni021A" 	k="125" />
<hkern g1="hyphen,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="43" />
<hkern g1="hyphen,endash,emdash" 	g2="AE,AEacute" 	k="55" />
<hkern g1="hyphen,endash,emdash" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="68" />
<hkern g1="hyphen,endash,emdash" 	g2="X" 	k="90" />
<hkern g1="hyphen,endash,emdash" 	g2="x" 	k="55" />
<hkern g1="hyphen,endash,emdash" 	g2="J,Jcircumflex" 	k="20" />
<hkern g1="hyphen,endash,emdash" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="25" />
<hkern g1="hyphen,endash,emdash" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="35" />
<hkern g1="hyphen,endash,emdash" 	g2="f,uniFB01,uniFB02" 	k="27" />
<hkern g1="hyphen,endash,emdash" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="29" />
<hkern g1="hyphen,endash,emdash" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="47" />
<hkern g1="hyphen,endash,emdash" 	g2="z,zacute,zdotaccent,zcaron" 	k="39" />
<hkern g1="hyphen,endash,emdash" 	g2="j" 	k="23" />
<hkern g1="hyphen,endash,emdash" 	g2="v" 	k="41" />
<hkern g1="hyphen,endash,emdash" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron" 	k="10" />
<hkern g1="hyphen,endash,emdash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="10" />
<hkern g1="hyphen,endash,emdash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="16" />
<hkern g1="hyphen,endash,emdash" 	g2="t,tcaron,tbar,uni021B" 	k="23" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="158" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="V" 	k="129" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="T,Tcaron,Tbar,uni021A" 	k="129" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="10" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="63" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="f,uniFB01,uniFB02" 	k="47" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="76" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="111" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="j" 	k="16" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="v" 	k="102" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron" 	k="10" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="35" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="t,tcaron,tbar,uni021B" 	k="57" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="25" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="d,q,dcaron,dcroat" 	k="14" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="14" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="14" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="16" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="eth" 	k="10" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="139" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE,AEacute" 	k="164" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="68" />
<hkern g1="quoteleft,quotedblleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="14" />
<hkern g1="quoteleft,quotedblleft" 	g2="d,q,dcaron,dcroat" 	k="82" />
<hkern g1="quoteleft,quotedblleft" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="68" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="70" />
<hkern g1="quoteleft,quotedblleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="14" />
<hkern g1="quoteleft,quotedblleft" 	g2="eth" 	k="84" />
<hkern g1="quoteleft,quotedblleft" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="53" />
<hkern g1="quoteleft,quotedblleft" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="16" />
<hkern g1="quoteright,quotedblright" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="-14" />
<hkern g1="quoteright,quotedblright" 	g2="V" 	k="-6" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="147" />
<hkern g1="quoteright,quotedblright" 	g2="AE,AEacute" 	k="172" />
<hkern g1="quoteright,quotedblright" 	g2="x" 	k="18" />
<hkern g1="quoteright,quotedblright" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="16" />
<hkern g1="quoteright,quotedblright" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="6" />
<hkern g1="quoteright,quotedblright" 	g2="z,zacute,zdotaccent,zcaron" 	k="27" />
<hkern g1="quoteright,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="84" />
<hkern g1="quoteright,quotedblright" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="31" />
<hkern g1="quoteright,quotedblright" 	g2="d,q,dcaron,dcroat" 	k="98" />
<hkern g1="quoteright,quotedblright" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="82" />
<hkern g1="quoteright,quotedblright" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="88" />
<hkern g1="quoteright,quotedblright" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="31" />
<hkern g1="quoteright,quotedblright" 	g2="eth" 	k="86" />
<hkern g1="quoteright,quotedblright" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="70" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="33" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="133" />
<hkern g1="quotedbl,quotesingle" 	g2="AE,AEacute" 	k="158" />
<hkern g1="quotedbl,quotesingle" 	g2="J,Jcircumflex" 	k="6" />
<hkern g1="quotedbl,quotesingle" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="53" />
<hkern g1="quotedbl,quotesingle" 	g2="d,q,dcaron,dcroat" 	k="63" />
<hkern g1="quotedbl,quotesingle" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="51" />
<hkern g1="quotedbl,quotesingle" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="55" />
<hkern g1="quotedbl,quotesingle" 	g2="eth" 	k="80" />
<hkern g1="quotedbl,quotesingle" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="41" />
<hkern g1="quotedbl,quotesingle" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="10" />
<hkern g1="asterisk" 	g2="T,Tcaron,Tbar,uni021A" 	k="-6" />
<hkern g1="asterisk" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="123" />
<hkern g1="asterisk" 	g2="AE,AEacute" 	k="145" />
<hkern g1="asterisk" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="47" />
<hkern g1="asterisk" 	g2="d,q,dcaron,dcroat" 	k="47" />
<hkern g1="asterisk" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="41" />
<hkern g1="asterisk" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="45" />
<hkern g1="asterisk" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="10" />
<hkern g1="asterisk" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="35" />
<hkern g1="asterisk" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="12" />
<hkern g1="backslash" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="172" />
<hkern g1="backslash" 	g2="T,Tcaron,Tbar,uni021A" 	k="139" />
<hkern g1="backslash" 	g2="J,Jcircumflex" 	k="12" />
<hkern g1="backslash" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="14" />
<hkern g1="backslash" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="74" />
<hkern g1="backslash" 	g2="f,uniFB01,uniFB02" 	k="63" />
<hkern g1="backslash" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="86" />
<hkern g1="backslash" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="90" />
<hkern g1="backslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="51" />
<hkern g1="backslash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="10" />
<hkern g1="backslash" 	g2="t,tcaron,tbar,uni021B" 	k="82" />
<hkern g1="backslash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="47" />
<hkern g1="backslash" 	g2="d,q,dcaron,dcroat" 	k="39" />
<hkern g1="backslash" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="39" />
<hkern g1="backslash" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="41" />
<hkern g1="backslash" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="14" />
<hkern g1="braceleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="33" />
<hkern g1="braceleft" 	g2="AE,AEacute" 	k="27" />
<hkern g1="braceleft" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="37" />
<hkern g1="braceleft" 	g2="f,uniFB01,uniFB02" 	k="49" />
<hkern g1="braceleft" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="68" />
<hkern g1="braceleft" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="57" />
<hkern g1="braceleft" 	g2="z,zacute,zdotaccent,zcaron" 	k="31" />
<hkern g1="braceleft" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron" 	k="12" />
<hkern g1="braceleft" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="12" />
<hkern g1="braceleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="57" />
<hkern g1="braceleft" 	g2="t,tcaron,tbar,uni021B" 	k="47" />
<hkern g1="braceleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="51" />
<hkern g1="braceleft" 	g2="d,q,dcaron,dcroat" 	k="78" />
<hkern g1="braceleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="80" />
<hkern g1="braceleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="68" />
<hkern g1="braceleft" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="63" />
<hkern g1="braceleft" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="47" />
<hkern g1="bracketleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="35" />
<hkern g1="bracketleft" 	g2="AE,AEacute" 	k="31" />
<hkern g1="bracketleft" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="35" />
<hkern g1="bracketleft" 	g2="f,uniFB01,uniFB02" 	k="47" />
<hkern g1="bracketleft" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="68" />
<hkern g1="bracketleft" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="57" />
<hkern g1="bracketleft" 	g2="z,zacute,zdotaccent,zcaron" 	k="33" />
<hkern g1="bracketleft" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron" 	k="12" />
<hkern g1="bracketleft" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="12" />
<hkern g1="bracketleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="55" />
<hkern g1="bracketleft" 	g2="t,tcaron,tbar,uni021B" 	k="45" />
<hkern g1="bracketleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="49" />
<hkern g1="bracketleft" 	g2="d,q,dcaron,dcroat" 	k="70" />
<hkern g1="bracketleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="72" />
<hkern g1="bracketleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="61" />
<hkern g1="bracketleft" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="57" />
<hkern g1="bracketleft" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="37" />
<hkern g1="exclamdown" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="84" />
<hkern g1="exclamdown" 	g2="T,Tcaron,Tbar,uni021A" 	k="74" />
<hkern g1="parenleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="39" />
<hkern g1="parenleft" 	g2="AE,AEacute" 	k="33" />
<hkern g1="parenleft" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="43" />
<hkern g1="parenleft" 	g2="f,uniFB01,uniFB02" 	k="55" />
<hkern g1="parenleft" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="82" />
<hkern g1="parenleft" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="66" />
<hkern g1="parenleft" 	g2="z,zacute,zdotaccent,zcaron" 	k="41" />
<hkern g1="parenleft" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,uni0136,Lacute,uni013B,Lcaron,Ldot,Lslash,Nacute,uni0145,Ncaron,Eng,Racute,uni0156,Rcaron" 	k="12" />
<hkern g1="parenleft" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="14" />
<hkern g1="parenleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="74" />
<hkern g1="parenleft" 	g2="t,tcaron,tbar,uni021B" 	k="51" />
<hkern g1="parenleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="59" />
<hkern g1="parenleft" 	g2="d,q,dcaron,dcroat" 	k="88" />
<hkern g1="parenleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="92" />
<hkern g1="parenleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="80" />
<hkern g1="parenleft" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="76" />
<hkern g1="parenleft" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="57" />
<hkern g1="periodcentered" 	g2="l,lacute,uni013C,lcaron,ldot,lslash" 	k="84" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="168" />
<hkern g1="questiondown" 	g2="T,Tcaron,Tbar,uni021A" 	k="141" />
<hkern g1="questiondown" 	g2="J,Jcircumflex" 	k="16" />
<hkern g1="questiondown" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="16" />
<hkern g1="questiondown" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="76" />
<hkern g1="questiondown" 	g2="f,uniFB01,uniFB02" 	k="45" />
<hkern g1="questiondown" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="94" />
<hkern g1="questiondown" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="106" />
<hkern g1="questiondown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="66" />
<hkern g1="questiondown" 	g2="t,tcaron,tbar,uni021B" 	k="76" />
<hkern g1="questiondown" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="63" />
<hkern g1="questiondown" 	g2="d,q,dcaron,dcroat" 	k="31" />
<hkern g1="questiondown" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="35" />
<hkern g1="questiondown" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="33" />
<hkern g1="slash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="143" />
<hkern g1="slash" 	g2="AE,AEacute" 	k="152" />
<hkern g1="slash" 	g2="J,Jcircumflex" 	k="10" />
<hkern g1="slash" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218" 	k="35" />
<hkern g1="slash" 	g2="f,uniFB01,uniFB02" 	k="39" />
<hkern g1="slash" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="61" />
<hkern g1="slash" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="66" />
<hkern g1="slash" 	g2="z,zacute,zdotaccent,zcaron" 	k="70" />
<hkern g1="slash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aeacute" 	k="111" />
<hkern g1="slash" 	g2="t,tcaron,tbar,uni021B" 	k="23" />
<hkern g1="slash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,uni0122,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="47" />
<hkern g1="slash" 	g2="d,q,dcaron,dcroat" 	k="109" />
<hkern g1="slash" 	g2="g,gcircumflex,gbreve,gdotaccent,uni0123" 	k="106" />
<hkern g1="slash" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="109" />
<hkern g1="slash" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="74" />
<hkern g1="slash" 	g2="s,sacute,scircumflex,scedilla,scaron,uni0219" 	k="98" />
<hkern g1="slash" 	g2="m,n,p,r,ntilde,kgreenlandic,nacute,uni0146,ncaron,napostrophe,eng,racute,uni0157,rcaron" 	k="80" />
<hkern g1="ampersand" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="-10" />
<hkern g1="ampersand" 	g2="AE,AEacute" 	k="-6" />
<hkern g1="ampersand" 	g2="T,Tcaron,Tbar,uni021A" 	k="98" />
<hkern g1="ampersand" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="113" />
<hkern g1="ampersand" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="29" />
<hkern g1="ampersand" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="16" />
<hkern g1="at" 	g2="T,Tcaron,Tbar,uni021A" 	k="45" />
<hkern g1="at" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="78" />
<hkern g1="at" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="12" />
<hkern g1="copyright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="33" />
<hkern g1="copyright" 	g2="AE,AEacute" 	k="39" />
<hkern g1="copyright" 	g2="T,Tcaron,Tbar,uni021A" 	k="39" />
<hkern g1="copyright" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="74" />
<hkern g1="copyright" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="23" />
<hkern g1="registered" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="33" />
<hkern g1="registered" 	g2="AE,AEacute" 	k="39" />
<hkern g1="registered" 	g2="T,Tcaron,Tbar,uni021A" 	k="39" />
<hkern g1="registered" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="74" />
<hkern g1="registered" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="23" />
<hkern g1="eight" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="47" />
<hkern g1="five" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="12" />
<hkern g1="four" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="74" />
<hkern g1="four" 	g2="T,Tcaron,Tbar,uni021A" 	k="49" />
<hkern g1="four" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="12" />
<hkern g1="nine" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="33" />
<hkern g1="nine" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="74" />
<hkern g1="nine" 	g2="J,Jcircumflex" 	k="18" />
<hkern g1="nine" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="14" />
<hkern g1="seven" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="102" />
<hkern g1="six" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="98" />
<hkern g1="six" 	g2="T,Tcaron,Tbar,uni021A" 	k="82" />
<hkern g1="six" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="14" />
<hkern g1="two" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="37" />
<hkern g1="zero" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="47" />
<hkern g1="zero" 	g2="T,Tcaron,Tbar,uni021A" 	k="12" />
<hkern g1="zero" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="8" />
<hkern g1="space" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek" 	k="53" />
<hkern g1="space" 	g2="AE,AEacute" 	k="57" />
<hkern g1="space" 	g2="T,Tcaron,Tbar,uni021A" 	k="49" />
<hkern g1="space" 	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave" 	k="59" />
<hkern g1="space" 	g2="f,uniFB01,uniFB02" 	k="37" />
<hkern g1="space" 	g2="t,tcaron,tbar,uni021B" 	k="41" />
<hkern g1="space" 	g2="w,wcircumflex,wgrave,wacute,wdieresis" 	k="45" />
<hkern g1="space" 	g2="y,yacute,ydieresis,ycircumflex,ygrave" 	k="53" />
<hkern g1="space" 	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis" 	k="25" />
<hkern g1="uni0403,uni0413" 	g2="uni0409,uni041B" 	k="66" />
<hkern g1="uni0403,uni0413" 	g2="uni0404,uni041E,uni0421" 	k="23" />
<hkern g1="uni0403,uni0413" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="135" />
<hkern g1="uni0403,uni0413" 	g2="uni043B,uni0459" 	k="221" />
<hkern g1="uni0403,uni0413" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="238" />
<hkern g1="uni0403,uni0413" 	g2="uni0456,uni0457" 	k="6" />
<hkern g1="uni0403,uni0413" 	g2="uni0452,uni045B" 	k="-18" />
<hkern g1="uni0403,uni0413" 	g2="guillemotleft,guilsinglleft" 	k="123" />
<hkern g1="uni0403,uni0413" 	g2="hyphen,endash,emdash" 	k="150" />
<hkern g1="uni0403,uni0413" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="193" />
<hkern g1="uni0403,uni0413" 	g2="asterisk" 	k="-10" />
<hkern g1="uni0403,uni0413" 	g2="slash" 	k="164" />
<hkern g1="uni0403,uni0413" 	g2="uni0443,uni045E" 	k="119" />
<hkern g1="uni0403,uni0413" 	g2="colon,semicolon" 	k="78" />
<hkern g1="uni0403,uni0413" 	g2="guillemotright,guilsinglright" 	k="39" />
<hkern g1="uni0400,uni0401,uni0415" 	g2="uni0404,uni041E,uni0421" 	k="12" />
<hkern g1="uni0400,uni0401,uni0415" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="8" />
<hkern g1="uni0400,uni0401,uni0415" 	g2="guillemotleft,guilsinglleft" 	k="12" />
<hkern g1="uni0400,uni0401,uni0415" 	g2="hyphen,endash,emdash" 	k="23" />
<hkern g1="uni0400,uni0401,uni0415" 	g2="uni0443,uni045E" 	k="29" />
<hkern g1="uni040C,uni041A" 	g2="uni0404,uni041E,uni0421" 	k="74" />
<hkern g1="uni040C,uni041A" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="59" />
<hkern g1="uni040C,uni041A" 	g2="guillemotleft,guilsinglleft" 	k="102" />
<hkern g1="uni040C,uni041A" 	g2="hyphen,endash,emdash" 	k="104" />
<hkern g1="uni040C,uni041A" 	g2="uni0443,uni045E" 	k="104" />
<hkern g1="uni040C,uni041A" 	g2="quoteleft,quotedblleft" 	k="16" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="uni0409,uni041B" 	k="18" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="uni043B,uni0459" 	k="27" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="25" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="slash" 	k="45" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="uni040E,uni0423" 	k="20" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="backslash" 	k="47" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="braceright" 	k="51" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="bracketright" 	k="49" />
<hkern g1="uni041E,uni042D,uni042E" 	g2="parenright" 	k="59" />
<hkern g1="uni040E,uni0423" 	g2="uni0409,uni041B" 	k="72" />
<hkern g1="uni040E,uni0423" 	g2="uni0404,uni041E,uni0421" 	k="33" />
<hkern g1="uni040E,uni0423" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="84" />
<hkern g1="uni040E,uni0423" 	g2="uni043B,uni0459" 	k="170" />
<hkern g1="uni040E,uni0423" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="147" />
<hkern g1="uni040E,uni0423" 	g2="uni0452,uni045B" 	k="-25" />
<hkern g1="uni040E,uni0423" 	g2="guillemotleft,guilsinglleft" 	k="78" />
<hkern g1="uni040E,uni0423" 	g2="hyphen,endash,emdash" 	k="94" />
<hkern g1="uni040E,uni0423" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="156" />
<hkern g1="uni040E,uni0423" 	g2="slash" 	k="158" />
<hkern g1="uni040E,uni0423" 	g2="uni0443,uni045E" 	k="35" />
<hkern g1="uni040E,uni0423" 	g2="colon,semicolon" 	k="43" />
<hkern g1="uni040E,uni0423" 	g2="guillemotright,guilsinglright" 	k="10" />
<hkern g1="uni040E,uni0423" 	g2="braceright" 	k="-4" />
<hkern g1="uni040E,uni0423" 	g2="parenright" 	k="-4" />
<hkern g1="uni0426,uni0429" 	g2="uni0404,uni041E,uni0421" 	k="4" />
<hkern g1="uni0426,uni0429" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="6" />
<hkern g1="uni0426,uni0429" 	g2="uni0452,uni045B" 	k="4" />
<hkern g1="uni0426,uni0429" 	g2="guillemotleft,guilsinglleft" 	k="8" />
<hkern g1="uni0426,uni0429" 	g2="hyphen,endash,emdash" 	k="16" />
<hkern g1="uni0426,uni0429" 	g2="asterisk" 	k="27" />
<hkern g1="uni0426,uni0429" 	g2="uni0443,uni045E" 	k="41" />
<hkern g1="uni0426,uni0429" 	g2="quoteleft,quotedblleft" 	k="27" />
<hkern g1="uni0426,uni0429" 	g2="backslash" 	k="31" />
<hkern g1="uni0426,uni0429" 	g2="quoteright,quotedblright" 	k="27" />
<hkern g1="uni0426,uni0429" 	g2="quotedbl,quotesingle" 	k="27" />
<hkern g1="uni0426,uni0429" 	g2="question" 	k="18" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="uni0452,uni045B" 	k="10" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="asterisk" 	k="92" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="slash" 	k="16" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="uni0443,uni045E" 	k="43" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="quoteleft,quotedblleft" 	k="106" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="uni040E,uni0423" 	k="33" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="backslash" 	k="117" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="braceright" 	k="72" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="bracketright" 	k="63" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="parenright" 	k="84" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="quoteright,quotedblright" 	k="113" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="quotedbl,quotesingle" 	k="117" />
<hkern g1="uni0409,uni040A,uni042A,uni042C" 	g2="question" 	k="61" />
<hkern g1="uni0406,uni0407,uni040D,uni040F,uni0418,uni0419,uni041B,uni041C,uni041D,uni041F,uni0427,uni0428,uni042B,uni042F" 	g2="hyphen,endash,emdash" 	k="10" />
<hkern g1="uni0406,uni0407,uni040D,uni040F,uni0418,uni0419,uni041B,uni041C,uni041D,uni041F,uni0427,uni0428,uni042B,uni042F" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="10" />
<hkern g1="uni0406,uni0407,uni040D,uni040F,uni0418,uni0419,uni041B,uni041C,uni041D,uni041F,uni0427,uni0428,uni042B,uni042F" 	g2="braceright" 	k="12" />
<hkern g1="uni0406,uni0407,uni040D,uni040F,uni0418,uni0419,uni041B,uni041C,uni041D,uni041F,uni0427,uni0428,uni042B,uni042F" 	g2="bracketright" 	k="12" />
<hkern g1="uni0406,uni0407,uni040D,uni040F,uni0418,uni0419,uni041B,uni041C,uni041D,uni041F,uni0427,uni0428,uni042B,uni042F" 	g2="parenright" 	k="12" />
<hkern g1="uni0433,uni0453" 	g2="uni043B,uni0459" 	k="59" />
<hkern g1="uni0433,uni0453" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="18" />
<hkern g1="uni0433,uni0453" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="156" />
<hkern g1="uni0433,uni0453" 	g2="backslash" 	k="29" />
<hkern g1="uni0433,uni0453" 	g2="braceright" 	k="70" />
<hkern g1="uni0433,uni0453" 	g2="bracketright" 	k="70" />
<hkern g1="uni0433,uni0453" 	g2="parenright" 	k="84" />
<hkern g1="uni0433,uni0453" 	g2="slash" 	k="121" />
<hkern g1="uni0433,uni0453" 	g2="guillemotleft,guilsinglleft" 	k="12" />
<hkern g1="uni0433,uni0453" 	g2="hyphen,endash,emdash" 	k="68" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="uni043B,uni0459" 	k="10" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="backslash" 	k="104" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="braceright" 	k="76" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="bracketright" 	k="68" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="parenright" 	k="86" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="slash" 	k="20" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="uni0443,uni045E" 	k="45" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="uni0452,uni045B" 	k="12" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="quoteright,quotedblright" 	k="53" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="quotedbl,quotesingle" 	k="53" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="asterisk" 	k="43" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="question" 	k="39" />
<hkern g1="uni0435,uni0450,uni0451" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="backslash" 	k="80" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="braceright" 	k="47" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="bracketright" 	k="37" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="parenright" 	k="57" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="uni0452,uni045B" 	k="4" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="quoteright,quotedblright" 	k="12" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="quotedbl,quotesingle" 	k="10" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="asterisk" 	k="12" />
<hkern g1="uni0438,uni0439,uni043B,uni043C,uni043D,uni043F,uni0447,uni0448,uni044B,uni044F,uni045D,uni045F" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="uni043A,uni045C" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="47" />
<hkern g1="uni043A,uni045C" 	g2="backslash" 	k="68" />
<hkern g1="uni043A,uni045C" 	g2="braceright" 	k="29" />
<hkern g1="uni043A,uni045C" 	g2="bracketright" 	k="31" />
<hkern g1="uni043A,uni045C" 	g2="parenright" 	k="37" />
<hkern g1="uni043A,uni045C" 	g2="guillemotleft,guilsinglleft" 	k="39" />
<hkern g1="uni043A,uni045C" 	g2="hyphen,endash,emdash" 	k="72" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="uni043B,uni0459" 	k="18" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="14" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="backslash" 	k="109" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="braceright" 	k="80" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="bracketright" 	k="72" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="parenright" 	k="92" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="slash" 	k="39" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="uni0443,uni045E" 	k="45" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="uni0452,uni045B" 	k="12" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="quoteright,quotedblright" 	k="53" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="quotedbl,quotesingle" 	k="55" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="asterisk" 	k="45" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="question" 	k="41" />
<hkern g1="uni043E,uni044D,uni044E" 	g2="quoteleft,quotedblleft" 	k="45" />
<hkern g1="uni0440,uni0444" 	g2="uni043B,uni0459" 	k="16" />
<hkern g1="uni0440,uni0444" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="14" />
<hkern g1="uni0440,uni0444" 	g2="backslash" 	k="106" />
<hkern g1="uni0440,uni0444" 	g2="braceright" 	k="78" />
<hkern g1="uni0440,uni0444" 	g2="bracketright" 	k="70" />
<hkern g1="uni0440,uni0444" 	g2="parenright" 	k="90" />
<hkern g1="uni0440,uni0444" 	g2="slash" 	k="39" />
<hkern g1="uni0440,uni0444" 	g2="uni0443,uni045E" 	k="43" />
<hkern g1="uni0440,uni0444" 	g2="uni0452,uni045B" 	k="10" />
<hkern g1="uni0440,uni0444" 	g2="quoteright,quotedblright" 	k="45" />
<hkern g1="uni0440,uni0444" 	g2="quotedbl,quotesingle" 	k="51" />
<hkern g1="uni0440,uni0444" 	g2="asterisk" 	k="41" />
<hkern g1="uni0440,uni0444" 	g2="question" 	k="37" />
<hkern g1="uni0440,uni0444" 	g2="quoteleft,quotedblleft" 	k="43" />
<hkern g1="uni0443,uni045E" 	g2="uni043B,uni0459" 	k="68" />
<hkern g1="uni0443,uni045E" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="39" />
<hkern g1="uni0443,uni045E" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="109" />
<hkern g1="uni0443,uni045E" 	g2="backslash" 	k="61" />
<hkern g1="uni0443,uni045E" 	g2="braceright" 	k="74" />
<hkern g1="uni0443,uni045E" 	g2="bracketright" 	k="74" />
<hkern g1="uni0443,uni045E" 	g2="parenright" 	k="88" />
<hkern g1="uni0443,uni045E" 	g2="slash" 	k="109" />
<hkern g1="uni0443,uni045E" 	g2="guillemotleft,guilsinglleft" 	k="18" />
<hkern g1="uni0443,uni045E" 	g2="hyphen,endash,emdash" 	k="43" />
<hkern g1="uni0443,uni045E" 	g2="question" 	k="-6" />
<hkern g1="uni0446,uni0449" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="4" />
<hkern g1="uni0446,uni0449" 	g2="backslash" 	k="104" />
<hkern g1="uni0446,uni0449" 	g2="guillemotleft,guilsinglleft" 	k="14" />
<hkern g1="uni0446,uni0449" 	g2="hyphen,endash,emdash" 	k="25" />
<hkern g1="uni0446,uni0449" 	g2="uni0443,uni045E" 	k="37" />
<hkern g1="uni0446,uni0449" 	g2="uni0452,uni045B" 	k="10" />
<hkern g1="uni0446,uni0449" 	g2="quoteright,quotedblright" 	k="37" />
<hkern g1="uni0446,uni0449" 	g2="quotedbl,quotesingle" 	k="41" />
<hkern g1="uni0446,uni0449" 	g2="asterisk" 	k="43" />
<hkern g1="uni0446,uni0449" 	g2="question" 	k="27" />
<hkern g1="uni0446,uni0449" 	g2="quoteleft,quotedblleft" 	k="35" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="backslash" 	k="133" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="braceright" 	k="76" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="bracketright" 	k="68" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="parenright" 	k="88" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="slash" 	k="18" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="uni0443,uni045E" 	k="70" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="uni0452,uni045B" 	k="14" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="quoteright,quotedblright" 	k="133" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="quotedbl,quotesingle" 	k="141" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="asterisk" 	k="119" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="question" 	k="88" />
<hkern g1="uni044A,uni044C,uni0459,uni045A" 	g2="quoteleft,quotedblleft" 	k="129" />
<hkern g1="guillemotright,guilsinglright" 	g2="uni0409,uni041B" 	k="51" />
<hkern g1="guillemotright,guilsinglright" 	g2="uni040E,uni0423" 	k="70" />
<hkern g1="guillemotright,guilsinglright" 	g2="uni043B,uni0459" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="uni0443,uni045E" 	k="18" />
<hkern g1="hyphen,endash,emdash" 	g2="uni0409,uni041B" 	k="49" />
<hkern g1="hyphen,endash,emdash" 	g2="uni040E,uni0423" 	k="78" />
<hkern g1="hyphen,endash,emdash" 	g2="uni043B,uni0459" 	k="43" />
<hkern g1="hyphen,endash,emdash" 	g2="uni0443,uni045E" 	k="47" />
<hkern g1="hyphen,endash,emdash" 	g2="uni0452,uni045B" 	k="10" />
<hkern g1="hyphen,endash,emdash" 	g2="uni0400,uni0401,uni0403,uni0406,uni0407,uni040A,uni040C,uni040D,uni040F,uni0411,uni0412,uni0413,uni0415,uni0418,uni0419,uni041A,uni041C,uni041D,uni041F,uni0420,uni0426,uni0428,uni0429,uni042B,uni042C,uni042E" 	k="10" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="uni040E,uni0423" 	k="10" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="uni0443,uni045E" 	k="113" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="uni0452,uni045B" 	k="8" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="uni0400,uni0401,uni0403,uni0406,uni0407,uni040A,uni040C,uni040D,uni040F,uni0411,uni0412,uni0413,uni0415,uni0418,uni0419,uni041A,uni041C,uni041D,uni041F,uni0420,uni0426,uni0428,uni0429,uni042B,uni042C,uni042E" 	k="10" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="uni0404,uni041E,uni0421" 	k="25" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="14" />
<hkern g1="quoteleft,quotedblleft" 	g2="uni0409,uni041B" 	k="66" />
<hkern g1="quoteleft,quotedblleft" 	g2="uni043B,uni0459" 	k="68" />
<hkern g1="quoteleft,quotedblleft" 	g2="uni0452,uni045B" 	k="-4" />
<hkern g1="quoteleft,quotedblleft" 	g2="uni0404,uni041E,uni0421" 	k="14" />
<hkern g1="quoteleft,quotedblleft" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="70" />
<hkern g1="quoteleft,quotedblleft" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="16" />
<hkern g1="quoteright,quotedblright" 	g2="uni0409,uni041B" 	k="63" />
<hkern g1="quoteright,quotedblright" 	g2="uni040E,uni0423" 	k="-10" />
<hkern g1="quoteright,quotedblright" 	g2="uni043B,uni0459" 	k="80" />
<hkern g1="quoteright,quotedblright" 	g2="uni0443,uni045E" 	k="6" />
<hkern g1="quoteright,quotedblright" 	g2="uni0452,uni045B" 	k="-14" />
<hkern g1="quoteright,quotedblright" 	g2="uni0404,uni041E,uni0421" 	k="31" />
<hkern g1="quoteright,quotedblright" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="88" />
<hkern g1="quoteright,quotedblright" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="33" />
<hkern g1="quotedbl,quotesingle" 	g2="uni0409,uni041B" 	k="63" />
<hkern g1="quotedbl,quotesingle" 	g2="uni043B,uni0459" 	k="59" />
<hkern g1="quotedbl,quotesingle" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="55" />
<hkern g1="quotedbl,quotesingle" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="10" />
<hkern g1="asterisk" 	g2="uni0409,uni041B" 	k="61" />
<hkern g1="asterisk" 	g2="uni043B,uni0459" 	k="59" />
<hkern g1="asterisk" 	g2="uni0452,uni045B" 	k="-29" />
<hkern g1="asterisk" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="45" />
<hkern g1="asterisk" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="12" />
<hkern g1="backslash" 	g2="uni0443,uni045E" 	k="57" />
<hkern g1="backslash" 	g2="uni0452,uni045B" 	k="20" />
<hkern g1="backslash" 	g2="uni0404,uni041E,uni0421" 	k="47" />
<hkern g1="backslash" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="39" />
<hkern g1="braceleft" 	g2="uni0409,uni041B" 	k="18" />
<hkern g1="braceleft" 	g2="uni043B,uni0459" 	k="31" />
<hkern g1="braceleft" 	g2="uni0443,uni045E" 	k="10" />
<hkern g1="braceleft" 	g2="uni0452,uni045B" 	k="-10" />
<hkern g1="braceleft" 	g2="uni0400,uni0401,uni0403,uni0406,uni0407,uni040A,uni040C,uni040D,uni040F,uni0411,uni0412,uni0413,uni0415,uni0418,uni0419,uni041A,uni041C,uni041D,uni041F,uni0420,uni0426,uni0428,uni0429,uni042B,uni042C,uni042E" 	k="12" />
<hkern g1="braceleft" 	g2="uni0404,uni041E,uni0421" 	k="51" />
<hkern g1="braceleft" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="80" />
<hkern g1="braceleft" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="47" />
<hkern g1="bracketleft" 	g2="uni0409,uni041B" 	k="23" />
<hkern g1="bracketleft" 	g2="uni043B,uni0459" 	k="33" />
<hkern g1="bracketleft" 	g2="uni0443,uni045E" 	k="10" />
<hkern g1="bracketleft" 	g2="uni0452,uni045B" 	k="-8" />
<hkern g1="bracketleft" 	g2="uni0400,uni0401,uni0403,uni0406,uni0407,uni040A,uni040C,uni040D,uni040F,uni0411,uni0412,uni0413,uni0415,uni0418,uni0419,uni041A,uni041C,uni041D,uni041F,uni0420,uni0426,uni0428,uni0429,uni042B,uni042C,uni042E" 	k="12" />
<hkern g1="bracketleft" 	g2="uni0404,uni041E,uni0421" 	k="49" />
<hkern g1="bracketleft" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="72" />
<hkern g1="bracketleft" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="37" />
<hkern g1="parenleft" 	g2="uni0409,uni041B" 	k="23" />
<hkern g1="parenleft" 	g2="uni043B,uni0459" 	k="37" />
<hkern g1="parenleft" 	g2="uni0443,uni045E" 	k="10" />
<hkern g1="parenleft" 	g2="uni0452,uni045B" 	k="-10" />
<hkern g1="parenleft" 	g2="uni0400,uni0401,uni0403,uni0406,uni0407,uni040A,uni040C,uni040D,uni040F,uni0411,uni0412,uni0413,uni0415,uni0418,uni0419,uni041A,uni041C,uni041D,uni041F,uni0420,uni0426,uni0428,uni0429,uni042B,uni042C,uni042E" 	k="14" />
<hkern g1="parenleft" 	g2="uni0404,uni041E,uni0421" 	k="59" />
<hkern g1="parenleft" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="92" />
<hkern g1="parenleft" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="57" />
<hkern g1="slash" 	g2="uni0409,uni041B" 	k="66" />
<hkern g1="slash" 	g2="uni043B,uni0459" 	k="125" />
<hkern g1="slash" 	g2="uni0443,uni045E" 	k="63" />
<hkern g1="slash" 	g2="uni0404,uni041E,uni0421" 	k="47" />
<hkern g1="slash" 	g2="uni0435,uni043E,uni0441,uni0450,uni0451,uni0454" 	k="109" />
<hkern g1="slash" 	g2="uni0432,uni0433,uni0438,uni0439,uni043A,uni043C,uni043D,uni043F,uni0440,uni0446,uni0448,uni0449,uni044B,uni044C,uni044E,uni0453,uni045A,uni045C,uni045D,uni045F" 	k="80" />
</font>
</defs></svg> 