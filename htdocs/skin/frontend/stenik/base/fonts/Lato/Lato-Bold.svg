<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg>
<metadata>
Created by FontForge 20090914 at Fri Apr 21 08:46:22 2017
 By www-data
Copyright (c) 2010-2011 by tyPoland Lukasz Dziedzic with Reserved Font Name "Lato". Licensed under the SIL Open Font License, Version 1.1.
</metadata>
<defs>
<font id="Lato-Bold" horiz-adv-x="1160" >
  <font-face 
    font-family="Lato"
    font-weight="700"
    font-stretch="normal"
    units-per-em="2000"
    panose-1="2 15 8 2 2 2 4 3 2 3"
    ascent="1610"
    descent="-390"
    x-height="1026"
    cap-height="1446"
    bbox="-177 -360.998 2286 1874"
    underline-thickness="160"
    underline-position="-40"
    unicode-range="U+000D-U+FB02"
  />
<missing-glyph horiz-adv-x="1094" 
d="M239 1147c38 30.667 81.167 56.499 129.5 77.499s105.5 31.5 171.5 31.5c46.667 0 88.667 -6.33301 126 -19s69 -30.5 95 -53.5s45.833 -50.667 59.5 -83s20.5 -68.166 20.5 -107.499c0 -36.667 -4.66699 -68.167 -14 -94.5s-21 -49.333 -35 -69s-29.333 -36.5 -46 -50.5
l-48 -39.5c-15.333 -12.333 -28.833 -24.5 -40.5 -36.5s-19.167 -25.333 -22.5 -40l-22 -96h-169l-17 114c-4 24.667 -2.16699 45.834 5.5 63.501s18.334 33.667 32.001 48s29 27.666 46 39.999s33 25.333 48 39s27.667 29 38 46s15.5 37.167 15.5 60.5s-8 42.333 -24 57
s-39.333 22 -70 22c-24 0 -44.167 -2.33301 -60.5 -7s-30.5 -9.66699 -42.5 -15s-22.5 -10.333 -31.5 -15s-18.5 -7 -28.5 -7c-22.667 0 -39.334 9.66699 -50.001 29zM385 312.999c0 18.667 3.5 36.334 10.5 53.001s16.5 31.167 28.5 43.5s26.333 22 43 29
s34.667 10.5 54 10.5c18.667 0 36.167 -3.5 52.5 -10.5s30.666 -16.667 42.999 -29s22 -26.833 29 -43.5s10.5 -34.334 10.5 -53.001c0 -19.333 -3.5 -37.166 -10.5 -53.499s-16.667 -30.5 -29 -42.5s-26.666 -21.5 -42.999 -28.5s-33.833 -10.5 -52.5 -10.5
c-19.333 0 -37.333 3.5 -54 10.5s-31 16.5 -43 28.5s-21.5 26.167 -28.5 42.5s-10.5 34.166 -10.5 53.499zM42 1446h1010v-1446h-1010v1446zM107 68.999h873v1309h-873v-1309z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="1202" 
d="M176 0l0.000976562 840l-89 14c-19.333 3.33301 -34.833 10 -46.5 20s-17.5 24 -17.5 42v101h153v33c0 62.667 10.333 120.834 31 174.501s52 100.167 94 139.5s95 70.166 159 92.499s139.667 33.5 227 33.5c28 0 56.667 -1.5 86 -4.5s54.666 -7.83301 75.999 -14.5
l-8 -128c-1.33301 -15.333 -9.66602 -24.5 -24.999 -27.5s-34.333 -4.5 -57 -4.5c-64.667 0 -118.834 -5.66699 -162.501 -17s-79 -28.166 -106 -50.499s-46.333 -50.333 -58 -84s-17.5 -72.834 -17.5 -117.501v-25h636v-1017h-247v841h-381v-841h-247z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="1234" 
d="M176 0v840.001l-89 14c-19.333 3.33301 -34.833 10 -46.5 20s-17.5 24 -17.5 42v101h153v43c0 54.667 8.5 107 25.5 157s43.167 94.167 78.5 132.5s80 69 134 92s118 34.5 192 34.5c60.667 0 118.167 -2 172.5 -6s108.5 -6 162.5 -6h139v-1464h-246v1292
c-35.333 1.33301 -69.833 3 -103.5 5s-62.167 3 -85.5 3c-74.667 0 -131.667 -20.833 -171 -62.5s-59 -100.834 -59 -177.501v-43h243v-176h-235v-841h-247z" />
    <glyph glyph-name=".notdef" horiz-adv-x="1094" 
d="M239 1147c38 30.667 81.167 56.499 129.5 77.499s105.5 31.5 171.5 31.5c46.667 0 88.667 -6.33301 126 -19s69 -30.5 95 -53.5s45.833 -50.667 59.5 -83s20.5 -68.166 20.5 -107.499c0 -36.667 -4.66699 -68.167 -14 -94.5s-21 -49.333 -35 -69s-29.333 -36.5 -46 -50.5
l-48 -39.5c-15.333 -12.333 -28.833 -24.5 -40.5 -36.5s-19.167 -25.333 -22.5 -40l-22 -96h-169l-17 114c-4 24.667 -2.16699 45.834 5.5 63.501s18.334 33.667 32.001 48s29 27.666 46 39.999s33 25.333 48 39s27.667 29 38 46s15.5 37.167 15.5 60.5s-8 42.333 -24 57
s-39.333 22 -70 22c-24 0 -44.167 -2.33301 -60.5 -7s-30.5 -9.66699 -42.5 -15s-22.5 -10.333 -31.5 -15s-18.5 -7 -28.5 -7c-22.667 0 -39.334 9.66699 -50.001 29zM385 312.999c0 18.667 3.5 36.334 10.5 53.001s16.5 31.167 28.5 43.5s26.333 22 43 29
s34.667 10.5 54 10.5c18.667 0 36.167 -3.5 52.5 -10.5s30.666 -16.667 42.999 -29s22 -26.833 29 -43.5s10.5 -34.334 10.5 -53.001c0 -19.333 -3.5 -37.166 -10.5 -53.499s-16.667 -30.5 -29 -42.5s-26.666 -21.5 -42.999 -28.5s-33.833 -10.5 -52.5 -10.5
c-19.333 0 -37.333 3.5 -54 10.5s-31 16.5 -43 28.5s-21.5 26.167 -28.5 42.5s-10.5 34.166 -10.5 53.499zM42 1446h1010v-1446h-1010v1446zM107 68.999h873v1309h-873v-1309z" />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" unicode="&#xd;" horiz-adv-x="0" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="386" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="721" 
d="M480 1446v-572c0 -60.667 -3 -120.167 -9 -178.5s-14 -120.166 -24 -185.499h-167c-10 65.333 -18 127.166 -24 185.499s-9 117.833 -9 178.5v572h233zM209 136c0 20.667 3.83301 40.334 11.5 59.001s18.334 34.667 32.001 48s29.834 24 48.501 32s38.667 12 60 12
c20.667 0 40.334 -4 59.001 -12s34.667 -18.667 48 -32s24 -29.333 32 -48s12 -38.334 12 -59.001c0 -21.333 -4 -41.166 -12 -59.499s-18.667 -34.166 -32 -47.499s-29.333 -23.833 -48 -31.5s-38.334 -11.5 -59.001 -11.5c-21.333 0 -41.333 3.83301 -60 11.5
s-34.834 18.167 -48.501 31.5s-24.334 29.166 -32.001 47.499s-11.5 38.166 -11.5 59.499z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="844" 
d="M346 1446v-288l-23 -156c-4.66699 -29.333 -13.167 -51.833 -25.5 -67.5s-32.5 -23.5 -60.5 -23.5c-24 0 -43 7.83301 -57 23.5s-23 38.167 -27 67.5l-22 156v288h215zM714 1446v-288l-23 -156c-4.66699 -29.333 -13.167 -51.833 -25.5 -67.5s-32.5 -23.5 -60.5 -23.5
c-24 0 -43 7.83301 -57 23.5s-23 38.167 -27 67.5l-22 156v288h215z" />
    <glyph glyph-name="numbersign" unicode="#" 
d="M831 408l-77.001 -408.001h-113c-20 0 -37.333 8 -52 24s-22 35.667 -22 59c0 3.33301 0.166992 6.5 0.5 9.5s0.833008 6.16699 1.5 9.5l59 306h-205l-58 -318c-6 -32 -19.667 -55 -41 -69s-45 -21 -71 -21h-109l76 408h-115c-21.333 0 -37.666 5.16699 -48.999 15.5
s-17 27.5 -17 51.5c0 9.33301 1 20 3 32l13 79h190l52 276h-214l19 103c4.66699 25.333 15 44.166 31 56.499s42 18.5 78 18.5h111l62 322c5.33301 26.667 17.666 47.334 36.999 62.001s42 22 68 22h112l-76 -406h204l77 406h110c23.333 0 42.333 -6.66699 57 -20
s22 -30.333 22 -51c0 -6.66699 -0.333008 -11.667 -1 -15l-62 -320h203l-19 -103c-4.66699 -25.333 -15.167 -44.166 -31.5 -56.499s-42.166 -18.5 -77.499 -18.5h-100l-51 -276h142c21.333 0 37.5 -5.16699 48.5 -15.5s16.5 -27.833 16.5 -52.5c0 -9.33301 -1 -20 -3 -32
l-12 -78h-217zM447.999 585.999h205l51 276h-204z" />
    <glyph glyph-name="dollar" unicode="$" 
d="M463 -9c-78 10.667 -151.168 32.002 -219.501 64.002s-126.5 71.667 -174.5 119l76 113c6.66699 10 15.5 18.167 26.5 24.5s22.5 9.5 34.5 9.5c14.667 0 30.334 -5.16699 47.001 -15.5l57 -36.5c21.333 -14 45.666 -28 72.999 -42s59.666 -24.333 96.999 -31l38 448
c-47.333 13.333 -94.166 29 -140.499 47s-88 41.833 -125 71.5s-66.833 67.167 -89.5 112.5s-34 102 -34 170c0 50.667 10.167 100.167 30.5 148.5s49.833 91.5 88.5 129.5s86.334 69.333 143.001 94s121.667 38.667 195 42l11 126c1.33301 16 8 30.333 20 43
s27.667 19 47 19h91l-17 -197c70.667 -11.333 131.667 -31.333 183 -60s96 -61 134 -97l-60 -91c-9.33301 -13.333 -18.666 -23.333 -27.999 -30s-20.666 -10 -33.999 -10c-10 0 -21.833 3.16699 -35.5 9.5s-29.334 14 -47.001 23s-37.167 18.167 -58.5 27.5s-45 17 -71 23
l-35 -414c48 -14.667 95.5 -30.834 142.5 -48.501s89.333 -40.5 127 -68.5s68.167 -62.833 91.5 -104.5s35 -93.834 35 -156.501c0 -62 -10.333 -120.167 -31 -174.5s-50.834 -102.333 -90.501 -144s-88.5 -75.5 -146.5 -101.5s-124.333 -41.667 -199 -47l-12 -147
c-1.33301 -15.333 -8 -29.5 -20 -42.5s-27.667 -19.5 -47 -19.5h-91zM847.999 415.002c0 25.333 -4.5 47.499 -13.5 66.499s-21.667 35.833 -38 50.5s-35.5 27.334 -57.5 38.001s-46 20.334 -72 29.001l-34 -406c70.667 9.33301 124.167 33.166 160.5 71.499
s54.5 88.5 54.5 150.5zM361.999 1069c0 -25.333 4.33398 -47.667 13.001 -67s20.834 -36.5 36.501 -51.5s34.167 -28.333 55.5 -40s44.666 -22.167 69.999 -31.5l31 370c-36.667 -4.66699 -68 -12.5 -94 -23.5s-47.333 -24.5 -64 -40.5s-28.834 -33.833 -36.501 -53.5
s-11.5 -40.5 -11.5 -62.5z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="1606" 
d="M729 1096c0 -54.667 -9.16602 -104 -27.499 -148s-42.833 -81.5 -73.5 -112.5s-66.334 -54.833 -107.001 -71.5s-83 -25 -127 -25c-48 0 -92.333 8.33301 -133 25s-75.834 40.5 -105.501 71.5s-52.834 68.5 -69.501 112.5s-25 93.333 -25 148
c0 56 8.33301 106.667 25 152s39.834 83.666 69.501 114.999s64.834 55.5 105.501 72.5s85 25.5 133 25.5s92.5 -8.5 133.5 -25.5s76.5 -41.167 106.5 -72.5s53.333 -69.666 70 -114.999s25 -96 25 -152zM538.001 1096c0 38.667 -3.66699 71 -11 97s-17.5 47 -30.5 63
s-28.333 27.5 -46 34.5s-36.5 10.5 -56.5 10.5s-38.667 -3.5 -56 -10.5s-32.333 -18.5 -45 -34.5s-22.5 -37 -29.5 -63s-10.5 -58.333 -10.5 -97c0 -37.333 3.5 -68.5 10.5 -93.5s16.833 -45.167 29.5 -60.5s27.667 -26.333 45 -33s36 -10 56 -10s38.833 3.33301 56.5 10
s33 17.667 46 33s23.167 35.5 30.5 60.5s11 56.167 11 93.5zM1210 1407c8 10 17.999 19 29.999 27s28.667 12 50 12h179l-1074 -1409c-8 -10.667 -18.167 -19.5 -30.5 -26.5s-27.166 -10.5 -44.499 -10.5h-184zM1545 340c0 -54.667 -9.16602 -104 -27.499 -148
s-42.833 -81.5 -73.5 -112.5s-66.334 -55 -107.001 -72s-83 -25.5 -127 -25.5c-48 0 -92.333 8.5 -133 25.5s-75.834 41 -105.501 72s-52.834 68.5 -69.501 112.5s-25 93.333 -25 148c0 56 8.33301 106.667 25 152s39.834 83.666 69.501 114.999s64.834 55.5 105.501 72.5
s85 25.5 133 25.5s92.5 -8.5 133.5 -25.5s76.5 -41.167 106.5 -72.5s53.333 -69.666 70 -114.999s25 -96 25 -152zM1355 340c0 38.667 -3.83301 71 -11.5 97s-18 47 -31 63s-28.333 27.5 -46 34.5s-36.5 10.5 -56.5 10.5s-38.667 -3.5 -56 -10.5
s-32.166 -18.5 -44.499 -34.5s-22.166 -37 -29.499 -63s-11 -58.333 -11 -97c0 -37.333 3.66699 -68.666 11 -93.999s17.166 -45.666 29.499 -60.999s27.166 -26.333 44.499 -33s36 -10 56 -10s38.833 3.33301 56.5 10s33 17.667 46 33s23.333 35.666 31 60.999
s11.5 56.666 11.5 93.999z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="1428" 
d="M663 1462c58.667 0 111.167 -8.99609 157.5 -26.9961s86 -42 119 -72s59 -64.333 78 -103s30.167 -78.667 33.5 -120l-157 -33c-2 -0.666992 -4.16699 -1 -6.5 -1h-6.5c-11.333 0 -21.5 3.16699 -30.5 9.5s-16.167 16.166 -21.5 29.499
c-6 17.333 -13.667 33.833 -23 49.5s-20.666 29.334 -33.999 41.001s-29 21 -47 28s-38.667 10.5 -62 10.5c-28 0 -53 -4.83301 -75 -14.5s-40.667 -22.667 -56 -39s-27 -35.333 -35 -57s-12 -44.5 -12 -68.5c0 -20 2.16699 -39.333 6.5 -58s11.166 -37.334 20.499 -56.001
s21.5 -37.834 36.5 -57.501s33.5 -40.834 55.5 -63.501l384 -399c22 42 39.667 86.333 53 133s22 94 26 142c1.33301 15.333 6.33301 27.666 15 36.999s20.667 14 36 14h155c-0.666992 -88 -13.334 -172 -38.001 -252s-60 -153.667 -106 -221l303 -314h-242
c-12.667 0 -23.834 0.666992 -33.501 2s-18.834 3.83301 -27.501 7.5s-17 8.5 -25 14.5s-16.333 13.667 -25 23l-100 103c-64 -52 -135.167 -92.667 -213.5 -122s-163.166 -44 -254.499 -44c-56 0 -110.667 9.5 -164 28.5s-101 46.5 -143 82.5s-75.833 79.5 -101.5 130.5
s-38.5 108.5 -38.5 172.5c0 44.667 7.33301 87.167 22 127.5s35 77.833 61 112.5s56.833 66 92.5 94s74.834 52 117.501 72c-35.333 50 -61.166 98.833 -77.499 146.5s-24.5 95.167 -24.5 142.5c0 50 9.16699 97.5 27.5 142.5s45 84.167 80 117.5s77.833 59.833 128.5 79.5
s108 29.5 172 29.5zM315 418.004c0 -36.667 6.33203 -69.668 18.999 -99.001s30 -54.166 52 -74.499s47.5 -36 76.5 -47s59.833 -16.5 92.5 -16.5c58.667 0 112.167 9.16699 160.5 27.5s92.5 43.833 132.5 76.5l-380 390c-53.333 -34.667 -92.166 -73.667 -116.499 -117
s-36.5 -90 -36.5 -140z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="476" 
d="M346 1446v-288l-23 -156c-4.66699 -29.333 -13.167 -51.833 -25.5 -67.5s-32.5 -23.5 -60.5 -23.5c-24 0 -43 7.83301 -57 23.5s-23 38.167 -27 67.5l-22 156v288h215z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="600" 
d="M320 627c0 -137.333 16.665 -272.334 49.998 -405.001s81 -257.334 143 -374.001c4.66699 -8.66699 7.83398 -16.667 9.50098 -24s2.5 -14 2.5 -20c0 -13.333 -3.33301 -24.166 -10 -32.499s-14.667 -15.166 -24 -20.499l-110 -67c-49.333 76 -91.333 152.333 -126 229
s-63 154 -85 232s-38.167 157 -48.5 237s-15.5 161.667 -15.5 245s5.16699 165.166 15.5 245.499s26.5 159.5 48.5 237.5s50.333 155.167 85 231.5s76.667 152.5 126 228.5l110 -66c9.33301 -5.33301 17.333 -12.166 24 -20.499s10 -18.833 10 -31.5
s-4.33301 -27.667 -13 -45c-61.333 -116.667 -108.666 -241.5 -141.999 -374.5s-50 -268.167 -50 -405.5z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="600" 
d="M280 627c0 137.333 -16.6689 272.501 -50.002 405.501s-80.666 257.833 -141.999 374.5c-8.66699 17.333 -13 32.333 -13 45s3.33301 23.167 10 31.5s14.667 15.166 24 20.499l110 66c49.333 -76 91.333 -152.167 126 -228.5s63 -153.5 85 -231.5
s38.167 -157.167 48.5 -237.5s15.5 -162.166 15.5 -245.499s-5.16699 -165 -15.5 -245s-26.5 -159 -48.5 -237s-50.333 -155.333 -85 -232s-76.667 -153 -126 -229l-110 67c-9.33301 5.33301 -17.333 12.166 -24 20.499s-10 19.166 -10 32.499c0 6 0.833008 12.667 2.5 20
s4.83398 15.333 9.50098 24c62 116.667 109.667 241.334 143 374.001s50 267.667 50 405z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="800" 
d="M340 837l0.00488281 183.002c0 13.333 0.833008 26.333 2.5 39s4.5 24.667 8.5 36c-7.33301 -9.33301 -16 -17.666 -26 -24.999s-20.667 -14.666 -32 -21.999l-158 -92l-59 100l159 92c12 7.33301 23.833 13.333 35.5 18s23.834 8 36.501 10
c-12.667 1.33301 -24.834 4.66602 -36.501 9.99902s-23.5 11.666 -35.5 18.999l-159 93l58 100l159 -94c11.333 -7.33301 22.166 -14.833 32.499 -22.5s19.166 -16.167 26.499 -25.5c-4.66699 11.333 -7.83398 23.333 -9.50098 36s-2.5 25.667 -2.5 39v185h118v-183
c0 -14 -0.833008 -27.5 -2.5 -40.5s-4.83398 -25.167 -9.50098 -36.5c7.33301 9.33301 16 17.833 26 25.5s21 15.167 33 22.5l158 92l59 -100l-159 -91c-12 -7.33301 -23.833 -13.5 -35.5 -18.5s-23.834 -8.5 -36.501 -10.5c22.667 -4 46.667 -13.333 72 -28l159 -93
l-58 -100l-159 93c-12 7.33301 -23 14.666 -33 21.999s-19 15.666 -27 24.999c8.66699 -21.333 13 -46 13 -74v-184h-118z" />
    <glyph glyph-name="plus" unicode="+" 
d="M678 1173v-410h389v-183h-389v-412h-200v412h-387v183h387v410h200z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="450" 
d="M80 152c0 18.667 3.49707 36.166 10.4971 52.499s16.833 30.666 29.5 42.999s28 22 46 29s37.667 10.5 59 10.5c25.333 0 47.333 -4.5 66 -13.5s34.334 -21.333 47.001 -37s22 -33.667 28 -54s9 -41.833 9 -64.5c0 -32 -4.83301 -65.667 -14.5 -101
s-23.834 -70.666 -42.501 -105.999s-41.667 -69.666 -69 -102.999s-58.666 -64 -93.999 -92l-43 39c-12 10 -18 22.333 -18 37c0 5.33301 1.83301 11.333 5.5 18s8.16699 12.667 13.5 18l27 29.5c10.667 11.667 21.334 25 32.001 40s20.5 31.333 29.5 49
s15.5 36.5 19.5 56.5c-20.667 0 -39.667 3.83301 -57 11.5s-32.166 18.167 -44.499 31.5s-22 29 -29 47s-10.5 37.667 -10.5 59z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="718" 
d="M100 707h518v-206h-518v206z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="450" 
d="M73 136c0 20.667 3.83301 40.334 11.5 59.001s18.334 34.667 32.001 48s29.834 24 48.501 32s38.667 12 60 12c20.667 0 40.334 -4 59.001 -12s34.667 -18.667 48 -32s24 -29.333 32 -48s12 -38.334 12 -59.001c0 -21.333 -4 -41.166 -12 -59.499
s-18.667 -34.166 -32 -47.499s-29.333 -23.833 -48 -31.5s-38.334 -11.5 -59.001 -11.5c-21.333 0 -41.333 3.83301 -60 11.5s-34.834 18.167 -48.501 31.5s-24.334 29.166 -32.001 47.499s-11.5 38.166 -11.5 59.499z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="774" 
d="M218 -3c-6 -15.333 -13.666 -28.667 -22.999 -40s-20 -20.833 -32 -28.5s-24.667 -13.5 -38 -17.5s-26.333 -6 -39 -6h-104l584 1494c11.333 28 27.666 49.5 48.999 64.5s47 22.5 77 22.5h105z" />
    <glyph glyph-name="zero" unicode="0" 
d="M1110 723c0 -126 -13.5 -235.5 -40.5 -328.5s-64.333 -169.833 -112 -230.5s-104 -105.834 -169 -135.501s-135.167 -44.5 -210.5 -44.5s-145.166 14.833 -209.499 44.5s-120.166 74.834 -167.499 135.501s-84.333 137.5 -111 230.5s-40 202.5 -40 328.5
c0 126.667 13.333 236.334 40 329.001s63.667 169.334 111 230.001s103.166 105.834 167.499 135.501s134.166 44.5 209.499 44.5s145.5 -14.833 210.5 -44.5s121.333 -74.834 169 -135.501s85 -137.334 112 -230.001s40.5 -202.334 40.5 -329.001zM855 723
c0 104.667 -7.66699 191.334 -23 260.001s-35.833 123.334 -61.5 164.001s-55.167 69.167 -88.5 85.5s-68 24.5 -104 24.5c-35.333 0 -69.5 -8.16699 -102.5 -24.5s-62.167 -44.833 -87.5 -85.5s-45.5 -95.334 -60.5 -164.001s-22.5 -155.334 -22.5 -260.001
s7.5 -191.334 22.5 -260.001s35.167 -123.334 60.5 -164.001s54.5 -69.167 87.5 -85.5s67.167 -24.5 102.5 -24.5c36 0 70.667 8.16699 104 24.5s62.833 44.833 88.5 85.5s46.167 95.334 61.5 164.001s23 155.334 23 260.001z" />
    <glyph glyph-name="one" unicode="1" 
d="M269 185h293v843c0 32.667 1 67 3 103l-208 -174c-8.66699 -7.33301 -17.5 -12.333 -26.5 -15s-17.833 -4 -26.5 -4c-13.333 0 -25.5 2.83301 -36.5 8.5s-19.167 12.167 -24.5 19.5l-78 107l441 375h203v-1263h260v-185h-800v185z" />
    <glyph glyph-name="two" unicode="2" 
d="M602 1462c66.667 0 127.666 -9.83301 182.999 -29.5s102.666 -47.5 141.999 -83.5s70 -79.333 92 -130s33 -107 33 -169c0 -53.333 -7.83301 -102.666 -23.5 -147.999s-36.667 -88.666 -63 -129.999s-57.166 -81.333 -92.499 -120l-111 -118l-326 -333
c32 9.33301 63.333 16.5 94 21.5s60 7.5 88 7.5h371c26.667 0 47.834 -7.5 63.501 -22.5s23.5 -34.833 23.5 -59.5v-148h-992v82c0 16.667 3.5 34 10.5 52s18.167 34.667 33.5 50l439 440c36.667 37.333 69.5 73 98.5 107s53.333 67.667 73 101s34.667 67.166 45 101.499
s15.5 70.5 15.5 108.5c0 34.667 -5 65.167 -15 91.5s-24.333 48.5 -43 66.5s-40.834 31.5 -66.501 40.5s-54.5 13.5 -86.5 13.5c-59.333 0 -108.166 -15 -146.499 -45s-65.166 -70.333 -80.499 -121c-7.33301 -25.333 -18.333 -43.5 -33 -54.5s-33.334 -16.5 -56.001 -16.5
c-10 0 -21 1 -33 3l-130 23c10 69.333 29.333 130.166 58 182.499s64.5 96 107.5 131s92.333 61.333 148 79s115.5 26.5 179.5 26.5z" />
    <glyph glyph-name="three" unicode="3" 
d="M625 1462c66.667 0 126.833 -9.5 180.5 -28.5s99.5 -45.333 137.5 -79s67.167 -73.167 87.5 -118.5s30.5 -94.333 30.5 -147c0 -46 -5.16699 -86.5 -15.5 -121.5s-25.333 -65.5 -45 -91.5s-43.667 -48 -72 -66s-60.5 -33 -96.5 -45
c86.667 -27.333 151.334 -69 194.001 -125s64 -126.333 64 -211c0 -72 -13.333 -135.667 -40 -191s-62.667 -101.833 -108 -139.5s-97.833 -66.167 -157.5 -85.5s-122.834 -29 -189.501 -29c-72.667 0 -136 8.33301 -190 25s-101 41.334 -141 74.001s-74 72.667 -102 120
s-52 102 -72 164l109 45c18.667 8 37.667 12 57 12c17.333 0 32.833 -3.66699 46.5 -11s24.167 -18 31.5 -32c12 -23.333 25.167 -46.333 39.5 -69s31.5 -42.834 51.5 -60.501s43.5 -32 70.5 -43s59.167 -16.5 96.5 -16.5c42 0 78.667 6.83301 110 20.5s57.5 31.5 78.5 53.5
s36.667 46.5 47 73.5s15.5 54.167 15.5 81.5c0 34.667 -3.66699 66.167 -11 94.5s-22.666 52.5 -45.999 72.5s-56.833 35.667 -100.5 47s-102.167 17 -175.5 17v176c60.667 0.666992 111 6.33398 151 17.001s71.833 25.5 95.5 44.5s40.167 41.833 49.5 68.5s14 56 14 88
c0 68.667 -18.667 120.5 -56 155.5s-88.333 52.5 -153 52.5c-58.667 0 -107.334 -15.5 -146.001 -46.5s-65.667 -70.833 -81 -119.5c-8 -25.333 -19 -43.5 -33 -54.5s-32.333 -16.5 -55 -16.5c-10.667 0 -22 1 -34 3l-130 23c10 69.333 29.333 130.166 58 182.499
s64.5 96 107.5 131s92.333 61.333 148 79s115.5 26.5 179.5 26.5z" />
    <glyph glyph-name="four" unicode="4" 
d="M942 545l182.002 0.000976562v-143c0 -13.333 -4.33301 -24.833 -13 -34.5s-21.334 -14.5 -38.001 -14.5h-131v-353h-216v353h-610c-16.667 0 -31.334 5.16699 -44.001 15.5s-20.667 23.166 -24 38.499l-25 125l685 915h234v-902zM726.002 1018
c0 21.333 0.667969 44.332 2.00098 68.999s3.66602 50.334 6.99902 77.001l-449 -619h440v473z" />
    <glyph glyph-name="five" unicode="5" 
d="M989 1341c0 -35.333 -11.165 -63.9971 -33.498 -85.9971s-59.5 -33 -111.5 -33h-398l-52 -302c65.333 13.333 125 20 179 20c76 0 142.833 -11.333 200.5 -34s106.167 -54 145.5 -94s69 -86.833 89 -140.5s30 -111.5 30 -173.5c0 -76.667 -13.5 -146.667 -40.5 -210
s-64.5 -117.5 -112.5 -162.5s-104.833 -79.833 -170.5 -104.5s-137.167 -37 -214.5 -37c-45.333 0 -88.333 4.66699 -129 14s-78.834 21.833 -114.501 37.5s-68.667 33.667 -99 54s-57.5 41.833 -81.5 64.5l76 105c16 22.667 37.333 34 64 34
c16.667 0 33.5 -5.33301 50.5 -16s36.833 -22.334 59.5 -35.001s49 -24.334 79 -35.001s66.333 -16 109 -16c45.333 0 85.333 7.33301 120 22s63.5 35.167 86.5 61.5s40.333 57.666 52 93.999s17.5 75.833 17.5 118.5c0 78.667 -22.833 140.167 -68.5 184.5
s-112.834 66.5 -201.501 66.5c-70 0 -140.333 -12.667 -211 -38l-154 44l120 702h714v-105z" />
    <glyph glyph-name="six" unicode="6" 
d="M670 903c54.667 0 107.832 -8.99902 159.499 -26.999s97.167 -45.167 136.5 -81.5s71 -81.5 95 -135.5s36 -117 36 -189c0 -67.333 -12.333 -130.5 -37 -189.5s-59.334 -110.5 -104.001 -154.5s-98.5 -78.667 -161.5 -104s-132.5 -38 -208.5 -38
c-77.333 0 -146.666 12.333 -207.999 37s-113.666 59.167 -156.999 103.5s-76.5 97.666 -99.5 159.999s-34.5 131.5 -34.5 207.5c0 68 13.833 137.833 41.5 209.5s70.5 146.167 128.5 223.5l345 463c12 16 29.5 29.667 52.5 41s49.5 17 79.5 17h220l-429 -525l-26.5 -32
c-8.33301 -10 -16.5 -20.333 -24.5 -31c28 14 58.167 25 90.5 33s67.5 12 105.5 12zM323.998 454.001c0 -40 5.5 -76.501 16.5 -109.501s27.333 -61.167 49 -84.5s48.5 -41.5 80.5 -54.5s69 -19.5 111 -19.5c39.333 0 75.5 6.66699 108.5 20s61.333 32 85 56
s42.167 52.167 55.5 84.5s20 67.5 20 105.5c0 41.333 -6.33301 78.5 -19 111.5s-30.667 61 -54 84s-51.333 40.5 -84 52.5s-68.667 18 -108 18s-75 -6.66699 -107 -20s-59.5 -31.833 -82.5 -55.5s-40.667 -51.667 -53 -84s-18.5 -67.166 -18.5 -104.499z" />
    <glyph glyph-name="seven" unicode="7" 
d="M1096 1446l-0.000976562 -107c0 -32 -3.5 -57.833 -10.5 -77.5s-13.833 -36.167 -20.5 -49.5l-547 -1128c-11.333 -23.333 -27.333 -43.166 -48 -59.499s-48.667 -24.5 -84 -24.5h-179l560 1105c23.333 45.333 49.333 84.333 78 117h-692
c-15.333 0 -28.666 5.66699 -39.999 17s-17 24.666 -17 39.999v167h1000z" />
    <glyph glyph-name="eight" unicode="8" 
d="M580 -16c-74.667 0 -142.834 10 -204.501 30s-114.5 48.333 -158.5 85s-78.167 81 -102.5 133s-36.5 110 -36.5 174c0 85.333 21 157.5 63 216.5s107.333 102.833 196 131.5c-70.667 29.333 -123.5 71 -158.5 125s-52.5 119 -52.5 195c0 54.667 11.167 105.667 33.5 153
s53.5 88.5 93.5 123.5s87.833 62.333 143.5 82s116.834 29.5 183.501 29.5s127.834 -9.83301 183.501 -29.5s103.5 -47 143.5 -82s71.167 -76.167 93.5 -123.5s33.5 -98.333 33.5 -153c0 -76 -17.667 -141 -53 -195s-88 -95.667 -158 -125
c88.667 -28.667 154 -72.5 196 -131.5s63 -131.167 63 -216.5c0 -64 -12.167 -122 -36.5 -174s-58.5 -96.333 -102.5 -133s-96.833 -65 -158.5 -85s-129.834 -30 -204.501 -30zM580 177c40.667 0 76.332 5.83301 106.999 17.5s56.5 27.834 77.5 48.501
s36.833 45.334 47.5 74.001s16 59.667 16 93c0 79.333 -22 139.333 -66 180s-104.667 61 -182 61s-138 -20.333 -182 -61s-66 -100.667 -66 -180c0 -33.333 5.33301 -64.333 16 -93s26.5 -53.334 47.5 -74.001s46.833 -36.834 77.5 -48.501s66.334 -17.5 107.001 -17.5z
M579.998 845c40 0 73.6699 6.33301 101.003 19s49.333 29.5 66 50.5s28.5 45 35.5 72s10.5 54.833 10.5 83.5c0 27.333 -4.33301 53.333 -13 78s-21.667 46.5 -39 65.5s-39.333 34.167 -66 45.5s-58.334 17 -95.001 17s-68.334 -5.66699 -95.001 -17
s-48.834 -26.5 -66.501 -45.5s-30.667 -40.833 -39 -65.5s-12.5 -50.667 -12.5 -78c0 -28.667 3.5 -56.5 10.5 -83.5s18.833 -51 35.5 -72s38.667 -37.833 66 -50.5s61 -19 101 -19z" />
    <glyph glyph-name="nine" unicode="9" 
d="M530 577c-50 0 -99 8.66406 -147 25.9971s-90.833 43.5 -128.5 78.5s-67.834 78.333 -90.501 130s-34 112.167 -34 181.5c0 64.667 12 125.334 36 182.001s57.667 106.334 101 149.001s95.333 76.334 156 101.001s127.667 37 201 37c74 0 140.5 -11.667 199.5 -35
s109.333 -56 151 -98s73.667 -92.333 96 -151s33.5 -123 33.5 -193c0 -44.667 -3.83301 -87 -11.5 -127s-18.667 -78.5 -33 -115.5s-31.5 -73 -51.5 -108s-42.333 -70.167 -67 -105.5l-331 -472c-11.333 -16 -28.166 -29.5 -50.499 -40.5s-47.833 -16.5 -76.5 -16.5h-227
l449 561l32 41l29 40c-33.333 -21.333 -70 -37.5 -110 -48.5s-82 -16.5 -126 -16.5zM867 1010c0 39.333 -6.00098 74.501 -18.001 105.501s-29 57.167 -51 78.5s-48.167 37.666 -78.5 48.999s-63.5 17 -99.5 17c-37.333 0 -71 -6.16699 -101 -18.5s-55.5 -29.5 -76.5 -51.5
s-37.167 -48.167 -48.5 -78.5s-17 -63.5 -17 -99.5c0 -80.667 20.833 -142.167 62.5 -184.5s100.834 -63.5 177.501 -63.5c40 0 75.5 6.33301 106.5 19s57.167 30 78.5 52s37.5 48 48.5 78s16.5 62.333 16.5 97z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="530" 
d="M113 136c0 20.667 3.83301 40.334 11.5 59.001s18.334 34.667 32.001 48s29.834 24 48.501 32s38.667 12 60 12c20.667 0 40.334 -4 59.001 -12s34.667 -18.667 48 -32s24 -29.333 32 -48s12 -38.334 12 -59.001c0 -21.333 -4 -41.166 -12 -59.499
s-18.667 -34.166 -32 -47.499s-29.333 -23.833 -48 -31.5s-38.334 -11.5 -59.001 -11.5c-21.333 0 -41.333 3.83301 -60 11.5s-34.834 18.167 -48.501 31.5s-24.334 29.166 -32.001 47.499s-11.5 38.166 -11.5 59.499zM113 849c0 20.667 3.83301 40.334 11.5 59.001
s18.334 34.667 32.001 48s29.834 24 48.501 32s38.667 12 60 12c20.667 0 40.334 -4 59.001 -12s34.667 -18.667 48 -32s24 -29.333 32 -48s12 -38.334 12 -59.001c0 -21.333 -4 -41.166 -12 -59.499s-18.667 -34.166 -32 -47.499s-29.333 -23.833 -48 -31.5
s-38.334 -11.5 -59.001 -11.5c-21.333 0 -41.333 3.83301 -60 11.5s-34.834 18.167 -48.501 31.5s-24.334 29.166 -32.001 47.499s-11.5 38.166 -11.5 59.499z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="530" 
d="M120 152c0 18.667 3.49707 36.166 10.4971 52.499s16.833 30.666 29.5 42.999s28 22 46 29s37.667 10.5 59 10.5c25.333 0 47.333 -4.5 66 -13.5s34.334 -21.333 47.001 -37s22 -33.667 28 -54s9 -41.833 9 -64.5c0 -32 -4.83301 -65.667 -14.5 -101
s-23.834 -70.666 -42.501 -105.999s-41.667 -69.666 -69 -102.999s-58.666 -64 -93.999 -92l-43 39c-12 10 -18 22.333 -18 37c0 5.33301 1.83301 11.333 5.5 18s8.16699 12.667 13.5 18l27 29.5c10.667 11.667 21.334 25 32.001 40s20.5 31.333 29.5 49
s15.5 36.5 19.5 56.5c-20.667 0 -39.667 3.83301 -57 11.5s-32.166 18.167 -44.499 31.5s-22 29 -29 47s-10.5 37.667 -10.5 59zM112.997 848.999c0 20.667 3.83301 40.334 11.5 59.001s18.334 34.667 32.001 48s29.834 24 48.501 32s38.667 12 60 12
c20.667 0 40.334 -4 59.001 -12s34.667 -18.667 48 -32s24 -29.333 32 -48s12 -38.334 12 -59.001c0 -21.333 -4 -41.166 -12 -59.499s-18.667 -34.166 -32 -47.499s-29.333 -23.833 -48 -31.5s-38.334 -11.5 -59.001 -11.5c-21.333 0 -41.333 3.83301 -60 11.5
s-34.834 18.167 -48.501 31.5s-24.334 29.166 -32.001 47.499s-11.5 38.166 -11.5 59.499z" />
    <glyph glyph-name="less" unicode="&#x3c;" 
d="M139 724l792 413.002v-176c0 -13.333 -3.33301 -25.5 -10 -36.5s-17.667 -20.5 -33 -28.5l-363 -186c-15.333 -8 -31.5 -14.833 -48.5 -20.5s-35.167 -10.834 -54.5 -15.501c19.333 -4.66699 37.5 -9.83398 54.5 -15.501s33.167 -12.5 48.5 -20.5l363 -187
c15.333 -8 26.333 -17.5 33 -28.5s10 -23.167 10 -36.5v-176l-792 414v100z" />
    <glyph glyph-name="equal" unicode="=" 
d="M136 588h886v-184h-886v184zM136 940h886v-183h-886v183z" />
    <glyph glyph-name="greater" unicode="&#x3e;" 
d="M229 210l0.000976562 175.999c0 13.333 3.33301 25.5 10 36.5s17.667 20.5 33 28.5l363 187c29.333 14.667 63.666 26.667 102.999 36c-19.333 4.66699 -37.5 9.83398 -54.5 15.501s-33.167 12.5 -48.5 20.5l-363 186c-15.333 8 -26.333 17.5 -33 28.5
s-10 23.167 -10 36.5v176l792 -413v-100z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="841" 
d="M37 1315c22.667 20 47.498 38.999 74.498 56.999s56.167 33.667 87.5 47s65.333 23.833 102 31.5s76.334 11.5 119.001 11.5c58 0 110.833 -8 158.5 -24s88.5 -38.833 122.5 -68.5s60.333 -65.5 79 -107.5s28 -89 28 -141c0 -50.667 -7.33301 -94.5 -22 -131.5
s-33.167 -69.167 -55.5 -96.5s-46.5 -51 -72.5 -71l-74 -56.5c-23.333 -17.667 -43.5 -35 -60.5 -52s-27.167 -36.167 -30.5 -57.5l-23 -146h-169l-17 163c-0.666992 3.33301 -1 6.16602 -1 8.49902v8.5c0 29.333 7.33301 54.833 22 76.5s33 42 55 61s45.667 37.5 71 55.5
s49 37.5 71 58.5s40.333 44.833 55 71.5s22 58 22 94c0 24 -4.5 45.5 -13.5 64.5s-21.5 35.333 -37.5 49s-35.167 24.167 -57.5 31.5s-46.5 11 -72.5 11c-38 0 -70.167 -4.16699 -96.5 -12.5s-48.666 -17.666 -66.999 -27.999s-33.833 -19.666 -46.5 -27.999
s-24 -12.5 -34 -12.5c-24 0 -41.333 10 -52 30zM225.998 135.999c0 20.667 3.83301 40.334 11.5 59.001s18.334 34.667 32.001 48s29.834 24 48.501 32s38.667 12 60 12c20.667 0 40.334 -4 59.001 -12s34.667 -18.667 48 -32s24 -29.333 32 -48s12 -38.334 12 -59.001
c0 -21.333 -4 -41.166 -12 -59.499s-18.667 -34.166 -32 -47.499s-29.333 -23.833 -48 -31.5s-38.334 -11.5 -59.001 -11.5c-21.333 0 -41.333 3.83301 -60 11.5s-34.834 18.167 -48.501 31.5s-24.334 29.166 -32.001 47.499s-11.5 38.166 -11.5 59.499z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="1645" 
d="M1166 184c-45.333 0 -85.8291 10.667 -121.496 32s-60.5 55.666 -74.5 102.999c-39.333 -48 -81 -82.333 -125 -103s-91.333 -31 -142 -31c-42 0 -78.5 7.33301 -109.5 22s-57 34.834 -78 60.501s-36.667 56 -47 91s-15.5 73.167 -15.5 114.5
c0 38 4.83301 76.5 14.5 115.5s24.167 76.667 43.5 113s43.5 70.333 72.5 102s63.167 59.334 102.5 83.001s83.333 42.334 132 56.001s102.667 20.5 162 20.5c51.333 0 95.333 -4 132 -12s72 -19.333 106 -34l-96 -371c-10.667 -42.667 -16 -78.334 -16 -107.001
c0 -20 2.5 -36.667 7.5 -50s11.667 -24 20 -32s18.5 -13.5 30.5 -16.5s25 -4.5 39 -4.5c28 0 54.5 8.83301 79.5 26.5s46.833 42.5 65.5 74.5s33.5 70 44.5 114s16.5 92 16.5 144c0 86.667 -13.5 163 -40.5 229s-64.667 121.167 -113 165.5s-106.166 77.666 -173.499 99.999
s-141 33.5 -221 33.5c-87.333 0 -168.5 -16.5 -243.5 -49.5s-140 -78.167 -195 -135.5s-98 -125 -129 -203s-46.5 -162 -46.5 -252c0 -109.333 17 -205.5 51 -288.5s80.667 -152.333 140 -208s129.166 -97.5 209.499 -125.5s167.166 -42 260.499 -42
c51.333 0 99.333 2.83301 144 8.5s85.667 13.334 123 23.001s71.333 20.667 102 33s58 25.5 82 39.5c13.333 7.33301 24.666 11 33.999 11c19.333 0 33 -10.667 41 -32l34 -89c-69.333 -45.333 -150.333 -82.333 -243 -111s-198.334 -43 -317.001 -43
c-120.667 0 -232.334 19 -335.001 57s-191.5 92.5 -266.5 163.5s-133.667 157.5 -176 259.5s-63.5 216.667 -63.5 344c0 71.333 9.16699 140.666 27.5 207.999s44.5 130.5 78.5 189.5s75 113.167 123 162.5s101.333 91.666 160 126.999s122.167 62.833 190.5 82.5
s139.833 29.5 214.5 29.5c94.667 0 185.5 -15.833 272.5 -47.5s164 -77 231 -136s120.5 -131 160.5 -216s60 -180.833 60 -287.5c0 -71.333 -10.5 -138.166 -31.5 -200.499s-50.333 -116.5 -88 -162.5s-82 -82.167 -133 -108.5s-106.5 -39.5 -166.5 -39.5zM752.004 344
c17.333 0 34.833 2.66895 52.5 8.00195s34.5 14.833 50.5 28.5s30.667 31.834 44 54.501s24.333 51 33 85l73 282c-21.333 4 -43.333 6 -66 6c-41.333 0 -80 -9 -116 -27s-67.167 -42.333 -93.5 -73s-47.166 -65.834 -62.499 -105.501s-23 -80.834 -23 -123.501
s9.16699 -75.834 27.5 -99.501s45.166 -35.5 80.499 -35.5z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="1420" 
d="M1417 0h-207.999c-23.333 -0 -42.5 5.83301 -57.5 17.5s-25.833 26.167 -32.5 43.5l-108 295h-599l-108 -295c-5.33301 -15.333 -15.833 -29.333 -31.5 -42s-34.834 -19 -57.501 -19h-209l568 1446h275zM481.001 546h461l-176 481c-8 21.333 -16.833 46.5 -26.5 75.5
s-19.167 60.5 -28.5 94.5c-9.33301 -34 -18.5 -65.667 -27.5 -95s-17.833 -55 -26.5 -77z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="1303" 
d="M146 0v1446h499c94.667 0 175.5 -9 242.5 -27s122 -43.667 165 -77s74.5 -73.666 94.5 -120.999s30 -100.666 30 -159.999c0 -34 -5 -66.5 -15 -97.5s-25.5 -60 -46.5 -87s-47.667 -51.5 -80 -73.5s-70.833 -40.667 -115.5 -56c198.667 -44.667 298 -152 298 -322
c0 -61.333 -11.667 -118 -35 -170s-57.333 -96.833 -102 -134.5s-99.667 -67.167 -165 -88.5s-140 -32 -224 -32h-546zM415 633.998l-0.00195312 -424h273c50 0 91.833 6 125.5 18s60.667 28 81 48s35 43.333 44 70s13.5 55 13.5 85c0 31.333 -5 59.5 -15 84.5
s-25.667 46.333 -47 64s-48.666 31.167 -81.999 40.5s-73.666 14 -120.999 14h-272zM414.998 819.998h215.002c91.333 0 160.666 16.667 207.999 50s71 86.333 71 159c0 75.333 -21.333 129 -64 161s-109.334 48 -200.001 48h-230v-418z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="1341" 
d="M1140 341c14.667 0 27.3379 -5.66699 38.0049 -17l106 -115c-58.667 -72.667 -130.834 -128.334 -216.501 -167.001s-188.5 -58 -308.5 -58c-107.333 0 -203.833 18.333 -289.5 55s-158.834 87.667 -219.501 153s-107.167 143.333 -139.5 234s-48.5 189.667 -48.5 297
c0 108.667 18 208.167 54 298.5s86.667 168.166 152 233.499s143.5 116.166 234.5 152.499s191.5 54.5 301.5 54.5c107.333 0 201.166 -17.167 281.499 -51.5s149.166 -79.833 206.499 -136.5l-90 -125c-5.33301 -8 -12.166 -15 -20.499 -21s-19.833 -9 -34.5 -9
c-15.333 0 -31 6 -47 18s-36.333 25 -61 39s-55.834 27 -93.501 39s-85.5 18 -143.5 18c-68 0 -130.5 -11.833 -187.5 -35.5s-106 -57.5 -147 -101.5s-73 -97.5 -96 -160.5s-34.5 -133.833 -34.5 -212.5c0 -81.333 11.5 -153.666 34.5 -216.999
s54.167 -116.666 93.5 -159.999s85.666 -76.5 138.999 -99.5s110.666 -34.5 171.999 -34.5c36.667 0 69.834 2 99.501 6s57 10.333 82 19s48.667 19.834 71 33.501s44.5 30.5 66.5 50.5c6.66699 6 13.667 10.833 21 14.5s15.333 5.5 24 5.5z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="1495" 
d="M1430 723c0 -106 -17.667 -203.331 -53 -291.998s-85 -165 -149 -229s-141 -113.667 -231 -149s-189.667 -53 -299 -53h-552v1446h552c109.333 0 209 -17.833 299 -53.5s167 -85.334 231 -149.001s113.667 -139.834 149 -228.501s53 -186 53 -292zM1155 723.002
c0 79.333 -10.667 150.5 -32 213.5s-51.666 116.333 -90.999 160s-87.166 77.167 -143.499 100.5s-119.833 35 -190.5 35h-281v-1018h281c70.667 0 134.167 11.667 190.5 35s104.166 56.833 143.499 100.5s69.666 97 90.999 160s32 134.167 32 213.5z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="1145" 
d="M1058 1446v-214h-641v-401h505v-207h-505v-409h641v-215h-912v1446h912z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="1123" 
d="M1058 1446v-214h-641v-428h541v-215h-541v-589h-271v1446h912z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="1446" 
d="M810 198c60.667 0 113.501 5.50293 158.501 16.5029s87.833 26.167 128.5 45.5v263h-181c-17.333 0 -31 4.83301 -41 14.5s-15 21.5 -15 35.5v152h481v-585c-36.667 -26.667 -74.834 -49.834 -114.501 -69.501s-82 -35.834 -127 -48.501s-93.167 -22.167 -144.5 -28.5
s-106.666 -9.5 -165.999 -9.5c-105.333 0 -202.333 18.333 -291 55s-165.334 87.667 -230.001 153s-115.167 143.333 -151.5 234s-54.5 189.667 -54.5 297c0 108.667 17.667 208.334 53 299.001s85.5 168.667 150.5 234s143.833 116 236.5 152s196.667 54 312 54
c118 0 220.167 -17.5 306.5 -52.5s159.5 -80.5 219.5 -136.5l-78 -122c-15.333 -24.667 -35.666 -37 -60.999 -37c-16 0 -32.333 5.33301 -49 16l-65.5 38c-22.333 12.667 -47 23.667 -74 33s-57.333 17 -91 23s-72.5 9 -116.5 9c-71.333 0 -135.833 -12 -193.5 -36
s-106.834 -58.333 -147.501 -103s-72 -98.334 -94 -161.001s-33 -132.667 -33 -210c0 -83.333 11.667 -157.5 35 -222.5s56 -119.833 98 -164.5s92 -78.834 150 -102.501s121.333 -35.5 190 -35.5z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="1512" 
d="M1366 0h-271v632h-678v-632h-271v1446h271v-622h678v622h271v-1446z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="628" 
d="M449 0h-270v1446h270v-1446z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="874" 
d="M728 514c0 -81.333 -10 -154.666 -30 -219.999s-49.833 -121 -89.5 -167s-89.167 -81.333 -148.5 -106s-128.333 -37 -207 -37c-36 0 -71.333 2.16699 -106 6.5s-71 11.166 -109 20.499l14 160c1.33301 14.667 6.83301 26.5 16.5 35.5s23.834 13.5 42.501 13.5
c11.333 0 26.166 -2.33301 44.499 -7s41.5 -7 69.5 -7c38 0 71.667 5.5 101 16.5s53.833 28.5 73.5 52.5s34.5 55.167 44.5 93.5s15 84.833 15 139.5v938h269v-932z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="1396" 
d="M424 840l63.001 0.00292969c25.333 0 46.333 3.5 63 10.5s31 18.167 43 33.5l399 505c16.667 21.333 34.167 36.166 52.5 44.499s41.5 12.5 69.5 12.5h232l-487 -601c-15.333 -18.667 -30 -34.334 -44 -47.001s-29 -23 -45 -31c22 -8 41.833 -19.333 59.5 -34
s34.834 -33.334 51.501 -56.001l502 -677h-238c-32 0 -55.833 4.5 -71.5 13.5s-28.834 22.167 -39.501 39.5l-409 534c-13.333 17.333 -28.333 29.833 -45 37.5s-40.334 11.5 -71.001 11.5h-85v-636h-269v1447h269v-607z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="1034" 
d="M415 222h579v-222h-848v1446h269v-1224z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="1860" 
d="M872 600c11.333 -21.333 21.833 -43.499 31.5 -66.499l28.5 -69.5c9.33301 24 19 47.667 29 71s20.667 45.666 32 66.999l428 801c5.33301 10 10.833 18 16.5 24s12 10.333 19 13s14.833 4.33398 23.5 5.00098s19 1 31 1h203v-1446h-237v934c0 17.333 0.5 36.333 1.5 57
s2.5 41.667 4.5 63l-437 -820c-10 -18.667 -23 -33.167 -39 -43.5s-34.667 -15.5 -56 -15.5h-37c-21.333 0 -40 5.16699 -56 15.5s-29 24.833 -39 43.5l-443 823c2.66699 -22 4.5 -43.5 5.5 -64.5s1.5 -40.5 1.5 -58.5v-934h-237v1446h203c12 0 22.333 -0.333008 31 -1
s16.5 -2.33398 23.5 -5.00098s13.5 -7 19.5 -13s11.667 -14 17 -24l432 -803v0z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="1512" 
d="M287 1446c12 0 22 -0.498047 30 -1.49805s15.167 -3 21.5 -6s12.5 -7.33301 18.5 -13s12.667 -13.167 20 -22.5l759 -967c-2.66699 23.333 -4.5 46.166 -5.5 68.499s-1.5 43.166 -1.5 62.499v879h237v-1446h-139c-21.333 -0 -39 3.33301 -53 10s-27.667 18.667 -41 36
l-756 963c2 -21.333 3.5 -42.5 4.5 -63.5s1.5 -40.167 1.5 -57.5v-888h-237v1446h141v0z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="1599" 
d="M1533 723c0 -106 -17.667 -204.167 -53 -294.5s-85 -168.5 -149 -234.5s-141 -117.5 -231 -154.5s-189.667 -55.5 -299 -55.5s-209.166 18.5 -299.499 55.5s-167.666 88.5 -231.999 154.5s-114.166 144.167 -149.499 234.5s-53 188.5 -53 294.5s17.667 204.167 53 294.5
s85.166 168.5 149.499 234.5s141.666 117.5 231.999 154.5s190.166 55.5 299.499 55.5s209 -18.667 299 -56s167 -88.833 231 -154.5s113.667 -143.667 149 -234s53 -188.5 53 -294.5zM1257 723c0 79.333 -10.5 150.5 -31.5 213.5s-51.167 116.5 -90.5 160.5
s-87.166 77.667 -143.499 101s-119.833 35 -190.5 35s-134.334 -11.667 -191.001 -35s-104.834 -57 -144.501 -101s-70.167 -97.5 -91.5 -160.5s-32 -134.167 -32 -213.5s10.667 -150.5 32 -213.5s51.833 -116.333 91.5 -160s87.834 -77.167 144.501 -100.5
s120.334 -35 191.001 -35s134.167 11.667 190.5 35s104.166 56.833 143.499 100.5s69.5 97 90.5 160s31.5 134.167 31.5 213.5z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="1250" 
d="M424 509l-0.000976562 -508.998h-269v1446h471c96.667 0 180.334 -11.333 251.001 -34s129 -54.334 175 -95.001s80 -89.334 102 -146.001s33 -118.667 33 -186c0 -70 -11.667 -134.167 -35 -192.5s-58.333 -108.5 -105 -150.5s-105 -74.667 -175 -98s-152 -35 -246 -35
h-202zM423.999 719.002h202c49.333 0 92.333 6.16699 129 18.5s67 30 91 53s42 51 54 84s18 69.833 18 110.5c0 38.667 -6 73.667 -18 105s-30 58 -54 80s-54.333 38.833 -91 50.5s-79.667 17.5 -129 17.5h-202v-519z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="1599" 
d="M1533 723c0 -64.667 -6.66602 -126.5 -19.999 -185.5s-32.5 -114.167 -57.5 -165.5s-55.5 -98.5 -91.5 -141.5s-77 -80.833 -123 -113.5l367 -400h-222c-32 0 -60.833 4.33301 -86.5 13s-49.167 24.667 -70.5 48l-212 234c-34 -9.33301 -68.833 -16.333 -104.5 -21
s-72.834 -7 -111.501 -7c-109.333 0 -209.166 18.5 -299.499 55.5s-167.666 88.5 -231.999 154.5s-114.166 144.167 -149.499 234.5s-53 188.5 -53 294.5s17.667 204.167 53 294.5s85.166 168.5 149.499 234.5s141.666 117.5 231.999 154.5s190.166 55.5 299.499 55.5
s209 -18.667 299 -56s167 -88.833 231 -154.5s113.667 -143.667 149 -234s53 -188.5 53 -294.5zM1257 723c0 79.333 -10.5 150.5 -31.5 213.5s-51.167 116.5 -90.5 160.5s-87.166 77.667 -143.499 101s-119.833 35 -190.5 35s-134.334 -11.667 -191.001 -35
s-104.834 -57 -144.501 -101s-70.167 -97.5 -91.5 -160.5s-32 -134.167 -32 -213.5s10.667 -150.5 32 -213.5s51.833 -116.333 91.5 -160s87.834 -77.167 144.501 -100.5s120.334 -35 191.001 -35s134.167 11.667 190.5 35s104.166 56.833 143.499 100.5s69.5 97 90.5 160
s31.5 134.167 31.5 213.5z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="1310" 
d="M424 565v-564.999h-269v1446h441c98.667 0 183.167 -10.167 253.5 -30.5s128 -48.833 173 -85.5s78 -80.5 99 -131.5s31.5 -107.167 31.5 -168.5c0 -48.667 -7.16699 -94.667 -21.5 -138s-35 -82.666 -62 -117.999s-60.333 -66.333 -100 -93s-84.834 -48 -135.501 -64
c34 -19.333 63.333 -47 88 -83l362 -534h-242c-23.333 0 -43.166 4.66699 -59.499 14s-30.166 22.666 -41.499 39.999l-304 463c-11.333 17.333 -23.833 29.666 -37.5 36.999s-33.834 11 -60.501 11h-115zM424 758.001h167.998c50.667 0 94.834 6.33301 132.501 19
s68.667 30.167 93 52.5s42.5 48.833 54.5 79.5s18 64.334 18 101.001c0 73.333 -24.167 129.666 -72.5 168.999s-122.166 59 -221.499 59h-172v-480z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="1073" 
d="M921 1183c-7.33301 -14.667 -15.8311 -24.9961 -25.498 -30.9961s-21.167 -9 -34.5 -9s-28.333 5.16699 -45 15.5s-36.334 21.833 -59.001 34.5s-49.167 24.167 -79.5 34.5s-66.166 15.5 -107.499 15.5c-37.333 0 -69.833 -4.5 -97.5 -13.5s-51 -21.5 -70 -37.5
s-33.167 -35.167 -42.5 -57.5s-14 -46.833 -14 -73.5c0 -34 9.5 -62.333 28.5 -85s44.167 -42 75.5 -58s67 -30.333 107 -43l122.5 -40.5c41.667 -14.333 82.5 -31 122.5 -50s75.667 -43 107 -72s56.5 -64.5 75.5 -106.5s28.5 -93 28.5 -153
c0 -65.333 -11.167 -126.5 -33.5 -183.5s-55 -106.667 -98 -149s-95.5 -75.666 -157.5 -99.999s-133 -36.5 -213 -36.5c-46 0 -91.333 4.5 -136 13.5s-87.5 21.833 -128.5 38.5s-79.333 36.667 -115 60s-67.5 49.333 -95.5 78l78 129
c7.33301 9.33301 16.166 17.166 26.499 23.499s21.833 9.5 34.5 9.5c16.667 0 34.667 -6.83301 54 -20.5s42.166 -28.834 68.499 -45.501s57.333 -31.834 93 -45.501s78.5 -20.5 128.5 -20.5c76.667 0 136 18.167 178 54.5s63 88.5 63 156.5c0 38 -9.5 69 -28.5 93
s-44.167 44.167 -75.5 60.5s-67 30.166 -107 41.499s-80.667 23.666 -122 36.999s-82 29.333 -122 48s-75.667 43 -107 73s-56.5 67.5 -75.5 112.5s-28.5 100.5 -28.5 166.5c0 52.667 10.5 104 31.5 154s51.667 94.333 92 133s89.833 69.667 148.5 93s125.667 35 201 35
c85.333 0 164 -13.333 236 -40s133.333 -64 184 -112z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="1190" 
d="M1165 1446v-221h-435v-1225h-269v1225h-437v221h1141z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="1456" 
d="M728 217c52 0 98.501 8.66895 139.501 26.002s75.667 41.666 104 72.999s50 69.333 65 114s22.5 94.667 22.5 150v866h269v-866c0 -86 -13.833 -165.5 -41.5 -238.5s-67.5 -136 -119.5 -189s-115 -94.333 -189 -124s-157.333 -44.5 -250 -44.5s-176 14.833 -250 44.5
s-136.833 71 -188.5 124s-91.334 116 -119.001 189s-41.5 152.5 -41.5 238.5v866h269v-865c0 -55.333 7.5 -105.333 22.5 -150s36.5 -82.834 64.5 -114.501s62.5 -56.167 103.5 -73.5s87.5 -26 139.5 -26z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="1420" 
d="M4 1446l217.002 -0.000976562c23.333 0 42.333 -5.66699 57 -17s25.667 -26 33 -44l340 -882c11.333 -28.667 22.166 -60.167 32.499 -94.5s20.166 -70.5 29.499 -108.5c15.333 76.667 34.333 144.334 57 203.001l339 882c6 15.333 16.667 29.333 32 42s34.333 19 57 19
h217l-584 -1446h-243z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="2093" 
d="M12 1446h226.001c23.333 0 42.833 -5.5 58.5 -16.5s26.167 -25.833 31.5 -44.5l246 -865c6 -21.333 11.5 -44.5 16.5 -69.5s9.83301 -51.5 14.5 -79.5c5.33301 28 11.166 54.5 17.499 79.5s13.166 48.167 20.499 69.5l284 865c5.33301 15.333 15.833 29.333 31.5 42
s34.834 19 57.501 19h79c23.333 0 42.666 -5.5 57.999 -16.5s26 -25.833 32 -44.5l282 -865c14.667 -42.667 27.334 -90 38.001 -142c4.66699 26 9.5 51 14.5 75s10.167 46.333 15.5 67l246 865c4.66699 16.667 15 31 31 43s35.333 18 58 18h211l-449 -1446h-243l-316 988
l-12.5 41c-4.33301 14.667 -8.5 30.334 -12.5 47.001c-4 -16.667 -8.16699 -32.334 -12.5 -47.001l-12.5 -41l-319 -988h-243z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="1358" 
d="M493 744l-461.001 702h268c18.667 0 32.167 -2.5 40.5 -7.5s15.833 -13.167 22.5 -24.5l329 -531c3.33301 8.66699 7 17.167 11 25.5s8.66699 16.833 14 25.5l301 475c14.667 24.667 33.667 37 57 37h258l-466 -691l479 -755h-269c-18 0 -32.5 4.66699 -43.5 14
s-20.167 20 -27.5 32l-335 554c-2.66699 -8 -5.66699 -15.5 -9 -22.5s-6.66602 -13.5 -9.99902 -19.5l-321 -512c-7.33301 -11.333 -16.333 -21.833 -27 -31.5s-24 -14.5 -40 -14.5h-252z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="1309" 
d="M789 562l0.000976562 -562h-269v562l-527 884h237c23.333 0 41.833 -5.66699 55.5 -17s25.167 -25.666 34.5 -42.999l265 -483c15.333 -28.667 28.666 -55.834 39.999 -81.501s21.666 -50.834 30.999 -75.501c8.66699 25.333 18.5 50.833 29.5 76.5
s24.167 52.5 39.5 80.5l263 483c7.33301 14.667 18.333 28.334 33 41.001s33.334 19 56.001 19h238z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="1234" 
d="M1179 1446v-98.999c0 -30.667 -8.66699 -58.667 -26 -84l-742 -1048h750v-215h-1099v106c0 13.333 2.33301 26.166 7 38.499s10.667 23.833 18 34.5l744 1053h-722v214h1070z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="600" 
d="M115 -308v1855h410v-97c0 -17.333 -6.16699 -32.166 -18.5 -44.499s-28.166 -18.5 -47.499 -18.5h-138v-1535h138c19.333 0 35.166 -6.16699 47.499 -18.5s18.5 -27.166 18.5 -44.499v-97h-410z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="781" 
d="M-28 1486h106c29.333 0 54.833 -7.5 76.5 -22.5s38.167 -36.5 49.5 -64.5l583 -1494h-104c-26 0 -51.5 7.66699 -76.5 23s-43.167 38.333 -54.5 69z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="600" 
d="M75 -308v97.002c0 17.333 6.16699 32.166 18.5 44.499s28.166 18.5 47.499 18.5h138v1535h-138c-19.333 0 -35.166 6.16699 -47.499 18.5s-18.5 27.166 -18.5 44.499v97h410v-1855h-410z" />
    <glyph glyph-name="asciicircum" unicode="^" 
d="M1028 777h-181.001c-15.333 0 -27.833 4 -37.5 12s-17.834 17.667 -24.501 29l-159 289l-27.5 53c-8.33301 16.667 -15.166 33.667 -20.499 51c-5.33301 -17.333 -11.666 -34.5 -18.999 -51.5s-16 -34.5 -26 -52.5l-156 -289c-6 -11.333 -14 -21 -24 -29
s-23.667 -12 -41 -12h-190l373 669h160z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="788" 
d="M788 -134v-160h-788v160h788z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="638" 
d="M230 1462c27.333 0 47.501 -4.5 60.501 -13.5s24.5 -22.167 34.5 -39.5l143 -243h-141c-18.667 0 -33.834 2.5 -45.501 7.5s-23.5 14.167 -35.5 27.5l-247 261h231z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="1047" 
d="M936 0l-111 -0.00195312c-23.333 0 -41.666 3.5 -54.999 10.5s-23.333 21.167 -30 42.5l-22 73c-26 -23.333 -51.5 -43.833 -76.5 -61.5s-50.833 -32.5 -77.5 -44.5s-55 -21 -85 -27s-63.333 -9 -100 -9c-43.333 0 -83.333 5.83301 -120 17.5s-68.167 29.167 -94.5 52.5
s-46.833 52.333 -61.5 87s-22 75 -22 121c0 38.667 10.167 76.834 30.5 114.501s54.166 71.667 101.499 102s110.333 55.5 189 75.5s176.334 31.333 293.001 34v60c0 68.667 -14.5 119.5 -43.5 152.5s-71.167 49.5 -126.5 49.5c-40 0 -73.333 -4.66699 -100 -14
s-49.834 -19.833 -69.501 -31.5l-54.5 -31.5c-16.667 -9.33301 -35 -14 -55 -14c-16.667 0 -31 4.33301 -43 13s-21.667 19.334 -29 32.001l-45 79c118 108 260.333 162 427 162c60 0 113.5 -9.83301 160.5 -29.5s86.833 -47 119.5 -82s57.5 -76.833 74.5 -125.5
s25.5 -102 25.5 -160v-648zM456 153.998c25.333 0 48.665 2.33203 69.998 6.99902s41.5 11.667 60.5 21s37.333 20.833 55 34.5s35.5 29.834 53.5 48.501v173c-72 -3.33301 -132.167 -9.5 -180.5 -18.5s-87.166 -20.5 -116.499 -34.5s-50.166 -30.333 -62.499 -49
s-18.5 -39 -18.5 -61c0 -43.333 12.833 -74.333 38.5 -93s59.167 -28 100.5 -28z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="1140" 
d="M135 0l-0.00195312 1486h247v-586c40.667 43.333 86.667 77.833 138 103.5s111.333 38.5 180 38.5c56 0 107.167 -11.5 153.5 -34.5s86.333 -56.5 120 -100.5s59.667 -98.333 78 -163s27.5 -139 27.5 -223c0 -76.667 -10.333 -147.667 -31 -213s-50.167 -122 -88.5 -170
s-84.666 -85.5 -138.999 -112.5s-115.166 -40.5 -182.499 -40.5c-31.333 0 -60 3.16699 -86 9.5s-49.667 15.166 -71 26.499s-41.166 25.166 -59.499 41.499s-35.833 34.5 -52.5 54.5l-11 -69c-4 -17.333 -10.833 -29.666 -20.5 -36.999s-22.834 -11 -39.501 -11h-163z
M614.998 850.001c-51.333 0 -95.167 -10.833 -131.5 -32.5s-70.166 -52.167 -101.499 -91.5v-460c28 -34.667 58.5 -58.834 91.5 -72.501s68.833 -20.5 107.5 -20.5c37.333 0 71 7 101 21s55.5 35.333 76.5 64s37.167 64.834 48.5 108.501s17 95.167 17 154.5
c0 60 -4.83301 110.833 -14.5 152.5s-23.5 75.5 -41.5 101.5s-39.833 45 -65.5 57s-54.834 18 -87.501 18z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="954" 
d="M853 809c-7.33301 -9.33301 -14.4961 -16.668 -21.4961 -22.001s-17.167 -8 -30.5 -8c-12.667 0 -25 3.83301 -37 11.5s-26.333 16.334 -43 26.001s-36.5 18.334 -59.5 26.001s-51.5 11.5 -85.5 11.5c-43.333 0 -81.333 -7.83301 -114 -23.5
s-59.834 -38.167 -81.501 -67.5s-37.834 -64.833 -48.501 -106.5s-16 -88.834 -16 -141.501c0 -54.667 5.83301 -103.334 17.5 -146.001s28.5 -78.5 50.5 -107.5s48.667 -51 80 -66s66.666 -22.5 105.999 -22.5s71.166 4.83301 95.499 14.5s44.833 20.334 61.5 32.001
s31.167 22.334 43.5 32.001s26.166 14.5 41.499 14.5c20 0 35 -7.66699 45 -23l71 -90c-27.333 -32 -57 -58.833 -89 -80.5s-65.167 -39 -99.5 -52s-69.833 -22.167 -106.5 -27.5s-73 -8 -109 -8c-63.333 0 -123 11.833 -179 35.5s-104.833 58.167 -146.5 103.5
s-74.667 100.833 -99 166.5s-36.5 140.5 -36.5 224.5c0 75.333 10.833 145.166 32.5 209.499s53.5 120 95.5 167s94 83.833 156 110.5s133.333 40 214 40c76.667 0 143.834 -12.333 201.501 -37s109.5 -60 155.5 -106z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="1140" 
d="M854 0c-32 0 -53.001 15.002 -63.001 45.002l-20 99c-21.333 -24 -43.666 -45.667 -66.999 -65s-48.5 -36 -75.5 -50s-56 -24.833 -87 -32.5s-64.5 -11.5 -100.5 -11.5c-56 0 -107.333 11.667 -154 35s-86.834 57.166 -120.501 101.499s-59.667 99.166 -78 164.499
s-27.5 140 -27.5 224c0 76 10.333 146.667 31 212s50.334 122 89.001 170s85 85.5 139 112.5s114.667 40.5 182 40.5c57.333 0 106.333 -9.16699 147 -27.5s77 -42.833 109 -73.5v542h247v-1486h-151zM524.999 181.002c51.333 0 94.999 10.668 130.999 32.001
s70 51.666 102 90.999v460c-28 34 -58.5 58 -91.5 72s-68.5 21 -106.5 21c-37.333 0 -71.166 -7 -101.499 -21s-56 -35.167 -77 -63.5s-37.167 -64.333 -48.5 -108s-17 -95.167 -17 -154.5c0 -60 4.83301 -110.833 14.5 -152.5s23.5 -75.667 41.5 -102s40 -45.333 66 -57
s55 -17.5 87 -17.5z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="1069" 
d="M556 1042c64.667 0 124.168 -10.3311 178.501 -30.998s101.166 -50.834 140.499 -90.501s70 -88.334 92 -146.001s33 -123.5 33 -197.5c0 -18.667 -0.833008 -34.167 -2.5 -46.5s-4.66699 -22 -9 -29s-10.166 -12 -17.499 -15s-16.666 -4.5 -27.999 -4.5h-634
c7.33301 -105.333 35.666 -182.666 84.999 -231.999s114.666 -74 195.999 -74c40 0 74.5 4.66699 103.5 14s54.333 19.666 76 30.999s40.667 21.666 57 30.999s32.166 14 47.499 14c10 0 18.667 -2 26 -6s13.666 -9.66699 18.999 -17l72 -90
c-27.333 -32 -58 -58.833 -92 -80.5s-69.5 -39 -106.5 -52s-74.667 -22.167 -113 -27.5s-75.5 -8 -111.5 -8c-71.333 0 -137.666 11.833 -198.999 35.5s-114.666 58.667 -159.999 105s-81 103.666 -107 171.999s-39 147.5 -39 237.5c0 70 11.333 135.833 34 197.5
s55.167 115.334 97.5 161.001s94 81.834 155 108.501s129.833 40 206.5 40zM561.001 865.002c-72 0 -128.333 -20.3301 -169 -60.9971s-66.667 -98.334 -78 -173.001h464c0 32 -4.33301 62.167 -13 90.5s-22 53.166 -40 74.499s-40.667 38.166 -68 50.499
s-59.333 18.5 -96 18.5z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="701" 
d="M176 0l0.000976562 839.999l-89 14c-19.333 3.33301 -34.833 10 -46.5 20s-17.5 24 -17.5 42v101h153v76c0 58.667 8.83301 111.334 26.5 158.001s43 86.334 76 119.001s73.167 57.667 120.5 75s100.666 26 159.999 26c47.333 0 91.333 -6.33301 132 -19l-5 -124
c-1.33301 -19.333 -10.333 -31.333 -27 -36s-36 -7 -58 -7c-29.333 0 -55.5 -3.16699 -78.5 -9.5s-42.5 -17.333 -58.5 -33s-28.167 -36.334 -36.5 -62.001s-12.5 -57.5 -12.5 -95.5v-68h267v-176h-259v-841h-247z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="1035" 
d="M487 1044c44 0 85.332 -4.5 123.999 -13.5s74 -22.167 106 -39.5h295v-92c0 -15.333 -4 -27.333 -12 -36s-21.667 -14.667 -41 -18l-92 -17c6.66699 -17.333 11.834 -35.666 15.501 -54.999s5.5 -39.666 5.5 -60.999c0 -50.667 -10.167 -96.5 -30.5 -137.5
s-48.333 -75.833 -84 -104.5s-78 -50.834 -127 -66.501s-102.167 -23.5 -159.5 -23.5c-38.667 0 -76.334 3.66699 -113.001 11c-32 -19.333 -48 -41 -48 -65c0 -20.667 9.5 -35.834 28.5 -45.501s44 -16.5 75 -20.5s66.167 -6.5 105.5 -7.5s79.666 -3.16699 120.999 -6.5
s81.666 -9.16602 120.999 -17.499s74.5 -21.5 105.5 -39.5s56 -42.5 75 -73.5s28.5 -70.833 28.5 -119.5c0 -45.333 -11.167 -89.333 -33.5 -132s-54.666 -80.667 -96.999 -114s-94.166 -60.166 -155.499 -80.499s-131.333 -30.5 -210 -30.5c-78 0 -145.667 7.5 -203 22.5
s-104.833 35 -142.5 60s-65.834 53.833 -84.501 86.5s-28 66.667 -28 102c0 48 14.667 88.333 44 121s70 58.667 122 78c-25.333 14 -45.666 32.667 -60.999 56s-23 53.666 -23 90.999c0 15.333 2.66699 31.166 8 47.499s13.5 32.5 24.5 48.5s24.833 31.167 41.5 45.5
s36.334 27.166 59.001 38.499c-52 28 -92.833 65.333 -122.5 112s-44.5 101.334 -44.5 164.001c0 50.667 10.167 96.5 30.5 137.5s48.666 76 84.999 105s79.333 51.167 129 66.5s103.834 23 162.501 23zM757.999 -46c0 20 -5.99805 36.332 -17.998 48.999
s-28.333 22.5 -49 29.5s-44.834 12.167 -72.501 15.5s-57 5.83301 -88 7.5l-96 5c-33 1.66699 -64.833 4.5 -95.5 8.5c-28 -15.333 -50.5 -33.5 -67.5 -54.5s-25.5 -45.167 -25.5 -72.5c0 -18 4.5 -34.833 13.5 -50.5s23.333 -29.167 43 -40.5s45.167 -20.166 76.5 -26.499
s69.666 -9.5 114.999 -9.5c46 0 85.667 3.5 119 10.5s60.833 16.667 82.5 29s37.5 27 47.5 44s15 35.5 15 55.5zM487.001 537.999c30.667 0 57.333 4.16504 80 12.498s41.5 19.833 56.5 34.5s26.333 32.334 34 53.001s11.5 43.334 11.5 68.001
c0 50.667 -15.167 90.834 -45.5 120.501s-75.833 44.5 -136.5 44.5s-106.167 -14.833 -136.5 -44.5s-45.5 -69.834 -45.5 -120.501c0 -24 3.83301 -46.333 11.5 -67s19 -38.5 34 -53.5s34 -26.667 57 -35s49.5 -12.5 79.5 -12.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="1137" 
d="M132 0l-0.000976562 1486h247v-571c40 38 84 68.667 132 92s104.333 35 169 35c56 0 105.667 -9.5 149 -28.5s79.5 -45.667 108.5 -80s51 -75.333 66 -123s22.5 -100.167 22.5 -157.5v-653h-247v653c0 62.667 -14.5 111.167 -43.5 145.5s-72.5 51.5 -130.5 51.5
c-42.667 0 -82.667 -9.66699 -120 -29s-72.666 -45.666 -105.999 -78.999v-742h-247z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="542" 
d="M395 1026v-1026h-247v1026h247zM432 1325c0 -21.333 -4.33301 -41.333 -13 -60s-20.167 -35 -34.5 -49s-31.166 -25.167 -50.499 -33.5s-40 -12.5 -62 -12.5c-21.333 0 -41.5 4.16699 -60.5 12.5s-35.5 19.5 -49.5 33.5s-25.167 30.333 -33.5 49s-12.5 38.667 -12.5 60
c0 22 4.16699 42.667 12.5 62s19.5 36 33.5 50s30.5 25.167 49.5 33.5s39.167 12.5 60.5 12.5c22 0 42.667 -4.16699 62 -12.5s36.166 -19.5 50.499 -33.5s25.833 -30.667 34.5 -50s13 -40 13 -62z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="538" 
d="M395 1026l0.00195312 -1061c0 -44.667 -5.83301 -86.667 -17.5 -126s-30.334 -73.833 -56.001 -103.5s-59.334 -53 -101.001 -70s-92.5 -25.5 -152.5 -25.5c-23.333 0 -45 1.5 -65 4.5s-40.333 7.83301 -61 14.5l8 133c2 13.333 7.33301 21.833 16 25.5
s25.334 5.5 50.001 5.5s45.5 2.5 62.5 7.5s30.667 13 41 24s17.666 25.5 21.999 43.5s6.5 40.333 6.5 67v1061h247zM432.002 1325c0 -21.333 -4.33301 -41.333 -13 -60s-20.167 -35 -34.5 -49s-31.166 -25.167 -50.499 -33.5s-40 -12.5 -62 -12.5
c-21.333 0 -41.5 4.16699 -60.5 12.5s-35.5 19.5 -49.5 33.5s-25.167 30.333 -33.5 49s-12.5 38.667 -12.5 60c0 22 4.16699 42.667 12.5 62s19.5 36 33.5 50s30.5 25.167 49.5 33.5s39.167 12.5 60.5 12.5c22 0 42.667 -4.16699 62 -12.5s36.166 -19.5 50.499 -33.5
s25.833 -30.667 34.5 -50s13 -40 13 -62z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="1103" 
d="M382 1486l-0.00292969 -851.001h46c16.667 0 29.667 2.33301 39 7s19.333 13.334 30 26.001l255 315c11.333 13.333 23.333 23.833 36 31.5s29.334 11.5 50.001 11.5h226l-319 -381c-11.333 -14 -23 -26.833 -35 -38.5s-25 -21.834 -39 -30.501
c14 -10 26.333 -21.667 37 -35s21.334 -27.666 32.001 -42.999l342 -498h-223c-19.333 -0 -35.666 3.33301 -48.999 10s-25.333 18 -36 34l-261 389c-10 15.333 -20 25.333 -30 30s-25 7 -45 7h-56v-470h-247v1486h247z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="542" 
d="M395 1486v-1486h-247v1486h247z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="1684" 
d="M132 0l-0.00390625 1026h151c32 0 53 -15 63 -45l16 -76c18 20 36.833 38.333 56.5 55s40.667 31 63 43s46.333 21.5 72 28.5s53.834 10.5 84.501 10.5c64.667 0 117.834 -17.5 159.501 -52.5s72.834 -81.5 93.501 -139.5c16 34 36 63.167 60 87.5
s50.333 44.166 79 59.499s59.167 26.666 91.5 33.999s64.833 11 97.5 11c56.667 0 107 -8.66699 151 -26s81 -42.666 111 -75.999s52.833 -74 68.5 -122s23.5 -103 23.5 -165v-653h-247v653c0 65.333 -14.333 114.5 -43 147.5s-70.667 49.5 -126 49.5
c-25.333 0 -48.833 -4.33301 -70.5 -13s-40.667 -21.167 -57 -37.5s-29.166 -36.833 -38.499 -61.5s-14 -53 -14 -85v-653h-248v653c0 68.667 -13.833 118.667 -41.5 150s-68.5 47 -122.5 47c-35.333 0 -68.5 -8.83301 -99.5 -26.5s-59.833 -41.834 -86.5 -72.501v-751h-247
z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="1137" 
d="M132 0l-0.000976562 1026h151c32 0 53 -15 63 -45l17 -81c20.667 21.333 42.5 40.666 65.5 57.999s47.333 32.333 73 45s53.167 22.334 82.5 29.001s61.333 10 96 10c56 0 105.667 -9.5 149 -28.5s79.5 -45.667 108.5 -80s51 -75.333 66 -123s22.5 -100.167 22.5 -157.5
v-653h-247v653c0 62.667 -14.5 111.167 -43.5 145.5s-72.5 51.5 -130.5 51.5c-42.667 0 -82.667 -9.66699 -120 -29s-72.666 -45.666 -105.999 -78.999v-742h-247z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="1137" 
d="M570 1042c76.667 0 146.167 -12.333 208.5 -37s115.5 -59.667 159.5 -105s78 -100.666 102 -165.999s36 -138.333 36 -219c0 -81.333 -12 -154.666 -36 -219.999s-58 -121 -102 -167s-97.167 -81.333 -159.5 -106s-131.833 -37 -208.5 -37s-146.334 12.333 -209.001 37
s-116.167 60 -160.5 106s-78.666 101.667 -102.999 167s-36.5 138.666 -36.5 219.999c0 80.667 12.167 153.667 36.5 219s58.666 120.666 102.999 165.999s97.833 80.333 160.5 105s132.334 37 209.001 37zM570 175c85.333 0 148.5 28.667 189.5 86s61.5 141.333 61.5 252
s-20.5 195 -61.5 253s-104.167 87 -189.5 87c-86.667 0 -150.667 -29.167 -192 -87.5s-62 -142.5 -62 -252.5s20.667 -193.833 62 -251.5s105.333 -86.5 192 -86.5z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="1131" 
d="M132 -335l-0.000976562 1361h151c16 0 29.667 -3.66699 41 -11s18.666 -18.666 21.999 -33.999l20 -95c41.333 47.333 88.833 85.666 142.5 114.999s116.5 44 188.5 44c56 0 107.167 -11.667 153.5 -35s86.333 -57.166 120 -101.499s59.667 -99 78 -164
s27.5 -139.5 27.5 -223.5c0 -76.667 -10.333 -147.667 -31 -213s-50.167 -122 -88.5 -170s-84.666 -85.5 -138.999 -112.5s-115.166 -40.5 -182.499 -40.5c-58 0 -107.167 8.83301 -147.5 26.5s-76.5 42.167 -108.5 73.5v-420h-247zM611.999 849.999
c-51.333 0 -95.166 -10.833 -131.499 -32.5s-70.166 -52.167 -101.499 -91.5v-460c28 -34.667 58.5 -58.834 91.5 -72.501s68.5 -20.5 106.5 -20.5c37.333 0 71.166 7 101.499 21s56 35.333 77 64s37.167 64.834 48.5 108.501s17 95.167 17 154.5
c0 60 -4.83301 110.833 -14.5 152.5s-23.5 75.5 -41.5 101.5s-39.833 45 -65.5 57s-54.834 18 -87.501 18z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="1140" 
d="M1005 1026l0.00195312 -1360.99h-247v464c-20.667 -22 -42.5 -41.833 -65.5 -59.5s-47.5 -32.667 -73.5 -45s-53.667 -22 -83 -29s-61 -10.5 -95 -10.5c-56 0 -107.333 11.667 -154 35s-86.834 57.166 -120.501 101.499s-59.667 99.166 -78 164.499s-27.5 140 -27.5 224
c0 76 10.333 146.667 31 212s50.334 122 89.001 170s85 85.5 139 112.5s114.667 40.5 182 40.5c32 0 61.167 -2.83301 87.5 -8.5s50.666 -13.834 72.999 -24.501s42.833 -23.334 61.5 -38.001s36.667 -31.334 54 -50.001l13 57c3.33301 15.333 10.666 26.666 21.999 33.999
s25 11 41 11h151zM525.002 181.006c51.333 0 94.999 10.668 130.999 32.001s70 51.666 102 90.999v460c-28 34 -58.5 58 -91.5 72s-68.5 21 -106.5 21c-37.333 0 -71.166 -7 -101.499 -21s-56 -35.167 -77 -63.5s-37.167 -64.333 -48.5 -108s-17 -95.167 -17 -154.5
c0 -60 4.83301 -110.833 14.5 -152.5s23.5 -75.667 41.5 -102s40 -45.333 66 -57s55 -17.5 87 -17.5z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="817" 
d="M132 0l0.000976562 1026h145c25.333 0 43 -4.66699 53 -14s16.667 -25.333 20 -48l15 -124c36.667 63.333 79.667 113.333 129 150s104.666 55 165.999 55c50.667 0 92.667 -11.667 126 -35l-32 -185c-2 -12 -6.33301 -20.5 -13 -25.5s-15.667 -7.5 -27 -7.5
c-10 0 -23.667 2.33301 -41 7s-40.333 7 -69 7c-51.333 0 -95.333 -14.167 -132 -42.5s-67.667 -69.833 -93 -124.5v-639h-247z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="874" 
d="M741 826c-6.66699 -10.667 -13.665 -18.1641 -20.998 -22.4971s-16.666 -6.5 -27.999 -6.5c-12 0 -24.833 3.33301 -38.5 10l-47.5 22.5c-18 8.33301 -38.5 15.833 -61.5 22.5s-50.167 10 -81.5 10c-48.667 0 -87 -10.333 -115 -31s-42 -47.667 -42 -81
c0 -22 7.16699 -40.5 21.5 -55.5s33.333 -28.167 57 -39.5s50.5 -21.5 80.5 -30.5s60.667 -18.833 92 -29.5s62 -22.834 92 -36.501s56.833 -31 80.5 -52s42.667 -46.167 57 -75.5s21.5 -64.666 21.5 -105.999c0 -49.333 -9 -94.833 -27 -136.5s-44.333 -77.667 -79 -108
s-77.5 -54 -128.5 -71s-109.5 -25.5 -175.5 -25.5c-35.333 0 -69.833 3.16699 -103.5 9.5s-66 15.166 -97 26.499s-59.667 24.666 -86 39.999s-49.5 32 -69.5 50l57 94c7.33301 11.333 16 20 26 26s22.667 9 38 9s29.833 -4.33301 43.5 -13s29.5 -18 47.5 -28
s39.167 -19.333 63.5 -28s55.166 -13 92.499 -13c29.333 0 54.5 3.5 75.5 10.5s38.333 16.167 52 27.5s23.667 24.5 30 39.5s9.5 30.5 9.5 46.5c0 24 -7.16699 43.667 -21.5 59s-33.333 28.666 -57 39.999s-50.667 21.5 -81 30.5s-61.333 18.833 -93 29.5
s-62.667 23.167 -93 37.5s-57.333 32.5 -81 54.5s-42.667 49 -57 81s-21.5 70.667 -21.5 116c0 42 8.33301 82 25 120s41.167 71.167 73.5 99.5s72.666 51 120.999 68s104.166 25.5 167.499 25.5c70.667 0 135 -11.667 193 -35s106.333 -54 145 -92z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="774" 
d="M469 -16c-88.667 0 -157.001 25.168 -205.001 75.501s-72 119.833 -72 208.5v573h-104c-13.333 0 -24.833 4.33301 -34.5 13s-14.5 21.667 -14.5 39v98l165 27l52 280c2.66699 13.333 8.83398 23.666 18.501 30.999s21.834 11 36.501 11h128v-323h270v-176h-270v-556
c0 -32 8 -57 24 -75s37.333 -27 64 -27c15.333 0 28.166 1.83301 38.499 5.5s19.333 7.5 27 11.5s14.5 7.83301 20.5 11.5s12 5.5 18 5.5c7.33301 0 13.333 -1.83301 18 -5.5s9.66699 -9.16699 15 -16.5l74 -120c-36 -30 -77.333 -52.667 -124 -68s-95 -23 -145 -23z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="1137" 
d="M358 1026l0.00292969 -652.001c0 -62.667 14.5 -111.167 43.5 -145.5s72.5 -51.5 130.5 -51.5c42.667 0 82.667 9.5 120 28.5s72.666 45.167 105.999 78.5v742h247v-1026h-151c-32 0 -53 15 -63 45l-17 82c-21.333 -21.333 -43.333 -40.833 -66 -58.5
s-46.834 -32.667 -72.501 -45s-53.334 -22 -83.001 -29s-61.5 -10.5 -95.5 -10.5c-56 0 -105.5 9.5 -148.5 28.5s-79.167 45.833 -108.5 80.5s-51.5 75.834 -66.5 123.501s-22.5 100.167 -22.5 157.5v652h247z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="1067" 
d="M646 0h-224l-408 1026h205c18 0 33.167 -4.33301 45.5 -13s20.833 -19.667 25.5 -33l198 -548c11.333 -32 20.833 -63.333 28.5 -94s14.5 -61.334 20.5 -92.001c6 30.667 12.833 61.334 20.5 92.001s17.5 62 29.5 94l203 548c4.66699 13.333 13 24.333 25 33
s26.333 13 43 13h195z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="1582" 
d="M7 1026l195.996 -0.00292969c18.667 0 34.334 -4.33301 47.001 -13s20.667 -19.667 24 -33l147 -548c8 -30 14.5 -59.333 19.5 -88l14.5 -86c7.33301 28.667 15.166 57.334 23.499 86.001l26.5 88l170 550c4 13.333 12 24.333 24 33s26 13 42 13h109
c18 0 33 -4.33301 45 -13s20 -19.667 24 -33l168 -560c8.66699 -28.667 16.5 -56.5 23.5 -83.5l20.5 -81.5c4.66699 28.667 9.83398 57.334 15.501 86.001s12.834 58.334 21.501 89.001l152 548c3.33301 13.333 11.333 24.333 24 33s27.334 13 44.001 13h187l-325 -1026
h-199c-21.333 0 -36.666 14.667 -45.999 44l-185 593c-6 19.333 -11.5 38.833 -16.5 58.5s-9.16699 39.167 -12.5 58.5c-4 -20 -8.33301 -39.833 -13 -59.5s-10 -39.5 -16 -59.5l-187 -591c-9.33301 -29.333 -27.333 -44 -54 -44h-189z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="1080" 
d="M375 529l-337.002 497.002h238c18 0 31.333 -2.5 40 -7.5s16.334 -13.167 23.001 -24.5l215 -343c3.33301 11.333 7.5 22.666 12.5 33.999s11.167 22.666 18.5 33.999l173 270c8 12.667 16.333 22 25 28s19.334 9 32.001 9h227l-338 -486l352 -540h-238
c-18 0 -32.5 4.66699 -43.5 14s-20.167 20 -27.5 32l-218 357c-6 -24 -14.333 -44 -25 -60l-192 -297c-7.33301 -11.333 -16.333 -21.833 -27 -31.5s-24 -14.5 -40 -14.5h-221z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="1067" 
d="M496 -282c-7.33301 -17.333 -16.8281 -30.499 -28.4951 -39.499s-29.5 -13.5 -53.5 -13.5h-184l192 411l-415 950h216c20 0 35.333 -4.66699 46 -14s18.667 -20 24 -32l219 -532c7.33301 -17.333 13.666 -35.333 18.999 -54s10 -37.334 14 -56.001
c5.33301 19.333 11.166 38 17.499 56s13.166 36.333 20.499 55l206 531c5.33301 13.333 14.166 24.333 26.499 33s26.166 13 41.499 13h198z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="939" 
d="M874 924c0 -17.333 -3.16602 -34.168 -9.49902 -50.501s-13.833 -30.166 -22.5 -41.499l-488 -642h506v-190h-793v103c0 11.333 2.66699 24.833 8 40.5s13.666 30.5 24.999 44.5l492 649h-496v189h778v-102z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="600" 
d="M149 410c0 40.667 -9.5 73.8359 -28.5 99.5029s-49.5 38.5 -91.5 38.5v143c42 0 72.5 12.833 91.5 38.5s28.5 58.834 28.5 99.501c0 31.333 -2.16699 62.5 -6.5 93.5s-9.33301 62.167 -15 93.5s-10.667 62.833 -15 94.5s-6.5 63.834 -6.5 96.501
c0 50.667 7.33301 96.834 22 138.501s37.167 77.5 67.5 107.5s68.666 53.167 114.999 69.5s100.833 24.5 163.5 24.5h53v-110c0 -8.66699 -1.83301 -16.167 -5.5 -22.5s-8.33398 -11.5 -14.001 -15.5s-11.5 -7 -17.5 -9s-11.333 -3 -16 -3h-9
c-46.667 0 -82.334 -14.667 -107.001 -44s-37 -69.666 -37 -120.999c0 -36.667 2 -71.834 6 -105.501s8.16699 -66.334 12.5 -98.001l12.5 -93.5c4 -30.667 6 -61.667 6 -93c0 -25.333 -3.5 -49.333 -10.5 -72s-17.333 -43.167 -31 -61.5s-30 -34.5 -49 -48.5
s-40.5 -24.333 -64.5 -31c24 -7.33301 45.5 -17.833 64.5 -31.5s35.333 -29.834 49 -48.501s24 -39.334 31 -62.001s10.5 -46.334 10.5 -71.001c0 -31.333 -2 -62.333 -6 -93l-12.5 -93.5c-4.33301 -31.667 -8.5 -64.334 -12.5 -98.001s-6 -68.834 -6 -105.501
c0 -50.667 12.333 -90.667 37 -120s60.334 -44 107.001 -44h9c4.66699 0 10 -1 16 -3s11.833 -5 17.5 -9s10.334 -9.33301 14.001 -16s5.5 -14.334 5.5 -23.001v-109h-53c-62.667 0 -117.167 8.16699 -163.5 24.5s-84.666 39.333 -114.999 69s-52.833 65.334 -67.5 107.001
s-22 87.834 -22 138.501c0 32.667 2.16699 64.834 6.5 96.501s9.33301 63.167 15 94.5s10.667 62.666 15 93.999s6.5 62.666 6.5 93.999z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="600" 
d="M204 1547h192v-1882h-192v1882z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="600" 
d="M451 410c0 -31.333 2.16699 -62.668 6.5 -94.001s9.33301 -62.666 15 -93.999s10.667 -62.833 15 -94.5s6.5 -63.834 6.5 -96.501c0 -50.667 -7.33301 -96.834 -22 -138.501s-37.167 -77.334 -67.5 -107.001s-68.666 -52.667 -114.999 -69s-100.833 -24.5 -163.5 -24.5
h-53v109c0 8.66699 1.83301 16.334 5.5 23.001s8.33398 12 14.001 16s11.5 7 17.5 9s11.333 3 16 3h9c46.667 0 82.334 14.667 107.001 44s37 69.333 37 120c0 36.667 -2 71.834 -6 105.501s-8.16699 66.334 -12.5 98.001l-12.5 93.5c-4 30.667 -6 61.667 -6 93
c0 24.667 3.5 48.334 10.5 71.001s17.167 43.334 30.5 62.001s29.666 34.834 48.999 48.501s41 24.167 65 31.5c-24 6.66699 -45.667 17 -65 31s-35.666 30.167 -48.999 48.5s-23.5 38.833 -30.5 61.5s-10.5 46.667 -10.5 72c0 31.333 2 62.333 6 93l12.5 93.5
c4.33301 31.667 8.5 64.334 12.5 98.001s6 68.834 6 105.501c0 51.333 -12.333 91.666 -37 120.999s-60.334 44 -107.001 44h-9c-4.66699 0 -10 1 -16 3s-11.833 5 -17.5 9s-10.334 9.16699 -14.001 15.5s-5.5 13.833 -5.5 22.5v110h53
c62.667 0 117.167 -8.16699 163.5 -24.5s84.666 -39.5 114.999 -69.5s52.833 -65.833 67.5 -107.5s22 -87.834 22 -138.501c0 -32.667 -2.16699 -64.834 -6.5 -96.501s-9.33301 -63.167 -15 -94.5s-10.667 -62.5 -15 -93.5s-6.5 -62.167 -6.5 -93.5
c0 -40.667 9.5 -73.834 28.5 -99.501s49.5 -38.5 91.5 -38.5v-143c-42 0 -72.5 -12.833 -91.5 -38.5s-28.5 -58.834 -28.5 -99.501z" />
    <glyph glyph-name="asciitilde" unicode="~" 
d="M745 648c37.333 0 66.333 12 87 36s31 57.667 31 101h199c0 -51.333 -6.83301 -97.833 -20.5 -139.5s-33.334 -77.334 -59.001 -107.001s-57.167 -52.5 -94.5 -68.5s-80 -24 -128 -24c-35.333 0 -69.166 4.66699 -101.499 14s-63 19.5 -92 30.5l-81.5 30.5
c-25.333 9.33301 -48.666 14 -69.999 14c-37.333 0 -66.333 -12 -87 -36s-31 -57.667 -31 -101h-199c0 51.333 6.83301 97.833 20.5 139.5s33.334 77.334 59.001 107.001s57.167 52.5 94.5 68.5s80 24 128 24c35.333 0 69.166 -4.66699 101.499 -14s63 -19.5 92 -30.5
l81.5 -30.5c25.333 -9.33301 48.666 -14 69.999 -14z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="386" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="721" 
d="M247 -335v533c0 60.667 3 120.167 9 178.5s14 120.166 24 185.499h167c10 -65.333 18 -127.166 24 -185.499s9 -117.833 9 -178.5v-533h-233zM209 892c0 21.333 3.83301 41.166 11.5 59.499s18.167 34.166 31.5 47.499s29.333 23.833 48 31.5s38.667 11.5 60 11.5
s41.166 -3.83301 59.499 -11.5s34.333 -18.167 48 -31.5s24.5 -29.166 32.5 -47.499s12 -38.166 12 -59.499s-4 -41.166 -12 -59.499s-18.833 -34.166 -32.5 -47.499s-29.667 -24 -48 -32s-38.166 -12 -59.499 -12s-41.333 4 -60 12s-34.667 18.667 -48 32
s-23.833 29.166 -31.5 47.499s-11.5 38.166 -11.5 59.499z" />
    <glyph glyph-name="cent" unicode="&#xa2;" 
d="M539 -8c-59.333 8.66699 -114.333 26.498 -165 53.498s-94.667 62.667 -132 107s-66.5 96.666 -87.5 156.999s-31.5 128.166 -31.5 203.499c0 72.667 11 140.5 33 203.5s54.5 117.833 97.5 164.5s96 84 159 112s135.167 43.667 216.5 47l14 157
c1.33301 15.333 8 29.5 20 42.5s27.667 19.5 47 19.5h91l-20 -229c52.667 -9.33301 100.334 -25.166 143.001 -47.499s82 -50.166 118 -83.499l-64 -87c-6.66699 -9.33301 -13.334 -16.333 -20.001 -21s-16.667 -7 -30 -7c-9.33301 0 -19 2.16699 -29 6.5
s-21.5 9.83301 -34.5 16.5s-27.5 13.334 -43.5 20.001s-34.667 12.667 -56 18l-58 -673c35.333 3.33301 64.833 9.83301 88.5 19.5s44 19.5 61 29.5l44.5 27c12.667 8 25.667 12 39 12c20 0 35.333 -7 46 -21l68 -88c-23.333 -27.333 -49 -50.666 -77 -69.999
s-57.333 -35.5 -88 -48.5s-62.667 -23 -96 -30s-67.333 -11.833 -102 -14.5l-12 -147c-1.33301 -16 -8 -30.5 -20 -43.5s-27.667 -19.5 -47 -19.5h-91zM367 512.998c0 -89.333 16.332 -161.499 48.999 -216.499s79.334 -92.167 140.001 -111.5l57 667
c-84 -10.667 -146 -45.334 -186 -104.001s-60 -137 -60 -235z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" 
d="M39 679c0 22.667 7 42.167 21 58.5s34.667 24.5 62 24.5h108v236c0 62.667 9.33301 122.167 28 178.5s47 105.666 85 147.999s85.667 75.833 143 100.5s124.666 37 201.999 37c53.333 0 101.166 -6.83301 143.499 -20.5s80 -32.167 113 -55.5s61.667 -50.833 86 -82.5
s45.166 -65.834 62.499 -102.501l-99 -63c-21.333 -10.667 -40.333 -16 -57 -16c-24.667 0 -46.667 11 -66 33c-12.667 14.667 -25.167 28.167 -37.5 40.5s-25.666 22.833 -39.999 31.5s-30 15.334 -47 20.001s-36.5 7 -58.5 7c-68.667 0 -120 -22.5 -154 -67.5
s-51 -107.5 -51 -187.5v-237h409v-98c0 -16 -6.5 -30.667 -19.5 -44s-29.833 -20 -50.5 -20h-339v-194c0 -44 -8.16699 -83.167 -24.5 -117.5s-38.833 -66.166 -67.5 -95.499c49.333 11.333 98.333 17 147 17h586v-104c0 -12 -2.5 -24.333 -7.5 -37
s-12.167 -24.167 -21.5 -34.5s-20.5 -18.666 -33.5 -24.999s-27.5 -9.5 -43.5 -9.5h-959v155c22.667 5.33301 44.334 12.833 65.001 22.5s38.834 22 54.501 37s28.167 32.667 37.5 53s14 44.166 14 71.499v261h-191v79z" />
    <glyph glyph-name="currency" unicode="&#xa4;" 
d="M211 673c0 35.333 4.5 68.8301 13.5 100.497s21.833 61.5 38.5 89.5l-153 152l125 122l150 -150c28.667 17.333 59.334 30.833 92.001 40.5s67 14.5 103 14.5c34.667 0 68 -4.5 100 -13.5s62 -21.833 90 -38.5l152 152l123 -123l-151 -151
c17.333 -28.667 30.833 -59.334 40.5 -92.001s14.5 -67 14.5 -103c0 -35.333 -4.5 -68.666 -13.5 -99.999s-21.833 -61 -38.5 -89l153 -152l-125 -123l-151 150c-28 -17.333 -58.333 -30.666 -91 -39.999s-67 -14 -103 -14c-34.667 0 -67.834 4.33301 -99.501 13
s-61.5 21.334 -89.5 38.001l-153 -152l-123 124l151 150c-17.333 28.667 -30.833 59.334 -40.5 92.001s-14.5 67 -14.5 103zM393 672.997c0 -25.333 4.83301 -49.5 14.5 -72.5s23 -43 40 -60s36.833 -30.5 59.5 -40.5s47 -15 73 -15s50.5 5 73.5 15s43 23.5 60 40.5
s30.5 37 40.5 60s15 47.167 15 72.5c0 26.667 -5 51.5 -15 74.5s-23.5 43 -40.5 60s-37 30.5 -60 40.5s-47.5 15 -73.5 15s-50.333 -5 -73 -15s-42.5 -23.5 -59.5 -40.5s-30.333 -37 -40 -60s-14.5 -47.833 -14.5 -74.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" 
d="M133 633h275.999l-398 813h205c23.333 0 42.333 -5.5 57 -16.5s26 -25.5 34 -43.5l219 -488c13.333 -30 24 -57.667 32 -83s14.667 -50.333 20 -75c5.33301 24.667 11.833 49.834 19.5 75.501s18.167 53.167 31.5 82.5l217 488c7.33301 15.333 18.333 29.166 33 41.499
s33.334 18.5 56.001 18.5h207l-399 -813h276v-138h-319v-95h319v-137h-319v-263h-247v263h-320v137h320v95h-320v138z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="600" 
d="M204 1547h192v-809h-192v809zM204 473h192v-808h-192v808z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="1010" 
d="M827 1245c-6.66699 -10.667 -13.667 -18.333 -21 -23s-16.666 -7 -27.999 -7c-12.667 0 -25.834 3.33301 -39.501 10l-47.5 22.5c-18 8.33301 -38.5 15.833 -61.5 22.5s-50.167 10 -81.5 10c-28 0 -52.667 -3.16699 -74 -9.5s-39.166 -15 -53.499 -26
s-25.333 -23.833 -33 -38.5s-11.5 -30.334 -11.5 -47.001c0 -21.333 7.83301 -40 23.5 -56s36.5 -31.167 62.5 -45.5s55.5 -28.166 88.5 -41.499l101.5 -42.5c34.667 -15 68.5 -31.5 101.5 -49.5s62.5 -38.667 88.5 -62s46.833 -50.333 62.5 -81s23.5 -66 23.5 -106
c0 -54 -12.5 -102.833 -37.5 -146.5s-65.167 -78.834 -120.5 -105.501c30 -24.667 54.5 -53.334 73.5 -86.001s28.5 -71.667 28.5 -117c0 -49.333 -8.83301 -94.833 -26.5 -136.5s-43.834 -77.667 -78.501 -108s-77.5 -54 -128.5 -71s-109.833 -25.5 -176.5 -25.5
c-35.333 0 -69.833 3.16699 -103.5 9.5s-66 15.166 -97 26.499s-59.667 24.666 -86 39.999s-49.5 32 -69.5 50l58 94c7.33301 11.333 15.833 20 25.5 26s22.167 9 37.5 9s29.833 -4.33301 43.5 -13s29.834 -18 48.501 -28s41 -19.333 67 -28s58.667 -13 98 -13
c54.667 0 97.167 11.333 127.5 34s45.5 54 45.5 94c0 26.667 -8 49.667 -24 69s-37.333 36.5 -64 51.5s-56.834 28.667 -90.501 41l-103 39.5c-35 14 -69.333 29.5 -103 46.5s-63.834 37.167 -90.501 60.5s-48 51 -64 83s-24 70 -24 114
c0 52.667 13.833 99.834 41.5 141.501s69.834 74.834 126.501 99.501c-30.667 26 -55.667 56.833 -75 92.5s-29 78.834 -29 129.501c0 42 8.16699 81.833 24.5 119.5s40.833 70.834 73.5 99.501s73 51.5 121 68.5s103.667 25.5 167 25.5c70.667 0 135.167 -11.667 193.5 -35
s106.5 -54 144.5 -92zM313 725.001c0 -28 9.66699 -52.167 29 -72.5s44.5 -38.666 75.5 -54.999s65.833 -32 104.5 -47s77 -31.167 115 -48.5c26.667 13.333 46 30.166 58 50.499s18 42.5 18 66.5c0 29.333 -9.33301 54.166 -28 74.499s-43 38.833 -73 55.5
s-63.833 32.334 -101.5 47.001s-75.167 30.667 -112.5 48c-30 -16 -51.667 -33.5 -65 -52.5s-20 -41.167 -20 -66.5z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="638" 
d="M270 1292c0 -18.667 -3.66699 -36.167 -11 -52.5s-17.5 -30.5 -30.5 -42.5s-28 -21.5 -45 -28.5s-34.833 -10.5 -53.5 -10.5c-18 0 -35.167 3.5 -51.5 10.5s-30.833 16.5 -43.5 28.5s-22.667 26.167 -30 42.5s-11 33.833 -11 52.5c0 19.333 3.66699 37.5 11 54.5
s17.333 31.833 30 44.5s27.167 22.667 43.5 30s33.5 11 51.5 11c18.667 0 36.5 -3.66699 53.5 -11s32 -17.333 45 -30s23.167 -27.5 30.5 -44.5s11 -35.167 11 -54.5zM644 1292c0 -18.667 -3.66699 -36.167 -11 -52.5s-17.333 -30.5 -30 -42.5s-27.5 -21.5 -44.5 -28.5
s-34.833 -10.5 -53.5 -10.5s-36.334 3.5 -53.001 10.5s-31.167 16.5 -43.5 28.5s-22.166 26.167 -29.499 42.5s-11 33.833 -11 52.5c0 19.333 3.66699 37.5 11 54.5s17.166 31.833 29.499 44.5s26.833 22.667 43.5 30s34.334 11 53.001 11s36.5 -3.66699 53.5 -11
s31.833 -17.333 44.5 -30s22.667 -27.5 30 -44.5s11 -35.167 11 -54.5z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="1583" 
d="M1017 512c7.33301 0 14.002 -1.49902 20.002 -4.49902s11 -7.16699 15 -12.5l82 -87c-37.333 -47.333 -84.333 -83.5 -141 -108.5s-123.334 -37.5 -200.001 -37.5c-66.667 0 -127 11.833 -181 35.5s-100.167 56.334 -138.5 98.001s-67.833 90.667 -88.5 147
s-31 117.5 -31 183.5c0 68 11.667 130.333 35 187s55.666 105.5 96.999 146.5s89.833 72.833 145.5 95.5s116.167 34 181.5 34c74.667 0 138.5 -12.333 191.5 -37s97.5 -56.667 133.5 -96l-65 -89c-4 -5.33301 -9.33301 -10.666 -16 -15.999s-15.667 -8 -27 -8
s-22 3.33301 -32 10s-22 14 -36 22s-31.667 15.333 -53 22s-49 10 -83 10c-40 0 -75.667 -6.5 -107 -19.5s-57.833 -31.667 -79.5 -56s-38.167 -53.833 -49.5 -88.5s-17 -73.667 -17 -117c0 -45.333 5.83301 -85.333 17.5 -120s27.834 -63.834 48.501 -87.501
s45.167 -41.667 73.5 -54s59.166 -18.5 92.499 -18.5c32.667 0 59.5 2.66699 80.5 8s38.833 11.666 53.5 18.999s27.667 14.666 39 21.999s24.333 13 39 17zM53.002 723.001c0 68 8.83301 133.5 26.5 196.5s42.5 121.833 74.5 176.5s70.5 104.5 115.5 149.5
s94.833 83.5 149.5 115.5s113.334 56.833 176.001 74.5s128 26.5 196 26.5s133.5 -8.83301 196.5 -26.5s121.833 -42.5 176.5 -74.5s104.5 -70.5 149.5 -115.5s83.5 -94.833 115.5 -149.5s56.833 -113.5 74.5 -176.5s26.5 -128.5 26.5 -196.5
c0 -67.333 -8.83301 -132.5 -26.5 -195.5s-42.5 -121.667 -74.5 -176s-70.5 -104 -115.5 -149s-94.833 -83.5 -149.5 -115.5s-113.5 -56.833 -176.5 -74.5s-128.5 -26.5 -196.5 -26.5s-133.333 8.83301 -196 26.5s-121.334 42.5 -176.001 74.5s-104.5 70.5 -149.5 115.5
s-83.5 94.667 -115.5 149s-56.833 112.833 -74.5 175.5s-26.5 128 -26.5 196zM188.002 723.001c0 -86.667 15.5029 -167.667 46.5029 -243s73.667 -140.833 128 -196.5s118.166 -99.5 191.499 -131.5s152.333 -48 237 -48s164 16 238 48s138.333 75.833 193 131.5
s97.667 121.167 129 196.5s47 156.333 47 243c0 58 -7.16699 113.667 -21.5 167s-34.5 103.166 -60.5 149.499s-57.5 88.333 -94.5 126s-77.833 70 -122.5 97s-93 47.833 -145 62.5s-106.333 22 -163 22s-110.834 -7.33301 -162.501 -22s-99.834 -35.5 -144.501 -62.5
s-85.167 -59.333 -121.5 -97s-67.5 -79.667 -93.5 -126s-46 -96.166 -60 -149.499s-21 -109 -21 -167z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="696" 
d="M622 841l-82.998 0.000976562c-16 0 -28.667 2.16699 -38 6.5s-16.666 14.166 -21.999 29.499l-12 36c-16 -13.333 -31.333 -25 -46 -35s-29.667 -18.333 -45 -25s-31.833 -11.667 -49.5 -15s-37.5 -5 -59.5 -5c-28 0 -53.333 3.66699 -76 11
s-42.167 18.166 -58.5 32.499s-29 32 -38 53s-13.5 45.167 -13.5 72.5c0 22 5.66699 44.5 17 67.5s31 43.833 59 62.5s65.667 34.167 113 46.5s107 19.5 179 21.5v25c0 36 -7.83301 61.5 -23.5 76.5s-38.5 22.5 -68.5 22.5c-22.667 0 -41.334 -2.33301 -56.001 -7
s-27.667 -10 -39 -16s-22 -11.333 -32 -16s-22 -7 -36 -7c-12.667 0 -23.334 3.33301 -32.001 10s-15.334 14.334 -20.001 23.001l-31 57c37.333 34 78.333 58.833 123 74.5s93 23.5 145 23.5c37.333 0 71 -6 101 -18s55.5 -28.833 76.5 -50.5s37.167 -47.167 48.5 -76.5
s17 -61.333 17 -96v-384zM323.002 954.001c25.333 0 47.667 4.49902 67 13.499s39 23.167 59 42.5v81c-39.333 -1.33301 -71.666 -4.33301 -96.999 -9s-45.5 -10.5 -60.5 -17.5s-25.5 -15 -31.5 -24s-9 -18.833 -9 -29.5c0 -21.333 6.16699 -36.166 18.5 -44.499
s30.166 -12.5 53.499 -12.5z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="972" 
d="M123 522l-0.000976562 32l256 396l81 -38c13.333 -6 23 -13.667 29 -23s9 -19.666 9 -30.999c0 -14 -4.33301 -28.333 -13 -43l-138 -235c-9.33301 -17.333 -20 -31.333 -32 -42c10.667 -9.33301 21.334 -23.333 32.001 -42l138 -236
c8.66699 -14.667 13 -29.334 13 -44.001c0 -22.667 -12.667 -40 -38 -52l-81 -38zM451.999 522l-0.000976562 32l256 396l81 -38c13.333 -6 23 -13.667 29 -23s9 -19.666 9 -30.999c0 -14 -4.33301 -28.333 -13 -43l-138 -235c-9.33301 -17.333 -20 -31.333 -32 -42
c10.667 -9.33301 21.334 -23.333 32.001 -42l138 -236c8.66699 -14.667 13 -29.334 13 -44.001c0 -22.667 -12.667 -40 -38 -52l-81 -38z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" 
d="M136 763h886v-466h-209v283h-677v183z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="718" 
d="M100 707h518v-206h-518v206z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="1583" 
d="M53 723c0 68 8.83301 133.5 26.5 196.5s42.5 121.833 74.5 176.5s70.5 104.5 115.5 149.5s94.833 83.5 149.5 115.5s113.334 56.833 176.001 74.5s128 26.5 196 26.5s133.5 -8.83301 196.5 -26.5s121.833 -42.5 176.5 -74.5s104.5 -70.5 149.5 -115.5
s83.5 -94.833 115.5 -149.5s56.833 -113.5 74.5 -176.5s26.5 -128.5 26.5 -196.5c0 -67.333 -8.83301 -132.5 -26.5 -195.5s-42.5 -121.667 -74.5 -176s-70.5 -104 -115.5 -149s-94.833 -83.5 -149.5 -115.5s-113.5 -56.833 -176.5 -74.5s-128.5 -26.5 -196.5 -26.5
s-133.333 8.83301 -196 26.5s-121.334 42.5 -176.001 74.5s-104.5 70.5 -149.5 115.5s-83.5 94.667 -115.5 149s-56.833 112.833 -74.5 175.5s-26.5 128 -26.5 196zM188 723c0 -86.667 15.5029 -167.667 46.5029 -243s73.667 -140.833 128 -196.5
s118.166 -99.5 191.499 -131.5s152.333 -48 237 -48s164 16 238 48s138.333 75.833 193 131.5s97.667 121.167 129 196.5s47 156.333 47 243c0 58 -7.16699 113.667 -21.5 167s-34.5 103.166 -60.5 149.499s-57.5 88.333 -94.5 126s-77.833 70 -122.5 97
s-93 47.833 -145 62.5s-106.333 22 -163 22s-110.834 -7.33301 -162.501 -22s-99.834 -35.5 -144.501 -62.5s-85.167 -59.333 -121.5 -97s-67.5 -79.667 -93.5 -126s-46 -96.166 -60 -149.499s-21 -109 -21 -167zM679.003 602l0.000976562 -325.001h-214v897h321
c124.667 0 216 -22.333 274 -67s87 -108.667 87 -192c0 -59.333 -15.333 -111.166 -46 -155.499s-78.334 -76.833 -143.001 -97.5c15.333 -8.66699 28 -19.5 38 -32.5s20 -28.167 30 -45.5l181 -307h-206c-30 0 -51 11 -63 33l-144 263
c-6.66699 9.33301 -14.167 16.5 -22.5 21.5s-20.833 7.5 -37.5 7.5h-55zM679.004 754.999l89.001 -0.000976562c34 0 62 2.83301 84 8.5s39.167 14.167 51.5 25.5s20.833 25.333 25.5 42s7 35.667 7 57c0 20.667 -2 38.834 -6 54.501s-11.5 28.667 -22.5 39
s-26.333 18 -46 23s-44.834 7.5 -75.501 7.5h-107v-257z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="638" 
d="M20 1372h598v-158h-598v158z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="803" 
d="M55 1123c0 47.333 8.83301 91.833 26.5 133.5s42 77.834 73 108.501s67.5 54.834 109.5 72.501s87.333 26.5 136 26.5s94.167 -8.83301 136.5 -26.5s79.166 -41.834 110.499 -72.501s56 -66.834 74 -108.501s27 -86.167 27 -133.5c0 -46 -9 -89.667 -27 -131
s-42.667 -77.5 -74 -108.5s-68.166 -55.5 -110.499 -73.5s-87.833 -27 -136.5 -27s-94 9 -136 27s-78.5 42.5 -109.5 73.5s-55.333 67.167 -73 108.5s-26.5 85 -26.5 131zM227 1121c0 -24.667 4.33301 -47.667 13 -69s20.834 -39.833 36.501 -55.5s34 -28 55 -37
s43.833 -13.5 68.5 -13.5s47.667 4.5 69 13.5s39.833 21.333 55.5 37s27.834 34.167 36.501 55.5s13 44.333 13 69c0 25.333 -4.33301 48.833 -13 70.5s-20.834 40.5 -36.501 56.5s-34.167 28.667 -55.5 38s-44.333 14 -69 14s-47.5 -4.66699 -68.5 -14s-39.333 -22 -55 -38
s-27.834 -34.833 -36.501 -56.5s-13 -45.167 -13 -70.5z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" 
d="M678 1241v-354h389v-184h-389v-341h-200v341h-387v184h387v354h200zM91 263h976v-183h-976v183z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="666" 
d="M350 1649c38.667 0 73.168 -5.66699 103.501 -17s56 -26.666 77 -45.999s36.833 -42.166 47.5 -68.499s16 -54.833 16 -85.5c0 -27.333 -4.16699 -52.333 -12.5 -75s-19.333 -44.334 -33 -65.001s-29.5 -40.667 -47.5 -60l-56 -59l-128 -130
c18.667 5.33301 37 9.5 55 12.5s34.667 4.5 50 4.5h129c18.667 0 33.167 -5 43.5 -15s15.5 -23.333 15.5 -40v-105h-541v57c0 11.333 2.16699 23.333 6.5 36s11.833 24.334 22.5 35.001l209 206c14.667 14.667 28.667 30.167 42 46.5s24.666 32.666 33.999 48.999
s16.833 32.666 22.5 48.999s8.5 32.166 8.5 47.499c0 24 -6.5 43.5 -19.5 58.5s-31.167 22.5 -54.5 22.5c-22 0 -39.667 -5.5 -53 -16.5s-24.333 -26.833 -33 -47.5c-7.33301 -12 -15.166 -21 -23.499 -27s-20.166 -9 -35.499 -9c-3.33301 0 -6.83301 0.166992 -10.5 0.5
s-7.83398 0.833008 -12.501 1.5l-99 15c11.333 76.667 41.666 133.334 90.999 170.001s111.333 55 186 55z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="666" 
d="M360 1649c38 0 71.6689 -5.50293 101.002 -16.5029s54.333 -25.5 75 -43.5s36.334 -38.5 47.001 -61.5s16 -46.833 16 -71.5c0 -42 -8.66699 -77.5 -26 -106.5s-45 -51.5 -83 -67.5c40 -13.333 70.333 -32.166 91 -56.499s31 -56.166 31 -95.499
c0 -41.333 -7.66699 -76.833 -23 -106.5s-35.333 -54.334 -60 -74.001s-52.667 -34.167 -84 -43.5s-63 -14 -95 -14c-36 0 -68.333 3.5 -97 10.5s-54.167 18.333 -76.5 34s-41.833 36.334 -58.5 62.001s-31.334 57.167 -44.001 94.5l77 31c14.667 5.33301 28 8 40 8
c24.667 0 41.667 -9.33301 51 -28c4 -7.33301 8.66699 -15 14 -23s12 -15.167 20 -21.5s17.333 -11.5 28 -15.5s23 -6 37 -6c30 0 53 8.33301 69 25s24 36.667 24 60c0 18 -2.33301 33 -7 45s-12.5 21.667 -23.5 29s-26 12.5 -45 15.5s-42.5 4.5 -70.5 4.5v116
c28 0 51.167 2.16699 69.5 6.5s32.666 10.333 42.999 18s17.666 17.167 21.999 28.5s6.5 24 6.5 38c0 24.667 -6.5 44.167 -19.5 58.5s-32.5 21.5 -58.5 21.5c-23.333 0 -41.833 -5.5 -55.5 -16.5s-24.167 -25.167 -31.5 -42.5c-6 -13.333 -13 -23.166 -21 -29.499
s-18.667 -9.5 -32 -9.5c-7.33301 0 -16 1 -26 3l-91 15c5.33301 38 15.833 71.167 31.5 99.5s35.167 51.833 58.5 70.5s50 32.5 80 41.5s62 13.5 96 13.5z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="638" 
d="M665 1462l-246.998 -261c-12.667 -13.333 -24.834 -22.5 -36.501 -27.5s-26.834 -7.5 -45.501 -7.5h-148l142 243c10 17.333 21.667 30.5 35 39.5s33.333 13.5 60 13.5h240z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="1137" 
d="M358 1026l0.000976562 -652.002c0 -62.667 14.5 -111.167 43.5 -145.5s72.5 -51.5 130.5 -51.5c42.667 0 82.667 9.5 120 28.5s72.666 45.167 105.999 78.5v742h247v-1026h-151c-32 0 -53 15 -63 45l-17 83c-20.667 -20.667 -41 -38.167 -61 -52.5s-40.333 -26 -61 -35
s-42.167 -15.5 -64.5 -19.5s-46.166 -6 -71.499 -6c-39.333 0 -75 6 -107 18s-60.333 29 -85 51c7.33301 -29.333 12.166 -59.666 14.499 -90.999s3.5 -60.666 3.5 -87.999v-240h-122c-34.667 0 -61.5 8.83301 -80.5 26.5s-28.5 43.167 -28.5 76.5v1258h247z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="1401" 
d="M1370 1446v-206.999h-214v-1448h-216v1448h-235v-1448h-216v839c-69.333 0 -132 10.833 -188 32.5s-103.833 51 -143.5 88s-70.334 80.167 -92.001 129.5s-32.5 101.666 -32.5 156.999c0 60.667 10.833 116 32.5 166s52.334 93 92.001 129s87.5 64 143.5 84
s118.667 30 188 30h881z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="559" 
d="M91 595c0 26 4.83301 50.5 14.5 73.5s23 43 40 60s36.833 30.333 59.5 40s47 14.5 73 14.5c26.667 0 51.5 -4.83301 74.5 -14.5s43 -23 60 -40s30.5 -37 40.5 -60s15 -47.5 15 -73.5s-5 -50.167 -15 -72.5s-23.5 -42 -40.5 -59s-37 -30.333 -60 -40
s-47.833 -14.5 -74.5 -14.5c-26 0 -50.333 4.83301 -73 14.5s-42.5 23 -59.5 40s-30.333 36.667 -40 59s-14.5 46.5 -14.5 72.5z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="638" 
d="M183 -227c4.66699 0 9.49902 -0.666016 14.499 -1.99902s10.667 -3 17 -5s13.333 -3.66699 21 -5s16.834 -2 27.501 -2c22 0 38.5 4.16699 49.5 12.5s16.5 18.5 16.5 30.5c0 19.333 -12 33.166 -36 41.499s-61 15.833 -111 22.5l45 148h154l-20 -69
c59.333 -14.667 101 -34.5 125 -59.5s36 -54.5 36 -88.5c0 -22 -5.83301 -42 -17.5 -60s-28.167 -33.333 -49.5 -46s-47 -22.5 -77 -29.5s-63 -10.5 -99 -10.5c-27.333 0 -52.833 2 -76.5 6s-47.167 9.66699 -70.5 17l23 76c4 15.333 13.333 23 28 23z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="666" 
d="M161 1014h139v360l5 51l-77 -61c-10.667 -8 -22 -12 -34 -12c-10 0 -19 2 -27 6s-13.667 8.66699 -17 14l-55 75l233 194h149v-627h115v-114h-431v114z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="776" 
d="M390 1464c48 0 92 -7.33301 132 -22s74.333 -35.5 103 -62.5s50.834 -60 66.501 -99s23.5 -82.833 23.5 -131.5c0 -49.333 -7.83301 -93.833 -23.5 -133.5s-37.834 -73.334 -66.501 -101.001s-63 -48.834 -103 -63.501s-84 -22 -132 -22
c-49.333 0 -94.166 7.33301 -134.499 22s-74.833 35.834 -103.5 63.501s-51 61.334 -67 101.001s-24 84.167 -24 133.5c0 48.667 8 92.5 24 131.5s38.333 72 67 99s63.167 47.833 103.5 62.5s85.166 22 134.499 22zM390 973c44.667 0 77.834 14.167 99.501 42.5
s32.5 72.166 32.5 131.499s-10.833 103 -32.5 131s-54.834 42 -99.501 42c-47.333 0 -81.833 -14 -103.5 -42s-32.5 -71.667 -32.5 -131s10.833 -103.166 32.5 -131.499s56.167 -42.5 103.5 -42.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="972" 
d="M263 126l-81.001 38.001c-13.333 6 -23 13.667 -29 23s-9 19.666 -9 30.999c0 13.333 4.33301 27.333 13 42l138 236c10.667 18.667 21.334 32.667 32.001 42c-12 10.667 -22.667 24.667 -32 42l-138 235c-8.66699 14.667 -13 29 -13 43c0 24 12.667 42 38 54l81 38
l256 -396v-32zM591.999 126.001l-81.001 38.001c-13.333 6 -23 13.667 -29 23s-9 19.666 -9 30.999c0 13.333 4.33301 27.333 13 42l138 236c10.667 18.667 21.334 32.667 32.001 42c-12 10.667 -22.667 24.667 -32 42l-138 235c-8.66699 14.667 -13 29 -13 43
c0 24 12.667 42 38 54l81 38l256 -396v-32z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="1424" 
d="M455 71c-17.333 -27.333 -35.833 -45.999 -55.5 -55.999s-42.167 -15 -67.5 -15h-106l834 1365c15.333 25.333 33.333 45.166 54 59.499s46 21.5 76 21.5h105zM1320 282.001l94 0.000976562v-88c0 -8.66699 -3 -16.334 -9 -23.001s-14.333 -10 -25 -10h-60v-161h-148v161
h-289c-16 0 -28.5 3.5 -37.5 10.5s-14.5 15.833 -16.5 26.5l-15 76l335 467h171v-459zM142 820.002h139v360l5 51l-77 -61c-10.667 -8 -22 -12 -34 -12c-10 0 -19 2 -27 6s-13.667 8.66699 -17 14l-55 75l233 194h149v-627h115v-114h-431v114zM1172 450.002
c0 15.333 0.5 32.333 1.5 51s2.83301 38 5.5 58l-202 -277h195v168z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="1424" 
d="M414 71c-17.333 -27.333 -35.833 -45.999 -55.5 -55.999s-42.167 -15 -67.5 -15h-106l834 1365c15.333 25.333 33.333 45.166 54 59.499s46 21.5 76 21.5h105zM1128 749.001c38.667 0 73.1709 -5.66699 103.504 -17s56 -26.666 77 -45.999s36.833 -42.166 47.5 -68.499
s16 -54.833 16 -85.5c0 -27.333 -4.16699 -52.333 -12.5 -75s-19.333 -44.334 -33 -65.001s-29.5 -40.667 -47.5 -60l-56 -59l-128 -130c18.667 5.33301 37 9.5 55 12.5s34.667 4.5 50 4.5h129c18.667 0 33.167 -5 43.5 -15s15.5 -23.333 15.5 -40v-105h-541v57
c0 11.333 2.16699 23.333 6.5 36s11.833 24.334 22.5 35.001l209 206c14.667 14.667 28.667 30.167 42 46.5s24.666 32.666 33.999 48.999s16.833 32.666 22.5 48.999s8.5 32.166 8.5 47.499c0 24 -6.5 43.5 -19.5 58.5s-31.167 22.5 -54.5 22.5
c-22 0 -39.333 -5.16699 -52 -15.5s-24 -26.5 -34 -48.5c-10.667 -24 -30.334 -36 -59.001 -36c-3.33301 0 -6.83301 0.166992 -10.5 0.5s-7.83398 0.833008 -12.501 1.5l-99 15c11.333 76.667 41.666 133.334 90.999 170.001s111.333 55 186 55zM142.004 820.001h139v360
l5 51l-77 -61c-10.667 -8 -22 -12 -34 -12c-10 0 -19 2 -27 6s-13.667 8.66699 -17 14l-55 75l233 194h149v-627h115v-114h-431v114z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="1425" 
d="M458 71c-17.333 -27.333 -35.833 -45.999 -55.5 -55.999s-42.167 -15 -67.5 -15h-106l834 1365c15.333 25.333 33.333 45.166 54 59.499s46 21.5 76 21.5h105zM1320 282.001l94 0.000976562v-88c0 -8.66699 -3 -16.334 -9 -23.001s-14.333 -10 -25 -10h-60v-161h-148v161
h-289c-16 0 -28.5 3.5 -37.5 10.5s-14.5 15.833 -16.5 26.5l-15 76l335 467h171v-459zM341 1455c38 0 71.6699 -5.50293 101.003 -16.5029s54.333 -25.5 75 -43.5s36.334 -38.5 47.001 -61.5s16 -46.833 16 -71.5c0 -42 -8.66699 -77.5 -26 -106.5s-45 -51.5 -83 -67.5
c40 -13.333 70.333 -32.166 91 -56.499s31 -56.166 31 -95.499c0 -41.333 -7.66699 -76.833 -23 -106.5s-35.333 -54.334 -60 -74.001s-52.667 -34.167 -84 -43.5s-63 -14 -95 -14c-36 0 -68.333 3.5 -97 10.5s-54.167 18.333 -76.5 34s-41.833 36.334 -58.5 62.001
s-31.334 57.167 -44.001 94.5l77 31c14.667 5.33301 28 8 40 8c24.667 0 41.667 -9.33301 51 -28c4 -7.33301 8.66699 -15 14 -23s12 -15.167 20 -21.5s17.333 -11.5 28 -15.5s23 -6 37 -6c30 0 53 8.33301 69 25s24 36.667 24 60c0 18 -2.33301 33 -7 45
s-12.5 21.667 -23.5 29s-26 12.5 -45 15.5s-42.5 4.5 -70.5 4.5v116c28 0 51.167 2.16699 69.5 6.5s32.666 10.333 42.999 18s17.666 17.167 21.999 28.5s6.5 24 6.5 38c0 24.667 -6.5 44.167 -19.5 58.5s-32.5 21.5 -58.5 21.5c-23.333 0 -41.833 -5.33301 -55.5 -16
s-24.167 -25 -31.5 -43c-6.66699 -13.333 -13.834 -23.166 -21.501 -29.499s-18.167 -9.5 -31.5 -9.5c-7.33301 0 -16 1 -26 3l-91 15c5.33301 38 15.833 71.167 31.5 99.5s35.167 51.833 58.5 70.5s50 32.5 80 41.5s62 13.5 96 13.5zM1172 449.999
c0 15.333 0.5 32.333 1.5 51s2.83301 38 5.5 58l-202 -277h195v168z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="841" 
d="M820 -203c-23.333 -20 -48.498 -38.8281 -75.498 -56.4951s-56.167 -33.334 -87.5 -47.001s-65.333 -24.334 -102 -32.001s-76.334 -11.5 -119.001 -11.5c-58 0 -110.833 7.66699 -158.5 23s-88.5 37.333 -122.5 66s-60.333 63.5 -79 104.5s-28 87.5 -28 139.5
c0 50.667 7.33301 93.667 22 129s33.167 65.5 55.5 90.5s46.666 46.167 72.999 63.5l74.5 49c23.333 15.333 43.5 30.833 60.5 46.5s27.167 34.167 30.5 55.5l23 145h169l17 -163c0.666992 -3.33301 1 -6.5 1 -9.5v-9.5c0 -30.667 -7.33301 -56.334 -22 -77.001
s-33 -39 -55 -55s-45.667 -31.167 -71 -45.5s-49 -30.333 -71 -48s-40.333 -38.334 -55 -62.001s-22 -52.834 -22 -87.501c0 -24 4.5 -45.5 13.5 -64.5s21.5 -35.333 37.5 -49s35 -24.167 57 -31.5s46 -11 72 -11c38 0 70.167 4.33301 96.5 13s48.666 18 66.999 28
s33.833 19.333 46.5 28s24.334 13 35.001 13c23.333 0 40.333 -10.333 51 -31zM324.002 892.005c0 20.667 3.83301 40.334 11.5 59.001s18.334 34.667 32.001 48s29.834 24 48.501 32s38.667 12 60 12c20.667 0 40.334 -4 59.001 -12s34.667 -18.667 48 -32
s24 -29.333 32 -48s12 -38.334 12 -59.001c0 -21.333 -4 -41.166 -12 -59.499s-18.667 -34.166 -32 -47.499s-29.333 -23.833 -48 -31.5s-38.334 -11.5 -59.001 -11.5c-21.333 0 -41.333 3.83301 -60 11.5s-34.834 18.167 -48.501 31.5s-24.334 29.166 -32.001 47.499
s-11.5 38.166 -11.5 59.499z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="1420" 
d="M1417 0h-207.999c-23.333 -0 -42.5 5.83301 -57.5 17.5s-25.833 26.167 -32.5 43.5l-108 295h-599l-108 -295c-5.33301 -15.333 -15.833 -29.333 -31.5 -42s-34.834 -19 -57.501 -19h-209l568 1446h275zM481.001 546h461l-176 481c-8 21.333 -16.833 46.5 -26.5 75.5
s-19.167 60.5 -28.5 94.5c-9.33301 -34 -18.5 -65.667 -27.5 -95s-17.833 -55 -26.5 -77zM568.001 1791c13.333 0 24.666 -0.499023 33.999 -1.49902s17.833 -3 25.5 -6s14.834 -7.16699 21.501 -12.5s14 -12 22 -20l197 -202h-195c-9.33301 0 -17.5 0.166992 -24.5 0.5
s-13.5 1.33301 -19.5 3s-12 4.16699 -18 7.5s-12.667 7.66602 -20 12.999l-297 218h274z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="1420" 
d="M1417 0h-207.999c-23.333 -0 -42.5 5.83301 -57.5 17.5s-25.833 26.167 -32.5 43.5l-108 295h-599l-108 -295c-5.33301 -15.333 -15.833 -29.333 -31.5 -42s-34.834 -19 -57.501 -19h-209l568 1446h275zM481.001 546h461l-176 481c-8 21.333 -16.833 46.5 -26.5 75.5
s-19.167 60.5 -28.5 94.5c-9.33301 -34 -18.5 -65.667 -27.5 -95s-17.833 -55 -26.5 -77zM1095 1791l-296 -216.001c-7.33301 -5.33301 -14.166 -9.66602 -20.499 -12.999s-12.666 -6 -18.999 -8s-13 -3.33301 -20 -4s-15.167 -1 -24.5 -1h-194l196 202
c8 8 15.5 14.667 22.5 20s14.167 9.5 21.5 12.5s15.666 5 24.999 6s20.666 1.5 33.999 1.5h275z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="1420" 
d="M1417 0h-207.999c-23.333 -0 -42.5 5.83301 -57.5 17.5s-25.833 26.167 -32.5 43.5l-108 295h-599l-108 -295c-5.33301 -15.333 -15.833 -29.333 -31.5 -42s-34.834 -19 -57.501 -19h-209l568 1446h275zM481.001 546h461l-176 481c-8 21.333 -16.833 46.5 -26.5 75.5
s-19.167 60.5 -28.5 94.5c-9.33301 -34 -18.5 -65.667 -27.5 -95s-17.833 -55 -26.5 -77zM1076 1549h-187c-10 0 -21 1.33301 -33 4s-21.667 6.66699 -29 12l-101 67c-2.66699 1.33301 -5.16699 3 -7.5 5s-4.83301 4 -7.5 6c-1.33301 -1.33301 -3.33301 -3 -6 -5
s-5.66699 -4 -9 -6l-101 -67c-7.33301 -5.33301 -17 -9.33301 -29 -12s-23 -4 -33 -4h-187l242 219h246z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="1420" 
d="M1417 0h-207.999c-23.333 -0 -42.5 5.83301 -57.5 17.5s-25.833 26.167 -32.5 43.5l-108 295h-599l-108 -295c-5.33301 -15.333 -15.833 -29.333 -31.5 -42s-34.834 -19 -57.501 -19h-209l568 1446h275zM481.001 546h461l-176 481c-8 21.333 -16.833 46.5 -26.5 75.5
s-19.167 60.5 -28.5 94.5c-9.33301 -34 -18.5 -65.667 -27.5 -95s-17.833 -55 -26.5 -77zM834.001 1699c18 0 32.5 5.16504 43.5 15.498s16.833 26.166 17.5 47.499h134c0 -33.333 -4.33301 -63.666 -13 -90.999s-21 -50.666 -37 -69.999s-35.667 -34.5 -59 -45.5
s-50 -16.5 -80 -16.5c-24 0 -47.167 3.66699 -69.5 11s-43.666 15.333 -63.999 24l-57 24c-17.667 7.33301 -33.5 11 -47.5 11c-18 0 -32.333 -5.5 -43 -16.5s-16.334 -27.167 -17.001 -48.5h-136c0 32.667 4.33301 62.834 13 90.501s21.334 51.5 38.001 71.5
s36.667 35.5 60 46.5s49.666 16.5 78.999 16.5c24 0 47.333 -3.66699 70 -11s44.167 -15.333 64.5 -24l56.5 -24c17.333 -7.33301 33 -11 47 -11z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="1420" 
d="M1417 0h-207.999c-23.333 -0 -42.5 5.83301 -57.5 17.5s-25.833 26.167 -32.5 43.5l-108 295h-599l-108 -295c-5.33301 -15.333 -15.833 -29.333 -31.5 -42s-34.834 -19 -57.501 -19h-209l568 1446h275zM481.001 546h461l-176 481c-8 21.333 -16.833 46.5 -26.5 75.5
s-19.167 60.5 -28.5 94.5c-9.33301 -34 -18.5 -65.667 -27.5 -95s-17.833 -55 -26.5 -77zM629.001 1667c0 -18 -3.66699 -35 -11 -51s-17.333 -30 -30 -42s-27.334 -21.333 -44.001 -28s-34.334 -10 -53.001 -10c-17.333 0 -33.833 3.33301 -49.5 10s-29.5 16 -41.5 28
s-21.5 26 -28.5 42s-10.5 33 -10.5 51s3.5 35.167 10.5 51.5s16.5 30.666 28.5 42.999s25.833 22.166 41.5 29.499s32.167 11 49.5 11c18.667 0 36.334 -3.66699 53.001 -11s31.334 -17.166 44.001 -29.499s22.667 -26.666 30 -42.999s11 -33.5 11 -51.5zM1061 1667
c0 -18 -3.5 -35 -10.5 -51s-16.5 -30 -28.5 -42s-26.167 -21.333 -42.5 -28s-33.833 -10 -52.5 -10c-18 0 -35.167 3.33301 -51.5 10s-30.666 16 -42.999 28s-22 26 -29 42s-10.5 33 -10.5 51s3.5 35.167 10.5 51.5s16.667 30.666 29 42.999s26.666 22.166 42.999 29.499
s33.5 11 51.5 11c18.667 0 36.167 -3.66699 52.5 -11s30.5 -17.166 42.5 -29.499s21.5 -26.666 28.5 -42.999s10.5 -33.5 10.5 -51.5z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="1420" 
d="M1417 0h-207.999c-23.333 -0 -42.5 5.83301 -57.5 17.5s-25.833 26.167 -32.5 43.5l-108 295h-599l-108 -295c-5.33301 -15.333 -15.833 -29.333 -31.5 -42s-34.834 -19 -57.501 -19h-209l568 1446h275zM481.001 546h461l-176 481c-8 21.333 -16.833 46.5 -26.5 75.5
s-19.167 60.5 -28.5 94.5c-9.33301 -34 -18.5 -65.667 -27.5 -95s-17.833 -55 -26.5 -77zM505.001 1683c0 28.667 5.66699 54.667 17 78s26.333 43.333 45 60s40.334 29.667 65.001 39s50.667 14 78 14c28.667 0 55.667 -4.66699 81 -14s47.666 -22.333 66.999 -39
s34.666 -36.667 45.999 -60s17 -49.333 17 -78c0 -28 -5.66699 -53.5 -17 -76.5s-26.666 -42.833 -45.999 -59.5s-41.666 -29.5 -66.999 -38.5s-52.333 -13.5 -81 -13.5c-27.333 0 -53.333 4.5 -78 13.5s-46.334 21.833 -65.001 38.5s-33.667 36.5 -45 59.5
s-17 48.5 -17 76.5zM623.001 1683c0 -26 7.83301 -47.333 23.5 -64s38.167 -25 67.5 -25c26.667 0 48 8.33301 64 25s24 38 24 64c0 28 -8 50 -24 66s-37.333 24 -64 24c-29.333 0 -51.833 -8 -67.5 -24s-23.5 -38 -23.5 -66z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="1868" 
d="M1780 1232h-694l50 -401h509v-207h-484l50 -409h569v-215h-797l-44 356h-520l-142 -296c-8.66699 -18 -21.667 -32.5 -39 -43.5s-38 -16.5 -62 -16.5h-204l735 1446h1073v-214zM511 546h405.001l-86 700c-10.667 -34 -21.834 -66 -33.501 -96s-23.167 -57.667 -34.5 -83z
" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="1341" 
d="M632 -227c4.66699 0 9.50098 -0.664062 14.501 -1.99707s10.667 -3 17 -5s13.333 -3.66699 21 -5s16.834 -2 27.501 -2c22 0 38.5 4.16699 49.5 12.5s16.5 18.5 16.5 30.5c0 19.333 -12 33.166 -36 41.499s-61 15.833 -111 22.5l37 123c-94 10 -178.333 35.167 -253 75.5
s-138.167 92.666 -190.5 156.999s-92.5 139 -120.5 224s-42 177.5 -42 277.5c0 108.667 18 208.167 54 298.5s86.667 168.166 152 233.499s143.5 116.166 234.5 152.499s191.5 54.5 301.5 54.5c107.333 0 201.166 -17.167 281.499 -51.5s149.166 -79.833 206.499 -136.5
l-90 -125c-5.33301 -8 -12.166 -15 -20.499 -21s-19.833 -9 -34.5 -9c-15.333 0 -31 6 -47 18s-36.333 25 -61 39s-55.834 27 -93.501 39s-85.5 18 -143.5 18c-68 0 -130.5 -11.833 -187.5 -35.5s-106 -57.5 -147 -101.5s-73 -97.5 -96 -160.5s-34.5 -133.833 -34.5 -212.5
c0 -81.333 11.5 -153.666 34.5 -216.999s54.167 -116.666 93.5 -159.999s85.666 -76.5 138.999 -99.5s110.666 -34.5 171.999 -34.5c36.667 0 69.834 2 99.501 6s57 10.333 82 19s48.667 19.834 71 33.501s44.5 30.5 66.5 50.5c6.66699 6 13.667 10.833 21 14.5
s15.333 5.5 24 5.5c14.667 0 27.334 -5.66699 38.001 -17l106 -115c-53.333 -66.667 -117.833 -118.834 -193.5 -156.501s-165.167 -59.834 -268.5 -66.501l-12 -41c59.333 -14.667 101 -34.5 125 -59.5s36 -54.5 36 -88.5c0 -22 -5.83301 -42 -17.5 -60
s-28.167 -33.333 -49.5 -46s-47 -22.5 -77 -29.5s-63 -10.5 -99 -10.5c-27.333 0 -52.833 2 -76.5 6s-47.167 9.66699 -70.5 17l23 76c4 15.333 13.333 23 28 23z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="1145" 
d="M1058 1446v-214h-641v-401h505v-207h-505v-409h641v-215h-912v1446h912zM474 1791c13.333 0 24.666 -0.499023 33.999 -1.49902s17.833 -3 25.5 -6s14.834 -7.16699 21.501 -12.5s14 -12 22 -20l197 -202h-195c-9.33301 0 -17.5 0.166992 -24.5 0.5
s-13.5 1.33301 -19.5 3s-12 4.16699 -18 7.5s-12.667 7.66602 -20 12.999l-297 218h274z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="1145" 
d="M1058 1446v-214h-641v-401h505v-207h-505v-409h641v-215h-912v1446h912zM1001 1791l-296 -216.001c-7.33301 -5.33301 -14.166 -9.66602 -20.499 -12.999s-12.666 -6 -18.999 -8s-13 -3.33301 -20 -4s-15.167 -1 -24.5 -1h-194l196 202c8 8 15.5 14.667 22.5 20
s14.167 9.5 21.5 12.5s15.666 5 24.999 6s20.666 1.5 33.999 1.5h275z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="1145" 
d="M1058 1446v-214h-641v-401h505v-207h-505v-409h641v-215h-912v1446h912zM982 1549h-187c-10 0 -21 1.33301 -33 4s-21.667 6.66699 -29 12l-101 67c-2.66699 1.33301 -5.16699 3 -7.5 5s-4.83301 4 -7.5 6c-1.33301 -1.33301 -3.33301 -3 -6 -5s-5.66699 -4 -9 -6
l-101 -67c-7.33301 -5.33301 -17 -9.33301 -29 -12s-23 -4 -33 -4h-187l242 219h246z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="1145" 
d="M1058 1446v-214h-641v-401h505v-207h-505v-409h641v-215h-912v1446h912zM535 1667c0 -18 -3.66699 -35 -11 -51s-17.333 -30 -30 -42s-27.334 -21.333 -44.001 -28s-34.334 -10 -53.001 -10c-17.333 0 -33.833 3.33301 -49.5 10s-29.5 16 -41.5 28s-21.5 26 -28.5 42
s-10.5 33 -10.5 51s3.5 35.167 10.5 51.5s16.5 30.666 28.5 42.999s25.833 22.166 41.5 29.499s32.167 11 49.5 11c18.667 0 36.334 -3.66699 53.001 -11s31.334 -17.166 44.001 -29.499s22.667 -26.666 30 -42.999s11 -33.5 11 -51.5zM967 1667c0 -18 -3.5 -35 -10.5 -51
s-16.5 -30 -28.5 -42s-26.167 -21.333 -42.5 -28s-33.833 -10 -52.5 -10c-18 0 -35.167 3.33301 -51.5 10s-30.666 16 -42.999 28s-22 26 -29 42s-10.5 33 -10.5 51s3.5 35.167 10.5 51.5s16.667 30.666 29 42.999s26.666 22.166 42.999 29.499s33.5 11 51.5 11
c18.667 0 36.167 -3.66699 52.5 -11s30.5 -17.166 42.5 -29.499s21.5 -26.666 28.5 -42.999s10.5 -33.5 10.5 -51.5z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="628" 
d="M449 0h-270v1446h270v-1446zM176 1791c13.333 0 24.666 -0.499023 33.999 -1.49902s17.833 -3 25.5 -6s14.834 -7.16699 21.501 -12.5s14 -12 22 -20l197 -202h-195c-9.33301 0 -17.5 0.166992 -24.5 0.5s-13.5 1.33301 -19.5 3s-12 4.16699 -18 7.5
s-12.667 7.66602 -20 12.999l-297 218h274z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="628" 
d="M449 0h-270v1446h270v-1446zM703 1791l-296 -216.001c-7.33301 -5.33301 -14.166 -9.66602 -20.499 -12.999s-12.666 -6 -18.999 -8s-13 -3.33301 -20 -4s-15.167 -1 -24.5 -1h-194l196 202c8 8 15.5 14.667 22.5 20s14.167 9.5 21.5 12.5s15.666 5 24.999 6
s20.666 1.5 33.999 1.5h275z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="628" 
d="M449 0h-270v1446h270v-1446zM683 1549h-187c-10 0 -21 1.33301 -33 4s-21.667 6.66699 -29 12l-101 67c-2.66699 1.33301 -5.16699 3 -7.5 5s-4.83301 4 -7.5 6c-1.33301 -1.33301 -3.33301 -3 -6 -5s-5.66699 -4 -9 -6l-101 -67c-7.33301 -5.33301 -17 -9.33301 -29 -12
s-23 -4 -33 -4h-187l242 219h246z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="628" 
d="M449 0h-270v1446h270v-1446zM236 1667c0 -18 -3.66699 -35 -11 -51s-17.333 -30 -30 -42s-27.334 -21.333 -44.001 -28s-34.334 -10 -53.001 -10c-17.333 0 -33.833 3.33301 -49.5 10s-29.5 16 -41.5 28s-21.5 26 -28.5 42s-10.5 33 -10.5 51s3.5 35.167 10.5 51.5
s16.5 30.666 28.5 42.999s25.833 22.166 41.5 29.499s32.167 11 49.5 11c18.667 0 36.334 -3.66699 53.001 -11s31.334 -17.166 44.001 -29.499s22.667 -26.666 30 -42.999s11 -33.5 11 -51.5zM668 1667c0 -18 -3.5 -35 -10.5 -51s-16.5 -30 -28.5 -42
s-26.167 -21.333 -42.5 -28s-33.833 -10 -52.5 -10c-18 0 -35.167 3.33301 -51.5 10s-30.666 16 -42.999 28s-22 26 -29 42s-10.5 33 -10.5 51s3.5 35.167 10.5 51.5s16.667 30.666 29 42.999s26.666 22.166 42.999 29.499s33.5 11 51.5 11
c18.667 0 36.167 -3.66699 52.5 -11s30.5 -17.166 42.5 -29.499s21.5 -26.666 28.5 -42.999s10.5 -33.5 10.5 -51.5z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="1581" 
d="M53 804l180 0.00195312v642h552c109.333 0 209 -17.833 299 -53.5s167 -85.334 231 -149.001s113.667 -139.834 149 -228.501s53 -186 53 -292s-17.667 -203.333 -53 -292s-85 -165 -149 -229s-141 -113.667 -231 -149s-189.667 -53 -299 -53h-552v651h-180v153z
M1241 723.002c0 79.333 -10.5 150.5 -31.5 213.5s-51.167 116.333 -90.5 160s-87.166 77.167 -143.499 100.5s-119.833 35 -190.5 35h-281v-428h361v-153h-361v-437h281c70.667 0 134.167 11.667 190.5 35s104.166 56.833 143.499 100.5s69.5 97 90.5 160
s31.5 134.167 31.5 213.5z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="1512" 
d="M287 1446c12 0 22 -0.498047 30 -1.49805s15.167 -3 21.5 -6s12.5 -7.33301 18.5 -13s12.667 -13.167 20 -22.5l759 -967c-2.66699 23.333 -4.5 46.166 -5.5 68.499s-1.5 43.166 -1.5 62.499v879h237v-1446h-139c-21.333 -0 -39 3.33301 -53 10s-27.667 18.667 -41 36
l-756 963c2 -21.333 3.5 -42.5 4.5 -63.5s1.5 -40.167 1.5 -57.5v-888h-237v1446h141v0zM895 1699c18 0 32.5 5.16504 43.5 15.498s16.833 26.166 17.5 47.499h134c0 -33.333 -4.33301 -63.666 -13 -90.999s-21 -50.666 -37 -69.999s-35.667 -34.5 -59 -45.5
s-50 -16.5 -80 -16.5c-24 0 -47.167 3.66699 -69.5 11s-43.666 15.333 -63.999 24l-57 24c-17.667 7.33301 -33.5 11 -47.5 11c-18 0 -32.333 -5.5 -43 -16.5s-16.334 -27.167 -17.001 -48.5h-136c0 32.667 4.33301 62.834 13 90.501s21.334 51.5 38.001 71.5
s36.667 35.5 60 46.5s49.666 16.5 78.999 16.5c24 0 47.333 -3.66699 70 -11s44.167 -15.333 64.5 -24l56.5 -24c17.333 -7.33301 33 -11 47 -11z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="1599" 
d="M1533 723c0 -106 -17.667 -204.167 -53 -294.5s-85 -168.5 -149 -234.5s-141 -117.5 -231 -154.5s-189.667 -55.5 -299 -55.5s-209.166 18.5 -299.499 55.5s-167.666 88.5 -231.999 154.5s-114.166 144.167 -149.499 234.5s-53 188.5 -53 294.5s17.667 204.167 53 294.5
s85.166 168.5 149.499 234.5s141.666 117.5 231.999 154.5s190.166 55.5 299.499 55.5s209 -18.667 299 -56s167 -88.833 231 -154.5s113.667 -143.667 149 -234s53 -188.5 53 -294.5zM1257 723c0 79.333 -10.5 150.5 -31.5 213.5s-51.167 116.5 -90.5 160.5
s-87.166 77.667 -143.499 101s-119.833 35 -190.5 35s-134.334 -11.667 -191.001 -35s-104.834 -57 -144.501 -101s-70.167 -97.5 -91.5 -160.5s-32 -134.167 -32 -213.5s10.667 -150.5 32 -213.5s51.833 -116.333 91.5 -160s87.834 -77.167 144.501 -100.5
s120.334 -35 191.001 -35s134.167 11.667 190.5 35s104.166 56.833 143.499 100.5s69.5 97 90.5 160s31.5 134.167 31.5 213.5zM660 1791c13.333 0 24.666 -0.499023 33.999 -1.49902s17.833 -3 25.5 -6s14.834 -7.16699 21.501 -12.5s14 -12 22 -20l197 -202h-195
c-9.33301 0 -17.5 0.166992 -24.5 0.5s-13.5 1.33301 -19.5 3s-12 4.16699 -18 7.5s-12.667 7.66602 -20 12.999l-297 218h274z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="1599" 
d="M1533 723c0 -106 -17.667 -204.167 -53 -294.5s-85 -168.5 -149 -234.5s-141 -117.5 -231 -154.5s-189.667 -55.5 -299 -55.5s-209.166 18.5 -299.499 55.5s-167.666 88.5 -231.999 154.5s-114.166 144.167 -149.499 234.5s-53 188.5 -53 294.5s17.667 204.167 53 294.5
s85.166 168.5 149.499 234.5s141.666 117.5 231.999 154.5s190.166 55.5 299.499 55.5s209 -18.667 299 -56s167 -88.833 231 -154.5s113.667 -143.667 149 -234s53 -188.5 53 -294.5zM1257 723c0 79.333 -10.5 150.5 -31.5 213.5s-51.167 116.5 -90.5 160.5
s-87.166 77.667 -143.499 101s-119.833 35 -190.5 35s-134.334 -11.667 -191.001 -35s-104.834 -57 -144.501 -101s-70.167 -97.5 -91.5 -160.5s-32 -134.167 -32 -213.5s10.667 -150.5 32 -213.5s51.833 -116.333 91.5 -160s87.834 -77.167 144.501 -100.5
s120.334 -35 191.001 -35s134.167 11.667 190.5 35s104.166 56.833 143.499 100.5s69.5 97 90.5 160s31.5 134.167 31.5 213.5zM1187 1791l-296 -216.001c-7.33301 -5.33301 -14.166 -9.66602 -20.499 -12.999s-12.666 -6 -18.999 -8s-13 -3.33301 -20 -4
s-15.167 -1 -24.5 -1h-194l196 202c8 8 15.5 14.667 22.5 20s14.167 9.5 21.5 12.5s15.666 5 24.999 6s20.666 1.5 33.999 1.5h275z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="1599" 
d="M1533 723c0 -106 -17.667 -204.167 -53 -294.5s-85 -168.5 -149 -234.5s-141 -117.5 -231 -154.5s-189.667 -55.5 -299 -55.5s-209.166 18.5 -299.499 55.5s-167.666 88.5 -231.999 154.5s-114.166 144.167 -149.499 234.5s-53 188.5 -53 294.5s17.667 204.167 53 294.5
s85.166 168.5 149.499 234.5s141.666 117.5 231.999 154.5s190.166 55.5 299.499 55.5s209 -18.667 299 -56s167 -88.833 231 -154.5s113.667 -143.667 149 -234s53 -188.5 53 -294.5zM1257 723c0 79.333 -10.5 150.5 -31.5 213.5s-51.167 116.5 -90.5 160.5
s-87.166 77.667 -143.499 101s-119.833 35 -190.5 35s-134.334 -11.667 -191.001 -35s-104.834 -57 -144.501 -101s-70.167 -97.5 -91.5 -160.5s-32 -134.167 -32 -213.5s10.667 -150.5 32 -213.5s51.833 -116.333 91.5 -160s87.834 -77.167 144.501 -100.5
s120.334 -35 191.001 -35s134.167 11.667 190.5 35s104.166 56.833 143.499 100.5s69.5 97 90.5 160s31.5 134.167 31.5 213.5zM1168 1549h-187c-10 0 -21 1.33301 -33 4s-21.667 6.66699 -29 12l-101 67c-2.66699 1.33301 -5.16699 3 -7.5 5s-4.83301 4 -7.5 6
c-1.33301 -1.33301 -3.33301 -3 -6 -5s-5.66699 -4 -9 -6l-101 -67c-7.33301 -5.33301 -17 -9.33301 -29 -12s-23 -4 -33 -4h-187l242 219h246z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="1599" 
d="M1533 723c0 -106 -17.667 -204.167 -53 -294.5s-85 -168.5 -149 -234.5s-141 -117.5 -231 -154.5s-189.667 -55.5 -299 -55.5s-209.166 18.5 -299.499 55.5s-167.666 88.5 -231.999 154.5s-114.166 144.167 -149.499 234.5s-53 188.5 -53 294.5s17.667 204.167 53 294.5
s85.166 168.5 149.499 234.5s141.666 117.5 231.999 154.5s190.166 55.5 299.499 55.5s209 -18.667 299 -56s167 -88.833 231 -154.5s113.667 -143.667 149 -234s53 -188.5 53 -294.5zM1257 723c0 79.333 -10.5 150.5 -31.5 213.5s-51.167 116.5 -90.5 160.5
s-87.166 77.667 -143.499 101s-119.833 35 -190.5 35s-134.334 -11.667 -191.001 -35s-104.834 -57 -144.501 -101s-70.167 -97.5 -91.5 -160.5s-32 -134.167 -32 -213.5s10.667 -150.5 32 -213.5s51.833 -116.333 91.5 -160s87.834 -77.167 144.501 -100.5
s120.334 -35 191.001 -35s134.167 11.667 190.5 35s104.166 56.833 143.499 100.5s69.5 97 90.5 160s31.5 134.167 31.5 213.5zM926 1699c18 0 32.5 5.16504 43.5 15.498s16.833 26.166 17.5 47.499h134c0 -33.333 -4.33301 -63.666 -13 -90.999s-21 -50.666 -37 -69.999
s-35.667 -34.5 -59 -45.5s-50 -16.5 -80 -16.5c-24 0 -47.167 3.66699 -69.5 11s-43.666 15.333 -63.999 24l-57 24c-17.667 7.33301 -33.5 11 -47.5 11c-18 0 -32.333 -5.5 -43 -16.5s-16.334 -27.167 -17.001 -48.5h-136c0 32.667 4.33301 62.834 13 90.501
s21.334 51.5 38.001 71.5s36.667 35.5 60 46.5s49.666 16.5 78.999 16.5c24 0 47.333 -3.66699 70 -11s44.167 -15.333 64.5 -24l56.5 -24c17.333 -7.33301 33 -11 47 -11z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="1599" 
d="M1533 723c0 -106 -17.667 -204.167 -53 -294.5s-85 -168.5 -149 -234.5s-141 -117.5 -231 -154.5s-189.667 -55.5 -299 -55.5s-209.166 18.5 -299.499 55.5s-167.666 88.5 -231.999 154.5s-114.166 144.167 -149.499 234.5s-53 188.5 -53 294.5s17.667 204.167 53 294.5
s85.166 168.5 149.499 234.5s141.666 117.5 231.999 154.5s190.166 55.5 299.499 55.5s209 -18.667 299 -56s167 -88.833 231 -154.5s113.667 -143.667 149 -234s53 -188.5 53 -294.5zM1257 723c0 79.333 -10.5 150.5 -31.5 213.5s-51.167 116.5 -90.5 160.5
s-87.166 77.667 -143.499 101s-119.833 35 -190.5 35s-134.334 -11.667 -191.001 -35s-104.834 -57 -144.501 -101s-70.167 -97.5 -91.5 -160.5s-32 -134.167 -32 -213.5s10.667 -150.5 32 -213.5s51.833 -116.333 91.5 -160s87.834 -77.167 144.501 -100.5
s120.334 -35 191.001 -35s134.167 11.667 190.5 35s104.166 56.833 143.499 100.5s69.5 97 90.5 160s31.5 134.167 31.5 213.5zM721 1667c0 -18 -3.66699 -35 -11 -51s-17.333 -30 -30 -42s-27.334 -21.333 -44.001 -28s-34.334 -10 -53.001 -10
c-17.333 0 -33.833 3.33301 -49.5 10s-29.5 16 -41.5 28s-21.5 26 -28.5 42s-10.5 33 -10.5 51s3.5 35.167 10.5 51.5s16.5 30.666 28.5 42.999s25.833 22.166 41.5 29.499s32.167 11 49.5 11c18.667 0 36.334 -3.66699 53.001 -11s31.334 -17.166 44.001 -29.499
s22.667 -26.666 30 -42.999s11 -33.5 11 -51.5zM1153 1667c0 -18 -3.5 -35 -10.5 -51s-16.5 -30 -28.5 -42s-26.167 -21.333 -42.5 -28s-33.833 -10 -52.5 -10c-18 0 -35.167 3.33301 -51.5 10s-30.666 16 -42.999 28s-22 26 -29 42s-10.5 33 -10.5 51s3.5 35.167 10.5 51.5
s16.667 30.666 29 42.999s26.666 22.166 42.999 29.499s33.5 11 51.5 11c18.667 0 36.167 -3.66699 52.5 -11s30.5 -17.166 42.5 -29.499s21.5 -26.666 28.5 -42.999s10.5 -33.5 10.5 -51.5z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" 
d="M1033 996l-325 -325l341 -340l-131 -129l-340 339l-341 -341l-131 129l342 342l-327 327l130 130l327 -327l324 325z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="1599" 
d="M1533 723c0 -106 -17.6631 -204.167 -52.9961 -294.5s-85 -168.5 -149 -234.5s-141 -117.5 -231 -154.5s-189.667 -55.5 -299 -55.5c-67.333 0 -130.666 7 -189.999 21s-115 34.333 -167 61l-76 -105c-19.333 -26.667 -42.5 -45.334 -69.5 -56.001s-53.167 -16 -78.5 -16
h-106l195 268c-76.667 66.667 -136.167 148.334 -178.5 245.001s-63.5 203.667 -63.5 321c0 106 17.667 204.167 53 294.5s85.166 168.5 149.499 234.5s141.666 117.5 231.999 154.5s190.166 55.5 299.499 55.5c73.333 0 142.166 -8.33301 206.499 -25
s123.833 -41 178.5 -73l59 82c8.66699 12 16.667 22.333 24 31s15.166 15.5 23.499 20.5s17.5 8.66699 27.5 11s22.333 3.5 37 3.5h138l-179 -246c69.333 -66.667 122.833 -146.167 160.5 -238.5s56.5 -193.833 56.5 -304.5zM342.004 723
c0 -76.667 9.66895 -145.333 29.002 -206s47.666 -113 84.999 -157l589 810c-69.333 42 -150.666 63 -243.999 63c-70.667 0 -134.334 -11.667 -191.001 -35s-104.834 -57 -144.501 -101s-70.167 -97.5 -91.5 -160.5s-32 -134.167 -32 -213.5zM1257.01 723.001
c0 68.667 -7.83203 131 -23.499 187s-38.5 105.333 -68.5 148l-580 -798c63.333 -30.667 135.333 -46 216 -46c70.667 0 134.167 11.667 190.5 35s104.166 56.833 143.499 100.5s69.5 97 90.5 160s31.5 134.167 31.5 213.5z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="1456" 
d="M728 217c52 0 98.501 8.66895 139.501 26.002s75.667 41.666 104 72.999s50 69.333 65 114s22.5 94.667 22.5 150v866h269v-866c0 -86 -13.833 -165.5 -41.5 -238.5s-67.5 -136 -119.5 -189s-115 -94.333 -189 -124s-157.333 -44.5 -250 -44.5s-176 14.833 -250 44.5
s-136.833 71 -188.5 124s-91.334 116 -119.001 189s-41.5 152.5 -41.5 238.5v866h269v-865c0 -55.333 7.5 -105.333 22.5 -150s36.5 -82.834 64.5 -114.501s62.5 -56.167 103.5 -73.5s87.5 -26 139.5 -26zM586.001 1791c13.333 0 24.666 -0.499023 33.999 -1.49902
s17.833 -3 25.5 -6s14.834 -7.16699 21.501 -12.5s14 -12 22 -20l197 -202h-195c-9.33301 0 -17.5 0.166992 -24.5 0.5s-13.5 1.33301 -19.5 3s-12 4.16699 -18 7.5s-12.667 7.66602 -20 12.999l-297 218h274z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="1456" 
d="M728 217c52 0 98.501 8.66895 139.501 26.002s75.667 41.666 104 72.999s50 69.333 65 114s22.5 94.667 22.5 150v866h269v-866c0 -86 -13.833 -165.5 -41.5 -238.5s-67.5 -136 -119.5 -189s-115 -94.333 -189 -124s-157.333 -44.5 -250 -44.5s-176 14.833 -250 44.5
s-136.833 71 -188.5 124s-91.334 116 -119.001 189s-41.5 152.5 -41.5 238.5v866h269v-865c0 -55.333 7.5 -105.333 22.5 -150s36.5 -82.834 64.5 -114.501s62.5 -56.167 103.5 -73.5s87.5 -26 139.5 -26zM1113 1791l-296 -216.001
c-7.33301 -5.33301 -14.166 -9.66602 -20.499 -12.999s-12.666 -6 -18.999 -8s-13 -3.33301 -20 -4s-15.167 -1 -24.5 -1h-194l196 202c8 8 15.5 14.667 22.5 20s14.167 9.5 21.5 12.5s15.666 5 24.999 6s20.666 1.5 33.999 1.5h275z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="1456" 
d="M728 217c52 0 98.501 8.66895 139.501 26.002s75.667 41.666 104 72.999s50 69.333 65 114s22.5 94.667 22.5 150v866h269v-866c0 -86 -13.833 -165.5 -41.5 -238.5s-67.5 -136 -119.5 -189s-115 -94.333 -189 -124s-157.333 -44.5 -250 -44.5s-176 14.833 -250 44.5
s-136.833 71 -188.5 124s-91.334 116 -119.001 189s-41.5 152.5 -41.5 238.5v866h269v-865c0 -55.333 7.5 -105.333 22.5 -150s36.5 -82.834 64.5 -114.501s62.5 -56.167 103.5 -73.5s87.5 -26 139.5 -26zM1093 1549h-187c-10 0 -21 1.33301 -33 4s-21.667 6.66699 -29 12
l-101 67c-2.66699 1.33301 -5.16699 3 -7.5 5s-4.83301 4 -7.5 6c-1.33301 -1.33301 -3.33301 -3 -6 -5s-5.66699 -4 -9 -6l-101 -67c-7.33301 -5.33301 -17 -9.33301 -29 -12s-23 -4 -33 -4h-187l242 219h246z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="1456" 
d="M728 217c52 0 98.501 8.66895 139.501 26.002s75.667 41.666 104 72.999s50 69.333 65 114s22.5 94.667 22.5 150v866h269v-866c0 -86 -13.833 -165.5 -41.5 -238.5s-67.5 -136 -119.5 -189s-115 -94.333 -189 -124s-157.333 -44.5 -250 -44.5s-176 14.833 -250 44.5
s-136.833 71 -188.5 124s-91.334 116 -119.001 189s-41.5 152.5 -41.5 238.5v866h269v-865c0 -55.333 7.5 -105.333 22.5 -150s36.5 -82.834 64.5 -114.501s62.5 -56.167 103.5 -73.5s87.5 -26 139.5 -26zM646.001 1667c0 -18 -3.66699 -35 -11 -51s-17.333 -30 -30 -42
s-27.334 -21.333 -44.001 -28s-34.334 -10 -53.001 -10c-17.333 0 -33.833 3.33301 -49.5 10s-29.5 16 -41.5 28s-21.5 26 -28.5 42s-10.5 33 -10.5 51s3.5 35.167 10.5 51.5s16.5 30.666 28.5 42.999s25.833 22.166 41.5 29.499s32.167 11 49.5 11
c18.667 0 36.334 -3.66699 53.001 -11s31.334 -17.166 44.001 -29.499s22.667 -26.666 30 -42.999s11 -33.5 11 -51.5zM1078 1667c0 -18 -3.5 -35 -10.5 -51s-16.5 -30 -28.5 -42s-26.167 -21.333 -42.5 -28s-33.833 -10 -52.5 -10c-18 0 -35.167 3.33301 -51.5 10
s-30.666 16 -42.999 28s-22 26 -29 42s-10.5 33 -10.5 51s3.5 35.167 10.5 51.5s16.667 30.666 29 42.999s26.666 22.166 42.999 29.499s33.5 11 51.5 11c18.667 0 36.167 -3.66699 52.5 -11s30.5 -17.166 42.5 -29.499s21.5 -26.666 28.5 -42.999s10.5 -33.5 10.5 -51.5z
" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="1309" 
d="M789 562l0.000976562 -562h-269v562l-527 884h237c23.333 0 41.833 -5.66699 55.5 -17s25.167 -25.666 34.5 -42.999l265 -483c15.333 -28.667 28.666 -55.834 39.999 -81.501s21.666 -50.834 30.999 -75.501c8.66699 25.333 18.5 50.833 29.5 76.5
s24.167 52.5 39.5 80.5l263 483c7.33301 14.667 18.333 28.334 33 41.001s33.334 19 56.001 19h238zM1041 1791l-296 -216.001c-7.33301 -5.33301 -14.166 -9.66602 -20.499 -12.999s-12.666 -6 -18.999 -8s-13 -3.33301 -20 -4s-15.167 -1 -24.5 -1h-194l196 202
c8 8 15.5 14.667 22.5 20s14.167 9.5 21.5 12.5s15.666 5 24.999 6s20.666 1.5 33.999 1.5h275z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="1250" 
d="M424 261l-0.000976562 -260.998h-269v1446h269v-248h202c96.667 0 180.334 -11.333 251.001 -34s129 -54.334 175 -95.001s80 -89.334 102 -146.001s33 -118.667 33 -186c0 -70 -11.667 -134.167 -35 -192.5s-58.333 -108.5 -105 -150.5s-105 -74.667 -175 -98
s-152 -35 -246 -35h-202zM423.999 471.002h202c49.333 0 92.333 6.16699 129 18.5s67 30 91 53s42 51 54 84s18 69.833 18 110.5c0 38.667 -6 73.667 -18 105s-30 58 -54 80s-54.333 38.833 -91 50.5s-79.667 17.5 -129 17.5h-202v-519z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="1270" 
d="M700 1471c78 0 144.668 -11.3291 200.001 -33.9961s100.333 -51 135 -85s60 -70.667 76 -110s24 -76 24 -110c0 -38.667 -6.5 -71.667 -19.5 -99s-29.333 -51.333 -49 -72s-40.834 -38.667 -63.501 -54l-63.5 -44c-19.667 -14 -36 -28.5 -49 -43.5
s-19.5 -32.167 -19.5 -51.5c0 -22 8.33301 -41 25 -57s37.334 -31.667 62.001 -47l81 -49c29.333 -17.333 56.333 -38.5 81 -63.5s45.334 -55.167 62.001 -90.5s25 -78.666 25 -129.999c0 -56.667 -10.5 -106.5 -31.5 -149.5s-49.5 -79.167 -85.5 -108.5
s-77.833 -51.5 -125.5 -66.5s-98.167 -22.5 -151.5 -22.5c-30.667 0 -61 3.16699 -91 9.5s-58.833 15.166 -86.5 26.499s-53.834 24.666 -78.501 39.999s-47 32 -67 50l58 94c6.66699 11.333 15 20 25 26s22.667 9 38 9s30 -4.33301 44 -13l46 -28
c16.667 -10 35.667 -19.333 57 -28s47 -13 77 -13c42 0 76 12.5 102 37.5s39 57.5 39 97.5c0 31.333 -9 57.333 -27 78s-40.5 39.167 -67.5 55.5s-56.167 32.333 -87.5 48s-60.5 34 -87.5 55s-49.5 46.5 -67.5 76.5s-27 68 -27 114c0 38 7 70.667 21 98
s31.5 51.666 52.5 72.999s43.833 40.5 68.5 57.5s47.5 34.5 68.5 52.5s38.5 37.333 52.5 58s21 45 21 73c0 22 -3.83301 42.833 -11.5 62.5s-19.834 36.667 -36.501 51s-38.167 25.833 -64.5 34.5s-57.833 13 -94.5 13c-87.333 0 -153.5 -26.833 -198.5 -80.5
s-67.5 -132.167 -67.5 -235.5v-976h-247v984c0 70.667 12.167 135.667 36.5 195s59 110.666 104 153.999s99.833 77.166 164.5 101.499s137.667 36.5 219 36.5z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="1047" 
d="M936 0l-111 -0.00195312c-23.333 0 -41.666 3.5 -54.999 10.5s-23.333 21.167 -30 42.5l-22 73c-26 -23.333 -51.5 -43.833 -76.5 -61.5s-50.833 -32.5 -77.5 -44.5s-55 -21 -85 -27s-63.333 -9 -100 -9c-43.333 0 -83.333 5.83301 -120 17.5s-68.167 29.167 -94.5 52.5
s-46.833 52.333 -61.5 87s-22 75 -22 121c0 38.667 10.167 76.834 30.5 114.501s54.166 71.667 101.499 102s110.333 55.5 189 75.5s176.334 31.333 293.001 34v60c0 68.667 -14.5 119.5 -43.5 152.5s-71.167 49.5 -126.5 49.5c-40 0 -73.333 -4.66699 -100 -14
s-49.834 -19.833 -69.501 -31.5l-54.5 -31.5c-16.667 -9.33301 -35 -14 -55 -14c-16.667 0 -31 4.33301 -43 13s-21.667 19.334 -29 32.001l-45 79c118 108 260.333 162 427 162c60 0 113.5 -9.83301 160.5 -29.5s86.833 -47 119.5 -82s57.5 -76.833 74.5 -125.5
s25.5 -102 25.5 -160v-648zM456 153.998c25.333 0 48.665 2.33203 69.998 6.99902s41.5 11.667 60.5 21s37.333 20.833 55 34.5s35.5 29.834 53.5 48.501v173c-72 -3.33301 -132.167 -9.5 -180.5 -18.5s-87.166 -20.5 -116.499 -34.5s-50.166 -30.333 -62.499 -49
s-18.5 -39 -18.5 -61c0 -43.333 12.833 -74.333 38.5 -93s59.167 -28 100.5 -28zM456.999 1462c27.333 0 47.501 -4.5 60.501 -13.5s24.5 -22.167 34.5 -39.5l143 -243h-141c-18.667 0 -33.834 2.5 -45.501 7.5s-23.5 14.167 -35.5 27.5l-247 261h231z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="1047" 
d="M936 0l-111 -0.00195312c-23.333 0 -41.666 3.5 -54.999 10.5s-23.333 21.167 -30 42.5l-22 73c-26 -23.333 -51.5 -43.833 -76.5 -61.5s-50.833 -32.5 -77.5 -44.5s-55 -21 -85 -27s-63.333 -9 -100 -9c-43.333 0 -83.333 5.83301 -120 17.5s-68.167 29.167 -94.5 52.5
s-46.833 52.333 -61.5 87s-22 75 -22 121c0 38.667 10.167 76.834 30.5 114.501s54.166 71.667 101.499 102s110.333 55.5 189 75.5s176.334 31.333 293.001 34v60c0 68.667 -14.5 119.5 -43.5 152.5s-71.167 49.5 -126.5 49.5c-40 0 -73.333 -4.66699 -100 -14
s-49.834 -19.833 -69.501 -31.5l-54.5 -31.5c-16.667 -9.33301 -35 -14 -55 -14c-16.667 0 -31 4.33301 -43 13s-21.667 19.334 -29 32.001l-45 79c118 108 260.333 162 427 162c60 0 113.5 -9.83301 160.5 -29.5s86.833 -47 119.5 -82s57.5 -76.833 74.5 -125.5
s25.5 -102 25.5 -160v-648zM456 153.998c25.333 0 48.665 2.33203 69.998 6.99902s41.5 11.667 60.5 21s37.333 20.833 55 34.5s35.5 29.834 53.5 48.501v173c-72 -3.33301 -132.167 -9.5 -180.5 -18.5s-87.166 -20.5 -116.499 -34.5s-50.166 -30.333 -62.499 -49
s-18.5 -39 -18.5 -61c0 -43.333 12.833 -74.333 38.5 -93s59.167 -28 100.5 -28zM891.999 1462l-246.998 -261c-12.667 -13.333 -24.834 -22.5 -36.501 -27.5s-26.834 -7.5 -45.501 -7.5h-148l142 243c10 17.333 21.667 30.5 35 39.5s33.333 13.5 60 13.5h240z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="1047" 
d="M936 0l-111 -0.00195312c-23.333 0 -41.666 3.5 -54.999 10.5s-23.333 21.167 -30 42.5l-22 73c-26 -23.333 -51.5 -43.833 -76.5 -61.5s-50.833 -32.5 -77.5 -44.5s-55 -21 -85 -27s-63.333 -9 -100 -9c-43.333 0 -83.333 5.83301 -120 17.5s-68.167 29.167 -94.5 52.5
s-46.833 52.333 -61.5 87s-22 75 -22 121c0 38.667 10.167 76.834 30.5 114.501s54.166 71.667 101.499 102s110.333 55.5 189 75.5s176.334 31.333 293.001 34v60c0 68.667 -14.5 119.5 -43.5 152.5s-71.167 49.5 -126.5 49.5c-40 0 -73.333 -4.66699 -100 -14
s-49.834 -19.833 -69.501 -31.5l-54.5 -31.5c-16.667 -9.33301 -35 -14 -55 -14c-16.667 0 -31 4.33301 -43 13s-21.667 19.334 -29 32.001l-45 79c118 108 260.333 162 427 162c60 0 113.5 -9.83301 160.5 -29.5s86.833 -47 119.5 -82s57.5 -76.833 74.5 -125.5
s25.5 -102 25.5 -160v-648zM456 153.998c25.333 0 48.665 2.33203 69.998 6.99902s41.5 11.667 60.5 21s37.333 20.833 55 34.5s35.5 29.834 53.5 48.501v173c-72 -3.33301 -132.167 -9.5 -180.5 -18.5s-87.166 -20.5 -116.499 -34.5s-50.166 -30.333 -62.499 -49
s-18.5 -39 -18.5 -61c0 -43.333 12.833 -74.333 38.5 -93s59.167 -28 100.5 -28zM890.999 1168l-164.999 0.000976562c-20.667 0 -37.334 6 -50.001 18l-105 95l-10.5 10c-3.66699 3.33301 -7.16699 7.33301 -10.5 12c-3.33301 -4.66699 -6.83301 -8.66699 -10.5 -12
l-10.5 -10l-107 -95c-5.33301 -4.66699 -12.5 -8.83398 -21.5 -12.501s-18.5 -5.5 -28.5 -5.5h-171l229 278h232z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="1047" 
d="M936 0l-111 -0.00195312c-23.333 0 -41.666 3.5 -54.999 10.5s-23.333 21.167 -30 42.5l-22 73c-26 -23.333 -51.5 -43.833 -76.5 -61.5s-50.833 -32.5 -77.5 -44.5s-55 -21 -85 -27s-63.333 -9 -100 -9c-43.333 0 -83.333 5.83301 -120 17.5s-68.167 29.167 -94.5 52.5
s-46.833 52.333 -61.5 87s-22 75 -22 121c0 38.667 10.167 76.834 30.5 114.501s54.166 71.667 101.499 102s110.333 55.5 189 75.5s176.334 31.333 293.001 34v60c0 68.667 -14.5 119.5 -43.5 152.5s-71.167 49.5 -126.5 49.5c-40 0 -73.333 -4.66699 -100 -14
s-49.834 -19.833 -69.501 -31.5l-54.5 -31.5c-16.667 -9.33301 -35 -14 -55 -14c-16.667 0 -31 4.33301 -43 13s-21.667 19.334 -29 32.001l-45 79c118 108 260.333 162 427 162c60 0 113.5 -9.83301 160.5 -29.5s86.833 -47 119.5 -82s57.5 -76.833 74.5 -125.5
s25.5 -102 25.5 -160v-648zM456 153.998c25.333 0 48.665 2.33203 69.998 6.99902s41.5 11.667 60.5 21s37.333 20.833 55 34.5s35.5 29.834 53.5 48.501v173c-72 -3.33301 -132.167 -9.5 -180.5 -18.5s-87.166 -20.5 -116.499 -34.5s-50.166 -30.333 -62.499 -49
s-18.5 -39 -18.5 -61c0 -43.333 12.833 -74.333 38.5 -93s59.167 -28 100.5 -28zM652.999 1359c20 0 35.668 5.50098 47.001 16.501s17 30.833 17 59.5h150c0 -37.333 -5.16699 -71.166 -15.5 -101.499s-24.5 -56.333 -42.5 -78s-39.5 -38.334 -64.5 -50.001
s-52.5 -17.5 -82.5 -17.5c-24 0 -46.333 4.33301 -67 13s-40 18 -58 28l-50.5 28c-15.667 8.66699 -30.167 13 -43.5 13c-19.333 0 -34.5 -5.83301 -45.5 -17.5s-16.5 -31.5 -16.5 -59.5h-153c0 37.333 5.16699 71.166 15.5 101.499s24.833 56.333 43.5 78
s40.5 38.5 65.5 50.5s52.167 18 81.5 18c24 0 46.5 -4.33301 67.5 -13s40.5 -18 58.5 -28l50 -28c15.333 -8.66699 29.666 -13 42.999 -13z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="1047" 
d="M936 0l-111 -0.00195312c-23.333 0 -41.666 3.5 -54.999 10.5s-23.333 21.167 -30 42.5l-22 73c-26 -23.333 -51.5 -43.833 -76.5 -61.5s-50.833 -32.5 -77.5 -44.5s-55 -21 -85 -27s-63.333 -9 -100 -9c-43.333 0 -83.333 5.83301 -120 17.5s-68.167 29.167 -94.5 52.5
s-46.833 52.333 -61.5 87s-22 75 -22 121c0 38.667 10.167 76.834 30.5 114.501s54.166 71.667 101.499 102s110.333 55.5 189 75.5s176.334 31.333 293.001 34v60c0 68.667 -14.5 119.5 -43.5 152.5s-71.167 49.5 -126.5 49.5c-40 0 -73.333 -4.66699 -100 -14
s-49.834 -19.833 -69.501 -31.5l-54.5 -31.5c-16.667 -9.33301 -35 -14 -55 -14c-16.667 0 -31 4.33301 -43 13s-21.667 19.334 -29 32.001l-45 79c118 108 260.333 162 427 162c60 0 113.5 -9.83301 160.5 -29.5s86.833 -47 119.5 -82s57.5 -76.833 74.5 -125.5
s25.5 -102 25.5 -160v-648zM456 153.998c25.333 0 48.665 2.33203 69.998 6.99902s41.5 11.667 60.5 21s37.333 20.833 55 34.5s35.5 29.834 53.5 48.501v173c-72 -3.33301 -132.167 -9.5 -180.5 -18.5s-87.166 -20.5 -116.499 -34.5s-50.166 -30.333 -62.499 -49
s-18.5 -39 -18.5 -61c0 -43.333 12.833 -74.333 38.5 -93s59.167 -28 100.5 -28zM496.999 1292c0 -18.667 -3.66699 -36.167 -11 -52.5s-17.5 -30.5 -30.5 -42.5s-28 -21.5 -45 -28.5s-34.833 -10.5 -53.5 -10.5c-18 0 -35.167 3.5 -51.5 10.5s-30.833 16.5 -43.5 28.5
s-22.667 26.167 -30 42.5s-11 33.833 -11 52.5c0 19.333 3.66699 37.5 11 54.5s17.333 31.833 30 44.5s27.167 22.667 43.5 30s33.5 11 51.5 11c18.667 0 36.5 -3.66699 53.5 -11s32 -17.333 45 -30s23.167 -27.5 30.5 -44.5s11 -35.167 11 -54.5zM870.999 1292
c0 -18.667 -3.66699 -36.167 -11 -52.5s-17.333 -30.5 -30 -42.5s-27.5 -21.5 -44.5 -28.5s-34.833 -10.5 -53.5 -10.5s-36.334 3.5 -53.001 10.5s-31.167 16.5 -43.5 28.5s-22.166 26.167 -29.499 42.5s-11 33.833 -11 52.5c0 19.333 3.66699 37.5 11 54.5
s17.166 31.833 29.499 44.5s26.833 22.667 43.5 30s34.334 11 53.001 11s36.5 -3.66699 53.5 -11s31.833 -17.333 44.5 -30s22.667 -27.5 30 -44.5s11 -35.167 11 -54.5z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="1047" 
d="M936 0l-111 -0.00195312c-23.333 0 -41.666 3.5 -54.999 10.5s-23.333 21.167 -30 42.5l-22 73c-26 -23.333 -51.5 -43.833 -76.5 -61.5s-50.833 -32.5 -77.5 -44.5s-55 -21 -85 -27s-63.333 -9 -100 -9c-43.333 0 -83.333 5.83301 -120 17.5s-68.167 29.167 -94.5 52.5
s-46.833 52.333 -61.5 87s-22 75 -22 121c0 38.667 10.167 76.834 30.5 114.501s54.166 71.667 101.499 102s110.333 55.5 189 75.5s176.334 31.333 293.001 34v60c0 68.667 -14.5 119.5 -43.5 152.5s-71.167 49.5 -126.5 49.5c-40 0 -73.333 -4.66699 -100 -14
s-49.834 -19.833 -69.501 -31.5l-54.5 -31.5c-16.667 -9.33301 -35 -14 -55 -14c-16.667 0 -31 4.33301 -43 13s-21.667 19.334 -29 32.001l-45 79c118 108 260.333 162 427 162c60 0 113.5 -9.83301 160.5 -29.5s86.833 -47 119.5 -82s57.5 -76.833 74.5 -125.5
s25.5 -102 25.5 -160v-648zM456 153.998c25.333 0 48.665 2.33203 69.998 6.99902s41.5 11.667 60.5 21s37.333 20.833 55 34.5s35.5 29.834 53.5 48.501v173c-72 -3.33301 -132.167 -9.5 -180.5 -18.5s-87.166 -20.5 -116.499 -34.5s-50.166 -30.333 -62.499 -49
s-18.5 -39 -18.5 -61c0 -43.333 12.833 -74.333 38.5 -93s59.167 -28 100.5 -28zM323.999 1325c0 30.667 6 58.667 18 84s28.167 47 48.5 65s43.833 32 70.5 42s54.667 15 84 15c30 0 58.667 -5 86 -15s51.333 -24 72 -42s37.167 -39.667 49.5 -65s18.5 -53.333 18.5 -84
c0 -30 -6.16699 -57.333 -18.5 -82s-28.833 -45.834 -49.5 -63.501s-44.667 -31.334 -72 -41.001s-56 -14.5 -86 -14.5c-29.333 0 -57.333 4.83301 -84 14.5s-50.167 23.334 -70.5 41.001s-36.5 38.834 -48.5 63.501s-18 52 -18 82zM457.999 1325
c0 -25.333 7.83301 -46.5 23.5 -63.5s38.167 -25.5 67.5 -25.5c26.667 0 48 8.5 64 25.5s24 38.167 24 63.5c0 28 -8 50.167 -24 66.5s-37.333 24.5 -64 24.5c-29.333 0 -51.833 -8.16699 -67.5 -24.5s-23.5 -38.5 -23.5 -66.5z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="1651" 
d="M1174 1042c58 0 112.006 -11.168 162.006 -33.501s93.167 -54.5 129.5 -96.5s64.833 -93 85.5 -153s31 -127.333 31 -202c0 -18.667 -0.833008 -34.167 -2.5 -46.5s-4.5 -22 -8.5 -29s-9.5 -12 -16.5 -15s-16.167 -4.5 -27.5 -4.5h-598
c9.33301 -98 37.166 -170.167 83.499 -216.5s106.166 -69.5 179.499 -69.5c45.333 0 82.166 4.33301 110.499 13s51.833 18.334 70.5 29.001s34.334 20.334 47.001 29.001s26 13 40 13c12 0 22 -2 30 -6s14.667 -9.66699 20 -17l66 -84
c-27.333 -32 -57.333 -58.833 -90 -80.5s-66.834 -39 -102.501 -52s-71.834 -22.167 -108.501 -27.5s-72.334 -8 -107.001 -8c-72.667 0 -140.5 16.333 -203.5 49s-114.833 83.667 -155.5 153c-20 -36 -44.667 -66.667 -74 -92s-61.666 -46.333 -96.999 -63
s-73 -28.834 -113 -36.501s-80.333 -11.5 -121 -11.5c-50 0 -94.833 6.33301 -134.5 19s-73.5 31.167 -101.5 55.5s-49.5 54.666 -64.5 90.999s-22.5 78.166 -22.5 125.499c0 38.667 10.167 77.5 30.5 116.5s54.166 74.5 101.499 106.5s110.333 58.5 189 79.5
s176.334 32.833 293.001 35.5v35c0 68.667 -14.5 120.667 -43.5 156s-71.167 53 -126.5 53c-40 0 -73.333 -5.16699 -100 -15.5s-49.834 -21.5 -69.501 -33.5l-54.5 -33.5c-16.667 -10.333 -35 -15.5 -55 -15.5c-16.667 0 -31 4.33301 -43 13s-21.667 19.334 -29 32.001
l-45 79c59.333 54 121.166 94.5 185.499 121.5s135.833 40.5 214.5 40.5c77.333 0 140.666 -14.667 189.999 -44s87 -69.666 113 -120.999c38 50 85.333 89.5 142 118.5s123.334 43.5 200.001 43.5zM695.006 461.999c-72 -3.33301 -132.169 -10.168 -180.502 -20.501
s-87.166 -23.166 -116.499 -38.499s-50.166 -33 -62.499 -53s-18.5 -41 -18.5 -63c0 -46 13 -79.667 39 -101s61.667 -32 107 -32c32.667 0 63.167 5 91.5 15s52.833 25.5 73.5 46.5s37 48.167 49 81.5s18 73 18 119v46zM1163 864.997
c-70 0 -124.168 -22.1689 -162.501 -66.502s-61.833 -106.833 -70.5 -187.5h433c0 32.667 -4 64.334 -12 95.001s-20.167 57.834 -36.5 81.501s-37.166 42.5 -62.499 56.5s-55 21 -89 21z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="954" 
d="M405 -227c4.66699 0 9.50098 -0.666992 14.501 -2s10.667 -3 17 -5s13.333 -3.66699 21 -5s16.834 -2 27.501 -2c22 0 38.5 4.16699 49.5 12.5s16.5 18.5 16.5 30.5c0 19.333 -12 33.166 -36 41.499s-61 15.833 -111 22.5l38 126
c-53.333 9.33301 -103.166 27.833 -149.499 55.5s-86.5 63.667 -120.5 108s-60.667 96.666 -80 156.999s-29 127.833 -29 202.5c0 75.333 10.833 145.166 32.5 209.499s53.5 120 95.5 167s94 83.833 156 110.5s133.333 40 214 40c76.667 0 143.834 -12.333 201.501 -37
s109.5 -60 155.5 -106l-65 -90c-7.33301 -9.33301 -14.5 -16.666 -21.5 -21.999s-17.167 -8 -30.5 -8c-12.667 0 -25 3.83301 -37 11.5s-26.333 16.334 -43 26.001s-36.5 18.334 -59.5 26.001s-51.5 11.5 -85.5 11.5c-43.333 0 -81.333 -7.83301 -114 -23.5
s-59.834 -38.167 -81.501 -67.5s-37.834 -64.833 -48.501 -106.5s-16 -88.834 -16 -141.501c0 -54.667 5.83301 -103.334 17.5 -146.001s28.5 -78.5 50.5 -107.5s48.667 -51 80 -66s66.666 -22.5 105.999 -22.5s71.166 4.83301 95.499 14.5s44.833 20.334 61.5 32.001
s31.167 22.334 43.5 32.001s26.166 14.5 41.499 14.5c18 0 33 -7.66699 45 -23l71 -90c-46 -54 -97.667 -93.5 -155 -118.5s-116.666 -40.167 -177.999 -45.5l-12 -44c59.333 -14.667 101 -34.5 125 -59.5s36 -54.5 36 -88.5c0 -22 -5.83301 -42 -17.5 -60
s-28.167 -33.333 -49.5 -46s-47 -22.5 -77 -29.5s-63 -10.5 -99 -10.5c-27.333 0 -52.833 2 -76.5 6s-47.167 9.66699 -70.5 17l23 76c4 15.333 13.333 23 28 23z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="1069" 
d="M556 1042c64.667 0 124.168 -10.3311 178.501 -30.998s101.166 -50.834 140.499 -90.501s70 -88.334 92 -146.001s33 -123.5 33 -197.5c0 -18.667 -0.833008 -34.167 -2.5 -46.5s-4.66699 -22 -9 -29s-10.166 -12 -17.499 -15s-16.666 -4.5 -27.999 -4.5h-634
c7.33301 -105.333 35.666 -182.666 84.999 -231.999s114.666 -74 195.999 -74c40 0 74.5 4.66699 103.5 14s54.333 19.666 76 30.999s40.667 21.666 57 30.999s32.166 14 47.499 14c10 0 18.667 -2 26 -6s13.666 -9.66699 18.999 -17l72 -90
c-27.333 -32 -58 -58.833 -92 -80.5s-69.5 -39 -106.5 -52s-74.667 -22.167 -113 -27.5s-75.5 -8 -111.5 -8c-71.333 0 -137.666 11.833 -198.999 35.5s-114.666 58.667 -159.999 105s-81 103.666 -107 171.999s-39 147.5 -39 237.5c0 70 11.333 135.833 34 197.5
s55.167 115.334 97.5 161.001s94 81.834 155 108.501s129.833 40 206.5 40zM561.001 865.002c-72 0 -128.333 -20.3301 -169 -60.9971s-66.667 -98.334 -78 -173.001h464c0 32 -4.33301 62.167 -13 90.5s-22 53.166 -40 74.499s-40.667 38.166 -68 50.499
s-59.333 18.5 -96 18.5zM473.001 1462c27.333 0 47.501 -4.5 60.501 -13.5s24.5 -22.167 34.5 -39.5l143 -243h-141c-18.667 0 -33.834 2.5 -45.501 7.5s-23.5 14.167 -35.5 27.5l-247 261h231z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="1069" 
d="M556 1042c64.667 0 124.168 -10.3311 178.501 -30.998s101.166 -50.834 140.499 -90.501s70 -88.334 92 -146.001s33 -123.5 33 -197.5c0 -18.667 -0.833008 -34.167 -2.5 -46.5s-4.66699 -22 -9 -29s-10.166 -12 -17.499 -15s-16.666 -4.5 -27.999 -4.5h-634
c7.33301 -105.333 35.666 -182.666 84.999 -231.999s114.666 -74 195.999 -74c40 0 74.5 4.66699 103.5 14s54.333 19.666 76 30.999s40.667 21.666 57 30.999s32.166 14 47.499 14c10 0 18.667 -2 26 -6s13.666 -9.66699 18.999 -17l72 -90
c-27.333 -32 -58 -58.833 -92 -80.5s-69.5 -39 -106.5 -52s-74.667 -22.167 -113 -27.5s-75.5 -8 -111.5 -8c-71.333 0 -137.666 11.833 -198.999 35.5s-114.666 58.667 -159.999 105s-81 103.666 -107 171.999s-39 147.5 -39 237.5c0 70 11.333 135.833 34 197.5
s55.167 115.334 97.5 161.001s94 81.834 155 108.501s129.833 40 206.5 40zM561.001 865.002c-72 0 -128.333 -20.3301 -169 -60.9971s-66.667 -98.334 -78 -173.001h464c0 32 -4.33301 62.167 -13 90.5s-22 53.166 -40 74.499s-40.667 38.166 -68 50.499
s-59.333 18.5 -96 18.5zM908.001 1462l-246.998 -261c-12.667 -13.333 -24.834 -22.5 -36.501 -27.5s-26.834 -7.5 -45.501 -7.5h-148l142 243c10 17.333 21.667 30.5 35 39.5s33.333 13.5 60 13.5h240z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="1069" 
d="M556 1042c64.667 0 124.168 -10.3311 178.501 -30.998s101.166 -50.834 140.499 -90.501s70 -88.334 92 -146.001s33 -123.5 33 -197.5c0 -18.667 -0.833008 -34.167 -2.5 -46.5s-4.66699 -22 -9 -29s-10.166 -12 -17.499 -15s-16.666 -4.5 -27.999 -4.5h-634
c7.33301 -105.333 35.666 -182.666 84.999 -231.999s114.666 -74 195.999 -74c40 0 74.5 4.66699 103.5 14s54.333 19.666 76 30.999s40.667 21.666 57 30.999s32.166 14 47.499 14c10 0 18.667 -2 26 -6s13.666 -9.66699 18.999 -17l72 -90
c-27.333 -32 -58 -58.833 -92 -80.5s-69.5 -39 -106.5 -52s-74.667 -22.167 -113 -27.5s-75.5 -8 -111.5 -8c-71.333 0 -137.666 11.833 -198.999 35.5s-114.666 58.667 -159.999 105s-81 103.666 -107 171.999s-39 147.5 -39 237.5c0 70 11.333 135.833 34 197.5
s55.167 115.334 97.5 161.001s94 81.834 155 108.501s129.833 40 206.5 40zM561.001 865.002c-72 0 -128.333 -20.3301 -169 -60.9971s-66.667 -98.334 -78 -173.001h464c0 32 -4.33301 62.167 -13 90.5s-22 53.166 -40 74.499s-40.667 38.166 -68 50.499
s-59.333 18.5 -96 18.5zM907.001 1168l-164.999 0.000976562c-20.667 0 -37.334 6 -50.001 18l-105 95l-10.5 10c-3.66699 3.33301 -7.16699 7.33301 -10.5 12c-3.33301 -4.66699 -6.83301 -8.66699 -10.5 -12l-10.5 -10l-107 -95
c-5.33301 -4.66699 -12.5 -8.83398 -21.5 -12.501s-18.5 -5.5 -28.5 -5.5h-171l229 278h232z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="1069" 
d="M556 1042c64.667 0 124.168 -10.3311 178.501 -30.998s101.166 -50.834 140.499 -90.501s70 -88.334 92 -146.001s33 -123.5 33 -197.5c0 -18.667 -0.833008 -34.167 -2.5 -46.5s-4.66699 -22 -9 -29s-10.166 -12 -17.499 -15s-16.666 -4.5 -27.999 -4.5h-634
c7.33301 -105.333 35.666 -182.666 84.999 -231.999s114.666 -74 195.999 -74c40 0 74.5 4.66699 103.5 14s54.333 19.666 76 30.999s40.667 21.666 57 30.999s32.166 14 47.499 14c10 0 18.667 -2 26 -6s13.666 -9.66699 18.999 -17l72 -90
c-27.333 -32 -58 -58.833 -92 -80.5s-69.5 -39 -106.5 -52s-74.667 -22.167 -113 -27.5s-75.5 -8 -111.5 -8c-71.333 0 -137.666 11.833 -198.999 35.5s-114.666 58.667 -159.999 105s-81 103.666 -107 171.999s-39 147.5 -39 237.5c0 70 11.333 135.833 34 197.5
s55.167 115.334 97.5 161.001s94 81.834 155 108.501s129.833 40 206.5 40zM561.001 865.002c-72 0 -128.333 -20.3301 -169 -60.9971s-66.667 -98.334 -78 -173.001h464c0 32 -4.33301 62.167 -13 90.5s-22 53.166 -40 74.499s-40.667 38.166 -68 50.499
s-59.333 18.5 -96 18.5zM513.001 1292c0 -18.667 -3.66699 -36.167 -11 -52.5s-17.5 -30.5 -30.5 -42.5s-28 -21.5 -45 -28.5s-34.833 -10.5 -53.5 -10.5c-18 0 -35.167 3.5 -51.5 10.5s-30.833 16.5 -43.5 28.5s-22.667 26.167 -30 42.5s-11 33.833 -11 52.5
c0 19.333 3.66699 37.5 11 54.5s17.333 31.833 30 44.5s27.167 22.667 43.5 30s33.5 11 51.5 11c18.667 0 36.5 -3.66699 53.5 -11s32 -17.333 45 -30s23.167 -27.5 30.5 -44.5s11 -35.167 11 -54.5zM887.001 1292c0 -18.667 -3.66699 -36.167 -11 -52.5
s-17.333 -30.5 -30 -42.5s-27.5 -21.5 -44.5 -28.5s-34.833 -10.5 -53.5 -10.5s-36.334 3.5 -53.001 10.5s-31.167 16.5 -43.5 28.5s-22.166 26.167 -29.499 42.5s-11 33.833 -11 52.5c0 19.333 3.66699 37.5 11 54.5s17.166 31.833 29.499 44.5s26.833 22.667 43.5 30
s34.334 11 53.001 11s36.5 -3.66699 53.5 -11s31.833 -17.333 44.5 -30s22.667 -27.5 30 -44.5s11 -35.167 11 -54.5z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="542" 
d="M395 1026v-1026h-247v1026h247zM190 1462c27.333 0 47.501 -4.5 60.501 -13.5s24.5 -22.167 34.5 -39.5l143 -243h-141c-18.667 0 -33.834 2.5 -45.501 7.5s-23.5 14.167 -35.5 27.5l-247 261h231z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="542" 
d="M395 1026v-1026h-247v1026h247zM625 1462l-246.998 -261c-12.667 -13.333 -24.834 -22.5 -36.501 -27.5s-26.834 -7.5 -45.501 -7.5h-148l142 243c10 17.333 21.667 30.5 35 39.5s33.333 13.5 60 13.5h240z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="542" 
d="M395 1026v-1026h-247v1026h247zM624 1168l-164.999 0.000976562c-20.667 0 -37.334 6 -50.001 18l-105 95l-10.5 10c-3.66699 3.33301 -7.16699 7.33301 -10.5 12c-3.33301 -4.66699 -6.83301 -8.66699 -10.5 -12l-10.5 -10l-107 -95
c-5.33301 -4.66699 -12.5 -8.83398 -21.5 -12.501s-18.5 -5.5 -28.5 -5.5h-171l229 278h232z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="542" 
d="M395 1026v-1026h-247v1026h247zM230 1292c0 -18.667 -3.66699 -36.167 -11 -52.5s-17.5 -30.5 -30.5 -42.5s-28 -21.5 -45 -28.5s-34.833 -10.5 -53.5 -10.5c-18 0 -35.167 3.5 -51.5 10.5s-30.833 16.5 -43.5 28.5s-22.667 26.167 -30 42.5s-11 33.833 -11 52.5
c0 19.333 3.66699 37.5 11 54.5s17.333 31.833 30 44.5s27.167 22.667 43.5 30s33.5 11 51.5 11c18.667 0 36.5 -3.66699 53.5 -11s32 -17.333 45 -30s23.167 -27.5 30.5 -44.5s11 -35.167 11 -54.5zM604 1292c0 -18.667 -3.66699 -36.167 -11 -52.5
s-17.333 -30.5 -30 -42.5s-27.5 -21.5 -44.5 -28.5s-34.833 -10.5 -53.5 -10.5s-36.334 3.5 -53.001 10.5s-31.167 16.5 -43.5 28.5s-22.166 26.167 -29.499 42.5s-11 33.833 -11 52.5c0 19.333 3.66699 37.5 11 54.5s17.166 31.833 29.499 44.5s26.833 22.667 43.5 30
s34.334 11 53.001 11s36.5 -3.66699 53.5 -11s31.833 -17.333 44.5 -30s22.667 -27.5 30 -44.5s11 -35.167 11 -54.5z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="1136" 
d="M386 1062c-6.66699 10.667 -10.0029 21.667 -10.0029 33c0 20 10.667 36 32 48l75 43c-21.333 8.66699 -43.833 16.667 -67.5 24l-74.5 22c-16.667 4.66699 -30.5 13 -41.5 25s-16.5 28 -16.5 48c0 10.667 2.66699 24.667 8 42l29 85
c67.333 -11.333 132.166 -27.333 194.499 -48s121.166 -47.334 176.499 -80.001l176 114l49 -80c6.66699 -11.333 10 -21.666 10 -30.999s-2.5 -18 -7.5 -26s-12.167 -14.667 -21.5 -20l-81 -46c38 -34 72.333 -71.833 103 -113.5s57 -87.5 79 -137.5
s38.833 -104.667 50.5 -164s17.5 -123.333 17.5 -192c0 -98 -11.167 -185.5 -33.5 -262.5s-55.166 -142.333 -98.499 -196s-97 -94.5 -161 -122.5s-137.333 -42 -220 -42c-69.333 0 -133.666 11.167 -192.999 33.5s-110.666 54.5 -153.999 96.5s-77.5 93.167 -102.5 153.5
s-37.5 128.833 -37.5 205.5c0 61.333 10.833 119.666 32.5 174.999s52.334 103.833 92.001 145.5s87.5 74.834 143.5 99.501s118.667 37 188 37c63.333 0 121.333 -11.5 174 -34.5s99.334 -56.5 140.001 -100.5c-14 70.667 -37.667 131.5 -71 182.5s-78.333 95.5 -135 133.5
l-198 -128zM558.997 173c38.667 0 74.334 7.00098 107.001 21.001s61 36.5 85 67.5s43.167 71.167 57.5 120.5s22.5 109.333 24.5 180c-10 24.667 -22.833 48 -38.5 70s-34.167 41.167 -55.5 57.5s-45.833 29.333 -73.5 39s-58.834 14.5 -93.501 14.5
c-42.667 0 -80.167 -7 -112.5 -21s-59.5 -33.333 -81.5 -58s-38.667 -53.334 -50 -86.001s-17 -68 -17 -106c0 -48.667 6.5 -91.5 19.5 -128.5s30.667 -68.167 53 -93.5s48.5 -44.5 78.5 -57.5s62.333 -19.5 97 -19.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="1137" 
d="M132 0l-0.000976562 1026h151c32 0 53 -15 63 -45l17 -81c20.667 21.333 42.5 40.666 65.5 57.999s47.333 32.333 73 45s53.167 22.334 82.5 29.001s61.333 10 96 10c56 0 105.667 -9.5 149 -28.5s79.5 -45.667 108.5 -80s51 -75.333 66 -123s22.5 -100.167 22.5 -157.5
v-653h-247v653c0 62.667 -14.5 111.167 -43.5 145.5s-72.5 51.5 -130.5 51.5c-42.667 0 -82.667 -9.66699 -120 -29s-72.666 -45.666 -105.999 -78.999v-742h-247zM684.999 1359c20 0 35.668 5.50098 47.001 16.501s17 30.833 17 59.5h150
c0 -37.333 -5.16699 -71.166 -15.5 -101.499s-24.5 -56.333 -42.5 -78s-39.5 -38.334 -64.5 -50.001s-52.5 -17.5 -82.5 -17.5c-24 0 -46.333 4.33301 -67 13s-40 18 -58 28l-50.5 28c-15.667 8.66699 -30.167 13 -43.5 13c-19.333 0 -34.5 -5.83301 -45.5 -17.5
s-16.5 -31.5 -16.5 -59.5h-153c0 37.333 5.16699 71.166 15.5 101.499s24.833 56.333 43.5 78s40.5 38.5 65.5 50.5s52.167 18 81.5 18c24 0 46.5 -4.33301 67.5 -13s40.5 -18 58.5 -28l50 -28c15.333 -8.66699 29.666 -13 42.999 -13z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="1137" 
d="M570 1042c76.667 0 146.167 -12.333 208.5 -37s115.5 -59.667 159.5 -105s78 -100.666 102 -165.999s36 -138.333 36 -219c0 -81.333 -12 -154.666 -36 -219.999s-58 -121 -102 -167s-97.167 -81.333 -159.5 -106s-131.833 -37 -208.5 -37s-146.334 12.333 -209.001 37
s-116.167 60 -160.5 106s-78.666 101.667 -102.999 167s-36.5 138.666 -36.5 219.999c0 80.667 12.167 153.667 36.5 219s58.666 120.666 102.999 165.999s97.833 80.333 160.5 105s132.334 37 209.001 37zM570 175c85.333 0 148.5 28.667 189.5 86s61.5 141.333 61.5 252
s-20.5 195 -61.5 253s-104.167 87 -189.5 87c-86.667 0 -150.667 -29.167 -192 -87.5s-62 -142.5 -62 -252.5s20.667 -193.833 62 -251.5s105.333 -86.5 192 -86.5zM482 1462c27.333 0 47.501 -4.5 60.501 -13.5s24.5 -22.167 34.5 -39.5l143 -243h-141
c-18.667 0 -33.834 2.5 -45.501 7.5s-23.5 14.167 -35.5 27.5l-247 261h231z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="1137" 
d="M570 1042c76.667 0 146.167 -12.333 208.5 -37s115.5 -59.667 159.5 -105s78 -100.666 102 -165.999s36 -138.333 36 -219c0 -81.333 -12 -154.666 -36 -219.999s-58 -121 -102 -167s-97.167 -81.333 -159.5 -106s-131.833 -37 -208.5 -37s-146.334 12.333 -209.001 37
s-116.167 60 -160.5 106s-78.666 101.667 -102.999 167s-36.5 138.666 -36.5 219.999c0 80.667 12.167 153.667 36.5 219s58.666 120.666 102.999 165.999s97.833 80.333 160.5 105s132.334 37 209.001 37zM570 175c85.333 0 148.5 28.667 189.5 86s61.5 141.333 61.5 252
s-20.5 195 -61.5 253s-104.167 87 -189.5 87c-86.667 0 -150.667 -29.167 -192 -87.5s-62 -142.5 -62 -252.5s20.667 -193.833 62 -251.5s105.333 -86.5 192 -86.5zM917 1462l-246.998 -261c-12.667 -13.333 -24.834 -22.5 -36.501 -27.5s-26.834 -7.5 -45.501 -7.5h-148
l142 243c10 17.333 21.667 30.5 35 39.5s33.333 13.5 60 13.5h240z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="1137" 
d="M570 1042c76.667 0 146.167 -12.333 208.5 -37s115.5 -59.667 159.5 -105s78 -100.666 102 -165.999s36 -138.333 36 -219c0 -81.333 -12 -154.666 -36 -219.999s-58 -121 -102 -167s-97.167 -81.333 -159.5 -106s-131.833 -37 -208.5 -37s-146.334 12.333 -209.001 37
s-116.167 60 -160.5 106s-78.666 101.667 -102.999 167s-36.5 138.666 -36.5 219.999c0 80.667 12.167 153.667 36.5 219s58.666 120.666 102.999 165.999s97.833 80.333 160.5 105s132.334 37 209.001 37zM570 175c85.333 0 148.5 28.667 189.5 86s61.5 141.333 61.5 252
s-20.5 195 -61.5 253s-104.167 87 -189.5 87c-86.667 0 -150.667 -29.167 -192 -87.5s-62 -142.5 -62 -252.5s20.667 -193.833 62 -251.5s105.333 -86.5 192 -86.5zM916 1168l-164.999 0.000976562c-20.667 0 -37.334 6 -50.001 18l-105 95l-10.5 10
c-3.66699 3.33301 -7.16699 7.33301 -10.5 12c-3.33301 -4.66699 -6.83301 -8.66699 -10.5 -12l-10.5 -10l-107 -95c-5.33301 -4.66699 -12.5 -8.83398 -21.5 -12.501s-18.5 -5.5 -28.5 -5.5h-171l229 278h232z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="1137" 
d="M570 1042c76.667 0 146.167 -12.333 208.5 -37s115.5 -59.667 159.5 -105s78 -100.666 102 -165.999s36 -138.333 36 -219c0 -81.333 -12 -154.666 -36 -219.999s-58 -121 -102 -167s-97.167 -81.333 -159.5 -106s-131.833 -37 -208.5 -37s-146.334 12.333 -209.001 37
s-116.167 60 -160.5 106s-78.666 101.667 -102.999 167s-36.5 138.666 -36.5 219.999c0 80.667 12.167 153.667 36.5 219s58.666 120.666 102.999 165.999s97.833 80.333 160.5 105s132.334 37 209.001 37zM570 175c85.333 0 148.5 28.667 189.5 86s61.5 141.333 61.5 252
s-20.5 195 -61.5 253s-104.167 87 -189.5 87c-86.667 0 -150.667 -29.167 -192 -87.5s-62 -142.5 -62 -252.5s20.667 -193.833 62 -251.5s105.333 -86.5 192 -86.5zM678 1359c20 0 35.668 5.50098 47.001 16.501s17 30.833 17 59.5h150
c0 -37.333 -5.16699 -71.166 -15.5 -101.499s-24.5 -56.333 -42.5 -78s-39.5 -38.334 -64.5 -50.001s-52.5 -17.5 -82.5 -17.5c-24 0 -46.333 4.33301 -67 13s-40 18 -58 28l-50.5 28c-15.667 8.66699 -30.167 13 -43.5 13c-19.333 0 -34.5 -5.83301 -45.5 -17.5
s-16.5 -31.5 -16.5 -59.5h-153c0 37.333 5.16699 71.166 15.5 101.499s24.833 56.333 43.5 78s40.5 38.5 65.5 50.5s52.167 18 81.5 18c24 0 46.5 -4.33301 67.5 -13s40.5 -18 58.5 -28l50 -28c15.333 -8.66699 29.666 -13 42.999 -13z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="1137" 
d="M570 1042c76.667 0 146.167 -12.333 208.5 -37s115.5 -59.667 159.5 -105s78 -100.666 102 -165.999s36 -138.333 36 -219c0 -81.333 -12 -154.666 -36 -219.999s-58 -121 -102 -167s-97.167 -81.333 -159.5 -106s-131.833 -37 -208.5 -37s-146.334 12.333 -209.001 37
s-116.167 60 -160.5 106s-78.666 101.667 -102.999 167s-36.5 138.666 -36.5 219.999c0 80.667 12.167 153.667 36.5 219s58.666 120.666 102.999 165.999s97.833 80.333 160.5 105s132.334 37 209.001 37zM570 175c85.333 0 148.5 28.667 189.5 86s61.5 141.333 61.5 252
s-20.5 195 -61.5 253s-104.167 87 -189.5 87c-86.667 0 -150.667 -29.167 -192 -87.5s-62 -142.5 -62 -252.5s20.667 -193.833 62 -251.5s105.333 -86.5 192 -86.5zM522 1292c0 -18.667 -3.66699 -36.167 -11 -52.5s-17.5 -30.5 -30.5 -42.5s-28 -21.5 -45 -28.5
s-34.833 -10.5 -53.5 -10.5c-18 0 -35.167 3.5 -51.5 10.5s-30.833 16.5 -43.5 28.5s-22.667 26.167 -30 42.5s-11 33.833 -11 52.5c0 19.333 3.66699 37.5 11 54.5s17.333 31.833 30 44.5s27.167 22.667 43.5 30s33.5 11 51.5 11c18.667 0 36.5 -3.66699 53.5 -11
s32 -17.333 45 -30s23.167 -27.5 30.5 -44.5s11 -35.167 11 -54.5zM896 1292c0 -18.667 -3.66699 -36.167 -11 -52.5s-17.333 -30.5 -30 -42.5s-27.5 -21.5 -44.5 -28.5s-34.833 -10.5 -53.5 -10.5s-36.334 3.5 -53.001 10.5s-31.167 16.5 -43.5 28.5
s-22.166 26.167 -29.499 42.5s-11 33.833 -11 52.5c0 19.333 3.66699 37.5 11 54.5s17.166 31.833 29.499 44.5s26.833 22.667 43.5 30s34.334 11 53.001 11s36.5 -3.66699 53.5 -11s31.833 -17.333 44.5 -30s22.667 -27.5 30 -44.5s11 -35.167 11 -54.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" 
d="M91 763h976v-183h-976v183zM427 1027c0 20.667 3.83301 40.334 11.5 59.001s18.167 34.667 31.5 48s29.333 24 48 32s38.667 12 60 12c20.667 0 40.334 -4 59.001 -12s34.834 -18.667 48.501 -32s24.5 -29.333 32.5 -48s12 -38.334 12 -59.001
c0 -21.333 -4 -41.166 -12 -59.499s-18.833 -34.166 -32.5 -47.499s-29.834 -23.833 -48.501 -31.5s-38.334 -11.5 -59.001 -11.5c-21.333 0 -41.333 3.83301 -60 11.5s-34.667 18.167 -48 31.5s-23.833 29.166 -31.5 47.499s-11.5 38.166 -11.5 59.499zM427 315
c0 20.667 3.83301 40.334 11.5 59.001s18.167 34.667 31.5 48s29.333 24 48 32s38.667 12 60 12c20.667 0 40.334 -4 59.001 -12s34.834 -18.667 48.501 -32s24.5 -29.333 32.5 -48s12 -38.334 12 -59.001c0 -21.333 -4 -41.166 -12 -59.499s-18.833 -34.166 -32.5 -47.499
s-29.834 -23.833 -48.501 -31.5s-38.334 -11.5 -59.001 -11.5c-21.333 0 -41.333 3.83301 -60 11.5s-34.667 18.167 -48 31.5s-23.833 29.166 -31.5 47.499s-11.5 38.166 -11.5 59.499z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="1137" 
d="M943 893c42.667 -46 75.501 -100.667 98.501 -164s34.5 -134.666 34.5 -213.999c0 -81.333 -12 -154.666 -36 -219.999s-58 -121 -102 -167s-97.167 -81.333 -159.5 -106s-131.833 -37 -208.5 -37c-48 0 -93.167 4.83301 -135.5 14.5s-81.833 23.834 -118.5 42.501
l-34 -47c-19.333 -26 -42.5 -44.5 -69.5 -55.5s-53.167 -16.5 -78.5 -16.5h-91l154 209c-44 46 -77.667 101.167 -101 165.5s-35 136.833 -35 217.5s12.167 153.667 36.5 219s58.666 120.666 102.999 165.999s97.833 80.333 160.5 105s132.334 37 209.001 37
c48 0 93.333 -5 136 -15s82 -24.333 118 -43l56 75c8.66699 12 16.667 22.167 24 30.5s15.166 15.166 23.499 20.499s17.5 9.16602 27.5 11.499s22.333 3.5 37 3.5h122zM300.001 513c0 -79.333 10.666 -145 31.999 -197l376 510c-37.333 22.667 -83.333 34 -138 34
c-87.333 0 -154.166 -30.167 -200.499 -90.5s-69.5 -145.833 -69.5 -256.5zM570 167c85.333 0 151.167 30 197.5 90s69.5 145.333 69.5 256c0 39.333 -2.66699 75 -8 107s-13 60.667 -23 86l-374 -507c36.667 -21.333 82.667 -32 138 -32z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="1137" 
d="M358 1026l0.00292969 -652.001c0 -62.667 14.5 -111.167 43.5 -145.5s72.5 -51.5 130.5 -51.5c42.667 0 82.667 9.5 120 28.5s72.666 45.167 105.999 78.5v742h247v-1026h-151c-32 0 -53 15 -63 45l-17 82c-21.333 -21.333 -43.333 -40.833 -66 -58.5
s-46.834 -32.667 -72.501 -45s-53.334 -22 -83.001 -29s-61.5 -10.5 -95.5 -10.5c-56 0 -105.5 9.5 -148.5 28.5s-79.167 45.833 -108.5 80.5s-51.5 75.834 -66.5 123.501s-22.5 100.167 -22.5 157.5v652h247zM477.003 1462c27.333 0 47.501 -4.5 60.501 -13.5
s24.5 -22.167 34.5 -39.5l143 -243h-141c-18.667 0 -33.834 2.5 -45.501 7.5s-23.5 14.167 -35.5 27.5l-247 261h231z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="1137" 
d="M358 1026l0.00292969 -652.001c0 -62.667 14.5 -111.167 43.5 -145.5s72.5 -51.5 130.5 -51.5c42.667 0 82.667 9.5 120 28.5s72.666 45.167 105.999 78.5v742h247v-1026h-151c-32 0 -53 15 -63 45l-17 82c-21.333 -21.333 -43.333 -40.833 -66 -58.5
s-46.834 -32.667 -72.501 -45s-53.334 -22 -83.001 -29s-61.5 -10.5 -95.5 -10.5c-56 0 -105.5 9.5 -148.5 28.5s-79.167 45.833 -108.5 80.5s-51.5 75.834 -66.5 123.501s-22.5 100.167 -22.5 157.5v652h247zM912.003 1462l-246.998 -261
c-12.667 -13.333 -24.834 -22.5 -36.501 -27.5s-26.834 -7.5 -45.501 -7.5h-148l142 243c10 17.333 21.667 30.5 35 39.5s33.333 13.5 60 13.5h240z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="1137" 
d="M358 1026l0.00292969 -652.001c0 -62.667 14.5 -111.167 43.5 -145.5s72.5 -51.5 130.5 -51.5c42.667 0 82.667 9.5 120 28.5s72.666 45.167 105.999 78.5v742h247v-1026h-151c-32 0 -53 15 -63 45l-17 82c-21.333 -21.333 -43.333 -40.833 -66 -58.5
s-46.834 -32.667 -72.501 -45s-53.334 -22 -83.001 -29s-61.5 -10.5 -95.5 -10.5c-56 0 -105.5 9.5 -148.5 28.5s-79.167 45.833 -108.5 80.5s-51.5 75.834 -66.5 123.501s-22.5 100.167 -22.5 157.5v652h247zM911.003 1168l-164.999 0.000976562
c-20.667 0 -37.334 6 -50.001 18l-105 95l-10.5 10c-3.66699 3.33301 -7.16699 7.33301 -10.5 12c-3.33301 -4.66699 -6.83301 -8.66699 -10.5 -12l-10.5 -10l-107 -95c-5.33301 -4.66699 -12.5 -8.83398 -21.5 -12.501s-18.5 -5.5 -28.5 -5.5h-171l229 278h232z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="1137" 
d="M358 1026l0.00292969 -652.001c0 -62.667 14.5 -111.167 43.5 -145.5s72.5 -51.5 130.5 -51.5c42.667 0 82.667 9.5 120 28.5s72.666 45.167 105.999 78.5v742h247v-1026h-151c-32 0 -53 15 -63 45l-17 82c-21.333 -21.333 -43.333 -40.833 -66 -58.5
s-46.834 -32.667 -72.501 -45s-53.334 -22 -83.001 -29s-61.5 -10.5 -95.5 -10.5c-56 0 -105.5 9.5 -148.5 28.5s-79.167 45.833 -108.5 80.5s-51.5 75.834 -66.5 123.501s-22.5 100.167 -22.5 157.5v652h247zM517.003 1292c0 -18.667 -3.66699 -36.167 -11 -52.5
s-17.5 -30.5 -30.5 -42.5s-28 -21.5 -45 -28.5s-34.833 -10.5 -53.5 -10.5c-18 0 -35.167 3.5 -51.5 10.5s-30.833 16.5 -43.5 28.5s-22.667 26.167 -30 42.5s-11 33.833 -11 52.5c0 19.333 3.66699 37.5 11 54.5s17.333 31.833 30 44.5s27.167 22.667 43.5 30
s33.5 11 51.5 11c18.667 0 36.5 -3.66699 53.5 -11s32 -17.333 45 -30s23.167 -27.5 30.5 -44.5s11 -35.167 11 -54.5zM891.003 1292c0 -18.667 -3.66699 -36.167 -11 -52.5s-17.333 -30.5 -30 -42.5s-27.5 -21.5 -44.5 -28.5s-34.833 -10.5 -53.5 -10.5
s-36.334 3.5 -53.001 10.5s-31.167 16.5 -43.5 28.5s-22.166 26.167 -29.499 42.5s-11 33.833 -11 52.5c0 19.333 3.66699 37.5 11 54.5s17.166 31.833 29.499 44.5s26.833 22.667 43.5 30s34.334 11 53.001 11s36.5 -3.66699 53.5 -11s31.833 -17.333 44.5 -30
s22.667 -27.5 30 -44.5s11 -35.167 11 -54.5z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="1067" 
d="M496 -282c-7.33301 -17.333 -16.8281 -30.499 -28.4951 -39.499s-29.5 -13.5 -53.5 -13.5h-184l192 411l-415 950h216c20 0 35.333 -4.66699 46 -14s18.667 -20 24 -32l219 -532c7.33301 -17.333 13.666 -35.333 18.999 -54s10 -37.334 14 -56.001
c5.33301 19.333 11.166 38 17.499 56s13.166 36.333 20.499 55l206 531c5.33301 13.333 14.166 24.333 26.499 33s26.166 13 41.499 13h198zM899.005 1462l-246.998 -261c-12.667 -13.333 -24.834 -22.5 -36.501 -27.5s-26.834 -7.5 -45.501 -7.5h-148l142 243
c10 17.333 21.667 30.5 35 39.5s33.333 13.5 60 13.5h240z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="1131" 
d="M132 -335l-0.000976562 1821h247v-586c40.667 43.333 86.667 77.833 138 103.5s111.333 38.5 180 38.5c56 0 107.167 -11.5 153.5 -34.5s86.333 -56.5 120 -100.5s59.667 -98.333 78 -163s27.5 -139 27.5 -223c0 -76.667 -10.333 -147.667 -31 -213
s-50.167 -122 -88.5 -170s-84.666 -85.5 -138.999 -112.5s-115.166 -40.5 -182.499 -40.5c-29.333 0 -56.333 2.66699 -81 8s-47.334 13.166 -68.001 23.499s-39.667 22.666 -57 36.999s-34 30.5 -50 48.5v-437h-247zM611.999 850.002
c-51.333 0 -95.166 -10.833 -131.499 -32.5s-70.166 -52.167 -101.499 -91.5v-460c28 -34.667 58.5 -58.834 91.5 -72.501s68.5 -20.5 106.5 -20.5c37.333 0 71.166 7 101.499 21s56 35.333 77 64s37.167 64.834 48.5 108.501s17 95.167 17 154.5
c0 60 -4.83301 110.833 -14.5 152.5s-23.5 75.5 -41.5 101.5s-39.833 45 -65.5 57s-54.834 18 -87.501 18z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="1067" 
d="M496 -282c-7.33301 -17.333 -16.8281 -30.499 -28.4951 -39.499s-29.5 -13.5 -53.5 -13.5h-184l192 411l-415 950h216c20 0 35.333 -4.66699 46 -14s18.667 -20 24 -32l219 -532c7.33301 -17.333 13.666 -35.333 18.999 -54s10 -37.334 14 -56.001
c5.33301 19.333 11.166 38 17.499 56s13.166 36.333 20.499 55l206 531c5.33301 13.333 14.166 24.333 26.499 33s26.166 13 41.499 13h198zM504.005 1292c0 -18.667 -3.66699 -36.167 -11 -52.5s-17.5 -30.5 -30.5 -42.5s-28 -21.5 -45 -28.5s-34.833 -10.5 -53.5 -10.5
c-18 0 -35.167 3.5 -51.5 10.5s-30.833 16.5 -43.5 28.5s-22.667 26.167 -30 42.5s-11 33.833 -11 52.5c0 19.333 3.66699 37.5 11 54.5s17.333 31.833 30 44.5s27.167 22.667 43.5 30s33.5 11 51.5 11c18.667 0 36.5 -3.66699 53.5 -11s32 -17.333 45 -30
s23.167 -27.5 30.5 -44.5s11 -35.167 11 -54.5zM878.005 1292c0 -18.667 -3.66699 -36.167 -11 -52.5s-17.333 -30.5 -30 -42.5s-27.5 -21.5 -44.5 -28.5s-34.833 -10.5 -53.5 -10.5s-36.334 3.5 -53.001 10.5s-31.167 16.5 -43.5 28.5s-22.166 26.167 -29.499 42.5
s-11 33.833 -11 52.5c0 19.333 3.66699 37.5 11 54.5s17.166 31.833 29.499 44.5s26.833 22.667 43.5 30s34.334 11 53.001 11s36.5 -3.66699 53.5 -11s31.833 -17.333 44.5 -30s22.667 -27.5 30 -44.5s11 -35.167 11 -54.5z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="1420" 
d="M1404 -187c12 0 19.667 -5.33203 23 -15.999l38 -91c-22 -14.667 -49.5 -26.834 -82.5 -36.501s-67.833 -14.5 -104.5 -14.5c-65.333 0 -115.666 13.667 -150.999 41s-53 62.666 -53 105.999c0 36.667 12.167 72 36.5 106s59.166 64.667 104.499 92h-6
c-23.333 0 -42.5 5.83301 -57.5 17.5s-25.833 26.167 -32.5 43.5l-108 295h-599l-108 -295c-5.33301 -15.333 -15.833 -29.333 -31.5 -42s-34.834 -19 -57.501 -19h-209l568 1446h275l568 -1446h-53c-13.333 -7.33301 -26.333 -15.666 -39 -24.999s-24 -19.833 -34 -31.5
s-18 -24.167 -24 -37.5s-9 -27.333 -9 -42c0 -20 6.33301 -35.833 19 -47.5s30.334 -17.5 53.001 -17.5c12.667 0 23 0.666992 31 2s14.5 3 19.5 5s9.33301 3.66699 13 5s7.16699 2 10.5 2zM481 546.001h460.999l-176 481c-8 21.333 -16.833 46.5 -26.5 75.5
s-19.167 60.5 -28.5 94.5c-9.33301 -34 -18.666 -65.667 -27.999 -95s-18 -55 -26 -77z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="1047" 
d="M936 0c-13.333 -7.33301 -26.335 -15.666 -39.002 -24.999s-24 -19.833 -34 -31.5s-18 -24.167 -24 -37.5s-9 -27.333 -9 -42c0 -20 6.33301 -35.833 19 -47.5s30.334 -17.5 53.001 -17.5c12.667 0 23 0.666992 31 2s14.5 3 19.5 5s9.33301 3.66699 13 5
s7.16699 2 10.5 2c12 0 19.667 -5.33301 23 -16l38 -91c-22 -14.667 -49.5 -26.834 -82.5 -36.501s-67.833 -14.5 -104.5 -14.5c-65.333 0 -115.666 13.667 -150.999 41s-53 62.666 -53 105.999c0 37.333 12.667 73.166 38 107.499s61.333 65.5 108 93.5
c-13.333 2.66699 -24.166 7.83398 -32.499 15.501s-14.833 19.167 -19.5 34.5l-22 73c-26 -23.333 -51.5 -43.833 -76.5 -61.5s-50.833 -32.5 -77.5 -44.5s-55 -21 -85 -27s-63.333 -9 -100 -9c-43.333 0 -83.333 5.83301 -120 17.5s-68.167 29.167 -94.5 52.5
s-46.833 52.333 -61.5 87s-22 75 -22 121c0 38.667 10.167 76.834 30.5 114.501s54.166 71.667 101.499 102s110.333 55.5 189 75.5s176.334 31.333 293.001 34v60c0 68.667 -14.5 119.5 -43.5 152.5s-71.167 49.5 -126.5 49.5c-40 0 -73.333 -4.66699 -100 -14
s-49.834 -19.833 -69.501 -31.5l-54.5 -31.5c-16.667 -9.33301 -35 -14 -55 -14c-16.667 0 -31 4.5 -43 13.5s-21.667 19.5 -29 31.5l-45 79c118 108 260.333 162 427 162c60 0 113.5 -9.83301 160.5 -29.5s86.833 -47 119.5 -82s57.5 -76.833 74.5 -125.5
s25.5 -102 25.5 -160v-648zM455.998 154c25.333 0 48.665 2.33203 69.998 6.99902s41.5 11.667 60.5 21s37.333 20.833 55 34.5s35.5 29.834 53.5 48.501v173c-72 -3.33301 -132.167 -9.5 -180.5 -18.5s-87.166 -20.5 -116.499 -34.5s-50.166 -30.333 -62.499 -49
s-18.5 -39 -18.5 -61c0 -43.333 12.833 -74.333 38.5 -93s59.167 -28 100.5 -28z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="1341" 
d="M1140 341c14.667 0 27.3379 -5.66699 38.0049 -17l106 -115c-58.667 -72.667 -130.834 -128.334 -216.501 -167.001s-188.5 -58 -308.5 -58c-107.333 0 -203.833 18.333 -289.5 55s-158.834 87.667 -219.501 153s-107.167 143.333 -139.5 234s-48.5 189.667 -48.5 297
c0 108.667 18 208.167 54 298.5s86.667 168.166 152 233.499s143.5 116.166 234.5 152.499s191.5 54.5 301.5 54.5c107.333 0 201.166 -17.167 281.499 -51.5s149.166 -79.833 206.499 -136.5l-90 -125c-5.33301 -8 -12.166 -15 -20.499 -21s-19.833 -9 -34.5 -9
c-15.333 0 -31 6 -47 18s-36.333 25 -61 39s-55.834 27 -93.501 39s-85.5 18 -143.5 18c-68 0 -130.5 -11.833 -187.5 -35.5s-106 -57.5 -147 -101.5s-73 -97.5 -96 -160.5s-34.5 -133.833 -34.5 -212.5c0 -81.333 11.5 -153.666 34.5 -216.999
s54.167 -116.666 93.5 -159.999s85.666 -76.5 138.999 -99.5s110.666 -34.5 171.999 -34.5c36.667 0 69.834 2 99.501 6s57 10.333 82 19s48.667 19.834 71 33.501s44.5 30.5 66.5 50.5c6.66699 6 13.667 10.833 21 14.5s15.333 5.5 24 5.5zM1181 1791l-296 -216.001
c-7.33301 -5.33301 -14.166 -9.66602 -20.499 -12.999s-12.666 -6 -18.999 -8s-13 -3.33301 -20 -4s-15.167 -1 -24.5 -1h-194l196 202c8 8 15.5 14.667 22.5 20s14.167 9.5 21.5 12.5s15.666 5 24.999 6s20.666 1.5 33.999 1.5h275z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="954" 
d="M853 809c-7.33301 -9.33301 -14.4961 -16.668 -21.4961 -22.001s-17.167 -8 -30.5 -8c-12.667 0 -25 3.83301 -37 11.5s-26.333 16.334 -43 26.001s-36.5 18.334 -59.5 26.001s-51.5 11.5 -85.5 11.5c-43.333 0 -81.333 -7.83301 -114 -23.5
s-59.834 -38.167 -81.501 -67.5s-37.834 -64.833 -48.501 -106.5s-16 -88.834 -16 -141.501c0 -54.667 5.83301 -103.334 17.5 -146.001s28.5 -78.5 50.5 -107.5s48.667 -51 80 -66s66.666 -22.5 105.999 -22.5s71.166 4.83301 95.499 14.5s44.833 20.334 61.5 32.001
s31.167 22.334 43.5 32.001s26.166 14.5 41.499 14.5c20 0 35 -7.66699 45 -23l71 -90c-27.333 -32 -57 -58.833 -89 -80.5s-65.167 -39 -99.5 -52s-69.833 -22.167 -106.5 -27.5s-73 -8 -109 -8c-63.333 0 -123 11.833 -179 35.5s-104.833 58.167 -146.5 103.5
s-74.667 100.833 -99 166.5s-36.5 140.5 -36.5 224.5c0 75.333 10.833 145.166 32.5 209.499s53.5 120 95.5 167s94 83.833 156 110.5s133.333 40 214 40c76.667 0 143.834 -12.333 201.501 -37s109.5 -60 155.5 -106zM905.004 1462l-246.998 -261
c-12.667 -13.333 -24.834 -22.5 -36.501 -27.5s-26.834 -7.5 -45.501 -7.5h-148l142 243c10 17.333 21.667 30.5 35 39.5s33.333 13.5 60 13.5h240z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="1158" 
d="M1007 -187c12 0 19.666 -5.33203 22.999 -15.999l38 -91c-22 -14.667 -49.5 -26.834 -82.5 -36.501s-67.833 -14.5 -104.5 -14.5c-65.333 0 -115.666 13.667 -150.999 41s-53 62.666 -53 105.999c0 36.667 12.167 72 36.5 106s59.166 64.667 104.499 92h-672v1446h912
v-214h-641v-401h505v-207h-505v-409h641v-215h-91c-13.333 -7.33301 -26.333 -15.666 -39 -24.999s-24 -19.833 -34 -31.5s-18 -24.167 -24 -37.5s-9 -27.333 -9 -42c0 -20 6.33301 -35.833 19 -47.5s30.334 -17.5 53.001 -17.5c12.667 0 23 0.666992 31 2s14.5 3 19.5 5
s9.33301 3.66699 13 5s7.16699 2 10.5 2z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="1069" 
d="M745 -187c12 0 19.6641 -5.33008 22.9971 -15.9971l38 -91c-22 -14.667 -49.5 -26.834 -82.5 -36.501s-67.833 -14.5 -104.5 -14.5c-65.333 0 -115.666 13.667 -150.999 41s-53 62.666 -53 105.999c0 34 10.333 66.667 31 98s50 60 88 86
c-67.333 3.33301 -129.666 17.666 -186.999 42.999s-107 61.166 -149 107.499s-75 102.833 -99 169.5s-36 143 -36 229c0 70 11.333 135.833 34 197.5s55.167 115.334 97.5 161.001s94 81.834 155 108.501s129.833 40 206.5 40c64.667 0 124.167 -10.333 178.5 -31
s101.166 -50.834 140.499 -90.501s70 -88.334 92 -146.001s33 -123.5 33 -197.5c0 -18.667 -0.833008 -34.167 -2.5 -46.5s-4.66699 -22 -9 -29s-10.166 -12 -17.499 -15s-16.666 -4.5 -27.999 -4.5h-634c7.33301 -105.333 35.666 -182.666 84.999 -231.999
s114.666 -74 195.999 -74c40 0 74.5 4.66699 103.5 14s54.333 19.666 76 30.999s40.667 21.666 57 30.999s32.166 14 47.499 14c20.667 0 35.667 -7.66699 45 -23l72 -90c-39.333 -45.333 -84.666 -80.333 -135.999 -105s-103.666 -41.667 -156.999 -51
c-12.667 -7.33301 -25 -15.666 -37 -24.999s-22.5 -19.666 -31.5 -30.999s-16.333 -23.5 -22 -36.5s-8.5 -26.5 -8.5 -40.5c0 -20 6.33301 -35.833 19 -47.5s30.334 -17.5 53.001 -17.5c12.667 0 23 0.666992 31 2s14.5 3 19.5 5s9.33301 3.66699 13 5s7.16699 2 10.5 2z
M560.997 865.003c-72 0 -128.333 -20.3301 -169 -60.9971s-66.667 -98.334 -78 -173.001h464c0 32 -4.33301 62.167 -13 90.5s-22 53.166 -40 74.499s-40.667 38.166 -68 50.499s-59.333 18.5 -96 18.5z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="542" 
d="M395 1026v-1026h-247v1026h247z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="1101" 
d="M483 870l358 179v-180c0 -24.667 -11 -42.667 -33 -54l-325 -169v-424h579v-222h-848v528l-180 -90v185c0 21.333 10.333 37.333 31 48l149 77v698h269v-576z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="678" 
d="M463 1486v-557l163 66v-142c0 -27.333 -11.333 -45.333 -34 -54l-129 -55v-744h-247v657l-164 -66v146c0 23.333 10.333 39.333 31 48l133 56v645h247z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="1512" 
d="M287 1446c12 0 22 -0.498047 30 -1.49805s15.167 -3 21.5 -6s12.5 -7.33301 18.5 -13s12.667 -13.167 20 -22.5l759 -967c-2.66699 23.333 -4.5 46.166 -5.5 68.499s-1.5 43.166 -1.5 62.499v879h237v-1446h-139c-21.333 -0 -39 3.33301 -53 10s-27.667 18.667 -41 36
l-756 963c2 -21.333 3.5 -42.5 4.5 -63.5s1.5 -40.167 1.5 -57.5v-888h-237v1446h141v0zM1157 1791l-296 -216.001c-7.33301 -5.33301 -14.166 -9.66602 -20.499 -12.999s-12.666 -6 -18.999 -8s-13 -3.33301 -20 -4s-15.167 -1 -24.5 -1h-194l196 202
c8 8 15.5 14.667 22.5 20s14.167 9.5 21.5 12.5s15.666 5 24.999 6s20.666 1.5 33.999 1.5h275z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="1137" 
d="M132 0l-0.000976562 1026h151c32 0 53 -15 63 -45l17 -81c20.667 21.333 42.5 40.666 65.5 57.999s47.333 32.333 73 45s53.167 22.334 82.5 29.001s61.333 10 96 10c56 0 105.667 -9.5 149 -28.5s79.5 -45.667 108.5 -80s51 -75.333 66 -123s22.5 -100.167 22.5 -157.5
v-653h-247v653c0 62.667 -14.5 111.167 -43.5 145.5s-72.5 51.5 -130.5 51.5c-42.667 0 -82.667 -9.66699 -120 -29s-72.666 -45.666 -105.999 -78.999v-742h-247zM929.999 1462l-246.998 -261c-12.667 -13.333 -24.834 -22.5 -36.501 -27.5s-26.834 -7.5 -45.501 -7.5h-148
l142 243c10 17.333 21.667 30.5 35 39.5s33.333 13.5 60 13.5h240z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="2163" 
d="M2075 1446l-0.00195312 -214.001h-641v-401h506v-207h-506v-409h641v-215h-880v185c-58 -62.667 -126.167 -111.667 -204.5 -147s-166.166 -53 -263.499 -53c-98.667 0 -188.5 18.5 -269.5 55.5s-150.5 88.333 -208.5 154s-102.833 143.667 -134.5 234
s-47.5 188.5 -47.5 294.5s15.833 204.167 47.5 294.5s76.5 168.5 134.5 234.5s127.5 117.5 208.5 154.5s170.833 55.5 269.5 55.5c96.667 0 184.334 -17.667 263.001 -53s147 -84.666 205 -147.999v185h880zM1164 722.999c0 79.333 -9.33301 150.833 -28 214.5
s-45.667 118 -81 163s-78.333 79.5 -129 103.5s-107.667 36 -171 36c-64 0 -121.667 -12 -173 -36s-94.666 -58.5 -129.999 -103.5s-62.5 -99.333 -81.5 -163s-28.5 -135.167 -28.5 -214.5s9.5 -150.833 28.5 -214.5s46.167 -117.834 81.5 -162.501s78.666 -79 129.999 -103
s109 -36 173 -36c63.333 0 120.333 12 171 36s93.667 58.333 129 103s62.333 98.834 81 162.501s28 135.167 28 214.5z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1749" 
d="M1272 1042c58 0 112.005 -11.1631 162.005 -33.4961s93.167 -54.5 129.5 -96.5s64.833 -93 85.5 -153s31 -127.333 31 -202c0 -18.667 -0.833008 -34.167 -2.5 -46.5s-4.5 -22 -8.5 -29s-9.5 -12 -16.5 -15s-16.167 -4.5 -27.5 -4.5h-598
c9.33301 -98 37.166 -170.167 83.499 -216.5s106.166 -69.5 179.499 -69.5c37.333 0 69.333 4.66699 96 14s50.167 19.666 70.5 30.999l54.5 31c16 9.33301 31.667 14 47 14c17.333 0 32 -7.66699 44 -23l72 -90c-27.333 -32 -57.333 -58.833 -90 -80.5
s-66.834 -39 -102.501 -52s-71.834 -22.167 -108.501 -27.5s-72.334 -8 -107.001 -8c-72 0 -139.5 16.167 -202.5 48.5s-114.833 83.166 -155.5 152.499c-40 -64.667 -92.167 -114.334 -156.5 -149.001s-139.166 -52 -224.499 -52c-68 0 -130.5 12.333 -187.5 37
s-106.167 60 -147.5 106s-73.5 101.667 -96.5 167s-34.5 138.666 -34.5 219.999c0 80.667 11.667 153.667 35 219s55.833 120.666 97.5 165.999s91.667 80.333 150 105s122.5 37 192.5 37c82.667 0 155.167 -17.167 217.5 -51.5s113.166 -83.166 152.499 -146.499
c36.667 60 85.667 108 147 144s134.333 54 219 54zM550.005 175.004c78.667 0 136.999 28.667 174.999 86s57 141.333 57 252s-19 195 -57 253s-96.333 87 -175 87c-80 0 -139 -29.167 -177 -87.5s-57 -142.5 -57 -252.5c0 -110.667 18.833 -194.667 56.5 -252
s96.834 -86 177.501 -86zM1261 865.004c-70 0 -124.168 -22.1689 -162.501 -66.502s-61.833 -106.833 -70.5 -187.5h433c0 32.667 -4 64.334 -12 95.001s-20.167 57.834 -36.5 81.501s-37.166 42.5 -62.499 56.5s-55 21 -89 21z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="1107" 
d="M921 1183c-7.33301 -14.667 -15.8311 -24.9961 -25.498 -30.9961s-21.167 -9 -34.5 -9s-28.333 5.16699 -45 15.5s-36.334 21.833 -59.001 34.5s-49.167 24.167 -79.5 34.5s-66.166 15.5 -107.499 15.5c-37.333 0 -69.833 -4.5 -97.5 -13.5s-51 -21.5 -70 -37.5
s-33.167 -35.167 -42.5 -57.5s-14 -46.833 -14 -73.5c0 -34 9.5 -62.333 28.5 -85s44.167 -42 75.5 -58s67 -30.333 107 -43l122.5 -40.5c41.667 -14.333 82.5 -31 122.5 -50s75.667 -43 107 -72s56.5 -64.5 75.5 -106.5s28.5 -93 28.5 -153
c0 -65.333 -11.167 -126.5 -33.5 -183.5s-55 -106.667 -98 -149s-95.5 -75.666 -157.5 -99.999s-133 -36.5 -213 -36.5c-46 0 -91.333 4.5 -136 13.5s-87.5 21.833 -128.5 38.5s-79.333 36.667 -115 60s-67.5 49.333 -95.5 78l78 129
c7.33301 9.33301 16.166 17.166 26.499 23.499s21.833 9.5 34.5 9.5c16.667 0 34.667 -6.83301 54 -20.5s42.166 -28.834 68.499 -45.501s57.333 -31.834 93 -45.501s78.5 -20.5 128.5 -20.5c76.667 0 136 18.167 178 54.5s63 88.5 63 156.5c0 38 -9.5 69 -28.5 93
s-44.167 44.167 -75.5 60.5s-67 30.166 -107 41.499s-80.667 23.666 -122 36.999s-82 29.333 -122 48s-75.667 43 -107 73s-56.5 67.5 -75.5 112.5s-28.5 100.5 -28.5 166.5c0 52.667 10.5 104 31.5 154s51.667 94.333 92 133s89.833 69.667 148.5 93s125.667 35 201 35
c85.333 0 164 -13.333 236 -40s133.333 -64 184 -112zM977.002 1791l-296 -216.001c-7.33301 -5.33301 -14.166 -9.66602 -20.499 -12.999s-12.666 -6 -18.999 -8s-13 -3.33301 -20 -4s-15.167 -1 -24.5 -1h-194l196 202c8 8 15.5 14.667 22.5 20s14.167 9.5 21.5 12.5
s15.666 5 24.999 6s20.666 1.5 33.999 1.5h275z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="874" 
d="M741 826c-6.66699 -10.667 -13.665 -18.1641 -20.998 -22.4971s-16.666 -6.5 -27.999 -6.5c-12 0 -24.833 3.33301 -38.5 10l-47.5 22.5c-18 8.33301 -38.5 15.833 -61.5 22.5s-50.167 10 -81.5 10c-48.667 0 -87 -10.333 -115 -31s-42 -47.667 -42 -81
c0 -22 7.16699 -40.5 21.5 -55.5s33.333 -28.167 57 -39.5s50.5 -21.5 80.5 -30.5s60.667 -18.833 92 -29.5s62 -22.834 92 -36.501s56.833 -31 80.5 -52s42.667 -46.167 57 -75.5s21.5 -64.666 21.5 -105.999c0 -49.333 -9 -94.833 -27 -136.5s-44.333 -77.667 -79 -108
s-77.5 -54 -128.5 -71s-109.5 -25.5 -175.5 -25.5c-35.333 0 -69.833 3.16699 -103.5 9.5s-66 15.166 -97 26.499s-59.667 24.666 -86 39.999s-49.5 32 -69.5 50l57 94c7.33301 11.333 16 20 26 26s22.667 9 38 9s29.833 -4.33301 43.5 -13s29.5 -18 47.5 -28
s39.167 -19.333 63.5 -28s55.166 -13 92.499 -13c29.333 0 54.5 3.5 75.5 10.5s38.333 16.167 52 27.5s23.667 24.5 30 39.5s9.5 30.5 9.5 46.5c0 24 -7.16699 43.667 -21.5 59s-33.333 28.666 -57 39.999s-50.667 21.5 -81 30.5s-61.333 18.833 -93 29.5
s-62.667 23.167 -93 37.5s-57.333 32.5 -81 54.5s-42.667 49 -57 81s-21.5 70.667 -21.5 116c0 42 8.33301 82 25 120s41.167 71.167 73.5 99.5s72.666 51 120.999 68s104.166 25.5 167.499 25.5c70.667 0 135 -11.667 193 -35s106.333 -54 145 -92zM815.002 1462
l-246.998 -261c-12.667 -13.333 -24.834 -22.5 -36.501 -27.5s-26.834 -7.5 -45.501 -7.5h-148l142 243c10 17.333 21.667 30.5 35 39.5s33.333 13.5 60 13.5h240z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="1073" 
d="M921 1183c-7.33301 -14.667 -15.8311 -24.9961 -25.498 -30.9961s-21.167 -9 -34.5 -9s-28.333 5.16699 -45 15.5s-36.334 21.833 -59.001 34.5s-49.167 24.167 -79.5 34.5s-66.166 15.5 -107.499 15.5c-37.333 0 -69.833 -4.5 -97.5 -13.5s-51 -21.5 -70 -37.5
s-33.167 -35.167 -42.5 -57.5s-14 -46.833 -14 -73.5c0 -34 9.5 -62.333 28.5 -85s44.167 -42 75.5 -58s67 -30.333 107 -43l122.5 -40.5c41.667 -14.333 82.5 -31 122.5 -50s75.667 -43 107 -72s56.5 -64.5 75.5 -106.5s28.5 -93 28.5 -153
c0 -65.333 -11.167 -126.5 -33.5 -183.5s-55 -106.667 -98 -149s-95.5 -75.666 -157.5 -99.999s-133 -36.5 -213 -36.5c-46 0 -91.333 4.5 -136 13.5s-87.5 21.833 -128.5 38.5s-79.333 36.667 -115 60s-67.5 49.333 -95.5 78l78 129
c7.33301 9.33301 16.166 17.166 26.499 23.499s21.833 9.5 34.5 9.5c16.667 0 34.667 -6.83301 54 -20.5s42.166 -28.834 68.499 -45.501s57.333 -31.834 93 -45.501s78.5 -20.5 128.5 -20.5c76.667 0 136 18.167 178 54.5s63 88.5 63 156.5c0 38 -9.5 69 -28.5 93
s-44.167 44.167 -75.5 60.5s-67 30.166 -107 41.499s-80.667 23.666 -122 36.999s-82 29.333 -122 48s-75.667 43 -107 73s-56.5 67.5 -75.5 112.5s-28.5 100.5 -28.5 166.5c0 52.667 10.5 104 31.5 154s51.667 94.333 92 133s89.833 69.667 148.5 93s125.667 35 201 35
c85.333 0 164 -13.333 236 -40s133.333 -64 184 -112zM217.002 1768h187c10 0 21 -1.33301 33 -4s21.667 -6.66699 29 -12l101 -67c2.66699 -1.33301 5.16699 -2.83301 7.5 -4.5l7.5 -5.5l7.5 5.5c2.33301 1.66699 4.83301 3.16699 7.5 4.5l101 67
c7.33301 5.33301 17 9.33301 29 12s23 4 33 4h187l-242 -219h-246z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="874" 
d="M741 826c-6.66699 -10.667 -13.665 -18.1641 -20.998 -22.4971s-16.666 -6.5 -27.999 -6.5c-12 0 -24.833 3.33301 -38.5 10l-47.5 22.5c-18 8.33301 -38.5 15.833 -61.5 22.5s-50.167 10 -81.5 10c-48.667 0 -87 -10.333 -115 -31s-42 -47.667 -42 -81
c0 -22 7.16699 -40.5 21.5 -55.5s33.333 -28.167 57 -39.5s50.5 -21.5 80.5 -30.5s60.667 -18.833 92 -29.5s62 -22.834 92 -36.501s56.833 -31 80.5 -52s42.667 -46.167 57 -75.5s21.5 -64.666 21.5 -105.999c0 -49.333 -9 -94.833 -27 -136.5s-44.333 -77.667 -79 -108
s-77.5 -54 -128.5 -71s-109.5 -25.5 -175.5 -25.5c-35.333 0 -69.833 3.16699 -103.5 9.5s-66 15.166 -97 26.499s-59.667 24.666 -86 39.999s-49.5 32 -69.5 50l57 94c7.33301 11.333 16 20 26 26s22.667 9 38 9s29.833 -4.33301 43.5 -13s29.5 -18 47.5 -28
s39.167 -19.333 63.5 -28s55.166 -13 92.499 -13c29.333 0 54.5 3.5 75.5 10.5s38.333 16.167 52 27.5s23.667 24.5 30 39.5s9.5 30.5 9.5 46.5c0 24 -7.16699 43.667 -21.5 59s-33.333 28.666 -57 39.999s-50.667 21.5 -81 30.5s-61.333 18.833 -93 29.5
s-62.667 23.167 -93 37.5s-57.333 32.5 -81 54.5s-42.667 49 -57 81s-21.5 70.667 -21.5 116c0 42 8.33301 82 25 120s41.167 71.167 73.5 99.5s72.666 51 120.999 68s104.166 25.5 167.499 25.5c70.667 0 135 -11.667 193 -35s106.333 -54 145 -92zM576.002 1168
l-231.998 -0.000976562l-229 278h171c10 0 19.5 -1.66699 28.5 -5s16.167 -7.33301 21.5 -12l106 -96c6 -5.33301 13 -13 21 -23c3.33301 4.66699 7 8.83398 11 12.501l11 10.5l105 96c5.33301 4.66699 12.666 8.66699 21.999 12s18.666 5 27.999 5h165z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="1309" 
d="M789 562l0.000976562 -562h-269v562l-527 884h237c23.333 0 41.833 -5.66699 55.5 -17s25.167 -25.666 34.5 -42.999l265 -483c15.333 -28.667 28.666 -55.834 39.999 -81.501s21.666 -50.834 30.999 -75.501c8.66699 25.333 18.5 50.833 29.5 76.5
s24.167 52.5 39.5 80.5l263 483c7.33301 14.667 18.333 28.334 33 41.001s33.334 19 56.001 19h238zM575.001 1667c0 -18 -3.66699 -35 -11 -51s-17.333 -30 -30 -42s-27.334 -21.333 -44.001 -28s-34.334 -10 -53.001 -10c-17.333 0 -33.833 3.33301 -49.5 10
s-29.5 16 -41.5 28s-21.5 26 -28.5 42s-10.5 33 -10.5 51s3.5 35.167 10.5 51.5s16.5 30.666 28.5 42.999s25.833 22.166 41.5 29.499s32.167 11 49.5 11c18.667 0 36.334 -3.66699 53.001 -11s31.334 -17.166 44.001 -29.499s22.667 -26.666 30 -42.999s11 -33.5 11 -51.5z
M1007 1667c0 -18 -3.5 -35 -10.5 -51s-16.5 -30 -28.5 -42s-26.167 -21.333 -42.5 -28s-33.833 -10 -52.5 -10c-18 0 -35.167 3.33301 -51.5 10s-30.666 16 -42.999 28s-22 26 -29 42s-10.5 33 -10.5 51s3.5 35.167 10.5 51.5s16.667 30.666 29 42.999
s26.666 22.166 42.999 29.499s33.5 11 51.5 11c18.667 0 36.167 -3.66699 52.5 -11s30.5 -17.166 42.5 -29.499s21.5 -26.666 28.5 -42.999s10.5 -33.5 10.5 -51.5z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="1234" 
d="M1179 1446v-98.999c0 -30.667 -8.66699 -58.667 -26 -84l-742 -1048h750v-215h-1099v106c0 13.333 2.33301 26.166 7 38.499s10.667 23.833 18 34.5l744 1053h-722v214h1070zM1032 1791l-296 -216.001c-7.33301 -5.33301 -14.166 -9.66602 -20.499 -12.999
s-12.666 -6 -18.999 -8s-13 -3.33301 -20 -4s-15.167 -1 -24.5 -1h-194l196 202c8 8 15.5 14.667 22.5 20s14.167 9.5 21.5 12.5s15.666 5 24.999 6s20.666 1.5 33.999 1.5h275z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="939" 
d="M874 924c0 -17.333 -3.16602 -34.168 -9.49902 -50.501s-13.833 -30.166 -22.5 -41.499l-488 -642h506v-190h-793v103c0 11.333 2.66699 24.833 8 40.5s13.666 30.5 24.999 44.5l492 649h-496v189h778v-102zM844.001 1462l-246.998 -261
c-12.667 -13.333 -24.834 -22.5 -36.501 -27.5s-26.834 -7.5 -45.501 -7.5h-148l142 243c10 17.333 21.667 30.5 35 39.5s33.333 13.5 60 13.5h240z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="1234" 
d="M1179 1446v-98.999c0 -30.667 -8.66699 -58.667 -26 -84l-742 -1048h750v-215h-1099v106c0 13.333 2.33301 26.166 7 38.499s10.667 23.833 18 34.5l744 1053h-722v214h1070zM799 1695c0 -20 -4.16699 -38.833 -12.5 -56.5s-19.5 -33.334 -33.5 -47.001
s-30.333 -24.5 -49 -32.5s-38.334 -12 -59.001 -12c-19.333 0 -38 4 -56 12s-33.833 18.833 -47.5 32.5s-24.5 29.334 -32.5 47.001s-12 36.5 -12 56.5c0 20.667 4 40.167 12 58.5s18.833 34.333 32.5 48s29.5 24.5 47.5 32.5s36.667 12 56 12
c20.667 0 40.334 -4 59.001 -12s35 -18.833 49 -32.5s25.167 -29.667 33.5 -48s12.5 -37.833 12.5 -58.5z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="939" 
d="M874 924c0 -17.333 -3.16602 -34.168 -9.49902 -50.501s-13.833 -30.166 -22.5 -41.499l-488 -642h506v-190h-793v103c0 11.333 2.66699 24.833 8 40.5s13.666 30.5 24.999 44.5l492 649h-496v189h778v-102zM658.001 1329c0 -21.333 -4.33301 -41.5 -13 -60.5
s-20.167 -35.5 -34.5 -49.5s-31.333 -25.167 -51 -33.5s-40.5 -12.5 -62.5 -12.5c-21.333 0 -41.333 4.16699 -60 12.5s-35 19.5 -49 33.5s-25.167 30.5 -33.5 49.5s-12.5 39.167 -12.5 60.5c0 22 4.16699 42.5 12.5 61.5s19.5 35.667 33.5 50s30.333 25.666 49 33.999
s38.667 12.5 60 12.5c22 0 42.833 -4.16699 62.5 -12.5s36.667 -19.666 51 -33.999s25.833 -31 34.5 -50s13 -39.5 13 -61.5z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="1234" 
d="M1179 1446v-98.999c0 -30.667 -8.66699 -58.667 -26 -84l-742 -1048h750v-215h-1099v106c0 13.333 2.33301 26.166 7 38.499s10.667 23.833 18 34.5l744 1053h-722v214h1070zM283 1767h187c10 0 21 -1.33301 33 -4s21.667 -6.66699 29 -12l101 -67
c2.66699 -1.33301 5.16699 -2.83301 7.5 -4.5l7.5 -5.5l7.5 5.5c2.33301 1.66699 4.83301 3.16699 7.5 4.5l101 67c7.33301 5.33301 17 9.33301 29 12s23 4 33 4h187l-242 -219h-246z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="939" 
d="M874 924c0 -17.333 -3.16602 -34.168 -9.49902 -50.501s-13.833 -30.166 -22.5 -41.499l-488 -642h506v-190h-793v103c0 11.333 2.66699 24.833 8 40.5s13.666 30.5 24.999 44.5l492 649h-496v189h778v-102zM614.001 1168l-231.998 -0.000976562l-229 278h171
c10 0 19.5 -1.66699 28.5 -5s16.167 -7.33301 21.5 -12l106 -96c6 -5.33301 13 -13 21 -23c3.33301 4.66699 7 8.83398 11 12.501l11 10.5l105 96c5.33301 4.66699 12.666 8.66699 21.999 12s18.666 5 27.999 5h165z" />
    <glyph glyph-name="florin" unicode="&#x192;" 
d="M706 745l-83.002 -687.997c-8.66699 -70.667 -25.5 -131.334 -50.5 -182.001s-59 -92.334 -102 -125.001s-95.333 -56.667 -157 -72s-133.5 -23 -215.5 -23v129c0 50.667 26.333 76 79 76c26.667 0 51.334 3.66699 74.001 11s42.667 19 60 35s31.666 37 42.999 63
s19.333 57.667 24 95l83 676l-123 18c-37.333 8 -56 28.667 -56 62v101h201l18 148c16.667 133.333 67.334 233.666 152.001 300.999s209 101 373 101v-135c0 -26 -6.16699 -44.167 -18.5 -54.5s-32.166 -15.5 -59.499 -15.5s-52.5 -3.66699 -75.5 -11s-43.5 -19 -61.5 -35
s-33 -37 -45 -63s-20.333 -57.667 -25 -95l-19 -141h294v-176h-310z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="638" 
d="M664 1168l-164.999 0.000976562c-20.667 0 -37.334 6 -50.001 18l-105 95l-10.5 10c-3.66699 3.33301 -7.16699 7.33301 -10.5 12c-3.33301 -4.66699 -6.83301 -8.66699 -10.5 -12l-10.5 -10l-107 -95c-5.33301 -4.66699 -12.5 -8.83398 -21.5 -12.501
s-18.5 -5.5 -28.5 -5.5h-171l229 278h232z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="638" 
d="M435 1168l-231.998 -0.000976562l-229 278h171c10 0 19.5 -1.66699 28.5 -5s16.167 -7.33301 21.5 -12l106 -96c6 -5.33301 13 -13 21 -23c3.33301 4.66699 7 8.83398 11 12.501l11 10.5l105 96c5.33301 4.66699 12.666 8.66699 21.999 12s18.666 5 27.999 5h165z" />
    <glyph glyph-name="uni02C9" unicode="&#x2c9;" horiz-adv-x="638" 
d="M20 1372h598v-158h-598v158z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="638" 
d="M319 1161c-57.333 0 -105.169 7.5 -143.502 22.5s-69.333 35.5 -93 61.5s-40.5 56.333 -50.5 91s-15 71.334 -15 110.001h173c0 -20 1.83301 -38 5.5 -54s10.334 -29.5 20.001 -40.5s22.834 -19.5 39.501 -25.5s38 -9 64 -9s47.333 3 64 9s29.834 14.5 39.501 25.5
s16.334 24.5 20.001 40.5s5.5 34 5.5 54h173c0 -38.667 -5 -75.334 -15 -110.001s-26.833 -65 -50.5 -91s-54.834 -46.5 -93.501 -61.5s-86.334 -22.5 -143.001 -22.5z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="638" 
d="M478 1329c0 -21.333 -4.33301 -41.5 -13 -60.5s-20.167 -35.5 -34.5 -49.5s-31.333 -25.167 -51 -33.5s-40.5 -12.5 -62.5 -12.5c-21.333 0 -41.333 4.16699 -60 12.5s-35 19.5 -49 33.5s-25.167 30.5 -33.5 49.5s-12.5 39.167 -12.5 60.5c0 22 4.16699 42.5 12.5 61.5
s19.5 35.667 33.5 50s30.333 25.666 49 33.999s38.667 12.5 60 12.5c22 0 42.833 -4.16699 62.5 -12.5s36.667 -19.666 51 -33.999s25.833 -31 34.5 -50s13 -39.5 13 -61.5z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="638" 
d="M96 1325c0 30.667 6 58.667 18 84s28.167 47 48.5 65s43.833 32 70.5 42s54.667 15 84 15c30 0 58.667 -5 86 -15s51.333 -24 72 -42s37.167 -39.667 49.5 -65s18.5 -53.333 18.5 -84c0 -30 -6.16699 -57.333 -18.5 -82s-28.833 -45.834 -49.5 -63.501
s-44.667 -31.334 -72 -41.001s-56 -14.5 -86 -14.5c-29.333 0 -57.333 4.83301 -84 14.5s-50.167 23.334 -70.5 41.001s-36.5 38.834 -48.5 63.501s-18 52 -18 82zM230 1325c0 -25.333 7.83301 -46.5 23.5 -63.5s38.167 -25.5 67.5 -25.5c26.667 0 48 8.5 64 25.5
s24 38.167 24 63.5c0 28 -8 50.167 -24 66.5s-37.333 24.5 -64 24.5c-29.333 0 -51.833 -8.16699 -67.5 -24.5s-23.5 -38.5 -23.5 -66.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="638" 
d="M461 -187c12 0 19.665 -5.33105 22.998 -15.998l38 -91c-22 -14.667 -49.5 -26.834 -82.5 -36.501s-67.833 -14.5 -104.5 -14.5c-65.333 0 -115.666 13.667 -150.999 41s-53 62.666 -53 105.999c0 39.333 14 77.166 42 113.499s68 68.5 120 96.5l128 -12
c-13.333 -7.33301 -26.333 -15.666 -39 -24.999s-24 -19.833 -34 -31.5s-18 -24.167 -24 -37.5s-9 -27.333 -9 -42c0 -20 6.33301 -35.833 19 -47.5s30.334 -17.5 53.001 -17.5c12.667 0 23 0.666992 31 2s14.5 3 19.5 5s9.33301 3.66699 13 5s7.16699 2 10.5 2z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="638" 
d="M426 1359c20 0 35.668 5.50098 47.001 16.501s17 30.833 17 59.5h150c0 -37.333 -5.16699 -71.166 -15.5 -101.499s-24.5 -56.333 -42.5 -78s-39.5 -38.334 -64.5 -50.001s-52.5 -17.5 -82.5 -17.5c-24 0 -46.333 4.33301 -67 13s-40 18 -58 28l-50.5 28
c-15.667 8.66699 -30.167 13 -43.5 13c-19.333 0 -34.5 -5.83301 -45.5 -17.5s-16.5 -31.5 -16.5 -59.5h-153c0 37.333 5.16699 71.166 15.5 101.499s24.833 56.333 43.5 78s40.5 38.5 65.5 50.5s52.167 18 81.5 18c24 0 46.5 -4.33301 67.5 -13s40.5 -18 58.5 -28l50 -28
c15.333 -8.66699 29.666 -13 42.999 -13z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="638" 
d="M451 1462l-198.999 -261c-10.667 -13.333 -22.334 -22.5 -35.001 -27.5s-28.334 -7.5 -47.001 -7.5h-101l126 243c9.33301 17.333 20.833 30.5 34.5 39.5s33.834 13.5 60.501 13.5h161zM800.001 1462l-252 -261c-12.667 -12.667 -24.667 -21.667 -36 -27
s-26.666 -8 -45.999 -8h-116l177 243c6 8.66699 11.833 16.167 17.5 22.5s12 11.833 19 16.5s15.167 8.16699 24.5 10.5s20.666 3.5 33.999 3.5h178z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="1288" 
d="M1251 1026l-0.00292969 -98.001c0 -21.333 -6.5 -39.666 -19.5 -54.999s-32.167 -23 -57.5 -23h-107v-850h-247v850h-339v-584c0 -42 -5.16699 -80.167 -15.5 -114.5s-26.666 -63.833 -48.999 -88.5s-51.166 -43.667 -86.499 -57s-78 -20 -128 -20
c-24 0 -49.167 2.33301 -75.5 7s-51.166 13.334 -74.499 26.001l7 104c2 13.333 7.16699 23.333 15.5 30s25.833 10 52.5 10c40.667 0 68.667 8.16699 84 24.5s23 43.5 23 81.5v581h-179v88c0 10 2 20.167 6 30.5s10 19.666 18 27.999s17.667 15.333 29 21
s24.333 8.5 39 8.5h1104z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="1137" 
d="M163 687h811v-175h-811v175z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1684" 
d="M163 687h1357v-175h-1357v175z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="450" 
d="M166 977c-45.333 74 -67.999 148.668 -67.999 224.001c0 66.667 16.833 130.5 50.5 191.5s82.5 116.833 146.5 167.5l77 -47c6.66699 -4 11.334 -8.83301 14.001 -14.5s4 -11.167 4 -16.5c0 -6 -1.33301 -11.667 -4 -17s-5.66699 -10 -9 -14
c-8.66699 -10 -17.834 -21.667 -27.501 -35s-18.5 -27.833 -26.5 -43.5s-14.667 -32.834 -20 -51.501s-8 -38.667 -8 -60c0 -22.667 3.66699 -46.834 11 -72.501s19.666 -52.5 36.999 -80.5c6 -9.33301 9 -18.666 9 -27.999c0 -21.333 -12 -36.333 -36 -45z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="450" 
d="M309 1532c23.333 -37.333 40.499 -74.6709 51.499 -112.004s16.5 -74.666 16.5 -111.999c0 -66.667 -16.833 -130.334 -50.5 -191.001s-82.167 -116.667 -145.5 -168l-77 47c-6.66699 4 -11.334 8.83301 -14.001 14.5s-4 11.167 -4 16.5
c0 12.667 4.33301 23.334 13 32.001c8.66699 10 17.834 21.5 27.501 34.5s18.5 27.333 26.5 43s14.5 32.834 19.5 51.501s7.5 38.667 7.5 60c0 22.667 -3.66699 46.834 -11 72.501s-19.666 52.834 -36.999 81.501c-6 8.66699 -9 17.667 -9 27c0 21.333 12.333 36.333 37 45z
" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="450" 
d="M309 291c23.333 -37.333 40.499 -74.6709 51.499 -112.004s16.5 -74.666 16.5 -111.999c0 -66.667 -16.833 -130.334 -50.5 -191.001s-82.167 -116.667 -145.5 -168l-77 47c-6.66699 4 -11.334 8.83301 -14.001 14.5s-4 11.167 -4 16.5
c0 12.667 4.33301 23.334 13 32.001c8.66699 10 17.834 21.5 27.501 34.5s18.5 27.333 26.5 43s14.5 32.834 19.5 51.501s7.5 38.667 7.5 60c0 22.667 -3.66699 46.834 -11 72.501s-19.666 52.834 -36.999 81.501c-6 8.66699 -9 17.667 -9 27c0 21.333 12.333 36.333 37 45z
" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="782" 
d="M166 977c-45.333 74 -67.999 148.668 -67.999 224.001c0 66.667 16.833 130.5 50.5 191.5s82.5 116.833 146.5 167.5l77 -47c6.66699 -4 11.334 -8.83301 14.001 -14.5s4 -11.167 4 -16.5c0 -6 -1.33301 -11.667 -4 -17s-5.66699 -10 -9 -14
c-8.66699 -10 -17.834 -21.667 -27.501 -35s-18.5 -27.833 -26.5 -43.5s-14.667 -32.834 -20 -51.501s-8 -38.667 -8 -60c0 -22.667 3.66699 -46.834 11 -72.501s19.666 -52.5 36.999 -80.5c6 -9.33301 9 -18.666 9 -27.999c0 -21.333 -12 -36.333 -36 -45zM498.001 977.001
c-45.333 74 -67.999 148.668 -67.999 224.001c0 66.667 16.833 130.5 50.5 191.5s82.5 116.833 146.5 167.5l77 -47c6.66699 -4 11.334 -8.83301 14.001 -14.5s4 -11.167 4 -16.5c0 -6 -1.33301 -11.667 -4 -17s-5.66699 -10 -9 -14
c-8.66699 -10 -17.834 -21.667 -27.501 -35s-18.5 -27.833 -26.5 -43.5s-14.667 -32.834 -20 -51.501s-8 -38.667 -8 -60c0 -22.667 3.66699 -46.834 11 -72.501s19.666 -52.5 36.999 -80.5c6 -9.33301 9 -18.666 9 -27.999c0 -21.333 -12 -36.333 -36 -45z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="782" 
d="M309 1532c23.333 -37.333 40.499 -74.6699 51.499 -112.003s16.5 -74.666 16.5 -111.999c0 -66.667 -16.833 -130.334 -50.5 -191.001s-82.167 -116.667 -145.5 -168l-77 47c-6.66699 4 -11.334 8.83301 -14.001 14.5s-4 11.167 -4 16.5
c0 12.667 4.33301 23.334 13 32.001c8.66699 10 17.834 21.333 27.501 34s18.5 27 26.5 43s14.5 33.333 19.5 52s7.5 38.667 7.5 60c0 22.667 -3.66699 46.834 -11 72.501s-19.666 52.834 -36.999 81.501c-6 8.66699 -9 17.667 -9 27c0 21.333 12.333 36.333 37 45z
M640.999 1532c23.333 -37.333 40.499 -74.6699 51.499 -112.003s16.5 -74.666 16.5 -111.999c0 -66.667 -16.833 -130.334 -50.5 -191.001s-82.167 -116.667 -145.5 -168l-77 47c-6.66699 4 -11.334 8.83301 -14.001 14.5s-4 11.167 -4 16.5
c0 12.667 4.33301 23.334 13 32.001c8.66699 10 17.834 21.333 27.501 34s18.5 27 26.5 43s14.5 33.333 19.5 52s7.5 38.667 7.5 60c0 22.667 -3.66699 46.834 -11 72.501s-19.666 52.834 -36.999 81.501c-6 8.66699 -9 17.667 -9 27c0 21.333 12.333 36.333 37 45z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="782" 
d="M309 291c23.333 -37.333 40.499 -74.6699 51.499 -112.003s16.5 -74.666 16.5 -111.999c0 -66.667 -16.833 -130.334 -50.5 -191.001s-82.167 -116.667 -145.5 -168l-77 47c-6.66699 4 -11.334 8.83301 -14.001 14.5s-4 11.167 -4 16.5
c0 12.667 4.33301 23.334 13 32.001c8.66699 10 17.834 21.333 27.501 34s18.5 27 26.5 43s14.5 33.333 19.5 52s7.5 38.667 7.5 60c0 22.667 -3.66699 46.834 -11 72.501s-19.666 52.834 -36.999 81.501c-6 8.66699 -9 17.667 -9 27c0 21.333 12.333 36.333 37 45z
M640.999 290.996c23.333 -37.333 40.499 -74.6699 51.499 -112.003s16.5 -74.666 16.5 -111.999c0 -66.667 -16.833 -130.334 -50.5 -191.001s-82.167 -116.667 -145.5 -168l-77 47c-6.66699 4 -11.334 8.83301 -14.001 14.5s-4 11.167 -4 16.5
c0 12.667 4.33301 23.334 13 32.001c8.66699 10 17.834 21.333 27.501 34s18.5 27 26.5 43s14.5 33.333 19.5 52s7.5 38.667 7.5 60c0 22.667 -3.66699 46.834 -11 72.501s-19.666 52.834 -36.999 81.501c-6 8.66699 -9 17.667 -9 27c0 21.333 12.333 36.333 37 45z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" 
d="M84 913c0 14 2.16699 27.333 6.5 40s11.333 24 21 34s21.667 18 36 24s31.5 9 51.5 9c42 -0.666992 87.667 -6.33398 137 -17.001s99 -19.667 149 -27l-31 489c36.667 21.333 79.334 32 128.001 32c24 0 47 -2.66699 69 -8s41.667 -13.333 59 -24l-32 -489
c50 7.33301 99.833 16.333 149.5 27s95.5 16.334 137.5 17.001c20 0 37.333 -3 52 -9s26.667 -14 36 -24s16.333 -21.333 21 -34s7 -26 7 -40v-82h-403v-354l32 -800c-17.333 -10.667 -37 -18.667 -59 -24s-45 -8 -69 -8c-48.667 0 -91.334 10.667 -128.001 32l32 800v354
h-402v82z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" 
d="M84 913c0 14 2.16699 27.333 6.5 40s11.333 24 21 34s21.667 18 36 24s31.5 9 51.5 9c42 -0.666992 87.667 -6.33398 137 -17.001s99 -19.667 149 -27l-31 489c36.667 21.333 79.334 32 128.001 32c24 0 47 -2.66699 69 -8s41.667 -13.333 59 -24l-32 -489
c50 7.33301 99.833 16.333 149.5 27s95.5 16.334 137.5 17.001c20 0 37.333 -3 52 -9s26.667 -14 36 -24s16.333 -21.333 21 -34s7 -26 7 -40v-82h-403v-520h403v-82c0 -14 -2.33301 -27.333 -7 -40s-11.667 -24 -21 -34s-21.333 -18 -36 -24s-32 -9 -52 -9
c-42 0.666992 -87.833 6.16699 -137.5 16.5s-99.5 19.166 -149.5 26.499l32 -488c-17.333 -10.667 -37 -18.667 -59 -24s-45 -8 -69 -8c-48.667 0 -91.334 10.667 -128.001 32l31 488c-50 -7.33301 -99.667 -16.166 -149 -26.499s-95 -15.833 -137 -16.5
c-20 0 -37.167 3 -51.5 9s-26.333 14 -36 24s-16.667 21.333 -21 34s-6.5 26 -6.5 40v82h402v520h-402v82z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" 
d="M143 593c0 60 11.5 116.333 34.5 169s54.167 98.834 93.5 138.501s85.333 70.834 138 93.501s109 34 169 34s116.667 -11.333 170 -34s99.666 -53.834 138.999 -93.501s70.5 -85.834 93.5 -138.501s34.5 -109 34.5 -169c0 -59.333 -11.5 -115.166 -34.5 -167.499
s-54.167 -98.166 -93.5 -137.499s-85.666 -70.333 -138.999 -93s-110 -34 -170 -34s-116.333 11.333 -169 34s-98.667 53.667 -138 93s-70.5 85.166 -93.5 137.499s-34.5 108.166 -34.5 167.499z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="1517" 
d="M73 136c0 20.667 3.83301 40.334 11.5 59.001s18.334 34.667 32.001 48s29.834 24 48.501 32s38.667 12 60 12c20.667 0 40.334 -4 59.001 -12s34.667 -18.667 48 -32s24 -29.333 32 -48s12 -38.334 12 -59.001c0 -21.333 -4 -41.166 -12 -59.499
s-18.667 -34.166 -32 -47.499s-29.333 -23.833 -48 -31.5s-38.334 -11.5 -59.001 -11.5c-21.333 0 -41.333 3.83301 -60 11.5s-34.834 18.167 -48.501 31.5s-24.334 29.166 -32.001 47.499s-11.5 38.166 -11.5 59.499zM1141 136c0 20.667 3.83301 40.334 11.5 59.001
s18.334 34.667 32.001 48s29.834 24 48.501 32s38.667 12 60 12c20.667 0 40.334 -4 59.001 -12s34.667 -18.667 48 -32s24 -29.333 32 -48s12 -38.334 12 -59.001c0 -21.333 -4 -41.166 -12 -59.499s-18.667 -34.166 -32 -47.499s-29.333 -23.833 -48 -31.5
s-38.334 -11.5 -59.001 -11.5c-21.333 0 -41.333 3.83301 -60 11.5s-34.834 18.167 -48.501 31.5s-24.334 29.166 -32.001 47.499s-11.5 38.166 -11.5 59.499zM607 136c0 20.667 3.83301 40.334 11.5 59.001s18.334 34.667 32.001 48s29.834 24 48.501 32s38.667 12 60 12
c20.667 0 40.334 -4 59.001 -12s34.667 -18.667 48 -32s24 -29.333 32 -48s12 -38.334 12 -59.001c0 -21.333 -4 -41.166 -12 -59.499s-18.667 -34.166 -32 -47.499s-29.333 -23.833 -48 -31.5s-38.334 -11.5 -59.001 -11.5c-21.333 0 -41.333 3.83301 -60 11.5
s-34.834 18.167 -48.501 31.5s-24.334 29.166 -32.001 47.499s-11.5 38.166 -11.5 59.499z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="2347" 
d="M729 1096c0 -54.667 -9.16602 -104 -27.499 -148s-42.833 -81.5 -73.5 -112.5s-66.334 -54.833 -107.001 -71.5s-83 -25 -127 -25c-48 0 -92.333 8.33301 -133 25s-75.834 40.5 -105.501 71.5s-52.834 68.5 -69.501 112.5s-25 93.333 -25 148
c0 56 8.33301 106.667 25 152s39.834 83.666 69.501 114.999s64.834 55.5 105.501 72.5s85 25.5 133 25.5s92.5 -8.5 133.5 -25.5s76.5 -41.167 106.5 -72.5s53.333 -69.666 70 -114.999s25 -96 25 -152zM538.001 1096c0 38.667 -3.66699 71 -11 97s-17.5 47 -30.5 63
s-28.333 27.5 -46 34.5s-36.5 10.5 -56.5 10.5s-38.667 -3.5 -56 -10.5s-32.333 -18.5 -45 -34.5s-22.5 -37 -29.5 -63s-10.5 -58.333 -10.5 -97c0 -37.333 3.5 -68.5 10.5 -93.5s16.833 -45.167 29.5 -60.5s27.667 -26.333 45 -33s36 -10 56 -10s38.833 3.33301 56.5 10
s33 17.667 46 33s23.167 35.5 30.5 60.5s11 56.167 11 93.5zM1210 1407c8 10 17.999 19 29.999 27s28.667 12 50 12h179l-1074 -1409c-8 -10.667 -18.167 -19.5 -30.5 -26.5s-27.166 -10.5 -44.499 -10.5h-184zM1545 340c0 -54.667 -9.16602 -104 -27.499 -148
s-42.833 -81.5 -73.5 -112.5s-66.334 -55 -107.001 -72s-83 -25.5 -127 -25.5c-48 0 -92.333 8.5 -133 25.5s-75.834 41 -105.501 72s-52.834 68.5 -69.501 112.5s-25 93.333 -25 148c0 56 8.33301 106.667 25 152s39.834 83.666 69.501 114.999s64.834 55.5 105.501 72.5
s85 25.5 133 25.5s92.5 -8.5 133.5 -25.5s76.5 -41.167 106.5 -72.5s53.333 -69.666 70 -114.999s25 -96 25 -152zM1355 340c0 38.667 -3.83301 71 -11.5 97s-18 47 -31 63s-28.333 27.5 -46 34.5s-36.5 10.5 -56.5 10.5s-38.667 -3.5 -56 -10.5
s-32.166 -18.5 -44.499 -34.5s-22.166 -37 -29.499 -63s-11 -58.333 -11 -97c0 -37.333 3.66699 -68.666 11 -93.999s17.166 -45.666 29.499 -60.999s27.166 -26.333 44.499 -33s36 -10 56 -10s38.833 3.33301 56.5 10s33 17.667 46 33s23.333 35.666 31 60.999
s11.5 56.666 11.5 93.999zM2286 340c0 -54.667 -9.16602 -104 -27.499 -148s-42.833 -81.5 -73.5 -112.5s-66.334 -55 -107.001 -72s-83 -25.5 -127 -25.5c-48 0 -92.333 8.5 -133 25.5s-75.834 41 -105.501 72s-52.834 68.5 -69.501 112.5s-25 93.333 -25 148
c0 56 8.33301 106.667 25 152s39.834 83.666 69.501 114.999s64.834 55.5 105.501 72.5s85 25.5 133 25.5s92.5 -8.5 133.5 -25.5s76.5 -41.167 106.5 -72.5s53.333 -69.666 70 -114.999s25 -96 25 -152zM2096 340c0 38.667 -3.83301 71 -11.5 97s-18 47 -31 63
s-28.333 27.5 -46 34.5s-36.5 10.5 -56.5 10.5s-38.667 -3.5 -56 -10.5s-32.166 -18.5 -44.499 -34.5s-22.166 -37 -29.499 -63s-11 -58.333 -11 -97c0 -37.333 3.66699 -68.666 11 -93.999s17.166 -45.666 29.499 -60.999s27.166 -26.333 44.499 -33s36 -10 56 -10
s38.833 3.33301 56.5 10s33 17.667 46 33s23.333 35.666 31 60.999s11.5 56.666 11.5 93.999z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="642" 
d="M123 522l-0.000976562 31.999l256 396l81 -38c13.333 -6 23 -13.667 29 -23s9 -19.666 9 -30.999c0 -14 -4.33301 -28.333 -13 -43l-138 -235c-9.33301 -17.333 -20 -31.333 -32 -42c10.667 -9.33301 21.334 -23.333 32.001 -42l138 -236c8.66699 -14.667 13 -29 13 -43
c0 -23.333 -12.667 -41 -38 -53l-81 -38z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="642" 
d="M263 126l-81.001 38.001c-13.333 6 -23 13.667 -29 23s-9 19.666 -9 30.999c0 13.333 4.33301 27.333 13 42l138 236c10.667 18.667 21.334 32.667 32.001 42c-12 10.667 -22.667 24.667 -32 42l-138 235c-8.66699 14.667 -13 29 -13 43c0 24 12.667 42 38 54l81 38
l256 -396v-32z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="711" 
d="M52 71c-17.333 -27.333 -35.833 -45.999 -55.5 -55.999s-42.167 -15 -67.5 -15h-106l834 1365c15.333 25.333 33.333 45.166 54 59.499s46 21.5 76 21.5h105z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" 
d="M37 928l132.999 0.000976562c15.333 81.333 39.666 154.833 72.999 220.5s74 121.5 122 167.5s102.333 81.5 163 106.5s126.667 37.5 198 37.5c92.667 0 172 -17.833 238 -53.5s121.333 -83.834 166 -144.501l-87 -94c-6.66699 -7.33301 -13.667 -13.833 -21 -19.5
s-17.666 -8.5 -30.999 -8.5c-9.33301 0 -17.833 2.66699 -25.5 8s-16.167 12 -25.5 20l-31 26c-11.333 9.33301 -24.833 18 -40.5 26s-34.167 14.667 -55.5 20s-46.666 8 -75.999 8c-78.667 0 -145 -27 -199 -81s-91.667 -133.667 -113 -239h502v-76
c0 -15.333 -6.16699 -29.5 -18.5 -42.5s-29.166 -19.5 -50.499 -19.5h-450c-0.666992 -11.333 -1 -22.5 -1 -33.5v-33.5v-24c0 -8 0.333008 -15.667 1 -23h424v-75c0 -15.333 -6.16699 -29.5 -18.5 -42.5s-28.833 -19.5 -49.5 -19.5h-344
c18.667 -114.667 54.5 -200.167 107.5 -256.5s118.833 -84.5 197.5 -84.5c48 0 86.5 6.5 115.5 19.5s52.667 27.5 71 43.5s33.666 30.5 45.999 43.5s25.833 19.5 40.5 19.5c7.33301 0 13.833 -1.16699 19.5 -3.5s11.167 -6.83301 16.5 -13.5l107 -98
c-46.667 -72 -106 -127.167 -178 -165.5s-155.667 -57.5 -251 -57.5c-78 0 -148.167 13.167 -210.5 39.5s-116.5 63.5 -162.5 111.5s-83.667 106 -113 174s-50 144 -62 228h-127v137h116c-0.666992 7.33301 -1 15 -1 23v24v33.5c0 11 0.333008 22.167 1 33.5h-116v138z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="1452" 
d="M960 1165c5.33301 -12 10.166 -23.833 14.499 -35.5s8.16602 -23.5 11.499 -35.5c4 12 8.33301 24 13 36s10 23.667 16 35l133 255c6.66699 12.667 14 20.167 22 22.5s19 3.5 33 3.5h147v-603h-150v302l11 99l-153 -302c-12.667 -24.667 -32 -37 -58 -37h-24
c-27.333 0 -47 12.333 -59 37l-151 297l11 -94v-302h-150v603h147c14.667 0 25.834 -1 33.501 -3s14.834 -9.66699 21.501 -23zM555 1446v-140h-164v-463h-171v463h-163v140h498z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="1480" 
d="M811 0l-0.00195312 497c46 7.33301 88.667 20 128 38s73.5 42 102.5 72s51.667 66.333 68 109s24.5 92.667 24.5 150c0 62 -9.16699 116.667 -27.5 164s-44.666 87.166 -78.999 119.499s-75.833 56.833 -124.5 73.5s-103 25 -163 25s-114.333 -8.33301 -163 -25
s-90 -41.167 -124 -73.5s-60.167 -72.166 -78.5 -119.499s-27.5 -102 -27.5 -164c0 -57.333 8.16699 -107.333 24.5 -150s38.833 -79 67.5 -109s62.667 -54 102 -72s82 -30.667 128 -38v-497h-507c-28 -0 -50 7.83301 -66 23.5s-24 35.834 -24 60.501v137h389v139
c-62 16 -117.333 40.333 -166 73s-89.5 71.667 -122.5 117s-58.167 95.833 -75.5 151.5s-26 115.167 -26 178.5c0 80.667 16.167 156.334 48.5 227.001s77.833 132.334 136.5 185.001s129 94.167 211 124.5s173 45.5 273 45.5s191 -15.167 273 -45.5
s152.333 -71.833 211 -124.5s104.167 -114.334 136.5 -185.001s48.5 -146.334 48.5 -227.001c0 -63.333 -8.66699 -122.833 -26 -178.5s-42.5 -106.167 -75.5 -151.5s-73.833 -84.333 -122.5 -117s-104 -57 -166 -73v-139h390v-137
c0 -24.667 -8.16699 -44.834 -24.5 -60.501s-38.166 -23.5 -65.499 -23.5h-508z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" 
d="M306 1335c26.667 20 53.502 37.833 80.502 53.5s55 28.667 84 39s59.833 18.333 92.5 24s68.667 8.5 108 8.5c62 0 119.167 -12.667 171.5 -38s97.166 -61.833 134.499 -109.5s66.5 -105.667 87.5 -174s31.5 -145.833 31.5 -232.5c0 -138 -12.667 -263.667 -38 -377
s-63.333 -210.333 -114 -291s-114.167 -143 -190.5 -187s-165.5 -66 -267.5 -66c-60.667 0 -116.667 10 -168 30s-95.5 48.5 -132.5 85.5s-66 81.833 -87 134.5s-31.5 111.667 -31.5 177c0 78 13.333 150.5 40 217.5s63.167 125.167 109.5 174.5s101.333 88 165 116
s132.5 42 206.5 42c65.333 0 121.166 -11.667 167.499 -35s85.166 -56.333 116.499 -99c0.666992 13.333 1 26.5 1 39.5v37.5c0 60 -5.66699 112.5 -17 157.5s-27.333 82.5 -48 112.5s-45.5 52.5 -74.5 67.5s-61.167 22.5 -96.5 22.5
c-25.333 0 -49.166 -3.33301 -71.499 -10s-43 -14 -62 -22l-51 -22c-15 -6.66699 -27.833 -10 -38.5 -10c-9.33301 0 -18.333 2.66699 -27 8s-17.667 16 -27 32zM515.002 177c36 0 70.498 7.33496 103.498 22.002s63.5 38 91.5 70s52.833 73.333 74.5 124
s38.834 112 51.501 184c-6.66699 25.333 -15.667 49.666 -27 72.999s-25.833 44 -43.5 62s-38.834 32.333 -63.501 43s-54 16 -88 16c-49.333 0 -92.166 -8.83301 -128.499 -26.5s-66.666 -42.334 -90.999 -74.001s-42.5 -69.167 -54.5 -112.5s-18 -91 -18 -143
c0 -76 17.167 -134.667 51.5 -176s81.5 -62 141.5 -62z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="1467" 
d="M612 1446h243l599 -1446h-1442zM370 213l727 -0.00195312l-316 814c-7.33301 18.667 -15 40.334 -23 65.001s-16 51.334 -24 80.001c-8 -29.333 -16 -56.333 -24 -81s-15.667 -46.667 -23 -66z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="1372" 
d="M1321 1446v-207h-167v-1574h-255v1574h-426v-1574h-254v1574h-168v207h1270z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="1372" 
d="M82 1446h1208v-207h-826l514 -648v-71l-514 -648h826v-207h-1208v86c0 12 2 24.5 6 37.5s10.667 25.167 20 36.5l595 733l-595 728c-9.33301 11.333 -16 23.5 -20 36.5s-6 25.5 -6 37.5v86z" />
    <glyph glyph-name="minus" unicode="&#x2212;" 
d="M136 763h886v-183h-886v183z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="1165" 
d="M267 642h-121.001c-25.333 0 -47.166 7.5 -65.499 22.5s-27.5 40.5 -27.5 76.5v79h352c18.667 0 34.167 -4.33301 46.5 -13s20.5 -19.667 24.5 -33l102 -311c9.33301 -28 16.833 -56 22.5 -84s10.167 -56.333 13.5 -85c3.33301 22 7.5 44.5 12.5 67.5
s11.167 46.5 18.5 70.5l386 1228c4 13.333 12.167 24.333 24.5 33s26.833 13 43.5 13h163l-550 -1706h-208z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="1372" 
d="M1000 232c-36.667 0 -70.167 5 -100.5 15s-58.166 23.5 -83.499 40.5s-48.5 36.833 -69.5 59.5s-40.5 46.667 -58.5 72c-18 -25.333 -37.667 -49.333 -59 -72s-44.666 -42.5 -69.999 -59.5s-53.166 -30.5 -83.499 -40.5s-63.833 -15 -100.5 -15
c-44 0 -86 8.83301 -126 26.5s-75 42.334 -105 74.001s-53.833 69.5 -71.5 113.5s-26.5 92.667 -26.5 146c0 52.667 8.83301 101 26.5 145s41.5 81.833 71.5 113.5s65 56.334 105 74.001s82 26.5 126 26.5c36.667 0 70.167 -5 100.5 -15s58.166 -23.5 83.499 -40.5
s48.666 -36.833 69.999 -59.5s41 -46.667 59 -72c18 25.333 37.5 49.333 58.5 72s44.167 42.5 69.5 59.5s53.166 30.5 83.499 40.5s63.833 15 100.5 15c44 0 86 -8.83301 126 -26.5s75 -42.334 105 -74.001s53.833 -69.5 71.5 -113.5s26.5 -92.333 26.5 -145
c0 -53.333 -8.83301 -102 -26.5 -146s-41.5 -81.833 -71.5 -113.5s-65 -56.334 -105 -74.001s-82 -26.5 -126 -26.5zM385 431c20 0 38.833 4.16797 56.5 12.501s34.334 19.666 50.001 33.999s30.667 31.333 45 51s28.5 40.5 42.5 62.5c-14 22.667 -28.167 43.667 -42.5 63
s-29.333 36.333 -45 51s-32.334 26.167 -50.001 34.5s-36.5 12.5 -56.5 12.5c-19.333 0 -37.666 -3 -54.999 -9s-32.666 -15.5 -45.999 -28.5s-24 -29.667 -32 -50s-12 -44.5 -12 -72.5c0 -28.667 4 -53.167 12 -73.5s18.667 -37 32 -50s28.666 -22.5 45.999 -28.5
s35.666 -9 54.999 -9zM990 431.001c19.333 0 37.8311 2.99902 55.498 8.99902s33.167 15.5 46.5 28.5s23.833 29.667 31.5 50s11.5 44.833 11.5 73.5c0 28 -4 52.167 -12 72.5s-18.667 37 -32 50s-28.666 22.5 -45.999 28.5s-35.666 9 -54.999 9
c-20.667 0 -39.667 -4.16699 -57 -12.5s-33.833 -19.833 -49.5 -34.5s-30.5 -31.667 -44.5 -51s-28 -40.333 -42 -63c14 -22 28 -42.833 42 -62.5s28.833 -36.667 44.5 -51s32.167 -25.666 49.5 -33.999s36.333 -12.5 57 -12.5z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="827" 
d="M359 1094c14 116 51.165 206.33 111.498 270.997s143.5 97 249.5 97c25.333 0 51.5 -2.5 78.5 -7.5s52.5 -13.833 76.5 -26.5l-12 -119c-0.666992 -6.66699 -2.16699 -13.334 -4.5 -20.001s-6.16602 -12.667 -11.499 -18s-12.5 -9.66602 -21.5 -12.999s-20.5 -5 -34.5 -5
c-32 0 -59.5 -3.83301 -82.5 -11.5s-42.167 -19.5 -57.5 -35.5s-27.666 -36 -36.999 -60s-16 -52 -20 -84l-128 -1005c-8.66699 -69.333 -24.5 -129.166 -47.5 -179.499s-51.667 -91.666 -86 -123.999s-74.166 -56.333 -119.499 -72s-94.666 -23.5 -147.999 -23.5
c-24 0 -49.833 2.5 -77.5 7.5s-53.167 13.833 -76.5 26.5l13 103c1.33301 8.66699 3.33301 16.167 6 22.5s7 11.5 13 15.5s14 7 24 9s23.333 3 40 3c38.667 0 71.167 3.83301 97.5 11.5s48.166 19.667 65.499 36s30.666 37.333 39.999 63s16 56.834 20 93.501z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" 
d="M792 896c20.667 0 40.8301 2.33105 60.4971 6.99805s37.667 10.5 54 17.5s30.333 14.333 42 22s19.834 14.5 24.501 20.5l30 -157c-25.333 -33.333 -58.666 -57.833 -99.999 -73.5s-83.666 -23.5 -126.999 -23.5c-35.333 0 -70.5 5.16699 -105.5 15.5
s-69.333 21.833 -103 34.5s-66.5 24.167 -98.5 34.5s-62.333 15.5 -91 15.5c-22 0 -43 -2.5 -63 -7.5s-38.167 -11 -54.5 -18s-30.5 -14.667 -42.5 -23s-20.333 -15.5 -25 -21.5l-36 150c12.667 18.667 27.667 34.667 45 48s36.333 24.333 57 33s42.334 15 65.001 19
s45.667 6 69 6c35.333 0 70.666 -5.16699 105.999 -15.5s69.833 -21.666 103.5 -33.999s66.5 -23.666 98.5 -33.999s62.333 -15.5 91 -15.5zM791.996 539.998c20.667 0 40.8301 2.33105 60.4971 6.99805s37.667 10.5 54 17.5s30.333 14.333 42 22s19.834 14.5 24.501 20.5
l30 -157c-25.333 -33.333 -58.666 -57.833 -99.999 -73.5s-83.666 -23.5 -126.999 -23.5c-35.333 0 -70.5 5.16699 -105.5 15.5s-69.333 21.833 -103 34.5s-66.5 24.167 -98.5 34.5s-62.333 15.5 -91 15.5c-22 0 -43 -2.5 -63 -7.5s-38.167 -11 -54.5 -18
s-30.5 -14.667 -42.5 -23s-20.333 -15.5 -25 -21.5l-36 150c12.667 18.667 27.667 34.667 45 48s36.333 24.333 57 33s42.334 15 65.001 19s45.667 6 69 6c35.333 0 70.666 -5.16699 105.999 -15.5s69.833 -21.666 103.5 -33.999s66.5 -23.666 98.5 -33.999
s62.333 -15.5 91 -15.5z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" 
d="M136 940h502l110 221h177l-111 -221h208v-183h-299l-85 -169h384v-184h-476l-117 -235h-176l117 235h-234v184h326l84 169h-410v183z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" 
d="M139 839l791.999 378.998v-168c0 -13.333 -3.66699 -25.833 -11 -37.5s-21.666 -22.167 -42.999 -31.5l-352 -158c-17.333 -7.33301 -35.333 -13.5 -54 -18.5s-38.667 -9.83301 -60 -14.5c21.333 -4.66699 41.5 -9.66699 60.5 -15s36.833 -11.666 53.5 -18.999l351 -161
c21.333 -10 35.833 -20.667 43.5 -32s11.5 -23.666 11.5 -36.999v-168l-792 382v99zM138.999 262.998h792v-183h-792v183z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" 
d="M1021 839l-0.00195312 -98.998l-792 -382v168c0 13.333 3.83301 25.666 11.5 36.999s22.167 22 43.5 32l351 161c16.667 7.33301 34.334 13.666 53.001 18.999s38.667 10.333 60 15c-20.667 4.66699 -40.5 9.5 -59.5 14.5s-36.833 11.167 -53.5 18.5l-352 158
c-21.333 9.33301 -35.666 19.833 -42.999 31.5s-11 24.167 -11 37.5v168zM1021 80.002h-792v183h792v-183z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" 
d="M113 720l381 811h172l381 -811l-381 -811h-172zM315 720l231 -501c8 -17.333 14.667 -33.833 20 -49.5s10 -30.834 14 -45.501c4 14.667 8.66699 29.834 14 45.501s12 32.167 20 49.5l235 501l-235 501c-8 17.333 -14.5 33.833 -19.5 49.5l-14.5 45.5l-14.5 -45.5
c-5 -15.667 -11.5 -32.167 -19.5 -49.5z" />
    <glyph glyph-name="uni2669" unicode="&#x2669;" horiz-adv-x="0" 
d="M-2 1486h4v-1821h-4v1821z" />
    <glyph glyph-name="undercommaaccent" horiz-adv-x="638" 
d="M399 -72c18.667 0 30.165 -4.33398 34.498 -13.001s6.5 -17 6.5 -25c0 -6 -0.666992 -13.5 -2 -22.5s-4.33301 -21 -9 -36s-11.5 -34 -20.5 -57s-21.5 -51.167 -37.5 -84.5c-9.33301 -19.333 -20.666 -31.666 -33.999 -36.999s-30.666 -8 -51.999 -8h-87l50 283h151z" />
    <glyph glyph-name="grave.case" horiz-adv-x="638" 
d="M192 1791c13.333 0 24.666 -0.499023 33.999 -1.49902s17.833 -3 25.5 -6s14.834 -7.16699 21.501 -12.5s14 -12 22 -20l197 -202h-195c-9.33301 0 -17.5 0.166992 -24.5 0.5s-13.5 1.33301 -19.5 3s-12 4.16699 -18 7.5s-12.667 7.66602 -20 12.999l-297 218h274z" />
    <glyph glyph-name="dieresis.case" horiz-adv-x="638" 
d="M237 1667c0 -18 -3.66699 -35 -11 -51s-17.333 -30 -30 -42s-27.334 -21.333 -44.001 -28s-34.334 -10 -53.001 -10c-17.333 0 -33.833 3.33301 -49.5 10s-29.5 16 -41.5 28s-21.5 26 -28.5 42s-10.5 33 -10.5 51s3.5 35.167 10.5 51.5s16.5 30.666 28.5 42.999
s25.833 22.166 41.5 29.499s32.167 11 49.5 11c18.667 0 36.334 -3.66699 53.001 -11s31.334 -17.166 44.001 -29.499s22.667 -26.666 30 -42.999s11 -33.5 11 -51.5zM669 1667c0 -18 -3.5 -35 -10.5 -51s-16.5 -30 -28.5 -42s-26.167 -21.333 -42.5 -28
s-33.833 -10 -52.5 -10c-18 0 -35.167 3.33301 -51.5 10s-30.666 16 -42.999 28s-22 26 -29 42s-10.5 33 -10.5 51s3.5 35.167 10.5 51.5s16.667 30.666 29 42.999s26.666 22.166 42.999 29.499s33.5 11 51.5 11c18.667 0 36.167 -3.66699 52.5 -11
s30.5 -17.166 42.5 -29.499s21.5 -26.666 28.5 -42.999s10.5 -33.5 10.5 -51.5z" />
    <glyph glyph-name="macron.case" horiz-adv-x="638" 
d="M53 1705h532v-142h-532v142z" />
    <glyph glyph-name="acute.case" horiz-adv-x="638" 
d="M719 1791l-296 -216.001c-7.33301 -5.33301 -14.166 -9.66602 -20.499 -12.999s-12.666 -6 -18.999 -8s-13 -3.33301 -20 -4s-15.167 -1 -24.5 -1h-194l196 202c8 8 15.5 14.667 22.5 20s14.167 9.5 21.5 12.5s15.666 5 24.999 6s20.666 1.5 33.999 1.5h275z" />
    <glyph glyph-name="circumflex.case" horiz-adv-x="638" 
d="M684 1549h-187c-10 0 -21 1.33301 -33 4s-21.667 6.66699 -29 12l-101 67c-2.66699 1.33301 -5.16699 3 -7.5 5s-4.83301 4 -7.5 6c-1.33301 -1.33301 -3.33301 -3 -6 -5s-5.66699 -4 -9 -6l-101 -67c-7.33301 -5.33301 -17 -9.33301 -29 -12s-23 -4 -33 -4h-187l242 219
h246z" />
    <glyph glyph-name="caron.case" horiz-adv-x="638" 
d="M-46 1768h187c10 0 21 -1.33301 33 -4s21.667 -6.66699 29 -12l101 -67c2.66699 -1.33301 5.16699 -2.83301 7.5 -4.5l7.5 -5.5l7.5 5.5c2.33301 1.66699 4.83301 3.16699 7.5 4.5l101 67c7.33301 5.33301 17 9.33301 29 12s23 4 33 4h187l-242 -219h-246z" />
    <glyph glyph-name="breve.case" horiz-adv-x="638" 
d="M319 1526c-99.333 0 -175.833 20 -229.5 60s-80.5 100.667 -80.5 182h157c0 -33.333 12.5 -57.5 37.5 -72.5s63.5 -22.5 115.5 -22.5s90.5 7.5 115.5 22.5s37.5 39.167 37.5 72.5h157c0 -36.667 -6.83301 -70 -20.5 -100s-33.834 -55.5 -60.501 -76.5
s-59.167 -37.167 -97.5 -48.5s-82.166 -17 -131.499 -17z" />
    <glyph glyph-name="dotaccent.case" horiz-adv-x="638" 
d="M470 1696c0 -20 -4.16699 -38.833 -12.5 -56.5s-19.5 -33.334 -33.5 -47.001s-30.333 -24.5 -49 -32.5s-38.334 -12 -59.001 -12c-19.333 0 -38 4 -56 12s-33.833 18.833 -47.5 32.5s-24.5 29.334 -32.5 47.001s-12 36.5 -12 56.5c0 20.667 4 40.167 12 58.5
s18.833 34.333 32.5 48s29.5 24.5 47.5 32.5s36.667 12 56 12c20.667 0 40.334 -4 59.001 -12s35 -18.833 49 -32.5s25.167 -29.667 33.5 -48s12.5 -37.833 12.5 -58.5z" />
    <glyph glyph-name="ring.case" horiz-adv-x="638" 
d="M112 1683c0 28.667 5.66699 54.667 17 78s26.333 43.333 45 60s40.334 29.667 65.001 39s50.667 14 78 14c28.667 0 55.667 -4.66699 81 -14s47.666 -22.333 66.999 -39s34.666 -36.667 45.999 -60s17 -49.333 17 -78c0 -28 -5.66699 -53.5 -17 -76.5
s-26.666 -42.833 -45.999 -59.5s-41.666 -29.5 -66.999 -38.5s-52.333 -13.5 -81 -13.5c-27.333 0 -53.333 4.5 -78 13.5s-46.334 21.833 -65.001 38.5s-33.667 36.5 -45 59.5s-17 48.5 -17 76.5zM230 1683c0 -26 7.83301 -47.333 23.5 -64s38.167 -25 67.5 -25
c26.667 0 48 8.33301 64 25s24 38 24 64c0 28 -8 50 -24 66s-37.333 24 -64 24c-29.333 0 -51.833 -8 -67.5 -24s-23.5 -38 -23.5 -66z" />
    <glyph glyph-name="tilde.case" horiz-adv-x="638" 
d="M442 1699c18 0 32.5 5.16504 43.5 15.498s16.833 26.166 17.5 47.499h134c0 -33.333 -4.33301 -63.666 -13 -90.999s-21 -50.666 -37 -69.999s-35.667 -34.5 -59 -45.5s-50 -16.5 -80 -16.5c-24 0 -47.167 3.66699 -69.5 11s-43.666 15.333 -63.999 24l-57 24
c-17.667 7.33301 -33.5 11 -47.5 11c-18 0 -32.333 -5.5 -43 -16.5s-16.334 -27.167 -17.001 -48.5h-136c0 32.667 4.33301 62.834 13 90.501s21.334 51.5 38.001 71.5s36.667 35.5 60 46.5s49.666 16.5 78.999 16.5c24 0 47.333 -3.66699 70 -11s44.167 -15.333 64.5 -24
l56.5 -24c17.333 -7.33301 33 -11 47 -11z" />
    <glyph glyph-name="hungarumlaut.case" horiz-adv-x="638" 
d="M456 1775l-200.999 -190c-12.667 -12 -24.667 -21 -36 -27s-26.333 -9 -45 -9h-117l132 174c11.333 15.333 25.166 27.833 41.499 37.5s37.833 14.5 64.5 14.5h161zM821.001 1775l-253 -190.001c-14.667 -11.333 -28.5 -20.166 -41.5 -26.499s-28.833 -9.5 -47.5 -9.5
h-133l177 174c14.667 14.667 30.5 27 47.5 37s38.833 15 65.5 15h185z" />
    <glyph glyph-name="caron.salt" horiz-adv-x="638" 
d="M421 1488c18.667 0 30.167 -4.16797 34.5 -12.501s6.5 -16.5 6.5 -24.5c0 -6 -1.33301 -14.167 -4 -24.5s-7.5 -25.333 -14.5 -45s-16.667 -45.167 -29 -76.5l-48.5 -118c-8 -19.333 -18.833 -31.666 -32.5 -36.999s-31.167 -8 -52.5 -8h-88l62 346h166z" />
    <hkern u1="&#x22;" u2="&#x2206;" k="191" />
    <hkern u1="&#x22;" u2="&#x203a;" k="169" />
    <hkern u1="&#x22;" u2="&#x2039;" k="169" />
    <hkern u1="&#x22;" u2="&#x2022;" k="169" />
    <hkern u1="&#x22;" u2="&#x201e;" k="213" />
    <hkern u1="&#x22;" u2="&#x201a;" k="213" />
    <hkern u1="&#x22;" u2="&#x2014;" k="169" />
    <hkern u1="&#x22;" u2="&#x2013;" k="169" />
    <hkern u1="&#x22;" u2="&#x178;" k="-36" />
    <hkern u1="&#x22;" u2="&#x153;" k="96" />
    <hkern u1="&#x22;" u2="&#x152;" k="42" />
    <hkern u1="&#x22;" u2="&#x119;" k="96" />
    <hkern u1="&#x22;" u2="&#x107;" k="96" />
    <hkern u1="&#x22;" u2="&#x106;" k="42" />
    <hkern u1="&#x22;" u2="&#x105;" k="66" />
    <hkern u1="&#x22;" u2="&#x104;" k="191" />
    <hkern u1="&#x22;" u2="&#xf8;" k="96" />
    <hkern u1="&#x22;" u2="&#xf6;" k="96" />
    <hkern u1="&#x22;" u2="&#xf5;" k="96" />
    <hkern u1="&#x22;" u2="&#xf4;" k="96" />
    <hkern u1="&#x22;" u2="&#xf3;" k="96" />
    <hkern u1="&#x22;" u2="&#xf2;" k="96" />
    <hkern u1="&#x22;" u2="&#xf0;" k="96" />
    <hkern u1="&#x22;" u2="&#xeb;" k="96" />
    <hkern u1="&#x22;" u2="&#xea;" k="96" />
    <hkern u1="&#x22;" u2="&#xe9;" k="96" />
    <hkern u1="&#x22;" u2="&#xe8;" k="96" />
    <hkern u1="&#x22;" u2="&#xe7;" k="96" />
    <hkern u1="&#x22;" u2="&#xe6;" k="66" />
    <hkern u1="&#x22;" u2="&#xe5;" k="66" />
    <hkern u1="&#x22;" u2="&#xe4;" k="66" />
    <hkern u1="&#x22;" u2="&#xe3;" k="66" />
    <hkern u1="&#x22;" u2="&#xe2;" k="66" />
    <hkern u1="&#x22;" u2="&#xe1;" k="66" />
    <hkern u1="&#x22;" u2="&#xe0;" k="66" />
    <hkern u1="&#x22;" u2="&#xdd;" k="-36" />
    <hkern u1="&#x22;" u2="&#xd8;" k="42" />
    <hkern u1="&#x22;" u2="&#xd6;" k="42" />
    <hkern u1="&#x22;" u2="&#xd5;" k="42" />
    <hkern u1="&#x22;" u2="&#xd4;" k="42" />
    <hkern u1="&#x22;" u2="&#xd3;" k="42" />
    <hkern u1="&#x22;" u2="&#xd2;" k="42" />
    <hkern u1="&#x22;" u2="&#xc7;" k="42" />
    <hkern u1="&#x22;" u2="&#xc6;" k="191" />
    <hkern u1="&#x22;" u2="&#xc5;" k="191" />
    <hkern u1="&#x22;" u2="&#xc4;" k="191" />
    <hkern u1="&#x22;" u2="&#xc3;" k="191" />
    <hkern u1="&#x22;" u2="&#xc2;" k="191" />
    <hkern u1="&#x22;" u2="&#xc1;" k="191" />
    <hkern u1="&#x22;" u2="&#xc0;" k="191" />
    <hkern u1="&#x22;" u2="&#xbb;" k="169" />
    <hkern u1="&#x22;" u2="&#xb7;" k="169" />
    <hkern u1="&#x22;" u2="&#xae;" k="42" />
    <hkern u1="&#x22;" u2="&#xad;" k="169" />
    <hkern u1="&#x22;" u2="&#xab;" k="169" />
    <hkern u1="&#x22;" u2="&#xa9;" k="42" />
    <hkern u1="&#x22;" u2="q" k="96" />
    <hkern u1="&#x22;" u2="o" k="96" />
    <hkern u1="&#x22;" u2="e" k="96" />
    <hkern u1="&#x22;" u2="d" k="96" />
    <hkern u1="&#x22;" u2="c" k="96" />
    <hkern u1="&#x22;" u2="a" k="66" />
    <hkern u1="&#x22;" u2="\" k="-44" />
    <hkern u1="&#x22;" u2="Y" k="-36" />
    <hkern u1="&#x22;" u2="W" k="-44" />
    <hkern u1="&#x22;" u2="V" k="-44" />
    <hkern u1="&#x22;" u2="Q" k="42" />
    <hkern u1="&#x22;" u2="O" k="42" />
    <hkern u1="&#x22;" u2="G" k="42" />
    <hkern u1="&#x22;" u2="C" k="42" />
    <hkern u1="&#x22;" u2="A" k="191" />
    <hkern u1="&#x22;" u2="&#x40;" k="42" />
    <hkern u1="&#x22;" u2="&#x2f;" k="191" />
    <hkern u1="&#x22;" u2="&#x2e;" k="213" />
    <hkern u1="&#x22;" u2="&#x2d;" k="169" />
    <hkern u1="&#x22;" u2="&#x2c;" k="213" />
    <hkern u1="&#x22;" u2="&#x26;" k="191" />
    <hkern u1="&#x27;" u2="&#x2206;" k="191" />
    <hkern u1="&#x27;" u2="&#x203a;" k="169" />
    <hkern u1="&#x27;" u2="&#x2039;" k="169" />
    <hkern u1="&#x27;" u2="&#x2022;" k="169" />
    <hkern u1="&#x27;" u2="&#x201e;" k="213" />
    <hkern u1="&#x27;" u2="&#x201a;" k="213" />
    <hkern u1="&#x27;" u2="&#x2014;" k="169" />
    <hkern u1="&#x27;" u2="&#x2013;" k="169" />
    <hkern u1="&#x27;" u2="&#x178;" k="-36" />
    <hkern u1="&#x27;" u2="&#x153;" k="96" />
    <hkern u1="&#x27;" u2="&#x152;" k="42" />
    <hkern u1="&#x27;" u2="&#x119;" k="96" />
    <hkern u1="&#x27;" u2="&#x107;" k="96" />
    <hkern u1="&#x27;" u2="&#x106;" k="42" />
    <hkern u1="&#x27;" u2="&#x105;" k="66" />
    <hkern u1="&#x27;" u2="&#x104;" k="191" />
    <hkern u1="&#x27;" u2="&#xf8;" k="96" />
    <hkern u1="&#x27;" u2="&#xf6;" k="96" />
    <hkern u1="&#x27;" u2="&#xf5;" k="96" />
    <hkern u1="&#x27;" u2="&#xf4;" k="96" />
    <hkern u1="&#x27;" u2="&#xf3;" k="96" />
    <hkern u1="&#x27;" u2="&#xf2;" k="96" />
    <hkern u1="&#x27;" u2="&#xf0;" k="96" />
    <hkern u1="&#x27;" u2="&#xeb;" k="96" />
    <hkern u1="&#x27;" u2="&#xea;" k="96" />
    <hkern u1="&#x27;" u2="&#xe9;" k="96" />
    <hkern u1="&#x27;" u2="&#xe8;" k="96" />
    <hkern u1="&#x27;" u2="&#xe7;" k="96" />
    <hkern u1="&#x27;" u2="&#xe6;" k="66" />
    <hkern u1="&#x27;" u2="&#xe5;" k="66" />
    <hkern u1="&#x27;" u2="&#xe4;" k="66" />
    <hkern u1="&#x27;" u2="&#xe3;" k="66" />
    <hkern u1="&#x27;" u2="&#xe2;" k="66" />
    <hkern u1="&#x27;" u2="&#xe1;" k="66" />
    <hkern u1="&#x27;" u2="&#xe0;" k="66" />
    <hkern u1="&#x27;" u2="&#xdd;" k="-36" />
    <hkern u1="&#x27;" u2="&#xd8;" k="42" />
    <hkern u1="&#x27;" u2="&#xd6;" k="42" />
    <hkern u1="&#x27;" u2="&#xd5;" k="42" />
    <hkern u1="&#x27;" u2="&#xd4;" k="42" />
    <hkern u1="&#x27;" u2="&#xd3;" k="42" />
    <hkern u1="&#x27;" u2="&#xd2;" k="42" />
    <hkern u1="&#x27;" u2="&#xc7;" k="42" />
    <hkern u1="&#x27;" u2="&#xc6;" k="191" />
    <hkern u1="&#x27;" u2="&#xc5;" k="191" />
    <hkern u1="&#x27;" u2="&#xc4;" k="191" />
    <hkern u1="&#x27;" u2="&#xc3;" k="191" />
    <hkern u1="&#x27;" u2="&#xc2;" k="191" />
    <hkern u1="&#x27;" u2="&#xc1;" k="191" />
    <hkern u1="&#x27;" u2="&#xc0;" k="191" />
    <hkern u1="&#x27;" u2="&#xbb;" k="169" />
    <hkern u1="&#x27;" u2="&#xb7;" k="169" />
    <hkern u1="&#x27;" u2="&#xae;" k="42" />
    <hkern u1="&#x27;" u2="&#xad;" k="169" />
    <hkern u1="&#x27;" u2="&#xab;" k="169" />
    <hkern u1="&#x27;" u2="&#xa9;" k="42" />
    <hkern u1="&#x27;" u2="q" k="96" />
    <hkern u1="&#x27;" u2="o" k="96" />
    <hkern u1="&#x27;" u2="e" k="96" />
    <hkern u1="&#x27;" u2="d" k="96" />
    <hkern u1="&#x27;" u2="c" k="96" />
    <hkern u1="&#x27;" u2="a" k="66" />
    <hkern u1="&#x27;" u2="\" k="-44" />
    <hkern u1="&#x27;" u2="Y" k="-36" />
    <hkern u1="&#x27;" u2="W" k="-44" />
    <hkern u1="&#x27;" u2="V" k="-44" />
    <hkern u1="&#x27;" u2="Q" k="42" />
    <hkern u1="&#x27;" u2="O" k="42" />
    <hkern u1="&#x27;" u2="G" k="42" />
    <hkern u1="&#x27;" u2="C" k="42" />
    <hkern u1="&#x27;" u2="A" k="191" />
    <hkern u1="&#x27;" u2="&#x40;" k="42" />
    <hkern u1="&#x27;" u2="&#x2f;" k="191" />
    <hkern u1="&#x27;" u2="&#x2e;" k="213" />
    <hkern u1="&#x27;" u2="&#x2d;" k="169" />
    <hkern u1="&#x27;" u2="&#x2c;" k="213" />
    <hkern u1="&#x27;" u2="&#x26;" k="191" />
    <hkern u1="&#x28;" u2="&#x153;" k="36" />
    <hkern u1="&#x28;" u2="&#x152;" k="40" />
    <hkern u1="&#x28;" u2="&#x119;" k="36" />
    <hkern u1="&#x28;" u2="&#x107;" k="36" />
    <hkern u1="&#x28;" u2="&#x106;" k="40" />
    <hkern u1="&#x28;" u2="&#xf8;" k="36" />
    <hkern u1="&#x28;" u2="&#xf6;" k="36" />
    <hkern u1="&#x28;" u2="&#xf5;" k="36" />
    <hkern u1="&#x28;" u2="&#xf4;" k="36" />
    <hkern u1="&#x28;" u2="&#xf3;" k="36" />
    <hkern u1="&#x28;" u2="&#xf2;" k="36" />
    <hkern u1="&#x28;" u2="&#xf0;" k="36" />
    <hkern u1="&#x28;" u2="&#xeb;" k="36" />
    <hkern u1="&#x28;" u2="&#xea;" k="36" />
    <hkern u1="&#x28;" u2="&#xe9;" k="36" />
    <hkern u1="&#x28;" u2="&#xe8;" k="36" />
    <hkern u1="&#x28;" u2="&#xe7;" k="36" />
    <hkern u1="&#x28;" u2="&#xd8;" k="40" />
    <hkern u1="&#x28;" u2="&#xd6;" k="40" />
    <hkern u1="&#x28;" u2="&#xd5;" k="40" />
    <hkern u1="&#x28;" u2="&#xd4;" k="40" />
    <hkern u1="&#x28;" u2="&#xd3;" k="40" />
    <hkern u1="&#x28;" u2="&#xd2;" k="40" />
    <hkern u1="&#x28;" u2="&#xc7;" k="40" />
    <hkern u1="&#x28;" u2="&#xae;" k="40" />
    <hkern u1="&#x28;" u2="&#xa9;" k="40" />
    <hkern u1="&#x28;" u2="q" k="36" />
    <hkern u1="&#x28;" u2="o" k="36" />
    <hkern u1="&#x28;" u2="e" k="36" />
    <hkern u1="&#x28;" u2="d" k="36" />
    <hkern u1="&#x28;" u2="c" k="36" />
    <hkern u1="&#x28;" u2="Q" k="40" />
    <hkern u1="&#x28;" u2="O" k="40" />
    <hkern u1="&#x28;" u2="G" k="40" />
    <hkern u1="&#x28;" u2="C" k="40" />
    <hkern u1="&#x28;" u2="&#x40;" k="40" />
    <hkern u1="&#x2a;" u2="&#x2206;" k="191" />
    <hkern u1="&#x2a;" u2="&#x203a;" k="169" />
    <hkern u1="&#x2a;" u2="&#x2039;" k="169" />
    <hkern u1="&#x2a;" u2="&#x2022;" k="169" />
    <hkern u1="&#x2a;" u2="&#x201e;" k="213" />
    <hkern u1="&#x2a;" u2="&#x201a;" k="213" />
    <hkern u1="&#x2a;" u2="&#x2014;" k="169" />
    <hkern u1="&#x2a;" u2="&#x2013;" k="169" />
    <hkern u1="&#x2a;" u2="&#x178;" k="-36" />
    <hkern u1="&#x2a;" u2="&#x153;" k="96" />
    <hkern u1="&#x2a;" u2="&#x152;" k="42" />
    <hkern u1="&#x2a;" u2="&#x119;" k="96" />
    <hkern u1="&#x2a;" u2="&#x107;" k="96" />
    <hkern u1="&#x2a;" u2="&#x106;" k="42" />
    <hkern u1="&#x2a;" u2="&#x105;" k="66" />
    <hkern u1="&#x2a;" u2="&#x104;" k="191" />
    <hkern u1="&#x2a;" u2="&#xf8;" k="96" />
    <hkern u1="&#x2a;" u2="&#xf6;" k="96" />
    <hkern u1="&#x2a;" u2="&#xf5;" k="96" />
    <hkern u1="&#x2a;" u2="&#xf4;" k="96" />
    <hkern u1="&#x2a;" u2="&#xf3;" k="96" />
    <hkern u1="&#x2a;" u2="&#xf2;" k="96" />
    <hkern u1="&#x2a;" u2="&#xf0;" k="96" />
    <hkern u1="&#x2a;" u2="&#xeb;" k="96" />
    <hkern u1="&#x2a;" u2="&#xea;" k="96" />
    <hkern u1="&#x2a;" u2="&#xe9;" k="96" />
    <hkern u1="&#x2a;" u2="&#xe8;" k="96" />
    <hkern u1="&#x2a;" u2="&#xe7;" k="96" />
    <hkern u1="&#x2a;" u2="&#xe6;" k="66" />
    <hkern u1="&#x2a;" u2="&#xe5;" k="66" />
    <hkern u1="&#x2a;" u2="&#xe4;" k="66" />
    <hkern u1="&#x2a;" u2="&#xe3;" k="66" />
    <hkern u1="&#x2a;" u2="&#xe2;" k="66" />
    <hkern u1="&#x2a;" u2="&#xe1;" k="66" />
    <hkern u1="&#x2a;" u2="&#xe0;" k="66" />
    <hkern u1="&#x2a;" u2="&#xdd;" k="-36" />
    <hkern u1="&#x2a;" u2="&#xd8;" k="42" />
    <hkern u1="&#x2a;" u2="&#xd6;" k="42" />
    <hkern u1="&#x2a;" u2="&#xd5;" k="42" />
    <hkern u1="&#x2a;" u2="&#xd4;" k="42" />
    <hkern u1="&#x2a;" u2="&#xd3;" k="42" />
    <hkern u1="&#x2a;" u2="&#xd2;" k="42" />
    <hkern u1="&#x2a;" u2="&#xc7;" k="42" />
    <hkern u1="&#x2a;" u2="&#xc6;" k="191" />
    <hkern u1="&#x2a;" u2="&#xc5;" k="191" />
    <hkern u1="&#x2a;" u2="&#xc4;" k="191" />
    <hkern u1="&#x2a;" u2="&#xc3;" k="191" />
    <hkern u1="&#x2a;" u2="&#xc2;" k="191" />
    <hkern u1="&#x2a;" u2="&#xc1;" k="191" />
    <hkern u1="&#x2a;" u2="&#xc0;" k="191" />
    <hkern u1="&#x2a;" u2="&#xbb;" k="169" />
    <hkern u1="&#x2a;" u2="&#xb7;" k="169" />
    <hkern u1="&#x2a;" u2="&#xae;" k="42" />
    <hkern u1="&#x2a;" u2="&#xad;" k="169" />
    <hkern u1="&#x2a;" u2="&#xab;" k="169" />
    <hkern u1="&#x2a;" u2="&#xa9;" k="42" />
    <hkern u1="&#x2a;" u2="q" k="96" />
    <hkern u1="&#x2a;" u2="o" k="96" />
    <hkern u1="&#x2a;" u2="e" k="96" />
    <hkern u1="&#x2a;" u2="d" k="96" />
    <hkern u1="&#x2a;" u2="c" k="96" />
    <hkern u1="&#x2a;" u2="a" k="66" />
    <hkern u1="&#x2a;" u2="\" k="-44" />
    <hkern u1="&#x2a;" u2="Y" k="-36" />
    <hkern u1="&#x2a;" u2="W" k="-44" />
    <hkern u1="&#x2a;" u2="V" k="-44" />
    <hkern u1="&#x2a;" u2="Q" k="42" />
    <hkern u1="&#x2a;" u2="O" k="42" />
    <hkern u1="&#x2a;" u2="G" k="42" />
    <hkern u1="&#x2a;" u2="C" k="42" />
    <hkern u1="&#x2a;" u2="A" k="191" />
    <hkern u1="&#x2a;" u2="&#x40;" k="42" />
    <hkern u1="&#x2a;" u2="&#x2f;" k="191" />
    <hkern u1="&#x2a;" u2="&#x2e;" k="213" />
    <hkern u1="&#x2a;" u2="&#x2d;" k="169" />
    <hkern u1="&#x2a;" u2="&#x2c;" k="213" />
    <hkern u1="&#x2a;" u2="&#x26;" k="191" />
    <hkern u1="&#x2c;" u2="&#x2122;" k="213" />
    <hkern u1="&#x2c;" u2="&#x203a;" k="132" />
    <hkern u1="&#x2c;" u2="&#x2039;" k="132" />
    <hkern u1="&#x2c;" u2="&#x2022;" k="132" />
    <hkern u1="&#x2c;" u2="&#x201d;" k="213" />
    <hkern u1="&#x2c;" u2="&#x201c;" k="213" />
    <hkern u1="&#x2c;" u2="&#x2019;" k="213" />
    <hkern u1="&#x2c;" u2="&#x2018;" k="213" />
    <hkern u1="&#x2c;" u2="&#x2014;" k="132" />
    <hkern u1="&#x2c;" u2="&#x2013;" k="132" />
    <hkern u1="&#x2c;" u2="&#x178;" k="167" />
    <hkern u1="&#x2c;" u2="&#x152;" k="52" />
    <hkern u1="&#x2c;" u2="&#x106;" k="52" />
    <hkern u1="&#x2c;" u2="&#xff;" k="136" />
    <hkern u1="&#x2c;" u2="&#xfd;" k="136" />
    <hkern u1="&#x2c;" u2="&#xdd;" k="167" />
    <hkern u1="&#x2c;" u2="&#xd8;" k="52" />
    <hkern u1="&#x2c;" u2="&#xd6;" k="52" />
    <hkern u1="&#x2c;" u2="&#xd5;" k="52" />
    <hkern u1="&#x2c;" u2="&#xd4;" k="52" />
    <hkern u1="&#x2c;" u2="&#xd3;" k="52" />
    <hkern u1="&#x2c;" u2="&#xd2;" k="52" />
    <hkern u1="&#x2c;" u2="&#xc7;" k="52" />
    <hkern u1="&#x2c;" u2="&#xbb;" k="132" />
    <hkern u1="&#x2c;" u2="&#xba;" k="213" />
    <hkern u1="&#x2c;" u2="&#xb7;" k="132" />
    <hkern u1="&#x2c;" u2="&#xb0;" k="213" />
    <hkern u1="&#x2c;" u2="&#xae;" k="52" />
    <hkern u1="&#x2c;" u2="&#xad;" k="132" />
    <hkern u1="&#x2c;" u2="&#xab;" k="132" />
    <hkern u1="&#x2c;" u2="&#xaa;" k="213" />
    <hkern u1="&#x2c;" u2="&#xa9;" k="52" />
    <hkern u1="&#x2c;" u2="y" k="136" />
    <hkern u1="&#x2c;" u2="w" k="71" />
    <hkern u1="&#x2c;" u2="v" k="136" />
    <hkern u1="&#x2c;" u2="\" k="180" />
    <hkern u1="&#x2c;" u2="Y" k="167" />
    <hkern u1="&#x2c;" u2="W" k="131" />
    <hkern u1="&#x2c;" u2="V" k="180" />
    <hkern u1="&#x2c;" u2="T" k="180" />
    <hkern u1="&#x2c;" u2="Q" k="52" />
    <hkern u1="&#x2c;" u2="O" k="52" />
    <hkern u1="&#x2c;" u2="G" k="52" />
    <hkern u1="&#x2c;" u2="C" k="52" />
    <hkern u1="&#x2c;" u2="&#x40;" k="52" />
    <hkern u1="&#x2c;" u2="&#x2d;" k="132" />
    <hkern u1="&#x2c;" u2="&#x2a;" k="213" />
    <hkern u1="&#x2c;" u2="&#x27;" k="213" />
    <hkern u1="&#x2c;" u2="&#x22;" k="213" />
    <hkern u1="&#x2d;" u2="&#x2206;" k="67" />
    <hkern u1="&#x2d;" u2="&#x2122;" k="169" />
    <hkern u1="&#x2d;" u2="&#x201e;" k="132" />
    <hkern u1="&#x2d;" u2="&#x201d;" k="169" />
    <hkern u1="&#x2d;" u2="&#x201c;" k="169" />
    <hkern u1="&#x2d;" u2="&#x201a;" k="132" />
    <hkern u1="&#x2d;" u2="&#x2019;" k="169" />
    <hkern u1="&#x2d;" u2="&#x2018;" k="169" />
    <hkern u1="&#x2d;" u2="&#x17d;" k="48" />
    <hkern u1="&#x2d;" u2="&#x17b;" k="48" />
    <hkern u1="&#x2d;" u2="&#x179;" k="48" />
    <hkern u1="&#x2d;" u2="&#x178;" k="160" />
    <hkern u1="&#x2d;" u2="&#x104;" k="67" />
    <hkern u1="&#x2d;" u2="&#xdd;" k="160" />
    <hkern u1="&#x2d;" u2="&#xc6;" k="67" />
    <hkern u1="&#x2d;" u2="&#xc5;" k="67" />
    <hkern u1="&#x2d;" u2="&#xc4;" k="67" />
    <hkern u1="&#x2d;" u2="&#xc3;" k="67" />
    <hkern u1="&#x2d;" u2="&#xc2;" k="67" />
    <hkern u1="&#x2d;" u2="&#xc1;" k="67" />
    <hkern u1="&#x2d;" u2="&#xc0;" k="67" />
    <hkern u1="&#x2d;" u2="&#xba;" k="169" />
    <hkern u1="&#x2d;" u2="&#xb0;" k="169" />
    <hkern u1="&#x2d;" u2="&#xaa;" k="169" />
    <hkern u1="&#x2d;" u2="\" k="116" />
    <hkern u1="&#x2d;" u2="Z" k="48" />
    <hkern u1="&#x2d;" u2="Y" k="160" />
    <hkern u1="&#x2d;" u2="X" k="66" />
    <hkern u1="&#x2d;" u2="W" k="36" />
    <hkern u1="&#x2d;" u2="V" k="116" />
    <hkern u1="&#x2d;" u2="T" k="180" />
    <hkern u1="&#x2d;" u2="A" k="67" />
    <hkern u1="&#x2d;" u2="&#x2f;" k="67" />
    <hkern u1="&#x2d;" u2="&#x2e;" k="132" />
    <hkern u1="&#x2d;" u2="&#x2c;" k="132" />
    <hkern u1="&#x2d;" u2="&#x2a;" k="169" />
    <hkern u1="&#x2d;" u2="&#x27;" k="169" />
    <hkern u1="&#x2d;" u2="&#x26;" k="67" />
    <hkern u1="&#x2d;" u2="&#x22;" k="169" />
    <hkern u1="&#x2e;" u2="&#x2122;" k="213" />
    <hkern u1="&#x2e;" u2="&#x203a;" k="132" />
    <hkern u1="&#x2e;" u2="&#x2039;" k="132" />
    <hkern u1="&#x2e;" u2="&#x2022;" k="132" />
    <hkern u1="&#x2e;" u2="&#x201d;" k="213" />
    <hkern u1="&#x2e;" u2="&#x201c;" k="213" />
    <hkern u1="&#x2e;" u2="&#x2019;" k="213" />
    <hkern u1="&#x2e;" u2="&#x2018;" k="213" />
    <hkern u1="&#x2e;" u2="&#x2014;" k="132" />
    <hkern u1="&#x2e;" u2="&#x2013;" k="132" />
    <hkern u1="&#x2e;" u2="&#x178;" k="167" />
    <hkern u1="&#x2e;" u2="&#x152;" k="52" />
    <hkern u1="&#x2e;" u2="&#x106;" k="52" />
    <hkern u1="&#x2e;" u2="&#xff;" k="136" />
    <hkern u1="&#x2e;" u2="&#xfd;" k="136" />
    <hkern u1="&#x2e;" u2="&#xdd;" k="167" />
    <hkern u1="&#x2e;" u2="&#xd8;" k="52" />
    <hkern u1="&#x2e;" u2="&#xd6;" k="52" />
    <hkern u1="&#x2e;" u2="&#xd5;" k="52" />
    <hkern u1="&#x2e;" u2="&#xd4;" k="52" />
    <hkern u1="&#x2e;" u2="&#xd3;" k="52" />
    <hkern u1="&#x2e;" u2="&#xd2;" k="52" />
    <hkern u1="&#x2e;" u2="&#xc7;" k="52" />
    <hkern u1="&#x2e;" u2="&#xbb;" k="132" />
    <hkern u1="&#x2e;" u2="&#xba;" k="213" />
    <hkern u1="&#x2e;" u2="&#xb7;" k="132" />
    <hkern u1="&#x2e;" u2="&#xb0;" k="213" />
    <hkern u1="&#x2e;" u2="&#xae;" k="52" />
    <hkern u1="&#x2e;" u2="&#xad;" k="132" />
    <hkern u1="&#x2e;" u2="&#xab;" k="132" />
    <hkern u1="&#x2e;" u2="&#xaa;" k="213" />
    <hkern u1="&#x2e;" u2="&#xa9;" k="52" />
    <hkern u1="&#x2e;" u2="y" k="136" />
    <hkern u1="&#x2e;" u2="w" k="71" />
    <hkern u1="&#x2e;" u2="v" k="136" />
    <hkern u1="&#x2e;" u2="\" k="180" />
    <hkern u1="&#x2e;" u2="Y" k="167" />
    <hkern u1="&#x2e;" u2="W" k="131" />
    <hkern u1="&#x2e;" u2="V" k="180" />
    <hkern u1="&#x2e;" u2="T" k="180" />
    <hkern u1="&#x2e;" u2="Q" k="52" />
    <hkern u1="&#x2e;" u2="O" k="52" />
    <hkern u1="&#x2e;" u2="G" k="52" />
    <hkern u1="&#x2e;" u2="C" k="52" />
    <hkern u1="&#x2e;" u2="&#x40;" k="52" />
    <hkern u1="&#x2e;" u2="&#x2d;" k="132" />
    <hkern u1="&#x2e;" u2="&#x2a;" k="213" />
    <hkern u1="&#x2e;" u2="&#x27;" k="213" />
    <hkern u1="&#x2e;" u2="&#x22;" k="213" />
    <hkern u1="&#x2f;" u2="&#x2206;" k="169" />
    <hkern u1="&#x2f;" u2="&#x2122;" k="-44" />
    <hkern u1="&#x2f;" u2="&#x203a;" k="116" />
    <hkern u1="&#x2f;" u2="&#x2039;" k="116" />
    <hkern u1="&#x2f;" u2="&#x2022;" k="116" />
    <hkern u1="&#x2f;" u2="&#x201e;" k="196" />
    <hkern u1="&#x2f;" u2="&#x201d;" k="-44" />
    <hkern u1="&#x2f;" u2="&#x201c;" k="-44" />
    <hkern u1="&#x2f;" u2="&#x201a;" k="196" />
    <hkern u1="&#x2f;" u2="&#x2019;" k="-44" />
    <hkern u1="&#x2f;" u2="&#x2018;" k="-44" />
    <hkern u1="&#x2f;" u2="&#x2014;" k="116" />
    <hkern u1="&#x2f;" u2="&#x2013;" k="116" />
    <hkern u1="&#x2f;" u2="&#x17e;" k="91" />
    <hkern u1="&#x2f;" u2="&#x17c;" k="91" />
    <hkern u1="&#x2f;" u2="&#x17a;" k="91" />
    <hkern u1="&#x2f;" u2="&#x161;" k="108" />
    <hkern u1="&#x2f;" u2="&#x15b;" k="108" />
    <hkern u1="&#x2f;" u2="&#x153;" k="123" />
    <hkern u1="&#x2f;" u2="&#x152;" k="56" />
    <hkern u1="&#x2f;" u2="&#x144;" k="99" />
    <hkern u1="&#x2f;" u2="&#x131;" k="99" />
    <hkern u1="&#x2f;" u2="&#x119;" k="123" />
    <hkern u1="&#x2f;" u2="&#x107;" k="123" />
    <hkern u1="&#x2f;" u2="&#x106;" k="56" />
    <hkern u1="&#x2f;" u2="&#x105;" k="123" />
    <hkern u1="&#x2f;" u2="&#x104;" k="169" />
    <hkern u1="&#x2f;" u2="&#xff;" k="59" />
    <hkern u1="&#x2f;" u2="&#xfd;" k="59" />
    <hkern u1="&#x2f;" u2="&#xfc;" k="99" />
    <hkern u1="&#x2f;" u2="&#xfb;" k="99" />
    <hkern u1="&#x2f;" u2="&#xfa;" k="99" />
    <hkern u1="&#x2f;" u2="&#xf9;" k="99" />
    <hkern u1="&#x2f;" u2="&#xf8;" k="123" />
    <hkern u1="&#x2f;" u2="&#xf6;" k="123" />
    <hkern u1="&#x2f;" u2="&#xf5;" k="123" />
    <hkern u1="&#x2f;" u2="&#xf4;" k="123" />
    <hkern u1="&#x2f;" u2="&#xf3;" k="123" />
    <hkern u1="&#x2f;" u2="&#xf2;" k="123" />
    <hkern u1="&#x2f;" u2="&#xf1;" k="99" />
    <hkern u1="&#x2f;" u2="&#xf0;" k="123" />
    <hkern u1="&#x2f;" u2="&#xeb;" k="123" />
    <hkern u1="&#x2f;" u2="&#xea;" k="123" />
    <hkern u1="&#x2f;" u2="&#xe9;" k="123" />
    <hkern u1="&#x2f;" u2="&#xe8;" k="123" />
    <hkern u1="&#x2f;" u2="&#xe7;" k="123" />
    <hkern u1="&#x2f;" u2="&#xe6;" k="123" />
    <hkern u1="&#x2f;" u2="&#xe5;" k="123" />
    <hkern u1="&#x2f;" u2="&#xe4;" k="123" />
    <hkern u1="&#x2f;" u2="&#xe3;" k="123" />
    <hkern u1="&#x2f;" u2="&#xe2;" k="123" />
    <hkern u1="&#x2f;" u2="&#xe1;" k="123" />
    <hkern u1="&#x2f;" u2="&#xe0;" k="123" />
    <hkern u1="&#x2f;" u2="&#xd8;" k="56" />
    <hkern u1="&#x2f;" u2="&#xd6;" k="56" />
    <hkern u1="&#x2f;" u2="&#xd5;" k="56" />
    <hkern u1="&#x2f;" u2="&#xd4;" k="56" />
    <hkern u1="&#x2f;" u2="&#xd3;" k="56" />
    <hkern u1="&#x2f;" u2="&#xd2;" k="56" />
    <hkern u1="&#x2f;" u2="&#xc7;" k="56" />
    <hkern u1="&#x2f;" u2="&#xc6;" k="169" />
    <hkern u1="&#x2f;" u2="&#xc5;" k="169" />
    <hkern u1="&#x2f;" u2="&#xc4;" k="169" />
    <hkern u1="&#x2f;" u2="&#xc3;" k="169" />
    <hkern u1="&#x2f;" u2="&#xc2;" k="169" />
    <hkern u1="&#x2f;" u2="&#xc1;" k="169" />
    <hkern u1="&#x2f;" u2="&#xc0;" k="169" />
    <hkern u1="&#x2f;" u2="&#xbb;" k="116" />
    <hkern u1="&#x2f;" u2="&#xba;" k="-44" />
    <hkern u1="&#x2f;" u2="&#xb9;" k="-49" />
    <hkern u1="&#x2f;" u2="&#xb7;" k="116" />
    <hkern u1="&#x2f;" u2="&#xb5;" k="99" />
    <hkern u1="&#x2f;" u2="&#xb3;" k="-49" />
    <hkern u1="&#x2f;" u2="&#xb2;" k="-49" />
    <hkern u1="&#x2f;" u2="&#xb0;" k="-44" />
    <hkern u1="&#x2f;" u2="&#xae;" k="56" />
    <hkern u1="&#x2f;" u2="&#xad;" k="116" />
    <hkern u1="&#x2f;" u2="&#xab;" k="116" />
    <hkern u1="&#x2f;" u2="&#xaa;" k="-44" />
    <hkern u1="&#x2f;" u2="&#xa9;" k="56" />
    <hkern u1="&#x2f;" u2="z" k="91" />
    <hkern u1="&#x2f;" u2="y" k="59" />
    <hkern u1="&#x2f;" u2="x" k="67" />
    <hkern u1="&#x2f;" u2="v" k="59" />
    <hkern u1="&#x2f;" u2="u" k="99" />
    <hkern u1="&#x2f;" u2="t" k="46" />
    <hkern u1="&#x2f;" u2="s" k="108" />
    <hkern u1="&#x2f;" u2="r" k="99" />
    <hkern u1="&#x2f;" u2="q" k="123" />
    <hkern u1="&#x2f;" u2="p" k="99" />
    <hkern u1="&#x2f;" u2="o" k="123" />
    <hkern u1="&#x2f;" u2="n" k="99" />
    <hkern u1="&#x2f;" u2="m" k="99" />
    <hkern u1="&#x2f;" u2="g" k="138" />
    <hkern u1="&#x2f;" u2="f" k="30" />
    <hkern u1="&#x2f;" u2="e" k="123" />
    <hkern u1="&#x2f;" u2="d" k="123" />
    <hkern u1="&#x2f;" u2="c" k="123" />
    <hkern u1="&#x2f;" u2="a" k="123" />
    <hkern u1="&#x2f;" u2="Q" k="56" />
    <hkern u1="&#x2f;" u2="O" k="56" />
    <hkern u1="&#x2f;" u2="J" k="156" />
    <hkern u1="&#x2f;" u2="G" k="56" />
    <hkern u1="&#x2f;" u2="C" k="56" />
    <hkern u1="&#x2f;" u2="A" k="169" />
    <hkern u1="&#x2f;" u2="&#x40;" k="56" />
    <hkern u1="&#x2f;" u2="&#x3f;" k="-39" />
    <hkern u1="&#x2f;" u2="&#x3b;" k="99" />
    <hkern u1="&#x2f;" u2="&#x3a;" k="99" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="169" />
    <hkern u1="&#x2f;" u2="&#x2e;" k="196" />
    <hkern u1="&#x2f;" u2="&#x2d;" k="116" />
    <hkern u1="&#x2f;" u2="&#x2c;" k="196" />
    <hkern u1="&#x2f;" u2="&#x2a;" k="-44" />
    <hkern u1="&#x2f;" u2="&#x27;" k="-44" />
    <hkern u1="&#x2f;" u2="&#x26;" k="169" />
    <hkern u1="&#x2f;" u2="&#x22;" k="-44" />
    <hkern u1="&#x40;" u2="&#x2206;" k="51" />
    <hkern u1="&#x40;" u2="&#x2122;" k="42" />
    <hkern u1="&#x40;" u2="&#x201e;" k="52" />
    <hkern u1="&#x40;" u2="&#x201d;" k="42" />
    <hkern u1="&#x40;" u2="&#x201c;" k="42" />
    <hkern u1="&#x40;" u2="&#x201a;" k="52" />
    <hkern u1="&#x40;" u2="&#x2019;" k="42" />
    <hkern u1="&#x40;" u2="&#x2018;" k="42" />
    <hkern u1="&#x40;" u2="&#x17d;" k="64" />
    <hkern u1="&#x40;" u2="&#x17b;" k="64" />
    <hkern u1="&#x40;" u2="&#x179;" k="64" />
    <hkern u1="&#x40;" u2="&#x178;" k="80" />
    <hkern u1="&#x40;" u2="&#x104;" k="51" />
    <hkern u1="&#x40;" u2="&#xdd;" k="80" />
    <hkern u1="&#x40;" u2="&#xc6;" k="51" />
    <hkern u1="&#x40;" u2="&#xc5;" k="51" />
    <hkern u1="&#x40;" u2="&#xc4;" k="51" />
    <hkern u1="&#x40;" u2="&#xc3;" k="51" />
    <hkern u1="&#x40;" u2="&#xc2;" k="51" />
    <hkern u1="&#x40;" u2="&#xc1;" k="51" />
    <hkern u1="&#x40;" u2="&#xc0;" k="51" />
    <hkern u1="&#x40;" u2="&#xba;" k="42" />
    <hkern u1="&#x40;" u2="&#xb0;" k="42" />
    <hkern u1="&#x40;" u2="&#xaa;" k="42" />
    <hkern u1="&#x40;" u2="&#x7d;" k="40" />
    <hkern u1="&#x40;" u2="]" k="40" />
    <hkern u1="&#x40;" u2="\" k="56" />
    <hkern u1="&#x40;" u2="Z" k="64" />
    <hkern u1="&#x40;" u2="Y" k="80" />
    <hkern u1="&#x40;" u2="X" k="30" />
    <hkern u1="&#x40;" u2="V" k="56" />
    <hkern u1="&#x40;" u2="T" k="78" />
    <hkern u1="&#x40;" u2="A" k="51" />
    <hkern u1="&#x40;" u2="&#x2f;" k="51" />
    <hkern u1="&#x40;" u2="&#x2e;" k="52" />
    <hkern u1="&#x40;" u2="&#x2c;" k="52" />
    <hkern u1="&#x40;" u2="&#x2a;" k="42" />
    <hkern u1="&#x40;" u2="&#x29;" k="40" />
    <hkern u1="&#x40;" u2="&#x27;" k="42" />
    <hkern u1="&#x40;" u2="&#x26;" k="51" />
    <hkern u1="&#x40;" u2="&#x22;" k="42" />
    <hkern u1="A" u2="&#x2122;" k="191" />
    <hkern u1="A" u2="&#x203a;" k="67" />
    <hkern u1="A" u2="&#x2039;" k="67" />
    <hkern u1="A" u2="&#x2022;" k="67" />
    <hkern u1="A" u2="&#x201d;" k="191" />
    <hkern u1="A" u2="&#x201c;" k="191" />
    <hkern u1="A" u2="&#x2019;" k="191" />
    <hkern u1="A" u2="&#x2018;" k="191" />
    <hkern u1="A" u2="&#x2014;" k="67" />
    <hkern u1="A" u2="&#x2013;" k="67" />
    <hkern u1="A" u2="&#x178;" k="182" />
    <hkern u1="A" u2="&#x152;" k="51" />
    <hkern u1="A" u2="&#x106;" k="51" />
    <hkern u1="A" u2="&#xff;" k="91" />
    <hkern u1="A" u2="&#xfd;" k="91" />
    <hkern u1="A" u2="&#xdd;" k="182" />
    <hkern u1="A" u2="&#xdc;" k="52" />
    <hkern u1="A" u2="&#xdb;" k="52" />
    <hkern u1="A" u2="&#xda;" k="52" />
    <hkern u1="A" u2="&#xd9;" k="52" />
    <hkern u1="A" u2="&#xd8;" k="51" />
    <hkern u1="A" u2="&#xd6;" k="51" />
    <hkern u1="A" u2="&#xd5;" k="51" />
    <hkern u1="A" u2="&#xd4;" k="51" />
    <hkern u1="A" u2="&#xd3;" k="51" />
    <hkern u1="A" u2="&#xd2;" k="51" />
    <hkern u1="A" u2="&#xc7;" k="51" />
    <hkern u1="A" u2="&#xbb;" k="67" />
    <hkern u1="A" u2="&#xba;" k="191" />
    <hkern u1="A" u2="&#xb9;" k="202" />
    <hkern u1="A" u2="&#xb7;" k="67" />
    <hkern u1="A" u2="&#xb3;" k="202" />
    <hkern u1="A" u2="&#xb2;" k="202" />
    <hkern u1="A" u2="&#xb0;" k="191" />
    <hkern u1="A" u2="&#xae;" k="51" />
    <hkern u1="A" u2="&#xad;" k="67" />
    <hkern u1="A" u2="&#xab;" k="67" />
    <hkern u1="A" u2="&#xaa;" k="191" />
    <hkern u1="A" u2="&#xa9;" k="51" />
    <hkern u1="A" u2="y" k="91" />
    <hkern u1="A" u2="v" k="91" />
    <hkern u1="A" u2="\" k="169" />
    <hkern u1="A" u2="Y" k="182" />
    <hkern u1="A" u2="W" k="102" />
    <hkern u1="A" u2="V" k="169" />
    <hkern u1="A" u2="U" k="52" />
    <hkern u1="A" u2="T" k="147" />
    <hkern u1="A" u2="Q" k="51" />
    <hkern u1="A" u2="O" k="51" />
    <hkern u1="A" u2="J" k="-56" />
    <hkern u1="A" u2="G" k="51" />
    <hkern u1="A" u2="C" k="51" />
    <hkern u1="A" u2="&#x40;" k="51" />
    <hkern u1="A" u2="&#x3f;" k="63" />
    <hkern u1="A" u2="&#x2d;" k="67" />
    <hkern u1="A" u2="&#x2a;" k="191" />
    <hkern u1="A" u2="&#x27;" k="191" />
    <hkern u1="A" u2="&#x22;" k="191" />
    <hkern u1="C" u2="&#x203a;" k="144" />
    <hkern u1="C" u2="&#x2039;" k="144" />
    <hkern u1="C" u2="&#x2022;" k="144" />
    <hkern u1="C" u2="&#x2014;" k="144" />
    <hkern u1="C" u2="&#x2013;" k="144" />
    <hkern u1="C" u2="&#xbb;" k="144" />
    <hkern u1="C" u2="&#xb7;" k="144" />
    <hkern u1="C" u2="&#xad;" k="144" />
    <hkern u1="C" u2="&#xab;" k="144" />
    <hkern u1="C" u2="&#x2d;" k="144" />
    <hkern u1="D" u2="&#x2206;" k="51" />
    <hkern u1="D" u2="&#x2122;" k="42" />
    <hkern u1="D" u2="&#x201e;" k="52" />
    <hkern u1="D" u2="&#x201d;" k="42" />
    <hkern u1="D" u2="&#x201c;" k="42" />
    <hkern u1="D" u2="&#x201a;" k="52" />
    <hkern u1="D" u2="&#x2019;" k="42" />
    <hkern u1="D" u2="&#x2018;" k="42" />
    <hkern u1="D" u2="&#x17d;" k="64" />
    <hkern u1="D" u2="&#x17b;" k="64" />
    <hkern u1="D" u2="&#x179;" k="64" />
    <hkern u1="D" u2="&#x178;" k="80" />
    <hkern u1="D" u2="&#x104;" k="51" />
    <hkern u1="D" u2="&#xdd;" k="80" />
    <hkern u1="D" u2="&#xc6;" k="51" />
    <hkern u1="D" u2="&#xc5;" k="51" />
    <hkern u1="D" u2="&#xc4;" k="51" />
    <hkern u1="D" u2="&#xc3;" k="51" />
    <hkern u1="D" u2="&#xc2;" k="51" />
    <hkern u1="D" u2="&#xc1;" k="51" />
    <hkern u1="D" u2="&#xc0;" k="51" />
    <hkern u1="D" u2="&#xba;" k="42" />
    <hkern u1="D" u2="&#xb0;" k="42" />
    <hkern u1="D" u2="&#xaa;" k="42" />
    <hkern u1="D" u2="&#x7d;" k="40" />
    <hkern u1="D" u2="]" k="40" />
    <hkern u1="D" u2="\" k="56" />
    <hkern u1="D" u2="Z" k="64" />
    <hkern u1="D" u2="Y" k="80" />
    <hkern u1="D" u2="X" k="30" />
    <hkern u1="D" u2="V" k="56" />
    <hkern u1="D" u2="T" k="78" />
    <hkern u1="D" u2="A" k="51" />
    <hkern u1="D" u2="&#x2f;" k="51" />
    <hkern u1="D" u2="&#x2e;" k="52" />
    <hkern u1="D" u2="&#x2c;" k="52" />
    <hkern u1="D" u2="&#x2a;" k="42" />
    <hkern u1="D" u2="&#x29;" k="40" />
    <hkern u1="D" u2="&#x27;" k="42" />
    <hkern u1="D" u2="&#x26;" k="51" />
    <hkern u1="D" u2="&#x22;" k="42" />
    <hkern u1="F" u2="&#x2206;" k="147" />
    <hkern u1="F" u2="&#x201e;" k="180" />
    <hkern u1="F" u2="&#x201a;" k="180" />
    <hkern u1="F" u2="&#x153;" k="64" />
    <hkern u1="F" u2="&#x144;" k="60" />
    <hkern u1="F" u2="&#x131;" k="60" />
    <hkern u1="F" u2="&#x119;" k="64" />
    <hkern u1="F" u2="&#x107;" k="64" />
    <hkern u1="F" u2="&#x104;" k="147" />
    <hkern u1="F" u2="&#xfc;" k="60" />
    <hkern u1="F" u2="&#xfb;" k="60" />
    <hkern u1="F" u2="&#xfa;" k="60" />
    <hkern u1="F" u2="&#xf9;" k="60" />
    <hkern u1="F" u2="&#xf8;" k="64" />
    <hkern u1="F" u2="&#xf6;" k="64" />
    <hkern u1="F" u2="&#xf5;" k="64" />
    <hkern u1="F" u2="&#xf4;" k="64" />
    <hkern u1="F" u2="&#xf3;" k="64" />
    <hkern u1="F" u2="&#xf2;" k="64" />
    <hkern u1="F" u2="&#xf1;" k="60" />
    <hkern u1="F" u2="&#xf0;" k="64" />
    <hkern u1="F" u2="&#xeb;" k="64" />
    <hkern u1="F" u2="&#xea;" k="64" />
    <hkern u1="F" u2="&#xe9;" k="64" />
    <hkern u1="F" u2="&#xe8;" k="64" />
    <hkern u1="F" u2="&#xe7;" k="64" />
    <hkern u1="F" u2="&#xc6;" k="147" />
    <hkern u1="F" u2="&#xc5;" k="147" />
    <hkern u1="F" u2="&#xc4;" k="147" />
    <hkern u1="F" u2="&#xc3;" k="147" />
    <hkern u1="F" u2="&#xc2;" k="147" />
    <hkern u1="F" u2="&#xc1;" k="147" />
    <hkern u1="F" u2="&#xc0;" k="147" />
    <hkern u1="F" u2="&#xb5;" k="60" />
    <hkern u1="F" u2="u" k="60" />
    <hkern u1="F" u2="r" k="60" />
    <hkern u1="F" u2="q" k="64" />
    <hkern u1="F" u2="p" k="60" />
    <hkern u1="F" u2="o" k="64" />
    <hkern u1="F" u2="n" k="60" />
    <hkern u1="F" u2="m" k="60" />
    <hkern u1="F" u2="e" k="64" />
    <hkern u1="F" u2="d" k="64" />
    <hkern u1="F" u2="c" k="64" />
    <hkern u1="F" u2="J" k="189" />
    <hkern u1="F" u2="A" k="147" />
    <hkern u1="F" u2="&#x3f;" k="-30" />
    <hkern u1="F" u2="&#x3b;" k="60" />
    <hkern u1="F" u2="&#x3a;" k="60" />
    <hkern u1="F" u2="&#x2f;" k="147" />
    <hkern u1="F" u2="&#x2e;" k="180" />
    <hkern u1="F" u2="&#x2c;" k="180" />
    <hkern u1="F" u2="&#x26;" k="147" />
    <hkern u1="J" u2="&#x2206;" k="52" />
    <hkern u1="J" u2="&#x201e;" k="50" />
    <hkern u1="J" u2="&#x201a;" k="50" />
    <hkern u1="J" u2="&#x104;" k="52" />
    <hkern u1="J" u2="&#xc6;" k="52" />
    <hkern u1="J" u2="&#xc5;" k="52" />
    <hkern u1="J" u2="&#xc4;" k="52" />
    <hkern u1="J" u2="&#xc3;" k="52" />
    <hkern u1="J" u2="&#xc2;" k="52" />
    <hkern u1="J" u2="&#xc1;" k="52" />
    <hkern u1="J" u2="&#xc0;" k="52" />
    <hkern u1="J" u2="A" k="52" />
    <hkern u1="J" u2="&#x2f;" k="52" />
    <hkern u1="J" u2="&#x2e;" k="50" />
    <hkern u1="J" u2="&#x2c;" k="50" />
    <hkern u1="J" u2="&#x26;" k="52" />
    <hkern u1="K" u2="&#x203a;" k="66" />
    <hkern u1="K" u2="&#x2039;" k="66" />
    <hkern u1="K" u2="&#x2022;" k="66" />
    <hkern u1="K" u2="&#x2014;" k="66" />
    <hkern u1="K" u2="&#x2013;" k="66" />
    <hkern u1="K" u2="&#x153;" k="43" />
    <hkern u1="K" u2="&#x152;" k="30" />
    <hkern u1="K" u2="&#x119;" k="43" />
    <hkern u1="K" u2="&#x107;" k="43" />
    <hkern u1="K" u2="&#x106;" k="30" />
    <hkern u1="K" u2="&#xff;" k="73" />
    <hkern u1="K" u2="&#xfd;" k="73" />
    <hkern u1="K" u2="&#xf8;" k="43" />
    <hkern u1="K" u2="&#xf6;" k="43" />
    <hkern u1="K" u2="&#xf5;" k="43" />
    <hkern u1="K" u2="&#xf4;" k="43" />
    <hkern u1="K" u2="&#xf3;" k="43" />
    <hkern u1="K" u2="&#xf2;" k="43" />
    <hkern u1="K" u2="&#xf0;" k="43" />
    <hkern u1="K" u2="&#xeb;" k="43" />
    <hkern u1="K" u2="&#xea;" k="43" />
    <hkern u1="K" u2="&#xe9;" k="43" />
    <hkern u1="K" u2="&#xe8;" k="43" />
    <hkern u1="K" u2="&#xe7;" k="43" />
    <hkern u1="K" u2="&#xd8;" k="30" />
    <hkern u1="K" u2="&#xd6;" k="30" />
    <hkern u1="K" u2="&#xd5;" k="30" />
    <hkern u1="K" u2="&#xd4;" k="30" />
    <hkern u1="K" u2="&#xd3;" k="30" />
    <hkern u1="K" u2="&#xd2;" k="30" />
    <hkern u1="K" u2="&#xc7;" k="30" />
    <hkern u1="K" u2="&#xbb;" k="66" />
    <hkern u1="K" u2="&#xb7;" k="66" />
    <hkern u1="K" u2="&#xae;" k="30" />
    <hkern u1="K" u2="&#xad;" k="66" />
    <hkern u1="K" u2="&#xab;" k="66" />
    <hkern u1="K" u2="&#xa9;" k="30" />
    <hkern u1="K" u2="y" k="73" />
    <hkern u1="K" u2="w" k="52" />
    <hkern u1="K" u2="v" k="73" />
    <hkern u1="K" u2="t" k="91" />
    <hkern u1="K" u2="q" k="43" />
    <hkern u1="K" u2="o" k="43" />
    <hkern u1="K" u2="f" k="56" />
    <hkern u1="K" u2="e" k="43" />
    <hkern u1="K" u2="d" k="43" />
    <hkern u1="K" u2="c" k="43" />
    <hkern u1="K" u2="Q" k="30" />
    <hkern u1="K" u2="O" k="30" />
    <hkern u1="K" u2="G" k="30" />
    <hkern u1="K" u2="C" k="30" />
    <hkern u1="K" u2="&#x40;" k="30" />
    <hkern u1="K" u2="&#x2d;" k="66" />
    <hkern u1="L" u2="&#x2122;" k="284" />
    <hkern u1="L" u2="&#x203a;" k="178" />
    <hkern u1="L" u2="&#x2039;" k="178" />
    <hkern u1="L" u2="&#x2022;" k="178" />
    <hkern u1="L" u2="&#x201e;" k="-58" />
    <hkern u1="L" u2="&#x201d;" k="284" />
    <hkern u1="L" u2="&#x201c;" k="284" />
    <hkern u1="L" u2="&#x201a;" k="-58" />
    <hkern u1="L" u2="&#x2019;" k="284" />
    <hkern u1="L" u2="&#x2018;" k="284" />
    <hkern u1="L" u2="&#x2014;" k="178" />
    <hkern u1="L" u2="&#x2013;" k="178" />
    <hkern u1="L" u2="&#x178;" k="227" />
    <hkern u1="L" u2="&#x153;" k="38" />
    <hkern u1="L" u2="&#x152;" k="80" />
    <hkern u1="L" u2="&#x119;" k="38" />
    <hkern u1="L" u2="&#x107;" k="38" />
    <hkern u1="L" u2="&#x106;" k="80" />
    <hkern u1="L" u2="&#xff;" k="119" />
    <hkern u1="L" u2="&#xfd;" k="119" />
    <hkern u1="L" u2="&#xf8;" k="38" />
    <hkern u1="L" u2="&#xf6;" k="38" />
    <hkern u1="L" u2="&#xf5;" k="38" />
    <hkern u1="L" u2="&#xf4;" k="38" />
    <hkern u1="L" u2="&#xf3;" k="38" />
    <hkern u1="L" u2="&#xf2;" k="38" />
    <hkern u1="L" u2="&#xf0;" k="38" />
    <hkern u1="L" u2="&#xeb;" k="38" />
    <hkern u1="L" u2="&#xea;" k="38" />
    <hkern u1="L" u2="&#xe9;" k="38" />
    <hkern u1="L" u2="&#xe8;" k="38" />
    <hkern u1="L" u2="&#xe7;" k="38" />
    <hkern u1="L" u2="&#xdd;" k="227" />
    <hkern u1="L" u2="&#xd8;" k="80" />
    <hkern u1="L" u2="&#xd6;" k="80" />
    <hkern u1="L" u2="&#xd5;" k="80" />
    <hkern u1="L" u2="&#xd4;" k="80" />
    <hkern u1="L" u2="&#xd3;" k="80" />
    <hkern u1="L" u2="&#xd2;" k="80" />
    <hkern u1="L" u2="&#xc7;" k="80" />
    <hkern u1="L" u2="&#xbb;" k="178" />
    <hkern u1="L" u2="&#xba;" k="284" />
    <hkern u1="L" u2="&#xb9;" k="211" />
    <hkern u1="L" u2="&#xb7;" k="178" />
    <hkern u1="L" u2="&#xb3;" k="211" />
    <hkern u1="L" u2="&#xb2;" k="211" />
    <hkern u1="L" u2="&#xb0;" k="284" />
    <hkern u1="L" u2="&#xae;" k="80" />
    <hkern u1="L" u2="&#xad;" k="178" />
    <hkern u1="L" u2="&#xab;" k="178" />
    <hkern u1="L" u2="&#xaa;" k="284" />
    <hkern u1="L" u2="&#xa9;" k="80" />
    <hkern u1="L" u2="y" k="119" />
    <hkern u1="L" u2="w" k="80" />
    <hkern u1="L" u2="v" k="119" />
    <hkern u1="L" u2="q" k="38" />
    <hkern u1="L" u2="o" k="38" />
    <hkern u1="L" u2="e" k="38" />
    <hkern u1="L" u2="d" k="38" />
    <hkern u1="L" u2="c" k="38" />
    <hkern u1="L" u2="\" k="191" />
    <hkern u1="L" u2="Y" k="227" />
    <hkern u1="L" u2="W" k="167" />
    <hkern u1="L" u2="V" k="191" />
    <hkern u1="L" u2="T" k="176" />
    <hkern u1="L" u2="Q" k="80" />
    <hkern u1="L" u2="O" k="80" />
    <hkern u1="L" u2="G" k="80" />
    <hkern u1="L" u2="C" k="80" />
    <hkern u1="L" u2="&#x40;" k="80" />
    <hkern u1="L" u2="&#x3f;" k="50" />
    <hkern u1="L" u2="&#x2e;" k="-58" />
    <hkern u1="L" u2="&#x2d;" k="178" />
    <hkern u1="L" u2="&#x2c;" k="-58" />
    <hkern u1="L" u2="&#x2a;" k="284" />
    <hkern u1="L" u2="&#x27;" k="284" />
    <hkern u1="L" u2="&#x22;" k="284" />
    <hkern u1="O" u2="&#x2206;" k="51" />
    <hkern u1="O" u2="&#x2122;" k="42" />
    <hkern u1="O" u2="&#x201e;" k="52" />
    <hkern u1="O" u2="&#x201d;" k="42" />
    <hkern u1="O" u2="&#x201c;" k="42" />
    <hkern u1="O" u2="&#x201a;" k="52" />
    <hkern u1="O" u2="&#x2019;" k="42" />
    <hkern u1="O" u2="&#x2018;" k="42" />
    <hkern u1="O" u2="&#x17d;" k="64" />
    <hkern u1="O" u2="&#x17b;" k="64" />
    <hkern u1="O" u2="&#x179;" k="64" />
    <hkern u1="O" u2="&#x178;" k="80" />
    <hkern u1="O" u2="&#x104;" k="51" />
    <hkern u1="O" u2="&#xdd;" k="80" />
    <hkern u1="O" u2="&#xc6;" k="51" />
    <hkern u1="O" u2="&#xc5;" k="51" />
    <hkern u1="O" u2="&#xc4;" k="51" />
    <hkern u1="O" u2="&#xc3;" k="51" />
    <hkern u1="O" u2="&#xc2;" k="51" />
    <hkern u1="O" u2="&#xc1;" k="51" />
    <hkern u1="O" u2="&#xc0;" k="51" />
    <hkern u1="O" u2="&#xba;" k="42" />
    <hkern u1="O" u2="&#xb0;" k="42" />
    <hkern u1="O" u2="&#xaa;" k="42" />
    <hkern u1="O" u2="&#x7d;" k="40" />
    <hkern u1="O" u2="]" k="40" />
    <hkern u1="O" u2="\" k="56" />
    <hkern u1="O" u2="Z" k="64" />
    <hkern u1="O" u2="Y" k="80" />
    <hkern u1="O" u2="X" k="30" />
    <hkern u1="O" u2="V" k="56" />
    <hkern u1="O" u2="T" k="78" />
    <hkern u1="O" u2="A" k="51" />
    <hkern u1="O" u2="&#x2f;" k="51" />
    <hkern u1="O" u2="&#x2e;" k="52" />
    <hkern u1="O" u2="&#x2c;" k="52" />
    <hkern u1="O" u2="&#x2a;" k="42" />
    <hkern u1="O" u2="&#x29;" k="40" />
    <hkern u1="O" u2="&#x27;" k="42" />
    <hkern u1="O" u2="&#x26;" k="51" />
    <hkern u1="O" u2="&#x22;" k="42" />
    <hkern u1="P" u2="&#x2206;" k="155" />
    <hkern u1="P" u2="&#x201e;" k="265" />
    <hkern u1="P" u2="&#x201a;" k="265" />
    <hkern u1="P" u2="&#x153;" k="30" />
    <hkern u1="P" u2="&#x119;" k="30" />
    <hkern u1="P" u2="&#x107;" k="30" />
    <hkern u1="P" u2="&#x105;" k="50" />
    <hkern u1="P" u2="&#x104;" k="155" />
    <hkern u1="P" u2="&#xf8;" k="30" />
    <hkern u1="P" u2="&#xf6;" k="30" />
    <hkern u1="P" u2="&#xf5;" k="30" />
    <hkern u1="P" u2="&#xf4;" k="30" />
    <hkern u1="P" u2="&#xf3;" k="30" />
    <hkern u1="P" u2="&#xf2;" k="30" />
    <hkern u1="P" u2="&#xf0;" k="30" />
    <hkern u1="P" u2="&#xeb;" k="30" />
    <hkern u1="P" u2="&#xea;" k="30" />
    <hkern u1="P" u2="&#xe9;" k="30" />
    <hkern u1="P" u2="&#xe8;" k="30" />
    <hkern u1="P" u2="&#xe7;" k="30" />
    <hkern u1="P" u2="&#xe6;" k="50" />
    <hkern u1="P" u2="&#xe5;" k="50" />
    <hkern u1="P" u2="&#xe4;" k="50" />
    <hkern u1="P" u2="&#xe3;" k="50" />
    <hkern u1="P" u2="&#xe2;" k="50" />
    <hkern u1="P" u2="&#xe1;" k="50" />
    <hkern u1="P" u2="&#xe0;" k="50" />
    <hkern u1="P" u2="&#xc6;" k="155" />
    <hkern u1="P" u2="&#xc5;" k="155" />
    <hkern u1="P" u2="&#xc4;" k="155" />
    <hkern u1="P" u2="&#xc3;" k="155" />
    <hkern u1="P" u2="&#xc2;" k="155" />
    <hkern u1="P" u2="&#xc1;" k="155" />
    <hkern u1="P" u2="&#xc0;" k="155" />
    <hkern u1="P" u2="q" k="30" />
    <hkern u1="P" u2="o" k="30" />
    <hkern u1="P" u2="e" k="30" />
    <hkern u1="P" u2="d" k="30" />
    <hkern u1="P" u2="c" k="30" />
    <hkern u1="P" u2="a" k="50" />
    <hkern u1="P" u2="J" k="191" />
    <hkern u1="P" u2="A" k="155" />
    <hkern u1="P" u2="&#x2f;" k="155" />
    <hkern u1="P" u2="&#x2e;" k="265" />
    <hkern u1="P" u2="&#x2c;" k="265" />
    <hkern u1="P" u2="&#x26;" k="155" />
    <hkern u1="Q" u2="&#x2206;" k="51" />
    <hkern u1="Q" u2="&#x2122;" k="42" />
    <hkern u1="Q" u2="&#x201e;" k="52" />
    <hkern u1="Q" u2="&#x201d;" k="42" />
    <hkern u1="Q" u2="&#x201c;" k="42" />
    <hkern u1="Q" u2="&#x201a;" k="52" />
    <hkern u1="Q" u2="&#x2019;" k="42" />
    <hkern u1="Q" u2="&#x2018;" k="42" />
    <hkern u1="Q" u2="&#x17d;" k="64" />
    <hkern u1="Q" u2="&#x17b;" k="64" />
    <hkern u1="Q" u2="&#x179;" k="64" />
    <hkern u1="Q" u2="&#x178;" k="80" />
    <hkern u1="Q" u2="&#x104;" k="51" />
    <hkern u1="Q" u2="&#xdd;" k="80" />
    <hkern u1="Q" u2="&#xc6;" k="51" />
    <hkern u1="Q" u2="&#xc5;" k="51" />
    <hkern u1="Q" u2="&#xc4;" k="51" />
    <hkern u1="Q" u2="&#xc3;" k="51" />
    <hkern u1="Q" u2="&#xc2;" k="51" />
    <hkern u1="Q" u2="&#xc1;" k="51" />
    <hkern u1="Q" u2="&#xc0;" k="51" />
    <hkern u1="Q" u2="&#xba;" k="42" />
    <hkern u1="Q" u2="&#xb0;" k="42" />
    <hkern u1="Q" u2="&#xaa;" k="42" />
    <hkern u1="Q" u2="&#x7d;" k="40" />
    <hkern u1="Q" u2="]" k="40" />
    <hkern u1="Q" u2="\" k="56" />
    <hkern u1="Q" u2="Z" k="64" />
    <hkern u1="Q" u2="Y" k="80" />
    <hkern u1="Q" u2="X" k="30" />
    <hkern u1="Q" u2="V" k="56" />
    <hkern u1="Q" u2="T" k="78" />
    <hkern u1="Q" u2="A" k="51" />
    <hkern u1="Q" u2="&#x2f;" k="51" />
    <hkern u1="Q" u2="&#x2e;" k="52" />
    <hkern u1="Q" u2="&#x2c;" k="52" />
    <hkern u1="Q" u2="&#x2a;" k="42" />
    <hkern u1="Q" u2="&#x29;" k="40" />
    <hkern u1="Q" u2="&#x27;" k="42" />
    <hkern u1="Q" u2="&#x26;" k="51" />
    <hkern u1="Q" u2="&#x22;" k="42" />
    <hkern u1="R" u2="&#x152;" k="48" />
    <hkern u1="R" u2="&#x106;" k="48" />
    <hkern u1="R" u2="&#xdc;" k="51" />
    <hkern u1="R" u2="&#xdb;" k="51" />
    <hkern u1="R" u2="&#xda;" k="51" />
    <hkern u1="R" u2="&#xd9;" k="51" />
    <hkern u1="R" u2="&#xd8;" k="48" />
    <hkern u1="R" u2="&#xd6;" k="48" />
    <hkern u1="R" u2="&#xd5;" k="48" />
    <hkern u1="R" u2="&#xd4;" k="48" />
    <hkern u1="R" u2="&#xd3;" k="48" />
    <hkern u1="R" u2="&#xd2;" k="48" />
    <hkern u1="R" u2="&#xc7;" k="48" />
    <hkern u1="R" u2="&#xae;" k="48" />
    <hkern u1="R" u2="&#xa9;" k="48" />
    <hkern u1="R" u2="U" k="51" />
    <hkern u1="R" u2="T" k="56" />
    <hkern u1="R" u2="Q" k="48" />
    <hkern u1="R" u2="O" k="48" />
    <hkern u1="R" u2="G" k="48" />
    <hkern u1="R" u2="C" k="48" />
    <hkern u1="R" u2="&#x40;" k="48" />
    <hkern u1="T" u2="&#x2206;" k="147" />
    <hkern u1="T" u2="&#x203a;" k="180" />
    <hkern u1="T" u2="&#x2039;" k="180" />
    <hkern u1="T" u2="&#x2022;" k="180" />
    <hkern u1="T" u2="&#x201e;" k="180" />
    <hkern u1="T" u2="&#x201a;" k="180" />
    <hkern u1="T" u2="&#x2014;" k="180" />
    <hkern u1="T" u2="&#x2013;" k="180" />
    <hkern u1="T" u2="&#x17e;" k="120" />
    <hkern u1="T" u2="&#x17c;" k="120" />
    <hkern u1="T" u2="&#x17a;" k="120" />
    <hkern u1="T" u2="&#x161;" k="140" />
    <hkern u1="T" u2="&#x15b;" k="140" />
    <hkern u1="T" u2="&#x153;" k="204" />
    <hkern u1="T" u2="&#x152;" k="78" />
    <hkern u1="T" u2="&#x144;" k="160" />
    <hkern u1="T" u2="&#x131;" k="160" />
    <hkern u1="T" u2="&#x119;" k="204" />
    <hkern u1="T" u2="&#x107;" k="204" />
    <hkern u1="T" u2="&#x106;" k="78" />
    <hkern u1="T" u2="&#x105;" k="244" />
    <hkern u1="T" u2="&#x104;" k="147" />
    <hkern u1="T" u2="&#xff;" k="180" />
    <hkern u1="T" u2="&#xfd;" k="180" />
    <hkern u1="T" u2="&#xfc;" k="160" />
    <hkern u1="T" u2="&#xfb;" k="160" />
    <hkern u1="T" u2="&#xfa;" k="160" />
    <hkern u1="T" u2="&#xf9;" k="160" />
    <hkern u1="T" u2="&#xf8;" k="204" />
    <hkern u1="T" u2="&#xf6;" k="204" />
    <hkern u1="T" u2="&#xf5;" k="204" />
    <hkern u1="T" u2="&#xf4;" k="204" />
    <hkern u1="T" u2="&#xf3;" k="204" />
    <hkern u1="T" u2="&#xf2;" k="204" />
    <hkern u1="T" u2="&#xf1;" k="160" />
    <hkern u1="T" u2="&#xf0;" k="204" />
    <hkern u1="T" u2="&#xeb;" k="204" />
    <hkern u1="T" u2="&#xea;" k="204" />
    <hkern u1="T" u2="&#xe9;" k="204" />
    <hkern u1="T" u2="&#xe8;" k="204" />
    <hkern u1="T" u2="&#xe7;" k="204" />
    <hkern u1="T" u2="&#xe6;" k="244" />
    <hkern u1="T" u2="&#xe5;" k="244" />
    <hkern u1="T" u2="&#xe4;" k="244" />
    <hkern u1="T" u2="&#xe3;" k="244" />
    <hkern u1="T" u2="&#xe2;" k="244" />
    <hkern u1="T" u2="&#xe1;" k="244" />
    <hkern u1="T" u2="&#xe0;" k="244" />
    <hkern u1="T" u2="&#xd8;" k="78" />
    <hkern u1="T" u2="&#xd6;" k="78" />
    <hkern u1="T" u2="&#xd5;" k="78" />
    <hkern u1="T" u2="&#xd4;" k="78" />
    <hkern u1="T" u2="&#xd3;" k="78" />
    <hkern u1="T" u2="&#xd2;" k="78" />
    <hkern u1="T" u2="&#xc7;" k="78" />
    <hkern u1="T" u2="&#xc6;" k="147" />
    <hkern u1="T" u2="&#xc5;" k="147" />
    <hkern u1="T" u2="&#xc4;" k="147" />
    <hkern u1="T" u2="&#xc3;" k="147" />
    <hkern u1="T" u2="&#xc2;" k="147" />
    <hkern u1="T" u2="&#xc1;" k="147" />
    <hkern u1="T" u2="&#xc0;" k="147" />
    <hkern u1="T" u2="&#xbb;" k="180" />
    <hkern u1="T" u2="&#xb7;" k="180" />
    <hkern u1="T" u2="&#xb5;" k="160" />
    <hkern u1="T" u2="&#xae;" k="78" />
    <hkern u1="T" u2="&#xad;" k="180" />
    <hkern u1="T" u2="&#xab;" k="180" />
    <hkern u1="T" u2="&#xa9;" k="78" />
    <hkern u1="T" u2="z" k="120" />
    <hkern u1="T" u2="y" k="180" />
    <hkern u1="T" u2="x" k="137" />
    <hkern u1="T" u2="w" k="140" />
    <hkern u1="T" u2="v" k="180" />
    <hkern u1="T" u2="u" k="160" />
    <hkern u1="T" u2="s" k="140" />
    <hkern u1="T" u2="r" k="160" />
    <hkern u1="T" u2="q" k="204" />
    <hkern u1="T" u2="p" k="160" />
    <hkern u1="T" u2="o" k="204" />
    <hkern u1="T" u2="n" k="160" />
    <hkern u1="T" u2="m" k="160" />
    <hkern u1="T" u2="g" k="181" />
    <hkern u1="T" u2="e" k="204" />
    <hkern u1="T" u2="d" k="204" />
    <hkern u1="T" u2="c" k="204" />
    <hkern u1="T" u2="a" k="244" />
    <hkern u1="T" u2="Q" k="78" />
    <hkern u1="T" u2="O" k="78" />
    <hkern u1="T" u2="J" k="200" />
    <hkern u1="T" u2="G" k="78" />
    <hkern u1="T" u2="C" k="78" />
    <hkern u1="T" u2="A" k="147" />
    <hkern u1="T" u2="&#x40;" k="78" />
    <hkern u1="T" u2="&#x3b;" k="160" />
    <hkern u1="T" u2="&#x3a;" k="160" />
    <hkern u1="T" u2="&#x2f;" k="147" />
    <hkern u1="T" u2="&#x2e;" k="180" />
    <hkern u1="T" u2="&#x2d;" k="180" />
    <hkern u1="T" u2="&#x2c;" k="180" />
    <hkern u1="T" u2="&#x26;" k="147" />
    <hkern u1="U" u2="&#x2206;" k="52" />
    <hkern u1="U" u2="&#x201e;" k="50" />
    <hkern u1="U" u2="&#x201a;" k="50" />
    <hkern u1="U" u2="&#x104;" k="52" />
    <hkern u1="U" u2="&#xc6;" k="52" />
    <hkern u1="U" u2="&#xc5;" k="52" />
    <hkern u1="U" u2="&#xc4;" k="52" />
    <hkern u1="U" u2="&#xc3;" k="52" />
    <hkern u1="U" u2="&#xc2;" k="52" />
    <hkern u1="U" u2="&#xc1;" k="52" />
    <hkern u1="U" u2="&#xc0;" k="52" />
    <hkern u1="U" u2="A" k="52" />
    <hkern u1="U" u2="&#x2f;" k="52" />
    <hkern u1="U" u2="&#x2e;" k="50" />
    <hkern u1="U" u2="&#x2c;" k="50" />
    <hkern u1="U" u2="&#x26;" k="52" />
    <hkern u1="V" u2="&#x2206;" k="169" />
    <hkern u1="V" u2="&#x2122;" k="-44" />
    <hkern u1="V" u2="&#x203a;" k="116" />
    <hkern u1="V" u2="&#x2039;" k="116" />
    <hkern u1="V" u2="&#x2022;" k="116" />
    <hkern u1="V" u2="&#x201e;" k="196" />
    <hkern u1="V" u2="&#x201d;" k="-44" />
    <hkern u1="V" u2="&#x201c;" k="-44" />
    <hkern u1="V" u2="&#x201a;" k="196" />
    <hkern u1="V" u2="&#x2019;" k="-44" />
    <hkern u1="V" u2="&#x2018;" k="-44" />
    <hkern u1="V" u2="&#x2014;" k="116" />
    <hkern u1="V" u2="&#x2013;" k="116" />
    <hkern u1="V" u2="&#x17e;" k="91" />
    <hkern u1="V" u2="&#x17c;" k="91" />
    <hkern u1="V" u2="&#x17a;" k="91" />
    <hkern u1="V" u2="&#x161;" k="108" />
    <hkern u1="V" u2="&#x15b;" k="108" />
    <hkern u1="V" u2="&#x153;" k="123" />
    <hkern u1="V" u2="&#x152;" k="56" />
    <hkern u1="V" u2="&#x144;" k="99" />
    <hkern u1="V" u2="&#x131;" k="99" />
    <hkern u1="V" u2="&#x119;" k="123" />
    <hkern u1="V" u2="&#x107;" k="123" />
    <hkern u1="V" u2="&#x106;" k="56" />
    <hkern u1="V" u2="&#x105;" k="123" />
    <hkern u1="V" u2="&#x104;" k="169" />
    <hkern u1="V" u2="&#xff;" k="59" />
    <hkern u1="V" u2="&#xfd;" k="59" />
    <hkern u1="V" u2="&#xfc;" k="99" />
    <hkern u1="V" u2="&#xfb;" k="99" />
    <hkern u1="V" u2="&#xfa;" k="99" />
    <hkern u1="V" u2="&#xf9;" k="99" />
    <hkern u1="V" u2="&#xf8;" k="123" />
    <hkern u1="V" u2="&#xf6;" k="123" />
    <hkern u1="V" u2="&#xf5;" k="123" />
    <hkern u1="V" u2="&#xf4;" k="123" />
    <hkern u1="V" u2="&#xf3;" k="123" />
    <hkern u1="V" u2="&#xf2;" k="123" />
    <hkern u1="V" u2="&#xf1;" k="99" />
    <hkern u1="V" u2="&#xf0;" k="123" />
    <hkern u1="V" u2="&#xeb;" k="123" />
    <hkern u1="V" u2="&#xea;" k="123" />
    <hkern u1="V" u2="&#xe9;" k="123" />
    <hkern u1="V" u2="&#xe8;" k="123" />
    <hkern u1="V" u2="&#xe7;" k="123" />
    <hkern u1="V" u2="&#xe6;" k="123" />
    <hkern u1="V" u2="&#xe5;" k="123" />
    <hkern u1="V" u2="&#xe4;" k="123" />
    <hkern u1="V" u2="&#xe3;" k="123" />
    <hkern u1="V" u2="&#xe2;" k="123" />
    <hkern u1="V" u2="&#xe1;" k="123" />
    <hkern u1="V" u2="&#xe0;" k="123" />
    <hkern u1="V" u2="&#xd8;" k="56" />
    <hkern u1="V" u2="&#xd6;" k="56" />
    <hkern u1="V" u2="&#xd5;" k="56" />
    <hkern u1="V" u2="&#xd4;" k="56" />
    <hkern u1="V" u2="&#xd3;" k="56" />
    <hkern u1="V" u2="&#xd2;" k="56" />
    <hkern u1="V" u2="&#xc7;" k="56" />
    <hkern u1="V" u2="&#xc6;" k="169" />
    <hkern u1="V" u2="&#xc5;" k="169" />
    <hkern u1="V" u2="&#xc4;" k="169" />
    <hkern u1="V" u2="&#xc3;" k="169" />
    <hkern u1="V" u2="&#xc2;" k="169" />
    <hkern u1="V" u2="&#xc1;" k="169" />
    <hkern u1="V" u2="&#xc0;" k="169" />
    <hkern u1="V" u2="&#xbb;" k="116" />
    <hkern u1="V" u2="&#xba;" k="-44" />
    <hkern u1="V" u2="&#xb9;" k="-49" />
    <hkern u1="V" u2="&#xb7;" k="116" />
    <hkern u1="V" u2="&#xb5;" k="99" />
    <hkern u1="V" u2="&#xb3;" k="-49" />
    <hkern u1="V" u2="&#xb2;" k="-49" />
    <hkern u1="V" u2="&#xb0;" k="-44" />
    <hkern u1="V" u2="&#xae;" k="56" />
    <hkern u1="V" u2="&#xad;" k="116" />
    <hkern u1="V" u2="&#xab;" k="116" />
    <hkern u1="V" u2="&#xaa;" k="-44" />
    <hkern u1="V" u2="&#xa9;" k="56" />
    <hkern u1="V" u2="z" k="91" />
    <hkern u1="V" u2="y" k="59" />
    <hkern u1="V" u2="x" k="67" />
    <hkern u1="V" u2="v" k="59" />
    <hkern u1="V" u2="u" k="99" />
    <hkern u1="V" u2="t" k="46" />
    <hkern u1="V" u2="s" k="108" />
    <hkern u1="V" u2="r" k="99" />
    <hkern u1="V" u2="q" k="123" />
    <hkern u1="V" u2="p" k="99" />
    <hkern u1="V" u2="o" k="123" />
    <hkern u1="V" u2="n" k="99" />
    <hkern u1="V" u2="m" k="99" />
    <hkern u1="V" u2="g" k="138" />
    <hkern u1="V" u2="f" k="30" />
    <hkern u1="V" u2="e" k="123" />
    <hkern u1="V" u2="d" k="123" />
    <hkern u1="V" u2="c" k="123" />
    <hkern u1="V" u2="a" k="123" />
    <hkern u1="V" u2="Q" k="56" />
    <hkern u1="V" u2="O" k="56" />
    <hkern u1="V" u2="J" k="156" />
    <hkern u1="V" u2="G" k="56" />
    <hkern u1="V" u2="C" k="56" />
    <hkern u1="V" u2="A" k="169" />
    <hkern u1="V" u2="&#x40;" k="56" />
    <hkern u1="V" u2="&#x3f;" k="-39" />
    <hkern u1="V" u2="&#x3b;" k="99" />
    <hkern u1="V" u2="&#x3a;" k="99" />
    <hkern u1="V" u2="&#x2f;" k="169" />
    <hkern u1="V" u2="&#x2e;" k="196" />
    <hkern u1="V" u2="&#x2d;" k="116" />
    <hkern u1="V" u2="&#x2c;" k="196" />
    <hkern u1="V" u2="&#x2a;" k="-44" />
    <hkern u1="V" u2="&#x27;" k="-44" />
    <hkern u1="V" u2="&#x26;" k="169" />
    <hkern u1="V" u2="&#x22;" k="-44" />
    <hkern u1="W" u2="&#x2206;" k="118" />
    <hkern u1="W" u2="&#x2122;" k="-44" />
    <hkern u1="W" u2="&#x203a;" k="36" />
    <hkern u1="W" u2="&#x2039;" k="36" />
    <hkern u1="W" u2="&#x2022;" k="36" />
    <hkern u1="W" u2="&#x201e;" k="131" />
    <hkern u1="W" u2="&#x201d;" k="-44" />
    <hkern u1="W" u2="&#x201c;" k="-44" />
    <hkern u1="W" u2="&#x201a;" k="131" />
    <hkern u1="W" u2="&#x2019;" k="-44" />
    <hkern u1="W" u2="&#x2018;" k="-44" />
    <hkern u1="W" u2="&#x2014;" k="36" />
    <hkern u1="W" u2="&#x2013;" k="36" />
    <hkern u1="W" u2="&#x161;" k="53" />
    <hkern u1="W" u2="&#x15b;" k="53" />
    <hkern u1="W" u2="&#x153;" k="41" />
    <hkern u1="W" u2="&#x119;" k="41" />
    <hkern u1="W" u2="&#x107;" k="41" />
    <hkern u1="W" u2="&#x105;" k="99" />
    <hkern u1="W" u2="&#x104;" k="118" />
    <hkern u1="W" u2="&#xf8;" k="41" />
    <hkern u1="W" u2="&#xf6;" k="41" />
    <hkern u1="W" u2="&#xf5;" k="41" />
    <hkern u1="W" u2="&#xf4;" k="41" />
    <hkern u1="W" u2="&#xf3;" k="41" />
    <hkern u1="W" u2="&#xf2;" k="41" />
    <hkern u1="W" u2="&#xf0;" k="41" />
    <hkern u1="W" u2="&#xeb;" k="41" />
    <hkern u1="W" u2="&#xea;" k="41" />
    <hkern u1="W" u2="&#xe9;" k="41" />
    <hkern u1="W" u2="&#xe8;" k="41" />
    <hkern u1="W" u2="&#xe7;" k="41" />
    <hkern u1="W" u2="&#xe6;" k="99" />
    <hkern u1="W" u2="&#xe5;" k="99" />
    <hkern u1="W" u2="&#xe4;" k="99" />
    <hkern u1="W" u2="&#xe3;" k="99" />
    <hkern u1="W" u2="&#xe2;" k="99" />
    <hkern u1="W" u2="&#xe1;" k="99" />
    <hkern u1="W" u2="&#xe0;" k="99" />
    <hkern u1="W" u2="&#xc6;" k="118" />
    <hkern u1="W" u2="&#xc5;" k="118" />
    <hkern u1="W" u2="&#xc4;" k="118" />
    <hkern u1="W" u2="&#xc3;" k="118" />
    <hkern u1="W" u2="&#xc2;" k="118" />
    <hkern u1="W" u2="&#xc1;" k="118" />
    <hkern u1="W" u2="&#xc0;" k="118" />
    <hkern u1="W" u2="&#xbb;" k="36" />
    <hkern u1="W" u2="&#xba;" k="-44" />
    <hkern u1="W" u2="&#xb9;" k="-44" />
    <hkern u1="W" u2="&#xb7;" k="36" />
    <hkern u1="W" u2="&#xb3;" k="-44" />
    <hkern u1="W" u2="&#xb2;" k="-44" />
    <hkern u1="W" u2="&#xb0;" k="-44" />
    <hkern u1="W" u2="&#xad;" k="36" />
    <hkern u1="W" u2="&#xab;" k="36" />
    <hkern u1="W" u2="&#xaa;" k="-44" />
    <hkern u1="W" u2="s" k="53" />
    <hkern u1="W" u2="q" k="41" />
    <hkern u1="W" u2="o" k="41" />
    <hkern u1="W" u2="g" k="96" />
    <hkern u1="W" u2="e" k="41" />
    <hkern u1="W" u2="d" k="41" />
    <hkern u1="W" u2="c" k="41" />
    <hkern u1="W" u2="a" k="99" />
    <hkern u1="W" u2="J" k="111" />
    <hkern u1="W" u2="A" k="118" />
    <hkern u1="W" u2="&#x3f;" k="-32" />
    <hkern u1="W" u2="&#x2f;" k="118" />
    <hkern u1="W" u2="&#x2e;" k="131" />
    <hkern u1="W" u2="&#x2d;" k="36" />
    <hkern u1="W" u2="&#x2c;" k="131" />
    <hkern u1="W" u2="&#x2a;" k="-44" />
    <hkern u1="W" u2="&#x27;" k="-44" />
    <hkern u1="W" u2="&#x26;" k="118" />
    <hkern u1="W" u2="&#x22;" k="-44" />
    <hkern u1="X" u2="&#x203a;" k="66" />
    <hkern u1="X" u2="&#x2039;" k="66" />
    <hkern u1="X" u2="&#x2022;" k="66" />
    <hkern u1="X" u2="&#x2014;" k="66" />
    <hkern u1="X" u2="&#x2013;" k="66" />
    <hkern u1="X" u2="&#x153;" k="43" />
    <hkern u1="X" u2="&#x152;" k="30" />
    <hkern u1="X" u2="&#x119;" k="43" />
    <hkern u1="X" u2="&#x107;" k="43" />
    <hkern u1="X" u2="&#x106;" k="30" />
    <hkern u1="X" u2="&#xff;" k="73" />
    <hkern u1="X" u2="&#xfd;" k="73" />
    <hkern u1="X" u2="&#xf8;" k="43" />
    <hkern u1="X" u2="&#xf6;" k="43" />
    <hkern u1="X" u2="&#xf5;" k="43" />
    <hkern u1="X" u2="&#xf4;" k="43" />
    <hkern u1="X" u2="&#xf3;" k="43" />
    <hkern u1="X" u2="&#xf2;" k="43" />
    <hkern u1="X" u2="&#xf0;" k="43" />
    <hkern u1="X" u2="&#xeb;" k="43" />
    <hkern u1="X" u2="&#xea;" k="43" />
    <hkern u1="X" u2="&#xe9;" k="43" />
    <hkern u1="X" u2="&#xe8;" k="43" />
    <hkern u1="X" u2="&#xe7;" k="43" />
    <hkern u1="X" u2="&#xd8;" k="30" />
    <hkern u1="X" u2="&#xd6;" k="30" />
    <hkern u1="X" u2="&#xd5;" k="30" />
    <hkern u1="X" u2="&#xd4;" k="30" />
    <hkern u1="X" u2="&#xd3;" k="30" />
    <hkern u1="X" u2="&#xd2;" k="30" />
    <hkern u1="X" u2="&#xc7;" k="30" />
    <hkern u1="X" u2="&#xbb;" k="66" />
    <hkern u1="X" u2="&#xb7;" k="66" />
    <hkern u1="X" u2="&#xae;" k="30" />
    <hkern u1="X" u2="&#xad;" k="66" />
    <hkern u1="X" u2="&#xab;" k="66" />
    <hkern u1="X" u2="&#xa9;" k="30" />
    <hkern u1="X" u2="y" k="73" />
    <hkern u1="X" u2="w" k="52" />
    <hkern u1="X" u2="v" k="73" />
    <hkern u1="X" u2="t" k="91" />
    <hkern u1="X" u2="q" k="43" />
    <hkern u1="X" u2="o" k="43" />
    <hkern u1="X" u2="f" k="56" />
    <hkern u1="X" u2="e" k="43" />
    <hkern u1="X" u2="d" k="43" />
    <hkern u1="X" u2="c" k="43" />
    <hkern u1="X" u2="Q" k="30" />
    <hkern u1="X" u2="O" k="30" />
    <hkern u1="X" u2="G" k="30" />
    <hkern u1="X" u2="C" k="30" />
    <hkern u1="X" u2="&#x40;" k="30" />
    <hkern u1="X" u2="&#x2d;" k="66" />
    <hkern u1="Y" u2="&#x2206;" k="182" />
    <hkern u1="Y" u2="&#x2122;" k="-36" />
    <hkern u1="Y" u2="&#x203a;" k="160" />
    <hkern u1="Y" u2="&#x2039;" k="160" />
    <hkern u1="Y" u2="&#x2022;" k="160" />
    <hkern u1="Y" u2="&#x201e;" k="167" />
    <hkern u1="Y" u2="&#x201d;" k="-36" />
    <hkern u1="Y" u2="&#x201c;" k="-36" />
    <hkern u1="Y" u2="&#x201a;" k="167" />
    <hkern u1="Y" u2="&#x2019;" k="-36" />
    <hkern u1="Y" u2="&#x2018;" k="-36" />
    <hkern u1="Y" u2="&#x2014;" k="160" />
    <hkern u1="Y" u2="&#x2013;" k="160" />
    <hkern u1="Y" u2="&#x161;" k="139" />
    <hkern u1="Y" u2="&#x15b;" k="139" />
    <hkern u1="Y" u2="&#x153;" k="160" />
    <hkern u1="Y" u2="&#x152;" k="80" />
    <hkern u1="Y" u2="&#x144;" k="131" />
    <hkern u1="Y" u2="&#x131;" k="131" />
    <hkern u1="Y" u2="&#x119;" k="160" />
    <hkern u1="Y" u2="&#x107;" k="160" />
    <hkern u1="Y" u2="&#x106;" k="80" />
    <hkern u1="Y" u2="&#x105;" k="145" />
    <hkern u1="Y" u2="&#x104;" k="182" />
    <hkern u1="Y" u2="&#xff;" k="100" />
    <hkern u1="Y" u2="&#xfd;" k="100" />
    <hkern u1="Y" u2="&#xfc;" k="131" />
    <hkern u1="Y" u2="&#xfb;" k="131" />
    <hkern u1="Y" u2="&#xfa;" k="131" />
    <hkern u1="Y" u2="&#xf9;" k="131" />
    <hkern u1="Y" u2="&#xf8;" k="160" />
    <hkern u1="Y" u2="&#xf6;" k="160" />
    <hkern u1="Y" u2="&#xf5;" k="160" />
    <hkern u1="Y" u2="&#xf4;" k="160" />
    <hkern u1="Y" u2="&#xf3;" k="160" />
    <hkern u1="Y" u2="&#xf2;" k="160" />
    <hkern u1="Y" u2="&#xf1;" k="131" />
    <hkern u1="Y" u2="&#xf0;" k="160" />
    <hkern u1="Y" u2="&#xeb;" k="160" />
    <hkern u1="Y" u2="&#xea;" k="160" />
    <hkern u1="Y" u2="&#xe9;" k="160" />
    <hkern u1="Y" u2="&#xe8;" k="160" />
    <hkern u1="Y" u2="&#xe7;" k="160" />
    <hkern u1="Y" u2="&#xe6;" k="145" />
    <hkern u1="Y" u2="&#xe5;" k="145" />
    <hkern u1="Y" u2="&#xe4;" k="145" />
    <hkern u1="Y" u2="&#xe3;" k="145" />
    <hkern u1="Y" u2="&#xe2;" k="145" />
    <hkern u1="Y" u2="&#xe1;" k="145" />
    <hkern u1="Y" u2="&#xe0;" k="145" />
    <hkern u1="Y" u2="&#xd8;" k="80" />
    <hkern u1="Y" u2="&#xd6;" k="80" />
    <hkern u1="Y" u2="&#xd5;" k="80" />
    <hkern u1="Y" u2="&#xd4;" k="80" />
    <hkern u1="Y" u2="&#xd3;" k="80" />
    <hkern u1="Y" u2="&#xd2;" k="80" />
    <hkern u1="Y" u2="&#xc7;" k="80" />
    <hkern u1="Y" u2="&#xc6;" k="182" />
    <hkern u1="Y" u2="&#xc5;" k="182" />
    <hkern u1="Y" u2="&#xc4;" k="182" />
    <hkern u1="Y" u2="&#xc3;" k="182" />
    <hkern u1="Y" u2="&#xc2;" k="182" />
    <hkern u1="Y" u2="&#xc1;" k="182" />
    <hkern u1="Y" u2="&#xc0;" k="182" />
    <hkern u1="Y" u2="&#xbb;" k="160" />
    <hkern u1="Y" u2="&#xba;" k="-36" />
    <hkern u1="Y" u2="&#xb9;" k="-56" />
    <hkern u1="Y" u2="&#xb7;" k="160" />
    <hkern u1="Y" u2="&#xb5;" k="131" />
    <hkern u1="Y" u2="&#xb3;" k="-56" />
    <hkern u1="Y" u2="&#xb2;" k="-56" />
    <hkern u1="Y" u2="&#xb0;" k="-36" />
    <hkern u1="Y" u2="&#xae;" k="80" />
    <hkern u1="Y" u2="&#xad;" k="160" />
    <hkern u1="Y" u2="&#xab;" k="160" />
    <hkern u1="Y" u2="&#xaa;" k="-36" />
    <hkern u1="Y" u2="&#xa9;" k="80" />
    <hkern u1="Y" u2="y" k="100" />
    <hkern u1="Y" u2="x" k="136" />
    <hkern u1="Y" u2="w" k="96" />
    <hkern u1="Y" u2="v" k="100" />
    <hkern u1="Y" u2="u" k="131" />
    <hkern u1="Y" u2="s" k="139" />
    <hkern u1="Y" u2="r" k="131" />
    <hkern u1="Y" u2="q" k="160" />
    <hkern u1="Y" u2="p" k="131" />
    <hkern u1="Y" u2="o" k="160" />
    <hkern u1="Y" u2="n" k="131" />
    <hkern u1="Y" u2="m" k="131" />
    <hkern u1="Y" u2="g" k="176" />
    <hkern u1="Y" u2="e" k="160" />
    <hkern u1="Y" u2="d" k="160" />
    <hkern u1="Y" u2="c" k="160" />
    <hkern u1="Y" u2="a" k="145" />
    <hkern u1="Y" u2="Q" k="80" />
    <hkern u1="Y" u2="O" k="80" />
    <hkern u1="Y" u2="J" k="200" />
    <hkern u1="Y" u2="G" k="80" />
    <hkern u1="Y" u2="C" k="80" />
    <hkern u1="Y" u2="A" k="182" />
    <hkern u1="Y" u2="&#x40;" k="80" />
    <hkern u1="Y" u2="&#x3f;" k="-32" />
    <hkern u1="Y" u2="&#x3b;" k="131" />
    <hkern u1="Y" u2="&#x3a;" k="131" />
    <hkern u1="Y" u2="&#x2f;" k="182" />
    <hkern u1="Y" u2="&#x2e;" k="167" />
    <hkern u1="Y" u2="&#x2d;" k="160" />
    <hkern u1="Y" u2="&#x2c;" k="167" />
    <hkern u1="Y" u2="&#x2a;" k="-36" />
    <hkern u1="Y" u2="&#x27;" k="-36" />
    <hkern u1="Y" u2="&#x26;" k="182" />
    <hkern u1="Y" u2="&#x22;" k="-36" />
    <hkern u1="Z" u2="&#x203a;" k="64" />
    <hkern u1="Z" u2="&#x2039;" k="64" />
    <hkern u1="Z" u2="&#x2022;" k="64" />
    <hkern u1="Z" u2="&#x2014;" k="64" />
    <hkern u1="Z" u2="&#x2013;" k="64" />
    <hkern u1="Z" u2="&#x161;" k="19" />
    <hkern u1="Z" u2="&#x15b;" k="19" />
    <hkern u1="Z" u2="&#x153;" k="29" />
    <hkern u1="Z" u2="&#x152;" k="49" />
    <hkern u1="Z" u2="&#x119;" k="29" />
    <hkern u1="Z" u2="&#x107;" k="29" />
    <hkern u1="Z" u2="&#x106;" k="49" />
    <hkern u1="Z" u2="&#xff;" k="34" />
    <hkern u1="Z" u2="&#xfd;" k="34" />
    <hkern u1="Z" u2="&#xf8;" k="29" />
    <hkern u1="Z" u2="&#xf6;" k="29" />
    <hkern u1="Z" u2="&#xf5;" k="29" />
    <hkern u1="Z" u2="&#xf4;" k="29" />
    <hkern u1="Z" u2="&#xf3;" k="29" />
    <hkern u1="Z" u2="&#xf2;" k="29" />
    <hkern u1="Z" u2="&#xf0;" k="29" />
    <hkern u1="Z" u2="&#xeb;" k="29" />
    <hkern u1="Z" u2="&#xea;" k="29" />
    <hkern u1="Z" u2="&#xe9;" k="29" />
    <hkern u1="Z" u2="&#xe8;" k="29" />
    <hkern u1="Z" u2="&#xe7;" k="29" />
    <hkern u1="Z" u2="&#xd8;" k="49" />
    <hkern u1="Z" u2="&#xd6;" k="49" />
    <hkern u1="Z" u2="&#xd5;" k="49" />
    <hkern u1="Z" u2="&#xd4;" k="49" />
    <hkern u1="Z" u2="&#xd3;" k="49" />
    <hkern u1="Z" u2="&#xd2;" k="49" />
    <hkern u1="Z" u2="&#xc7;" k="49" />
    <hkern u1="Z" u2="&#xbb;" k="64" />
    <hkern u1="Z" u2="&#xb7;" k="64" />
    <hkern u1="Z" u2="&#xae;" k="49" />
    <hkern u1="Z" u2="&#xad;" k="64" />
    <hkern u1="Z" u2="&#xab;" k="64" />
    <hkern u1="Z" u2="&#xa9;" k="49" />
    <hkern u1="Z" u2="y" k="34" />
    <hkern u1="Z" u2="v" k="34" />
    <hkern u1="Z" u2="s" k="19" />
    <hkern u1="Z" u2="q" k="29" />
    <hkern u1="Z" u2="o" k="29" />
    <hkern u1="Z" u2="e" k="29" />
    <hkern u1="Z" u2="d" k="29" />
    <hkern u1="Z" u2="c" k="29" />
    <hkern u1="Z" u2="Q" k="49" />
    <hkern u1="Z" u2="O" k="49" />
    <hkern u1="Z" u2="G" k="49" />
    <hkern u1="Z" u2="C" k="49" />
    <hkern u1="Z" u2="&#x40;" k="49" />
    <hkern u1="Z" u2="&#x3f;" k="-32" />
    <hkern u1="Z" u2="&#x2d;" k="64" />
    <hkern u1="[" u2="&#x153;" k="36" />
    <hkern u1="[" u2="&#x152;" k="40" />
    <hkern u1="[" u2="&#x119;" k="36" />
    <hkern u1="[" u2="&#x107;" k="36" />
    <hkern u1="[" u2="&#x106;" k="40" />
    <hkern u1="[" u2="&#xf8;" k="36" />
    <hkern u1="[" u2="&#xf6;" k="36" />
    <hkern u1="[" u2="&#xf5;" k="36" />
    <hkern u1="[" u2="&#xf4;" k="36" />
    <hkern u1="[" u2="&#xf3;" k="36" />
    <hkern u1="[" u2="&#xf2;" k="36" />
    <hkern u1="[" u2="&#xf0;" k="36" />
    <hkern u1="[" u2="&#xeb;" k="36" />
    <hkern u1="[" u2="&#xea;" k="36" />
    <hkern u1="[" u2="&#xe9;" k="36" />
    <hkern u1="[" u2="&#xe8;" k="36" />
    <hkern u1="[" u2="&#xe7;" k="36" />
    <hkern u1="[" u2="&#xd8;" k="40" />
    <hkern u1="[" u2="&#xd6;" k="40" />
    <hkern u1="[" u2="&#xd5;" k="40" />
    <hkern u1="[" u2="&#xd4;" k="40" />
    <hkern u1="[" u2="&#xd3;" k="40" />
    <hkern u1="[" u2="&#xd2;" k="40" />
    <hkern u1="[" u2="&#xc7;" k="40" />
    <hkern u1="[" u2="&#xae;" k="40" />
    <hkern u1="[" u2="&#xa9;" k="40" />
    <hkern u1="[" u2="q" k="36" />
    <hkern u1="[" u2="o" k="36" />
    <hkern u1="[" u2="e" k="36" />
    <hkern u1="[" u2="d" k="36" />
    <hkern u1="[" u2="c" k="36" />
    <hkern u1="[" u2="Q" k="40" />
    <hkern u1="[" u2="O" k="40" />
    <hkern u1="[" u2="G" k="40" />
    <hkern u1="[" u2="C" k="40" />
    <hkern u1="[" u2="&#x40;" k="40" />
    <hkern u1="\" u2="&#x2122;" k="191" />
    <hkern u1="\" u2="&#x203a;" k="67" />
    <hkern u1="\" u2="&#x2039;" k="67" />
    <hkern u1="\" u2="&#x2022;" k="67" />
    <hkern u1="\" u2="&#x201d;" k="191" />
    <hkern u1="\" u2="&#x201c;" k="191" />
    <hkern u1="\" u2="&#x2019;" k="191" />
    <hkern u1="\" u2="&#x2018;" k="191" />
    <hkern u1="\" u2="&#x2014;" k="67" />
    <hkern u1="\" u2="&#x2013;" k="67" />
    <hkern u1="\" u2="&#x178;" k="182" />
    <hkern u1="\" u2="&#x152;" k="51" />
    <hkern u1="\" u2="&#x106;" k="51" />
    <hkern u1="\" u2="&#xff;" k="91" />
    <hkern u1="\" u2="&#xfd;" k="91" />
    <hkern u1="\" u2="&#xdd;" k="182" />
    <hkern u1="\" u2="&#xdc;" k="52" />
    <hkern u1="\" u2="&#xdb;" k="52" />
    <hkern u1="\" u2="&#xda;" k="52" />
    <hkern u1="\" u2="&#xd9;" k="52" />
    <hkern u1="\" u2="&#xd8;" k="51" />
    <hkern u1="\" u2="&#xd6;" k="51" />
    <hkern u1="\" u2="&#xd5;" k="51" />
    <hkern u1="\" u2="&#xd4;" k="51" />
    <hkern u1="\" u2="&#xd3;" k="51" />
    <hkern u1="\" u2="&#xd2;" k="51" />
    <hkern u1="\" u2="&#xc7;" k="51" />
    <hkern u1="\" u2="&#xbb;" k="67" />
    <hkern u1="\" u2="&#xba;" k="191" />
    <hkern u1="\" u2="&#xb9;" k="202" />
    <hkern u1="\" u2="&#xb7;" k="67" />
    <hkern u1="\" u2="&#xb3;" k="202" />
    <hkern u1="\" u2="&#xb2;" k="202" />
    <hkern u1="\" u2="&#xb0;" k="191" />
    <hkern u1="\" u2="&#xae;" k="51" />
    <hkern u1="\" u2="&#xad;" k="67" />
    <hkern u1="\" u2="&#xab;" k="67" />
    <hkern u1="\" u2="&#xaa;" k="191" />
    <hkern u1="\" u2="&#xa9;" k="51" />
    <hkern u1="\" u2="y" k="91" />
    <hkern u1="\" u2="v" k="91" />
    <hkern u1="\" u2="\" k="169" />
    <hkern u1="\" u2="Y" k="182" />
    <hkern u1="\" u2="W" k="102" />
    <hkern u1="\" u2="V" k="169" />
    <hkern u1="\" u2="U" k="52" />
    <hkern u1="\" u2="T" k="147" />
    <hkern u1="\" u2="Q" k="51" />
    <hkern u1="\" u2="O" k="51" />
    <hkern u1="\" u2="J" k="-56" />
    <hkern u1="\" u2="G" k="51" />
    <hkern u1="\" u2="C" k="51" />
    <hkern u1="\" u2="&#x40;" k="51" />
    <hkern u1="\" u2="&#x3f;" k="63" />
    <hkern u1="\" u2="&#x2d;" k="67" />
    <hkern u1="\" u2="&#x2a;" k="191" />
    <hkern u1="\" u2="&#x27;" k="191" />
    <hkern u1="\" u2="&#x22;" k="191" />
    <hkern u1="a" u2="&#x2122;" k="76" />
    <hkern u1="a" u2="&#x201d;" k="76" />
    <hkern u1="a" u2="&#x201c;" k="76" />
    <hkern u1="a" u2="&#x2019;" k="76" />
    <hkern u1="a" u2="&#x2018;" k="76" />
    <hkern u1="a" u2="&#xff;" k="36" />
    <hkern u1="a" u2="&#xfd;" k="36" />
    <hkern u1="a" u2="&#xba;" k="76" />
    <hkern u1="a" u2="&#xb9;" k="76" />
    <hkern u1="a" u2="&#xb3;" k="76" />
    <hkern u1="a" u2="&#xb2;" k="76" />
    <hkern u1="a" u2="&#xb0;" k="76" />
    <hkern u1="a" u2="&#xaa;" k="76" />
    <hkern u1="a" u2="y" k="36" />
    <hkern u1="a" u2="w" k="18" />
    <hkern u1="a" u2="v" k="36" />
    <hkern u1="a" u2="&#x2a;" k="76" />
    <hkern u1="a" u2="&#x27;" k="76" />
    <hkern u1="a" u2="&#x22;" k="76" />
    <hkern u1="b" u2="&#x2122;" k="96" />
    <hkern u1="b" u2="&#x201d;" k="96" />
    <hkern u1="b" u2="&#x201c;" k="96" />
    <hkern u1="b" u2="&#x2019;" k="96" />
    <hkern u1="b" u2="&#x2018;" k="96" />
    <hkern u1="b" u2="&#xff;" k="33" />
    <hkern u1="b" u2="&#xfd;" k="33" />
    <hkern u1="b" u2="&#xba;" k="96" />
    <hkern u1="b" u2="&#xb0;" k="96" />
    <hkern u1="b" u2="&#xaa;" k="96" />
    <hkern u1="b" u2="&#x7d;" k="36" />
    <hkern u1="b" u2="y" k="33" />
    <hkern u1="b" u2="x" k="60" />
    <hkern u1="b" u2="v" k="33" />
    <hkern u1="b" u2="]" k="36" />
    <hkern u1="b" u2="\" k="123" />
    <hkern u1="b" u2="W" k="41" />
    <hkern u1="b" u2="V" k="123" />
    <hkern u1="b" u2="&#x2a;" k="96" />
    <hkern u1="b" u2="&#x29;" k="36" />
    <hkern u1="b" u2="&#x27;" k="96" />
    <hkern u1="b" u2="&#x22;" k="96" />
    <hkern u1="e" u2="&#x2122;" k="96" />
    <hkern u1="e" u2="&#x201d;" k="96" />
    <hkern u1="e" u2="&#x201c;" k="96" />
    <hkern u1="e" u2="&#x2019;" k="96" />
    <hkern u1="e" u2="&#x2018;" k="96" />
    <hkern u1="e" u2="&#xff;" k="33" />
    <hkern u1="e" u2="&#xfd;" k="33" />
    <hkern u1="e" u2="&#xba;" k="96" />
    <hkern u1="e" u2="&#xb0;" k="96" />
    <hkern u1="e" u2="&#xaa;" k="96" />
    <hkern u1="e" u2="&#x7d;" k="36" />
    <hkern u1="e" u2="y" k="33" />
    <hkern u1="e" u2="x" k="60" />
    <hkern u1="e" u2="v" k="33" />
    <hkern u1="e" u2="]" k="36" />
    <hkern u1="e" u2="\" k="123" />
    <hkern u1="e" u2="W" k="41" />
    <hkern u1="e" u2="V" k="123" />
    <hkern u1="e" u2="&#x2a;" k="96" />
    <hkern u1="e" u2="&#x29;" k="36" />
    <hkern u1="e" u2="&#x27;" k="96" />
    <hkern u1="e" u2="&#x22;" k="96" />
    <hkern u1="f" u2="&#x2122;" k="-64" />
    <hkern u1="f" u2="&#x201e;" k="124" />
    <hkern u1="f" u2="&#x201d;" k="-64" />
    <hkern u1="f" u2="&#x201c;" k="-64" />
    <hkern u1="f" u2="&#x201a;" k="124" />
    <hkern u1="f" u2="&#x2019;" k="-64" />
    <hkern u1="f" u2="&#x2018;" k="-64" />
    <hkern u1="f" u2="&#xba;" k="-64" />
    <hkern u1="f" u2="&#xb9;" k="-100" />
    <hkern u1="f" u2="&#xb3;" k="-100" />
    <hkern u1="f" u2="&#xb2;" k="-100" />
    <hkern u1="f" u2="&#xb0;" k="-64" />
    <hkern u1="f" u2="&#xaa;" k="-64" />
    <hkern u1="f" u2="&#x2e;" k="124" />
    <hkern u1="f" u2="&#x2c;" k="124" />
    <hkern u1="f" u2="&#x2a;" k="-64" />
    <hkern u1="f" u2="&#x27;" k="-64" />
    <hkern u1="f" u2="&#x22;" k="-64" />
    <hkern u1="h" u2="&#x2122;" k="76" />
    <hkern u1="h" u2="&#x201d;" k="76" />
    <hkern u1="h" u2="&#x201c;" k="76" />
    <hkern u1="h" u2="&#x2019;" k="76" />
    <hkern u1="h" u2="&#x2018;" k="76" />
    <hkern u1="h" u2="&#xff;" k="36" />
    <hkern u1="h" u2="&#xfd;" k="36" />
    <hkern u1="h" u2="&#xba;" k="76" />
    <hkern u1="h" u2="&#xb9;" k="76" />
    <hkern u1="h" u2="&#xb3;" k="76" />
    <hkern u1="h" u2="&#xb2;" k="76" />
    <hkern u1="h" u2="&#xb0;" k="76" />
    <hkern u1="h" u2="&#xaa;" k="76" />
    <hkern u1="h" u2="y" k="36" />
    <hkern u1="h" u2="w" k="18" />
    <hkern u1="h" u2="v" k="36" />
    <hkern u1="h" u2="&#x2a;" k="76" />
    <hkern u1="h" u2="&#x27;" k="76" />
    <hkern u1="h" u2="&#x22;" k="76" />
    <hkern u1="k" u2="&#x153;" k="60" />
    <hkern u1="k" u2="&#x119;" k="60" />
    <hkern u1="k" u2="&#x107;" k="60" />
    <hkern u1="k" u2="&#xf8;" k="60" />
    <hkern u1="k" u2="&#xf6;" k="60" />
    <hkern u1="k" u2="&#xf5;" k="60" />
    <hkern u1="k" u2="&#xf4;" k="60" />
    <hkern u1="k" u2="&#xf3;" k="60" />
    <hkern u1="k" u2="&#xf2;" k="60" />
    <hkern u1="k" u2="&#xf0;" k="60" />
    <hkern u1="k" u2="&#xeb;" k="60" />
    <hkern u1="k" u2="&#xea;" k="60" />
    <hkern u1="k" u2="&#xe9;" k="60" />
    <hkern u1="k" u2="&#xe8;" k="60" />
    <hkern u1="k" u2="&#xe7;" k="60" />
    <hkern u1="k" u2="q" k="60" />
    <hkern u1="k" u2="o" k="60" />
    <hkern u1="k" u2="e" k="60" />
    <hkern u1="k" u2="d" k="60" />
    <hkern u1="k" u2="c" k="60" />
    <hkern u1="m" u2="&#x2122;" k="76" />
    <hkern u1="m" u2="&#x201d;" k="76" />
    <hkern u1="m" u2="&#x201c;" k="76" />
    <hkern u1="m" u2="&#x2019;" k="76" />
    <hkern u1="m" u2="&#x2018;" k="76" />
    <hkern u1="m" u2="&#xff;" k="36" />
    <hkern u1="m" u2="&#xfd;" k="36" />
    <hkern u1="m" u2="&#xba;" k="76" />
    <hkern u1="m" u2="&#xb9;" k="76" />
    <hkern u1="m" u2="&#xb3;" k="76" />
    <hkern u1="m" u2="&#xb2;" k="76" />
    <hkern u1="m" u2="&#xb0;" k="76" />
    <hkern u1="m" u2="&#xaa;" k="76" />
    <hkern u1="m" u2="y" k="36" />
    <hkern u1="m" u2="w" k="18" />
    <hkern u1="m" u2="v" k="36" />
    <hkern u1="m" u2="&#x2a;" k="76" />
    <hkern u1="m" u2="&#x27;" k="76" />
    <hkern u1="m" u2="&#x22;" k="76" />
    <hkern u1="n" u2="&#x2122;" k="76" />
    <hkern u1="n" u2="&#x201d;" k="76" />
    <hkern u1="n" u2="&#x201c;" k="76" />
    <hkern u1="n" u2="&#x2019;" k="76" />
    <hkern u1="n" u2="&#x2018;" k="76" />
    <hkern u1="n" u2="&#xff;" k="36" />
    <hkern u1="n" u2="&#xfd;" k="36" />
    <hkern u1="n" u2="&#xba;" k="76" />
    <hkern u1="n" u2="&#xb9;" k="76" />
    <hkern u1="n" u2="&#xb3;" k="76" />
    <hkern u1="n" u2="&#xb2;" k="76" />
    <hkern u1="n" u2="&#xb0;" k="76" />
    <hkern u1="n" u2="&#xaa;" k="76" />
    <hkern u1="n" u2="y" k="36" />
    <hkern u1="n" u2="w" k="18" />
    <hkern u1="n" u2="v" k="36" />
    <hkern u1="n" u2="&#x2a;" k="76" />
    <hkern u1="n" u2="&#x27;" k="76" />
    <hkern u1="n" u2="&#x22;" k="76" />
    <hkern u1="o" u2="&#x2122;" k="96" />
    <hkern u1="o" u2="&#x201d;" k="96" />
    <hkern u1="o" u2="&#x201c;" k="96" />
    <hkern u1="o" u2="&#x2019;" k="96" />
    <hkern u1="o" u2="&#x2018;" k="96" />
    <hkern u1="o" u2="&#xff;" k="33" />
    <hkern u1="o" u2="&#xfd;" k="33" />
    <hkern u1="o" u2="&#xba;" k="96" />
    <hkern u1="o" u2="&#xb0;" k="96" />
    <hkern u1="o" u2="&#xaa;" k="96" />
    <hkern u1="o" u2="&#x7d;" k="36" />
    <hkern u1="o" u2="y" k="33" />
    <hkern u1="o" u2="x" k="60" />
    <hkern u1="o" u2="v" k="33" />
    <hkern u1="o" u2="]" k="36" />
    <hkern u1="o" u2="\" k="123" />
    <hkern u1="o" u2="W" k="41" />
    <hkern u1="o" u2="V" k="123" />
    <hkern u1="o" u2="&#x2a;" k="96" />
    <hkern u1="o" u2="&#x29;" k="36" />
    <hkern u1="o" u2="&#x27;" k="96" />
    <hkern u1="o" u2="&#x22;" k="96" />
    <hkern u1="p" u2="&#x2122;" k="96" />
    <hkern u1="p" u2="&#x201d;" k="96" />
    <hkern u1="p" u2="&#x201c;" k="96" />
    <hkern u1="p" u2="&#x2019;" k="96" />
    <hkern u1="p" u2="&#x2018;" k="96" />
    <hkern u1="p" u2="&#xff;" k="33" />
    <hkern u1="p" u2="&#xfd;" k="33" />
    <hkern u1="p" u2="&#xba;" k="96" />
    <hkern u1="p" u2="&#xb0;" k="96" />
    <hkern u1="p" u2="&#xaa;" k="96" />
    <hkern u1="p" u2="&#x7d;" k="36" />
    <hkern u1="p" u2="y" k="33" />
    <hkern u1="p" u2="x" k="60" />
    <hkern u1="p" u2="v" k="33" />
    <hkern u1="p" u2="]" k="36" />
    <hkern u1="p" u2="\" k="123" />
    <hkern u1="p" u2="W" k="41" />
    <hkern u1="p" u2="V" k="123" />
    <hkern u1="p" u2="&#x2a;" k="96" />
    <hkern u1="p" u2="&#x29;" k="36" />
    <hkern u1="p" u2="&#x27;" k="96" />
    <hkern u1="p" u2="&#x22;" k="96" />
    <hkern u1="r" u2="&#x201e;" k="136" />
    <hkern u1="r" u2="&#x201a;" k="136" />
    <hkern u1="r" u2="&#x105;" k="29" />
    <hkern u1="r" u2="&#xe6;" k="29" />
    <hkern u1="r" u2="&#xe5;" k="29" />
    <hkern u1="r" u2="&#xe4;" k="29" />
    <hkern u1="r" u2="&#xe3;" k="29" />
    <hkern u1="r" u2="&#xe2;" k="29" />
    <hkern u1="r" u2="&#xe1;" k="29" />
    <hkern u1="r" u2="&#xe0;" k="29" />
    <hkern u1="r" u2="a" k="29" />
    <hkern u1="r" u2="&#x2e;" k="136" />
    <hkern u1="r" u2="&#x2c;" k="136" />
    <hkern u1="v" u2="&#x2206;" k="91" />
    <hkern u1="v" u2="&#x201e;" k="136" />
    <hkern u1="v" u2="&#x201a;" k="136" />
    <hkern u1="v" u2="&#x153;" k="33" />
    <hkern u1="v" u2="&#x119;" k="33" />
    <hkern u1="v" u2="&#x107;" k="33" />
    <hkern u1="v" u2="&#x104;" k="91" />
    <hkern u1="v" u2="&#xf8;" k="33" />
    <hkern u1="v" u2="&#xf6;" k="33" />
    <hkern u1="v" u2="&#xf5;" k="33" />
    <hkern u1="v" u2="&#xf4;" k="33" />
    <hkern u1="v" u2="&#xf3;" k="33" />
    <hkern u1="v" u2="&#xf2;" k="33" />
    <hkern u1="v" u2="&#xf0;" k="33" />
    <hkern u1="v" u2="&#xeb;" k="33" />
    <hkern u1="v" u2="&#xea;" k="33" />
    <hkern u1="v" u2="&#xe9;" k="33" />
    <hkern u1="v" u2="&#xe8;" k="33" />
    <hkern u1="v" u2="&#xe7;" k="33" />
    <hkern u1="v" u2="&#xc6;" k="91" />
    <hkern u1="v" u2="&#xc5;" k="91" />
    <hkern u1="v" u2="&#xc4;" k="91" />
    <hkern u1="v" u2="&#xc3;" k="91" />
    <hkern u1="v" u2="&#xc2;" k="91" />
    <hkern u1="v" u2="&#xc1;" k="91" />
    <hkern u1="v" u2="&#xc0;" k="91" />
    <hkern u1="v" u2="q" k="33" />
    <hkern u1="v" u2="o" k="33" />
    <hkern u1="v" u2="e" k="33" />
    <hkern u1="v" u2="d" k="33" />
    <hkern u1="v" u2="c" k="33" />
    <hkern u1="v" u2="A" k="91" />
    <hkern u1="v" u2="&#x2f;" k="91" />
    <hkern u1="v" u2="&#x2e;" k="136" />
    <hkern u1="v" u2="&#x2c;" k="136" />
    <hkern u1="v" u2="&#x26;" k="91" />
    <hkern u1="w" u2="&#x201e;" k="71" />
    <hkern u1="w" u2="&#x201a;" k="71" />
    <hkern u1="w" u2="&#x2e;" k="71" />
    <hkern u1="w" u2="&#x2c;" k="71" />
    <hkern u1="x" u2="&#x153;" k="60" />
    <hkern u1="x" u2="&#x119;" k="60" />
    <hkern u1="x" u2="&#x107;" k="60" />
    <hkern u1="x" u2="&#xf8;" k="60" />
    <hkern u1="x" u2="&#xf6;" k="60" />
    <hkern u1="x" u2="&#xf5;" k="60" />
    <hkern u1="x" u2="&#xf4;" k="60" />
    <hkern u1="x" u2="&#xf3;" k="60" />
    <hkern u1="x" u2="&#xf2;" k="60" />
    <hkern u1="x" u2="&#xf0;" k="60" />
    <hkern u1="x" u2="&#xeb;" k="60" />
    <hkern u1="x" u2="&#xea;" k="60" />
    <hkern u1="x" u2="&#xe9;" k="60" />
    <hkern u1="x" u2="&#xe8;" k="60" />
    <hkern u1="x" u2="&#xe7;" k="60" />
    <hkern u1="x" u2="q" k="60" />
    <hkern u1="x" u2="o" k="60" />
    <hkern u1="x" u2="e" k="60" />
    <hkern u1="x" u2="d" k="60" />
    <hkern u1="x" u2="c" k="60" />
    <hkern u1="y" u2="&#x2206;" k="91" />
    <hkern u1="y" u2="&#x201e;" k="136" />
    <hkern u1="y" u2="&#x201a;" k="136" />
    <hkern u1="y" u2="&#x153;" k="33" />
    <hkern u1="y" u2="&#x119;" k="33" />
    <hkern u1="y" u2="&#x107;" k="33" />
    <hkern u1="y" u2="&#x104;" k="91" />
    <hkern u1="y" u2="&#xf8;" k="33" />
    <hkern u1="y" u2="&#xf6;" k="33" />
    <hkern u1="y" u2="&#xf5;" k="33" />
    <hkern u1="y" u2="&#xf4;" k="33" />
    <hkern u1="y" u2="&#xf3;" k="33" />
    <hkern u1="y" u2="&#xf2;" k="33" />
    <hkern u1="y" u2="&#xf0;" k="33" />
    <hkern u1="y" u2="&#xeb;" k="33" />
    <hkern u1="y" u2="&#xea;" k="33" />
    <hkern u1="y" u2="&#xe9;" k="33" />
    <hkern u1="y" u2="&#xe8;" k="33" />
    <hkern u1="y" u2="&#xe7;" k="33" />
    <hkern u1="y" u2="&#xc6;" k="91" />
    <hkern u1="y" u2="&#xc5;" k="91" />
    <hkern u1="y" u2="&#xc4;" k="91" />
    <hkern u1="y" u2="&#xc3;" k="91" />
    <hkern u1="y" u2="&#xc2;" k="91" />
    <hkern u1="y" u2="&#xc1;" k="91" />
    <hkern u1="y" u2="&#xc0;" k="91" />
    <hkern u1="y" u2="q" k="33" />
    <hkern u1="y" u2="o" k="33" />
    <hkern u1="y" u2="e" k="33" />
    <hkern u1="y" u2="d" k="33" />
    <hkern u1="y" u2="c" k="33" />
    <hkern u1="y" u2="A" k="91" />
    <hkern u1="y" u2="&#x2f;" k="91" />
    <hkern u1="y" u2="&#x2e;" k="136" />
    <hkern u1="y" u2="&#x2c;" k="136" />
    <hkern u1="y" u2="&#x26;" k="91" />
    <hkern u1="&#x7b;" u2="&#x153;" k="36" />
    <hkern u1="&#x7b;" u2="&#x152;" k="40" />
    <hkern u1="&#x7b;" u2="&#x119;" k="36" />
    <hkern u1="&#x7b;" u2="&#x107;" k="36" />
    <hkern u1="&#x7b;" u2="&#x106;" k="40" />
    <hkern u1="&#x7b;" u2="&#xf8;" k="36" />
    <hkern u1="&#x7b;" u2="&#xf6;" k="36" />
    <hkern u1="&#x7b;" u2="&#xf5;" k="36" />
    <hkern u1="&#x7b;" u2="&#xf4;" k="36" />
    <hkern u1="&#x7b;" u2="&#xf3;" k="36" />
    <hkern u1="&#x7b;" u2="&#xf2;" k="36" />
    <hkern u1="&#x7b;" u2="&#xf0;" k="36" />
    <hkern u1="&#x7b;" u2="&#xeb;" k="36" />
    <hkern u1="&#x7b;" u2="&#xea;" k="36" />
    <hkern u1="&#x7b;" u2="&#xe9;" k="36" />
    <hkern u1="&#x7b;" u2="&#xe8;" k="36" />
    <hkern u1="&#x7b;" u2="&#xe7;" k="36" />
    <hkern u1="&#x7b;" u2="&#xd8;" k="40" />
    <hkern u1="&#x7b;" u2="&#xd6;" k="40" />
    <hkern u1="&#x7b;" u2="&#xd5;" k="40" />
    <hkern u1="&#x7b;" u2="&#xd4;" k="40" />
    <hkern u1="&#x7b;" u2="&#xd3;" k="40" />
    <hkern u1="&#x7b;" u2="&#xd2;" k="40" />
    <hkern u1="&#x7b;" u2="&#xc7;" k="40" />
    <hkern u1="&#x7b;" u2="&#xae;" k="40" />
    <hkern u1="&#x7b;" u2="&#xa9;" k="40" />
    <hkern u1="&#x7b;" u2="q" k="36" />
    <hkern u1="&#x7b;" u2="o" k="36" />
    <hkern u1="&#x7b;" u2="e" k="36" />
    <hkern u1="&#x7b;" u2="d" k="36" />
    <hkern u1="&#x7b;" u2="c" k="36" />
    <hkern u1="&#x7b;" u2="Q" k="40" />
    <hkern u1="&#x7b;" u2="O" k="40" />
    <hkern u1="&#x7b;" u2="G" k="40" />
    <hkern u1="&#x7b;" u2="C" k="40" />
    <hkern u1="&#x7b;" u2="&#x40;" k="40" />
    <hkern u1="&#xa9;" u2="&#x2206;" k="51" />
    <hkern u1="&#xa9;" u2="&#x2122;" k="42" />
    <hkern u1="&#xa9;" u2="&#x201e;" k="52" />
    <hkern u1="&#xa9;" u2="&#x201d;" k="42" />
    <hkern u1="&#xa9;" u2="&#x201c;" k="42" />
    <hkern u1="&#xa9;" u2="&#x201a;" k="52" />
    <hkern u1="&#xa9;" u2="&#x2019;" k="42" />
    <hkern u1="&#xa9;" u2="&#x2018;" k="42" />
    <hkern u1="&#xa9;" u2="&#x17d;" k="64" />
    <hkern u1="&#xa9;" u2="&#x17b;" k="64" />
    <hkern u1="&#xa9;" u2="&#x179;" k="64" />
    <hkern u1="&#xa9;" u2="&#x178;" k="80" />
    <hkern u1="&#xa9;" u2="&#x104;" k="51" />
    <hkern u1="&#xa9;" u2="&#xdd;" k="80" />
    <hkern u1="&#xa9;" u2="&#xc6;" k="51" />
    <hkern u1="&#xa9;" u2="&#xc5;" k="51" />
    <hkern u1="&#xa9;" u2="&#xc4;" k="51" />
    <hkern u1="&#xa9;" u2="&#xc3;" k="51" />
    <hkern u1="&#xa9;" u2="&#xc2;" k="51" />
    <hkern u1="&#xa9;" u2="&#xc1;" k="51" />
    <hkern u1="&#xa9;" u2="&#xc0;" k="51" />
    <hkern u1="&#xa9;" u2="&#xba;" k="42" />
    <hkern u1="&#xa9;" u2="&#xb0;" k="42" />
    <hkern u1="&#xa9;" u2="&#xaa;" k="42" />
    <hkern u1="&#xa9;" u2="&#x7d;" k="40" />
    <hkern u1="&#xa9;" u2="]" k="40" />
    <hkern u1="&#xa9;" u2="\" k="56" />
    <hkern u1="&#xa9;" u2="Z" k="64" />
    <hkern u1="&#xa9;" u2="Y" k="80" />
    <hkern u1="&#xa9;" u2="X" k="30" />
    <hkern u1="&#xa9;" u2="V" k="56" />
    <hkern u1="&#xa9;" u2="T" k="78" />
    <hkern u1="&#xa9;" u2="A" k="51" />
    <hkern u1="&#xa9;" u2="&#x2f;" k="51" />
    <hkern u1="&#xa9;" u2="&#x2e;" k="52" />
    <hkern u1="&#xa9;" u2="&#x2c;" k="52" />
    <hkern u1="&#xa9;" u2="&#x2a;" k="42" />
    <hkern u1="&#xa9;" u2="&#x29;" k="40" />
    <hkern u1="&#xa9;" u2="&#x27;" k="42" />
    <hkern u1="&#xa9;" u2="&#x26;" k="51" />
    <hkern u1="&#xa9;" u2="&#x22;" k="42" />
    <hkern u1="&#xaa;" u2="&#x2206;" k="191" />
    <hkern u1="&#xaa;" u2="&#x203a;" k="169" />
    <hkern u1="&#xaa;" u2="&#x2039;" k="169" />
    <hkern u1="&#xaa;" u2="&#x2022;" k="169" />
    <hkern u1="&#xaa;" u2="&#x201e;" k="213" />
    <hkern u1="&#xaa;" u2="&#x201a;" k="213" />
    <hkern u1="&#xaa;" u2="&#x2014;" k="169" />
    <hkern u1="&#xaa;" u2="&#x2013;" k="169" />
    <hkern u1="&#xaa;" u2="&#x178;" k="-36" />
    <hkern u1="&#xaa;" u2="&#x153;" k="96" />
    <hkern u1="&#xaa;" u2="&#x152;" k="42" />
    <hkern u1="&#xaa;" u2="&#x119;" k="96" />
    <hkern u1="&#xaa;" u2="&#x107;" k="96" />
    <hkern u1="&#xaa;" u2="&#x106;" k="42" />
    <hkern u1="&#xaa;" u2="&#x105;" k="66" />
    <hkern u1="&#xaa;" u2="&#x104;" k="191" />
    <hkern u1="&#xaa;" u2="&#xf8;" k="96" />
    <hkern u1="&#xaa;" u2="&#xf6;" k="96" />
    <hkern u1="&#xaa;" u2="&#xf5;" k="96" />
    <hkern u1="&#xaa;" u2="&#xf4;" k="96" />
    <hkern u1="&#xaa;" u2="&#xf3;" k="96" />
    <hkern u1="&#xaa;" u2="&#xf2;" k="96" />
    <hkern u1="&#xaa;" u2="&#xf0;" k="96" />
    <hkern u1="&#xaa;" u2="&#xeb;" k="96" />
    <hkern u1="&#xaa;" u2="&#xea;" k="96" />
    <hkern u1="&#xaa;" u2="&#xe9;" k="96" />
    <hkern u1="&#xaa;" u2="&#xe8;" k="96" />
    <hkern u1="&#xaa;" u2="&#xe7;" k="96" />
    <hkern u1="&#xaa;" u2="&#xe6;" k="66" />
    <hkern u1="&#xaa;" u2="&#xe5;" k="66" />
    <hkern u1="&#xaa;" u2="&#xe4;" k="66" />
    <hkern u1="&#xaa;" u2="&#xe3;" k="66" />
    <hkern u1="&#xaa;" u2="&#xe2;" k="66" />
    <hkern u1="&#xaa;" u2="&#xe1;" k="66" />
    <hkern u1="&#xaa;" u2="&#xe0;" k="66" />
    <hkern u1="&#xaa;" u2="&#xdd;" k="-36" />
    <hkern u1="&#xaa;" u2="&#xd8;" k="42" />
    <hkern u1="&#xaa;" u2="&#xd6;" k="42" />
    <hkern u1="&#xaa;" u2="&#xd5;" k="42" />
    <hkern u1="&#xaa;" u2="&#xd4;" k="42" />
    <hkern u1="&#xaa;" u2="&#xd3;" k="42" />
    <hkern u1="&#xaa;" u2="&#xd2;" k="42" />
    <hkern u1="&#xaa;" u2="&#xc7;" k="42" />
    <hkern u1="&#xaa;" u2="&#xc6;" k="191" />
    <hkern u1="&#xaa;" u2="&#xc5;" k="191" />
    <hkern u1="&#xaa;" u2="&#xc4;" k="191" />
    <hkern u1="&#xaa;" u2="&#xc3;" k="191" />
    <hkern u1="&#xaa;" u2="&#xc2;" k="191" />
    <hkern u1="&#xaa;" u2="&#xc1;" k="191" />
    <hkern u1="&#xaa;" u2="&#xc0;" k="191" />
    <hkern u1="&#xaa;" u2="&#xbb;" k="169" />
    <hkern u1="&#xaa;" u2="&#xb7;" k="169" />
    <hkern u1="&#xaa;" u2="&#xae;" k="42" />
    <hkern u1="&#xaa;" u2="&#xad;" k="169" />
    <hkern u1="&#xaa;" u2="&#xab;" k="169" />
    <hkern u1="&#xaa;" u2="&#xa9;" k="42" />
    <hkern u1="&#xaa;" u2="q" k="96" />
    <hkern u1="&#xaa;" u2="o" k="96" />
    <hkern u1="&#xaa;" u2="e" k="96" />
    <hkern u1="&#xaa;" u2="d" k="96" />
    <hkern u1="&#xaa;" u2="c" k="96" />
    <hkern u1="&#xaa;" u2="a" k="66" />
    <hkern u1="&#xaa;" u2="\" k="-44" />
    <hkern u1="&#xaa;" u2="Y" k="-36" />
    <hkern u1="&#xaa;" u2="W" k="-44" />
    <hkern u1="&#xaa;" u2="V" k="-44" />
    <hkern u1="&#xaa;" u2="Q" k="42" />
    <hkern u1="&#xaa;" u2="O" k="42" />
    <hkern u1="&#xaa;" u2="G" k="42" />
    <hkern u1="&#xaa;" u2="C" k="42" />
    <hkern u1="&#xaa;" u2="A" k="191" />
    <hkern u1="&#xaa;" u2="&#x40;" k="42" />
    <hkern u1="&#xaa;" u2="&#x2f;" k="191" />
    <hkern u1="&#xaa;" u2="&#x2e;" k="213" />
    <hkern u1="&#xaa;" u2="&#x2d;" k="169" />
    <hkern u1="&#xaa;" u2="&#x2c;" k="213" />
    <hkern u1="&#xaa;" u2="&#x26;" k="191" />
    <hkern u1="&#xab;" u2="&#x2206;" k="67" />
    <hkern u1="&#xab;" u2="&#x2122;" k="169" />
    <hkern u1="&#xab;" u2="&#x201e;" k="132" />
    <hkern u1="&#xab;" u2="&#x201d;" k="169" />
    <hkern u1="&#xab;" u2="&#x201c;" k="169" />
    <hkern u1="&#xab;" u2="&#x201a;" k="132" />
    <hkern u1="&#xab;" u2="&#x2019;" k="169" />
    <hkern u1="&#xab;" u2="&#x2018;" k="169" />
    <hkern u1="&#xab;" u2="&#x17d;" k="48" />
    <hkern u1="&#xab;" u2="&#x17b;" k="48" />
    <hkern u1="&#xab;" u2="&#x179;" k="48" />
    <hkern u1="&#xab;" u2="&#x178;" k="160" />
    <hkern u1="&#xab;" u2="&#x104;" k="67" />
    <hkern u1="&#xab;" u2="&#xdd;" k="160" />
    <hkern u1="&#xab;" u2="&#xc6;" k="67" />
    <hkern u1="&#xab;" u2="&#xc5;" k="67" />
    <hkern u1="&#xab;" u2="&#xc4;" k="67" />
    <hkern u1="&#xab;" u2="&#xc3;" k="67" />
    <hkern u1="&#xab;" u2="&#xc2;" k="67" />
    <hkern u1="&#xab;" u2="&#xc1;" k="67" />
    <hkern u1="&#xab;" u2="&#xc0;" k="67" />
    <hkern u1="&#xab;" u2="&#xba;" k="169" />
    <hkern u1="&#xab;" u2="&#xb0;" k="169" />
    <hkern u1="&#xab;" u2="&#xaa;" k="169" />
    <hkern u1="&#xab;" u2="\" k="116" />
    <hkern u1="&#xab;" u2="Z" k="48" />
    <hkern u1="&#xab;" u2="Y" k="160" />
    <hkern u1="&#xab;" u2="X" k="66" />
    <hkern u1="&#xab;" u2="W" k="36" />
    <hkern u1="&#xab;" u2="V" k="116" />
    <hkern u1="&#xab;" u2="T" k="180" />
    <hkern u1="&#xab;" u2="A" k="67" />
    <hkern u1="&#xab;" u2="&#x2f;" k="67" />
    <hkern u1="&#xab;" u2="&#x2e;" k="132" />
    <hkern u1="&#xab;" u2="&#x2c;" k="132" />
    <hkern u1="&#xab;" u2="&#x2a;" k="169" />
    <hkern u1="&#xab;" u2="&#x27;" k="169" />
    <hkern u1="&#xab;" u2="&#x26;" k="67" />
    <hkern u1="&#xab;" u2="&#x22;" k="169" />
    <hkern u1="&#xad;" u2="&#x2206;" k="67" />
    <hkern u1="&#xad;" u2="&#x2122;" k="169" />
    <hkern u1="&#xad;" u2="&#x201e;" k="132" />
    <hkern u1="&#xad;" u2="&#x201d;" k="169" />
    <hkern u1="&#xad;" u2="&#x201c;" k="169" />
    <hkern u1="&#xad;" u2="&#x201a;" k="132" />
    <hkern u1="&#xad;" u2="&#x2019;" k="169" />
    <hkern u1="&#xad;" u2="&#x2018;" k="169" />
    <hkern u1="&#xad;" u2="&#x17d;" k="48" />
    <hkern u1="&#xad;" u2="&#x17b;" k="48" />
    <hkern u1="&#xad;" u2="&#x179;" k="48" />
    <hkern u1="&#xad;" u2="&#x178;" k="160" />
    <hkern u1="&#xad;" u2="&#x104;" k="67" />
    <hkern u1="&#xad;" u2="&#xdd;" k="160" />
    <hkern u1="&#xad;" u2="&#xc6;" k="67" />
    <hkern u1="&#xad;" u2="&#xc5;" k="67" />
    <hkern u1="&#xad;" u2="&#xc4;" k="67" />
    <hkern u1="&#xad;" u2="&#xc3;" k="67" />
    <hkern u1="&#xad;" u2="&#xc2;" k="67" />
    <hkern u1="&#xad;" u2="&#xc1;" k="67" />
    <hkern u1="&#xad;" u2="&#xc0;" k="67" />
    <hkern u1="&#xad;" u2="&#xba;" k="169" />
    <hkern u1="&#xad;" u2="&#xb0;" k="169" />
    <hkern u1="&#xad;" u2="&#xaa;" k="169" />
    <hkern u1="&#xad;" u2="\" k="116" />
    <hkern u1="&#xad;" u2="Z" k="48" />
    <hkern u1="&#xad;" u2="Y" k="160" />
    <hkern u1="&#xad;" u2="X" k="66" />
    <hkern u1="&#xad;" u2="W" k="36" />
    <hkern u1="&#xad;" u2="V" k="116" />
    <hkern u1="&#xad;" u2="T" k="180" />
    <hkern u1="&#xad;" u2="A" k="67" />
    <hkern u1="&#xad;" u2="&#x2f;" k="67" />
    <hkern u1="&#xad;" u2="&#x2e;" k="132" />
    <hkern u1="&#xad;" u2="&#x2c;" k="132" />
    <hkern u1="&#xad;" u2="&#x2a;" k="169" />
    <hkern u1="&#xad;" u2="&#x27;" k="169" />
    <hkern u1="&#xad;" u2="&#x26;" k="67" />
    <hkern u1="&#xad;" u2="&#x22;" k="169" />
    <hkern u1="&#xae;" u2="&#x2206;" k="51" />
    <hkern u1="&#xae;" u2="&#x2122;" k="42" />
    <hkern u1="&#xae;" u2="&#x201e;" k="52" />
    <hkern u1="&#xae;" u2="&#x201d;" k="42" />
    <hkern u1="&#xae;" u2="&#x201c;" k="42" />
    <hkern u1="&#xae;" u2="&#x201a;" k="52" />
    <hkern u1="&#xae;" u2="&#x2019;" k="42" />
    <hkern u1="&#xae;" u2="&#x2018;" k="42" />
    <hkern u1="&#xae;" u2="&#x17d;" k="64" />
    <hkern u1="&#xae;" u2="&#x17b;" k="64" />
    <hkern u1="&#xae;" u2="&#x179;" k="64" />
    <hkern u1="&#xae;" u2="&#x178;" k="80" />
    <hkern u1="&#xae;" u2="&#x104;" k="51" />
    <hkern u1="&#xae;" u2="&#xdd;" k="80" />
    <hkern u1="&#xae;" u2="&#xc6;" k="51" />
    <hkern u1="&#xae;" u2="&#xc5;" k="51" />
    <hkern u1="&#xae;" u2="&#xc4;" k="51" />
    <hkern u1="&#xae;" u2="&#xc3;" k="51" />
    <hkern u1="&#xae;" u2="&#xc2;" k="51" />
    <hkern u1="&#xae;" u2="&#xc1;" k="51" />
    <hkern u1="&#xae;" u2="&#xc0;" k="51" />
    <hkern u1="&#xae;" u2="&#xba;" k="42" />
    <hkern u1="&#xae;" u2="&#xb0;" k="42" />
    <hkern u1="&#xae;" u2="&#xaa;" k="42" />
    <hkern u1="&#xae;" u2="&#x7d;" k="40" />
    <hkern u1="&#xae;" u2="]" k="40" />
    <hkern u1="&#xae;" u2="\" k="56" />
    <hkern u1="&#xae;" u2="Z" k="64" />
    <hkern u1="&#xae;" u2="Y" k="80" />
    <hkern u1="&#xae;" u2="X" k="30" />
    <hkern u1="&#xae;" u2="V" k="56" />
    <hkern u1="&#xae;" u2="T" k="78" />
    <hkern u1="&#xae;" u2="A" k="51" />
    <hkern u1="&#xae;" u2="&#x2f;" k="51" />
    <hkern u1="&#xae;" u2="&#x2e;" k="52" />
    <hkern u1="&#xae;" u2="&#x2c;" k="52" />
    <hkern u1="&#xae;" u2="&#x2a;" k="42" />
    <hkern u1="&#xae;" u2="&#x29;" k="40" />
    <hkern u1="&#xae;" u2="&#x27;" k="42" />
    <hkern u1="&#xae;" u2="&#x26;" k="51" />
    <hkern u1="&#xae;" u2="&#x22;" k="42" />
    <hkern u1="&#xb0;" u2="&#x2206;" k="191" />
    <hkern u1="&#xb0;" u2="&#x203a;" k="169" />
    <hkern u1="&#xb0;" u2="&#x2039;" k="169" />
    <hkern u1="&#xb0;" u2="&#x2022;" k="169" />
    <hkern u1="&#xb0;" u2="&#x201e;" k="213" />
    <hkern u1="&#xb0;" u2="&#x201a;" k="213" />
    <hkern u1="&#xb0;" u2="&#x2014;" k="169" />
    <hkern u1="&#xb0;" u2="&#x2013;" k="169" />
    <hkern u1="&#xb0;" u2="&#x178;" k="-36" />
    <hkern u1="&#xb0;" u2="&#x153;" k="96" />
    <hkern u1="&#xb0;" u2="&#x152;" k="42" />
    <hkern u1="&#xb0;" u2="&#x119;" k="96" />
    <hkern u1="&#xb0;" u2="&#x107;" k="96" />
    <hkern u1="&#xb0;" u2="&#x106;" k="42" />
    <hkern u1="&#xb0;" u2="&#x105;" k="66" />
    <hkern u1="&#xb0;" u2="&#x104;" k="191" />
    <hkern u1="&#xb0;" u2="&#xf8;" k="96" />
    <hkern u1="&#xb0;" u2="&#xf6;" k="96" />
    <hkern u1="&#xb0;" u2="&#xf5;" k="96" />
    <hkern u1="&#xb0;" u2="&#xf4;" k="96" />
    <hkern u1="&#xb0;" u2="&#xf3;" k="96" />
    <hkern u1="&#xb0;" u2="&#xf2;" k="96" />
    <hkern u1="&#xb0;" u2="&#xf0;" k="96" />
    <hkern u1="&#xb0;" u2="&#xeb;" k="96" />
    <hkern u1="&#xb0;" u2="&#xea;" k="96" />
    <hkern u1="&#xb0;" u2="&#xe9;" k="96" />
    <hkern u1="&#xb0;" u2="&#xe8;" k="96" />
    <hkern u1="&#xb0;" u2="&#xe7;" k="96" />
    <hkern u1="&#xb0;" u2="&#xe6;" k="66" />
    <hkern u1="&#xb0;" u2="&#xe5;" k="66" />
    <hkern u1="&#xb0;" u2="&#xe4;" k="66" />
    <hkern u1="&#xb0;" u2="&#xe3;" k="66" />
    <hkern u1="&#xb0;" u2="&#xe2;" k="66" />
    <hkern u1="&#xb0;" u2="&#xe1;" k="66" />
    <hkern u1="&#xb0;" u2="&#xe0;" k="66" />
    <hkern u1="&#xb0;" u2="&#xdd;" k="-36" />
    <hkern u1="&#xb0;" u2="&#xd8;" k="42" />
    <hkern u1="&#xb0;" u2="&#xd6;" k="42" />
    <hkern u1="&#xb0;" u2="&#xd5;" k="42" />
    <hkern u1="&#xb0;" u2="&#xd4;" k="42" />
    <hkern u1="&#xb0;" u2="&#xd3;" k="42" />
    <hkern u1="&#xb0;" u2="&#xd2;" k="42" />
    <hkern u1="&#xb0;" u2="&#xc7;" k="42" />
    <hkern u1="&#xb0;" u2="&#xc6;" k="191" />
    <hkern u1="&#xb0;" u2="&#xc5;" k="191" />
    <hkern u1="&#xb0;" u2="&#xc4;" k="191" />
    <hkern u1="&#xb0;" u2="&#xc3;" k="191" />
    <hkern u1="&#xb0;" u2="&#xc2;" k="191" />
    <hkern u1="&#xb0;" u2="&#xc1;" k="191" />
    <hkern u1="&#xb0;" u2="&#xc0;" k="191" />
    <hkern u1="&#xb0;" u2="&#xbb;" k="169" />
    <hkern u1="&#xb0;" u2="&#xb7;" k="169" />
    <hkern u1="&#xb0;" u2="&#xae;" k="42" />
    <hkern u1="&#xb0;" u2="&#xad;" k="169" />
    <hkern u1="&#xb0;" u2="&#xab;" k="169" />
    <hkern u1="&#xb0;" u2="&#xa9;" k="42" />
    <hkern u1="&#xb0;" u2="q" k="96" />
    <hkern u1="&#xb0;" u2="o" k="96" />
    <hkern u1="&#xb0;" u2="e" k="96" />
    <hkern u1="&#xb0;" u2="d" k="96" />
    <hkern u1="&#xb0;" u2="c" k="96" />
    <hkern u1="&#xb0;" u2="a" k="66" />
    <hkern u1="&#xb0;" u2="\" k="-44" />
    <hkern u1="&#xb0;" u2="Y" k="-36" />
    <hkern u1="&#xb0;" u2="W" k="-44" />
    <hkern u1="&#xb0;" u2="V" k="-44" />
    <hkern u1="&#xb0;" u2="Q" k="42" />
    <hkern u1="&#xb0;" u2="O" k="42" />
    <hkern u1="&#xb0;" u2="G" k="42" />
    <hkern u1="&#xb0;" u2="C" k="42" />
    <hkern u1="&#xb0;" u2="A" k="191" />
    <hkern u1="&#xb0;" u2="&#x40;" k="42" />
    <hkern u1="&#xb0;" u2="&#x2f;" k="191" />
    <hkern u1="&#xb0;" u2="&#x2e;" k="213" />
    <hkern u1="&#xb0;" u2="&#x2d;" k="169" />
    <hkern u1="&#xb0;" u2="&#x2c;" k="213" />
    <hkern u1="&#xb0;" u2="&#x26;" k="191" />
    <hkern u1="&#xb2;" u2="&#x2206;" k="202" />
    <hkern u1="&#xb2;" u2="&#x178;" k="-40" />
    <hkern u1="&#xb2;" u2="&#x104;" k="202" />
    <hkern u1="&#xb2;" u2="&#xdd;" k="-40" />
    <hkern u1="&#xb2;" u2="&#xc6;" k="202" />
    <hkern u1="&#xb2;" u2="&#xc5;" k="202" />
    <hkern u1="&#xb2;" u2="&#xc4;" k="202" />
    <hkern u1="&#xb2;" u2="&#xc3;" k="202" />
    <hkern u1="&#xb2;" u2="&#xc2;" k="202" />
    <hkern u1="&#xb2;" u2="&#xc1;" k="202" />
    <hkern u1="&#xb2;" u2="&#xc0;" k="202" />
    <hkern u1="&#xb2;" u2="\" k="-49" />
    <hkern u1="&#xb2;" u2="Y" k="-40" />
    <hkern u1="&#xb2;" u2="W" k="-49" />
    <hkern u1="&#xb2;" u2="V" k="-49" />
    <hkern u1="&#xb2;" u2="A" k="202" />
    <hkern u1="&#xb2;" u2="&#x2f;" k="202" />
    <hkern u1="&#xb2;" u2="&#x26;" k="202" />
    <hkern u1="&#xb3;" u2="&#x2206;" k="202" />
    <hkern u1="&#xb3;" u2="&#x178;" k="-40" />
    <hkern u1="&#xb3;" u2="&#x104;" k="202" />
    <hkern u1="&#xb3;" u2="&#xdd;" k="-40" />
    <hkern u1="&#xb3;" u2="&#xc6;" k="202" />
    <hkern u1="&#xb3;" u2="&#xc5;" k="202" />
    <hkern u1="&#xb3;" u2="&#xc4;" k="202" />
    <hkern u1="&#xb3;" u2="&#xc3;" k="202" />
    <hkern u1="&#xb3;" u2="&#xc2;" k="202" />
    <hkern u1="&#xb3;" u2="&#xc1;" k="202" />
    <hkern u1="&#xb3;" u2="&#xc0;" k="202" />
    <hkern u1="&#xb3;" u2="\" k="-49" />
    <hkern u1="&#xb3;" u2="Y" k="-40" />
    <hkern u1="&#xb3;" u2="W" k="-49" />
    <hkern u1="&#xb3;" u2="V" k="-49" />
    <hkern u1="&#xb3;" u2="A" k="202" />
    <hkern u1="&#xb3;" u2="&#x2f;" k="202" />
    <hkern u1="&#xb3;" u2="&#x26;" k="202" />
    <hkern u1="&#xb7;" u2="&#x2206;" k="67" />
    <hkern u1="&#xb7;" u2="&#x2122;" k="169" />
    <hkern u1="&#xb7;" u2="&#x201e;" k="132" />
    <hkern u1="&#xb7;" u2="&#x201d;" k="169" />
    <hkern u1="&#xb7;" u2="&#x201c;" k="169" />
    <hkern u1="&#xb7;" u2="&#x201a;" k="132" />
    <hkern u1="&#xb7;" u2="&#x2019;" k="169" />
    <hkern u1="&#xb7;" u2="&#x2018;" k="169" />
    <hkern u1="&#xb7;" u2="&#x17d;" k="48" />
    <hkern u1="&#xb7;" u2="&#x17b;" k="48" />
    <hkern u1="&#xb7;" u2="&#x179;" k="48" />
    <hkern u1="&#xb7;" u2="&#x178;" k="160" />
    <hkern u1="&#xb7;" u2="&#x104;" k="67" />
    <hkern u1="&#xb7;" u2="&#xdd;" k="160" />
    <hkern u1="&#xb7;" u2="&#xc6;" k="67" />
    <hkern u1="&#xb7;" u2="&#xc5;" k="67" />
    <hkern u1="&#xb7;" u2="&#xc4;" k="67" />
    <hkern u1="&#xb7;" u2="&#xc3;" k="67" />
    <hkern u1="&#xb7;" u2="&#xc2;" k="67" />
    <hkern u1="&#xb7;" u2="&#xc1;" k="67" />
    <hkern u1="&#xb7;" u2="&#xc0;" k="67" />
    <hkern u1="&#xb7;" u2="&#xba;" k="169" />
    <hkern u1="&#xb7;" u2="&#xb0;" k="169" />
    <hkern u1="&#xb7;" u2="&#xaa;" k="169" />
    <hkern u1="&#xb7;" u2="\" k="116" />
    <hkern u1="&#xb7;" u2="Z" k="48" />
    <hkern u1="&#xb7;" u2="Y" k="160" />
    <hkern u1="&#xb7;" u2="X" k="66" />
    <hkern u1="&#xb7;" u2="W" k="36" />
    <hkern u1="&#xb7;" u2="V" k="116" />
    <hkern u1="&#xb7;" u2="T" k="180" />
    <hkern u1="&#xb7;" u2="A" k="67" />
    <hkern u1="&#xb7;" u2="&#x2f;" k="67" />
    <hkern u1="&#xb7;" u2="&#x2e;" k="132" />
    <hkern u1="&#xb7;" u2="&#x2c;" k="132" />
    <hkern u1="&#xb7;" u2="&#x2a;" k="169" />
    <hkern u1="&#xb7;" u2="&#x27;" k="169" />
    <hkern u1="&#xb7;" u2="&#x26;" k="67" />
    <hkern u1="&#xb7;" u2="&#x22;" k="169" />
    <hkern u1="&#xb9;" u2="&#x2206;" k="202" />
    <hkern u1="&#xb9;" u2="&#x178;" k="-40" />
    <hkern u1="&#xb9;" u2="&#x104;" k="202" />
    <hkern u1="&#xb9;" u2="&#xdd;" k="-40" />
    <hkern u1="&#xb9;" u2="&#xc6;" k="202" />
    <hkern u1="&#xb9;" u2="&#xc5;" k="202" />
    <hkern u1="&#xb9;" u2="&#xc4;" k="202" />
    <hkern u1="&#xb9;" u2="&#xc3;" k="202" />
    <hkern u1="&#xb9;" u2="&#xc2;" k="202" />
    <hkern u1="&#xb9;" u2="&#xc1;" k="202" />
    <hkern u1="&#xb9;" u2="&#xc0;" k="202" />
    <hkern u1="&#xb9;" u2="\" k="-49" />
    <hkern u1="&#xb9;" u2="Y" k="-40" />
    <hkern u1="&#xb9;" u2="W" k="-49" />
    <hkern u1="&#xb9;" u2="V" k="-49" />
    <hkern u1="&#xb9;" u2="A" k="202" />
    <hkern u1="&#xb9;" u2="&#x2f;" k="202" />
    <hkern u1="&#xb9;" u2="&#x26;" k="202" />
    <hkern u1="&#xba;" u2="&#x2206;" k="191" />
    <hkern u1="&#xba;" u2="&#x203a;" k="169" />
    <hkern u1="&#xba;" u2="&#x2039;" k="169" />
    <hkern u1="&#xba;" u2="&#x2022;" k="169" />
    <hkern u1="&#xba;" u2="&#x201e;" k="213" />
    <hkern u1="&#xba;" u2="&#x201a;" k="213" />
    <hkern u1="&#xba;" u2="&#x2014;" k="169" />
    <hkern u1="&#xba;" u2="&#x2013;" k="169" />
    <hkern u1="&#xba;" u2="&#x178;" k="-36" />
    <hkern u1="&#xba;" u2="&#x153;" k="96" />
    <hkern u1="&#xba;" u2="&#x152;" k="42" />
    <hkern u1="&#xba;" u2="&#x119;" k="96" />
    <hkern u1="&#xba;" u2="&#x107;" k="96" />
    <hkern u1="&#xba;" u2="&#x106;" k="42" />
    <hkern u1="&#xba;" u2="&#x105;" k="66" />
    <hkern u1="&#xba;" u2="&#x104;" k="191" />
    <hkern u1="&#xba;" u2="&#xf8;" k="96" />
    <hkern u1="&#xba;" u2="&#xf6;" k="96" />
    <hkern u1="&#xba;" u2="&#xf5;" k="96" />
    <hkern u1="&#xba;" u2="&#xf4;" k="96" />
    <hkern u1="&#xba;" u2="&#xf3;" k="96" />
    <hkern u1="&#xba;" u2="&#xf2;" k="96" />
    <hkern u1="&#xba;" u2="&#xf0;" k="96" />
    <hkern u1="&#xba;" u2="&#xeb;" k="96" />
    <hkern u1="&#xba;" u2="&#xea;" k="96" />
    <hkern u1="&#xba;" u2="&#xe9;" k="96" />
    <hkern u1="&#xba;" u2="&#xe8;" k="96" />
    <hkern u1="&#xba;" u2="&#xe7;" k="96" />
    <hkern u1="&#xba;" u2="&#xe6;" k="66" />
    <hkern u1="&#xba;" u2="&#xe5;" k="66" />
    <hkern u1="&#xba;" u2="&#xe4;" k="66" />
    <hkern u1="&#xba;" u2="&#xe3;" k="66" />
    <hkern u1="&#xba;" u2="&#xe2;" k="66" />
    <hkern u1="&#xba;" u2="&#xe1;" k="66" />
    <hkern u1="&#xba;" u2="&#xe0;" k="66" />
    <hkern u1="&#xba;" u2="&#xdd;" k="-36" />
    <hkern u1="&#xba;" u2="&#xd8;" k="42" />
    <hkern u1="&#xba;" u2="&#xd6;" k="42" />
    <hkern u1="&#xba;" u2="&#xd5;" k="42" />
    <hkern u1="&#xba;" u2="&#xd4;" k="42" />
    <hkern u1="&#xba;" u2="&#xd3;" k="42" />
    <hkern u1="&#xba;" u2="&#xd2;" k="42" />
    <hkern u1="&#xba;" u2="&#xc7;" k="42" />
    <hkern u1="&#xba;" u2="&#xc6;" k="191" />
    <hkern u1="&#xba;" u2="&#xc5;" k="191" />
    <hkern u1="&#xba;" u2="&#xc4;" k="191" />
    <hkern u1="&#xba;" u2="&#xc3;" k="191" />
    <hkern u1="&#xba;" u2="&#xc2;" k="191" />
    <hkern u1="&#xba;" u2="&#xc1;" k="191" />
    <hkern u1="&#xba;" u2="&#xc0;" k="191" />
    <hkern u1="&#xba;" u2="&#xbb;" k="169" />
    <hkern u1="&#xba;" u2="&#xb7;" k="169" />
    <hkern u1="&#xba;" u2="&#xae;" k="42" />
    <hkern u1="&#xba;" u2="&#xad;" k="169" />
    <hkern u1="&#xba;" u2="&#xab;" k="169" />
    <hkern u1="&#xba;" u2="&#xa9;" k="42" />
    <hkern u1="&#xba;" u2="q" k="96" />
    <hkern u1="&#xba;" u2="o" k="96" />
    <hkern u1="&#xba;" u2="e" k="96" />
    <hkern u1="&#xba;" u2="d" k="96" />
    <hkern u1="&#xba;" u2="c" k="96" />
    <hkern u1="&#xba;" u2="a" k="66" />
    <hkern u1="&#xba;" u2="\" k="-44" />
    <hkern u1="&#xba;" u2="Y" k="-36" />
    <hkern u1="&#xba;" u2="W" k="-44" />
    <hkern u1="&#xba;" u2="V" k="-44" />
    <hkern u1="&#xba;" u2="Q" k="42" />
    <hkern u1="&#xba;" u2="O" k="42" />
    <hkern u1="&#xba;" u2="G" k="42" />
    <hkern u1="&#xba;" u2="C" k="42" />
    <hkern u1="&#xba;" u2="A" k="191" />
    <hkern u1="&#xba;" u2="&#x40;" k="42" />
    <hkern u1="&#xba;" u2="&#x2f;" k="191" />
    <hkern u1="&#xba;" u2="&#x2e;" k="213" />
    <hkern u1="&#xba;" u2="&#x2d;" k="169" />
    <hkern u1="&#xba;" u2="&#x2c;" k="213" />
    <hkern u1="&#xba;" u2="&#x26;" k="191" />
    <hkern u1="&#xbb;" u2="&#x2206;" k="67" />
    <hkern u1="&#xbb;" u2="&#x2122;" k="169" />
    <hkern u1="&#xbb;" u2="&#x201e;" k="132" />
    <hkern u1="&#xbb;" u2="&#x201d;" k="169" />
    <hkern u1="&#xbb;" u2="&#x201c;" k="169" />
    <hkern u1="&#xbb;" u2="&#x201a;" k="132" />
    <hkern u1="&#xbb;" u2="&#x2019;" k="169" />
    <hkern u1="&#xbb;" u2="&#x2018;" k="169" />
    <hkern u1="&#xbb;" u2="&#x17d;" k="48" />
    <hkern u1="&#xbb;" u2="&#x17b;" k="48" />
    <hkern u1="&#xbb;" u2="&#x179;" k="48" />
    <hkern u1="&#xbb;" u2="&#x178;" k="160" />
    <hkern u1="&#xbb;" u2="&#x104;" k="67" />
    <hkern u1="&#xbb;" u2="&#xdd;" k="160" />
    <hkern u1="&#xbb;" u2="&#xc6;" k="67" />
    <hkern u1="&#xbb;" u2="&#xc5;" k="67" />
    <hkern u1="&#xbb;" u2="&#xc4;" k="67" />
    <hkern u1="&#xbb;" u2="&#xc3;" k="67" />
    <hkern u1="&#xbb;" u2="&#xc2;" k="67" />
    <hkern u1="&#xbb;" u2="&#xc1;" k="67" />
    <hkern u1="&#xbb;" u2="&#xc0;" k="67" />
    <hkern u1="&#xbb;" u2="&#xba;" k="169" />
    <hkern u1="&#xbb;" u2="&#xb0;" k="169" />
    <hkern u1="&#xbb;" u2="&#xaa;" k="169" />
    <hkern u1="&#xbb;" u2="\" k="116" />
    <hkern u1="&#xbb;" u2="Z" k="48" />
    <hkern u1="&#xbb;" u2="Y" k="160" />
    <hkern u1="&#xbb;" u2="X" k="66" />
    <hkern u1="&#xbb;" u2="W" k="36" />
    <hkern u1="&#xbb;" u2="V" k="116" />
    <hkern u1="&#xbb;" u2="T" k="180" />
    <hkern u1="&#xbb;" u2="A" k="67" />
    <hkern u1="&#xbb;" u2="&#x2f;" k="67" />
    <hkern u1="&#xbb;" u2="&#x2e;" k="132" />
    <hkern u1="&#xbb;" u2="&#x2c;" k="132" />
    <hkern u1="&#xbb;" u2="&#x2a;" k="169" />
    <hkern u1="&#xbb;" u2="&#x27;" k="169" />
    <hkern u1="&#xbb;" u2="&#x26;" k="67" />
    <hkern u1="&#xbb;" u2="&#x22;" k="169" />
    <hkern u1="&#xc0;" u2="&#x2122;" k="191" />
    <hkern u1="&#xc0;" u2="&#x203a;" k="67" />
    <hkern u1="&#xc0;" u2="&#x2039;" k="67" />
    <hkern u1="&#xc0;" u2="&#x2022;" k="67" />
    <hkern u1="&#xc0;" u2="&#x201d;" k="191" />
    <hkern u1="&#xc0;" u2="&#x201c;" k="191" />
    <hkern u1="&#xc0;" u2="&#x2019;" k="191" />
    <hkern u1="&#xc0;" u2="&#x2018;" k="191" />
    <hkern u1="&#xc0;" u2="&#x2014;" k="67" />
    <hkern u1="&#xc0;" u2="&#x2013;" k="67" />
    <hkern u1="&#xc0;" u2="&#x178;" k="182" />
    <hkern u1="&#xc0;" u2="&#x152;" k="51" />
    <hkern u1="&#xc0;" u2="&#x106;" k="51" />
    <hkern u1="&#xc0;" u2="&#xff;" k="91" />
    <hkern u1="&#xc0;" u2="&#xfd;" k="91" />
    <hkern u1="&#xc0;" u2="&#xdd;" k="182" />
    <hkern u1="&#xc0;" u2="&#xdc;" k="52" />
    <hkern u1="&#xc0;" u2="&#xdb;" k="52" />
    <hkern u1="&#xc0;" u2="&#xda;" k="52" />
    <hkern u1="&#xc0;" u2="&#xd9;" k="52" />
    <hkern u1="&#xc0;" u2="&#xd8;" k="51" />
    <hkern u1="&#xc0;" u2="&#xd6;" k="51" />
    <hkern u1="&#xc0;" u2="&#xd5;" k="51" />
    <hkern u1="&#xc0;" u2="&#xd4;" k="51" />
    <hkern u1="&#xc0;" u2="&#xd3;" k="51" />
    <hkern u1="&#xc0;" u2="&#xd2;" k="51" />
    <hkern u1="&#xc0;" u2="&#xc7;" k="51" />
    <hkern u1="&#xc0;" u2="&#xbb;" k="67" />
    <hkern u1="&#xc0;" u2="&#xba;" k="191" />
    <hkern u1="&#xc0;" u2="&#xb9;" k="202" />
    <hkern u1="&#xc0;" u2="&#xb7;" k="67" />
    <hkern u1="&#xc0;" u2="&#xb3;" k="202" />
    <hkern u1="&#xc0;" u2="&#xb2;" k="202" />
    <hkern u1="&#xc0;" u2="&#xb0;" k="191" />
    <hkern u1="&#xc0;" u2="&#xae;" k="51" />
    <hkern u1="&#xc0;" u2="&#xad;" k="67" />
    <hkern u1="&#xc0;" u2="&#xab;" k="67" />
    <hkern u1="&#xc0;" u2="&#xaa;" k="191" />
    <hkern u1="&#xc0;" u2="&#xa9;" k="51" />
    <hkern u1="&#xc0;" u2="y" k="91" />
    <hkern u1="&#xc0;" u2="v" k="91" />
    <hkern u1="&#xc0;" u2="\" k="169" />
    <hkern u1="&#xc0;" u2="Y" k="182" />
    <hkern u1="&#xc0;" u2="W" k="102" />
    <hkern u1="&#xc0;" u2="V" k="169" />
    <hkern u1="&#xc0;" u2="U" k="52" />
    <hkern u1="&#xc0;" u2="T" k="147" />
    <hkern u1="&#xc0;" u2="Q" k="51" />
    <hkern u1="&#xc0;" u2="O" k="51" />
    <hkern u1="&#xc0;" u2="J" k="-56" />
    <hkern u1="&#xc0;" u2="G" k="51" />
    <hkern u1="&#xc0;" u2="C" k="51" />
    <hkern u1="&#xc0;" u2="&#x40;" k="51" />
    <hkern u1="&#xc0;" u2="&#x3f;" k="63" />
    <hkern u1="&#xc0;" u2="&#x2d;" k="67" />
    <hkern u1="&#xc0;" u2="&#x2a;" k="191" />
    <hkern u1="&#xc0;" u2="&#x27;" k="191" />
    <hkern u1="&#xc0;" u2="&#x22;" k="191" />
    <hkern u1="&#xc1;" u2="&#x2122;" k="191" />
    <hkern u1="&#xc1;" u2="&#x203a;" k="67" />
    <hkern u1="&#xc1;" u2="&#x2039;" k="67" />
    <hkern u1="&#xc1;" u2="&#x2022;" k="67" />
    <hkern u1="&#xc1;" u2="&#x201d;" k="191" />
    <hkern u1="&#xc1;" u2="&#x201c;" k="191" />
    <hkern u1="&#xc1;" u2="&#x2019;" k="191" />
    <hkern u1="&#xc1;" u2="&#x2018;" k="191" />
    <hkern u1="&#xc1;" u2="&#x2014;" k="67" />
    <hkern u1="&#xc1;" u2="&#x2013;" k="67" />
    <hkern u1="&#xc1;" u2="&#x178;" k="182" />
    <hkern u1="&#xc1;" u2="&#x152;" k="51" />
    <hkern u1="&#xc1;" u2="&#x106;" k="51" />
    <hkern u1="&#xc1;" u2="&#xff;" k="91" />
    <hkern u1="&#xc1;" u2="&#xfd;" k="91" />
    <hkern u1="&#xc1;" u2="&#xdd;" k="182" />
    <hkern u1="&#xc1;" u2="&#xdc;" k="52" />
    <hkern u1="&#xc1;" u2="&#xdb;" k="52" />
    <hkern u1="&#xc1;" u2="&#xda;" k="52" />
    <hkern u1="&#xc1;" u2="&#xd9;" k="52" />
    <hkern u1="&#xc1;" u2="&#xd8;" k="51" />
    <hkern u1="&#xc1;" u2="&#xd6;" k="51" />
    <hkern u1="&#xc1;" u2="&#xd5;" k="51" />
    <hkern u1="&#xc1;" u2="&#xd4;" k="51" />
    <hkern u1="&#xc1;" u2="&#xd3;" k="51" />
    <hkern u1="&#xc1;" u2="&#xd2;" k="51" />
    <hkern u1="&#xc1;" u2="&#xc7;" k="51" />
    <hkern u1="&#xc1;" u2="&#xbb;" k="67" />
    <hkern u1="&#xc1;" u2="&#xba;" k="191" />
    <hkern u1="&#xc1;" u2="&#xb9;" k="202" />
    <hkern u1="&#xc1;" u2="&#xb7;" k="67" />
    <hkern u1="&#xc1;" u2="&#xb3;" k="202" />
    <hkern u1="&#xc1;" u2="&#xb2;" k="202" />
    <hkern u1="&#xc1;" u2="&#xb0;" k="191" />
    <hkern u1="&#xc1;" u2="&#xae;" k="51" />
    <hkern u1="&#xc1;" u2="&#xad;" k="67" />
    <hkern u1="&#xc1;" u2="&#xab;" k="67" />
    <hkern u1="&#xc1;" u2="&#xaa;" k="191" />
    <hkern u1="&#xc1;" u2="&#xa9;" k="51" />
    <hkern u1="&#xc1;" u2="y" k="91" />
    <hkern u1="&#xc1;" u2="v" k="91" />
    <hkern u1="&#xc1;" u2="\" k="169" />
    <hkern u1="&#xc1;" u2="Y" k="182" />
    <hkern u1="&#xc1;" u2="W" k="102" />
    <hkern u1="&#xc1;" u2="V" k="169" />
    <hkern u1="&#xc1;" u2="U" k="52" />
    <hkern u1="&#xc1;" u2="T" k="147" />
    <hkern u1="&#xc1;" u2="Q" k="51" />
    <hkern u1="&#xc1;" u2="O" k="51" />
    <hkern u1="&#xc1;" u2="J" k="-56" />
    <hkern u1="&#xc1;" u2="G" k="51" />
    <hkern u1="&#xc1;" u2="C" k="51" />
    <hkern u1="&#xc1;" u2="&#x40;" k="51" />
    <hkern u1="&#xc1;" u2="&#x3f;" k="63" />
    <hkern u1="&#xc1;" u2="&#x2d;" k="67" />
    <hkern u1="&#xc1;" u2="&#x2a;" k="191" />
    <hkern u1="&#xc1;" u2="&#x27;" k="191" />
    <hkern u1="&#xc1;" u2="&#x22;" k="191" />
    <hkern u1="&#xc2;" u2="&#x2122;" k="191" />
    <hkern u1="&#xc2;" u2="&#x203a;" k="67" />
    <hkern u1="&#xc2;" u2="&#x2039;" k="67" />
    <hkern u1="&#xc2;" u2="&#x2022;" k="67" />
    <hkern u1="&#xc2;" u2="&#x201d;" k="191" />
    <hkern u1="&#xc2;" u2="&#x201c;" k="191" />
    <hkern u1="&#xc2;" u2="&#x2019;" k="191" />
    <hkern u1="&#xc2;" u2="&#x2018;" k="191" />
    <hkern u1="&#xc2;" u2="&#x2014;" k="67" />
    <hkern u1="&#xc2;" u2="&#x2013;" k="67" />
    <hkern u1="&#xc2;" u2="&#x178;" k="182" />
    <hkern u1="&#xc2;" u2="&#x152;" k="51" />
    <hkern u1="&#xc2;" u2="&#x106;" k="51" />
    <hkern u1="&#xc2;" u2="&#xff;" k="91" />
    <hkern u1="&#xc2;" u2="&#xfd;" k="91" />
    <hkern u1="&#xc2;" u2="&#xdd;" k="182" />
    <hkern u1="&#xc2;" u2="&#xdc;" k="52" />
    <hkern u1="&#xc2;" u2="&#xdb;" k="52" />
    <hkern u1="&#xc2;" u2="&#xda;" k="52" />
    <hkern u1="&#xc2;" u2="&#xd9;" k="52" />
    <hkern u1="&#xc2;" u2="&#xd8;" k="51" />
    <hkern u1="&#xc2;" u2="&#xd6;" k="51" />
    <hkern u1="&#xc2;" u2="&#xd5;" k="51" />
    <hkern u1="&#xc2;" u2="&#xd4;" k="51" />
    <hkern u1="&#xc2;" u2="&#xd3;" k="51" />
    <hkern u1="&#xc2;" u2="&#xd2;" k="51" />
    <hkern u1="&#xc2;" u2="&#xc7;" k="51" />
    <hkern u1="&#xc2;" u2="&#xbb;" k="67" />
    <hkern u1="&#xc2;" u2="&#xba;" k="191" />
    <hkern u1="&#xc2;" u2="&#xb9;" k="202" />
    <hkern u1="&#xc2;" u2="&#xb7;" k="67" />
    <hkern u1="&#xc2;" u2="&#xb3;" k="202" />
    <hkern u1="&#xc2;" u2="&#xb2;" k="202" />
    <hkern u1="&#xc2;" u2="&#xb0;" k="191" />
    <hkern u1="&#xc2;" u2="&#xae;" k="51" />
    <hkern u1="&#xc2;" u2="&#xad;" k="67" />
    <hkern u1="&#xc2;" u2="&#xab;" k="67" />
    <hkern u1="&#xc2;" u2="&#xaa;" k="191" />
    <hkern u1="&#xc2;" u2="&#xa9;" k="51" />
    <hkern u1="&#xc2;" u2="y" k="91" />
    <hkern u1="&#xc2;" u2="v" k="91" />
    <hkern u1="&#xc2;" u2="\" k="169" />
    <hkern u1="&#xc2;" u2="Y" k="182" />
    <hkern u1="&#xc2;" u2="W" k="102" />
    <hkern u1="&#xc2;" u2="V" k="169" />
    <hkern u1="&#xc2;" u2="U" k="52" />
    <hkern u1="&#xc2;" u2="T" k="147" />
    <hkern u1="&#xc2;" u2="Q" k="51" />
    <hkern u1="&#xc2;" u2="O" k="51" />
    <hkern u1="&#xc2;" u2="J" k="-56" />
    <hkern u1="&#xc2;" u2="G" k="51" />
    <hkern u1="&#xc2;" u2="C" k="51" />
    <hkern u1="&#xc2;" u2="&#x40;" k="51" />
    <hkern u1="&#xc2;" u2="&#x3f;" k="63" />
    <hkern u1="&#xc2;" u2="&#x2d;" k="67" />
    <hkern u1="&#xc2;" u2="&#x2a;" k="191" />
    <hkern u1="&#xc2;" u2="&#x27;" k="191" />
    <hkern u1="&#xc2;" u2="&#x22;" k="191" />
    <hkern u1="&#xc3;" u2="&#x2122;" k="191" />
    <hkern u1="&#xc3;" u2="&#x203a;" k="67" />
    <hkern u1="&#xc3;" u2="&#x2039;" k="67" />
    <hkern u1="&#xc3;" u2="&#x2022;" k="67" />
    <hkern u1="&#xc3;" u2="&#x201d;" k="191" />
    <hkern u1="&#xc3;" u2="&#x201c;" k="191" />
    <hkern u1="&#xc3;" u2="&#x2019;" k="191" />
    <hkern u1="&#xc3;" u2="&#x2018;" k="191" />
    <hkern u1="&#xc3;" u2="&#x2014;" k="67" />
    <hkern u1="&#xc3;" u2="&#x2013;" k="67" />
    <hkern u1="&#xc3;" u2="&#x178;" k="182" />
    <hkern u1="&#xc3;" u2="&#x152;" k="51" />
    <hkern u1="&#xc3;" u2="&#x106;" k="51" />
    <hkern u1="&#xc3;" u2="&#xff;" k="91" />
    <hkern u1="&#xc3;" u2="&#xfd;" k="91" />
    <hkern u1="&#xc3;" u2="&#xdd;" k="182" />
    <hkern u1="&#xc3;" u2="&#xdc;" k="52" />
    <hkern u1="&#xc3;" u2="&#xdb;" k="52" />
    <hkern u1="&#xc3;" u2="&#xda;" k="52" />
    <hkern u1="&#xc3;" u2="&#xd9;" k="52" />
    <hkern u1="&#xc3;" u2="&#xd8;" k="51" />
    <hkern u1="&#xc3;" u2="&#xd6;" k="51" />
    <hkern u1="&#xc3;" u2="&#xd5;" k="51" />
    <hkern u1="&#xc3;" u2="&#xd4;" k="51" />
    <hkern u1="&#xc3;" u2="&#xd3;" k="51" />
    <hkern u1="&#xc3;" u2="&#xd2;" k="51" />
    <hkern u1="&#xc3;" u2="&#xc7;" k="51" />
    <hkern u1="&#xc3;" u2="&#xbb;" k="67" />
    <hkern u1="&#xc3;" u2="&#xba;" k="191" />
    <hkern u1="&#xc3;" u2="&#xb9;" k="202" />
    <hkern u1="&#xc3;" u2="&#xb7;" k="67" />
    <hkern u1="&#xc3;" u2="&#xb3;" k="202" />
    <hkern u1="&#xc3;" u2="&#xb2;" k="202" />
    <hkern u1="&#xc3;" u2="&#xb0;" k="191" />
    <hkern u1="&#xc3;" u2="&#xae;" k="51" />
    <hkern u1="&#xc3;" u2="&#xad;" k="67" />
    <hkern u1="&#xc3;" u2="&#xab;" k="67" />
    <hkern u1="&#xc3;" u2="&#xaa;" k="191" />
    <hkern u1="&#xc3;" u2="&#xa9;" k="51" />
    <hkern u1="&#xc3;" u2="y" k="91" />
    <hkern u1="&#xc3;" u2="v" k="91" />
    <hkern u1="&#xc3;" u2="\" k="169" />
    <hkern u1="&#xc3;" u2="Y" k="182" />
    <hkern u1="&#xc3;" u2="W" k="102" />
    <hkern u1="&#xc3;" u2="V" k="169" />
    <hkern u1="&#xc3;" u2="U" k="52" />
    <hkern u1="&#xc3;" u2="T" k="147" />
    <hkern u1="&#xc3;" u2="Q" k="51" />
    <hkern u1="&#xc3;" u2="O" k="51" />
    <hkern u1="&#xc3;" u2="J" k="-56" />
    <hkern u1="&#xc3;" u2="G" k="51" />
    <hkern u1="&#xc3;" u2="C" k="51" />
    <hkern u1="&#xc3;" u2="&#x40;" k="51" />
    <hkern u1="&#xc3;" u2="&#x3f;" k="63" />
    <hkern u1="&#xc3;" u2="&#x2d;" k="67" />
    <hkern u1="&#xc3;" u2="&#x2a;" k="191" />
    <hkern u1="&#xc3;" u2="&#x27;" k="191" />
    <hkern u1="&#xc3;" u2="&#x22;" k="191" />
    <hkern u1="&#xc4;" u2="&#x2122;" k="191" />
    <hkern u1="&#xc4;" u2="&#x203a;" k="67" />
    <hkern u1="&#xc4;" u2="&#x2039;" k="67" />
    <hkern u1="&#xc4;" u2="&#x2022;" k="67" />
    <hkern u1="&#xc4;" u2="&#x201d;" k="191" />
    <hkern u1="&#xc4;" u2="&#x201c;" k="191" />
    <hkern u1="&#xc4;" u2="&#x2019;" k="191" />
    <hkern u1="&#xc4;" u2="&#x2018;" k="191" />
    <hkern u1="&#xc4;" u2="&#x2014;" k="67" />
    <hkern u1="&#xc4;" u2="&#x2013;" k="67" />
    <hkern u1="&#xc4;" u2="&#x178;" k="182" />
    <hkern u1="&#xc4;" u2="&#x152;" k="51" />
    <hkern u1="&#xc4;" u2="&#x106;" k="51" />
    <hkern u1="&#xc4;" u2="&#xff;" k="91" />
    <hkern u1="&#xc4;" u2="&#xfd;" k="91" />
    <hkern u1="&#xc4;" u2="&#xdd;" k="182" />
    <hkern u1="&#xc4;" u2="&#xdc;" k="52" />
    <hkern u1="&#xc4;" u2="&#xdb;" k="52" />
    <hkern u1="&#xc4;" u2="&#xda;" k="52" />
    <hkern u1="&#xc4;" u2="&#xd9;" k="52" />
    <hkern u1="&#xc4;" u2="&#xd8;" k="51" />
    <hkern u1="&#xc4;" u2="&#xd6;" k="51" />
    <hkern u1="&#xc4;" u2="&#xd5;" k="51" />
    <hkern u1="&#xc4;" u2="&#xd4;" k="51" />
    <hkern u1="&#xc4;" u2="&#xd3;" k="51" />
    <hkern u1="&#xc4;" u2="&#xd2;" k="51" />
    <hkern u1="&#xc4;" u2="&#xc7;" k="51" />
    <hkern u1="&#xc4;" u2="&#xbb;" k="67" />
    <hkern u1="&#xc4;" u2="&#xba;" k="191" />
    <hkern u1="&#xc4;" u2="&#xb9;" k="202" />
    <hkern u1="&#xc4;" u2="&#xb7;" k="67" />
    <hkern u1="&#xc4;" u2="&#xb3;" k="202" />
    <hkern u1="&#xc4;" u2="&#xb2;" k="202" />
    <hkern u1="&#xc4;" u2="&#xb0;" k="191" />
    <hkern u1="&#xc4;" u2="&#xae;" k="51" />
    <hkern u1="&#xc4;" u2="&#xad;" k="67" />
    <hkern u1="&#xc4;" u2="&#xab;" k="67" />
    <hkern u1="&#xc4;" u2="&#xaa;" k="191" />
    <hkern u1="&#xc4;" u2="&#xa9;" k="51" />
    <hkern u1="&#xc4;" u2="y" k="91" />
    <hkern u1="&#xc4;" u2="v" k="91" />
    <hkern u1="&#xc4;" u2="\" k="169" />
    <hkern u1="&#xc4;" u2="Y" k="182" />
    <hkern u1="&#xc4;" u2="W" k="102" />
    <hkern u1="&#xc4;" u2="V" k="169" />
    <hkern u1="&#xc4;" u2="U" k="52" />
    <hkern u1="&#xc4;" u2="T" k="147" />
    <hkern u1="&#xc4;" u2="Q" k="51" />
    <hkern u1="&#xc4;" u2="O" k="51" />
    <hkern u1="&#xc4;" u2="J" k="-56" />
    <hkern u1="&#xc4;" u2="G" k="51" />
    <hkern u1="&#xc4;" u2="C" k="51" />
    <hkern u1="&#xc4;" u2="&#x40;" k="51" />
    <hkern u1="&#xc4;" u2="&#x3f;" k="63" />
    <hkern u1="&#xc4;" u2="&#x2d;" k="67" />
    <hkern u1="&#xc4;" u2="&#x2a;" k="191" />
    <hkern u1="&#xc4;" u2="&#x27;" k="191" />
    <hkern u1="&#xc4;" u2="&#x22;" k="191" />
    <hkern u1="&#xc5;" u2="&#x2122;" k="191" />
    <hkern u1="&#xc5;" u2="&#x203a;" k="67" />
    <hkern u1="&#xc5;" u2="&#x2039;" k="67" />
    <hkern u1="&#xc5;" u2="&#x2022;" k="67" />
    <hkern u1="&#xc5;" u2="&#x201d;" k="191" />
    <hkern u1="&#xc5;" u2="&#x201c;" k="191" />
    <hkern u1="&#xc5;" u2="&#x2019;" k="191" />
    <hkern u1="&#xc5;" u2="&#x2018;" k="191" />
    <hkern u1="&#xc5;" u2="&#x2014;" k="67" />
    <hkern u1="&#xc5;" u2="&#x2013;" k="67" />
    <hkern u1="&#xc5;" u2="&#x178;" k="182" />
    <hkern u1="&#xc5;" u2="&#x152;" k="51" />
    <hkern u1="&#xc5;" u2="&#x106;" k="51" />
    <hkern u1="&#xc5;" u2="&#xff;" k="91" />
    <hkern u1="&#xc5;" u2="&#xfd;" k="91" />
    <hkern u1="&#xc5;" u2="&#xdd;" k="182" />
    <hkern u1="&#xc5;" u2="&#xdc;" k="52" />
    <hkern u1="&#xc5;" u2="&#xdb;" k="52" />
    <hkern u1="&#xc5;" u2="&#xda;" k="52" />
    <hkern u1="&#xc5;" u2="&#xd9;" k="52" />
    <hkern u1="&#xc5;" u2="&#xd8;" k="51" />
    <hkern u1="&#xc5;" u2="&#xd6;" k="51" />
    <hkern u1="&#xc5;" u2="&#xd5;" k="51" />
    <hkern u1="&#xc5;" u2="&#xd4;" k="51" />
    <hkern u1="&#xc5;" u2="&#xd3;" k="51" />
    <hkern u1="&#xc5;" u2="&#xd2;" k="51" />
    <hkern u1="&#xc5;" u2="&#xc7;" k="51" />
    <hkern u1="&#xc5;" u2="&#xbb;" k="67" />
    <hkern u1="&#xc5;" u2="&#xba;" k="191" />
    <hkern u1="&#xc5;" u2="&#xb9;" k="202" />
    <hkern u1="&#xc5;" u2="&#xb7;" k="67" />
    <hkern u1="&#xc5;" u2="&#xb3;" k="202" />
    <hkern u1="&#xc5;" u2="&#xb2;" k="202" />
    <hkern u1="&#xc5;" u2="&#xb0;" k="191" />
    <hkern u1="&#xc5;" u2="&#xae;" k="51" />
    <hkern u1="&#xc5;" u2="&#xad;" k="67" />
    <hkern u1="&#xc5;" u2="&#xab;" k="67" />
    <hkern u1="&#xc5;" u2="&#xaa;" k="191" />
    <hkern u1="&#xc5;" u2="&#xa9;" k="51" />
    <hkern u1="&#xc5;" u2="y" k="91" />
    <hkern u1="&#xc5;" u2="v" k="91" />
    <hkern u1="&#xc5;" u2="\" k="169" />
    <hkern u1="&#xc5;" u2="Y" k="182" />
    <hkern u1="&#xc5;" u2="W" k="102" />
    <hkern u1="&#xc5;" u2="V" k="169" />
    <hkern u1="&#xc5;" u2="U" k="52" />
    <hkern u1="&#xc5;" u2="T" k="147" />
    <hkern u1="&#xc5;" u2="Q" k="51" />
    <hkern u1="&#xc5;" u2="O" k="51" />
    <hkern u1="&#xc5;" u2="J" k="-56" />
    <hkern u1="&#xc5;" u2="G" k="51" />
    <hkern u1="&#xc5;" u2="C" k="51" />
    <hkern u1="&#xc5;" u2="&#x40;" k="51" />
    <hkern u1="&#xc5;" u2="&#x3f;" k="63" />
    <hkern u1="&#xc5;" u2="&#x2d;" k="67" />
    <hkern u1="&#xc5;" u2="&#x2a;" k="191" />
    <hkern u1="&#xc5;" u2="&#x27;" k="191" />
    <hkern u1="&#xc5;" u2="&#x22;" k="191" />
    <hkern u1="&#xc7;" u2="&#x203a;" k="144" />
    <hkern u1="&#xc7;" u2="&#x2039;" k="144" />
    <hkern u1="&#xc7;" u2="&#x2022;" k="144" />
    <hkern u1="&#xc7;" u2="&#x2014;" k="144" />
    <hkern u1="&#xc7;" u2="&#x2013;" k="144" />
    <hkern u1="&#xc7;" u2="&#xbb;" k="144" />
    <hkern u1="&#xc7;" u2="&#xb7;" k="144" />
    <hkern u1="&#xc7;" u2="&#xad;" k="144" />
    <hkern u1="&#xc7;" u2="&#xab;" k="144" />
    <hkern u1="&#xc7;" u2="&#x2d;" k="144" />
    <hkern u1="&#xd0;" u2="&#x2206;" k="51" />
    <hkern u1="&#xd0;" u2="&#x2122;" k="42" />
    <hkern u1="&#xd0;" u2="&#x201e;" k="52" />
    <hkern u1="&#xd0;" u2="&#x201d;" k="42" />
    <hkern u1="&#xd0;" u2="&#x201c;" k="42" />
    <hkern u1="&#xd0;" u2="&#x201a;" k="52" />
    <hkern u1="&#xd0;" u2="&#x2019;" k="42" />
    <hkern u1="&#xd0;" u2="&#x2018;" k="42" />
    <hkern u1="&#xd0;" u2="&#x17d;" k="64" />
    <hkern u1="&#xd0;" u2="&#x17b;" k="64" />
    <hkern u1="&#xd0;" u2="&#x179;" k="64" />
    <hkern u1="&#xd0;" u2="&#x178;" k="80" />
    <hkern u1="&#xd0;" u2="&#x104;" k="51" />
    <hkern u1="&#xd0;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd0;" u2="&#xc6;" k="51" />
    <hkern u1="&#xd0;" u2="&#xc5;" k="51" />
    <hkern u1="&#xd0;" u2="&#xc4;" k="51" />
    <hkern u1="&#xd0;" u2="&#xc3;" k="51" />
    <hkern u1="&#xd0;" u2="&#xc2;" k="51" />
    <hkern u1="&#xd0;" u2="&#xc1;" k="51" />
    <hkern u1="&#xd0;" u2="&#xc0;" k="51" />
    <hkern u1="&#xd0;" u2="&#xba;" k="42" />
    <hkern u1="&#xd0;" u2="&#xb0;" k="42" />
    <hkern u1="&#xd0;" u2="&#xaa;" k="42" />
    <hkern u1="&#xd0;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd0;" u2="]" k="40" />
    <hkern u1="&#xd0;" u2="\" k="56" />
    <hkern u1="&#xd0;" u2="Z" k="64" />
    <hkern u1="&#xd0;" u2="Y" k="80" />
    <hkern u1="&#xd0;" u2="X" k="30" />
    <hkern u1="&#xd0;" u2="V" k="56" />
    <hkern u1="&#xd0;" u2="T" k="78" />
    <hkern u1="&#xd0;" u2="A" k="51" />
    <hkern u1="&#xd0;" u2="&#x2f;" k="51" />
    <hkern u1="&#xd0;" u2="&#x2e;" k="52" />
    <hkern u1="&#xd0;" u2="&#x2c;" k="52" />
    <hkern u1="&#xd0;" u2="&#x2a;" k="42" />
    <hkern u1="&#xd0;" u2="&#x29;" k="40" />
    <hkern u1="&#xd0;" u2="&#x27;" k="42" />
    <hkern u1="&#xd0;" u2="&#x26;" k="51" />
    <hkern u1="&#xd0;" u2="&#x22;" k="42" />
    <hkern u1="&#xd2;" u2="&#x2206;" k="51" />
    <hkern u1="&#xd2;" u2="&#x2122;" k="42" />
    <hkern u1="&#xd2;" u2="&#x201e;" k="52" />
    <hkern u1="&#xd2;" u2="&#x201d;" k="42" />
    <hkern u1="&#xd2;" u2="&#x201c;" k="42" />
    <hkern u1="&#xd2;" u2="&#x201a;" k="52" />
    <hkern u1="&#xd2;" u2="&#x2019;" k="42" />
    <hkern u1="&#xd2;" u2="&#x2018;" k="42" />
    <hkern u1="&#xd2;" u2="&#x17d;" k="64" />
    <hkern u1="&#xd2;" u2="&#x17b;" k="64" />
    <hkern u1="&#xd2;" u2="&#x179;" k="64" />
    <hkern u1="&#xd2;" u2="&#x178;" k="80" />
    <hkern u1="&#xd2;" u2="&#x104;" k="51" />
    <hkern u1="&#xd2;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd2;" u2="&#xc6;" k="51" />
    <hkern u1="&#xd2;" u2="&#xc5;" k="51" />
    <hkern u1="&#xd2;" u2="&#xc4;" k="51" />
    <hkern u1="&#xd2;" u2="&#xc3;" k="51" />
    <hkern u1="&#xd2;" u2="&#xc2;" k="51" />
    <hkern u1="&#xd2;" u2="&#xc1;" k="51" />
    <hkern u1="&#xd2;" u2="&#xc0;" k="51" />
    <hkern u1="&#xd2;" u2="&#xba;" k="42" />
    <hkern u1="&#xd2;" u2="&#xb0;" k="42" />
    <hkern u1="&#xd2;" u2="&#xaa;" k="42" />
    <hkern u1="&#xd2;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd2;" u2="]" k="40" />
    <hkern u1="&#xd2;" u2="\" k="56" />
    <hkern u1="&#xd2;" u2="Z" k="64" />
    <hkern u1="&#xd2;" u2="Y" k="80" />
    <hkern u1="&#xd2;" u2="X" k="30" />
    <hkern u1="&#xd2;" u2="V" k="56" />
    <hkern u1="&#xd2;" u2="T" k="78" />
    <hkern u1="&#xd2;" u2="A" k="51" />
    <hkern u1="&#xd2;" u2="&#x2f;" k="51" />
    <hkern u1="&#xd2;" u2="&#x2e;" k="52" />
    <hkern u1="&#xd2;" u2="&#x2c;" k="52" />
    <hkern u1="&#xd2;" u2="&#x2a;" k="42" />
    <hkern u1="&#xd2;" u2="&#x29;" k="40" />
    <hkern u1="&#xd2;" u2="&#x27;" k="42" />
    <hkern u1="&#xd2;" u2="&#x26;" k="51" />
    <hkern u1="&#xd2;" u2="&#x22;" k="42" />
    <hkern u1="&#xd3;" u2="&#x2206;" k="51" />
    <hkern u1="&#xd3;" u2="&#x2122;" k="42" />
    <hkern u1="&#xd3;" u2="&#x201e;" k="52" />
    <hkern u1="&#xd3;" u2="&#x201d;" k="42" />
    <hkern u1="&#xd3;" u2="&#x201c;" k="42" />
    <hkern u1="&#xd3;" u2="&#x201a;" k="52" />
    <hkern u1="&#xd3;" u2="&#x2019;" k="42" />
    <hkern u1="&#xd3;" u2="&#x2018;" k="42" />
    <hkern u1="&#xd3;" u2="&#x17d;" k="64" />
    <hkern u1="&#xd3;" u2="&#x17b;" k="64" />
    <hkern u1="&#xd3;" u2="&#x179;" k="64" />
    <hkern u1="&#xd3;" u2="&#x178;" k="80" />
    <hkern u1="&#xd3;" u2="&#x104;" k="51" />
    <hkern u1="&#xd3;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd3;" u2="&#xc6;" k="51" />
    <hkern u1="&#xd3;" u2="&#xc5;" k="51" />
    <hkern u1="&#xd3;" u2="&#xc4;" k="51" />
    <hkern u1="&#xd3;" u2="&#xc3;" k="51" />
    <hkern u1="&#xd3;" u2="&#xc2;" k="51" />
    <hkern u1="&#xd3;" u2="&#xc1;" k="51" />
    <hkern u1="&#xd3;" u2="&#xc0;" k="51" />
    <hkern u1="&#xd3;" u2="&#xba;" k="42" />
    <hkern u1="&#xd3;" u2="&#xb0;" k="42" />
    <hkern u1="&#xd3;" u2="&#xaa;" k="42" />
    <hkern u1="&#xd3;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd3;" u2="]" k="40" />
    <hkern u1="&#xd3;" u2="\" k="56" />
    <hkern u1="&#xd3;" u2="Z" k="64" />
    <hkern u1="&#xd3;" u2="Y" k="80" />
    <hkern u1="&#xd3;" u2="X" k="30" />
    <hkern u1="&#xd3;" u2="V" k="56" />
    <hkern u1="&#xd3;" u2="T" k="78" />
    <hkern u1="&#xd3;" u2="A" k="51" />
    <hkern u1="&#xd3;" u2="&#x2f;" k="51" />
    <hkern u1="&#xd3;" u2="&#x2e;" k="52" />
    <hkern u1="&#xd3;" u2="&#x2c;" k="52" />
    <hkern u1="&#xd3;" u2="&#x2a;" k="42" />
    <hkern u1="&#xd3;" u2="&#x29;" k="40" />
    <hkern u1="&#xd3;" u2="&#x27;" k="42" />
    <hkern u1="&#xd3;" u2="&#x26;" k="51" />
    <hkern u1="&#xd3;" u2="&#x22;" k="42" />
    <hkern u1="&#xd4;" u2="&#x2206;" k="51" />
    <hkern u1="&#xd4;" u2="&#x2122;" k="42" />
    <hkern u1="&#xd4;" u2="&#x201e;" k="52" />
    <hkern u1="&#xd4;" u2="&#x201d;" k="42" />
    <hkern u1="&#xd4;" u2="&#x201c;" k="42" />
    <hkern u1="&#xd4;" u2="&#x201a;" k="52" />
    <hkern u1="&#xd4;" u2="&#x2019;" k="42" />
    <hkern u1="&#xd4;" u2="&#x2018;" k="42" />
    <hkern u1="&#xd4;" u2="&#x17d;" k="64" />
    <hkern u1="&#xd4;" u2="&#x17b;" k="64" />
    <hkern u1="&#xd4;" u2="&#x179;" k="64" />
    <hkern u1="&#xd4;" u2="&#x178;" k="80" />
    <hkern u1="&#xd4;" u2="&#x104;" k="51" />
    <hkern u1="&#xd4;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd4;" u2="&#xc6;" k="51" />
    <hkern u1="&#xd4;" u2="&#xc5;" k="51" />
    <hkern u1="&#xd4;" u2="&#xc4;" k="51" />
    <hkern u1="&#xd4;" u2="&#xc3;" k="51" />
    <hkern u1="&#xd4;" u2="&#xc2;" k="51" />
    <hkern u1="&#xd4;" u2="&#xc1;" k="51" />
    <hkern u1="&#xd4;" u2="&#xc0;" k="51" />
    <hkern u1="&#xd4;" u2="&#xba;" k="42" />
    <hkern u1="&#xd4;" u2="&#xb0;" k="42" />
    <hkern u1="&#xd4;" u2="&#xaa;" k="42" />
    <hkern u1="&#xd4;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd4;" u2="]" k="40" />
    <hkern u1="&#xd4;" u2="\" k="56" />
    <hkern u1="&#xd4;" u2="Z" k="64" />
    <hkern u1="&#xd4;" u2="Y" k="80" />
    <hkern u1="&#xd4;" u2="X" k="30" />
    <hkern u1="&#xd4;" u2="V" k="56" />
    <hkern u1="&#xd4;" u2="T" k="78" />
    <hkern u1="&#xd4;" u2="A" k="51" />
    <hkern u1="&#xd4;" u2="&#x2f;" k="51" />
    <hkern u1="&#xd4;" u2="&#x2e;" k="52" />
    <hkern u1="&#xd4;" u2="&#x2c;" k="52" />
    <hkern u1="&#xd4;" u2="&#x2a;" k="42" />
    <hkern u1="&#xd4;" u2="&#x29;" k="40" />
    <hkern u1="&#xd4;" u2="&#x27;" k="42" />
    <hkern u1="&#xd4;" u2="&#x26;" k="51" />
    <hkern u1="&#xd4;" u2="&#x22;" k="42" />
    <hkern u1="&#xd5;" u2="&#x2206;" k="51" />
    <hkern u1="&#xd5;" u2="&#x2122;" k="42" />
    <hkern u1="&#xd5;" u2="&#x201e;" k="52" />
    <hkern u1="&#xd5;" u2="&#x201d;" k="42" />
    <hkern u1="&#xd5;" u2="&#x201c;" k="42" />
    <hkern u1="&#xd5;" u2="&#x201a;" k="52" />
    <hkern u1="&#xd5;" u2="&#x2019;" k="42" />
    <hkern u1="&#xd5;" u2="&#x2018;" k="42" />
    <hkern u1="&#xd5;" u2="&#x17d;" k="64" />
    <hkern u1="&#xd5;" u2="&#x17b;" k="64" />
    <hkern u1="&#xd5;" u2="&#x179;" k="64" />
    <hkern u1="&#xd5;" u2="&#x178;" k="80" />
    <hkern u1="&#xd5;" u2="&#x104;" k="51" />
    <hkern u1="&#xd5;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd5;" u2="&#xc6;" k="51" />
    <hkern u1="&#xd5;" u2="&#xc5;" k="51" />
    <hkern u1="&#xd5;" u2="&#xc4;" k="51" />
    <hkern u1="&#xd5;" u2="&#xc3;" k="51" />
    <hkern u1="&#xd5;" u2="&#xc2;" k="51" />
    <hkern u1="&#xd5;" u2="&#xc1;" k="51" />
    <hkern u1="&#xd5;" u2="&#xc0;" k="51" />
    <hkern u1="&#xd5;" u2="&#xba;" k="42" />
    <hkern u1="&#xd5;" u2="&#xb0;" k="42" />
    <hkern u1="&#xd5;" u2="&#xaa;" k="42" />
    <hkern u1="&#xd5;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd5;" u2="]" k="40" />
    <hkern u1="&#xd5;" u2="\" k="56" />
    <hkern u1="&#xd5;" u2="Z" k="64" />
    <hkern u1="&#xd5;" u2="Y" k="80" />
    <hkern u1="&#xd5;" u2="X" k="30" />
    <hkern u1="&#xd5;" u2="V" k="56" />
    <hkern u1="&#xd5;" u2="T" k="78" />
    <hkern u1="&#xd5;" u2="A" k="51" />
    <hkern u1="&#xd5;" u2="&#x2f;" k="51" />
    <hkern u1="&#xd5;" u2="&#x2e;" k="52" />
    <hkern u1="&#xd5;" u2="&#x2c;" k="52" />
    <hkern u1="&#xd5;" u2="&#x2a;" k="42" />
    <hkern u1="&#xd5;" u2="&#x29;" k="40" />
    <hkern u1="&#xd5;" u2="&#x27;" k="42" />
    <hkern u1="&#xd5;" u2="&#x26;" k="51" />
    <hkern u1="&#xd5;" u2="&#x22;" k="42" />
    <hkern u1="&#xd6;" u2="&#x2206;" k="51" />
    <hkern u1="&#xd6;" u2="&#x2122;" k="42" />
    <hkern u1="&#xd6;" u2="&#x201e;" k="52" />
    <hkern u1="&#xd6;" u2="&#x201d;" k="42" />
    <hkern u1="&#xd6;" u2="&#x201c;" k="42" />
    <hkern u1="&#xd6;" u2="&#x201a;" k="52" />
    <hkern u1="&#xd6;" u2="&#x2019;" k="42" />
    <hkern u1="&#xd6;" u2="&#x2018;" k="42" />
    <hkern u1="&#xd6;" u2="&#x17d;" k="64" />
    <hkern u1="&#xd6;" u2="&#x17b;" k="64" />
    <hkern u1="&#xd6;" u2="&#x179;" k="64" />
    <hkern u1="&#xd6;" u2="&#x178;" k="80" />
    <hkern u1="&#xd6;" u2="&#x104;" k="51" />
    <hkern u1="&#xd6;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd6;" u2="&#xc6;" k="51" />
    <hkern u1="&#xd6;" u2="&#xc5;" k="51" />
    <hkern u1="&#xd6;" u2="&#xc4;" k="51" />
    <hkern u1="&#xd6;" u2="&#xc3;" k="51" />
    <hkern u1="&#xd6;" u2="&#xc2;" k="51" />
    <hkern u1="&#xd6;" u2="&#xc1;" k="51" />
    <hkern u1="&#xd6;" u2="&#xc0;" k="51" />
    <hkern u1="&#xd6;" u2="&#xba;" k="42" />
    <hkern u1="&#xd6;" u2="&#xb0;" k="42" />
    <hkern u1="&#xd6;" u2="&#xaa;" k="42" />
    <hkern u1="&#xd6;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd6;" u2="]" k="40" />
    <hkern u1="&#xd6;" u2="\" k="56" />
    <hkern u1="&#xd6;" u2="Z" k="64" />
    <hkern u1="&#xd6;" u2="Y" k="80" />
    <hkern u1="&#xd6;" u2="X" k="30" />
    <hkern u1="&#xd6;" u2="V" k="56" />
    <hkern u1="&#xd6;" u2="T" k="78" />
    <hkern u1="&#xd6;" u2="A" k="51" />
    <hkern u1="&#xd6;" u2="&#x2f;" k="51" />
    <hkern u1="&#xd6;" u2="&#x2e;" k="52" />
    <hkern u1="&#xd6;" u2="&#x2c;" k="52" />
    <hkern u1="&#xd6;" u2="&#x2a;" k="42" />
    <hkern u1="&#xd6;" u2="&#x29;" k="40" />
    <hkern u1="&#xd6;" u2="&#x27;" k="42" />
    <hkern u1="&#xd6;" u2="&#x26;" k="51" />
    <hkern u1="&#xd6;" u2="&#x22;" k="42" />
    <hkern u1="&#xd8;" u2="&#x2206;" k="51" />
    <hkern u1="&#xd8;" u2="&#x2122;" k="42" />
    <hkern u1="&#xd8;" u2="&#x201e;" k="52" />
    <hkern u1="&#xd8;" u2="&#x201d;" k="42" />
    <hkern u1="&#xd8;" u2="&#x201c;" k="42" />
    <hkern u1="&#xd8;" u2="&#x201a;" k="52" />
    <hkern u1="&#xd8;" u2="&#x2019;" k="42" />
    <hkern u1="&#xd8;" u2="&#x2018;" k="42" />
    <hkern u1="&#xd8;" u2="&#x17d;" k="64" />
    <hkern u1="&#xd8;" u2="&#x17b;" k="64" />
    <hkern u1="&#xd8;" u2="&#x179;" k="64" />
    <hkern u1="&#xd8;" u2="&#x178;" k="80" />
    <hkern u1="&#xd8;" u2="&#x104;" k="51" />
    <hkern u1="&#xd8;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd8;" u2="&#xc6;" k="51" />
    <hkern u1="&#xd8;" u2="&#xc5;" k="51" />
    <hkern u1="&#xd8;" u2="&#xc4;" k="51" />
    <hkern u1="&#xd8;" u2="&#xc3;" k="51" />
    <hkern u1="&#xd8;" u2="&#xc2;" k="51" />
    <hkern u1="&#xd8;" u2="&#xc1;" k="51" />
    <hkern u1="&#xd8;" u2="&#xc0;" k="51" />
    <hkern u1="&#xd8;" u2="&#xba;" k="42" />
    <hkern u1="&#xd8;" u2="&#xb0;" k="42" />
    <hkern u1="&#xd8;" u2="&#xaa;" k="42" />
    <hkern u1="&#xd8;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd8;" u2="]" k="40" />
    <hkern u1="&#xd8;" u2="\" k="56" />
    <hkern u1="&#xd8;" u2="Z" k="64" />
    <hkern u1="&#xd8;" u2="Y" k="80" />
    <hkern u1="&#xd8;" u2="X" k="30" />
    <hkern u1="&#xd8;" u2="V" k="56" />
    <hkern u1="&#xd8;" u2="T" k="78" />
    <hkern u1="&#xd8;" u2="A" k="51" />
    <hkern u1="&#xd8;" u2="&#x2f;" k="51" />
    <hkern u1="&#xd8;" u2="&#x2e;" k="52" />
    <hkern u1="&#xd8;" u2="&#x2c;" k="52" />
    <hkern u1="&#xd8;" u2="&#x2a;" k="42" />
    <hkern u1="&#xd8;" u2="&#x29;" k="40" />
    <hkern u1="&#xd8;" u2="&#x27;" k="42" />
    <hkern u1="&#xd8;" u2="&#x26;" k="51" />
    <hkern u1="&#xd8;" u2="&#x22;" k="42" />
    <hkern u1="&#xd9;" u2="&#x2206;" k="52" />
    <hkern u1="&#xd9;" u2="&#x201e;" k="50" />
    <hkern u1="&#xd9;" u2="&#x201a;" k="50" />
    <hkern u1="&#xd9;" u2="&#x104;" k="52" />
    <hkern u1="&#xd9;" u2="&#xc6;" k="52" />
    <hkern u1="&#xd9;" u2="&#xc5;" k="52" />
    <hkern u1="&#xd9;" u2="&#xc4;" k="52" />
    <hkern u1="&#xd9;" u2="&#xc3;" k="52" />
    <hkern u1="&#xd9;" u2="&#xc2;" k="52" />
    <hkern u1="&#xd9;" u2="&#xc1;" k="52" />
    <hkern u1="&#xd9;" u2="&#xc0;" k="52" />
    <hkern u1="&#xd9;" u2="A" k="52" />
    <hkern u1="&#xd9;" u2="&#x2f;" k="52" />
    <hkern u1="&#xd9;" u2="&#x2e;" k="50" />
    <hkern u1="&#xd9;" u2="&#x2c;" k="50" />
    <hkern u1="&#xd9;" u2="&#x26;" k="52" />
    <hkern u1="&#xda;" u2="&#x2206;" k="52" />
    <hkern u1="&#xda;" u2="&#x201e;" k="50" />
    <hkern u1="&#xda;" u2="&#x201a;" k="50" />
    <hkern u1="&#xda;" u2="&#x104;" k="52" />
    <hkern u1="&#xda;" u2="&#xc6;" k="52" />
    <hkern u1="&#xda;" u2="&#xc5;" k="52" />
    <hkern u1="&#xda;" u2="&#xc4;" k="52" />
    <hkern u1="&#xda;" u2="&#xc3;" k="52" />
    <hkern u1="&#xda;" u2="&#xc2;" k="52" />
    <hkern u1="&#xda;" u2="&#xc1;" k="52" />
    <hkern u1="&#xda;" u2="&#xc0;" k="52" />
    <hkern u1="&#xda;" u2="A" k="52" />
    <hkern u1="&#xda;" u2="&#x2f;" k="52" />
    <hkern u1="&#xda;" u2="&#x2e;" k="50" />
    <hkern u1="&#xda;" u2="&#x2c;" k="50" />
    <hkern u1="&#xda;" u2="&#x26;" k="52" />
    <hkern u1="&#xdb;" u2="&#x2206;" k="52" />
    <hkern u1="&#xdb;" u2="&#x201e;" k="50" />
    <hkern u1="&#xdb;" u2="&#x201a;" k="50" />
    <hkern u1="&#xdb;" u2="&#x104;" k="52" />
    <hkern u1="&#xdb;" u2="&#xc6;" k="52" />
    <hkern u1="&#xdb;" u2="&#xc5;" k="52" />
    <hkern u1="&#xdb;" u2="&#xc4;" k="52" />
    <hkern u1="&#xdb;" u2="&#xc3;" k="52" />
    <hkern u1="&#xdb;" u2="&#xc2;" k="52" />
    <hkern u1="&#xdb;" u2="&#xc1;" k="52" />
    <hkern u1="&#xdb;" u2="&#xc0;" k="52" />
    <hkern u1="&#xdb;" u2="A" k="52" />
    <hkern u1="&#xdb;" u2="&#x2f;" k="52" />
    <hkern u1="&#xdb;" u2="&#x2e;" k="50" />
    <hkern u1="&#xdb;" u2="&#x2c;" k="50" />
    <hkern u1="&#xdb;" u2="&#x26;" k="52" />
    <hkern u1="&#xdc;" u2="&#x2206;" k="52" />
    <hkern u1="&#xdc;" u2="&#x201e;" k="50" />
    <hkern u1="&#xdc;" u2="&#x201a;" k="50" />
    <hkern u1="&#xdc;" u2="&#x104;" k="52" />
    <hkern u1="&#xdc;" u2="&#xc6;" k="52" />
    <hkern u1="&#xdc;" u2="&#xc5;" k="52" />
    <hkern u1="&#xdc;" u2="&#xc4;" k="52" />
    <hkern u1="&#xdc;" u2="&#xc3;" k="52" />
    <hkern u1="&#xdc;" u2="&#xc2;" k="52" />
    <hkern u1="&#xdc;" u2="&#xc1;" k="52" />
    <hkern u1="&#xdc;" u2="&#xc0;" k="52" />
    <hkern u1="&#xdc;" u2="A" k="52" />
    <hkern u1="&#xdc;" u2="&#x2f;" k="52" />
    <hkern u1="&#xdc;" u2="&#x2e;" k="50" />
    <hkern u1="&#xdc;" u2="&#x2c;" k="50" />
    <hkern u1="&#xdc;" u2="&#x26;" k="52" />
    <hkern u1="&#xdd;" u2="&#x2206;" k="182" />
    <hkern u1="&#xdd;" u2="&#x2122;" k="-36" />
    <hkern u1="&#xdd;" u2="&#x203a;" k="160" />
    <hkern u1="&#xdd;" u2="&#x2039;" k="160" />
    <hkern u1="&#xdd;" u2="&#x2022;" k="160" />
    <hkern u1="&#xdd;" u2="&#x201e;" k="167" />
    <hkern u1="&#xdd;" u2="&#x201d;" k="-36" />
    <hkern u1="&#xdd;" u2="&#x201c;" k="-36" />
    <hkern u1="&#xdd;" u2="&#x201a;" k="167" />
    <hkern u1="&#xdd;" u2="&#x2019;" k="-36" />
    <hkern u1="&#xdd;" u2="&#x2018;" k="-36" />
    <hkern u1="&#xdd;" u2="&#x2014;" k="160" />
    <hkern u1="&#xdd;" u2="&#x2013;" k="160" />
    <hkern u1="&#xdd;" u2="&#x161;" k="139" />
    <hkern u1="&#xdd;" u2="&#x15b;" k="139" />
    <hkern u1="&#xdd;" u2="&#x153;" k="160" />
    <hkern u1="&#xdd;" u2="&#x152;" k="80" />
    <hkern u1="&#xdd;" u2="&#x144;" k="131" />
    <hkern u1="&#xdd;" u2="&#x131;" k="131" />
    <hkern u1="&#xdd;" u2="&#x119;" k="160" />
    <hkern u1="&#xdd;" u2="&#x107;" k="160" />
    <hkern u1="&#xdd;" u2="&#x106;" k="80" />
    <hkern u1="&#xdd;" u2="&#x105;" k="145" />
    <hkern u1="&#xdd;" u2="&#x104;" k="182" />
    <hkern u1="&#xdd;" u2="&#xff;" k="100" />
    <hkern u1="&#xdd;" u2="&#xfd;" k="100" />
    <hkern u1="&#xdd;" u2="&#xfc;" k="131" />
    <hkern u1="&#xdd;" u2="&#xfb;" k="131" />
    <hkern u1="&#xdd;" u2="&#xfa;" k="131" />
    <hkern u1="&#xdd;" u2="&#xf9;" k="131" />
    <hkern u1="&#xdd;" u2="&#xf8;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf6;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf5;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf4;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf3;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf2;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf1;" k="131" />
    <hkern u1="&#xdd;" u2="&#xf0;" k="160" />
    <hkern u1="&#xdd;" u2="&#xeb;" k="160" />
    <hkern u1="&#xdd;" u2="&#xea;" k="160" />
    <hkern u1="&#xdd;" u2="&#xe9;" k="160" />
    <hkern u1="&#xdd;" u2="&#xe8;" k="160" />
    <hkern u1="&#xdd;" u2="&#xe7;" k="160" />
    <hkern u1="&#xdd;" u2="&#xe6;" k="145" />
    <hkern u1="&#xdd;" u2="&#xe5;" k="145" />
    <hkern u1="&#xdd;" u2="&#xe4;" k="145" />
    <hkern u1="&#xdd;" u2="&#xe3;" k="145" />
    <hkern u1="&#xdd;" u2="&#xe2;" k="145" />
    <hkern u1="&#xdd;" u2="&#xe1;" k="145" />
    <hkern u1="&#xdd;" u2="&#xe0;" k="145" />
    <hkern u1="&#xdd;" u2="&#xd8;" k="80" />
    <hkern u1="&#xdd;" u2="&#xd6;" k="80" />
    <hkern u1="&#xdd;" u2="&#xd5;" k="80" />
    <hkern u1="&#xdd;" u2="&#xd4;" k="80" />
    <hkern u1="&#xdd;" u2="&#xd3;" k="80" />
    <hkern u1="&#xdd;" u2="&#xd2;" k="80" />
    <hkern u1="&#xdd;" u2="&#xc7;" k="80" />
    <hkern u1="&#xdd;" u2="&#xc6;" k="182" />
    <hkern u1="&#xdd;" u2="&#xc5;" k="182" />
    <hkern u1="&#xdd;" u2="&#xc4;" k="182" />
    <hkern u1="&#xdd;" u2="&#xc3;" k="182" />
    <hkern u1="&#xdd;" u2="&#xc2;" k="182" />
    <hkern u1="&#xdd;" u2="&#xc1;" k="182" />
    <hkern u1="&#xdd;" u2="&#xc0;" k="182" />
    <hkern u1="&#xdd;" u2="&#xbb;" k="160" />
    <hkern u1="&#xdd;" u2="&#xba;" k="-36" />
    <hkern u1="&#xdd;" u2="&#xb9;" k="-56" />
    <hkern u1="&#xdd;" u2="&#xb7;" k="160" />
    <hkern u1="&#xdd;" u2="&#xb5;" k="131" />
    <hkern u1="&#xdd;" u2="&#xb3;" k="-56" />
    <hkern u1="&#xdd;" u2="&#xb2;" k="-56" />
    <hkern u1="&#xdd;" u2="&#xb0;" k="-36" />
    <hkern u1="&#xdd;" u2="&#xae;" k="80" />
    <hkern u1="&#xdd;" u2="&#xad;" k="160" />
    <hkern u1="&#xdd;" u2="&#xab;" k="160" />
    <hkern u1="&#xdd;" u2="&#xaa;" k="-36" />
    <hkern u1="&#xdd;" u2="&#xa9;" k="80" />
    <hkern u1="&#xdd;" u2="y" k="100" />
    <hkern u1="&#xdd;" u2="x" k="136" />
    <hkern u1="&#xdd;" u2="w" k="96" />
    <hkern u1="&#xdd;" u2="v" k="100" />
    <hkern u1="&#xdd;" u2="u" k="131" />
    <hkern u1="&#xdd;" u2="s" k="139" />
    <hkern u1="&#xdd;" u2="r" k="131" />
    <hkern u1="&#xdd;" u2="q" k="160" />
    <hkern u1="&#xdd;" u2="p" k="131" />
    <hkern u1="&#xdd;" u2="o" k="160" />
    <hkern u1="&#xdd;" u2="n" k="131" />
    <hkern u1="&#xdd;" u2="m" k="131" />
    <hkern u1="&#xdd;" u2="g" k="176" />
    <hkern u1="&#xdd;" u2="e" k="160" />
    <hkern u1="&#xdd;" u2="d" k="160" />
    <hkern u1="&#xdd;" u2="c" k="160" />
    <hkern u1="&#xdd;" u2="a" k="145" />
    <hkern u1="&#xdd;" u2="Q" k="80" />
    <hkern u1="&#xdd;" u2="O" k="80" />
    <hkern u1="&#xdd;" u2="J" k="200" />
    <hkern u1="&#xdd;" u2="G" k="80" />
    <hkern u1="&#xdd;" u2="C" k="80" />
    <hkern u1="&#xdd;" u2="A" k="182" />
    <hkern u1="&#xdd;" u2="&#x40;" k="80" />
    <hkern u1="&#xdd;" u2="&#x3f;" k="-32" />
    <hkern u1="&#xdd;" u2="&#x3b;" k="131" />
    <hkern u1="&#xdd;" u2="&#x3a;" k="131" />
    <hkern u1="&#xdd;" u2="&#x2f;" k="182" />
    <hkern u1="&#xdd;" u2="&#x2e;" k="167" />
    <hkern u1="&#xdd;" u2="&#x2d;" k="160" />
    <hkern u1="&#xdd;" u2="&#x2c;" k="167" />
    <hkern u1="&#xdd;" u2="&#x2a;" k="-36" />
    <hkern u1="&#xdd;" u2="&#x27;" k="-36" />
    <hkern u1="&#xdd;" u2="&#x26;" k="182" />
    <hkern u1="&#xdd;" u2="&#x22;" k="-36" />
    <hkern u1="&#xde;" u2="&#x2206;" k="51" />
    <hkern u1="&#xde;" u2="&#x2122;" k="42" />
    <hkern u1="&#xde;" u2="&#x201e;" k="52" />
    <hkern u1="&#xde;" u2="&#x201d;" k="42" />
    <hkern u1="&#xde;" u2="&#x201c;" k="42" />
    <hkern u1="&#xde;" u2="&#x201a;" k="52" />
    <hkern u1="&#xde;" u2="&#x2019;" k="42" />
    <hkern u1="&#xde;" u2="&#x2018;" k="42" />
    <hkern u1="&#xde;" u2="&#x17d;" k="64" />
    <hkern u1="&#xde;" u2="&#x17b;" k="64" />
    <hkern u1="&#xde;" u2="&#x179;" k="64" />
    <hkern u1="&#xde;" u2="&#x178;" k="80" />
    <hkern u1="&#xde;" u2="&#x104;" k="51" />
    <hkern u1="&#xde;" u2="&#xdd;" k="80" />
    <hkern u1="&#xde;" u2="&#xc6;" k="51" />
    <hkern u1="&#xde;" u2="&#xc5;" k="51" />
    <hkern u1="&#xde;" u2="&#xc4;" k="51" />
    <hkern u1="&#xde;" u2="&#xc3;" k="51" />
    <hkern u1="&#xde;" u2="&#xc2;" k="51" />
    <hkern u1="&#xde;" u2="&#xc1;" k="51" />
    <hkern u1="&#xde;" u2="&#xc0;" k="51" />
    <hkern u1="&#xde;" u2="&#xba;" k="42" />
    <hkern u1="&#xde;" u2="&#xb0;" k="42" />
    <hkern u1="&#xde;" u2="&#xaa;" k="42" />
    <hkern u1="&#xde;" u2="&#x7d;" k="40" />
    <hkern u1="&#xde;" u2="]" k="40" />
    <hkern u1="&#xde;" u2="\" k="56" />
    <hkern u1="&#xde;" u2="Z" k="64" />
    <hkern u1="&#xde;" u2="Y" k="80" />
    <hkern u1="&#xde;" u2="X" k="30" />
    <hkern u1="&#xde;" u2="V" k="56" />
    <hkern u1="&#xde;" u2="T" k="78" />
    <hkern u1="&#xde;" u2="A" k="51" />
    <hkern u1="&#xde;" u2="&#x2f;" k="51" />
    <hkern u1="&#xde;" u2="&#x2e;" k="52" />
    <hkern u1="&#xde;" u2="&#x2c;" k="52" />
    <hkern u1="&#xde;" u2="&#x2a;" k="42" />
    <hkern u1="&#xde;" u2="&#x29;" k="40" />
    <hkern u1="&#xde;" u2="&#x27;" k="42" />
    <hkern u1="&#xde;" u2="&#x26;" k="51" />
    <hkern u1="&#xde;" u2="&#x22;" k="42" />
    <hkern u1="&#xe0;" u2="&#x2122;" k="76" />
    <hkern u1="&#xe0;" u2="&#x201d;" k="76" />
    <hkern u1="&#xe0;" u2="&#x201c;" k="76" />
    <hkern u1="&#xe0;" u2="&#x2019;" k="76" />
    <hkern u1="&#xe0;" u2="&#x2018;" k="76" />
    <hkern u1="&#xe0;" u2="&#xff;" k="36" />
    <hkern u1="&#xe0;" u2="&#xfd;" k="36" />
    <hkern u1="&#xe0;" u2="&#xba;" k="76" />
    <hkern u1="&#xe0;" u2="&#xb9;" k="76" />
    <hkern u1="&#xe0;" u2="&#xb3;" k="76" />
    <hkern u1="&#xe0;" u2="&#xb2;" k="76" />
    <hkern u1="&#xe0;" u2="&#xb0;" k="76" />
    <hkern u1="&#xe0;" u2="&#xaa;" k="76" />
    <hkern u1="&#xe0;" u2="y" k="36" />
    <hkern u1="&#xe0;" u2="w" k="18" />
    <hkern u1="&#xe0;" u2="v" k="36" />
    <hkern u1="&#xe0;" u2="&#x2a;" k="76" />
    <hkern u1="&#xe0;" u2="&#x27;" k="76" />
    <hkern u1="&#xe0;" u2="&#x22;" k="76" />
    <hkern u1="&#xe1;" u2="&#x2122;" k="76" />
    <hkern u1="&#xe1;" u2="&#x201d;" k="76" />
    <hkern u1="&#xe1;" u2="&#x201c;" k="76" />
    <hkern u1="&#xe1;" u2="&#x2019;" k="76" />
    <hkern u1="&#xe1;" u2="&#x2018;" k="76" />
    <hkern u1="&#xe1;" u2="&#xff;" k="36" />
    <hkern u1="&#xe1;" u2="&#xfd;" k="36" />
    <hkern u1="&#xe1;" u2="&#xba;" k="76" />
    <hkern u1="&#xe1;" u2="&#xb9;" k="76" />
    <hkern u1="&#xe1;" u2="&#xb3;" k="76" />
    <hkern u1="&#xe1;" u2="&#xb2;" k="76" />
    <hkern u1="&#xe1;" u2="&#xb0;" k="76" />
    <hkern u1="&#xe1;" u2="&#xaa;" k="76" />
    <hkern u1="&#xe1;" u2="y" k="36" />
    <hkern u1="&#xe1;" u2="w" k="18" />
    <hkern u1="&#xe1;" u2="v" k="36" />
    <hkern u1="&#xe1;" u2="&#x2a;" k="76" />
    <hkern u1="&#xe1;" u2="&#x27;" k="76" />
    <hkern u1="&#xe1;" u2="&#x22;" k="76" />
    <hkern u1="&#xe2;" u2="&#x2122;" k="76" />
    <hkern u1="&#xe2;" u2="&#x201d;" k="76" />
    <hkern u1="&#xe2;" u2="&#x201c;" k="76" />
    <hkern u1="&#xe2;" u2="&#x2019;" k="76" />
    <hkern u1="&#xe2;" u2="&#x2018;" k="76" />
    <hkern u1="&#xe2;" u2="&#xff;" k="36" />
    <hkern u1="&#xe2;" u2="&#xfd;" k="36" />
    <hkern u1="&#xe2;" u2="&#xba;" k="76" />
    <hkern u1="&#xe2;" u2="&#xb9;" k="76" />
    <hkern u1="&#xe2;" u2="&#xb3;" k="76" />
    <hkern u1="&#xe2;" u2="&#xb2;" k="76" />
    <hkern u1="&#xe2;" u2="&#xb0;" k="76" />
    <hkern u1="&#xe2;" u2="&#xaa;" k="76" />
    <hkern u1="&#xe2;" u2="y" k="36" />
    <hkern u1="&#xe2;" u2="w" k="18" />
    <hkern u1="&#xe2;" u2="v" k="36" />
    <hkern u1="&#xe2;" u2="&#x2a;" k="76" />
    <hkern u1="&#xe2;" u2="&#x27;" k="76" />
    <hkern u1="&#xe2;" u2="&#x22;" k="76" />
    <hkern u1="&#xe3;" u2="&#x2122;" k="76" />
    <hkern u1="&#xe3;" u2="&#x201d;" k="76" />
    <hkern u1="&#xe3;" u2="&#x201c;" k="76" />
    <hkern u1="&#xe3;" u2="&#x2019;" k="76" />
    <hkern u1="&#xe3;" u2="&#x2018;" k="76" />
    <hkern u1="&#xe3;" u2="&#xff;" k="36" />
    <hkern u1="&#xe3;" u2="&#xfd;" k="36" />
    <hkern u1="&#xe3;" u2="&#xba;" k="76" />
    <hkern u1="&#xe3;" u2="&#xb9;" k="76" />
    <hkern u1="&#xe3;" u2="&#xb3;" k="76" />
    <hkern u1="&#xe3;" u2="&#xb2;" k="76" />
    <hkern u1="&#xe3;" u2="&#xb0;" k="76" />
    <hkern u1="&#xe3;" u2="&#xaa;" k="76" />
    <hkern u1="&#xe3;" u2="y" k="36" />
    <hkern u1="&#xe3;" u2="w" k="18" />
    <hkern u1="&#xe3;" u2="v" k="36" />
    <hkern u1="&#xe3;" u2="&#x2a;" k="76" />
    <hkern u1="&#xe3;" u2="&#x27;" k="76" />
    <hkern u1="&#xe3;" u2="&#x22;" k="76" />
    <hkern u1="&#xe4;" u2="&#x2122;" k="76" />
    <hkern u1="&#xe4;" u2="&#x201d;" k="76" />
    <hkern u1="&#xe4;" u2="&#x201c;" k="76" />
    <hkern u1="&#xe4;" u2="&#x2019;" k="76" />
    <hkern u1="&#xe4;" u2="&#x2018;" k="76" />
    <hkern u1="&#xe4;" u2="&#xff;" k="36" />
    <hkern u1="&#xe4;" u2="&#xfd;" k="36" />
    <hkern u1="&#xe4;" u2="&#xba;" k="76" />
    <hkern u1="&#xe4;" u2="&#xb9;" k="76" />
    <hkern u1="&#xe4;" u2="&#xb3;" k="76" />
    <hkern u1="&#xe4;" u2="&#xb2;" k="76" />
    <hkern u1="&#xe4;" u2="&#xb0;" k="76" />
    <hkern u1="&#xe4;" u2="&#xaa;" k="76" />
    <hkern u1="&#xe4;" u2="y" k="36" />
    <hkern u1="&#xe4;" u2="w" k="18" />
    <hkern u1="&#xe4;" u2="v" k="36" />
    <hkern u1="&#xe4;" u2="&#x2a;" k="76" />
    <hkern u1="&#xe4;" u2="&#x27;" k="76" />
    <hkern u1="&#xe4;" u2="&#x22;" k="76" />
    <hkern u1="&#xe5;" u2="&#x2122;" k="76" />
    <hkern u1="&#xe5;" u2="&#x201d;" k="76" />
    <hkern u1="&#xe5;" u2="&#x201c;" k="76" />
    <hkern u1="&#xe5;" u2="&#x2019;" k="76" />
    <hkern u1="&#xe5;" u2="&#x2018;" k="76" />
    <hkern u1="&#xe5;" u2="&#xff;" k="36" />
    <hkern u1="&#xe5;" u2="&#xfd;" k="36" />
    <hkern u1="&#xe5;" u2="&#xba;" k="76" />
    <hkern u1="&#xe5;" u2="&#xb9;" k="76" />
    <hkern u1="&#xe5;" u2="&#xb3;" k="76" />
    <hkern u1="&#xe5;" u2="&#xb2;" k="76" />
    <hkern u1="&#xe5;" u2="&#xb0;" k="76" />
    <hkern u1="&#xe5;" u2="&#xaa;" k="76" />
    <hkern u1="&#xe5;" u2="y" k="36" />
    <hkern u1="&#xe5;" u2="w" k="18" />
    <hkern u1="&#xe5;" u2="v" k="36" />
    <hkern u1="&#xe5;" u2="&#x2a;" k="76" />
    <hkern u1="&#xe5;" u2="&#x27;" k="76" />
    <hkern u1="&#xe5;" u2="&#x22;" k="76" />
    <hkern u1="&#xe6;" u2="&#x2122;" k="96" />
    <hkern u1="&#xe6;" u2="&#x201d;" k="96" />
    <hkern u1="&#xe6;" u2="&#x201c;" k="96" />
    <hkern u1="&#xe6;" u2="&#x2019;" k="96" />
    <hkern u1="&#xe6;" u2="&#x2018;" k="96" />
    <hkern u1="&#xe6;" u2="&#xff;" k="33" />
    <hkern u1="&#xe6;" u2="&#xfd;" k="33" />
    <hkern u1="&#xe6;" u2="&#xba;" k="96" />
    <hkern u1="&#xe6;" u2="&#xb0;" k="96" />
    <hkern u1="&#xe6;" u2="&#xaa;" k="96" />
    <hkern u1="&#xe6;" u2="&#x7d;" k="36" />
    <hkern u1="&#xe6;" u2="y" k="33" />
    <hkern u1="&#xe6;" u2="x" k="60" />
    <hkern u1="&#xe6;" u2="v" k="33" />
    <hkern u1="&#xe6;" u2="]" k="36" />
    <hkern u1="&#xe6;" u2="\" k="123" />
    <hkern u1="&#xe6;" u2="W" k="41" />
    <hkern u1="&#xe6;" u2="V" k="123" />
    <hkern u1="&#xe6;" u2="&#x2a;" k="96" />
    <hkern u1="&#xe6;" u2="&#x29;" k="36" />
    <hkern u1="&#xe6;" u2="&#x27;" k="96" />
    <hkern u1="&#xe6;" u2="&#x22;" k="96" />
    <hkern u1="&#xe8;" u2="&#x2122;" k="96" />
    <hkern u1="&#xe8;" u2="&#x201d;" k="96" />
    <hkern u1="&#xe8;" u2="&#x201c;" k="96" />
    <hkern u1="&#xe8;" u2="&#x2019;" k="96" />
    <hkern u1="&#xe8;" u2="&#x2018;" k="96" />
    <hkern u1="&#xe8;" u2="&#xff;" k="33" />
    <hkern u1="&#xe8;" u2="&#xfd;" k="33" />
    <hkern u1="&#xe8;" u2="&#xba;" k="96" />
    <hkern u1="&#xe8;" u2="&#xb0;" k="96" />
    <hkern u1="&#xe8;" u2="&#xaa;" k="96" />
    <hkern u1="&#xe8;" u2="&#x7d;" k="36" />
    <hkern u1="&#xe8;" u2="y" k="33" />
    <hkern u1="&#xe8;" u2="x" k="60" />
    <hkern u1="&#xe8;" u2="v" k="33" />
    <hkern u1="&#xe8;" u2="]" k="36" />
    <hkern u1="&#xe8;" u2="\" k="123" />
    <hkern u1="&#xe8;" u2="W" k="41" />
    <hkern u1="&#xe8;" u2="V" k="123" />
    <hkern u1="&#xe8;" u2="&#x2a;" k="96" />
    <hkern u1="&#xe8;" u2="&#x29;" k="36" />
    <hkern u1="&#xe8;" u2="&#x27;" k="96" />
    <hkern u1="&#xe8;" u2="&#x22;" k="96" />
    <hkern u1="&#xe9;" u2="&#x2122;" k="96" />
    <hkern u1="&#xe9;" u2="&#x201d;" k="96" />
    <hkern u1="&#xe9;" u2="&#x201c;" k="96" />
    <hkern u1="&#xe9;" u2="&#x2019;" k="96" />
    <hkern u1="&#xe9;" u2="&#x2018;" k="96" />
    <hkern u1="&#xe9;" u2="&#xff;" k="33" />
    <hkern u1="&#xe9;" u2="&#xfd;" k="33" />
    <hkern u1="&#xe9;" u2="&#xba;" k="96" />
    <hkern u1="&#xe9;" u2="&#xb0;" k="96" />
    <hkern u1="&#xe9;" u2="&#xaa;" k="96" />
    <hkern u1="&#xe9;" u2="&#x7d;" k="36" />
    <hkern u1="&#xe9;" u2="y" k="33" />
    <hkern u1="&#xe9;" u2="x" k="60" />
    <hkern u1="&#xe9;" u2="v" k="33" />
    <hkern u1="&#xe9;" u2="]" k="36" />
    <hkern u1="&#xe9;" u2="\" k="123" />
    <hkern u1="&#xe9;" u2="W" k="41" />
    <hkern u1="&#xe9;" u2="V" k="123" />
    <hkern u1="&#xe9;" u2="&#x2a;" k="96" />
    <hkern u1="&#xe9;" u2="&#x29;" k="36" />
    <hkern u1="&#xe9;" u2="&#x27;" k="96" />
    <hkern u1="&#xe9;" u2="&#x22;" k="96" />
    <hkern u1="&#xea;" u2="&#x2122;" k="96" />
    <hkern u1="&#xea;" u2="&#x201d;" k="96" />
    <hkern u1="&#xea;" u2="&#x201c;" k="96" />
    <hkern u1="&#xea;" u2="&#x2019;" k="96" />
    <hkern u1="&#xea;" u2="&#x2018;" k="96" />
    <hkern u1="&#xea;" u2="&#xff;" k="33" />
    <hkern u1="&#xea;" u2="&#xfd;" k="33" />
    <hkern u1="&#xea;" u2="&#xba;" k="96" />
    <hkern u1="&#xea;" u2="&#xb0;" k="96" />
    <hkern u1="&#xea;" u2="&#xaa;" k="96" />
    <hkern u1="&#xea;" u2="&#x7d;" k="36" />
    <hkern u1="&#xea;" u2="y" k="33" />
    <hkern u1="&#xea;" u2="x" k="60" />
    <hkern u1="&#xea;" u2="v" k="33" />
    <hkern u1="&#xea;" u2="]" k="36" />
    <hkern u1="&#xea;" u2="\" k="123" />
    <hkern u1="&#xea;" u2="W" k="41" />
    <hkern u1="&#xea;" u2="V" k="123" />
    <hkern u1="&#xea;" u2="&#x2a;" k="96" />
    <hkern u1="&#xea;" u2="&#x29;" k="36" />
    <hkern u1="&#xea;" u2="&#x27;" k="96" />
    <hkern u1="&#xea;" u2="&#x22;" k="96" />
    <hkern u1="&#xeb;" u2="&#x2122;" k="96" />
    <hkern u1="&#xeb;" u2="&#x201d;" k="96" />
    <hkern u1="&#xeb;" u2="&#x201c;" k="96" />
    <hkern u1="&#xeb;" u2="&#x2019;" k="96" />
    <hkern u1="&#xeb;" u2="&#x2018;" k="96" />
    <hkern u1="&#xeb;" u2="&#xff;" k="33" />
    <hkern u1="&#xeb;" u2="&#xfd;" k="33" />
    <hkern u1="&#xeb;" u2="&#xba;" k="96" />
    <hkern u1="&#xeb;" u2="&#xb0;" k="96" />
    <hkern u1="&#xeb;" u2="&#xaa;" k="96" />
    <hkern u1="&#xeb;" u2="&#x7d;" k="36" />
    <hkern u1="&#xeb;" u2="y" k="33" />
    <hkern u1="&#xeb;" u2="x" k="60" />
    <hkern u1="&#xeb;" u2="v" k="33" />
    <hkern u1="&#xeb;" u2="]" k="36" />
    <hkern u1="&#xeb;" u2="\" k="123" />
    <hkern u1="&#xeb;" u2="W" k="41" />
    <hkern u1="&#xeb;" u2="V" k="123" />
    <hkern u1="&#xeb;" u2="&#x2a;" k="96" />
    <hkern u1="&#xeb;" u2="&#x29;" k="36" />
    <hkern u1="&#xeb;" u2="&#x27;" k="96" />
    <hkern u1="&#xeb;" u2="&#x22;" k="96" />
    <hkern u1="&#xf1;" u2="&#x2122;" k="76" />
    <hkern u1="&#xf1;" u2="&#x201d;" k="76" />
    <hkern u1="&#xf1;" u2="&#x201c;" k="76" />
    <hkern u1="&#xf1;" u2="&#x2019;" k="76" />
    <hkern u1="&#xf1;" u2="&#x2018;" k="76" />
    <hkern u1="&#xf1;" u2="&#xff;" k="36" />
    <hkern u1="&#xf1;" u2="&#xfd;" k="36" />
    <hkern u1="&#xf1;" u2="&#xba;" k="76" />
    <hkern u1="&#xf1;" u2="&#xb9;" k="76" />
    <hkern u1="&#xf1;" u2="&#xb3;" k="76" />
    <hkern u1="&#xf1;" u2="&#xb2;" k="76" />
    <hkern u1="&#xf1;" u2="&#xb0;" k="76" />
    <hkern u1="&#xf1;" u2="&#xaa;" k="76" />
    <hkern u1="&#xf1;" u2="y" k="36" />
    <hkern u1="&#xf1;" u2="w" k="18" />
    <hkern u1="&#xf1;" u2="v" k="36" />
    <hkern u1="&#xf1;" u2="&#x2a;" k="76" />
    <hkern u1="&#xf1;" u2="&#x27;" k="76" />
    <hkern u1="&#xf1;" u2="&#x22;" k="76" />
    <hkern u1="&#xf2;" u2="&#x2122;" k="96" />
    <hkern u1="&#xf2;" u2="&#x201d;" k="96" />
    <hkern u1="&#xf2;" u2="&#x201c;" k="96" />
    <hkern u1="&#xf2;" u2="&#x2019;" k="96" />
    <hkern u1="&#xf2;" u2="&#x2018;" k="96" />
    <hkern u1="&#xf2;" u2="&#xff;" k="33" />
    <hkern u1="&#xf2;" u2="&#xfd;" k="33" />
    <hkern u1="&#xf2;" u2="&#xba;" k="96" />
    <hkern u1="&#xf2;" u2="&#xb0;" k="96" />
    <hkern u1="&#xf2;" u2="&#xaa;" k="96" />
    <hkern u1="&#xf2;" u2="&#x7d;" k="36" />
    <hkern u1="&#xf2;" u2="y" k="33" />
    <hkern u1="&#xf2;" u2="x" k="60" />
    <hkern u1="&#xf2;" u2="v" k="33" />
    <hkern u1="&#xf2;" u2="]" k="36" />
    <hkern u1="&#xf2;" u2="\" k="123" />
    <hkern u1="&#xf2;" u2="W" k="41" />
    <hkern u1="&#xf2;" u2="V" k="123" />
    <hkern u1="&#xf2;" u2="&#x2a;" k="96" />
    <hkern u1="&#xf2;" u2="&#x29;" k="36" />
    <hkern u1="&#xf2;" u2="&#x27;" k="96" />
    <hkern u1="&#xf2;" u2="&#x22;" k="96" />
    <hkern u1="&#xf3;" u2="&#x2122;" k="96" />
    <hkern u1="&#xf3;" u2="&#x201d;" k="96" />
    <hkern u1="&#xf3;" u2="&#x201c;" k="96" />
    <hkern u1="&#xf3;" u2="&#x2019;" k="96" />
    <hkern u1="&#xf3;" u2="&#x2018;" k="96" />
    <hkern u1="&#xf3;" u2="&#xff;" k="33" />
    <hkern u1="&#xf3;" u2="&#xfd;" k="33" />
    <hkern u1="&#xf3;" u2="&#xba;" k="96" />
    <hkern u1="&#xf3;" u2="&#xb0;" k="96" />
    <hkern u1="&#xf3;" u2="&#xaa;" k="96" />
    <hkern u1="&#xf3;" u2="&#x7d;" k="36" />
    <hkern u1="&#xf3;" u2="y" k="33" />
    <hkern u1="&#xf3;" u2="x" k="60" />
    <hkern u1="&#xf3;" u2="v" k="33" />
    <hkern u1="&#xf3;" u2="]" k="36" />
    <hkern u1="&#xf3;" u2="\" k="123" />
    <hkern u1="&#xf3;" u2="W" k="41" />
    <hkern u1="&#xf3;" u2="V" k="123" />
    <hkern u1="&#xf3;" u2="&#x2a;" k="96" />
    <hkern u1="&#xf3;" u2="&#x29;" k="36" />
    <hkern u1="&#xf3;" u2="&#x27;" k="96" />
    <hkern u1="&#xf3;" u2="&#x22;" k="96" />
    <hkern u1="&#xf4;" u2="&#x2122;" k="96" />
    <hkern u1="&#xf4;" u2="&#x201d;" k="96" />
    <hkern u1="&#xf4;" u2="&#x201c;" k="96" />
    <hkern u1="&#xf4;" u2="&#x2019;" k="96" />
    <hkern u1="&#xf4;" u2="&#x2018;" k="96" />
    <hkern u1="&#xf4;" u2="&#xff;" k="33" />
    <hkern u1="&#xf4;" u2="&#xfd;" k="33" />
    <hkern u1="&#xf4;" u2="&#xba;" k="96" />
    <hkern u1="&#xf4;" u2="&#xb0;" k="96" />
    <hkern u1="&#xf4;" u2="&#xaa;" k="96" />
    <hkern u1="&#xf4;" u2="&#x7d;" k="36" />
    <hkern u1="&#xf4;" u2="y" k="33" />
    <hkern u1="&#xf4;" u2="x" k="60" />
    <hkern u1="&#xf4;" u2="v" k="33" />
    <hkern u1="&#xf4;" u2="]" k="36" />
    <hkern u1="&#xf4;" u2="\" k="123" />
    <hkern u1="&#xf4;" u2="W" k="41" />
    <hkern u1="&#xf4;" u2="V" k="123" />
    <hkern u1="&#xf4;" u2="&#x2a;" k="96" />
    <hkern u1="&#xf4;" u2="&#x29;" k="36" />
    <hkern u1="&#xf4;" u2="&#x27;" k="96" />
    <hkern u1="&#xf4;" u2="&#x22;" k="96" />
    <hkern u1="&#xf5;" u2="&#x2122;" k="96" />
    <hkern u1="&#xf5;" u2="&#x201d;" k="96" />
    <hkern u1="&#xf5;" u2="&#x201c;" k="96" />
    <hkern u1="&#xf5;" u2="&#x2019;" k="96" />
    <hkern u1="&#xf5;" u2="&#x2018;" k="96" />
    <hkern u1="&#xf5;" u2="&#xff;" k="33" />
    <hkern u1="&#xf5;" u2="&#xfd;" k="33" />
    <hkern u1="&#xf5;" u2="&#xba;" k="96" />
    <hkern u1="&#xf5;" u2="&#xb0;" k="96" />
    <hkern u1="&#xf5;" u2="&#xaa;" k="96" />
    <hkern u1="&#xf5;" u2="&#x7d;" k="36" />
    <hkern u1="&#xf5;" u2="y" k="33" />
    <hkern u1="&#xf5;" u2="x" k="60" />
    <hkern u1="&#xf5;" u2="v" k="33" />
    <hkern u1="&#xf5;" u2="]" k="36" />
    <hkern u1="&#xf5;" u2="\" k="123" />
    <hkern u1="&#xf5;" u2="W" k="41" />
    <hkern u1="&#xf5;" u2="V" k="123" />
    <hkern u1="&#xf5;" u2="&#x2a;" k="96" />
    <hkern u1="&#xf5;" u2="&#x29;" k="36" />
    <hkern u1="&#xf5;" u2="&#x27;" k="96" />
    <hkern u1="&#xf5;" u2="&#x22;" k="96" />
    <hkern u1="&#xf6;" u2="&#x2122;" k="96" />
    <hkern u1="&#xf6;" u2="&#x201d;" k="96" />
    <hkern u1="&#xf6;" u2="&#x201c;" k="96" />
    <hkern u1="&#xf6;" u2="&#x2019;" k="96" />
    <hkern u1="&#xf6;" u2="&#x2018;" k="96" />
    <hkern u1="&#xf6;" u2="&#xff;" k="33" />
    <hkern u1="&#xf6;" u2="&#xfd;" k="33" />
    <hkern u1="&#xf6;" u2="&#xba;" k="96" />
    <hkern u1="&#xf6;" u2="&#xb0;" k="96" />
    <hkern u1="&#xf6;" u2="&#xaa;" k="96" />
    <hkern u1="&#xf6;" u2="&#x7d;" k="36" />
    <hkern u1="&#xf6;" u2="y" k="33" />
    <hkern u1="&#xf6;" u2="x" k="60" />
    <hkern u1="&#xf6;" u2="v" k="33" />
    <hkern u1="&#xf6;" u2="]" k="36" />
    <hkern u1="&#xf6;" u2="\" k="123" />
    <hkern u1="&#xf6;" u2="W" k="41" />
    <hkern u1="&#xf6;" u2="V" k="123" />
    <hkern u1="&#xf6;" u2="&#x2a;" k="96" />
    <hkern u1="&#xf6;" u2="&#x29;" k="36" />
    <hkern u1="&#xf6;" u2="&#x27;" k="96" />
    <hkern u1="&#xf6;" u2="&#x22;" k="96" />
    <hkern u1="&#xf8;" u2="&#x2122;" k="96" />
    <hkern u1="&#xf8;" u2="&#x201d;" k="96" />
    <hkern u1="&#xf8;" u2="&#x201c;" k="96" />
    <hkern u1="&#xf8;" u2="&#x2019;" k="96" />
    <hkern u1="&#xf8;" u2="&#x2018;" k="96" />
    <hkern u1="&#xf8;" u2="&#xff;" k="33" />
    <hkern u1="&#xf8;" u2="&#xfd;" k="33" />
    <hkern u1="&#xf8;" u2="&#xba;" k="96" />
    <hkern u1="&#xf8;" u2="&#xb0;" k="96" />
    <hkern u1="&#xf8;" u2="&#xaa;" k="96" />
    <hkern u1="&#xf8;" u2="&#x7d;" k="36" />
    <hkern u1="&#xf8;" u2="y" k="33" />
    <hkern u1="&#xf8;" u2="x" k="60" />
    <hkern u1="&#xf8;" u2="v" k="33" />
    <hkern u1="&#xf8;" u2="]" k="36" />
    <hkern u1="&#xf8;" u2="\" k="123" />
    <hkern u1="&#xf8;" u2="W" k="41" />
    <hkern u1="&#xf8;" u2="V" k="123" />
    <hkern u1="&#xf8;" u2="&#x2a;" k="96" />
    <hkern u1="&#xf8;" u2="&#x29;" k="36" />
    <hkern u1="&#xf8;" u2="&#x27;" k="96" />
    <hkern u1="&#xf8;" u2="&#x22;" k="96" />
    <hkern u1="&#xfd;" u2="&#x2206;" k="91" />
    <hkern u1="&#xfd;" u2="&#x201e;" k="136" />
    <hkern u1="&#xfd;" u2="&#x201a;" k="136" />
    <hkern u1="&#xfd;" u2="&#x153;" k="33" />
    <hkern u1="&#xfd;" u2="&#x119;" k="33" />
    <hkern u1="&#xfd;" u2="&#x107;" k="33" />
    <hkern u1="&#xfd;" u2="&#x104;" k="91" />
    <hkern u1="&#xfd;" u2="&#xf8;" k="33" />
    <hkern u1="&#xfd;" u2="&#xf6;" k="33" />
    <hkern u1="&#xfd;" u2="&#xf5;" k="33" />
    <hkern u1="&#xfd;" u2="&#xf4;" k="33" />
    <hkern u1="&#xfd;" u2="&#xf3;" k="33" />
    <hkern u1="&#xfd;" u2="&#xf2;" k="33" />
    <hkern u1="&#xfd;" u2="&#xf0;" k="33" />
    <hkern u1="&#xfd;" u2="&#xeb;" k="33" />
    <hkern u1="&#xfd;" u2="&#xea;" k="33" />
    <hkern u1="&#xfd;" u2="&#xe9;" k="33" />
    <hkern u1="&#xfd;" u2="&#xe8;" k="33" />
    <hkern u1="&#xfd;" u2="&#xe7;" k="33" />
    <hkern u1="&#xfd;" u2="&#xc6;" k="91" />
    <hkern u1="&#xfd;" u2="&#xc5;" k="91" />
    <hkern u1="&#xfd;" u2="&#xc4;" k="91" />
    <hkern u1="&#xfd;" u2="&#xc3;" k="91" />
    <hkern u1="&#xfd;" u2="&#xc2;" k="91" />
    <hkern u1="&#xfd;" u2="&#xc1;" k="91" />
    <hkern u1="&#xfd;" u2="&#xc0;" k="91" />
    <hkern u1="&#xfd;" u2="q" k="33" />
    <hkern u1="&#xfd;" u2="o" k="33" />
    <hkern u1="&#xfd;" u2="e" k="33" />
    <hkern u1="&#xfd;" u2="d" k="33" />
    <hkern u1="&#xfd;" u2="c" k="33" />
    <hkern u1="&#xfd;" u2="A" k="91" />
    <hkern u1="&#xfd;" u2="&#x2f;" k="91" />
    <hkern u1="&#xfd;" u2="&#x2e;" k="136" />
    <hkern u1="&#xfd;" u2="&#x2c;" k="136" />
    <hkern u1="&#xfd;" u2="&#x26;" k="91" />
    <hkern u1="&#xfe;" u2="&#x2122;" k="96" />
    <hkern u1="&#xfe;" u2="&#x201d;" k="96" />
    <hkern u1="&#xfe;" u2="&#x201c;" k="96" />
    <hkern u1="&#xfe;" u2="&#x2019;" k="96" />
    <hkern u1="&#xfe;" u2="&#x2018;" k="96" />
    <hkern u1="&#xfe;" u2="&#xff;" k="33" />
    <hkern u1="&#xfe;" u2="&#xfd;" k="33" />
    <hkern u1="&#xfe;" u2="&#xba;" k="96" />
    <hkern u1="&#xfe;" u2="&#xb0;" k="96" />
    <hkern u1="&#xfe;" u2="&#xaa;" k="96" />
    <hkern u1="&#xfe;" u2="&#x7d;" k="36" />
    <hkern u1="&#xfe;" u2="y" k="33" />
    <hkern u1="&#xfe;" u2="x" k="60" />
    <hkern u1="&#xfe;" u2="v" k="33" />
    <hkern u1="&#xfe;" u2="]" k="36" />
    <hkern u1="&#xfe;" u2="\" k="123" />
    <hkern u1="&#xfe;" u2="W" k="41" />
    <hkern u1="&#xfe;" u2="V" k="123" />
    <hkern u1="&#xfe;" u2="&#x2a;" k="96" />
    <hkern u1="&#xfe;" u2="&#x29;" k="36" />
    <hkern u1="&#xfe;" u2="&#x27;" k="96" />
    <hkern u1="&#xfe;" u2="&#x22;" k="96" />
    <hkern u1="&#xff;" u2="&#x2206;" k="91" />
    <hkern u1="&#xff;" u2="&#x201e;" k="136" />
    <hkern u1="&#xff;" u2="&#x201a;" k="136" />
    <hkern u1="&#xff;" u2="&#x153;" k="33" />
    <hkern u1="&#xff;" u2="&#x119;" k="33" />
    <hkern u1="&#xff;" u2="&#x107;" k="33" />
    <hkern u1="&#xff;" u2="&#x104;" k="91" />
    <hkern u1="&#xff;" u2="&#xf8;" k="33" />
    <hkern u1="&#xff;" u2="&#xf6;" k="33" />
    <hkern u1="&#xff;" u2="&#xf5;" k="33" />
    <hkern u1="&#xff;" u2="&#xf4;" k="33" />
    <hkern u1="&#xff;" u2="&#xf3;" k="33" />
    <hkern u1="&#xff;" u2="&#xf2;" k="33" />
    <hkern u1="&#xff;" u2="&#xf0;" k="33" />
    <hkern u1="&#xff;" u2="&#xeb;" k="33" />
    <hkern u1="&#xff;" u2="&#xea;" k="33" />
    <hkern u1="&#xff;" u2="&#xe9;" k="33" />
    <hkern u1="&#xff;" u2="&#xe8;" k="33" />
    <hkern u1="&#xff;" u2="&#xe7;" k="33" />
    <hkern u1="&#xff;" u2="&#xc6;" k="91" />
    <hkern u1="&#xff;" u2="&#xc5;" k="91" />
    <hkern u1="&#xff;" u2="&#xc4;" k="91" />
    <hkern u1="&#xff;" u2="&#xc3;" k="91" />
    <hkern u1="&#xff;" u2="&#xc2;" k="91" />
    <hkern u1="&#xff;" u2="&#xc1;" k="91" />
    <hkern u1="&#xff;" u2="&#xc0;" k="91" />
    <hkern u1="&#xff;" u2="q" k="33" />
    <hkern u1="&#xff;" u2="o" k="33" />
    <hkern u1="&#xff;" u2="e" k="33" />
    <hkern u1="&#xff;" u2="d" k="33" />
    <hkern u1="&#xff;" u2="c" k="33" />
    <hkern u1="&#xff;" u2="A" k="91" />
    <hkern u1="&#xff;" u2="&#x2f;" k="91" />
    <hkern u1="&#xff;" u2="&#x2e;" k="136" />
    <hkern u1="&#xff;" u2="&#x2c;" k="136" />
    <hkern u1="&#xff;" u2="&#x26;" k="91" />
    <hkern u1="&#x104;" u2="&#x2122;" k="191" />
    <hkern u1="&#x104;" u2="&#x203a;" k="67" />
    <hkern u1="&#x104;" u2="&#x2039;" k="67" />
    <hkern u1="&#x104;" u2="&#x2022;" k="67" />
    <hkern u1="&#x104;" u2="&#x201d;" k="191" />
    <hkern u1="&#x104;" u2="&#x201c;" k="191" />
    <hkern u1="&#x104;" u2="&#x2019;" k="191" />
    <hkern u1="&#x104;" u2="&#x2018;" k="191" />
    <hkern u1="&#x104;" u2="&#x2014;" k="67" />
    <hkern u1="&#x104;" u2="&#x2013;" k="67" />
    <hkern u1="&#x104;" u2="&#x178;" k="182" />
    <hkern u1="&#x104;" u2="&#x152;" k="51" />
    <hkern u1="&#x104;" u2="&#x106;" k="51" />
    <hkern u1="&#x104;" u2="&#xff;" k="91" />
    <hkern u1="&#x104;" u2="&#xfd;" k="91" />
    <hkern u1="&#x104;" u2="&#xdd;" k="182" />
    <hkern u1="&#x104;" u2="&#xdc;" k="52" />
    <hkern u1="&#x104;" u2="&#xdb;" k="52" />
    <hkern u1="&#x104;" u2="&#xda;" k="52" />
    <hkern u1="&#x104;" u2="&#xd9;" k="52" />
    <hkern u1="&#x104;" u2="&#xd8;" k="51" />
    <hkern u1="&#x104;" u2="&#xd6;" k="51" />
    <hkern u1="&#x104;" u2="&#xd5;" k="51" />
    <hkern u1="&#x104;" u2="&#xd4;" k="51" />
    <hkern u1="&#x104;" u2="&#xd3;" k="51" />
    <hkern u1="&#x104;" u2="&#xd2;" k="51" />
    <hkern u1="&#x104;" u2="&#xc7;" k="51" />
    <hkern u1="&#x104;" u2="&#xbb;" k="67" />
    <hkern u1="&#x104;" u2="&#xba;" k="191" />
    <hkern u1="&#x104;" u2="&#xb9;" k="202" />
    <hkern u1="&#x104;" u2="&#xb7;" k="67" />
    <hkern u1="&#x104;" u2="&#xb3;" k="202" />
    <hkern u1="&#x104;" u2="&#xb2;" k="202" />
    <hkern u1="&#x104;" u2="&#xb0;" k="191" />
    <hkern u1="&#x104;" u2="&#xae;" k="51" />
    <hkern u1="&#x104;" u2="&#xad;" k="67" />
    <hkern u1="&#x104;" u2="&#xab;" k="67" />
    <hkern u1="&#x104;" u2="&#xaa;" k="191" />
    <hkern u1="&#x104;" u2="&#xa9;" k="51" />
    <hkern u1="&#x104;" u2="y" k="91" />
    <hkern u1="&#x104;" u2="v" k="91" />
    <hkern u1="&#x104;" u2="\" k="169" />
    <hkern u1="&#x104;" u2="Y" k="182" />
    <hkern u1="&#x104;" u2="W" k="102" />
    <hkern u1="&#x104;" u2="V" k="169" />
    <hkern u1="&#x104;" u2="U" k="52" />
    <hkern u1="&#x104;" u2="T" k="147" />
    <hkern u1="&#x104;" u2="Q" k="51" />
    <hkern u1="&#x104;" u2="O" k="51" />
    <hkern u1="&#x104;" u2="J" k="-56" />
    <hkern u1="&#x104;" u2="G" k="51" />
    <hkern u1="&#x104;" u2="C" k="51" />
    <hkern u1="&#x104;" u2="&#x40;" k="51" />
    <hkern u1="&#x104;" u2="&#x3f;" k="63" />
    <hkern u1="&#x104;" u2="&#x2d;" k="67" />
    <hkern u1="&#x104;" u2="&#x2a;" k="191" />
    <hkern u1="&#x104;" u2="&#x27;" k="191" />
    <hkern u1="&#x104;" u2="&#x22;" k="191" />
    <hkern u1="&#x105;" u2="&#x2122;" k="76" />
    <hkern u1="&#x105;" u2="&#x201d;" k="76" />
    <hkern u1="&#x105;" u2="&#x201c;" k="76" />
    <hkern u1="&#x105;" u2="&#x2019;" k="76" />
    <hkern u1="&#x105;" u2="&#x2018;" k="76" />
    <hkern u1="&#x105;" u2="&#xff;" k="36" />
    <hkern u1="&#x105;" u2="&#xfd;" k="36" />
    <hkern u1="&#x105;" u2="&#xba;" k="76" />
    <hkern u1="&#x105;" u2="&#xb9;" k="76" />
    <hkern u1="&#x105;" u2="&#xb3;" k="76" />
    <hkern u1="&#x105;" u2="&#xb2;" k="76" />
    <hkern u1="&#x105;" u2="&#xb0;" k="76" />
    <hkern u1="&#x105;" u2="&#xaa;" k="76" />
    <hkern u1="&#x105;" u2="y" k="36" />
    <hkern u1="&#x105;" u2="w" k="18" />
    <hkern u1="&#x105;" u2="v" k="36" />
    <hkern u1="&#x105;" u2="&#x2a;" k="76" />
    <hkern u1="&#x105;" u2="&#x27;" k="76" />
    <hkern u1="&#x105;" u2="&#x22;" k="76" />
    <hkern u1="&#x106;" u2="&#x203a;" k="144" />
    <hkern u1="&#x106;" u2="&#x2039;" k="144" />
    <hkern u1="&#x106;" u2="&#x2022;" k="144" />
    <hkern u1="&#x106;" u2="&#x2014;" k="144" />
    <hkern u1="&#x106;" u2="&#x2013;" k="144" />
    <hkern u1="&#x106;" u2="&#xbb;" k="144" />
    <hkern u1="&#x106;" u2="&#xb7;" k="144" />
    <hkern u1="&#x106;" u2="&#xad;" k="144" />
    <hkern u1="&#x106;" u2="&#xab;" k="144" />
    <hkern u1="&#x106;" u2="&#x2d;" k="144" />
    <hkern u1="&#x119;" u2="&#x2122;" k="96" />
    <hkern u1="&#x119;" u2="&#x201d;" k="96" />
    <hkern u1="&#x119;" u2="&#x201c;" k="96" />
    <hkern u1="&#x119;" u2="&#x2019;" k="96" />
    <hkern u1="&#x119;" u2="&#x2018;" k="96" />
    <hkern u1="&#x119;" u2="&#xff;" k="33" />
    <hkern u1="&#x119;" u2="&#xfd;" k="33" />
    <hkern u1="&#x119;" u2="&#xba;" k="96" />
    <hkern u1="&#x119;" u2="&#xb0;" k="96" />
    <hkern u1="&#x119;" u2="&#xaa;" k="96" />
    <hkern u1="&#x119;" u2="&#x7d;" k="36" />
    <hkern u1="&#x119;" u2="y" k="33" />
    <hkern u1="&#x119;" u2="x" k="60" />
    <hkern u1="&#x119;" u2="v" k="33" />
    <hkern u1="&#x119;" u2="]" k="36" />
    <hkern u1="&#x119;" u2="\" k="123" />
    <hkern u1="&#x119;" u2="W" k="41" />
    <hkern u1="&#x119;" u2="V" k="123" />
    <hkern u1="&#x119;" u2="&#x2a;" k="96" />
    <hkern u1="&#x119;" u2="&#x29;" k="36" />
    <hkern u1="&#x119;" u2="&#x27;" k="96" />
    <hkern u1="&#x119;" u2="&#x22;" k="96" />
    <hkern u1="&#x141;" u2="&#x2122;" k="140" />
    <hkern u1="&#x141;" u2="&#x203a;" k="113" />
    <hkern u1="&#x141;" u2="&#x2039;" k="113" />
    <hkern u1="&#x141;" u2="&#x2022;" k="113" />
    <hkern u1="&#x141;" u2="&#x201d;" k="140" />
    <hkern u1="&#x141;" u2="&#x201c;" k="140" />
    <hkern u1="&#x141;" u2="&#x2019;" k="140" />
    <hkern u1="&#x141;" u2="&#x2018;" k="140" />
    <hkern u1="&#x141;" u2="&#x2014;" k="113" />
    <hkern u1="&#x141;" u2="&#x2013;" k="113" />
    <hkern u1="&#x141;" u2="&#x178;" k="167" />
    <hkern u1="&#x141;" u2="&#xff;" k="58" />
    <hkern u1="&#x141;" u2="&#xfd;" k="58" />
    <hkern u1="&#x141;" u2="&#xdd;" k="167" />
    <hkern u1="&#x141;" u2="&#xbb;" k="113" />
    <hkern u1="&#x141;" u2="&#xba;" k="140" />
    <hkern u1="&#x141;" u2="&#xb9;" k="136" />
    <hkern u1="&#x141;" u2="&#xb7;" k="113" />
    <hkern u1="&#x141;" u2="&#xb3;" k="136" />
    <hkern u1="&#x141;" u2="&#xb2;" k="136" />
    <hkern u1="&#x141;" u2="&#xb0;" k="140" />
    <hkern u1="&#x141;" u2="&#xad;" k="113" />
    <hkern u1="&#x141;" u2="&#xab;" k="113" />
    <hkern u1="&#x141;" u2="&#xaa;" k="140" />
    <hkern u1="&#x141;" u2="y" k="58" />
    <hkern u1="&#x141;" u2="v" k="58" />
    <hkern u1="&#x141;" u2="\" k="171" />
    <hkern u1="&#x141;" u2="Y" k="167" />
    <hkern u1="&#x141;" u2="W" k="131" />
    <hkern u1="&#x141;" u2="V" k="171" />
    <hkern u1="&#x141;" u2="&#x2d;" k="113" />
    <hkern u1="&#x141;" u2="&#x2a;" k="140" />
    <hkern u1="&#x141;" u2="&#x27;" k="140" />
    <hkern u1="&#x141;" u2="&#x22;" k="140" />
    <hkern u1="&#x144;" u2="&#x2122;" k="76" />
    <hkern u1="&#x144;" u2="&#x201d;" k="76" />
    <hkern u1="&#x144;" u2="&#x201c;" k="76" />
    <hkern u1="&#x144;" u2="&#x2019;" k="76" />
    <hkern u1="&#x144;" u2="&#x2018;" k="76" />
    <hkern u1="&#x144;" u2="&#xff;" k="36" />
    <hkern u1="&#x144;" u2="&#xfd;" k="36" />
    <hkern u1="&#x144;" u2="&#xba;" k="76" />
    <hkern u1="&#x144;" u2="&#xb9;" k="76" />
    <hkern u1="&#x144;" u2="&#xb3;" k="76" />
    <hkern u1="&#x144;" u2="&#xb2;" k="76" />
    <hkern u1="&#x144;" u2="&#xb0;" k="76" />
    <hkern u1="&#x144;" u2="&#xaa;" k="76" />
    <hkern u1="&#x144;" u2="y" k="36" />
    <hkern u1="&#x144;" u2="w" k="18" />
    <hkern u1="&#x144;" u2="v" k="36" />
    <hkern u1="&#x144;" u2="&#x2a;" k="76" />
    <hkern u1="&#x144;" u2="&#x27;" k="76" />
    <hkern u1="&#x144;" u2="&#x22;" k="76" />
    <hkern u1="&#x153;" u2="&#x2122;" k="96" />
    <hkern u1="&#x153;" u2="&#x201d;" k="96" />
    <hkern u1="&#x153;" u2="&#x201c;" k="96" />
    <hkern u1="&#x153;" u2="&#x2019;" k="96" />
    <hkern u1="&#x153;" u2="&#x2018;" k="96" />
    <hkern u1="&#x153;" u2="&#xff;" k="33" />
    <hkern u1="&#x153;" u2="&#xfd;" k="33" />
    <hkern u1="&#x153;" u2="&#xba;" k="96" />
    <hkern u1="&#x153;" u2="&#xb0;" k="96" />
    <hkern u1="&#x153;" u2="&#xaa;" k="96" />
    <hkern u1="&#x153;" u2="&#x7d;" k="36" />
    <hkern u1="&#x153;" u2="y" k="33" />
    <hkern u1="&#x153;" u2="x" k="60" />
    <hkern u1="&#x153;" u2="v" k="33" />
    <hkern u1="&#x153;" u2="]" k="36" />
    <hkern u1="&#x153;" u2="\" k="123" />
    <hkern u1="&#x153;" u2="W" k="41" />
    <hkern u1="&#x153;" u2="V" k="123" />
    <hkern u1="&#x153;" u2="&#x2a;" k="96" />
    <hkern u1="&#x153;" u2="&#x29;" k="36" />
    <hkern u1="&#x153;" u2="&#x27;" k="96" />
    <hkern u1="&#x153;" u2="&#x22;" k="96" />
    <hkern u1="&#x178;" u2="&#x2206;" k="182" />
    <hkern u1="&#x178;" u2="&#x2122;" k="-36" />
    <hkern u1="&#x178;" u2="&#x203a;" k="160" />
    <hkern u1="&#x178;" u2="&#x2039;" k="160" />
    <hkern u1="&#x178;" u2="&#x2022;" k="160" />
    <hkern u1="&#x178;" u2="&#x201e;" k="167" />
    <hkern u1="&#x178;" u2="&#x201d;" k="-36" />
    <hkern u1="&#x178;" u2="&#x201c;" k="-36" />
    <hkern u1="&#x178;" u2="&#x201a;" k="167" />
    <hkern u1="&#x178;" u2="&#x2019;" k="-36" />
    <hkern u1="&#x178;" u2="&#x2018;" k="-36" />
    <hkern u1="&#x178;" u2="&#x2014;" k="160" />
    <hkern u1="&#x178;" u2="&#x2013;" k="160" />
    <hkern u1="&#x178;" u2="&#x161;" k="139" />
    <hkern u1="&#x178;" u2="&#x15b;" k="139" />
    <hkern u1="&#x178;" u2="&#x153;" k="160" />
    <hkern u1="&#x178;" u2="&#x152;" k="80" />
    <hkern u1="&#x178;" u2="&#x144;" k="131" />
    <hkern u1="&#x178;" u2="&#x131;" k="131" />
    <hkern u1="&#x178;" u2="&#x119;" k="160" />
    <hkern u1="&#x178;" u2="&#x107;" k="160" />
    <hkern u1="&#x178;" u2="&#x106;" k="80" />
    <hkern u1="&#x178;" u2="&#x105;" k="145" />
    <hkern u1="&#x178;" u2="&#x104;" k="182" />
    <hkern u1="&#x178;" u2="&#xff;" k="100" />
    <hkern u1="&#x178;" u2="&#xfd;" k="100" />
    <hkern u1="&#x178;" u2="&#xfc;" k="131" />
    <hkern u1="&#x178;" u2="&#xfb;" k="131" />
    <hkern u1="&#x178;" u2="&#xfa;" k="131" />
    <hkern u1="&#x178;" u2="&#xf9;" k="131" />
    <hkern u1="&#x178;" u2="&#xf8;" k="160" />
    <hkern u1="&#x178;" u2="&#xf6;" k="160" />
    <hkern u1="&#x178;" u2="&#xf5;" k="160" />
    <hkern u1="&#x178;" u2="&#xf4;" k="160" />
    <hkern u1="&#x178;" u2="&#xf3;" k="160" />
    <hkern u1="&#x178;" u2="&#xf2;" k="160" />
    <hkern u1="&#x178;" u2="&#xf1;" k="131" />
    <hkern u1="&#x178;" u2="&#xf0;" k="160" />
    <hkern u1="&#x178;" u2="&#xeb;" k="160" />
    <hkern u1="&#x178;" u2="&#xea;" k="160" />
    <hkern u1="&#x178;" u2="&#xe9;" k="160" />
    <hkern u1="&#x178;" u2="&#xe8;" k="160" />
    <hkern u1="&#x178;" u2="&#xe7;" k="160" />
    <hkern u1="&#x178;" u2="&#xe6;" k="145" />
    <hkern u1="&#x178;" u2="&#xe5;" k="145" />
    <hkern u1="&#x178;" u2="&#xe4;" k="145" />
    <hkern u1="&#x178;" u2="&#xe3;" k="145" />
    <hkern u1="&#x178;" u2="&#xe2;" k="145" />
    <hkern u1="&#x178;" u2="&#xe1;" k="145" />
    <hkern u1="&#x178;" u2="&#xe0;" k="145" />
    <hkern u1="&#x178;" u2="&#xd8;" k="80" />
    <hkern u1="&#x178;" u2="&#xd6;" k="80" />
    <hkern u1="&#x178;" u2="&#xd5;" k="80" />
    <hkern u1="&#x178;" u2="&#xd4;" k="80" />
    <hkern u1="&#x178;" u2="&#xd3;" k="80" />
    <hkern u1="&#x178;" u2="&#xd2;" k="80" />
    <hkern u1="&#x178;" u2="&#xc7;" k="80" />
    <hkern u1="&#x178;" u2="&#xc6;" k="182" />
    <hkern u1="&#x178;" u2="&#xc5;" k="182" />
    <hkern u1="&#x178;" u2="&#xc4;" k="182" />
    <hkern u1="&#x178;" u2="&#xc3;" k="182" />
    <hkern u1="&#x178;" u2="&#xc2;" k="182" />
    <hkern u1="&#x178;" u2="&#xc1;" k="182" />
    <hkern u1="&#x178;" u2="&#xc0;" k="182" />
    <hkern u1="&#x178;" u2="&#xbb;" k="160" />
    <hkern u1="&#x178;" u2="&#xba;" k="-36" />
    <hkern u1="&#x178;" u2="&#xb9;" k="-56" />
    <hkern u1="&#x178;" u2="&#xb7;" k="160" />
    <hkern u1="&#x178;" u2="&#xb5;" k="131" />
    <hkern u1="&#x178;" u2="&#xb3;" k="-56" />
    <hkern u1="&#x178;" u2="&#xb2;" k="-56" />
    <hkern u1="&#x178;" u2="&#xb0;" k="-36" />
    <hkern u1="&#x178;" u2="&#xae;" k="80" />
    <hkern u1="&#x178;" u2="&#xad;" k="160" />
    <hkern u1="&#x178;" u2="&#xab;" k="160" />
    <hkern u1="&#x178;" u2="&#xaa;" k="-36" />
    <hkern u1="&#x178;" u2="&#xa9;" k="80" />
    <hkern u1="&#x178;" u2="y" k="100" />
    <hkern u1="&#x178;" u2="x" k="136" />
    <hkern u1="&#x178;" u2="w" k="96" />
    <hkern u1="&#x178;" u2="v" k="100" />
    <hkern u1="&#x178;" u2="u" k="131" />
    <hkern u1="&#x178;" u2="s" k="139" />
    <hkern u1="&#x178;" u2="r" k="131" />
    <hkern u1="&#x178;" u2="q" k="160" />
    <hkern u1="&#x178;" u2="p" k="131" />
    <hkern u1="&#x178;" u2="o" k="160" />
    <hkern u1="&#x178;" u2="n" k="131" />
    <hkern u1="&#x178;" u2="m" k="131" />
    <hkern u1="&#x178;" u2="g" k="176" />
    <hkern u1="&#x178;" u2="e" k="160" />
    <hkern u1="&#x178;" u2="d" k="160" />
    <hkern u1="&#x178;" u2="c" k="160" />
    <hkern u1="&#x178;" u2="a" k="145" />
    <hkern u1="&#x178;" u2="Q" k="80" />
    <hkern u1="&#x178;" u2="O" k="80" />
    <hkern u1="&#x178;" u2="J" k="200" />
    <hkern u1="&#x178;" u2="G" k="80" />
    <hkern u1="&#x178;" u2="C" k="80" />
    <hkern u1="&#x178;" u2="A" k="182" />
    <hkern u1="&#x178;" u2="&#x40;" k="80" />
    <hkern u1="&#x178;" u2="&#x3f;" k="-32" />
    <hkern u1="&#x178;" u2="&#x3b;" k="131" />
    <hkern u1="&#x178;" u2="&#x3a;" k="131" />
    <hkern u1="&#x178;" u2="&#x2f;" k="182" />
    <hkern u1="&#x178;" u2="&#x2e;" k="167" />
    <hkern u1="&#x178;" u2="&#x2d;" k="160" />
    <hkern u1="&#x178;" u2="&#x2c;" k="167" />
    <hkern u1="&#x178;" u2="&#x2a;" k="-36" />
    <hkern u1="&#x178;" u2="&#x27;" k="-36" />
    <hkern u1="&#x178;" u2="&#x26;" k="182" />
    <hkern u1="&#x178;" u2="&#x22;" k="-36" />
    <hkern u1="&#x179;" u2="&#x203a;" k="64" />
    <hkern u1="&#x179;" u2="&#x2039;" k="64" />
    <hkern u1="&#x179;" u2="&#x2022;" k="64" />
    <hkern u1="&#x179;" u2="&#x2014;" k="64" />
    <hkern u1="&#x179;" u2="&#x2013;" k="64" />
    <hkern u1="&#x179;" u2="&#x161;" k="19" />
    <hkern u1="&#x179;" u2="&#x15b;" k="19" />
    <hkern u1="&#x179;" u2="&#x153;" k="29" />
    <hkern u1="&#x179;" u2="&#x152;" k="49" />
    <hkern u1="&#x179;" u2="&#x119;" k="29" />
    <hkern u1="&#x179;" u2="&#x107;" k="29" />
    <hkern u1="&#x179;" u2="&#x106;" k="49" />
    <hkern u1="&#x179;" u2="&#xff;" k="34" />
    <hkern u1="&#x179;" u2="&#xfd;" k="34" />
    <hkern u1="&#x179;" u2="&#xf8;" k="29" />
    <hkern u1="&#x179;" u2="&#xf6;" k="29" />
    <hkern u1="&#x179;" u2="&#xf5;" k="29" />
    <hkern u1="&#x179;" u2="&#xf4;" k="29" />
    <hkern u1="&#x179;" u2="&#xf3;" k="29" />
    <hkern u1="&#x179;" u2="&#xf2;" k="29" />
    <hkern u1="&#x179;" u2="&#xf0;" k="29" />
    <hkern u1="&#x179;" u2="&#xeb;" k="29" />
    <hkern u1="&#x179;" u2="&#xea;" k="29" />
    <hkern u1="&#x179;" u2="&#xe9;" k="29" />
    <hkern u1="&#x179;" u2="&#xe8;" k="29" />
    <hkern u1="&#x179;" u2="&#xe7;" k="29" />
    <hkern u1="&#x179;" u2="&#xd8;" k="49" />
    <hkern u1="&#x179;" u2="&#xd6;" k="49" />
    <hkern u1="&#x179;" u2="&#xd5;" k="49" />
    <hkern u1="&#x179;" u2="&#xd4;" k="49" />
    <hkern u1="&#x179;" u2="&#xd3;" k="49" />
    <hkern u1="&#x179;" u2="&#xd2;" k="49" />
    <hkern u1="&#x179;" u2="&#xc7;" k="49" />
    <hkern u1="&#x179;" u2="&#xbb;" k="64" />
    <hkern u1="&#x179;" u2="&#xb7;" k="64" />
    <hkern u1="&#x179;" u2="&#xae;" k="49" />
    <hkern u1="&#x179;" u2="&#xad;" k="64" />
    <hkern u1="&#x179;" u2="&#xab;" k="64" />
    <hkern u1="&#x179;" u2="&#xa9;" k="49" />
    <hkern u1="&#x179;" u2="y" k="34" />
    <hkern u1="&#x179;" u2="v" k="34" />
    <hkern u1="&#x179;" u2="s" k="19" />
    <hkern u1="&#x179;" u2="q" k="29" />
    <hkern u1="&#x179;" u2="o" k="29" />
    <hkern u1="&#x179;" u2="e" k="29" />
    <hkern u1="&#x179;" u2="d" k="29" />
    <hkern u1="&#x179;" u2="c" k="29" />
    <hkern u1="&#x179;" u2="Q" k="49" />
    <hkern u1="&#x179;" u2="O" k="49" />
    <hkern u1="&#x179;" u2="G" k="49" />
    <hkern u1="&#x179;" u2="C" k="49" />
    <hkern u1="&#x179;" u2="&#x40;" k="49" />
    <hkern u1="&#x179;" u2="&#x3f;" k="-32" />
    <hkern u1="&#x179;" u2="&#x2d;" k="64" />
    <hkern u1="&#x17b;" u2="&#x203a;" k="64" />
    <hkern u1="&#x17b;" u2="&#x2039;" k="64" />
    <hkern u1="&#x17b;" u2="&#x2022;" k="64" />
    <hkern u1="&#x17b;" u2="&#x2014;" k="64" />
    <hkern u1="&#x17b;" u2="&#x2013;" k="64" />
    <hkern u1="&#x17b;" u2="&#x161;" k="19" />
    <hkern u1="&#x17b;" u2="&#x15b;" k="19" />
    <hkern u1="&#x17b;" u2="&#x153;" k="29" />
    <hkern u1="&#x17b;" u2="&#x152;" k="49" />
    <hkern u1="&#x17b;" u2="&#x119;" k="29" />
    <hkern u1="&#x17b;" u2="&#x107;" k="29" />
    <hkern u1="&#x17b;" u2="&#x106;" k="49" />
    <hkern u1="&#x17b;" u2="&#xff;" k="34" />
    <hkern u1="&#x17b;" u2="&#xfd;" k="34" />
    <hkern u1="&#x17b;" u2="&#xf8;" k="29" />
    <hkern u1="&#x17b;" u2="&#xf6;" k="29" />
    <hkern u1="&#x17b;" u2="&#xf5;" k="29" />
    <hkern u1="&#x17b;" u2="&#xf4;" k="29" />
    <hkern u1="&#x17b;" u2="&#xf3;" k="29" />
    <hkern u1="&#x17b;" u2="&#xf2;" k="29" />
    <hkern u1="&#x17b;" u2="&#xf0;" k="29" />
    <hkern u1="&#x17b;" u2="&#xeb;" k="29" />
    <hkern u1="&#x17b;" u2="&#xea;" k="29" />
    <hkern u1="&#x17b;" u2="&#xe9;" k="29" />
    <hkern u1="&#x17b;" u2="&#xe8;" k="29" />
    <hkern u1="&#x17b;" u2="&#xe7;" k="29" />
    <hkern u1="&#x17b;" u2="&#xd8;" k="49" />
    <hkern u1="&#x17b;" u2="&#xd6;" k="49" />
    <hkern u1="&#x17b;" u2="&#xd5;" k="49" />
    <hkern u1="&#x17b;" u2="&#xd4;" k="49" />
    <hkern u1="&#x17b;" u2="&#xd3;" k="49" />
    <hkern u1="&#x17b;" u2="&#xd2;" k="49" />
    <hkern u1="&#x17b;" u2="&#xc7;" k="49" />
    <hkern u1="&#x17b;" u2="&#xbb;" k="64" />
    <hkern u1="&#x17b;" u2="&#xb7;" k="64" />
    <hkern u1="&#x17b;" u2="&#xae;" k="49" />
    <hkern u1="&#x17b;" u2="&#xad;" k="64" />
    <hkern u1="&#x17b;" u2="&#xab;" k="64" />
    <hkern u1="&#x17b;" u2="&#xa9;" k="49" />
    <hkern u1="&#x17b;" u2="y" k="34" />
    <hkern u1="&#x17b;" u2="v" k="34" />
    <hkern u1="&#x17b;" u2="s" k="19" />
    <hkern u1="&#x17b;" u2="q" k="29" />
    <hkern u1="&#x17b;" u2="o" k="29" />
    <hkern u1="&#x17b;" u2="e" k="29" />
    <hkern u1="&#x17b;" u2="d" k="29" />
    <hkern u1="&#x17b;" u2="c" k="29" />
    <hkern u1="&#x17b;" u2="Q" k="49" />
    <hkern u1="&#x17b;" u2="O" k="49" />
    <hkern u1="&#x17b;" u2="G" k="49" />
    <hkern u1="&#x17b;" u2="C" k="49" />
    <hkern u1="&#x17b;" u2="&#x40;" k="49" />
    <hkern u1="&#x17b;" u2="&#x3f;" k="-32" />
    <hkern u1="&#x17b;" u2="&#x2d;" k="64" />
    <hkern u1="&#x17d;" u2="&#x203a;" k="64" />
    <hkern u1="&#x17d;" u2="&#x2039;" k="64" />
    <hkern u1="&#x17d;" u2="&#x2022;" k="64" />
    <hkern u1="&#x17d;" u2="&#x2014;" k="64" />
    <hkern u1="&#x17d;" u2="&#x2013;" k="64" />
    <hkern u1="&#x17d;" u2="&#x161;" k="19" />
    <hkern u1="&#x17d;" u2="&#x15b;" k="19" />
    <hkern u1="&#x17d;" u2="&#x153;" k="29" />
    <hkern u1="&#x17d;" u2="&#x152;" k="49" />
    <hkern u1="&#x17d;" u2="&#x119;" k="29" />
    <hkern u1="&#x17d;" u2="&#x107;" k="29" />
    <hkern u1="&#x17d;" u2="&#x106;" k="49" />
    <hkern u1="&#x17d;" u2="&#xff;" k="34" />
    <hkern u1="&#x17d;" u2="&#xfd;" k="34" />
    <hkern u1="&#x17d;" u2="&#xf8;" k="29" />
    <hkern u1="&#x17d;" u2="&#xf6;" k="29" />
    <hkern u1="&#x17d;" u2="&#xf5;" k="29" />
    <hkern u1="&#x17d;" u2="&#xf4;" k="29" />
    <hkern u1="&#x17d;" u2="&#xf3;" k="29" />
    <hkern u1="&#x17d;" u2="&#xf2;" k="29" />
    <hkern u1="&#x17d;" u2="&#xf0;" k="29" />
    <hkern u1="&#x17d;" u2="&#xeb;" k="29" />
    <hkern u1="&#x17d;" u2="&#xea;" k="29" />
    <hkern u1="&#x17d;" u2="&#xe9;" k="29" />
    <hkern u1="&#x17d;" u2="&#xe8;" k="29" />
    <hkern u1="&#x17d;" u2="&#xe7;" k="29" />
    <hkern u1="&#x17d;" u2="&#xd8;" k="49" />
    <hkern u1="&#x17d;" u2="&#xd6;" k="49" />
    <hkern u1="&#x17d;" u2="&#xd5;" k="49" />
    <hkern u1="&#x17d;" u2="&#xd4;" k="49" />
    <hkern u1="&#x17d;" u2="&#xd3;" k="49" />
    <hkern u1="&#x17d;" u2="&#xd2;" k="49" />
    <hkern u1="&#x17d;" u2="&#xc7;" k="49" />
    <hkern u1="&#x17d;" u2="&#xbb;" k="64" />
    <hkern u1="&#x17d;" u2="&#xb7;" k="64" />
    <hkern u1="&#x17d;" u2="&#xae;" k="49" />
    <hkern u1="&#x17d;" u2="&#xad;" k="64" />
    <hkern u1="&#x17d;" u2="&#xab;" k="64" />
    <hkern u1="&#x17d;" u2="&#xa9;" k="49" />
    <hkern u1="&#x17d;" u2="y" k="34" />
    <hkern u1="&#x17d;" u2="v" k="34" />
    <hkern u1="&#x17d;" u2="s" k="19" />
    <hkern u1="&#x17d;" u2="q" k="29" />
    <hkern u1="&#x17d;" u2="o" k="29" />
    <hkern u1="&#x17d;" u2="e" k="29" />
    <hkern u1="&#x17d;" u2="d" k="29" />
    <hkern u1="&#x17d;" u2="c" k="29" />
    <hkern u1="&#x17d;" u2="Q" k="49" />
    <hkern u1="&#x17d;" u2="O" k="49" />
    <hkern u1="&#x17d;" u2="G" k="49" />
    <hkern u1="&#x17d;" u2="C" k="49" />
    <hkern u1="&#x17d;" u2="&#x40;" k="49" />
    <hkern u1="&#x17d;" u2="&#x3f;" k="-32" />
    <hkern u1="&#x17d;" u2="&#x2d;" k="64" />
    <hkern u1="&#x2013;" u2="&#x2206;" k="67" />
    <hkern u1="&#x2013;" u2="&#x2122;" k="169" />
    <hkern u1="&#x2013;" u2="&#x201e;" k="132" />
    <hkern u1="&#x2013;" u2="&#x201d;" k="169" />
    <hkern u1="&#x2013;" u2="&#x201c;" k="169" />
    <hkern u1="&#x2013;" u2="&#x201a;" k="132" />
    <hkern u1="&#x2013;" u2="&#x2019;" k="169" />
    <hkern u1="&#x2013;" u2="&#x2018;" k="169" />
    <hkern u1="&#x2013;" u2="&#x17d;" k="48" />
    <hkern u1="&#x2013;" u2="&#x17b;" k="48" />
    <hkern u1="&#x2013;" u2="&#x179;" k="48" />
    <hkern u1="&#x2013;" u2="&#x178;" k="160" />
    <hkern u1="&#x2013;" u2="&#x104;" k="67" />
    <hkern u1="&#x2013;" u2="&#xdd;" k="160" />
    <hkern u1="&#x2013;" u2="&#xc6;" k="67" />
    <hkern u1="&#x2013;" u2="&#xc5;" k="67" />
    <hkern u1="&#x2013;" u2="&#xc4;" k="67" />
    <hkern u1="&#x2013;" u2="&#xc3;" k="67" />
    <hkern u1="&#x2013;" u2="&#xc2;" k="67" />
    <hkern u1="&#x2013;" u2="&#xc1;" k="67" />
    <hkern u1="&#x2013;" u2="&#xc0;" k="67" />
    <hkern u1="&#x2013;" u2="&#xba;" k="169" />
    <hkern u1="&#x2013;" u2="&#xb0;" k="169" />
    <hkern u1="&#x2013;" u2="&#xaa;" k="169" />
    <hkern u1="&#x2013;" u2="\" k="116" />
    <hkern u1="&#x2013;" u2="Z" k="48" />
    <hkern u1="&#x2013;" u2="Y" k="160" />
    <hkern u1="&#x2013;" u2="X" k="66" />
    <hkern u1="&#x2013;" u2="W" k="36" />
    <hkern u1="&#x2013;" u2="V" k="116" />
    <hkern u1="&#x2013;" u2="T" k="180" />
    <hkern u1="&#x2013;" u2="A" k="67" />
    <hkern u1="&#x2013;" u2="&#x2f;" k="67" />
    <hkern u1="&#x2013;" u2="&#x2e;" k="132" />
    <hkern u1="&#x2013;" u2="&#x2c;" k="132" />
    <hkern u1="&#x2013;" u2="&#x2a;" k="169" />
    <hkern u1="&#x2013;" u2="&#x27;" k="169" />
    <hkern u1="&#x2013;" u2="&#x26;" k="67" />
    <hkern u1="&#x2013;" u2="&#x22;" k="169" />
    <hkern u1="&#x2014;" u2="&#x2206;" k="67" />
    <hkern u1="&#x2014;" u2="&#x2122;" k="169" />
    <hkern u1="&#x2014;" u2="&#x201e;" k="132" />
    <hkern u1="&#x2014;" u2="&#x201d;" k="169" />
    <hkern u1="&#x2014;" u2="&#x201c;" k="169" />
    <hkern u1="&#x2014;" u2="&#x201a;" k="132" />
    <hkern u1="&#x2014;" u2="&#x2019;" k="169" />
    <hkern u1="&#x2014;" u2="&#x2018;" k="169" />
    <hkern u1="&#x2014;" u2="&#x17d;" k="48" />
    <hkern u1="&#x2014;" u2="&#x17b;" k="48" />
    <hkern u1="&#x2014;" u2="&#x179;" k="48" />
    <hkern u1="&#x2014;" u2="&#x178;" k="160" />
    <hkern u1="&#x2014;" u2="&#x104;" k="67" />
    <hkern u1="&#x2014;" u2="&#xdd;" k="160" />
    <hkern u1="&#x2014;" u2="&#xc6;" k="67" />
    <hkern u1="&#x2014;" u2="&#xc5;" k="67" />
    <hkern u1="&#x2014;" u2="&#xc4;" k="67" />
    <hkern u1="&#x2014;" u2="&#xc3;" k="67" />
    <hkern u1="&#x2014;" u2="&#xc2;" k="67" />
    <hkern u1="&#x2014;" u2="&#xc1;" k="67" />
    <hkern u1="&#x2014;" u2="&#xc0;" k="67" />
    <hkern u1="&#x2014;" u2="&#xba;" k="169" />
    <hkern u1="&#x2014;" u2="&#xb0;" k="169" />
    <hkern u1="&#x2014;" u2="&#xaa;" k="169" />
    <hkern u1="&#x2014;" u2="\" k="116" />
    <hkern u1="&#x2014;" u2="Z" k="48" />
    <hkern u1="&#x2014;" u2="Y" k="160" />
    <hkern u1="&#x2014;" u2="X" k="66" />
    <hkern u1="&#x2014;" u2="W" k="36" />
    <hkern u1="&#x2014;" u2="V" k="116" />
    <hkern u1="&#x2014;" u2="T" k="180" />
    <hkern u1="&#x2014;" u2="A" k="67" />
    <hkern u1="&#x2014;" u2="&#x2f;" k="67" />
    <hkern u1="&#x2014;" u2="&#x2e;" k="132" />
    <hkern u1="&#x2014;" u2="&#x2c;" k="132" />
    <hkern u1="&#x2014;" u2="&#x2a;" k="169" />
    <hkern u1="&#x2014;" u2="&#x27;" k="169" />
    <hkern u1="&#x2014;" u2="&#x26;" k="67" />
    <hkern u1="&#x2014;" u2="&#x22;" k="169" />
    <hkern u1="&#x2018;" u2="&#x2206;" k="191" />
    <hkern u1="&#x2018;" u2="&#x203a;" k="169" />
    <hkern u1="&#x2018;" u2="&#x2039;" k="169" />
    <hkern u1="&#x2018;" u2="&#x2022;" k="169" />
    <hkern u1="&#x2018;" u2="&#x201e;" k="213" />
    <hkern u1="&#x2018;" u2="&#x201a;" k="213" />
    <hkern u1="&#x2018;" u2="&#x2014;" k="169" />
    <hkern u1="&#x2018;" u2="&#x2013;" k="169" />
    <hkern u1="&#x2018;" u2="&#x178;" k="-36" />
    <hkern u1="&#x2018;" u2="&#x153;" k="96" />
    <hkern u1="&#x2018;" u2="&#x152;" k="42" />
    <hkern u1="&#x2018;" u2="&#x119;" k="96" />
    <hkern u1="&#x2018;" u2="&#x107;" k="96" />
    <hkern u1="&#x2018;" u2="&#x106;" k="42" />
    <hkern u1="&#x2018;" u2="&#x105;" k="66" />
    <hkern u1="&#x2018;" u2="&#x104;" k="191" />
    <hkern u1="&#x2018;" u2="&#xf8;" k="96" />
    <hkern u1="&#x2018;" u2="&#xf6;" k="96" />
    <hkern u1="&#x2018;" u2="&#xf5;" k="96" />
    <hkern u1="&#x2018;" u2="&#xf4;" k="96" />
    <hkern u1="&#x2018;" u2="&#xf3;" k="96" />
    <hkern u1="&#x2018;" u2="&#xf2;" k="96" />
    <hkern u1="&#x2018;" u2="&#xf0;" k="96" />
    <hkern u1="&#x2018;" u2="&#xeb;" k="96" />
    <hkern u1="&#x2018;" u2="&#xea;" k="96" />
    <hkern u1="&#x2018;" u2="&#xe9;" k="96" />
    <hkern u1="&#x2018;" u2="&#xe8;" k="96" />
    <hkern u1="&#x2018;" u2="&#xe7;" k="96" />
    <hkern u1="&#x2018;" u2="&#xe6;" k="66" />
    <hkern u1="&#x2018;" u2="&#xe5;" k="66" />
    <hkern u1="&#x2018;" u2="&#xe4;" k="66" />
    <hkern u1="&#x2018;" u2="&#xe3;" k="66" />
    <hkern u1="&#x2018;" u2="&#xe2;" k="66" />
    <hkern u1="&#x2018;" u2="&#xe1;" k="66" />
    <hkern u1="&#x2018;" u2="&#xe0;" k="66" />
    <hkern u1="&#x2018;" u2="&#xdd;" k="-36" />
    <hkern u1="&#x2018;" u2="&#xd8;" k="42" />
    <hkern u1="&#x2018;" u2="&#xd6;" k="42" />
    <hkern u1="&#x2018;" u2="&#xd5;" k="42" />
    <hkern u1="&#x2018;" u2="&#xd4;" k="42" />
    <hkern u1="&#x2018;" u2="&#xd3;" k="42" />
    <hkern u1="&#x2018;" u2="&#xd2;" k="42" />
    <hkern u1="&#x2018;" u2="&#xc7;" k="42" />
    <hkern u1="&#x2018;" u2="&#xc6;" k="191" />
    <hkern u1="&#x2018;" u2="&#xc5;" k="191" />
    <hkern u1="&#x2018;" u2="&#xc4;" k="191" />
    <hkern u1="&#x2018;" u2="&#xc3;" k="191" />
    <hkern u1="&#x2018;" u2="&#xc2;" k="191" />
    <hkern u1="&#x2018;" u2="&#xc1;" k="191" />
    <hkern u1="&#x2018;" u2="&#xc0;" k="191" />
    <hkern u1="&#x2018;" u2="&#xbb;" k="169" />
    <hkern u1="&#x2018;" u2="&#xb7;" k="169" />
    <hkern u1="&#x2018;" u2="&#xae;" k="42" />
    <hkern u1="&#x2018;" u2="&#xad;" k="169" />
    <hkern u1="&#x2018;" u2="&#xab;" k="169" />
    <hkern u1="&#x2018;" u2="&#xa9;" k="42" />
    <hkern u1="&#x2018;" u2="q" k="96" />
    <hkern u1="&#x2018;" u2="o" k="96" />
    <hkern u1="&#x2018;" u2="e" k="96" />
    <hkern u1="&#x2018;" u2="d" k="96" />
    <hkern u1="&#x2018;" u2="c" k="96" />
    <hkern u1="&#x2018;" u2="a" k="66" />
    <hkern u1="&#x2018;" u2="\" k="-44" />
    <hkern u1="&#x2018;" u2="Y" k="-36" />
    <hkern u1="&#x2018;" u2="W" k="-44" />
    <hkern u1="&#x2018;" u2="V" k="-44" />
    <hkern u1="&#x2018;" u2="Q" k="42" />
    <hkern u1="&#x2018;" u2="O" k="42" />
    <hkern u1="&#x2018;" u2="G" k="42" />
    <hkern u1="&#x2018;" u2="C" k="42" />
    <hkern u1="&#x2018;" u2="A" k="191" />
    <hkern u1="&#x2018;" u2="&#x40;" k="42" />
    <hkern u1="&#x2018;" u2="&#x2f;" k="191" />
    <hkern u1="&#x2018;" u2="&#x2e;" k="213" />
    <hkern u1="&#x2018;" u2="&#x2d;" k="169" />
    <hkern u1="&#x2018;" u2="&#x2c;" k="213" />
    <hkern u1="&#x2018;" u2="&#x26;" k="191" />
    <hkern u1="&#x2019;" u2="&#x2206;" k="191" />
    <hkern u1="&#x2019;" u2="&#x203a;" k="169" />
    <hkern u1="&#x2019;" u2="&#x2039;" k="169" />
    <hkern u1="&#x2019;" u2="&#x2022;" k="169" />
    <hkern u1="&#x2019;" u2="&#x201e;" k="213" />
    <hkern u1="&#x2019;" u2="&#x201a;" k="213" />
    <hkern u1="&#x2019;" u2="&#x2014;" k="169" />
    <hkern u1="&#x2019;" u2="&#x2013;" k="169" />
    <hkern u1="&#x2019;" u2="&#x178;" k="-36" />
    <hkern u1="&#x2019;" u2="&#x153;" k="96" />
    <hkern u1="&#x2019;" u2="&#x152;" k="42" />
    <hkern u1="&#x2019;" u2="&#x119;" k="96" />
    <hkern u1="&#x2019;" u2="&#x107;" k="96" />
    <hkern u1="&#x2019;" u2="&#x106;" k="42" />
    <hkern u1="&#x2019;" u2="&#x105;" k="66" />
    <hkern u1="&#x2019;" u2="&#x104;" k="191" />
    <hkern u1="&#x2019;" u2="&#xf8;" k="96" />
    <hkern u1="&#x2019;" u2="&#xf6;" k="96" />
    <hkern u1="&#x2019;" u2="&#xf5;" k="96" />
    <hkern u1="&#x2019;" u2="&#xf4;" k="96" />
    <hkern u1="&#x2019;" u2="&#xf3;" k="96" />
    <hkern u1="&#x2019;" u2="&#xf2;" k="96" />
    <hkern u1="&#x2019;" u2="&#xf0;" k="96" />
    <hkern u1="&#x2019;" u2="&#xeb;" k="96" />
    <hkern u1="&#x2019;" u2="&#xea;" k="96" />
    <hkern u1="&#x2019;" u2="&#xe9;" k="96" />
    <hkern u1="&#x2019;" u2="&#xe8;" k="96" />
    <hkern u1="&#x2019;" u2="&#xe7;" k="96" />
    <hkern u1="&#x2019;" u2="&#xe6;" k="66" />
    <hkern u1="&#x2019;" u2="&#xe5;" k="66" />
    <hkern u1="&#x2019;" u2="&#xe4;" k="66" />
    <hkern u1="&#x2019;" u2="&#xe3;" k="66" />
    <hkern u1="&#x2019;" u2="&#xe2;" k="66" />
    <hkern u1="&#x2019;" u2="&#xe1;" k="66" />
    <hkern u1="&#x2019;" u2="&#xe0;" k="66" />
    <hkern u1="&#x2019;" u2="&#xdd;" k="-36" />
    <hkern u1="&#x2019;" u2="&#xd8;" k="42" />
    <hkern u1="&#x2019;" u2="&#xd6;" k="42" />
    <hkern u1="&#x2019;" u2="&#xd5;" k="42" />
    <hkern u1="&#x2019;" u2="&#xd4;" k="42" />
    <hkern u1="&#x2019;" u2="&#xd3;" k="42" />
    <hkern u1="&#x2019;" u2="&#xd2;" k="42" />
    <hkern u1="&#x2019;" u2="&#xc7;" k="42" />
    <hkern u1="&#x2019;" u2="&#xc6;" k="191" />
    <hkern u1="&#x2019;" u2="&#xc5;" k="191" />
    <hkern u1="&#x2019;" u2="&#xc4;" k="191" />
    <hkern u1="&#x2019;" u2="&#xc3;" k="191" />
    <hkern u1="&#x2019;" u2="&#xc2;" k="191" />
    <hkern u1="&#x2019;" u2="&#xc1;" k="191" />
    <hkern u1="&#x2019;" u2="&#xc0;" k="191" />
    <hkern u1="&#x2019;" u2="&#xbb;" k="169" />
    <hkern u1="&#x2019;" u2="&#xb7;" k="169" />
    <hkern u1="&#x2019;" u2="&#xae;" k="42" />
    <hkern u1="&#x2019;" u2="&#xad;" k="169" />
    <hkern u1="&#x2019;" u2="&#xab;" k="169" />
    <hkern u1="&#x2019;" u2="&#xa9;" k="42" />
    <hkern u1="&#x2019;" u2="q" k="96" />
    <hkern u1="&#x2019;" u2="o" k="96" />
    <hkern u1="&#x2019;" u2="e" k="96" />
    <hkern u1="&#x2019;" u2="d" k="96" />
    <hkern u1="&#x2019;" u2="c" k="96" />
    <hkern u1="&#x2019;" u2="a" k="66" />
    <hkern u1="&#x2019;" u2="\" k="-44" />
    <hkern u1="&#x2019;" u2="Y" k="-36" />
    <hkern u1="&#x2019;" u2="W" k="-44" />
    <hkern u1="&#x2019;" u2="V" k="-44" />
    <hkern u1="&#x2019;" u2="Q" k="42" />
    <hkern u1="&#x2019;" u2="O" k="42" />
    <hkern u1="&#x2019;" u2="G" k="42" />
    <hkern u1="&#x2019;" u2="C" k="42" />
    <hkern u1="&#x2019;" u2="A" k="191" />
    <hkern u1="&#x2019;" u2="&#x40;" k="42" />
    <hkern u1="&#x2019;" u2="&#x2f;" k="191" />
    <hkern u1="&#x2019;" u2="&#x2e;" k="213" />
    <hkern u1="&#x2019;" u2="&#x2d;" k="169" />
    <hkern u1="&#x2019;" u2="&#x2c;" k="213" />
    <hkern u1="&#x2019;" u2="&#x26;" k="191" />
    <hkern u1="&#x201a;" u2="&#x2122;" k="213" />
    <hkern u1="&#x201a;" u2="&#x203a;" k="132" />
    <hkern u1="&#x201a;" u2="&#x2039;" k="132" />
    <hkern u1="&#x201a;" u2="&#x2022;" k="132" />
    <hkern u1="&#x201a;" u2="&#x201d;" k="213" />
    <hkern u1="&#x201a;" u2="&#x201c;" k="213" />
    <hkern u1="&#x201a;" u2="&#x2019;" k="213" />
    <hkern u1="&#x201a;" u2="&#x2018;" k="213" />
    <hkern u1="&#x201a;" u2="&#x2014;" k="132" />
    <hkern u1="&#x201a;" u2="&#x2013;" k="132" />
    <hkern u1="&#x201a;" u2="&#x178;" k="167" />
    <hkern u1="&#x201a;" u2="&#x152;" k="52" />
    <hkern u1="&#x201a;" u2="&#x106;" k="52" />
    <hkern u1="&#x201a;" u2="&#xff;" k="136" />
    <hkern u1="&#x201a;" u2="&#xfd;" k="136" />
    <hkern u1="&#x201a;" u2="&#xdd;" k="167" />
    <hkern u1="&#x201a;" u2="&#xd8;" k="52" />
    <hkern u1="&#x201a;" u2="&#xd6;" k="52" />
    <hkern u1="&#x201a;" u2="&#xd5;" k="52" />
    <hkern u1="&#x201a;" u2="&#xd4;" k="52" />
    <hkern u1="&#x201a;" u2="&#xd3;" k="52" />
    <hkern u1="&#x201a;" u2="&#xd2;" k="52" />
    <hkern u1="&#x201a;" u2="&#xc7;" k="52" />
    <hkern u1="&#x201a;" u2="&#xbb;" k="132" />
    <hkern u1="&#x201a;" u2="&#xba;" k="213" />
    <hkern u1="&#x201a;" u2="&#xb7;" k="132" />
    <hkern u1="&#x201a;" u2="&#xb0;" k="213" />
    <hkern u1="&#x201a;" u2="&#xae;" k="52" />
    <hkern u1="&#x201a;" u2="&#xad;" k="132" />
    <hkern u1="&#x201a;" u2="&#xab;" k="132" />
    <hkern u1="&#x201a;" u2="&#xaa;" k="213" />
    <hkern u1="&#x201a;" u2="&#xa9;" k="52" />
    <hkern u1="&#x201a;" u2="y" k="136" />
    <hkern u1="&#x201a;" u2="w" k="71" />
    <hkern u1="&#x201a;" u2="v" k="136" />
    <hkern u1="&#x201a;" u2="\" k="180" />
    <hkern u1="&#x201a;" u2="Y" k="167" />
    <hkern u1="&#x201a;" u2="W" k="131" />
    <hkern u1="&#x201a;" u2="V" k="180" />
    <hkern u1="&#x201a;" u2="T" k="180" />
    <hkern u1="&#x201a;" u2="Q" k="52" />
    <hkern u1="&#x201a;" u2="O" k="52" />
    <hkern u1="&#x201a;" u2="G" k="52" />
    <hkern u1="&#x201a;" u2="C" k="52" />
    <hkern u1="&#x201a;" u2="&#x40;" k="52" />
    <hkern u1="&#x201a;" u2="&#x2d;" k="132" />
    <hkern u1="&#x201a;" u2="&#x2a;" k="213" />
    <hkern u1="&#x201a;" u2="&#x27;" k="213" />
    <hkern u1="&#x201a;" u2="&#x22;" k="213" />
    <hkern u1="&#x201c;" u2="&#x2206;" k="191" />
    <hkern u1="&#x201c;" u2="&#x203a;" k="169" />
    <hkern u1="&#x201c;" u2="&#x2039;" k="169" />
    <hkern u1="&#x201c;" u2="&#x2022;" k="169" />
    <hkern u1="&#x201c;" u2="&#x201e;" k="213" />
    <hkern u1="&#x201c;" u2="&#x201a;" k="213" />
    <hkern u1="&#x201c;" u2="&#x2014;" k="169" />
    <hkern u1="&#x201c;" u2="&#x2013;" k="169" />
    <hkern u1="&#x201c;" u2="&#x178;" k="-36" />
    <hkern u1="&#x201c;" u2="&#x153;" k="96" />
    <hkern u1="&#x201c;" u2="&#x152;" k="42" />
    <hkern u1="&#x201c;" u2="&#x119;" k="96" />
    <hkern u1="&#x201c;" u2="&#x107;" k="96" />
    <hkern u1="&#x201c;" u2="&#x106;" k="42" />
    <hkern u1="&#x201c;" u2="&#x105;" k="66" />
    <hkern u1="&#x201c;" u2="&#x104;" k="191" />
    <hkern u1="&#x201c;" u2="&#xf8;" k="96" />
    <hkern u1="&#x201c;" u2="&#xf6;" k="96" />
    <hkern u1="&#x201c;" u2="&#xf5;" k="96" />
    <hkern u1="&#x201c;" u2="&#xf4;" k="96" />
    <hkern u1="&#x201c;" u2="&#xf3;" k="96" />
    <hkern u1="&#x201c;" u2="&#xf2;" k="96" />
    <hkern u1="&#x201c;" u2="&#xf0;" k="96" />
    <hkern u1="&#x201c;" u2="&#xeb;" k="96" />
    <hkern u1="&#x201c;" u2="&#xea;" k="96" />
    <hkern u1="&#x201c;" u2="&#xe9;" k="96" />
    <hkern u1="&#x201c;" u2="&#xe8;" k="96" />
    <hkern u1="&#x201c;" u2="&#xe7;" k="96" />
    <hkern u1="&#x201c;" u2="&#xe6;" k="66" />
    <hkern u1="&#x201c;" u2="&#xe5;" k="66" />
    <hkern u1="&#x201c;" u2="&#xe4;" k="66" />
    <hkern u1="&#x201c;" u2="&#xe3;" k="66" />
    <hkern u1="&#x201c;" u2="&#xe2;" k="66" />
    <hkern u1="&#x201c;" u2="&#xe1;" k="66" />
    <hkern u1="&#x201c;" u2="&#xe0;" k="66" />
    <hkern u1="&#x201c;" u2="&#xdd;" k="-36" />
    <hkern u1="&#x201c;" u2="&#xd8;" k="42" />
    <hkern u1="&#x201c;" u2="&#xd6;" k="42" />
    <hkern u1="&#x201c;" u2="&#xd5;" k="42" />
    <hkern u1="&#x201c;" u2="&#xd4;" k="42" />
    <hkern u1="&#x201c;" u2="&#xd3;" k="42" />
    <hkern u1="&#x201c;" u2="&#xd2;" k="42" />
    <hkern u1="&#x201c;" u2="&#xc7;" k="42" />
    <hkern u1="&#x201c;" u2="&#xc6;" k="191" />
    <hkern u1="&#x201c;" u2="&#xc5;" k="191" />
    <hkern u1="&#x201c;" u2="&#xc4;" k="191" />
    <hkern u1="&#x201c;" u2="&#xc3;" k="191" />
    <hkern u1="&#x201c;" u2="&#xc2;" k="191" />
    <hkern u1="&#x201c;" u2="&#xc1;" k="191" />
    <hkern u1="&#x201c;" u2="&#xc0;" k="191" />
    <hkern u1="&#x201c;" u2="&#xbb;" k="169" />
    <hkern u1="&#x201c;" u2="&#xb7;" k="169" />
    <hkern u1="&#x201c;" u2="&#xae;" k="42" />
    <hkern u1="&#x201c;" u2="&#xad;" k="169" />
    <hkern u1="&#x201c;" u2="&#xab;" k="169" />
    <hkern u1="&#x201c;" u2="&#xa9;" k="42" />
    <hkern u1="&#x201c;" u2="q" k="96" />
    <hkern u1="&#x201c;" u2="o" k="96" />
    <hkern u1="&#x201c;" u2="e" k="96" />
    <hkern u1="&#x201c;" u2="d" k="96" />
    <hkern u1="&#x201c;" u2="c" k="96" />
    <hkern u1="&#x201c;" u2="a" k="66" />
    <hkern u1="&#x201c;" u2="\" k="-44" />
    <hkern u1="&#x201c;" u2="Y" k="-36" />
    <hkern u1="&#x201c;" u2="W" k="-44" />
    <hkern u1="&#x201c;" u2="V" k="-44" />
    <hkern u1="&#x201c;" u2="Q" k="42" />
    <hkern u1="&#x201c;" u2="O" k="42" />
    <hkern u1="&#x201c;" u2="G" k="42" />
    <hkern u1="&#x201c;" u2="C" k="42" />
    <hkern u1="&#x201c;" u2="A" k="191" />
    <hkern u1="&#x201c;" u2="&#x40;" k="42" />
    <hkern u1="&#x201c;" u2="&#x2f;" k="191" />
    <hkern u1="&#x201c;" u2="&#x2e;" k="213" />
    <hkern u1="&#x201c;" u2="&#x2d;" k="169" />
    <hkern u1="&#x201c;" u2="&#x2c;" k="213" />
    <hkern u1="&#x201c;" u2="&#x26;" k="191" />
    <hkern u1="&#x201d;" u2="&#x2206;" k="191" />
    <hkern u1="&#x201d;" u2="&#x203a;" k="169" />
    <hkern u1="&#x201d;" u2="&#x2039;" k="169" />
    <hkern u1="&#x201d;" u2="&#x2022;" k="169" />
    <hkern u1="&#x201d;" u2="&#x201e;" k="213" />
    <hkern u1="&#x201d;" u2="&#x201a;" k="213" />
    <hkern u1="&#x201d;" u2="&#x2014;" k="169" />
    <hkern u1="&#x201d;" u2="&#x2013;" k="169" />
    <hkern u1="&#x201d;" u2="&#x178;" k="-36" />
    <hkern u1="&#x201d;" u2="&#x153;" k="96" />
    <hkern u1="&#x201d;" u2="&#x152;" k="42" />
    <hkern u1="&#x201d;" u2="&#x119;" k="96" />
    <hkern u1="&#x201d;" u2="&#x107;" k="96" />
    <hkern u1="&#x201d;" u2="&#x106;" k="42" />
    <hkern u1="&#x201d;" u2="&#x105;" k="66" />
    <hkern u1="&#x201d;" u2="&#x104;" k="191" />
    <hkern u1="&#x201d;" u2="&#xf8;" k="96" />
    <hkern u1="&#x201d;" u2="&#xf6;" k="96" />
    <hkern u1="&#x201d;" u2="&#xf5;" k="96" />
    <hkern u1="&#x201d;" u2="&#xf4;" k="96" />
    <hkern u1="&#x201d;" u2="&#xf3;" k="96" />
    <hkern u1="&#x201d;" u2="&#xf2;" k="96" />
    <hkern u1="&#x201d;" u2="&#xf0;" k="96" />
    <hkern u1="&#x201d;" u2="&#xeb;" k="96" />
    <hkern u1="&#x201d;" u2="&#xea;" k="96" />
    <hkern u1="&#x201d;" u2="&#xe9;" k="96" />
    <hkern u1="&#x201d;" u2="&#xe8;" k="96" />
    <hkern u1="&#x201d;" u2="&#xe7;" k="96" />
    <hkern u1="&#x201d;" u2="&#xe6;" k="66" />
    <hkern u1="&#x201d;" u2="&#xe5;" k="66" />
    <hkern u1="&#x201d;" u2="&#xe4;" k="66" />
    <hkern u1="&#x201d;" u2="&#xe3;" k="66" />
    <hkern u1="&#x201d;" u2="&#xe2;" k="66" />
    <hkern u1="&#x201d;" u2="&#xe1;" k="66" />
    <hkern u1="&#x201d;" u2="&#xe0;" k="66" />
    <hkern u1="&#x201d;" u2="&#xdd;" k="-36" />
    <hkern u1="&#x201d;" u2="&#xd8;" k="42" />
    <hkern u1="&#x201d;" u2="&#xd6;" k="42" />
    <hkern u1="&#x201d;" u2="&#xd5;" k="42" />
    <hkern u1="&#x201d;" u2="&#xd4;" k="42" />
    <hkern u1="&#x201d;" u2="&#xd3;" k="42" />
    <hkern u1="&#x201d;" u2="&#xd2;" k="42" />
    <hkern u1="&#x201d;" u2="&#xc7;" k="42" />
    <hkern u1="&#x201d;" u2="&#xc6;" k="191" />
    <hkern u1="&#x201d;" u2="&#xc5;" k="191" />
    <hkern u1="&#x201d;" u2="&#xc4;" k="191" />
    <hkern u1="&#x201d;" u2="&#xc3;" k="191" />
    <hkern u1="&#x201d;" u2="&#xc2;" k="191" />
    <hkern u1="&#x201d;" u2="&#xc1;" k="191" />
    <hkern u1="&#x201d;" u2="&#xc0;" k="191" />
    <hkern u1="&#x201d;" u2="&#xbb;" k="169" />
    <hkern u1="&#x201d;" u2="&#xb7;" k="169" />
    <hkern u1="&#x201d;" u2="&#xae;" k="42" />
    <hkern u1="&#x201d;" u2="&#xad;" k="169" />
    <hkern u1="&#x201d;" u2="&#xab;" k="169" />
    <hkern u1="&#x201d;" u2="&#xa9;" k="42" />
    <hkern u1="&#x201d;" u2="q" k="96" />
    <hkern u1="&#x201d;" u2="o" k="96" />
    <hkern u1="&#x201d;" u2="e" k="96" />
    <hkern u1="&#x201d;" u2="d" k="96" />
    <hkern u1="&#x201d;" u2="c" k="96" />
    <hkern u1="&#x201d;" u2="a" k="66" />
    <hkern u1="&#x201d;" u2="\" k="-44" />
    <hkern u1="&#x201d;" u2="Y" k="-36" />
    <hkern u1="&#x201d;" u2="W" k="-44" />
    <hkern u1="&#x201d;" u2="V" k="-44" />
    <hkern u1="&#x201d;" u2="Q" k="42" />
    <hkern u1="&#x201d;" u2="O" k="42" />
    <hkern u1="&#x201d;" u2="G" k="42" />
    <hkern u1="&#x201d;" u2="C" k="42" />
    <hkern u1="&#x201d;" u2="A" k="191" />
    <hkern u1="&#x201d;" u2="&#x40;" k="42" />
    <hkern u1="&#x201d;" u2="&#x2f;" k="191" />
    <hkern u1="&#x201d;" u2="&#x2e;" k="213" />
    <hkern u1="&#x201d;" u2="&#x2d;" k="169" />
    <hkern u1="&#x201d;" u2="&#x2c;" k="213" />
    <hkern u1="&#x201d;" u2="&#x26;" k="191" />
    <hkern u1="&#x201e;" u2="&#x2122;" k="213" />
    <hkern u1="&#x201e;" u2="&#x203a;" k="132" />
    <hkern u1="&#x201e;" u2="&#x2039;" k="132" />
    <hkern u1="&#x201e;" u2="&#x2022;" k="132" />
    <hkern u1="&#x201e;" u2="&#x201d;" k="213" />
    <hkern u1="&#x201e;" u2="&#x201c;" k="213" />
    <hkern u1="&#x201e;" u2="&#x2019;" k="213" />
    <hkern u1="&#x201e;" u2="&#x2018;" k="213" />
    <hkern u1="&#x201e;" u2="&#x2014;" k="132" />
    <hkern u1="&#x201e;" u2="&#x2013;" k="132" />
    <hkern u1="&#x201e;" u2="&#x178;" k="167" />
    <hkern u1="&#x201e;" u2="&#x152;" k="52" />
    <hkern u1="&#x201e;" u2="&#x106;" k="52" />
    <hkern u1="&#x201e;" u2="&#xff;" k="136" />
    <hkern u1="&#x201e;" u2="&#xfd;" k="136" />
    <hkern u1="&#x201e;" u2="&#xdd;" k="167" />
    <hkern u1="&#x201e;" u2="&#xd8;" k="52" />
    <hkern u1="&#x201e;" u2="&#xd6;" k="52" />
    <hkern u1="&#x201e;" u2="&#xd5;" k="52" />
    <hkern u1="&#x201e;" u2="&#xd4;" k="52" />
    <hkern u1="&#x201e;" u2="&#xd3;" k="52" />
    <hkern u1="&#x201e;" u2="&#xd2;" k="52" />
    <hkern u1="&#x201e;" u2="&#xc7;" k="52" />
    <hkern u1="&#x201e;" u2="&#xbb;" k="132" />
    <hkern u1="&#x201e;" u2="&#xba;" k="213" />
    <hkern u1="&#x201e;" u2="&#xb7;" k="132" />
    <hkern u1="&#x201e;" u2="&#xb0;" k="213" />
    <hkern u1="&#x201e;" u2="&#xae;" k="52" />
    <hkern u1="&#x201e;" u2="&#xad;" k="132" />
    <hkern u1="&#x201e;" u2="&#xab;" k="132" />
    <hkern u1="&#x201e;" u2="&#xaa;" k="213" />
    <hkern u1="&#x201e;" u2="&#xa9;" k="52" />
    <hkern u1="&#x201e;" u2="y" k="136" />
    <hkern u1="&#x201e;" u2="w" k="71" />
    <hkern u1="&#x201e;" u2="v" k="136" />
    <hkern u1="&#x201e;" u2="\" k="180" />
    <hkern u1="&#x201e;" u2="Y" k="167" />
    <hkern u1="&#x201e;" u2="W" k="131" />
    <hkern u1="&#x201e;" u2="V" k="180" />
    <hkern u1="&#x201e;" u2="T" k="180" />
    <hkern u1="&#x201e;" u2="Q" k="52" />
    <hkern u1="&#x201e;" u2="O" k="52" />
    <hkern u1="&#x201e;" u2="G" k="52" />
    <hkern u1="&#x201e;" u2="C" k="52" />
    <hkern u1="&#x201e;" u2="&#x40;" k="52" />
    <hkern u1="&#x201e;" u2="&#x2d;" k="132" />
    <hkern u1="&#x201e;" u2="&#x2a;" k="213" />
    <hkern u1="&#x201e;" u2="&#x27;" k="213" />
    <hkern u1="&#x201e;" u2="&#x22;" k="213" />
    <hkern u1="&#x2022;" u2="&#x2206;" k="67" />
    <hkern u1="&#x2022;" u2="&#x2122;" k="169" />
    <hkern u1="&#x2022;" u2="&#x201e;" k="132" />
    <hkern u1="&#x2022;" u2="&#x201d;" k="169" />
    <hkern u1="&#x2022;" u2="&#x201c;" k="169" />
    <hkern u1="&#x2022;" u2="&#x201a;" k="132" />
    <hkern u1="&#x2022;" u2="&#x2019;" k="169" />
    <hkern u1="&#x2022;" u2="&#x2018;" k="169" />
    <hkern u1="&#x2022;" u2="&#x17d;" k="48" />
    <hkern u1="&#x2022;" u2="&#x17b;" k="48" />
    <hkern u1="&#x2022;" u2="&#x179;" k="48" />
    <hkern u1="&#x2022;" u2="&#x178;" k="160" />
    <hkern u1="&#x2022;" u2="&#x104;" k="67" />
    <hkern u1="&#x2022;" u2="&#xdd;" k="160" />
    <hkern u1="&#x2022;" u2="&#xc6;" k="67" />
    <hkern u1="&#x2022;" u2="&#xc5;" k="67" />
    <hkern u1="&#x2022;" u2="&#xc4;" k="67" />
    <hkern u1="&#x2022;" u2="&#xc3;" k="67" />
    <hkern u1="&#x2022;" u2="&#xc2;" k="67" />
    <hkern u1="&#x2022;" u2="&#xc1;" k="67" />
    <hkern u1="&#x2022;" u2="&#xc0;" k="67" />
    <hkern u1="&#x2022;" u2="&#xba;" k="169" />
    <hkern u1="&#x2022;" u2="&#xb0;" k="169" />
    <hkern u1="&#x2022;" u2="&#xaa;" k="169" />
    <hkern u1="&#x2022;" u2="\" k="116" />
    <hkern u1="&#x2022;" u2="Z" k="48" />
    <hkern u1="&#x2022;" u2="Y" k="160" />
    <hkern u1="&#x2022;" u2="X" k="66" />
    <hkern u1="&#x2022;" u2="W" k="36" />
    <hkern u1="&#x2022;" u2="V" k="116" />
    <hkern u1="&#x2022;" u2="T" k="180" />
    <hkern u1="&#x2022;" u2="A" k="67" />
    <hkern u1="&#x2022;" u2="&#x2f;" k="67" />
    <hkern u1="&#x2022;" u2="&#x2e;" k="132" />
    <hkern u1="&#x2022;" u2="&#x2c;" k="132" />
    <hkern u1="&#x2022;" u2="&#x2a;" k="169" />
    <hkern u1="&#x2022;" u2="&#x27;" k="169" />
    <hkern u1="&#x2022;" u2="&#x26;" k="67" />
    <hkern u1="&#x2022;" u2="&#x22;" k="169" />
    <hkern u1="&#x2039;" u2="&#x2206;" k="67" />
    <hkern u1="&#x2039;" u2="&#x2122;" k="169" />
    <hkern u1="&#x2039;" u2="&#x201e;" k="132" />
    <hkern u1="&#x2039;" u2="&#x201d;" k="169" />
    <hkern u1="&#x2039;" u2="&#x201c;" k="169" />
    <hkern u1="&#x2039;" u2="&#x201a;" k="132" />
    <hkern u1="&#x2039;" u2="&#x2019;" k="169" />
    <hkern u1="&#x2039;" u2="&#x2018;" k="169" />
    <hkern u1="&#x2039;" u2="&#x17d;" k="48" />
    <hkern u1="&#x2039;" u2="&#x17b;" k="48" />
    <hkern u1="&#x2039;" u2="&#x179;" k="48" />
    <hkern u1="&#x2039;" u2="&#x178;" k="160" />
    <hkern u1="&#x2039;" u2="&#x104;" k="67" />
    <hkern u1="&#x2039;" u2="&#xdd;" k="160" />
    <hkern u1="&#x2039;" u2="&#xc6;" k="67" />
    <hkern u1="&#x2039;" u2="&#xc5;" k="67" />
    <hkern u1="&#x2039;" u2="&#xc4;" k="67" />
    <hkern u1="&#x2039;" u2="&#xc3;" k="67" />
    <hkern u1="&#x2039;" u2="&#xc2;" k="67" />
    <hkern u1="&#x2039;" u2="&#xc1;" k="67" />
    <hkern u1="&#x2039;" u2="&#xc0;" k="67" />
    <hkern u1="&#x2039;" u2="&#xba;" k="169" />
    <hkern u1="&#x2039;" u2="&#xb0;" k="169" />
    <hkern u1="&#x2039;" u2="&#xaa;" k="169" />
    <hkern u1="&#x2039;" u2="\" k="116" />
    <hkern u1="&#x2039;" u2="Z" k="48" />
    <hkern u1="&#x2039;" u2="Y" k="160" />
    <hkern u1="&#x2039;" u2="X" k="66" />
    <hkern u1="&#x2039;" u2="W" k="36" />
    <hkern u1="&#x2039;" u2="V" k="116" />
    <hkern u1="&#x2039;" u2="T" k="180" />
    <hkern u1="&#x2039;" u2="A" k="67" />
    <hkern u1="&#x2039;" u2="&#x2f;" k="67" />
    <hkern u1="&#x2039;" u2="&#x2e;" k="132" />
    <hkern u1="&#x2039;" u2="&#x2c;" k="132" />
    <hkern u1="&#x2039;" u2="&#x2a;" k="169" />
    <hkern u1="&#x2039;" u2="&#x27;" k="169" />
    <hkern u1="&#x2039;" u2="&#x26;" k="67" />
    <hkern u1="&#x2039;" u2="&#x22;" k="169" />
    <hkern u1="&#x203a;" u2="&#x2206;" k="67" />
    <hkern u1="&#x203a;" u2="&#x2122;" k="169" />
    <hkern u1="&#x203a;" u2="&#x201e;" k="132" />
    <hkern u1="&#x203a;" u2="&#x201d;" k="169" />
    <hkern u1="&#x203a;" u2="&#x201c;" k="169" />
    <hkern u1="&#x203a;" u2="&#x201a;" k="132" />
    <hkern u1="&#x203a;" u2="&#x2019;" k="169" />
    <hkern u1="&#x203a;" u2="&#x2018;" k="169" />
    <hkern u1="&#x203a;" u2="&#x17d;" k="48" />
    <hkern u1="&#x203a;" u2="&#x17b;" k="48" />
    <hkern u1="&#x203a;" u2="&#x179;" k="48" />
    <hkern u1="&#x203a;" u2="&#x178;" k="160" />
    <hkern u1="&#x203a;" u2="&#x104;" k="67" />
    <hkern u1="&#x203a;" u2="&#xdd;" k="160" />
    <hkern u1="&#x203a;" u2="&#xc6;" k="67" />
    <hkern u1="&#x203a;" u2="&#xc5;" k="67" />
    <hkern u1="&#x203a;" u2="&#xc4;" k="67" />
    <hkern u1="&#x203a;" u2="&#xc3;" k="67" />
    <hkern u1="&#x203a;" u2="&#xc2;" k="67" />
    <hkern u1="&#x203a;" u2="&#xc1;" k="67" />
    <hkern u1="&#x203a;" u2="&#xc0;" k="67" />
    <hkern u1="&#x203a;" u2="&#xba;" k="169" />
    <hkern u1="&#x203a;" u2="&#xb0;" k="169" />
    <hkern u1="&#x203a;" u2="&#xaa;" k="169" />
    <hkern u1="&#x203a;" u2="\" k="116" />
    <hkern u1="&#x203a;" u2="Z" k="48" />
    <hkern u1="&#x203a;" u2="Y" k="160" />
    <hkern u1="&#x203a;" u2="X" k="66" />
    <hkern u1="&#x203a;" u2="W" k="36" />
    <hkern u1="&#x203a;" u2="V" k="116" />
    <hkern u1="&#x203a;" u2="T" k="180" />
    <hkern u1="&#x203a;" u2="A" k="67" />
    <hkern u1="&#x203a;" u2="&#x2f;" k="67" />
    <hkern u1="&#x203a;" u2="&#x2e;" k="132" />
    <hkern u1="&#x203a;" u2="&#x2c;" k="132" />
    <hkern u1="&#x203a;" u2="&#x2a;" k="169" />
    <hkern u1="&#x203a;" u2="&#x27;" k="169" />
    <hkern u1="&#x203a;" u2="&#x26;" k="67" />
    <hkern u1="&#x203a;" u2="&#x22;" k="169" />
    <hkern u1="&#x2122;" u2="&#x2206;" k="191" />
    <hkern u1="&#x2122;" u2="&#x203a;" k="169" />
    <hkern u1="&#x2122;" u2="&#x2039;" k="169" />
    <hkern u1="&#x2122;" u2="&#x2022;" k="169" />
    <hkern u1="&#x2122;" u2="&#x201e;" k="213" />
    <hkern u1="&#x2122;" u2="&#x201a;" k="213" />
    <hkern u1="&#x2122;" u2="&#x2014;" k="169" />
    <hkern u1="&#x2122;" u2="&#x2013;" k="169" />
    <hkern u1="&#x2122;" u2="&#x178;" k="-36" />
    <hkern u1="&#x2122;" u2="&#x153;" k="96" />
    <hkern u1="&#x2122;" u2="&#x152;" k="42" />
    <hkern u1="&#x2122;" u2="&#x119;" k="96" />
    <hkern u1="&#x2122;" u2="&#x107;" k="96" />
    <hkern u1="&#x2122;" u2="&#x106;" k="42" />
    <hkern u1="&#x2122;" u2="&#x105;" k="66" />
    <hkern u1="&#x2122;" u2="&#x104;" k="191" />
    <hkern u1="&#x2122;" u2="&#xf8;" k="96" />
    <hkern u1="&#x2122;" u2="&#xf6;" k="96" />
    <hkern u1="&#x2122;" u2="&#xf5;" k="96" />
    <hkern u1="&#x2122;" u2="&#xf4;" k="96" />
    <hkern u1="&#x2122;" u2="&#xf3;" k="96" />
    <hkern u1="&#x2122;" u2="&#xf2;" k="96" />
    <hkern u1="&#x2122;" u2="&#xf0;" k="96" />
    <hkern u1="&#x2122;" u2="&#xeb;" k="96" />
    <hkern u1="&#x2122;" u2="&#xea;" k="96" />
    <hkern u1="&#x2122;" u2="&#xe9;" k="96" />
    <hkern u1="&#x2122;" u2="&#xe8;" k="96" />
    <hkern u1="&#x2122;" u2="&#xe7;" k="96" />
    <hkern u1="&#x2122;" u2="&#xe6;" k="66" />
    <hkern u1="&#x2122;" u2="&#xe5;" k="66" />
    <hkern u1="&#x2122;" u2="&#xe4;" k="66" />
    <hkern u1="&#x2122;" u2="&#xe3;" k="66" />
    <hkern u1="&#x2122;" u2="&#xe2;" k="66" />
    <hkern u1="&#x2122;" u2="&#xe1;" k="66" />
    <hkern u1="&#x2122;" u2="&#xe0;" k="66" />
    <hkern u1="&#x2122;" u2="&#xdd;" k="-36" />
    <hkern u1="&#x2122;" u2="&#xd8;" k="42" />
    <hkern u1="&#x2122;" u2="&#xd6;" k="42" />
    <hkern u1="&#x2122;" u2="&#xd5;" k="42" />
    <hkern u1="&#x2122;" u2="&#xd4;" k="42" />
    <hkern u1="&#x2122;" u2="&#xd3;" k="42" />
    <hkern u1="&#x2122;" u2="&#xd2;" k="42" />
    <hkern u1="&#x2122;" u2="&#xc7;" k="42" />
    <hkern u1="&#x2122;" u2="&#xc6;" k="191" />
    <hkern u1="&#x2122;" u2="&#xc5;" k="191" />
    <hkern u1="&#x2122;" u2="&#xc4;" k="191" />
    <hkern u1="&#x2122;" u2="&#xc3;" k="191" />
    <hkern u1="&#x2122;" u2="&#xc2;" k="191" />
    <hkern u1="&#x2122;" u2="&#xc1;" k="191" />
    <hkern u1="&#x2122;" u2="&#xc0;" k="191" />
    <hkern u1="&#x2122;" u2="&#xbb;" k="169" />
    <hkern u1="&#x2122;" u2="&#xb7;" k="169" />
    <hkern u1="&#x2122;" u2="&#xae;" k="42" />
    <hkern u1="&#x2122;" u2="&#xad;" k="169" />
    <hkern u1="&#x2122;" u2="&#xab;" k="169" />
    <hkern u1="&#x2122;" u2="&#xa9;" k="42" />
    <hkern u1="&#x2122;" u2="q" k="96" />
    <hkern u1="&#x2122;" u2="o" k="96" />
    <hkern u1="&#x2122;" u2="e" k="96" />
    <hkern u1="&#x2122;" u2="d" k="96" />
    <hkern u1="&#x2122;" u2="c" k="96" />
    <hkern u1="&#x2122;" u2="a" k="66" />
    <hkern u1="&#x2122;" u2="\" k="-44" />
    <hkern u1="&#x2122;" u2="Y" k="-36" />
    <hkern u1="&#x2122;" u2="W" k="-44" />
    <hkern u1="&#x2122;" u2="V" k="-44" />
    <hkern u1="&#x2122;" u2="Q" k="42" />
    <hkern u1="&#x2122;" u2="O" k="42" />
    <hkern u1="&#x2122;" u2="G" k="42" />
    <hkern u1="&#x2122;" u2="C" k="42" />
    <hkern u1="&#x2122;" u2="A" k="191" />
    <hkern u1="&#x2122;" u2="&#x40;" k="42" />
    <hkern u1="&#x2122;" u2="&#x2f;" k="191" />
    <hkern u1="&#x2122;" u2="&#x2e;" k="213" />
    <hkern u1="&#x2122;" u2="&#x2d;" k="169" />
    <hkern u1="&#x2122;" u2="&#x2c;" k="213" />
    <hkern u1="&#x2122;" u2="&#x26;" k="191" />
    <hkern u1="&#x2206;" u2="&#x2122;" k="191" />
    <hkern u1="&#x2206;" u2="&#x203a;" k="67" />
    <hkern u1="&#x2206;" u2="&#x2039;" k="67" />
    <hkern u1="&#x2206;" u2="&#x2022;" k="67" />
    <hkern u1="&#x2206;" u2="&#x201d;" k="191" />
    <hkern u1="&#x2206;" u2="&#x201c;" k="191" />
    <hkern u1="&#x2206;" u2="&#x2019;" k="191" />
    <hkern u1="&#x2206;" u2="&#x2018;" k="191" />
    <hkern u1="&#x2206;" u2="&#x2014;" k="67" />
    <hkern u1="&#x2206;" u2="&#x2013;" k="67" />
    <hkern u1="&#x2206;" u2="&#x178;" k="182" />
    <hkern u1="&#x2206;" u2="&#x152;" k="51" />
    <hkern u1="&#x2206;" u2="&#x106;" k="51" />
    <hkern u1="&#x2206;" u2="&#xff;" k="91" />
    <hkern u1="&#x2206;" u2="&#xfd;" k="91" />
    <hkern u1="&#x2206;" u2="&#xdd;" k="182" />
    <hkern u1="&#x2206;" u2="&#xdc;" k="52" />
    <hkern u1="&#x2206;" u2="&#xdb;" k="52" />
    <hkern u1="&#x2206;" u2="&#xda;" k="52" />
    <hkern u1="&#x2206;" u2="&#xd9;" k="52" />
    <hkern u1="&#x2206;" u2="&#xd8;" k="51" />
    <hkern u1="&#x2206;" u2="&#xd6;" k="51" />
    <hkern u1="&#x2206;" u2="&#xd5;" k="51" />
    <hkern u1="&#x2206;" u2="&#xd4;" k="51" />
    <hkern u1="&#x2206;" u2="&#xd3;" k="51" />
    <hkern u1="&#x2206;" u2="&#xd2;" k="51" />
    <hkern u1="&#x2206;" u2="&#xc7;" k="51" />
    <hkern u1="&#x2206;" u2="&#xbb;" k="67" />
    <hkern u1="&#x2206;" u2="&#xba;" k="191" />
    <hkern u1="&#x2206;" u2="&#xb9;" k="202" />
    <hkern u1="&#x2206;" u2="&#xb7;" k="67" />
    <hkern u1="&#x2206;" u2="&#xb3;" k="202" />
    <hkern u1="&#x2206;" u2="&#xb2;" k="202" />
    <hkern u1="&#x2206;" u2="&#xb0;" k="191" />
    <hkern u1="&#x2206;" u2="&#xae;" k="51" />
    <hkern u1="&#x2206;" u2="&#xad;" k="67" />
    <hkern u1="&#x2206;" u2="&#xab;" k="67" />
    <hkern u1="&#x2206;" u2="&#xaa;" k="191" />
    <hkern u1="&#x2206;" u2="&#xa9;" k="51" />
    <hkern u1="&#x2206;" u2="y" k="91" />
    <hkern u1="&#x2206;" u2="v" k="91" />
    <hkern u1="&#x2206;" u2="\" k="169" />
    <hkern u1="&#x2206;" u2="Y" k="182" />
    <hkern u1="&#x2206;" u2="W" k="102" />
    <hkern u1="&#x2206;" u2="V" k="169" />
    <hkern u1="&#x2206;" u2="U" k="52" />
    <hkern u1="&#x2206;" u2="T" k="147" />
    <hkern u1="&#x2206;" u2="Q" k="51" />
    <hkern u1="&#x2206;" u2="O" k="51" />
    <hkern u1="&#x2206;" u2="J" k="-56" />
    <hkern u1="&#x2206;" u2="G" k="51" />
    <hkern u1="&#x2206;" u2="C" k="51" />
    <hkern u1="&#x2206;" u2="&#x40;" k="51" />
    <hkern u1="&#x2206;" u2="&#x3f;" k="63" />
    <hkern u1="&#x2206;" u2="&#x2d;" k="67" />
    <hkern u1="&#x2206;" u2="&#x2a;" k="191" />
    <hkern u1="&#x2206;" u2="&#x27;" k="191" />
    <hkern u1="&#x2206;" u2="&#x22;" k="191" />
  </font>
</defs></svg>
