<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20161116 at Tue Jan 14 10:17:04 2014
 By <PERSON>rting
Copyright (c) 2010 by Ryoichi Tsunekawa. All rights reserved.
</metadata>
<defs>
<font id="BebasNeueBold" horiz-adv-x="396" >
  <font-face 
    font-family="Bebas Neue Bold"
    font-weight="700"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 11 6 6 2 2 2 5 2 1"
    ascent="750"
    descent="-250"
    x-height="700"
    cap-height="700"
    bbox="-137 -179 1000 894"
    underline-thickness="50"
    underline-position="-110"
    unicode-range="U+000D-2248"
  />
<missing-glyph horiz-adv-x="548" 
d="M50 700h448v-700h-448v700zM339 615l-64 -195l-63 195h-86l87 -258l-93 -272h77l69 210l68 -210h88l-93 272l87 258h-77z" />
    <glyph glyph-name=".notdef" horiz-adv-x="548" 
d="M50 700h448v-700h-448v700zM339 615l-64 -195l-63 195h-86l87 -258l-93 -272h77l69 210l68 -210h88l-93 272l87 258h-77z" />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" unicode="&#xd;" horiz-adv-x="155" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="155" 
 />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="410" 
d="M219 200h-65l-21 -200h-90l21 200h-49l8 78h49l19 183h-50l8 78h50l17 161h90l-17 -161h65l17 161h90l-17 -161h51l-8 -78h-51l-19 -183h51l-8 -78h-51l-21 -200h-90zM162 278h65l19 183h-65z" />
    <glyph glyph-name="dollar" unicode="$" 
d="M37 522q0 71 29 114.5t87 56.5v52h90v-52q59 -12 88.5 -55.5t29.5 -115.5v-22h-104v29q0 38 -14.5 53.5t-40.5 15.5t-40.5 -15.5t-14.5 -53.5q0 -36 16 -62t40 -48.5t51.5 -44t51.5 -48.5t40 -62.5t16 -85.5q0 -72 -30 -116t-89 -56v-51h-90v51q-60 12 -89.5 56
t-29.5 116v48h104v-55q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53q0 36 -16 62t-40 48.5t-51.5 44t-51.5 48.5t-40 62.5t-16 85.5z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="627" 
d="M104 358q0 -44 36 -44q17 0 26.5 10.5t9.5 33.5v240q0 23 -9.5 33.5t-26.5 10.5q-36 0 -36 -44v-240zM34 593q0 54 27 83t79 29t79 -29t27 -83v-230q0 -54 -27 -83t-79 -29t-79 29t-27 83v230zM416 700h65l-276 -700h-65zM451 102q0 -44 36 -44q17 0 26.5 10.5t9.5 33.5
v240q0 23 -9.5 33.5t-26.5 10.5q-36 0 -36 -44v-240zM381 337q0 54 27 83t79 29t79 -29t27 -83v-230q0 -54 -27 -83t-79 -29t-79 29t-27 83v230z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="414" 
d="M41 223q0 54 17.5 91.5t58.5 55.5q-76 32 -76 144v13q0 85 40.5 129t123.5 44h126v-100h-124q-26 0 -41 -15t-15 -53v-43q0 -42 17.5 -58t48.5 -16h48v78h110v-78h29v-100h-29v-230q0 -26 2 -45.5t10 -39.5h-112q-4 11 -6 21.5t-4 33.5q-29 -63 -102 -63q-61 0 -91.5 41
t-30.5 119v71zM151 161q0 -38 15.5 -53.5t41.5 -15.5q25 0 40 14t17 47v162h-42q-38 0 -55 -20t-17 -69v-65z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="180" 
d="M37 700h106l-16 -206h-75z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="251" 
d="M235 700v-90h-27q-26 0 -38.5 -12.5t-12.5 -50.5v-394q0 -38 12.5 -50.5t38.5 -12.5h27v-90h-34q-41 0 -70.5 8t-48 26.5t-27 47.5t-8.5 71v394q0 42 8.5 71t27 47.5t48 26.5t70.5 8h34z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="251" 
d="M50 700q82 0 118 -34.5t36 -118.5v-394q0 -84 -36 -118.5t-118 -34.5h-34v90h27q26 0 38.5 12.5t12.5 50.5v394q0 38 -12.5 50.5t-38.5 12.5h-27v90h34z" />
    <glyph glyph-name="asterisk" unicode="*" 
d="M179 535l-26 165h90l-26 -165l149 75l28 -85l-164 -28l117 -117l-72 -53l-77 148l-77 -148l-72 53l117 117l-164 28l28 85z" />
    <glyph glyph-name="plus" unicode="+" 
d="M237 389h134v-78h-134v-137h-78v137h-134v78h134v135h78v-135z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="186" 
d="M40 0v106h106v-95l-48 -111h-45l29 100h-42z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="270" 
d="M35 400h200v-100h-200v100z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="186" 
d="M146 106v-106h-106v106h106z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="381" 
d="M281 700h95l-276 -700h-95z" />
    <glyph glyph-name="zero" unicode="0" 
d="M141 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v378q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378zM31 532q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-364q0 -85 -43 -130.5t-124 -45.5t-124 45.5t-43 130.5v364z" />
    <glyph glyph-name="one" unicode="1" 
d="M108 612q32 0 52.5 7t33.5 19t21 28t14 34h74v-700h-110v534h-85v78z" />
    <glyph glyph-name="two" unicode="2" 
d="M258 528q0 47 -15.5 63.5t-41.5 16.5t-41.5 -15.5t-15.5 -53.5v-75h-104v68q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5q0 -55 -16 -99.5t-40.5 -81t-53 -67.5t-53 -59t-40.5 -54.5t-16 -55.5q0 -10 1 -15h208v-100h-318v86q0 51 16 90t40.5 71.5t52.5 61
t52.5 60.5t40.5 70t16 89z" />
    <glyph glyph-name="three" unicode="3" 
d="M252 528q0 47 -15.5 63.5t-41.5 16.5t-41.5 -15.5t-15.5 -53.5v-45h-104v38q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5v-18q0 -112 -77 -144q42 -18 59.5 -55.5t17.5 -91.5v-55q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v58h104v-65
q0 -38 15.5 -53.5t41.5 -15.5t41.5 16t15.5 63v55q0 49 -17 69t-55 20h-37v100h43q31 0 48.5 16t17.5 58v39z" />
    <glyph glyph-name="four" unicode="4" 
d="M14 227l200 473h120v-473h52v-100h-52v-127h-108v127h-212v100zM226 227v263l-111 -263h111z" />
    <glyph glyph-name="five" unicode="5" 
d="M139 226v-65q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v154q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-21h-104l20 406h290v-100h-191l-9 -167q31 51 96 51q61 0 91.5 -41t30.5 -119v-156q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v58h104z
" />
    <glyph glyph-name="six" unicode="6" 
d="M33 526q0 89 41 135.5t127 46.5q81 0 122.5 -45.5t41.5 -130.5v-18h-104v25q0 38 -15.5 53.5t-41.5 15.5q-29 0 -45 -18t-16 -68v-128q29 62 102 62q61 0 91.5 -41t30.5 -119v-128q0 -85 -43 -130.5t-124 -45.5t-124 45.5t-43 130.5v358zM200 356q-26 0 -41.5 -15.5
t-15.5 -53.5v-126q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v126q0 38 -15.5 53.5t-41.5 15.5z" />
    <glyph glyph-name="seven" unicode="7" 
d="M363 700v-96l-162 -604h-110l161 600h-219v100h330z" />
    <glyph glyph-name="eight" unicode="8" 
d="M370 168q0 -85 -44 -130.5t-128 -45.5t-128 45.5t-44 130.5v55q0 51 16 88.5t52 57.5q-68 37 -68 141v22q0 85 44 130.5t128 45.5t128 -45.5t44 -130.5v-22q0 -103 -68 -141q36 -20 52 -57.5t16 -88.5v-55zM136 171q0 -47 18 -63t44 -16t43.5 16t18.5 63v65
q0 42 -16.5 60.5t-45.5 18.5t-45.5 -18.5t-16.5 -60.5v-65zM136 489q0 -42 17.5 -58t44.5 -16q26 0 44 16t18 58v39q0 47 -18 63.5t-44 16.5t-44 -16.5t-18 -63.5v-39z" />
    <glyph glyph-name="nine" unicode="9" 
d="M363 174q0 -89 -41 -135.5t-127 -46.5q-81 0 -122.5 45.5t-41.5 130.5v18h104v-25q0 -38 15.5 -53.5t41.5 -15.5q29 0 45 18t16 68v128q-29 -62 -102 -62q-61 0 -91.5 41t-30.5 119v128q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-358zM196 344q26 0 41.5 15.5
t15.5 53.5v126q0 38 -15.5 53t-41.5 15t-41.5 -15t-15.5 -53v-126q0 -38 15.5 -53.5t41.5 -15.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="186" 
d="M146 106v-106h-106v106h106zM146 487v-106h-106v106h106z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="186" 
d="M40 0v106h106v-95l-48 -111h-45l29 100h-42zM146 487v-106h-106v106h106z" />
    <glyph glyph-name="less" unicode="&#x3c;" 
d="M30 311v78l326 120v-79l-224 -80l224 -80v-79z" />
    <glyph glyph-name="equal" unicode="=" 
d="M361 308v-78h-326v78h326zM361 470v-78h-326v78h326z" />
    <glyph glyph-name="greater" unicode="&#x3e;" 
d="M40 191v79l224 80l-224 80v79l326 -120v-78z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="360" 
d="M120 155q-6 18 -6 43q0 38 9 66t22 51.5t29 45t29 46.5t22 56.5t9 75.5q0 38 -14.5 53.5t-40.5 15.5t-40.5 -15.5t-14.5 -53.5v-76h-104v69q0 85 40.5 130.5t121.5 45.5t121.5 -45.5t40.5 -130.5q0 -46 -9.5 -79.5t-24 -60.5t-31.5 -49.5t-31.5 -44.5t-24 -47t-9.5 -57
q0 -22 4 -39h-98zM221 106v-106h-106v106h106z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="689" 
d="M476 127q-38 0 -61.5 15.5t-26.5 50.5q-31 -63 -97 -60q-53 2 -75.5 41.5t-14.5 112.5l11 101q8 74 38.5 112t83.5 36q62 -1 82 -62l6 58h96l-30 -289q-3 -25 20 -25q21 0 36 21t25 54t14.5 73t4.5 78q0 47 -11.5 86t-36 66.5t-62 43t-89.5 15.5q-67 0 -117.5 -26
t-84.5 -74t-51.5 -116.5t-17.5 -153.5q0 -121 57 -191t176 -70q28 0 54 3t51 10.5t50.5 21t51.5 33.5l-8 -96q-50 -33 -99.5 -44.5t-105.5 -11.5q-84 0 -145 25.5t-100 72t-57.5 110.5t-18.5 142q0 93 23 174.5t69 143t115 97t162 35.5q79 0 132.5 -23t85.5 -63t46 -93.5
t14 -114.5q0 -92 -19.5 -153.5t-49 -98.5t-63.5 -52t-63 -15zM298 292q-4 -35 8 -50t35 -16t37.5 13.5t18.5 49.5l10 95q3 29 -10 42.5t-33 13.5q-23 1 -38 -13.5t-19 -49.5z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="407" 
d="M396 0h-111l-19 127h-135l-19 -127h-101l112 700h161zM145 222h106l-53 354z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="406" 
d="M206 700q86 0 124.5 -40t38.5 -121v-25q0 -54 -17 -89t-54 -52q45 -17 63.5 -55.5t18.5 -94.5v-57q0 -81 -42 -123.5t-125 -42.5h-173v700h166zM150 315v-215h63q28 0 42.5 15t14.5 54v61q0 49 -16.5 67t-54.5 18h-49zM150 600v-185h43q31 0 48.5 16t17.5 58v39
q0 38 -13.5 55t-42.5 17h-53z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="386" 
d="M359 261v-93q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v364q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5v-68h-104v75q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v100h104z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="408" 
d="M40 700h174q83 0 123.5 -44t40.5 -129v-354q0 -85 -40.5 -129t-123.5 -44h-174v700zM150 600v-500h62q26 0 41 15t15 53v364q0 38 -15 53t-41 15h-62z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="368" 
d="M150 405h151v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="344" 
d="M150 389h142v-100h-142v-289h-110v700h291v-100h-181v-211z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="390" 
d="M205 390h154v-222q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v364q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5v-68h-104v75q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v129h-50v100z
" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="427" 
d="M150 0h-110v700h110v-300h125v300h112v-700h-112v300h-125v-300z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="190" 
d="M40 700h110v-700h-110v700z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="256" 
d="M10 100q9 -1 19 -1h19q26 0 44.5 13t18.5 54v534h110v-526q0 -53 -13 -87t-35 -53.5t-50.5 -27.5t-59.5 -8h-27t-26 2v100z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="414" 
d="M184 279l-34 -64v-215h-110v700h110v-305l144 305h110l-153 -312l153 -388h-113z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="339" 
d="M40 700h110v-600h181v-100h-291v700z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="541" 
d="M273 203l75 497h153v-700h-104v502l-76 -502h-104l-82 495v-495h-96v700h153z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="427" 
d="M138 507v-507h-99v700h138l113 -419v419h98v-700h-113z" />
    <glyph glyph-name="O" unicode="O" 
d="M141 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v378q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378zM31 532q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-364q0 -85 -43 -130.5t-124 -45.5t-124 45.5t-43 130.5v364z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="382" 
d="M202 700q83 0 123.5 -44t40.5 -129v-91q0 -85 -40.5 -129t-123.5 -44h-52v-263h-110v700h162zM150 600v-237h52q26 0 40 14t14 52v105q0 38 -14 52t-40 14h-52z" />
    <glyph glyph-name="Q" unicode="Q" 
d="M31 532q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-364q0 -59 -21 -98q4 -10 10.5 -12.5t20.5 -2.5h10v-98h-15q-37 0 -60.5 12.5t-34.5 35.5q-17 -6 -36 -9.5t-41 -3.5q-81 0 -124 45.5t-43 130.5v364zM141 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v378
q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="402" 
d="M270 0q-2 7 -4 13t-3.5 15t-2 23t-0.5 35v110q0 49 -17 69t-55 20h-38v-285h-110v700h166q86 0 124.5 -40t38.5 -121v-55q0 -108 -72 -142q42 -17 57.5 -55.5t15.5 -93.5v-108q0 -26 2 -45.5t10 -39.5h-112zM150 600v-215h43q31 0 48.5 16t17.5 58v69q0 38 -13.5 55
t-42.5 17h-53z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="374" 
d="M26 532q0 85 40.5 130.5t121.5 45.5t121.5 -45.5t40.5 -130.5v-22h-104v29q0 38 -14.5 53.5t-40.5 15.5t-40.5 -15.5t-14.5 -53.5q0 -36 16 -63.5t40 -52t51.5 -49t51.5 -53.5t40 -66t16 -87q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v43h104v-50
q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53q0 36 -16 63.5t-40 52t-51.5 49t-51.5 53.5t-40 66t-16 87z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="356" 
d="M8 700h340v-100h-115v-600h-110v600h-115v100z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="398" 
d="M145 700v-540q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v540h104v-533q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v533h110z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="402" 
d="M206 129l84 571h101l-108 -700h-164l-108 700h111z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="563" 
d="M284 373l-40 -373h-151l-78 700h107l59 -552l53 552h106l55 -556l57 556h96l-78 -700h-146z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="430" 
d="M407 700l-115 -340l123 -360h-116l-90 277l-91 -277h-103l123 360l-115 340h114l83 -258l85 258h102z" />
    <glyph glyph-name="Y" unicode="Y" 
d="M143 232l-139 468h115l84 -319l84 319h105l-139 -468v-232h-110v232z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="370" 
d="M345 700v-98l-209 -502h209v-100h-322v98l209 502h-199v100h312z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="251" 
d="M47 700h182v-90h-72v-520h72v-90h-182v700z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="381" 
d="M376 0h-95l-276 700h95z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="251" 
d="M204 0h-182v90h72v520h-72v90h182v-700z" />
    <glyph glyph-name="asciicircum" unicode="^" 
d="M159 700h78l139 -310h-88l-90 210l-90 -210h-88z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="500" 
d="M0 -10h500v-80h-500v80z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="250" 
d="M157 848l68 -111h-79l-102 111h113z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="407" 
d="M396 0h-111l-19 127h-135l-19 -127h-101l112 700h161zM145 222h106l-53 354z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="406" 
d="M206 700q86 0 124.5 -40t38.5 -121v-25q0 -54 -17 -89t-54 -52q45 -17 63.5 -55.5t18.5 -94.5v-57q0 -81 -42 -123.5t-125 -42.5h-173v700h166zM150 315v-215h63q28 0 42.5 15t14.5 54v61q0 49 -16.5 67t-54.5 18h-49zM150 600v-185h43q31 0 48.5 16t17.5 58v39
q0 38 -13.5 55t-42.5 17h-53z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="386" 
d="M359 261v-93q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v364q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5v-68h-104v75q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v100h104z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="408" 
d="M40 700h174q83 0 123.5 -44t40.5 -129v-354q0 -85 -40.5 -129t-123.5 -44h-174v700zM150 600v-500h62q26 0 41 15t15 53v364q0 38 -15 53t-41 15h-62z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="368" 
d="M150 405h151v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="344" 
d="M150 389h142v-100h-142v-289h-110v700h291v-100h-181v-211z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="390" 
d="M205 390h154v-222q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v364q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5v-68h-104v75q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v129h-50v100z
" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="427" 
d="M150 0h-110v700h110v-300h125v300h112v-700h-112v300h-125v-300z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="190" 
d="M40 700h110v-700h-110v700z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="256" 
d="M10 100q9 -1 19 -1h19q26 0 44.5 13t18.5 54v534h110v-526q0 -53 -13 -87t-35 -53.5t-50.5 -27.5t-59.5 -8h-27t-26 2v100z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="414" 
d="M184 279l-34 -64v-215h-110v700h110v-305l144 305h110l-153 -312l153 -388h-113z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="339" 
d="M40 700h110v-600h181v-100h-291v700z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="541" 
d="M273 203l75 497h153v-700h-104v502l-76 -502h-104l-82 495v-495h-96v700h153z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="427" 
d="M138 507v-507h-99v700h138l113 -419v419h98v-700h-113z" />
    <glyph glyph-name="o" unicode="o" 
d="M141 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v378q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378zM31 532q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-364q0 -85 -43 -130.5t-124 -45.5t-124 45.5t-43 130.5v364z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="377" 
d="M202 700q83 0 123.5 -44t40.5 -129v-91q0 -85 -40.5 -129t-123.5 -44h-52v-263h-110v700h162zM150 600v-237h52q26 0 40 14t14 52v105q0 38 -14 52t-40 14h-52z" />
    <glyph glyph-name="q" unicode="q" 
d="M31 532q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-364q0 -59 -21 -98q4 -10 10.5 -12.5t20.5 -2.5h10v-98h-15q-37 0 -60.5 12.5t-34.5 35.5q-17 -6 -36 -9.5t-41 -3.5q-81 0 -124 45.5t-43 130.5v364zM141 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v378
q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="402" 
d="M270 0q-2 7 -4 13t-3.5 15t-2 23t-0.5 35v110q0 49 -17 69t-55 20h-38v-285h-110v700h166q86 0 124.5 -40t38.5 -121v-55q0 -108 -72 -142q42 -17 57.5 -55.5t15.5 -93.5v-108q0 -26 2 -45.5t10 -39.5h-112zM150 600v-215h43q31 0 48.5 16t17.5 58v69q0 38 -13.5 55
t-42.5 17h-53z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="374" 
d="M26 532q0 85 40.5 130.5t121.5 45.5t121.5 -45.5t40.5 -130.5v-22h-104v29q0 38 -14.5 53.5t-40.5 15.5t-40.5 -15.5t-14.5 -53.5q0 -36 16 -63.5t40 -52t51.5 -49t51.5 -53.5t40 -66t16 -87q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v43h104v-50
q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53q0 36 -16 63.5t-40 52t-51.5 49t-51.5 53.5t-40 66t-16 87z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="356" 
d="M8 700h340v-100h-115v-600h-110v600h-115v100z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="398" 
d="M145 700v-540q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v540h104v-533q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v533h110z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="402" 
d="M206 129l84 571h101l-108 -700h-164l-108 700h111z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="563" 
d="M284 373l-40 -373h-151l-78 700h107l59 -552l53 552h106l55 -556l57 556h96l-78 -700h-146z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="430" 
d="M407 700l-115 -340l123 -360h-116l-90 277l-91 -277h-103l123 360l-115 340h114l83 -258l85 258h102z" />
    <glyph glyph-name="y" unicode="y" 
d="M143 232l-139 468h115l84 -319l84 319h105l-139 -468v-232h-110v232z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="370" 
d="M345 700v-98l-209 -502h209v-100h-322v98l209 502h-199v100h312z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="257" 
d="M102 350q19 -5 31.5 -14.5t19.5 -21.5t10 -25.5t4 -27.5l5 -100q2 -42 14.5 -56.5t38.5 -14.5h16v-90h-56q-47 0 -77 27t-34 84l-8 134q-2 35 -12.5 47.5t-42.5 12.5v90q32 0 42.5 12.5t12.5 47.5l8 134q4 57 34 84t77 27h56v-90h-16q-26 0 -38.5 -14.5t-14.5 -56.5
l-5 -100q-1 -14 -4 -28t-10 -26t-19.5 -21t-31.5 -14z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="500" 
d="M210 765h90v-830h-90v830z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="257" 
d="M155 350q-38 10 -51 35.5t-14 53.5l-5 100q-2 42 -14.5 56.5t-38.5 14.5h-16v90h56q47 0 77 -27t34 -84l8 -134q2 -35 12.5 -47.5t42.5 -12.5v-90q-32 0 -42.5 -12.5t-12.5 -47.5l-8 -134q-4 -57 -34 -84t-77 -27h-56v90h16q26 0 38.5 14.5t14.5 56.5l5 100q1 28 14 53.5
t51 35.5z" />
    <glyph glyph-name="asciitilde" unicode="~" 
d="M8 332q18 27 33.5 44t29.5 27.5t28 14.5t28 4q20 0 39 -9.5t37 -21t34.5 -21t32.5 -9.5q17 0 30 12.5t36 44.5l52 -54q-36 -53 -63.5 -69.5t-55.5 -16.5q-20 0 -39 9.5t-37 21t-34.5 21t-32.5 9.5q-17 0 -30 -12.5t-36 -44.5z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="155" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="190" 
d="M136 545l14 -253v-292h-110v292l14 253h82zM42 594v106h106v-106h-106z" />
    <glyph glyph-name="cent" unicode="&#xa2;" 
d="M153 37q-58 12 -87.5 55.5t-29.5 115.5v284q0 71 29.5 114.5t87.5 56.5v52h90v-51q60 -12 90.5 -56t30.5 -116v-48h-104v55q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-298q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v80h104v-73q0 -72 -30.5 -116.5
t-90.5 -55.5v-51h-90v52z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" 
d="M44 295h67q-9 26 -21 49t-23.5 49.5t-19 59.5t-7.5 79q0 85 40.5 130.5t121.5 45.5t121.5 -45.5t40.5 -130.5v-69h-104v76q0 38 -14.5 53.5t-40.5 15.5t-40.5 -15.5t-14.5 -53.5q0 -47 8 -80t18.5 -60t22 -51.5t18.5 -52.5h121v-85h-114q-4 -38 -18.5 -65t-37.5 -45h189
v-100h-327v96q39 0 66 30t29 84h-81v85z" />
    <glyph glyph-name="currency" unicode="&#xa4;" 
d="M134 350q0 -26 19 -45t45 -19t45 19t19 45t-19 45t-45 19t-45 -19t-19 -45zM87 282q-18 30 -18 68t18 67l-55 56l43 43l56 -56q30 19 67 19q35 0 67 -19l56 56l43 -43l-56 -56q19 -32 19 -67q0 -37 -19 -67l56 -56l-43 -43l-56 55q-29 -18 -67 -18t-68 18l-55 -55l-43 43
z" />
    <glyph glyph-name="yen" unicode="&#xa5;" 
d="M389 700l-128 -429h83v-55h-91v-45h91v-55h-91v-116h-110v116h-91v55h91v45h-91v55h82l-127 429h115l81 -309l81 309h105z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="500" 
d="M210 765h90v-355h-90v355zM210 290h90v-355h-90v355z" />
    <glyph glyph-name="section" unicode="&#xa7;" 
d="M35 314q0 38 17 70t52 52q-29 19 -49 48.5t-20 81.5q0 60 41 101t121 41q81 0 121.5 -38t40.5 -98v-36h-104v21q0 27 -14.5 43.5t-40.5 16.5q-55 0 -55 -56q0 -21 16 -34t40 -24.5t51.5 -24t51.5 -32.5t40 -49.5t16 -75.5q0 -38 -17.5 -70.5t-52.5 -52.5q29 -18 49.5 -48
t20.5 -81q0 -60 -41 -101t-121 -41q-81 0 -121.5 38t-40.5 98v50h104v-35q0 -27 14.5 -43.5t40.5 -16.5q55 0 55 56q0 21 -16 34t-40 24.5t-51.5 24t-51.5 32.5t-40 49.5t-16 75.5zM140 318q0 -30 17.5 -47t43.5 -30q25 4 39.5 26t14.5 50q0 29 -18 46t-44 30
q-25 -4 -39 -25.5t-14 -49.5z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="250" 
d="M97 833v-96h-96v96h96zM249 833v-96h-96v96h96z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="736" 
d="M477 308v-56q0 -59 -28 -90.5t-85 -31.5t-85 31.5t-28 90.5v194q0 60 28 92t85 32t85 -32t28 -92v-36h-72v42q0 26 -10.5 37t-28.5 11t-28.5 -11t-10.5 -37v-204q0 -26 10.5 -36.5t28.5 -10.5t28.5 10.5t10.5 36.5v60h72zM20 350q0 78 26.5 143.5t73 113.5t110.5 74.5
t138 26.5t138 -26.5t110.5 -74.5t73 -113.5t26.5 -143.5t-26.5 -143.5t-73 -113.5t-110.5 -74.5t-138 -26.5t-138 26.5t-110.5 74.5t-73 113.5t-26.5 143.5zM92 350q0 -63 21 -115.5t58 -91t87.5 -60t109.5 -21.5t109.5 21.5t87.5 60t58 91t21 115.5t-21 115.5t-58 91
t-87.5 60t-109.5 21.5t-109.5 -21.5t-87.5 -60t-58 -91t-21 -115.5z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="278" 
d="M30 433q0 36 10.5 59.5t29.5 37.5t45 20.5t57 7.5v46q0 37 -28 37q-17 0 -25.5 -9.5t-8.5 -33.5v-23h-66v19q0 53 25.5 82t77.5 29q51 0 73 -29t22 -82v-261h-59l-4 48q-17 -53 -71 -53q-78 0 -78 101v4zM242 287v-69h-212v69h212zM100 435q0 -24 9.5 -33.5t26.5 -9.5
q16 0 25 8t11 28v78q-23 -1 -37.5 -7t-22 -15t-10 -19.5t-2.5 -20.5v-9z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="355" 
d="M177 608l-61 -257l61 -273h-99l-62 273l62 257h99zM339 608l-61 -257l61 -273h-99l-62 273l62 257h99z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" 
d="M371 389v-215h-78v137h-268v78h346z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="270" 
d="M35 400h200v-100h-200v100z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="736" 
d="M20 350q0 78 26.5 143.5t73 113.5t110.5 74.5t138 26.5t138 -26.5t110.5 -74.5t73 -113.5t26.5 -143.5t-26.5 -143.5t-73 -113.5t-110.5 -74.5t-138 -26.5t-138 26.5t-110.5 74.5t-73 113.5t-26.5 143.5zM92 350q0 -63 21 -115.5t58 -91t87.5 -60t109.5 -21.5t109.5 21.5
t87.5 60t58 91t21 115.5t-21 115.5t-58 91t-87.5 60t-109.5 21.5t-109.5 -21.5t-87.5 -60t-58 -91t-21 -115.5zM418 136q-2 5 -3.5 9.5t-2 10.5t-1 15.5t-0.5 24.5v46q0 35 -12.5 48.5t-38.5 13.5h-26v-168h-76v428h116q59 0 86 -27.5t27 -84.5v-9q0 -76 -52 -99
q29 -12 40.5 -38.5t11.5 -64.5v-46q0 -17 1.5 -31t7.5 -28h-78zM334 494v-120h31q21 0 33.5 11t12.5 40v18q0 51 -41 51h-36z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="250" 
d="M240 819v-78h-230v78h230z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="228" 
d="M15 609q0 42 28.5 70.5t70.5 28.5t70.5 -28.5t28.5 -70.5t-28.5 -70.5t-70.5 -28.5t-70.5 28.5t-28.5 70.5zM65 609q0 -20 14.5 -34.5t34.5 -14.5t34.5 14.5t14.5 34.5t-14.5 34.5t-34.5 14.5t-34.5 -14.5t-14.5 -34.5z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" 
d="M361 252v-78h-326v78h124v80h-124v78h124v114h78v-114h124v-78h-124v-80h124z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="280" 
d="M180 688q0 29 -9.5 39.5t-26.5 10.5t-26.5 -10t-9.5 -34v-47h-66v43q0 54 26 82.5t78 28.5t78 -28.5t26 -82.5q0 -37 -11 -66t-27.5 -53t-35.5 -44.5t-34.5 -39t-24.5 -36.5t-5 -37h132v-63h-202v54q0 32 10 57t25.5 45.5t33.5 39.5t33.5 39.5t25.5 45t10 56.5z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="280" 
d="M174 494q0 32 -11 44t-34 12h-24v63h27q20 0 31 11t11 37v25q0 30 -9.5 40.5t-26.5 10.5t-26.5 -10.5t-9.5 -33.5v-28h-66v23q0 54 26 83t78 29t78 -29t26 -83v-11q0 -71 -49 -91q26 -11 37.5 -35t11.5 -58v-35q0 -54 -26 -83t-78 -29t-78 29t-26 83v36h66v-41
q0 -23 9.5 -33.5t26.5 -10.5t26.5 10.5t9.5 40.5v34z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="250" 
d="M203 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="uni00B5" unicode="&#xb5;" 
d="M141 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v339h110v-500h-100v52q-31 -60 -100 -60q-7 0 -13 0.5t-11 1.5v-87h-110v593h110v-339z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" 
d="M356 -65h-80v685h-54v-685h-80l2 330q-63 0 -96.5 48t-33.5 123v91q0 85 40.5 129t123.5 44h178v-765z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="186" 
d="M146 403v-106h-106v106h106z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="250" 
d="M26 -79h74v-8q0 -11 7 -15.5t15 -4.5q25 0 25 24q0 26 -36 26h-10v71h48v-53q37 0 55.5 -10.5t18.5 -36.5q0 -20 -7.5 -32t-21 -18.5t-32 -8.5t-40.5 -2q-41 0 -68.5 11.5t-27.5 46.5v10z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="200" 
d="M25 739q20 0 33 4.5t21.5 12t13.5 18t10 21.5h46v-444h-70v339h-54v49z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="280" 
d="M104 435q0 -24 9.5 -33.5t26.5 -9.5t26.5 9.5t9.5 33.5v163q0 24 -9.5 33.5t-26.5 9.5t-26.5 -9.5t-9.5 -33.5v-163zM34 594q0 53 27 82t79 29t79 -29t27 -82v-155q0 -53 -27 -82t-79 -29t-79 29t-27 82v155zM246 287v-69h-212v69h212z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="355" 
d="M277 608l62 -257l-62 -273h-99l61 273l-61 257h99zM115 608l62 -257l-62 -273h-99l61 273l-61 257h99z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="627" 
d="M65 644q20 0 33 4.5t21.5 12t13.5 18t10 21.5h46v-444h-70v339h-54v49zM436 700h65l-276 -700h-65zM371 144l127 300h76v-300h33v-64h-33v-80h-69v80h-134v64zM505 144v164l-71 -164h71z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="627" 
d="M65 644q20 0 33 4.5t21.5 12t13.5 18t10 21.5h46v-444h-70v339h-54v49zM406 700h65l-276 -700h-65zM527 337q0 29 -9.5 39.5t-26.5 10.5t-26.5 -10t-9.5 -34v-47h-66v43q0 54 26 82.5t78 28.5t78 -28.5t26 -82.5q0 -36 -11 -65.5t-27.5 -53.5t-35.5 -44.5t-34.5 -39
t-24.5 -36.5t-5 -37h132v-63h-202v54q0 32 10 57t25.5 45.5t33.5 39.5t33.5 39.5t25.5 45t10 56.5z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="627" 
d="M174 399q0 32 -11 44t-34 12h-24v63h27q20 0 31 11t11 37v25q0 30 -9.5 40.5t-26.5 10.5t-26.5 -10.5t-9.5 -33.5v-28h-66v23q0 54 26 83t78 29t78 -29t26 -83v-11q0 -71 -49 -91q26 -11 37.5 -35t11.5 -58v-35q0 -54 -26 -83t-78 -29t-78 29t-26 83v36h66v-41
q0 -23 9.5 -33.5t26.5 -10.5t26.5 10.5t9.5 40.5v34zM446 700h65l-276 -700h-65zM371 144l127 300h76v-300h33v-64h-33v-80h-69v80h-134v64zM505 144v164l-71 -164h71z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="360" 
d="M240 545q6 -18 6 -43q0 -38 -9 -66t-22 -51.5t-29 -45t-29 -46.5t-22 -57t-9 -75q0 -38 14.5 -53.5t40.5 -15.5t40.5 15.5t14.5 53.5v76h104v-69q0 -85 -40.5 -130.5t-121.5 -45.5t-121.5 45.5t-40.5 130.5q0 45 9.5 79t24 61t31.5 49.5t31.5 44.5t24 47t9.5 57
q0 22 -4 39h98zM139 594v106h106v-106h-106z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="407" 
d="M396 0h-111l-19 127h-135l-19 -127h-101l112 700h161zM145 222h106l-53 354zM196 848l68 -111h-79l-102 111h113z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="407" 
d="M396 0h-111l-19 127h-135l-19 -127h-101l112 700h161zM145 222h106l-53 354zM321 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="407" 
d="M396 0h-111l-19 127h-135l-19 -127h-101l112 700h161zM145 222h106l-53 354zM65 737l89 111h99l89 -111h-94l-45 55l-44 -55h-94z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="407" 
d="M396 0h-111l-19 127h-135l-19 -127h-101l112 700h161zM145 222h106l-53 354zM243 813q14 0 24 5t19 24l53 -37q-14 -38 -36.5 -53t-48.5 -15q-16 0 -27.5 4t-22 8.5t-20 8.5t-20.5 4q-14 0 -24 -5t-19 -24l-53 37q14 38 36.5 53t48.5 15q16 0 27.5 -4t22 -8.5t20 -8.5
t20.5 -4z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="407" 
d="M396 0h-111l-19 127h-135l-19 -127h-101l112 700h161zM145 222h106l-53 354zM175 833v-96h-96v96h96zM327 833v-96h-96v96h96z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="407" 
d="M396 0h-111l-19 127h-135l-19 -127h-101l112 700h161zM145 222h106l-53 354zM123 814q0 34 23 57t57 23t57 -23t23 -57t-23 -57t-57 -23t-57 23t-23 57zM173 814q0 -13 8.5 -21.5t21.5 -8.5t21.5 8.5t8.5 21.5t-8.5 21.5t-21.5 8.5t-21.5 -8.5t-8.5 -21.5z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="580" 
d="M252 127h-111l-32 -127h-105l187 700h361v-100h-190v-195h151v-100h-151v-205h190v-100h-300v127zM252 222v337l-86 -337h86z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="386" 
d="M359 261v-93q0 -78 -36 -123.5t-106 -51.5v-32q37 0 55.5 -10.5t18.5 -36.5q0 -20 -7.5 -32t-21 -18.5t-32 -8.5t-40.5 -2q-41 0 -68.5 11.5t-27.5 46.5v10h74v-8q0 -11 7 -15.5t15 -4.5q25 0 25 24q0 26 -36 26h-10v51q-69 7 -103.5 51.5t-34.5 122.5v364
q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5v-68h-104v75q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v100h104z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="368" 
d="M150 405h151v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195zM181 848l68 -111h-79l-102 111h113z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="368" 
d="M150 405h151v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195zM307 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="368" 
d="M150 405h151v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195zM50 737l89 111h99l89 -111h-94l-45 55l-44 -55h-94z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="368" 
d="M150 405h151v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195zM161 833v-96h-96v96h96zM313 833v-96h-96v96h96z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="190" 
d="M40 700h110v-700h-110v700zM88 848l68 -111h-79l-102 111h113z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="190" 
d="M40 700h110v-700h-110v700zM213 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="190" 
d="M40 700h110v-700h-110v700zM-43 737l89 111h99l89 -111h-94l-45 55l-44 -55h-94z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="190" 
d="M40 700h110v-700h-110v700zM67 833v-96h-96v96h96zM219 833v-96h-96v96h96z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="408" 
d="M40 305h-36v90h36v305h174q83 0 123.5 -44t40.5 -129v-354q0 -85 -40.5 -129t-123.5 -44h-174v305zM150 600v-205h68v-90h-68v-205h62q26 0 41 15t15 53v364q0 38 -15 53t-41 15h-62z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="427" 
d="M138 507v-507h-99v700h138l113 -419v419h98v-700h-113zM253 813q14 0 24 5t19 24l53 -37q-14 -38 -36.5 -53t-48.5 -15q-16 0 -27.5 4t-22 8.5t-20 8.5t-20.5 4q-14 0 -24 -5t-19 -24l-53 37q14 38 36.5 53t48.5 15q16 0 27.5 -4t22 -8.5t20 -8.5t20.5 -4z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" 
d="M141 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v378q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378zM31 532q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-364q0 -85 -43 -130.5t-124 -45.5t-124 45.5t-43 130.5v364zM190 848l68 -111h-79
l-102 111h113z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" 
d="M141 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v378q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378zM31 532q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-364q0 -85 -43 -130.5t-124 -45.5t-124 45.5t-43 130.5v364zM316 848l-102 -111h-74
l68 111h108z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" 
d="M141 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v378q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378zM31 532q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-364q0 -85 -43 -130.5t-124 -45.5t-124 45.5t-43 130.5v364zM59 737l89 111h99l89 -111
h-94l-45 55l-44 -55h-94z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" 
d="M141 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v378q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378zM31 532q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-364q0 -85 -43 -130.5t-124 -45.5t-124 45.5t-43 130.5v364zM238 813q14 0 24 5t19 24
l53 -37q-14 -38 -36.5 -53t-48.5 -15q-16 0 -27.5 4t-22 8.5t-20 8.5t-20.5 4q-14 0 -24 -5t-19 -24l-53 37q14 38 36.5 53t48.5 15q16 0 27.5 -4t22 -8.5t20 -8.5t20.5 -4z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" 
d="M141 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v378q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378zM31 532q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-364q0 -85 -43 -130.5t-124 -45.5t-124 45.5t-43 130.5v364zM170 833v-96h-96v96h96z
M322 833v-96h-96v96h96z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" 
d="M198 403l106 106l53 -53l-106 -106l106 -106l-53 -53l-106 106l-107 -106l-53 53l106 106l-105 105l53 53z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" 
d="M66 46q-35 46 -35 122v364q0 85 43 130.5t124 45.5q60 0 100 -26l20 55l37 -13l-26 -71q36 -44 36 -121v-364q0 -85 -43 -130.5t-124 -45.5q-62 0 -100 25l-20 -54l-37 13zM141 251l112 309q-4 26 -18.5 37t-36.5 11q-26 0 -41.5 -15.5t-15.5 -53.5v-288zM198 92
q26 0 41.5 15.5t15.5 53.5v288l-113 -309q5 -26 19 -37t37 -11z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="398" 
d="M145 700v-540q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v540h104v-533q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v533h110zM192 848l68 -111h-79l-102 111h113z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="398" 
d="M145 700v-540q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v540h104v-533q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v533h110zM318 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="398" 
d="M145 700v-540q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v540h104v-533q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v533h110zM62 737l89 111h99l89 -111h-94l-45 55l-44 -55h-94z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="398" 
d="M145 700v-540q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v540h104v-533q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v533h110zM173 833v-96h-96v96h96zM325 833v-96h-96v96h96z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" 
d="M143 232l-139 468h115l84 -319l84 319h105l-139 -468v-232h-110v232zM320 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="377" 
d="M40 700h110v-80h52q83 0 123.5 -44t40.5 -129v-91q0 -85 -40.5 -129t-123.5 -44h-52v-183h-110v700zM150 520v-237h52q26 0 40 14t14 52v105q0 38 -14 52t-40 14h-52z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="748" 
d="M26 532q0 85 40.5 130.5t121.5 45.5t121.5 -45.5t40.5 -130.5v-22h-104v29q0 38 -14.5 53.5t-40.5 15.5t-40.5 -15.5t-14.5 -53.5q0 -36 16 -63.5t40 -52t51.5 -49t51.5 -53.5t40 -66t16 -87q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v43h104v-50
q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53q0 36 -16 63.5t-40 52t-51.5 49t-51.5 53.5t-40 66t-16 87zM400 532q0 85 40.5 130.5t121.5 45.5t121.5 -45.5t40.5 -130.5v-22h-104v29q0 38 -14.5 53.5t-40.5 15.5t-40.5 -15.5t-14.5 -53.5q0 -36 16 -63.5t40 -52t51.5 -49
t51.5 -53.5t40 -66t16 -87q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v43h104v-50q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53q0 36 -16 63.5t-40 52t-51.5 49t-51.5 53.5t-40 66t-16 87z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="407" 
d="M396 0h-111l-19 127h-135l-19 -127h-101l112 700h161zM145 222h106l-53 354zM196 848l68 -111h-79l-102 111h113z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="407" 
d="M396 0h-111l-19 127h-135l-19 -127h-101l112 700h161zM145 222h106l-53 354zM321 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="407" 
d="M396 0h-111l-19 127h-135l-19 -127h-101l112 700h161zM145 222h106l-53 354zM65 737l89 111h99l89 -111h-94l-45 55l-44 -55h-94z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="407" 
d="M396 0h-111l-19 127h-135l-19 -127h-101l112 700h161zM145 222h106l-53 354zM243 813q14 0 24 5t19 24l53 -37q-14 -38 -36.5 -53t-48.5 -15q-16 0 -27.5 4t-22 8.5t-20 8.5t-20.5 4q-14 0 -24 -5t-19 -24l-53 37q14 38 36.5 53t48.5 15q16 0 27.5 -4t22 -8.5t20 -8.5
t20.5 -4z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="407" 
d="M396 0h-111l-19 127h-135l-19 -127h-101l112 700h161zM145 222h106l-53 354zM175 833v-96h-96v96h96zM327 833v-96h-96v96h96z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="407" 
d="M396 0h-111l-19 127h-135l-19 -127h-101l112 700h161zM145 222h106l-53 354zM123 814q0 34 23 57t57 23t57 -23t23 -57t-23 -57t-57 -23t-57 23t-23 57zM173 814q0 -13 8.5 -21.5t21.5 -8.5t21.5 8.5t8.5 21.5t-8.5 21.5t-21.5 8.5t-21.5 -8.5t-8.5 -21.5z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="580" 
d="M362 405h151v-100h-151v-205h190v-100h-300v127h-111l-32 -127h-105l187 700h361v-100h-190v-195zM252 222v337l-86 -337h86z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="386" 
d="M359 261v-93q0 -78 -36 -123.5t-106 -51.5v-32q37 0 55.5 -10.5t18.5 -36.5q0 -20 -7.5 -32t-21 -18.5t-32 -8.5t-40.5 -2q-41 0 -68.5 11.5t-27.5 46.5v10h74v-8q0 -11 7 -15.5t15 -4.5q25 0 25 24q0 26 -36 26h-10v51q-69 7 -103.5 51.5t-34.5 122.5v364
q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5v-68h-104v75q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v100h104z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="368" 
d="M150 405h151v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195zM181 848l68 -111h-79l-102 111h113z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="368" 
d="M150 405h151v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195zM307 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="368" 
d="M150 405h151v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195zM50 737l89 111h99l89 -111h-94l-45 55l-44 -55h-94z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="368" 
d="M150 405h151v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195zM161 833v-96h-96v96h96zM313 833v-96h-96v96h96z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="190" 
d="M40 700h110v-700h-110v700zM88 848l68 -111h-79l-102 111h113z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="190" 
d="M40 700h110v-700h-110v700zM213 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="190" 
d="M40 700h110v-700h-110v700zM-43 737l89 111h99l89 -111h-94l-45 55l-44 -55h-94z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="190" 
d="M40 700h110v-700h-110v700zM67 833v-96h-96v96h96zM219 833v-96h-96v96h96z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="408" 
d="M40 305h-36v90h36v305h174q83 0 123.5 -44t40.5 -129v-354q0 -85 -40.5 -129t-123.5 -44h-174v305zM150 600v-205h68v-90h-68v-205h62q26 0 41 15t15 53v364q0 38 -15 53t-41 15h-62z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="427" 
d="M138 507v-507h-99v700h138l113 -419v419h98v-700h-113zM253 813q14 0 24 5t19 24l53 -37q-14 -38 -36.5 -53t-48.5 -15q-16 0 -27.5 4t-22 8.5t-20 8.5t-20.5 4q-14 0 -24 -5t-19 -24l-53 37q14 38 36.5 53t48.5 15q16 0 27.5 -4t22 -8.5t20 -8.5t20.5 -4z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" 
d="M141 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v378q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378zM31 532q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-364q0 -85 -43 -130.5t-124 -45.5t-124 45.5t-43 130.5v364zM190 848l68 -111h-79
l-102 111h113z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" 
d="M141 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v378q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378zM31 532q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-364q0 -85 -43 -130.5t-124 -45.5t-124 45.5t-43 130.5v364zM316 848l-102 -111h-74
l68 111h108z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" 
d="M141 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v378q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378zM31 532q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-364q0 -85 -43 -130.5t-124 -45.5t-124 45.5t-43 130.5v364zM59 737l89 111h99l89 -111
h-94l-45 55l-44 -55h-94z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" 
d="M141 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v378q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378zM31 532q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-364q0 -85 -43 -130.5t-124 -45.5t-124 45.5t-43 130.5v364zM238 813q14 0 24 5t19 24
l53 -37q-14 -38 -36.5 -53t-48.5 -15q-16 0 -27.5 4t-22 8.5t-20 8.5t-20.5 4q-14 0 -24 -5t-19 -24l-53 37q14 38 36.5 53t48.5 15q16 0 27.5 -4t22 -8.5t20 -8.5t20.5 -4z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" 
d="M141 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v378q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378zM31 532q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-364q0 -85 -43 -130.5t-124 -45.5t-124 45.5t-43 130.5v364zM170 833v-96h-96v96h96z
M322 833v-96h-96v96h96z" />
    <glyph glyph-name="divide" unicode="&#xf7;" 
d="M251 250v-106h-106v106h106zM251 555v-106h-106v106h106zM371 389v-78h-346v78h346z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" 
d="M31 532q0 85 43 130.5t124 45.5q60 0 100 -26l20 55l37 -13l-26 -71q36 -44 36 -121v-364q0 -85 -43 -130.5t-124 -45.5q-62 0 -100 25l-20 -54l-37 13l25 70q-35 46 -35 122v364zM141 251l112 309q-4 26 -18.5 37t-36.5 11q-26 0 -41.5 -15.5t-15.5 -53.5v-288zM198 92
q26 0 41.5 15.5t15.5 53.5v288l-113 -309q5 -26 19 -37t37 -11z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="398" 
d="M145 700v-540q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v540h104v-533q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v533h110zM192 848l68 -111h-79l-102 111h113z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="398" 
d="M145 700v-540q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v540h104v-533q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v533h110zM318 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="398" 
d="M145 700v-540q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v540h104v-533q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v533h110zM62 737l89 111h99l89 -111h-94l-45 55l-44 -55h-94z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="398" 
d="M145 700v-540q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v540h104v-533q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v533h110zM173 833v-96h-96v96h96zM325 833v-96h-96v96h96z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" 
d="M143 232l-139 468h115l84 -319l84 319h105l-139 -468v-232h-110v232zM320 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="377" 
d="M40 700h110v-80h52q83 0 123.5 -44t40.5 -129v-91q0 -85 -40.5 -129t-123.5 -44h-52v-183h-110v700zM150 520v-237h52q26 0 40 14t14 52v105q0 38 -14 52t-40 14h-52z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" 
d="M143 232l-139 468h115l84 -319l84 319h105l-139 -468v-232h-110v232zM174 833v-96h-96v96h96zM326 833v-96h-96v96h96z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="407" 
d="M396 0h-111l-19 127h-135l-19 -127h-101l112 700h161zM145 222h106l-53 354zM318 819v-78h-230v78h230z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="407" 
d="M396 0h-111l-19 127h-135l-19 -127h-101l112 700h161zM145 222h106l-53 354zM318 819v-78h-230v78h230z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="407" 
d="M396 0h-111l-19 127h-135l-19 -127h-101l112 700h161zM145 222h106l-53 354zM315 848q0 -53 -28 -84t-85 -31t-83.5 31t-27.5 84h65q2 -29 13.5 -38t32.5 -9q22 0 34 9t14 38h65z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="407" 
d="M396 0h-111l-19 127h-135l-19 -127h-101l112 700h161zM145 222h106l-53 354zM315 848q0 -53 -28 -84t-85 -31t-83.5 31t-27.5 84h65q2 -29 13.5 -38t32.5 -9q22 0 34 9t14 38h65z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="407" 
d="M396 0h-41q-19 -16 -24.5 -31.5t-5.5 -26.5q0 -14 10.5 -21t24.5 -7q15 0 21.5 2t14.5 4v-56q-17 -7 -32 -9t-36 -2q-40 0 -63.5 15.5t-23.5 46.5q0 42 47 73l-22 139h-135l-19 -127h-101l112 700h161zM145 222h106l-53 354z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="407" 
d="M396 0h-41q-19 -16 -24.5 -31.5t-5.5 -26.5q0 -14 10.5 -21t24.5 -7q15 0 21.5 2t14.5 4v-56q-17 -7 -32 -9t-36 -2q-40 0 -63.5 15.5t-23.5 46.5q0 42 47 73l-22 139h-135l-19 -127h-101l112 700h161zM145 222h106l-53 354z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="386" 
d="M359 261v-93q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v364q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5v-68h-104v75q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v100h104zM313 848
l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="386" 
d="M359 261v-93q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v364q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5v-68h-104v75q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v100h104zM313 848
l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="386" 
d="M359 261v-93q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v364q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5v-68h-104v75q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v100h104zM56 737
l89 111h99l89 -111h-94l-45 55l-44 -55h-94z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="386" 
d="M359 261v-93q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v364q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5v-68h-104v75q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v100h104zM56 737
l89 111h99l89 -111h-94l-45 55l-44 -55h-94z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="386" 
d="M359 261v-93q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v364q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5v-68h-104v75q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v100h104zM243 833
v-96h-96v96h96z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="386" 
d="M359 261v-93q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v364q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5v-68h-104v75q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v100h104zM243 833
v-96h-96v96h96z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="386" 
d="M359 261v-93q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v364q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5v-68h-104v75q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v100h104zM150 848
l44 -56l45 56h94l-89 -111h-99l-89 111h94z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="386" 
d="M359 261v-93q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v364q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5v-68h-104v75q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v100h104zM150 848
l44 -56l45 56h94l-89 -111h-99l-89 111h94z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="408" 
d="M40 700h174q83 0 123.5 -44t40.5 -129v-354q0 -85 -40.5 -129t-123.5 -44h-174v700zM150 600v-500h62q26 0 41 15t15 53v364q0 38 -15 53t-41 15h-62zM150 848l44 -56l45 56h94l-89 -111h-99l-89 111h94z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="408" 
d="M40 700h174q83 0 123.5 -44t40.5 -129v-354q0 -85 -40.5 -129t-123.5 -44h-174v700zM150 600v-500h62q26 0 41 15t15 53v364q0 38 -15 53t-41 15h-62zM150 848l44 -56l45 56h94l-89 -111h-99l-89 111h94z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="408" 
d="M40 305h-36v90h36v305h174q83 0 123.5 -44t40.5 -129v-354q0 -85 -40.5 -129t-123.5 -44h-174v305zM150 600v-205h68v-90h-68v-205h62q26 0 41 15t15 53v364q0 38 -15 53t-41 15h-62z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="408" 
d="M40 305h-36v90h36v305h174q83 0 123.5 -44t40.5 -129v-354q0 -85 -40.5 -129t-123.5 -44h-174v305zM150 600v-205h68v-90h-68v-205h62q26 0 41 15t15 53v364q0 38 -15 53t-41 15h-62z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="368" 
d="M150 405h151v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195zM303 819v-78h-230v78h230z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="368" 
d="M150 405h151v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195zM303 819v-78h-230v78h230z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="368" 
d="M150 405h151v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195zM300 848q0 -53 -28 -84t-85 -31t-83.5 31t-27.5 84h65q2 -29 13.5 -38t32.5 -9q22 0 34 9t14 38h65z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="368" 
d="M150 405h151v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195zM300 848q0 -53 -28 -84t-85 -31t-83.5 31t-27.5 84h65q2 -29 13.5 -38t32.5 -9q22 0 34 9t14 38h65z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="368" 
d="M150 405h151v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195zM236 833v-96h-96v96h96z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="368" 
d="M150 405h151v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195zM236 833v-96h-96v96h96z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="368" 
d="M150 405h151v-100h-151v-205h190v-100h-41q-19 -16 -24.5 -31.5t-5.5 -26.5q0 -14 10.5 -21t24.5 -7q15 0 21.5 2t14.5 4v-56q-17 -7 -32 -9t-36 -2q-40 0 -63.5 15.5t-23.5 46.5q0 24 16.5 46t50.5 39h-212v700h300v-100h-190v-195z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="368" 
d="M150 405h151v-100h-151v-205h190v-100h-41q-19 -16 -24.5 -31.5t-5.5 -26.5q0 -14 10.5 -21t24.5 -7q15 0 21.5 2t14.5 4v-56q-17 -7 -32 -9t-36 -2q-40 0 -63.5 15.5t-23.5 46.5q0 24 16.5 46t50.5 39h-212v700h300v-100h-190v-195z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="368" 
d="M150 405h151v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195zM143 848l44 -56l45 56h94l-89 -111h-99l-89 111h94z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="368" 
d="M150 405h151v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195zM143 848l44 -56l45 56h94l-89 -111h-99l-89 111h94z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="390" 
d="M205 390h154v-222q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v364q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5v-68h-104v75q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v129h-50v100z
M56 737l89 111h99l89 -111h-94l-45 55l-44 -55h-94z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="390" 
d="M205 390h154v-222q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v364q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5v-68h-104v75q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v129h-50v100z
M56 737l89 111h99l89 -111h-94l-45 55l-44 -55h-94z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="390" 
d="M205 390h154v-222q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v364q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5v-68h-104v75q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v129h-50v100z
M306 848q0 -53 -28 -84t-85 -31t-83.5 31t-27.5 84h65q2 -29 13.5 -38t32.5 -9q22 0 34 9t14 38h65z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="390" 
d="M205 390h154v-222q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v364q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5v-68h-104v75q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v129h-50v100z
M306 848q0 -53 -28 -84t-85 -31t-83.5 31t-27.5 84h65q2 -29 13.5 -38t32.5 -9q22 0 34 9t14 38h65z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="390" 
d="M205 390h154v-222q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v364q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5v-68h-104v75q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v129h-50v100z
M242 833v-96h-96v96h96z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="390" 
d="M205 390h154v-222q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v364q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5v-68h-104v75q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v129h-50v100z
M242 833v-96h-96v96h96z" />
    <glyph glyph-name="uni0122" unicode="&#x122;" horiz-adv-x="390" 
d="M205 390h154v-222q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v364q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5v-68h-104v75q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v129h-50v100z
M149 -125v92h92v-82l-43 -64h-39l26 54h-36z" />
    <glyph glyph-name="uni0123" unicode="&#x123;" horiz-adv-x="390" 
d="M205 390h154v-222q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v364q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5v-68h-104v75q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v129h-50v100z
M149 -125v92h92v-82l-43 -64h-39l26 54h-36z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="427" 
d="M150 0h-110v700h110v-300h125v300h112v-700h-112v300h-125v-300zM75 737l89 111h99l89 -111h-94l-45 55l-44 -55h-94z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="427" 
d="M150 0h-110v700h110v-300h125v300h112v-700h-112v300h-125v-300zM75 737l89 111h99l89 -111h-94l-45 55l-44 -55h-94z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="437" 
d="M155 0h-110v502h-35v82h35v116h110v-116h125v116h112v-116h35v-82h-35v-502h-112v300h-125v-300zM280 400v102h-125v-102h125z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="437" 
d="M155 0h-110v502h-35v82h35v116h110v-116h125v116h112v-116h35v-82h-35v-502h-112v300h-125v-300zM280 400v102h-125v-102h125z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="190" 
d="M40 700h110v-700h-110v700zM135 813q14 0 24 5t19 24l53 -37q-14 -38 -36.5 -53t-48.5 -15q-16 0 -27.5 4t-22 8.5t-20 8.5t-20.5 4q-14 0 -24 -5t-19 -24l-53 37q14 38 36.5 53t48.5 15q16 0 27.5 -4t22 -8.5t20 -8.5t20.5 -4z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="190" 
d="M40 700h110v-700h-110v700zM135 813q14 0 24 5t19 24l53 -37q-14 -38 -36.5 -53t-48.5 -15q-16 0 -27.5 4t-22 8.5t-20 8.5t-20.5 4q-14 0 -24 -5t-19 -24l-53 37q14 38 36.5 53t48.5 15q16 0 27.5 -4t22 -8.5t20 -8.5t20.5 -4z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="190" 
d="M40 700h110v-700h-110v700zM210 819v-78h-230v78h230z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="190" 
d="M40 700h110v-700h-110v700zM210 819v-78h-230v78h230z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="190" 
d="M40 700h110v-700h-110v700zM207 848q0 -53 -28 -84t-85 -31t-83.5 31t-27.5 84h65q2 -29 13.5 -38t32.5 -9q22 0 34 9t14 38h65z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="190" 
d="M40 700h110v-700h-110v700zM207 848q0 -53 -28 -84t-85 -31t-83.5 31t-27.5 84h65q2 -29 13.5 -38t32.5 -9q22 0 34 9t14 38h65z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="190" 
d="M40 700h110v-700h-20q-19 -16 -24.5 -31.5t-5.5 -26.5q0 -14 10.5 -21t24.5 -7q15 0 21.5 2t14.5 4v-56q-17 -7 -32 -9t-36 -2q-40 0 -63.5 15.5t-23.5 46.5q0 24 16.5 46t50.5 39h-43v700z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="190" 
d="M40 700h110v-700h-20q-19 -16 -24.5 -31.5t-5.5 -26.5q0 -14 10.5 -21t24.5 -7q15 0 21.5 2t14.5 4v-56q-17 -7 -32 -9t-36 -2q-40 0 -63.5 15.5t-23.5 46.5q0 24 16.5 46t50.5 39h-43v700z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="190" 
d="M40 700h110v-700h-110v700zM143 833v-96h-96v96h96z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="190" 
d="M40 700h110v-700h-110v700z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="446" 
d="M40 700h110v-700h-110v700zM200 100q9 -1 19 -1h19q26 0 44.5 13t18.5 54v534h110v-526q0 -53 -13 -87t-35 -53.5t-50.5 -27.5t-59.5 -8h-27t-26 2v100z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="446" 
d="M40 700h110v-700h-110v700zM200 100q9 -1 19 -1h19q26 0 44.5 13t18.5 54v534h110v-526q0 -53 -13 -87t-35 -53.5t-50.5 -27.5t-59.5 -8h-27t-26 2v100z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="256" 
d="M10 100q9 -1 19 -1h19q26 0 44.5 13t18.5 54v534h110v-526q0 -53 -13 -87t-35 -53.5t-50.5 -27.5t-59.5 -8h-27t-26 2v100zM9 737l89 111h99l89 -111h-94l-45 55l-44 -55h-94z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="256" 
d="M10 100q9 -1 19 -1h19q26 0 44.5 13t18.5 54v534h110v-526q0 -53 -13 -87t-35 -53.5t-50.5 -27.5t-59.5 -8h-27t-26 2v100zM9 737l89 111h99l89 -111h-94l-45 55l-44 -55h-94z" />
    <glyph glyph-name="uni0136" unicode="&#x136;" horiz-adv-x="414" 
d="M184 279l-34 -64v-215h-110v700h110v-305l144 305h110l-153 -312l153 -388h-113zM169 -125v92h92v-82l-43 -64h-39l26 54h-36z" />
    <glyph glyph-name="uni0137" unicode="&#x137;" horiz-adv-x="414" 
d="M184 279l-34 -64v-215h-110v700h110v-305l144 305h110l-153 -312l153 -388h-113zM169 -125v92h92v-82l-43 -64h-39l26 54h-36z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="414" 
d="M184 279l-34 -64v-215h-110v700h110v-305l144 305h110l-153 -312l153 -388h-113z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="339" 
d="M40 700h110v-600h181v-100h-291v700zM248 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="339" 
d="M40 700h110v-600h181v-100h-291v700zM248 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="uni013B" unicode="&#x13b;" horiz-adv-x="339" 
d="M40 700h110v-600h181v-100h-291v700zM140 -125v92h92v-82l-43 -64h-39l26 54h-36z" />
    <glyph glyph-name="uni013C" unicode="&#x13c;" horiz-adv-x="339" 
d="M40 700h110v-600h181v-100h-291v700zM140 -125v92h92v-82l-43 -64h-39l26 54h-36z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="339" 
d="M40 700h110v-600h181v-100h-291v700zM211 594v106h106v-95l-48 -111h-45l29 100h-42z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="339" 
d="M40 700h110v-600h181v-100h-291v700zM211 594v106h106v-95l-48 -111h-45l29 100h-42z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="339" 
d="M40 700h110v-600h181v-100h-291v700zM316 403v-106h-106v106h106z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="339" 
d="M40 700h110v-600h181v-100h-291v700zM316 403v-106h-106v106h106z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="348" 
d="M0 195l49 52v453h110v-335l121 130v-100l-121 -130v-165h181v-100h-291v147l-49 -52v100z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="348" 
d="M0 195l49 52v453h110v-335l121 130v-100l-121 -130v-165h181v-100h-291v147l-49 -52v100z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="427" 
d="M138 507v-507h-99v700h138l113 -419v419h98v-700h-113zM331 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="427" 
d="M138 507v-507h-99v700h138l113 -419v419h98v-700h-113zM331 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="uni0145" unicode="&#x145;" horiz-adv-x="427" 
d="M138 507v-507h-99v700h138l113 -419v419h98v-700h-113zM167 -125v92h92v-82l-43 -64h-39l26 54h-36z" />
    <glyph glyph-name="uni0146" unicode="&#x146;" horiz-adv-x="427" 
d="M138 507v-507h-99v700h138l113 -419v419h98v-700h-113zM167 -125v92h92v-82l-43 -64h-39l26 54h-36z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="427" 
d="M138 507v-507h-99v700h138l113 -419v419h98v-700h-113zM169 848l44 -56l45 56h94l-89 -111h-99l-89 111h94z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="427" 
d="M138 507v-507h-99v700h138l113 -419v419h98v-700h-113zM169 848l44 -56l45 56h94l-89 -111h-99l-89 111h94z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="543" 
d="M254 507v-507h-99v700h138l113 -419v419h98v-700h-113zM5 594v106h106v-95l-48 -111h-45l29 100h-42z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" 
d="M141 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v378q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378zM31 532q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-364q0 -85 -43 -130.5t-124 -45.5t-124 45.5t-43 130.5v364zM313 819v-78h-230v78h230z
" />
    <glyph glyph-name="omacron" unicode="&#x14d;" 
d="M141 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v378q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378zM31 532q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-364q0 -85 -43 -130.5t-124 -45.5t-124 45.5t-43 130.5v364zM313 819v-78h-230v78h230z
" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" 
d="M141 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v378q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378zM31 532q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-364q0 -85 -43 -130.5t-124 -45.5t-124 45.5t-43 130.5v364zM310 848q0 -53 -28 -84
t-85 -31t-83.5 31t-27.5 84h65q2 -29 13.5 -38t32.5 -9q22 0 34 9t14 38h65z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" 
d="M141 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v378q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378zM31 532q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-364q0 -85 -43 -130.5t-124 -45.5t-124 45.5t-43 130.5v364zM310 848q0 -53 -28 -84
t-85 -31t-83.5 31t-27.5 84h65q2 -29 13.5 -38t32.5 -9q22 0 34 9t14 38h65z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" 
d="M141 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v378q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378zM31 532q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-364q0 -85 -43 -130.5t-124 -45.5t-124 45.5t-43 130.5v364zM232 848l-83 -111h-67
l50 111h100zM363 848l-84 -111h-66l49 111h101z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" 
d="M141 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v378q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378zM31 532q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-364q0 -85 -43 -130.5t-124 -45.5t-124 45.5t-43 130.5v364zM232 848l-83 -111h-67
l50 111h100zM363 848l-84 -111h-66l49 111h101z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="586" 
d="M368 405h151v-100h-151v-205h190v-100h-364q-83 0 -123.5 44t-40.5 129v354q0 85 40.5 129t123.5 44h364v-100h-190v-195zM258 600h-62q-26 0 -41 -15t-15 -53v-364q0 -38 15 -53t41 -15h62v500z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="586" 
d="M368 405h151v-100h-151v-205h190v-100h-364q-83 0 -123.5 44t-40.5 129v354q0 85 40.5 129t123.5 44h364v-100h-190v-195zM258 600h-62q-26 0 -41 -15t-15 -53v-364q0 -38 15 -53t41 -15h62v500z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="402" 
d="M270 0q-2 7 -4 13t-3.5 15t-2 23t-0.5 35v110q0 49 -17 69t-55 20h-38v-285h-110v700h166q86 0 124.5 -40t38.5 -121v-55q0 -108 -72 -142q42 -17 57.5 -55.5t15.5 -93.5v-108q0 -26 2 -45.5t10 -39.5h-112zM150 600v-215h43q31 0 48.5 16t17.5 58v69q0 38 -13.5 55
t-42.5 17h-53zM313 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="402" 
d="M270 0q-2 7 -4 13t-3.5 15t-2 23t-0.5 35v110q0 49 -17 69t-55 20h-38v-285h-110v700h166q86 0 124.5 -40t38.5 -121v-55q0 -108 -72 -142q42 -17 57.5 -55.5t15.5 -93.5v-108q0 -26 2 -45.5t10 -39.5h-112zM150 600v-215h43q31 0 48.5 16t17.5 58v69q0 38 -13.5 55
t-42.5 17h-53zM313 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="uni0156" unicode="&#x156;" horiz-adv-x="402" 
d="M270 0q-2 7 -4 13t-3.5 15t-2 23t-0.5 35v110q0 49 -17 69t-55 20h-38v-285h-110v700h166q86 0 124.5 -40t38.5 -121v-55q0 -108 -72 -142q42 -17 57.5 -55.5t15.5 -93.5v-108q0 -26 2 -45.5t10 -39.5h-112zM150 600v-215h43q31 0 48.5 16t17.5 58v69q0 38 -13.5 55
t-42.5 17h-53zM157 -125v92h92v-82l-43 -64h-39l26 54h-36z" />
    <glyph glyph-name="uni0157" unicode="&#x157;" horiz-adv-x="402" 
d="M270 0q-2 7 -4 13t-3.5 15t-2 23t-0.5 35v110q0 49 -17 69t-55 20h-38v-285h-110v700h166q86 0 124.5 -40t38.5 -121v-55q0 -108 -72 -142q42 -17 57.5 -55.5t15.5 -93.5v-108q0 -26 2 -45.5t10 -39.5h-112zM150 600v-215h43q31 0 48.5 16t17.5 58v69q0 38 -13.5 55
t-42.5 17h-53zM157 -125v92h92v-82l-43 -64h-39l26 54h-36z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="402" 
d="M270 0q-2 7 -4 13t-3.5 15t-2 23t-0.5 35v110q0 49 -17 69t-55 20h-38v-285h-110v700h166q86 0 124.5 -40t38.5 -121v-55q0 -108 -72 -142q42 -17 57.5 -55.5t15.5 -93.5v-108q0 -26 2 -45.5t10 -39.5h-112zM150 600v-215h43q31 0 48.5 16t17.5 58v69q0 38 -13.5 55
t-42.5 17h-53zM150 848l44 -56l45 56h94l-89 -111h-99l-89 111h94z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="402" 
d="M270 0q-2 7 -4 13t-3.5 15t-2 23t-0.5 35v110q0 49 -17 69t-55 20h-38v-285h-110v700h166q86 0 124.5 -40t38.5 -121v-55q0 -108 -72 -142q42 -17 57.5 -55.5t15.5 -93.5v-108q0 -26 2 -45.5t10 -39.5h-112zM150 600v-215h43q31 0 48.5 16t17.5 58v69q0 38 -13.5 55
t-42.5 17h-53zM150 848l44 -56l45 56h94l-89 -111h-99l-89 111h94z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="374" 
d="M26 532q0 85 40.5 130.5t121.5 45.5t121.5 -45.5t40.5 -130.5v-22h-104v29q0 38 -14.5 53.5t-40.5 15.5t-40.5 -15.5t-14.5 -53.5q0 -36 16 -63.5t40 -52t51.5 -49t51.5 -53.5t40 -66t16 -87q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v43h104v-50
q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53q0 36 -16 63.5t-40 52t-51.5 49t-51.5 53.5t-40 66t-16 87zM305 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="374" 
d="M26 532q0 85 40.5 130.5t121.5 45.5t121.5 -45.5t40.5 -130.5v-22h-104v29q0 38 -14.5 53.5t-40.5 15.5t-40.5 -15.5t-14.5 -53.5q0 -36 16 -63.5t40 -52t51.5 -49t51.5 -53.5t40 -66t16 -87q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v43h104v-50
q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53q0 36 -16 63.5t-40 52t-51.5 49t-51.5 53.5t-40 66t-16 87zM305 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="374" 
d="M26 532q0 85 40.5 130.5t121.5 45.5t121.5 -45.5t40.5 -130.5v-22h-104v29q0 38 -14.5 53.5t-40.5 15.5t-40.5 -15.5t-14.5 -53.5q0 -36 16 -63.5t40 -52t51.5 -49t51.5 -53.5t40 -66t16 -87q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v43h104v-50
q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53q0 36 -16 63.5t-40 52t-51.5 49t-51.5 53.5t-40 66t-16 87zM48 737l89 111h99l89 -111h-94l-45 55l-44 -55h-94z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="374" 
d="M26 532q0 85 40.5 130.5t121.5 45.5t121.5 -45.5t40.5 -130.5v-22h-104v29q0 38 -14.5 53.5t-40.5 15.5t-40.5 -15.5t-14.5 -53.5q0 -36 16 -63.5t40 -52t51.5 -49t51.5 -53.5t40 -66t16 -87q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v43h104v-50
q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53q0 36 -16 63.5t-40 52t-51.5 49t-51.5 53.5t-40 66t-16 87zM48 737l89 111h99l89 -111h-94l-45 55l-44 -55h-94z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="374" 
d="M26 532q0 85 40.5 130.5t121.5 45.5t121.5 -45.5t40.5 -130.5v-22h-104v29q0 38 -14.5 53.5t-40.5 15.5t-40.5 -15.5t-14.5 -53.5q0 -36 16 -63.5t40 -52t51.5 -49t51.5 -53.5t40 -66t16 -87q0 -80 -37 -125t-109 -50v-32q37 0 55.5 -10.5t18.5 -36.5q0 -20 -7.5 -32
t-21 -18.5t-32 -8.5t-40.5 -2q-41 0 -68.5 11.5t-27.5 46.5v10h74v-8q0 -11 7 -15.5t15 -4.5q25 0 25 24q0 26 -36 26h-10v51q-67 8 -100.5 53t-33.5 121v43h104v-50q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53q0 36 -16 63.5t-40 52t-51.5 49t-51.5 53.5t-40 66t-16 87z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="374" 
d="M26 532q0 85 40.5 130.5t121.5 45.5t121.5 -45.5t40.5 -130.5v-22h-104v29q0 38 -14.5 53.5t-40.5 15.5t-40.5 -15.5t-14.5 -53.5q0 -36 16 -63.5t40 -52t51.5 -49t51.5 -53.5t40 -66t16 -87q0 -80 -37 -125t-109 -50v-32q37 0 55.5 -10.5t18.5 -36.5q0 -20 -7.5 -32
t-21 -18.5t-32 -8.5t-40.5 -2q-41 0 -68.5 11.5t-27.5 46.5v10h74v-8q0 -11 7 -15.5t15 -4.5q25 0 25 24q0 26 -36 26h-10v51q-67 8 -100.5 53t-33.5 121v43h104v-50q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53q0 36 -16 63.5t-40 52t-51.5 49t-51.5 53.5t-40 66t-16 87z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="374" 
d="M26 532q0 85 40.5 130.5t121.5 45.5t121.5 -45.5t40.5 -130.5v-22h-104v29q0 38 -14.5 53.5t-40.5 15.5t-40.5 -15.5t-14.5 -53.5q0 -36 16 -63.5t40 -52t51.5 -49t51.5 -53.5t40 -66t16 -87q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v43h104v-50
q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53q0 36 -16 63.5t-40 52t-51.5 49t-51.5 53.5t-40 66t-16 87zM143 848l44 -56l45 56h94l-89 -111h-99l-89 111h94z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="374" 
d="M26 532q0 85 40.5 130.5t121.5 45.5t121.5 -45.5t40.5 -130.5v-22h-104v29q0 38 -14.5 53.5t-40.5 15.5t-40.5 -15.5t-14.5 -53.5q0 -36 16 -63.5t40 -52t51.5 -49t51.5 -53.5t40 -66t16 -87q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v43h104v-50
q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53q0 36 -16 63.5t-40 52t-51.5 49t-51.5 53.5t-40 66t-16 87zM142 848l44 -56l45 56h94l-89 -111h-99l-89 111h94z" />
    <glyph glyph-name="uni0162" unicode="&#x162;" horiz-adv-x="356" 
d="M88 -99h64v-10q0 -11 6.5 -14.5t14.5 -3.5q26 0 26 26q0 28 -36 28h-11v73h-29v600h-115v100h340v-100h-115v-600h-31v-39q29 0 46 -15.5t17 -41.5q0 -45 -27 -63.5t-64 -18.5q-34 0 -60 17t-26 52v10z" />
    <glyph glyph-name="uni0163" unicode="&#x163;" horiz-adv-x="356" 
d="M88 -99h64v-10q0 -11 6.5 -14.5t14.5 -3.5q26 0 26 26q0 28 -36 28h-11v73h-29v600h-115v100h340v-100h-115v-600h-31v-39q29 0 46 -15.5t17 -41.5q0 -45 -27 -63.5t-64 -18.5q-34 0 -60 17t-26 52v10z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="356" 
d="M8 700h340v-100h-115v-600h-110v600h-115v100zM133 848l44 -56l45 56h94l-89 -111h-99l-89 111h94z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="356" 
d="M8 700h340v-100h-115v-600h-110v600h-115v100zM133 848l44 -56l45 56h94l-89 -111h-99l-89 111h94z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="356" 
d="M123 487v113h-115v100h340v-100h-115v-113h66v-82h-66v-405h-110v405h-66v82h66z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="356" 
d="M123 487v113h-115v100h340v-100h-115v-113h66v-82h-66v-405h-110v405h-66v82h66z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="398" 
d="M145 700v-540q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v540h104v-533q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v533h110zM240 813q14 0 24 5t19 24l53 -37q-14 -38 -36.5 -53t-48.5 -15q-16 0 -27.5 4t-22 8.5t-20 8.5t-20.5 4q-14 0 -24 -5t-19 -24
l-53 37q14 38 36.5 53t48.5 15q16 0 27.5 -4t22 -8.5t20 -8.5t20.5 -4z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="398" 
d="M145 700v-540q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v540h104v-533q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v533h110zM240 813q14 0 24 5t19 24l53 -37q-14 -38 -36.5 -53t-48.5 -15q-16 0 -27.5 4t-22 8.5t-20 8.5t-20.5 4q-14 0 -24 -5t-19 -24
l-53 37q14 38 36.5 53t48.5 15q16 0 27.5 -4t22 -8.5t20 -8.5t20.5 -4z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="398" 
d="M145 700v-540q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v540h104v-533q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v533h110zM315 819v-78h-230v78h230z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="398" 
d="M145 700v-540q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v540h104v-533q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v533h110zM315 819v-78h-230v78h230z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="398" 
d="M145 700v-540q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v540h104v-533q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v533h110zM312 848q0 -53 -28 -84t-85 -31t-83.5 31t-27.5 84h65q2 -29 13.5 -38t32.5 -9q22 0 34 9t14 38h65z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="398" 
d="M145 700v-540q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v540h104v-533q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v533h110zM312 848q0 -53 -28 -84t-85 -31t-83.5 31t-27.5 84h65q2 -29 13.5 -38t32.5 -9q22 0 34 9t14 38h65z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="398" 
d="M145 700v-540q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v540h104v-533q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v533h110zM120 814q0 34 23 57t57 23t57 -23t23 -57t-23 -57t-57 -23t-57 23t-23 57zM170 814q0 -13 8.5 -21.5t21.5 -8.5t21.5 8.5
t8.5 21.5t-8.5 21.5t-21.5 8.5t-21.5 -8.5t-8.5 -21.5z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="398" 
d="M145 700v-540q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v540h104v-533q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v533h110zM120 814q0 34 23 57t57 23t57 -23t23 -57t-23 -57t-57 -23t-57 23t-23 57zM170 814q0 -13 8.5 -21.5t21.5 -8.5t21.5 8.5
t8.5 21.5t-8.5 21.5t-21.5 8.5t-21.5 -8.5t-8.5 -21.5z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="398" 
d="M145 700v-540q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v540h104v-533q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v533h110zM234 848l-83 -111h-67l50 111h100zM365 848l-84 -111h-66l49 111h101z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="398" 
d="M145 700v-540q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v540h104v-533q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v533h110zM234 848l-83 -111h-67l50 111h100zM365 848l-84 -111h-66l49 111h101z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="398" 
d="M145 700v-540q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v540h104v-533q0 -44 -11.5 -86t-51.5 -73q-21 -17 -27.5 -36t-6.5 -30q0 -14 10.5 -21t24.5 -7q15 0 21.5 2t14.5 4v-56q-17 -7 -32 -9t-36 -2q-40 0 -63.5 15.5t-23.5 46.5q0 46 58 80q-17 -4 -41 -4
q-81 0 -122.5 45.5t-41.5 130.5v533h110z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="398" 
d="M145 700v-540q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v540h104v-533q0 -44 -11.5 -86t-51.5 -73q-21 -17 -27.5 -36t-6.5 -30q0 -14 10.5 -21t24.5 -7q15 0 21.5 2t14.5 4v-56q-17 -7 -32 -9t-36 -2q-40 0 -63.5 15.5t-23.5 46.5q0 46 58 80q-17 -4 -41 -4
q-81 0 -122.5 45.5t-41.5 130.5v533h110z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="563" 
d="M284 373l-40 -373h-151l-78 700h107l59 -552l53 552h106l55 -556l57 556h96l-78 -700h-146zM143 737l89 111h99l89 -111h-94l-45 55l-44 -55h-94z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="563" 
d="M284 373l-40 -373h-151l-78 700h107l59 -552l53 552h106l55 -556l57 556h96l-78 -700h-146zM143 737l89 111h99l89 -111h-94l-45 55l-44 -55h-94z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" 
d="M143 232l-139 468h115l84 -319l84 319h105l-139 -468v-232h-110v232zM64 737l89 111h99l89 -111h-94l-45 55l-44 -55h-94z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" 
d="M143 232l-139 468h115l84 -319l84 319h105l-139 -468v-232h-110v232zM64 737l89 111h99l89 -111h-94l-45 55l-44 -55h-94z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" 
d="M143 232l-139 468h115l84 -319l84 319h105l-139 -468v-232h-110v232zM174 833v-96h-96v96h96zM326 833v-96h-96v96h96z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="370" 
d="M345 700v-98l-209 -502h209v-100h-322v98l209 502h-199v100h312zM304 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="370" 
d="M345 700v-98l-209 -502h209v-100h-322v98l209 502h-199v100h312zM304 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="370" 
d="M345 700v-98l-209 -502h209v-100h-322v98l209 502h-199v100h312zM234 833v-96h-96v96h96z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="370" 
d="M345 700v-98l-209 -502h209v-100h-322v98l209 502h-199v100h312zM234 833v-96h-96v96h96z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="370" 
d="M345 700v-98l-209 -502h209v-100h-322v98l209 502h-199v100h312zM142 848l44 -56l45 56h94l-89 -111h-99l-89 111h94z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="370" 
d="M345 700v-98l-209 -502h209v-100h-322v98l209 502h-199v100h312zM142 848l44 -56l45 56h94l-89 -111h-99l-89 111h94z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="580" 
d="M252 127h-111l-32 -127h-105l187 700h361v-100h-190v-195h151v-100h-151v-205h190v-100h-300v127zM252 222v337l-86 -337h86zM408 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="580" 
d="M252 127h-111l-32 -127h-105l187 700h361v-100h-190v-195h151v-100h-151v-205h190v-100h-300v127zM252 222v337l-86 -337h86zM408 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" 
d="M31 532q0 85 43 130.5t124 45.5q60 0 100 -26l20 55l37 -13l-26 -71q36 -44 36 -121v-364q0 -85 -43 -130.5t-124 -45.5q-62 0 -100 25l-20 -54l-37 13l25 70q-35 46 -35 122v364zM141 251l112 309q-4 26 -18.5 37t-36.5 11q-26 0 -41.5 -15.5t-15.5 -53.5v-288zM198 92
q26 0 41.5 15.5t15.5 53.5v288l-113 -309q5 -26 19 -37t37 -11zM316 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" 
d="M31 532q0 85 43 130.5t124 45.5q60 0 100 -26l20 55l37 -13l-26 -71q36 -44 36 -121v-364q0 -85 -43 -130.5t-124 -45.5q-62 0 -100 25l-20 -54l-37 13l25 70q-35 46 -35 122v364zM141 251l112 309q-4 26 -18.5 37t-36.5 11q-26 0 -41.5 -15.5t-15.5 -53.5v-288zM198 92
q26 0 41.5 15.5t15.5 53.5v288l-113 -309q5 -26 19 -37t37 -11zM316 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="uni0218" unicode="&#x218;" horiz-adv-x="374" 
d="M138 -125v92h92v-82l-43 -64h-39l26 54h-36zM26 532q0 85 40.5 130.5t121.5 45.5t121.5 -45.5t40.5 -130.5v-22h-104v29q0 38 -14.5 53.5t-40.5 15.5t-40.5 -15.5t-14.5 -53.5q0 -36 16 -63.5t40 -52t51.5 -49t51.5 -53.5t40 -66t16 -87q0 -85 -41.5 -130.5t-122.5 -45.5
t-122.5 45.5t-41.5 130.5v43h104v-50q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53q0 36 -16 63.5t-40 52t-51.5 49t-51.5 53.5t-40 66t-16 87z" />
    <glyph glyph-name="uni0219" unicode="&#x219;" horiz-adv-x="374" 
d="M138 -125v92h92v-82l-43 -64h-39l26 54h-36zM26 532q0 85 40.5 130.5t121.5 45.5t121.5 -45.5t40.5 -130.5v-22h-104v29q0 38 -14.5 53.5t-40.5 15.5t-40.5 -15.5t-14.5 -53.5q0 -36 16 -63.5t40 -52t51.5 -49t51.5 -53.5t40 -66t16 -87q0 -85 -41.5 -130.5t-122.5 -45.5
t-122.5 45.5t-41.5 130.5v43h104v-50q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53q0 36 -16 63.5t-40 52t-51.5 49t-51.5 53.5t-40 66t-16 87z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="356" 
d="M8 700h340v-100h-115v-600h-110v600h-115v100zM132 -125v92h92v-82l-43 -64h-39l26 54h-36z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="356" 
d="M8 700h340v-100h-115v-600h-110v600h-115v100zM132 -125v92h92v-82l-43 -64h-39l26 54h-36z" />
    <glyph glyph-name="uni0237" unicode="&#x237;" horiz-adv-x="256" 
d="M10 100q9 -1 19 -1h19q26 0 44.5 13t18.5 54v534h110v-526q0 -53 -13 -87t-35 -53.5t-50.5 -27.5t-59.5 -8h-27t-26 2v100z" />
    <glyph glyph-name="uni02BC" unicode="&#x2bc;" horiz-adv-x="186" 
d="M40 594v106h106v-95l-48 -111h-45l29 100h-42z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="250" 
d="M-13 737l89 111h99l89 -111h-94l-45 55l-44 -55h-94z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="250" 
d="M81 848l44 -56l45 56h94l-89 -111h-99l-89 111h94z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="250" 
d="M237 848q0 -53 -28 -84t-85 -31t-83.5 31t-27.5 84h65q2 -29 13.5 -38t32.5 -9q22 0 34 9t14 38h65z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="250" 
d="M173 833v-96h-96v96h96z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="250" 
d="M45 814q0 34 23 57t57 23t57 -23t23 -57t-23 -57t-57 -23t-57 23t-23 57zM95 814q0 -13 8.5 -21.5t21.5 -8.5t21.5 8.5t8.5 21.5t-8.5 21.5t-21.5 8.5t-21.5 -8.5t-8.5 -21.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="250" 
d="M166 4q-22 -17 -28.5 -34t-6.5 -28q0 -14 10.5 -21t24.5 -7q15 0 21.5 2t14.5 4v-56q-17 -7 -32 -9t-36 -2q-40 0 -63.5 15.5t-23.5 46.5q0 53 76 89h43z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="250" 
d="M165 813q14 0 24 5t19 24l53 -37q-14 -38 -36.5 -53t-48.5 -15q-16 0 -27.5 4t-22 8.5t-20 8.5t-20.5 4q-14 0 -24 -5t-19 -24l-53 37q14 38 36.5 53t48.5 15q16 0 27.5 -4t22 -8.5t20 -8.5t20.5 -4z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="250" 
d="M125 848l-83 -111h-67l50 111h100zM256 848l-84 -111h-66l49 111h101z" />
    <glyph glyph-name="uni1E02" unicode="&#x1e02;" horiz-adv-x="406" 
d="M206 700q86 0 124.5 -40t38.5 -121v-25q0 -54 -17 -89t-54 -52q45 -17 63.5 -55.5t18.5 -94.5v-57q0 -81 -42 -123.5t-125 -42.5h-173v700h166zM150 315v-215h63q28 0 42.5 15t14.5 54v61q0 49 -16.5 67t-54.5 18h-49zM150 600v-185h43q31 0 48.5 16t17.5 58v39
q0 38 -13.5 55t-42.5 17h-53zM234 833v-96h-96v96h96z" />
    <glyph glyph-name="uni1E03" unicode="&#x1e03;" horiz-adv-x="406" 
d="M206 700q86 0 124.5 -40t38.5 -121v-25q0 -54 -17 -89t-54 -52q45 -17 63.5 -55.5t18.5 -94.5v-57q0 -81 -42 -123.5t-125 -42.5h-173v700h166zM150 315v-215h63q28 0 42.5 15t14.5 54v61q0 49 -16.5 67t-54.5 18h-49zM150 600v-185h43q31 0 48.5 16t17.5 58v39
q0 38 -13.5 55t-42.5 17h-53zM234 833v-96h-96v96h96z" />
    <glyph glyph-name="uni1E0A" unicode="&#x1e0a;" horiz-adv-x="408" 
d="M40 700h174q83 0 123.5 -44t40.5 -129v-354q0 -85 -40.5 -129t-123.5 -44h-174v700zM150 600v-500h62q26 0 41 15t15 53v364q0 38 -15 53t-41 15h-62zM242 833v-96h-96v96h96z" />
    <glyph glyph-name="uni1E0B" unicode="&#x1e0b;" horiz-adv-x="408" 
d="M40 700h174q83 0 123.5 -44t40.5 -129v-354q0 -85 -40.5 -129t-123.5 -44h-174v700zM150 600v-500h62q26 0 41 15t15 53v364q0 38 -15 53t-41 15h-62zM242 833v-96h-96v96h96z" />
    <glyph glyph-name="uni1E1E" unicode="&#x1e1e;" horiz-adv-x="344" 
d="M150 389h142v-100h-142v-289h-110v700h291v-100h-181v-211zM230 833v-96h-96v96h96z" />
    <glyph glyph-name="uni1E1F" unicode="&#x1e1f;" horiz-adv-x="344" 
d="M150 389h142v-100h-142v-289h-110v700h291v-100h-181v-211zM230 833v-96h-96v96h96z" />
    <glyph glyph-name="uni1E40" unicode="&#x1e40;" horiz-adv-x="541" 
d="M273 203l75 497h153v-700h-104v502l-76 -502h-104l-82 495v-495h-96v700h153zM318 833v-96h-96v96h96z" />
    <glyph glyph-name="uni1E41" unicode="&#x1e41;" horiz-adv-x="541" 
d="M273 203l75 497h153v-700h-104v502l-76 -502h-104l-82 495v-495h-96v700h153zM318 833v-96h-96v96h96z" />
    <glyph glyph-name="uni1E56" unicode="&#x1e56;" horiz-adv-x="377" 
d="M202 700q83 0 123.5 -44t40.5 -129v-91q0 -85 -40.5 -129t-123.5 -44h-52v-263h-110v700h162zM150 600v-237h52q26 0 40 14t14 52v105q0 38 -14 52t-40 14h-52zM238 833v-96h-96v96h96z" />
    <glyph glyph-name="uni1E57" unicode="&#x1e57;" horiz-adv-x="377" 
d="M202 700q83 0 123.5 -44t40.5 -129v-91q0 -85 -40.5 -129t-123.5 -44h-52v-263h-110v700h162zM150 600v-237h52q26 0 40 14t14 52v105q0 38 -14 52t-40 14h-52zM238 833v-96h-96v96h96z" />
    <glyph glyph-name="uni1E60" unicode="&#x1e60;" horiz-adv-x="374" 
d="M26 532q0 85 40.5 130.5t121.5 45.5t121.5 -45.5t40.5 -130.5v-22h-104v29q0 38 -14.5 53.5t-40.5 15.5t-40.5 -15.5t-14.5 -53.5q0 -36 16 -63.5t40 -52t51.5 -49t51.5 -53.5t40 -66t16 -87q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v43h104v-50
q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53q0 36 -16 63.5t-40 52t-51.5 49t-51.5 53.5t-40 66t-16 87zM235 833v-96h-96v96h96z" />
    <glyph glyph-name="uni1E61" unicode="&#x1e61;" horiz-adv-x="374" 
d="M26 532q0 85 40.5 130.5t121.5 45.5t121.5 -45.5t40.5 -130.5v-22h-104v29q0 38 -14.5 53.5t-40.5 15.5t-40.5 -15.5t-14.5 -53.5q0 -36 16 -63.5t40 -52t51.5 -49t51.5 -53.5t40 -66t16 -87q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v43h104v-50
q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53q0 36 -16 63.5t-40 52t-51.5 49t-51.5 53.5t-40 66t-16 87zM235 833v-96h-96v96h96z" />
    <glyph glyph-name="uni1E6A" unicode="&#x1e6a;" horiz-adv-x="356" 
d="M8 700h340v-100h-115v-600h-110v600h-115v100zM226 833v-96h-96v96h96z" />
    <glyph glyph-name="uni1E6B" unicode="&#x1e6b;" horiz-adv-x="356" 
d="M8 700h340v-100h-115v-600h-110v600h-115v100zM226 833v-96h-96v96h96z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="563" 
d="M284 373l-40 -373h-151l-78 700h107l59 -552l53 552h106l55 -556l57 556h96l-78 -700h-146zM274 848l68 -111h-79l-102 111h113z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="563" 
d="M284 373l-40 -373h-151l-78 700h107l59 -552l53 552h106l55 -556l57 556h96l-78 -700h-146zM274 848l68 -111h-79l-102 111h113z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="563" 
d="M284 373l-40 -373h-151l-78 700h107l59 -552l53 552h106l55 -556l57 556h96l-78 -700h-146zM399 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="563" 
d="M284 373l-40 -373h-151l-78 700h107l59 -552l53 552h106l55 -556l57 556h96l-78 -700h-146zM399 848l-102 -111h-74l68 111h108z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="563" 
d="M284 373l-40 -373h-151l-78 700h107l59 -552l53 552h106l55 -556l57 556h96l-78 -700h-146zM253 833v-96h-96v96h96zM405 833v-96h-96v96h96z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="563" 
d="M284 373l-40 -373h-151l-78 700h107l59 -552l53 552h106l55 -556l57 556h96l-78 -700h-146zM253 833v-96h-96v96h96zM405 833v-96h-96v96h96z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" 
d="M143 232l-139 468h115l84 -319l84 319h105l-139 -468v-232h-110v232zM195 848l68 -111h-79l-102 111h113z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" 
d="M143 232l-139 468h115l84 -319l84 319h105l-139 -468v-232h-110v232zM195 848l68 -111h-79l-102 111h113z" />
    <glyph glyph-name="figuredash" unicode="&#x2012;" 
d="M0 395h396v-90h-396v90z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="500" 
d="M0 395h500v-90h-500v90z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1000" 
d="M0 395h1000v-90h-1000v90z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="186" 
d="M146 600v-106h-106v95l48 111h45l-29 -100h42z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="186" 
d="M40 594v106h106v-95l-48 -111h-45l29 100h-42z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="186" 
d="M40 0v106h106v-95l-48 -111h-45l29 100h-42z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="332" 
d="M146 600v-106h-106v95l48 111h45l-29 -100h42zM292 600v-106h-106v95l48 111h45l-29 -100h42z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="332" 
d="M40 594v106h106v-95l-48 -111h-45l29 100h-42zM186 594v106h106v-95l-48 -111h-45l29 100h-42z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="332" 
d="M40 0v106h106v-95l-48 -111h-45l29 100h-42zM186 0v106h106v-95l-48 -111h-45l29 100h-42z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" 
d="M25 525h128v175h90v-175h128v-85h-128v-505h-90v505h-128v85z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" 
d="M25 525h128v175h90v-175h128v-85h-128v-245h128v-85h-128v-175h-90v175h-128v85h128v245h-128v85z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" 
d="M59 350q0 29 11 54.5t29.5 44t44 29.5t54.5 11t54.5 -11t44 -29.5t29.5 -44t11 -54.5t-11 -54.5t-29.5 -44t-44 -29.5t-54.5 -11t-54.5 11t-44 29.5t-29.5 44t-11 54.5z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="478" 
d="M146 106v-106h-106v106h106zM292 106v-106h-106v106h106zM438 106v-106h-106v106h106z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="883" 
d="M104 358q0 -44 36 -44q17 0 26.5 10.5t9.5 33.5v240q0 23 -9.5 33.5t-26.5 10.5q-36 0 -36 -44v-240zM34 593q0 54 27 83t79 29t79 -29t27 -83v-230q0 -54 -27 -83t-79 -29t-79 29t-27 83v230zM416 700h65l-276 -700h-65zM451 102q0 -44 36 -44q17 0 26.5 10.5t9.5 33.5
v240q0 23 -9.5 33.5t-26.5 10.5q-36 0 -36 -44v-240zM381 337q0 54 27 83t79 29t79 -29t27 -83v-230q0 -54 -27 -83t-79 -29t-79 29t-27 83v230zM707 102q0 -44 36 -44q17 0 26.5 10.5t9.5 33.5v240q0 23 -9.5 33.5t-26.5 10.5q-36 0 -36 -44v-240zM637 337q0 54 27 83
t79 29t79 -29t27 -83v-230q0 -54 -27 -83t-79 -29t-79 29t-27 83v230z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="196" 
d="M180 617l-59 -266l59 -282h-104l-60 282l60 266h104z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="196" 
d="M120 617l60 -266l-60 -282h-104l59 282l-59 266h104z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="67" 
d="M139 700h65l-276 -700h-65z" />
    <glyph glyph-name="zerosuperior" unicode="&#x2070;" horiz-adv-x="280" 
d="M104 453q0 -44 36 -44q17 0 26.5 10.5t9.5 33.5v240q0 23 -9.5 33.5t-26.5 10.5q-36 0 -36 -44v-240zM34 688q0 54 27 83t79 29t79 -29t27 -83v-230q0 -54 -27 -83t-79 -29t-79 29t-27 83v230z" />
    <glyph glyph-name="foursuperior" unicode="&#x2074;" horiz-adv-x="280" 
d="M24 495l127 300h76v-300h33v-64h-33v-80h-69v80h-134v64zM158 495v164l-71 -164h71z" />
    <glyph glyph-name="fivesuperior" unicode="&#x2075;" horiz-adv-x="280" 
d="M103 493v-41q0 -24 9.5 -33.5t26.5 -9.5t26.5 9.5t9.5 33.5v97q0 24 -9.5 34t-26.5 10t-26.5 -10t-9.5 -34v-13h-66l13 258h184v-64h-122l-5 -106q19 33 61 33q38 0 57.5 -26.5t19.5 -75.5v-99q0 -54 -26 -83t-78 -29t-78 29t-26 83v37h66z" />
    <glyph glyph-name="sixsuperior" unicode="&#x2076;" horiz-adv-x="280" 
d="M36 685q0 56 26 85.5t81 29.5q51 0 77.5 -29t26.5 -83v-11h-66v16q0 24 -9.5 34t-26.5 10q-19 0 -29 -11.5t-10 -43.5v-81q19 39 65 39q38 0 57.5 -26t19.5 -75v-81q0 -54 -27 -83t-79 -29t-79 29t-27 83v227zM142 577q-17 0 -26.5 -10t-9.5 -34v-80q0 -24 9.5 -33.5
t26.5 -9.5t26.5 9.5t9.5 33.5v80q0 24 -9.5 34t-26.5 10z" />
    <glyph glyph-name="sevensuperior" unicode="&#x2077;" horiz-adv-x="280" 
d="M245 795v-61l-103 -383h-70l102 381h-139v63h210z" />
    <glyph glyph-name="eightsuperior" unicode="&#x2078;" horiz-adv-x="280" 
d="M249 458q0 -54 -28 -83t-81 -29t-81 29t-28 83v34q0 32 10 56t33 37q-43 24 -43 89v14q0 54 28 83t81 29t81 -29t28 -83v-14q0 -65 -43 -89q23 -13 33 -37t10 -56v-34zM101 459q0 -29 11 -39.5t28 -10.5t28 10.5t11 39.5v42q0 50 -39 50t-39 -50v-42zM101 661
q0 -26 11 -36.5t28 -10.5t28 10.5t11 36.5v25q0 30 -11 40.5t-28 10.5t-28 -10.5t-11 -40.5v-25z" />
    <glyph glyph-name="ninesuperior" unicode="&#x2079;" horiz-adv-x="280" 
d="M244 461q0 -56 -26 -85.5t-81 -29.5q-51 0 -77.5 29t-26.5 83v11h66v-16q0 -24 9.5 -34t26.5 -10q19 0 29 11.5t10 43.5v81q-19 -39 -65 -39q-38 0 -57.5 26t-19.5 75v81q0 54 27 83t79 29t79 -29t27 -83v-227zM138 569q17 0 26.5 10t9.5 34v80q0 24 -9.5 33.5t-26.5 9.5
t-26.5 -9.5t-9.5 -33.5v-80q0 -24 9.5 -34t26.5 -10z" />
    <glyph glyph-name="zeroinferior" unicode="&#x2080;" horiz-adv-x="280" 
d="M104 37q0 -44 36 -44q17 0 26.5 10.5t9.5 33.5v240q0 23 -9.5 33.5t-26.5 10.5q-36 0 -36 -44v-240zM34 272q0 54 27 83t79 29t79 -29t27 -83v-230q0 -54 -27 -83t-79 -29t-79 29t-27 83v230z" />
    <glyph glyph-name="oneinferior" unicode="&#x2081;" horiz-adv-x="200" 
d="M25 323q20 0 33 4.5t21.5 12t13.5 18t10 21.5h46v-444h-70v339h-54v49z" />
    <glyph glyph-name="twoinferior" unicode="&#x2082;" horiz-adv-x="280" 
d="M180 272q0 29 -9.5 39.5t-26.5 10.5t-26.5 -10t-9.5 -34v-47h-66v43q0 54 26 82.5t78 28.5t78 -28.5t26 -82.5q0 -36 -11 -65.5t-27.5 -53.5t-35.5 -44.5t-34.5 -39t-24.5 -36.5t-5 -37h132v-63h-202v54q0 32 10 57t25.5 45.5t33.5 39.5t33.5 39.5t25.5 45t10 56.5z" />
    <glyph glyph-name="threeinferior" unicode="&#x2083;" horiz-adv-x="280" 
d="M174 78q0 32 -11 44t-34 12h-24v63h27q20 0 31 11t11 37v25q0 30 -9.5 40.5t-26.5 10.5t-26.5 -10.5t-9.5 -33.5v-28h-66v23q0 54 26 83t78 29t78 -29t26 -83v-11q0 -71 -49 -91q26 -11 37.5 -35t11.5 -58v-35q0 -54 -26 -83t-78 -29t-78 29t-26 83v36h66v-41
q0 -23 9.5 -33.5t26.5 -10.5t26.5 10.5t9.5 40.5v34z" />
    <glyph glyph-name="fourinferior" unicode="&#x2084;" horiz-adv-x="280" 
d="M24 79l127 300h76v-300h33v-64h-33v-80h-69v80h-134v64zM158 79v164l-71 -164h71z" />
    <glyph glyph-name="fiveinferior" unicode="&#x2085;" horiz-adv-x="280" 
d="M103 77v-41q0 -24 9.5 -33.5t26.5 -9.5t26.5 9.5t9.5 33.5v97q0 24 -9.5 34t-26.5 10t-26.5 -10t-9.5 -34v-13h-66l13 258h184v-64h-122l-5 -106q19 33 61 33q38 0 57.5 -26.5t19.5 -75.5v-99q0 -54 -26 -83t-78 -29t-78 29t-26 83v37h66z" />
    <glyph glyph-name="sixinferior" unicode="&#x2086;" horiz-adv-x="280" 
d="M36 269q0 56 26 85.5t81 29.5q51 0 77.5 -29t26.5 -83v-11h-66v16q0 24 -9.5 34t-26.5 10q-19 0 -29 -11.5t-10 -43.5v-81q19 39 65 39q38 0 57.5 -26t19.5 -75v-81q0 -54 -27 -83t-79 -29t-79 29t-27 83v227zM142 161q-17 0 -26.5 -10t-9.5 -34v-80q0 -24 9.5 -33.5
t26.5 -9.5t26.5 9.5t9.5 33.5v80q0 24 -9.5 34t-26.5 10z" />
    <glyph glyph-name="seveninferior" unicode="&#x2087;" horiz-adv-x="280" 
d="M245 379v-61l-103 -383h-70l102 381h-139v63h210z" />
    <glyph glyph-name="eightinferior" unicode="&#x2088;" horiz-adv-x="280" 
d="M249 42q0 -54 -28 -83t-81 -29t-81 29t-28 83v34q0 32 10 56t33 37q-43 24 -43 89v14q0 54 28 83t81 29t81 -29t28 -83v-14q0 -65 -43 -89q23 -13 33 -37t10 -56v-34zM101 43q0 -29 11 -39.5t28 -10.5t28 10.5t11 39.5v42q0 50 -39 50t-39 -50v-42zM101 245
q0 -26 11 -36.5t28 -10.5t28 10.5t11 36.5v25q0 30 -11 40.5t-28 10.5t-28 -10.5t-11 -40.5v-25z" />
    <glyph glyph-name="nineinferior" unicode="&#x2089;" horiz-adv-x="280" 
d="M244 45q0 -56 -26 -85.5t-81 -29.5q-51 0 -77.5 29t-26.5 83v11h66v-16q0 -24 9.5 -34t26.5 -10q19 0 29 11.5t10 43.5v81q-19 -39 -65 -39q-38 0 -57.5 26t-19.5 75v81q0 54 27 83t79 29t79 -29t27 -83v-227zM138 153q17 0 26.5 10t9.5 34v80q0 24 -9.5 33.5t-26.5 9.5
t-26.5 -9.5t-9.5 -33.5v-80q0 -24 9.5 -34t26.5 -10z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" 
d="M19 435h28v97q0 85 40 130.5t121 45.5t121 -45.5t40 -130.5v-51h-104v58q0 38 -14 53.5t-40 15.5t-40 -15.5t-14 -53.5v-104h154v-55h-154v-55h154v-55h-154v-109q0 -38 14 -53t40 -15t40 15t14 53v63h104v-56q0 -85 -40 -130.5t-121 -45.5t-121 45.5t-40 130.5v102h-28
v55h28v55h-28v55z" />
    <glyph glyph-name="uni20B9" unicode="&#x20b9;" 
d="M202 549q0 38 -21 52t-63 14h-92v85h344v-85h-84q19 -28 25 -66h59v-85h-59q-3 -36 -17 -64t-35.5 -47.5t-50 -29.5t-60.5 -10h-5l233 -313h-118l-232 313v85h92q42 0 63 14t21 52h-176v85h176z" />
    <glyph glyph-name="uni2117" unicode="&#x2117;" horiz-adv-x="736" 
d="M20 350q0 78 26.5 143.5t73 113.5t110.5 74.5t138 26.5t138 -26.5t110.5 -74.5t73 -113.5t26.5 -143.5t-26.5 -143.5t-73 -113.5t-110.5 -74.5t-138 -26.5t-138 26.5t-110.5 74.5t-73 113.5t-26.5 143.5zM92 350q0 -63 21 -115.5t58 -91t87.5 -60t109.5 -21.5t109.5 21.5
t87.5 60t58 91t21 115.5t-21 115.5t-58 91t-87.5 60t-109.5 21.5t-109.5 -21.5t-87.5 -60t-58 -91t-21 -115.5zM275 555h116q59 0 86 -27.5t27 -84.5v-56q0 -57 -27 -84.5t-86 -27.5h-40v-148h-76v428zM428 434q0 51 -41 51h-36v-140h36q41 0 41 51v38z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="586" 
d="M417 450l47 250h97v-356h-66v253l-47 -253h-66l-53 251v-251h-61v356h97zM10 700h216v-64h-73v-292h-70v292h-73v64z" />
    <glyph glyph-name="onethird" unicode="&#x2153;" horiz-adv-x="627" 
d="M65 644q20 0 33 4.5t21.5 12t13.5 18t10 21.5h46v-444h-70v339h-54v49zM521 143q0 32 -11 44t-34 12h-24v63h27q20 0 31 11t11 37v25q0 30 -9.5 40.5t-26.5 10.5t-26.5 -10.5t-9.5 -33.5v-28h-66v23q0 54 26 83t78 29t78 -29t26 -83v-11q0 -71 -49 -91q26 -11 37.5 -35
t11.5 -58v-35q0 -54 -26 -83t-78 -29t-78 29t-26 83v36h66v-41q0 -23 9.5 -33.5t26.5 -10.5t26.5 10.5t9.5 40.5v34zM406 700h65l-276 -700h-65z" />
    <glyph glyph-name="twothirds" unicode="&#x2154;" horiz-adv-x="627" 
d="M180 592q0 29 -9.5 39.5t-26.5 10.5t-26.5 -10t-9.5 -34v-47h-66v43q0 54 26 82.5t78 28.5t78 -28.5t26 -82.5q0 -37 -11 -66t-27.5 -53t-35.5 -44.5t-34.5 -39t-24.5 -36.5t-5 -37h132v-63h-202v54q0 32 10 57t25.5 45.5t33.5 39.5t33.5 39.5t25.5 45t10 56.5zM429 700
h65l-276 -700h-65zM521 143q0 32 -11 44t-34 12h-24v63h27q20 0 31 11t11 37v25q0 30 -9.5 40.5t-26.5 10.5t-26.5 -10.5t-9.5 -33.5v-28h-66v23q0 54 26 83t78 29t78 -29t26 -83v-11q0 -71 -49 -91q26 -11 37.5 -35t11.5 -58v-35q0 -54 -26 -83t-78 -29t-78 29t-26 83v36
h66v-41q0 -23 9.5 -33.5t26.5 -10.5t26.5 10.5t9.5 40.5v34z" />
    <glyph glyph-name="oneeighth" unicode="&#x215b;" horiz-adv-x="627" 
d="M65 644q20 0 33 4.5t21.5 12t13.5 18t10 21.5h46v-444h-70v339h-54v49zM596 107q0 -54 -28 -83t-81 -29t-81 29t-28 83v34q0 32 10 56t33 37q-43 24 -43 89v14q0 54 28 83t81 29t81 -29t28 -83v-14q0 -65 -43 -89q23 -13 33 -37t10 -56v-34zM448 108q0 -29 11 -39.5
t28 -10.5t28 10.5t11 39.5v42q0 50 -39 50t-39 -50v-42zM448 310q0 -26 11 -36.5t28 -10.5t28 10.5t11 36.5v25q0 30 -11 40.5t-28 10.5t-28 -10.5t-11 -40.5v-25zM406 700h65l-276 -700h-65z" />
    <glyph glyph-name="threeeighths" unicode="&#x215c;" horiz-adv-x="627" 
d="M174 399q0 32 -11 44t-34 12h-24v63h27q20 0 31 11t11 37v25q0 30 -9.5 40.5t-26.5 10.5t-26.5 -10.5t-9.5 -33.5v-28h-66v23q0 54 26 83t78 29t78 -29t26 -83v-11q0 -71 -49 -91q26 -11 37.5 -35t11.5 -58v-35q0 -54 -26 -83t-78 -29t-78 29t-26 83v36h66v-41
q0 -23 9.5 -33.5t26.5 -10.5t26.5 10.5t9.5 40.5v34zM419 700h65l-276 -700h-65zM596 107q0 -54 -28 -83t-81 -29t-81 29t-28 83v34q0 32 10 56t33 37q-43 24 -43 89v14q0 54 28 83t81 29t81 -29t28 -83v-14q0 -65 -43 -89q23 -13 33 -37t10 -56v-34zM448 108
q0 -29 11 -39.5t28 -10.5t28 10.5t11 39.5v42q0 50 -39 50t-39 -50v-42zM448 310q0 -26 11 -36.5t28 -10.5t28 10.5t11 36.5v25q0 30 -11 40.5t-28 10.5t-28 -10.5t-11 -40.5v-25z" />
    <glyph glyph-name="fiveeighths" unicode="&#x215d;" horiz-adv-x="627" 
d="M103 398v-41q0 -24 9.5 -33.5t26.5 -9.5t26.5 9.5t9.5 33.5v97q0 24 -9.5 34t-26.5 10t-26.5 -10t-9.5 -34v-13h-66l13 258h184v-64h-122l-5 -106q19 33 61 33q38 0 57.5 -26.5t19.5 -75.5v-99q0 -54 -26 -83t-78 -29t-78 29t-26 83v37h66zM419 700h65l-276 -700h-65z
M596 107q0 -54 -28 -83t-81 -29t-81 29t-28 83v34q0 32 10 56t33 37q-43 24 -43 89v14q0 54 28 83t81 29t81 -29t28 -83v-14q0 -65 -43 -89q23 -13 33 -37t10 -56v-34zM448 108q0 -29 11 -39.5t28 -10.5t28 10.5t11 39.5v42q0 50 -39 50t-39 -50v-42zM448 310
q0 -26 11 -36.5t28 -10.5t28 10.5t11 36.5v25q0 30 -11 40.5t-28 10.5t-28 -10.5t-11 -40.5v-25z" />
    <glyph glyph-name="seveneighths" unicode="&#x215e;" horiz-adv-x="627" 
d="M245 700v-61l-103 -383h-70l102 381h-139v63h210zM389 700h65l-276 -700h-65zM596 107q0 -54 -28 -83t-81 -29t-81 29t-28 83v34q0 32 10 56t33 37q-43 24 -43 89v14q0 54 28 83t81 29t81 -29t28 -83v-14q0 -65 -43 -89q23 -13 33 -37t10 -56v-34zM448 108
q0 -29 11 -39.5t28 -10.5t28 10.5t11 39.5v42q0 50 -39 50t-39 -50v-42zM448 310q0 -26 11 -36.5t28 -10.5t28 10.5t11 36.5v25q0 30 -11 40.5t-28 10.5t-28 -10.5t-11 -40.5v-25z" />
    <glyph glyph-name="minus" unicode="&#x2212;" 
d="M371 389v-78h-346v78h346z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="600" 
d="M180 216q-67 0 -104.5 34t-37.5 100t37.5 100t104.5 34h10q41 0 70.5 -11t45.5 -40q14 27 42.5 39t69.5 12h12q67 0 103.5 -34t36.5 -100t-36.5 -100t-103.5 -34h-12q-41 0 -69.5 12.5t-42.5 38.5q-16 -29 -45.5 -40t-70.5 -11h-10zM181 409q-20 0 -33 -5t-20.5 -13
t-10 -18.5t-2.5 -22.5q0 -23 12.5 -40t53.5 -18h20q36 0 52 16t16 42t-16 42.5t-52 16.5h-20zM406 409q-20 0 -33 -5t-20.5 -13t-10 -18.5t-2.5 -22.5q0 -23 12.5 -40t53.5 -18h20q36 0 52 16t16 42t-16 42.5t-52 16.5h-20z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" 
d="M8 249q18 27 33.5 44t29.5 27.5t28 14.5t28 4q20 0 39 -9.5t37 -21t34.5 -21t32.5 -9.5q17 0 30 12.5t36 44.5l52 -54q-36 -53 -63.5 -69.5t-55.5 -16.5q-20 0 -39 9.5t-37 21t-34.5 21t-32.5 9.5q-17 0 -30 -12.5t-36 -44.5zM8 415q18 27 33.5 44t29.5 27.5t28 14.5
t28 4q20 0 39 -9.5t37 -21t34.5 -21t32.5 -9.5q17 0 30 12.5t36 44.5l52 -54q-36 -53 -63.5 -69.5t-55.5 -16.5q-20 0 -39 9.5t-37 21t-34.5 21t-32.5 9.5q-17 0 -30 -12.5t-36 -44.5z" />
    <glyph glyph-name="commaaccent" horiz-adv-x="250" 
d="M79 -125v92h92v-82l-43 -64h-39l26 54h-36z" />
    <glyph glyph-name="zero.alt" 
d="M141 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v378q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378zM31 532q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-364q0 -85 -43 -130.5t-124 -45.5t-124 45.5t-43 130.5v364z" />
    <glyph glyph-name="one.alt" horiz-adv-x="250" 
d="M15 612q32 0 52.5 7t33.5 19t21 28t14 34h74v-700h-110v534h-85v78z" />
    <glyph glyph-name="two.alt" horiz-adv-x="369" 
d="M244 528q0 47 -15.5 63.5t-41.5 16.5t-41.5 -15.5t-15.5 -53.5v-75h-104v68q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5q0 -57 -17.5 -103t-43.5 -84t-56 -69.5t-54 -60t-38 -56.5t-9 -59h208v-100h-318v86q0 51 16 90t40.5 71.5t52.5 61t52.5 60.5t40.5 70
t16 89z" />
    <glyph glyph-name="three.alt" horiz-adv-x="382" 
d="M245 528q0 47 -15.5 63.5t-41.5 16.5t-41.5 -15.5t-15.5 -53.5v-45h-104v38q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5v-18q0 -112 -77 -144q42 -18 59.5 -55.5t17.5 -91.5v-55q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v58h104v-65
q0 -38 15.5 -53.5t41.5 -15.5t41.5 16t15.5 63v55q0 49 -17 69t-55 20h-37v100h43q31 0 48.5 16t17.5 58v39z" />
    <glyph glyph-name="four.alt" horiz-adv-x="397" 
d="M14 227l200 473h120v-473h52v-100h-52v-127h-110v127h-210v100zM224 227v259l-109 -259h109z" />
    <glyph glyph-name="five.alt" horiz-adv-x="383" 
d="M132 226v-65q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v154q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-21h-104l20 406h290v-100h-191l-9 -167q31 51 96 51q61 0 91.5 -41t30.5 -119v-156q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v58h104z
" />
    <glyph glyph-name="six.alt" horiz-adv-x="392" 
d="M31 526q0 89 41 135.5t127 46.5q81 0 122.5 -45.5t41.5 -130.5v-18h-104v25q0 38 -15.5 53.5t-41.5 15.5q-29 0 -45 -18t-16 -68v-128q29 62 102 62q61 0 91.5 -41t30.5 -119v-128q0 -85 -43 -130.5t-124 -45.5t-124 45.5t-43 130.5v358zM198 356q-26 0 -41.5 -15.5
t-15.5 -53.5v-126q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v126q0 38 -15.5 53.5t-41.5 15.5z" />
    <glyph glyph-name="seven.alt" horiz-adv-x="340" 
d="M330 700v-96l-152 -604h-110l152 604v-4h-210v100h320z" />
    <glyph glyph-name="eight.alt" horiz-adv-x="398" 
d="M371 168q0 -85 -44 -130.5t-128 -45.5t-128 45.5t-44 130.5v55q0 51 16 88.5t52 57.5q-68 37 -68 141v22q0 85 44 130.5t128 45.5t128 -45.5t44 -130.5v-22q0 -103 -68 -141q36 -20 52 -57.5t16 -88.5v-55zM137 171q0 -47 18 -63t44 -16t43.5 16t18.5 63v65
q0 42 -16.5 60.5t-45.5 18.5t-45.5 -18.5t-16.5 -60.5v-65zM137 489q0 -42 17.5 -58t44.5 -16q26 0 44 16t18 58v39q0 47 -18 63.5t-44 16.5t-44 -16.5t-18 -63.5v-39z" />
    <glyph glyph-name="nine.alt" horiz-adv-x="392" 
d="M361 174q0 -89 -41 -135.5t-127 -46.5q-81 0 -122.5 45.5t-41.5 130.5v18h104v-25q0 -38 15.5 -53.5t41.5 -15.5q29 0 45 18t16 68v128q-29 -62 -102 -62q-61 0 -91.5 41t-30.5 119v128q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-358zM194 344q26 0 41.5 15.5
t15.5 53.5v126q0 38 -15.5 53t-41.5 15t-41.5 -15t-15.5 -53v-126q0 -38 15.5 -53.5t41.5 -15.5z" />
    <glyph glyph-name="uni0401" unicode="&#x401;" horiz-adv-x="368" 
d="M150 405h151v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195zM161 833v-96h-96v96h96zM313 833v-96h-96v96h96z" />
    <glyph glyph-name="uni0451" unicode="&#x451;" horiz-adv-x="368" 
d="M150 405h151v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195zM161 833v-96h-96v96h96zM313 833v-96h-96v96h96z" />
    <glyph glyph-name="uni0410" unicode="&#x410;" horiz-adv-x="407" 
d="M396 0h-111l-19 127h-135l-19 -127h-101l112 700h161zM145 222h106l-53 354z" />
    <glyph glyph-name="uni0411" unicode="&#x411;" horiz-adv-x="415" 
d="M41 0v700h308v-100h-198v-179h64q83 0 124.5 -44t41.5 -129v-75q0 -85 -41.5 -129t-124.5 -44h-174zM215 100q26 0 41 14t15 52v89q0 38 -15 52t-41 14h-64v-221h64z" />
    <glyph glyph-name="uni0412" unicode="&#x412;" horiz-adv-x="406" 
d="M206 700q86 0 124.5 -40t38.5 -121v-25q0 -54 -17 -89t-54 -52q45 -17 63.5 -55.5t18.5 -94.5v-57q0 -81 -42 -123.5t-125 -42.5h-173v700h166zM150 315v-215h63q28 0 42.5 15t14.5 54v61q0 49 -16.5 67t-54.5 18h-49zM150 600v-185h43q31 0 48.5 16t17.5 58v39
q0 38 -13.5 55t-42.5 17h-53z" />
    <glyph glyph-name="uni0413" unicode="&#x413;" horiz-adv-x="365" 
d="M41 0v700h290v-100h-180v-600h-110z" />
    <glyph glyph-name="uni0414" unicode="&#x414;" horiz-adv-x="488" 
d="M358 -94v94h-235v-94h-107v192h43q14 18 19.5 39t7.5 52l25 511h308v-602h48v-192h-109zM194 191q-2 -40 -9.5 -60t-21.5 -33h144v502h-93z" />
    <glyph glyph-name="uni0415" unicode="&#x415;" horiz-adv-x="368" 
d="M150 405h151v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195z" />
    <glyph glyph-name="uni0416" unicode="&#x416;" horiz-adv-x="604" 
d="M357 417l130 283h110l-135 -284l135 -416h-113l-100 310l-27 -53v-257h-109v257l-27 54l-100 -311h-113l136 413l-136 287h110l130 -283v283h109v-283z" />
    <glyph glyph-name="uni0417" unicode="&#x417;" 
d="M252 528q0 47 -15.5 63.5t-41.5 16.5t-42 -15.5t-16 -53.5v-57h-101v52q0 85 40.5 129.5t121.5 44.5t122.5 -45.5t41.5 -130.5v-18q0 -112 -77 -144q42 -18 59.5 -55.5t17.5 -91.5v-55q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v77h104v-84
q0 -38 15.5 -53.5t41.5 -15.5t41.5 16t15.5 63v55q0 49 -17 69t-55 20h-27v100h33q31 0 48.5 16t17.5 58v39z" />
    <glyph glyph-name="uni0418" unicode="&#x418;" horiz-adv-x="427" 
d="M153 0h-113v700h98v-443l40 154l90 289h121v-700h-99v493l-45 -172z" />
    <glyph glyph-name="uni0419" unicode="&#x419;" horiz-adv-x="429" 
d="M153 0h-113v700h98v-443l40 154l90 289h121v-700h-99v493l-45 -172zM333 856q0 -51 -29 -81t-88 -30q-58 0 -85.5 30t-29.5 81h68q2 -28 13.5 -37t33.5 -9q23 0 35.5 9t14.5 37h67z" />
    <glyph glyph-name="uni041A" unicode="&#x41a;" horiz-adv-x="414" 
d="M184 279l-34 -64v-215h-110v700h110v-305l144 305h110l-153 -312l153 -388h-113z" />
    <glyph glyph-name="uni041B" unicode="&#x41b;" horiz-adv-x="450" 
d="M9 99q19 0 31.5 2t20.5 9.5t11.5 22t4.5 38.5l18 529h311v-700h-112v600h-96l-12 -423q-3 -96 -40.5 -137t-119.5 -41h-17v100z" />
    <glyph glyph-name="uni041C" unicode="&#x41c;" horiz-adv-x="541" 
d="M273 203l75 497h153v-700h-104v502l-76 -502h-104l-82 495v-495h-96v700h153z" />
    <glyph glyph-name="uni041D" unicode="&#x41d;" horiz-adv-x="427" 
d="M150 0h-110v700h110v-300h125v300h112v-700h-112v300h-125v-300z" />
    <glyph glyph-name="uni041E" unicode="&#x41e;" 
d="M141 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v378q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378zM31 532q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-364q0 -85 -43 -130.5t-124 -45.5t-124 45.5t-43 130.5v364z" />
    <glyph glyph-name="uni041F" unicode="&#x41f;" horiz-adv-x="427" 
d="M150 0h-110v700h347v-700h-112v600h-125v-600z" />
    <glyph glyph-name="uni0420" unicode="&#x420;" horiz-adv-x="377" 
d="M202 700q83 0 123.5 -44t40.5 -129v-91q0 -85 -40.5 -129t-123.5 -44h-52v-263h-110v700h162zM150 600v-237h52q26 0 40 14t14 52v105q0 38 -14 52t-40 14h-52z" />
    <glyph glyph-name="uni0421" unicode="&#x421;" horiz-adv-x="386" 
d="M359 261v-93q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v364q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5v-68h-104v75q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v100h104z" />
    <glyph glyph-name="uni0422" unicode="&#x422;" horiz-adv-x="356" 
d="M8 700h340v-100h-115v-600h-110v600h-115v100z" />
    <glyph glyph-name="uni0423" unicode="&#x423;" horiz-adv-x="408" 
d="M284 178q-10 -47 -24.5 -82.5t-39 -58t-63 -32.5t-95.5 -6v98q42 -3 66 8t31 49l1 6l-152 540h109l60 -239l34 -164l26 163l50 240h115z" />
    <glyph glyph-name="uni0424" unicode="&#x424;" horiz-adv-x="583" 
d="M236 79h-41q-83 0 -123.5 44t-40.5 129v191q0 85 40.5 129t123.5 44h41v84h108v-84h41q83 0 123.5 -44t40.5 -129v-191q0 -85 -40.5 -129t-123.5 -44h-41v-79h-108v79zM344 519v-344h41q26 0 40.5 16t14.5 54v205q0 38 -14.5 53.5t-40.5 15.5h-41zM236 519h-41
q-26 0 -40.5 -15.5t-14.5 -53.5v-205q0 -38 14.5 -54t40.5 -16h41v344z" />
    <glyph glyph-name="uni0425" unicode="&#x425;" horiz-adv-x="430" 
d="M407 700l-115 -340l123 -360h-116l-90 277l-91 -277h-103l123 360l-115 340h114l83 -258l85 258h102z" />
    <glyph glyph-name="uni0426" unicode="&#x426;" horiz-adv-x="455" 
d="M40 700h110v-600h125v600h112v-602h48v-192h-107v94h-288v700z" />
    <glyph glyph-name="uni0427" unicode="&#x427;" horiz-adv-x="415" 
d="M374 700v-700h-110v282q-18 -20 -44.5 -29.5t-49.5 -9.5q-68 0 -102.5 44.5t-34.5 116.5v296h110v-287q0 -35 19 -52t45 -17t41.5 18.5t15.5 53.5v284h110z" />
    <glyph glyph-name="uni0428" unicode="&#x428;" horiz-adv-x="600" 
d="M40 0v700h110v-600h95v600h110v-600h95v600h110v-700h-520z" />
    <glyph glyph-name="uni0429" unicode="&#x429;" horiz-adv-x="629" 
d="M560 98h49v-192h-107v94h-462v700h110v-600h95v600h110v-600h95v600h110v-602z" />
    <glyph glyph-name="uni042A" unicode="&#x42a;" horiz-adv-x="439" 
d="M8 700h189v-263h52q83 0 123.5 -44t40.5 -129v-91q0 -85 -40.5 -129t-123.5 -44h-162v600h-79v100zM249 100q26 0 40 14t14 52v105q0 38 -14 52t-40 14h-52v-237h52z" />
    <glyph glyph-name="uni042B" unicode="&#x42b;" horiz-adv-x="579" 
d="M40 0v700h110v-263h52q83 0 123.5 -44t40.5 -129v-91q0 -85 -40.5 -129t-123.5 -44h-162zM202 100q26 0 40 14t14 52v105q0 38 -14 52t-40 14h-52v-237h52zM429 0v700h110v-700h-110z" />
    <glyph glyph-name="uni042C" unicode="&#x42c;" horiz-adv-x="392" 
d="M40 0v700h110v-263h52q83 0 123.5 -44t40.5 -129v-91q0 -85 -40.5 -129t-123.5 -44h-162zM202 100q26 0 40 14t14 52v105q0 38 -14 52t-40 14h-52v-237h52z" />
    <glyph glyph-name="uni042D" unicode="&#x42d;" horiz-adv-x="386" 
d="M130 251v-90q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v151h-101v100h101v127q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-66h-102v59q0 85 40.5 130.5t121.5 45.5t122.5 -45.5t41.5 -130.5v-364q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v83
h104z" />
    <glyph glyph-name="uni042E" unicode="&#x42e;" horiz-adv-x="599" 
d="M232 300h-82v-300h-110v700h110v-300h82v132q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-364q0 -85 -43 -130.5t-124 -45.5t-124 45.5t-43 130.5v132zM342 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v378q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5
t-15.5 -53.5v-378z" />
    <glyph glyph-name="uni042F" unicode="&#x42f;" horiz-adv-x="402" 
d="M20 0q8 20 10 39.5t2 45.5v108q0 55 15.5 93.5t57.5 55.5q-72 34 -72 142v55q0 81 38.5 121t124.5 40h166v-700h-110v285h-38q-38 0 -55 -20t-17 -69v-110q0 -21 -0.5 -35t-2 -23t-3.5 -15t-4 -13h-112zM199 600q-29 0 -42.5 -17t-13.5 -55v-69q0 -42 17.5 -58t48.5 -16
h43v215h-53z" />
    <glyph glyph-name="uni0430" unicode="&#x430;" horiz-adv-x="407" 
d="M396 0h-111l-19 127h-135l-19 -127h-101l112 700h161zM145 222h106l-53 354z" />
    <glyph glyph-name="uni0431" unicode="&#x431;" horiz-adv-x="415" 
d="M41 0v700h308v-100h-198v-179h64q83 0 124.5 -44t41.5 -129v-75q0 -85 -41.5 -129t-124.5 -44h-174zM215 100q26 0 41 14t15 52v89q0 38 -15 52t-41 14h-64v-221h64z" />
    <glyph glyph-name="uni0432" unicode="&#x432;" horiz-adv-x="406" 
d="M206 700q86 0 124.5 -40t38.5 -121v-25q0 -54 -17 -89t-54 -52q45 -17 63.5 -55.5t18.5 -94.5v-57q0 -81 -42 -123.5t-125 -42.5h-173v700h166zM150 315v-215h63q28 0 42.5 15t14.5 54v61q0 49 -16.5 67t-54.5 18h-49zM150 600v-185h43q31 0 48.5 16t17.5 58v39
q0 38 -13.5 55t-42.5 17h-53z" />
    <glyph glyph-name="uni0433" unicode="&#x433;" horiz-adv-x="365" 
d="M41 0v700h290v-100h-180v-600h-110z" />
    <glyph glyph-name="uni0434" unicode="&#x434;" horiz-adv-x="488" 
d="M358 -94v94h-235v-94h-107v192h43q14 18 19.5 39t7.5 52l25 511h308v-602h48v-192h-109zM194 191q-2 -40 -9.5 -60t-21.5 -33h144v502h-93z" />
    <glyph glyph-name="uni0435" unicode="&#x435;" horiz-adv-x="368" 
d="M150 405h151v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195z" />
    <glyph glyph-name="uni0436" unicode="&#x436;" horiz-adv-x="604" 
d="M357 417l130 283h110l-135 -284l135 -416h-113l-100 310l-27 -53v-257h-109v257l-27 54l-100 -311h-113l136 413l-136 287h110l130 -283v283h109v-283z" />
    <glyph glyph-name="uni0437" unicode="&#x437;" 
d="M252 528q0 47 -15.5 63.5t-41.5 16.5t-42 -15.5t-16 -53.5v-57h-101v52q0 85 40.5 129.5t121.5 44.5t122.5 -45.5t41.5 -130.5v-18q0 -112 -77 -144q42 -18 59.5 -55.5t17.5 -91.5v-55q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v77h104v-84
q0 -38 15.5 -53.5t41.5 -15.5t41.5 16t15.5 63v55q0 49 -17 69t-55 20h-27v100h33q31 0 48.5 16t17.5 58v39z" />
    <glyph glyph-name="uni0438" unicode="&#x438;" horiz-adv-x="427" 
d="M153 0h-113v700h98v-443l40 154l90 289h121v-700h-99v493l-45 -172z" />
    <glyph glyph-name="uni0439" unicode="&#x439;" horiz-adv-x="429" 
d="M153 0h-113v700h98v-443l40 154l90 289h121v-700h-99v493l-45 -172zM333 856q0 -51 -29 -81t-88 -30q-58 0 -85.5 30t-29.5 81h68q2 -28 13.5 -37t33.5 -9q23 0 35.5 9t14.5 37h67z" />
    <glyph glyph-name="uni043A" unicode="&#x43a;" horiz-adv-x="414" 
d="M184 279l-34 -64v-215h-110v700h110v-305l144 305h110l-153 -312l153 -388h-113z" />
    <glyph glyph-name="uni043B" unicode="&#x43b;" horiz-adv-x="450" 
d="M9 99q19 0 31.5 2t20.5 9.5t11.5 22t4.5 38.5l18 529h311v-700h-112v600h-96l-12 -423q-3 -96 -40.5 -137t-119.5 -41h-17v100z" />
    <glyph glyph-name="uni043C" unicode="&#x43c;" horiz-adv-x="541" 
d="M273 203l75 497h153v-700h-104v502l-76 -502h-104l-82 495v-495h-96v700h153z" />
    <glyph glyph-name="uni043D" unicode="&#x43d;" horiz-adv-x="427" 
d="M150 0h-110v700h110v-300h125v300h112v-700h-112v300h-125v-300z" />
    <glyph glyph-name="uni043E" unicode="&#x43e;" 
d="M141 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v378q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378zM31 532q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-364q0 -85 -43 -130.5t-124 -45.5t-124 45.5t-43 130.5v364z" />
    <glyph glyph-name="uni043F" unicode="&#x43f;" horiz-adv-x="427" 
d="M150 0h-110v700h347v-700h-112v600h-125v-600z" />
    <glyph glyph-name="uni0440" unicode="&#x440;" horiz-adv-x="377" 
d="M202 700q83 0 123.5 -44t40.5 -129v-91q0 -85 -40.5 -129t-123.5 -44h-52v-263h-110v700h162zM150 600v-237h52q26 0 40 14t14 52v105q0 38 -14 52t-40 14h-52z" />
    <glyph glyph-name="uni0441" unicode="&#x441;" horiz-adv-x="386" 
d="M359 261v-93q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v364q0 85 41.5 130.5t122.5 45.5t122.5 -45.5t41.5 -130.5v-68h-104v75q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-378q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v100h104z" />
    <glyph glyph-name="uni0442" unicode="&#x442;" horiz-adv-x="356" 
d="M8 700h340v-100h-115v-600h-110v600h-115v100z" />
    <glyph glyph-name="uni0443" unicode="&#x443;" horiz-adv-x="408" 
d="M284 178q-10 -47 -24.5 -82.5t-39 -58t-63 -32.5t-95.5 -6v98q42 -3 66 8t31 49l1 6l-152 540h109l60 -239l34 -164l26 163l50 240h115z" />
    <glyph glyph-name="uni0444" unicode="&#x444;" horiz-adv-x="583" 
d="M236 79h-41q-83 0 -123.5 44t-40.5 129v191q0 85 40.5 129t123.5 44h41v84h108v-84h41q83 0 123.5 -44t40.5 -129v-191q0 -85 -40.5 -129t-123.5 -44h-41v-79h-108v79zM344 519v-344h41q26 0 40.5 16t14.5 54v205q0 38 -14.5 53.5t-40.5 15.5h-41zM236 519h-41
q-26 0 -40.5 -15.5t-14.5 -53.5v-205q0 -38 14.5 -54t40.5 -16h41v344z" />
    <glyph glyph-name="uni0445" unicode="&#x445;" horiz-adv-x="430" 
d="M407 700l-115 -340l123 -360h-116l-90 277l-91 -277h-103l123 360l-115 340h114l83 -258l85 258h102z" />
    <glyph glyph-name="uni0446" unicode="&#x446;" horiz-adv-x="455" 
d="M40 700h110v-600h125v600h112v-602h48v-192h-107v94h-288v700z" />
    <glyph glyph-name="uni0447" unicode="&#x447;" horiz-adv-x="415" 
d="M374 700v-700h-110v282q-18 -20 -44.5 -29.5t-49.5 -9.5q-68 0 -102.5 44.5t-34.5 116.5v296h110v-287q0 -35 19 -52t45 -17t41.5 18.5t15.5 53.5v284h110z" />
    <glyph glyph-name="uni0448" unicode="&#x448;" horiz-adv-x="600" 
d="M40 0v700h110v-600h95v600h110v-600h95v600h110v-700h-520z" />
    <glyph glyph-name="uni0449" unicode="&#x449;" horiz-adv-x="629" 
d="M560 98h49v-192h-107v94h-462v700h110v-600h95v600h110v-600h95v600h110v-602z" />
    <glyph glyph-name="uni044A" unicode="&#x44a;" horiz-adv-x="439" 
d="M8 700h189v-263h52q83 0 123.5 -44t40.5 -129v-91q0 -85 -40.5 -129t-123.5 -44h-162v600h-79v100zM249 100q26 0 40 14t14 52v105q0 38 -14 52t-40 14h-52v-237h52z" />
    <glyph glyph-name="uni044B" unicode="&#x44b;" horiz-adv-x="579" 
d="M40 0v700h110v-263h52q83 0 123.5 -44t40.5 -129v-91q0 -85 -40.5 -129t-123.5 -44h-162zM202 100q26 0 40 14t14 52v105q0 38 -14 52t-40 14h-52v-237h52zM429 0v700h110v-700h-110z" />
    <glyph glyph-name="uni044C" unicode="&#x44c;" horiz-adv-x="392" 
d="M40 0v700h110v-263h52q83 0 123.5 -44t40.5 -129v-91q0 -85 -40.5 -129t-123.5 -44h-162zM202 100q26 0 40 14t14 52v105q0 38 -14 52t-40 14h-52v-237h52z" />
    <glyph glyph-name="uni044D" unicode="&#x44d;" horiz-adv-x="386" 
d="M130 251v-90q0 -38 15.5 -53t41.5 -15t41.5 15t15.5 53v151h-101v100h101v127q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5t-15.5 -53.5v-66h-102v59q0 85 40.5 130.5t121.5 45.5t122.5 -45.5t41.5 -130.5v-364q0 -85 -41.5 -130.5t-122.5 -45.5t-122.5 45.5t-41.5 130.5v83
h104z" />
    <glyph glyph-name="uni044E" unicode="&#x44e;" horiz-adv-x="599" 
d="M232 300h-82v-300h-110v700h110v-300h82v132q0 85 43 130.5t124 45.5t124 -45.5t43 -130.5v-364q0 -85 -43 -130.5t-124 -45.5t-124 45.5t-43 130.5v132zM342 161q0 -38 15.5 -53.5t41.5 -15.5t41.5 15.5t15.5 53.5v378q0 38 -15.5 53.5t-41.5 15.5t-41.5 -15.5
t-15.5 -53.5v-378z" />
    <glyph glyph-name="uni044F" unicode="&#x44f;" horiz-adv-x="402" 
d="M20 0q8 20 10 39.5t2 45.5v108q0 55 15.5 93.5t57.5 55.5q-72 34 -72 142v55q0 81 38.5 121t124.5 40h166v-700h-110v285h-38q-38 0 -55 -20t-17 -69v-110q0 -21 -0.5 -35t-2 -23t-3.5 -15t-4 -13h-112zM199 600q-29 0 -42.5 -17t-13.5 -55v-69q0 -42 17.5 -58t48.5 -16
h43v215h-53z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="325" 
d="M40 700h106l-16 -206h-75zM179 700h106l-16 -206h-75z" />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="190" 
d="M54 155l-14 253v292h110v-292l-14 -253h-82zM148 106v-106h-106v106h106z" />
    <hkern u1="&#x27;" g2="four.alt" k="40" />
    <hkern u1="&#x28;" u2="&#x1ef3;" k="-5" />
    <hkern u1="&#x28;" u2="&#x1ef2;" k="-5" />
    <hkern u1="&#x28;" u2="&#x1e85;" k="-3" />
    <hkern u1="&#x28;" u2="&#x1e84;" k="-3" />
    <hkern u1="&#x28;" u2="&#x1e83;" k="-3" />
    <hkern u1="&#x28;" u2="&#x1e82;" k="-3" />
    <hkern u1="&#x28;" u2="&#x1e81;" k="-3" />
    <hkern u1="&#x28;" u2="&#x1e80;" k="-3" />
    <hkern u1="&#x28;" u2="&#x1e6b;" k="-8" />
    <hkern u1="&#x28;" u2="&#x1e6a;" k="-8" />
    <hkern u1="&#x28;" u2="&#x21b;" k="-8" />
    <hkern u1="&#x28;" u2="&#x21a;" k="-8" />
    <hkern u1="&#x28;" u2="&#x178;" k="-5" />
    <hkern u1="&#x28;" u2="&#x177;" k="-5" />
    <hkern u1="&#x28;" u2="&#x176;" k="-5" />
    <hkern u1="&#x28;" u2="&#x175;" k="-3" />
    <hkern u1="&#x28;" u2="&#x174;" k="-3" />
    <hkern u1="&#x28;" u2="&#x167;" k="-8" />
    <hkern u1="&#x28;" u2="&#x166;" k="-8" />
    <hkern u1="&#x28;" u2="&#x165;" k="-8" />
    <hkern u1="&#x28;" u2="&#x164;" k="-8" />
    <hkern u1="&#x28;" u2="&#x163;" k="-8" />
    <hkern u1="&#x28;" u2="&#x162;" k="-8" />
    <hkern u1="&#x28;" u2="&#xff;" k="-5" />
    <hkern u1="&#x28;" u2="&#xfd;" k="-5" />
    <hkern u1="&#x28;" u2="&#xdd;" k="-5" />
    <hkern u1="&#x28;" u2="y" k="-5" />
    <hkern u1="&#x28;" u2="w" k="-3" />
    <hkern u1="&#x28;" u2="v" k="-3" />
    <hkern u1="&#x28;" u2="t" k="-8" />
    <hkern u1="&#x28;" u2="Y" k="-5" />
    <hkern u1="&#x28;" u2="W" k="-3" />
    <hkern u1="&#x28;" u2="V" k="-3" />
    <hkern u1="&#x28;" u2="T" k="-8" />
    <hkern u1="&#x2c;" u2="&#x1ef3;" k="80" />
    <hkern u1="&#x2c;" u2="&#x1ef2;" k="80" />
    <hkern u1="&#x2c;" u2="&#x1e85;" k="40" />
    <hkern u1="&#x2c;" u2="&#x1e84;" k="40" />
    <hkern u1="&#x2c;" u2="&#x1e83;" k="40" />
    <hkern u1="&#x2c;" u2="&#x1e82;" k="40" />
    <hkern u1="&#x2c;" u2="&#x1e81;" k="40" />
    <hkern u1="&#x2c;" u2="&#x1e80;" k="40" />
    <hkern u1="&#x2c;" u2="&#x1e6b;" k="60" />
    <hkern u1="&#x2c;" u2="&#x1e6a;" k="60" />
    <hkern u1="&#x2c;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x2c;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x2c;" u2="&#x21b;" k="60" />
    <hkern u1="&#x2c;" u2="&#x21a;" k="60" />
    <hkern u1="&#x2c;" u2="&#x219;" k="5" />
    <hkern u1="&#x2c;" u2="&#x218;" k="5" />
    <hkern u1="&#x2c;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x2c;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x2c;" u2="&#x178;" k="80" />
    <hkern u1="&#x2c;" u2="&#x177;" k="80" />
    <hkern u1="&#x2c;" u2="&#x176;" k="80" />
    <hkern u1="&#x2c;" u2="&#x175;" k="40" />
    <hkern u1="&#x2c;" u2="&#x174;" k="40" />
    <hkern u1="&#x2c;" u2="&#x173;" k="6" />
    <hkern u1="&#x2c;" u2="&#x172;" k="6" />
    <hkern u1="&#x2c;" u2="&#x171;" k="6" />
    <hkern u1="&#x2c;" u2="&#x170;" k="6" />
    <hkern u1="&#x2c;" u2="&#x16f;" k="6" />
    <hkern u1="&#x2c;" u2="&#x16e;" k="6" />
    <hkern u1="&#x2c;" u2="&#x16d;" k="6" />
    <hkern u1="&#x2c;" u2="&#x16c;" k="6" />
    <hkern u1="&#x2c;" u2="&#x16b;" k="6" />
    <hkern u1="&#x2c;" u2="&#x16a;" k="6" />
    <hkern u1="&#x2c;" u2="&#x169;" k="6" />
    <hkern u1="&#x2c;" u2="&#x168;" k="6" />
    <hkern u1="&#x2c;" u2="&#x167;" k="60" />
    <hkern u1="&#x2c;" u2="&#x166;" k="60" />
    <hkern u1="&#x2c;" u2="&#x165;" k="60" />
    <hkern u1="&#x2c;" u2="&#x164;" k="60" />
    <hkern u1="&#x2c;" u2="&#x163;" k="60" />
    <hkern u1="&#x2c;" u2="&#x162;" k="60" />
    <hkern u1="&#x2c;" u2="&#x161;" k="5" />
    <hkern u1="&#x2c;" u2="&#x160;" k="5" />
    <hkern u1="&#x2c;" u2="&#x15f;" k="5" />
    <hkern u1="&#x2c;" u2="&#x15e;" k="5" />
    <hkern u1="&#x2c;" u2="&#x15d;" k="5" />
    <hkern u1="&#x2c;" u2="&#x15c;" k="5" />
    <hkern u1="&#x2c;" u2="&#x15b;" k="5" />
    <hkern u1="&#x2c;" u2="&#x15a;" k="5" />
    <hkern u1="&#x2c;" u2="&#x153;" k="10" />
    <hkern u1="&#x2c;" u2="&#x152;" k="10" />
    <hkern u1="&#x2c;" u2="&#x151;" k="10" />
    <hkern u1="&#x2c;" u2="&#x150;" k="10" />
    <hkern u1="&#x2c;" u2="&#x14f;" k="10" />
    <hkern u1="&#x2c;" u2="&#x14e;" k="10" />
    <hkern u1="&#x2c;" u2="&#x14d;" k="10" />
    <hkern u1="&#x2c;" u2="&#x14c;" k="10" />
    <hkern u1="&#x2c;" u2="&#x123;" k="10" />
    <hkern u1="&#x2c;" u2="&#x122;" k="10" />
    <hkern u1="&#x2c;" u2="&#x121;" k="10" />
    <hkern u1="&#x2c;" u2="&#x120;" k="10" />
    <hkern u1="&#x2c;" u2="&#x11f;" k="10" />
    <hkern u1="&#x2c;" u2="&#x11e;" k="10" />
    <hkern u1="&#x2c;" u2="&#x11d;" k="10" />
    <hkern u1="&#x2c;" u2="&#x11c;" k="10" />
    <hkern u1="&#x2c;" u2="&#x10d;" k="10" />
    <hkern u1="&#x2c;" u2="&#x10c;" k="10" />
    <hkern u1="&#x2c;" u2="&#x10b;" k="10" />
    <hkern u1="&#x2c;" u2="&#x10a;" k="10" />
    <hkern u1="&#x2c;" u2="&#x109;" k="10" />
    <hkern u1="&#x2c;" u2="&#x108;" k="10" />
    <hkern u1="&#x2c;" u2="&#x107;" k="10" />
    <hkern u1="&#x2c;" u2="&#x106;" k="10" />
    <hkern u1="&#x2c;" u2="&#xff;" k="80" />
    <hkern u1="&#x2c;" u2="&#xfd;" k="80" />
    <hkern u1="&#x2c;" u2="&#xfc;" k="6" />
    <hkern u1="&#x2c;" u2="&#xfb;" k="6" />
    <hkern u1="&#x2c;" u2="&#xfa;" k="6" />
    <hkern u1="&#x2c;" u2="&#xf9;" k="6" />
    <hkern u1="&#x2c;" u2="&#xf8;" k="10" />
    <hkern u1="&#x2c;" u2="&#xf6;" k="10" />
    <hkern u1="&#x2c;" u2="&#xf5;" k="10" />
    <hkern u1="&#x2c;" u2="&#xf4;" k="10" />
    <hkern u1="&#x2c;" u2="&#xf3;" k="10" />
    <hkern u1="&#x2c;" u2="&#xf2;" k="10" />
    <hkern u1="&#x2c;" u2="&#xe7;" k="10" />
    <hkern u1="&#x2c;" u2="&#xdf;" k="5" />
    <hkern u1="&#x2c;" u2="&#xdd;" k="80" />
    <hkern u1="&#x2c;" u2="&#xdc;" k="6" />
    <hkern u1="&#x2c;" u2="&#xdb;" k="6" />
    <hkern u1="&#x2c;" u2="&#xda;" k="6" />
    <hkern u1="&#x2c;" u2="&#xd9;" k="6" />
    <hkern u1="&#x2c;" u2="&#xd8;" k="10" />
    <hkern u1="&#x2c;" u2="&#xd6;" k="10" />
    <hkern u1="&#x2c;" u2="&#xd5;" k="10" />
    <hkern u1="&#x2c;" u2="&#xd4;" k="10" />
    <hkern u1="&#x2c;" u2="&#xd3;" k="10" />
    <hkern u1="&#x2c;" u2="&#xd2;" k="10" />
    <hkern u1="&#x2c;" u2="&#xc7;" k="10" />
    <hkern u1="&#x2c;" u2="y" k="80" />
    <hkern u1="&#x2c;" u2="w" k="40" />
    <hkern u1="&#x2c;" u2="v" k="60" />
    <hkern u1="&#x2c;" u2="u" k="6" />
    <hkern u1="&#x2c;" u2="t" k="60" />
    <hkern u1="&#x2c;" u2="s" k="5" />
    <hkern u1="&#x2c;" u2="q" k="10" />
    <hkern u1="&#x2c;" u2="o" k="10" />
    <hkern u1="&#x2c;" u2="g" k="10" />
    <hkern u1="&#x2c;" u2="c" k="10" />
    <hkern u1="&#x2c;" u2="Y" k="80" />
    <hkern u1="&#x2c;" u2="W" k="40" />
    <hkern u1="&#x2c;" u2="V" k="60" />
    <hkern u1="&#x2c;" u2="U" k="6" />
    <hkern u1="&#x2c;" u2="T" k="60" />
    <hkern u1="&#x2c;" u2="S" k="5" />
    <hkern u1="&#x2c;" u2="Q" k="10" />
    <hkern u1="&#x2c;" u2="O" k="10" />
    <hkern u1="&#x2c;" u2="G" k="10" />
    <hkern u1="&#x2c;" u2="C" k="10" />
    <hkern u1="&#x2d;" u2="x" k="15" />
    <hkern u1="&#x2d;" u2="v" k="10" />
    <hkern u1="&#x2d;" u2="X" k="15" />
    <hkern u1="&#x2d;" u2="V" k="10" />
    <hkern u1="&#x2e;" u2="&#x1ef3;" k="80" />
    <hkern u1="&#x2e;" u2="&#x1ef2;" k="80" />
    <hkern u1="&#x2e;" u2="&#x1e85;" k="40" />
    <hkern u1="&#x2e;" u2="&#x1e84;" k="40" />
    <hkern u1="&#x2e;" u2="&#x1e83;" k="40" />
    <hkern u1="&#x2e;" u2="&#x1e82;" k="40" />
    <hkern u1="&#x2e;" u2="&#x1e81;" k="40" />
    <hkern u1="&#x2e;" u2="&#x1e80;" k="40" />
    <hkern u1="&#x2e;" u2="&#x1e6b;" k="60" />
    <hkern u1="&#x2e;" u2="&#x1e6a;" k="60" />
    <hkern u1="&#x2e;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x2e;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x2e;" u2="&#x21b;" k="60" />
    <hkern u1="&#x2e;" u2="&#x21a;" k="60" />
    <hkern u1="&#x2e;" u2="&#x219;" k="5" />
    <hkern u1="&#x2e;" u2="&#x218;" k="5" />
    <hkern u1="&#x2e;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x2e;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x2e;" u2="&#x178;" k="80" />
    <hkern u1="&#x2e;" u2="&#x177;" k="80" />
    <hkern u1="&#x2e;" u2="&#x176;" k="80" />
    <hkern u1="&#x2e;" u2="&#x175;" k="40" />
    <hkern u1="&#x2e;" u2="&#x174;" k="40" />
    <hkern u1="&#x2e;" u2="&#x173;" k="6" />
    <hkern u1="&#x2e;" u2="&#x172;" k="6" />
    <hkern u1="&#x2e;" u2="&#x171;" k="6" />
    <hkern u1="&#x2e;" u2="&#x170;" k="6" />
    <hkern u1="&#x2e;" u2="&#x16f;" k="6" />
    <hkern u1="&#x2e;" u2="&#x16e;" k="6" />
    <hkern u1="&#x2e;" u2="&#x16d;" k="6" />
    <hkern u1="&#x2e;" u2="&#x16c;" k="6" />
    <hkern u1="&#x2e;" u2="&#x16b;" k="6" />
    <hkern u1="&#x2e;" u2="&#x16a;" k="6" />
    <hkern u1="&#x2e;" u2="&#x169;" k="6" />
    <hkern u1="&#x2e;" u2="&#x168;" k="6" />
    <hkern u1="&#x2e;" u2="&#x167;" k="60" />
    <hkern u1="&#x2e;" u2="&#x166;" k="60" />
    <hkern u1="&#x2e;" u2="&#x165;" k="60" />
    <hkern u1="&#x2e;" u2="&#x164;" k="60" />
    <hkern u1="&#x2e;" u2="&#x163;" k="60" />
    <hkern u1="&#x2e;" u2="&#x162;" k="60" />
    <hkern u1="&#x2e;" u2="&#x161;" k="5" />
    <hkern u1="&#x2e;" u2="&#x160;" k="5" />
    <hkern u1="&#x2e;" u2="&#x15f;" k="5" />
    <hkern u1="&#x2e;" u2="&#x15e;" k="5" />
    <hkern u1="&#x2e;" u2="&#x15d;" k="5" />
    <hkern u1="&#x2e;" u2="&#x15c;" k="5" />
    <hkern u1="&#x2e;" u2="&#x15b;" k="5" />
    <hkern u1="&#x2e;" u2="&#x15a;" k="5" />
    <hkern u1="&#x2e;" u2="&#x153;" k="10" />
    <hkern u1="&#x2e;" u2="&#x152;" k="10" />
    <hkern u1="&#x2e;" u2="&#x151;" k="10" />
    <hkern u1="&#x2e;" u2="&#x150;" k="10" />
    <hkern u1="&#x2e;" u2="&#x14f;" k="10" />
    <hkern u1="&#x2e;" u2="&#x14e;" k="10" />
    <hkern u1="&#x2e;" u2="&#x14d;" k="10" />
    <hkern u1="&#x2e;" u2="&#x14c;" k="10" />
    <hkern u1="&#x2e;" u2="&#x123;" k="10" />
    <hkern u1="&#x2e;" u2="&#x122;" k="10" />
    <hkern u1="&#x2e;" u2="&#x121;" k="10" />
    <hkern u1="&#x2e;" u2="&#x120;" k="10" />
    <hkern u1="&#x2e;" u2="&#x11f;" k="10" />
    <hkern u1="&#x2e;" u2="&#x11e;" k="10" />
    <hkern u1="&#x2e;" u2="&#x11d;" k="10" />
    <hkern u1="&#x2e;" u2="&#x11c;" k="10" />
    <hkern u1="&#x2e;" u2="&#x10d;" k="10" />
    <hkern u1="&#x2e;" u2="&#x10c;" k="10" />
    <hkern u1="&#x2e;" u2="&#x10b;" k="10" />
    <hkern u1="&#x2e;" u2="&#x10a;" k="10" />
    <hkern u1="&#x2e;" u2="&#x109;" k="10" />
    <hkern u1="&#x2e;" u2="&#x108;" k="10" />
    <hkern u1="&#x2e;" u2="&#x107;" k="10" />
    <hkern u1="&#x2e;" u2="&#x106;" k="10" />
    <hkern u1="&#x2e;" u2="&#xff;" k="80" />
    <hkern u1="&#x2e;" u2="&#xfd;" k="80" />
    <hkern u1="&#x2e;" u2="&#xfc;" k="6" />
    <hkern u1="&#x2e;" u2="&#xfb;" k="6" />
    <hkern u1="&#x2e;" u2="&#xfa;" k="6" />
    <hkern u1="&#x2e;" u2="&#xf9;" k="6" />
    <hkern u1="&#x2e;" u2="&#xf8;" k="10" />
    <hkern u1="&#x2e;" u2="&#xf6;" k="10" />
    <hkern u1="&#x2e;" u2="&#xf5;" k="10" />
    <hkern u1="&#x2e;" u2="&#xf4;" k="10" />
    <hkern u1="&#x2e;" u2="&#xf3;" k="10" />
    <hkern u1="&#x2e;" u2="&#xf2;" k="10" />
    <hkern u1="&#x2e;" u2="&#xe7;" k="10" />
    <hkern u1="&#x2e;" u2="&#xdf;" k="5" />
    <hkern u1="&#x2e;" u2="&#xdd;" k="80" />
    <hkern u1="&#x2e;" u2="&#xdc;" k="6" />
    <hkern u1="&#x2e;" u2="&#xdb;" k="6" />
    <hkern u1="&#x2e;" u2="&#xda;" k="6" />
    <hkern u1="&#x2e;" u2="&#xd9;" k="6" />
    <hkern u1="&#x2e;" u2="&#xd8;" k="10" />
    <hkern u1="&#x2e;" u2="&#xd6;" k="10" />
    <hkern u1="&#x2e;" u2="&#xd5;" k="10" />
    <hkern u1="&#x2e;" u2="&#xd4;" k="10" />
    <hkern u1="&#x2e;" u2="&#xd3;" k="10" />
    <hkern u1="&#x2e;" u2="&#xd2;" k="10" />
    <hkern u1="&#x2e;" u2="&#xc7;" k="10" />
    <hkern u1="&#x2e;" u2="y" k="80" />
    <hkern u1="&#x2e;" u2="w" k="40" />
    <hkern u1="&#x2e;" u2="v" k="60" />
    <hkern u1="&#x2e;" u2="u" k="6" />
    <hkern u1="&#x2e;" u2="t" k="60" />
    <hkern u1="&#x2e;" u2="s" k="5" />
    <hkern u1="&#x2e;" u2="q" k="10" />
    <hkern u1="&#x2e;" u2="o" k="10" />
    <hkern u1="&#x2e;" u2="g" k="10" />
    <hkern u1="&#x2e;" u2="c" k="10" />
    <hkern u1="&#x2e;" u2="Y" k="80" />
    <hkern u1="&#x2e;" u2="W" k="40" />
    <hkern u1="&#x2e;" u2="V" k="60" />
    <hkern u1="&#x2e;" u2="U" k="6" />
    <hkern u1="&#x2e;" u2="T" k="60" />
    <hkern u1="&#x2e;" u2="S" k="5" />
    <hkern u1="&#x2e;" u2="Q" k="10" />
    <hkern u1="&#x2e;" u2="O" k="10" />
    <hkern u1="&#x2e;" u2="G" k="10" />
    <hkern u1="&#x2e;" u2="C" k="10" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="180" />
    <hkern u1="&#x3a;" u2="&#x1ef3;" k="20" />
    <hkern u1="&#x3a;" u2="&#x1ef2;" k="20" />
    <hkern u1="&#x3a;" u2="&#x1e6b;" k="35" />
    <hkern u1="&#x3a;" u2="&#x1e6a;" k="35" />
    <hkern u1="&#x3a;" u2="&#x21b;" k="35" />
    <hkern u1="&#x3a;" u2="&#x21a;" k="35" />
    <hkern u1="&#x3a;" u2="&#x178;" k="20" />
    <hkern u1="&#x3a;" u2="&#x177;" k="20" />
    <hkern u1="&#x3a;" u2="&#x176;" k="20" />
    <hkern u1="&#x3a;" u2="&#x167;" k="35" />
    <hkern u1="&#x3a;" u2="&#x166;" k="35" />
    <hkern u1="&#x3a;" u2="&#x165;" k="35" />
    <hkern u1="&#x3a;" u2="&#x164;" k="35" />
    <hkern u1="&#x3a;" u2="&#x163;" k="35" />
    <hkern u1="&#x3a;" u2="&#x162;" k="35" />
    <hkern u1="&#x3a;" u2="&#xff;" k="20" />
    <hkern u1="&#x3a;" u2="&#xfd;" k="20" />
    <hkern u1="&#x3a;" u2="&#xdd;" k="20" />
    <hkern u1="&#x3a;" u2="y" k="20" />
    <hkern u1="&#x3a;" u2="t" k="35" />
    <hkern u1="&#x3a;" u2="Y" k="20" />
    <hkern u1="&#x3a;" u2="T" k="35" />
    <hkern u1="&#x3b;" u2="&#x1ef3;" k="20" />
    <hkern u1="&#x3b;" u2="&#x1ef2;" k="20" />
    <hkern u1="&#x3b;" u2="&#x1e6b;" k="35" />
    <hkern u1="&#x3b;" u2="&#x1e6a;" k="35" />
    <hkern u1="&#x3b;" u2="&#x21b;" k="35" />
    <hkern u1="&#x3b;" u2="&#x21a;" k="35" />
    <hkern u1="&#x3b;" u2="&#x178;" k="20" />
    <hkern u1="&#x3b;" u2="&#x177;" k="20" />
    <hkern u1="&#x3b;" u2="&#x176;" k="20" />
    <hkern u1="&#x3b;" u2="&#x167;" k="35" />
    <hkern u1="&#x3b;" u2="&#x166;" k="35" />
    <hkern u1="&#x3b;" u2="&#x165;" k="35" />
    <hkern u1="&#x3b;" u2="&#x164;" k="35" />
    <hkern u1="&#x3b;" u2="&#x163;" k="35" />
    <hkern u1="&#x3b;" u2="&#x162;" k="35" />
    <hkern u1="&#x3b;" u2="&#xff;" k="20" />
    <hkern u1="&#x3b;" u2="&#xfd;" k="20" />
    <hkern u1="&#x3b;" u2="&#xdd;" k="20" />
    <hkern u1="&#x3b;" u2="y" k="20" />
    <hkern u1="&#x3b;" u2="t" k="35" />
    <hkern u1="&#x3b;" u2="Y" k="20" />
    <hkern u1="&#x3b;" u2="T" k="35" />
    <hkern u1="A" u2="&#x201d;" k="60" />
    <hkern u1="A" u2="&#x201c;" k="60" />
    <hkern u1="A" u2="&#x2018;" k="60" />
    <hkern u1="A" u2="&#x153;" k="1" />
    <hkern u1="A" u2="&#x152;" k="1" />
    <hkern u1="A" u2="v" k="25" />
    <hkern u1="A" u2="q" k="1" />
    <hkern u1="A" u2="V" k="25" />
    <hkern u1="A" u2="Q" k="1" />
    <hkern u1="A" u2="&#x3f;" k="30" />
    <hkern u1="B" u2="&#x2026;" k="5" />
    <hkern u1="B" u2="&#x201e;" k="5" />
    <hkern u1="B" u2="&#x201d;" k="10" />
    <hkern u1="B" u2="&#x201c;" k="10" />
    <hkern u1="B" u2="&#x201a;" k="5" />
    <hkern u1="B" u2="&#x2018;" k="10" />
    <hkern u1="B" u2="x" k="3" />
    <hkern u1="B" u2="v" k="4" />
    <hkern u1="B" u2="X" k="3" />
    <hkern u1="B" u2="V" k="4" />
    <hkern u1="B" u2="&#x2e;" k="5" />
    <hkern u1="B" u2="&#x2c;" k="5" />
    <hkern u1="C" u2="&#x2026;" k="6" />
    <hkern u1="C" u2="&#x201e;" k="6" />
    <hkern u1="C" u2="&#x201d;" k="6" />
    <hkern u1="C" u2="&#x201c;" k="6" />
    <hkern u1="C" u2="&#x201a;" k="6" />
    <hkern u1="C" u2="&#x2018;" k="6" />
    <hkern u1="C" u2="x" k="11" />
    <hkern u1="C" u2="X" k="11" />
    <hkern u1="C" u2="&#x2e;" k="6" />
    <hkern u1="C" u2="&#x2c;" k="6" />
    <hkern u1="D" u2="&#x2026;" k="10" />
    <hkern u1="D" u2="&#x201e;" k="10" />
    <hkern u1="D" u2="&#x201d;" k="10" />
    <hkern u1="D" u2="&#x201c;" k="10" />
    <hkern u1="D" u2="&#x201a;" k="10" />
    <hkern u1="D" u2="&#x2018;" k="10" />
    <hkern u1="D" u2="x" k="15" />
    <hkern u1="D" u2="v" k="1" />
    <hkern u1="D" u2="X" k="15" />
    <hkern u1="D" u2="V" k="1" />
    <hkern u1="D" u2="&#x2e;" k="10" />
    <hkern u1="D" u2="&#x2c;" k="10" />
    <hkern u1="F" u2="&#x2026;" k="70" />
    <hkern u1="F" u2="&#x201e;" k="70" />
    <hkern u1="F" u2="&#x201a;" k="70" />
    <hkern u1="F" u2="&#x7d;" k="-5" />
    <hkern u1="F" u2="]" k="-5" />
    <hkern u1="F" u2="&#x2e;" k="70" />
    <hkern u1="F" u2="&#x2c;" k="70" />
    <hkern u1="F" u2="&#x29;" k="-5" />
    <hkern u1="G" u2="&#x2026;" k="10" />
    <hkern u1="G" u2="&#x201e;" k="10" />
    <hkern u1="G" u2="&#x201d;" k="10" />
    <hkern u1="G" u2="&#x201c;" k="10" />
    <hkern u1="G" u2="&#x201a;" k="10" />
    <hkern u1="G" u2="&#x2018;" k="10" />
    <hkern u1="G" u2="x" k="15" />
    <hkern u1="G" u2="v" k="1" />
    <hkern u1="G" u2="X" k="15" />
    <hkern u1="G" u2="V" k="1" />
    <hkern u1="G" u2="&#x2e;" k="10" />
    <hkern u1="G" u2="&#x2c;" k="10" />
    <hkern u1="J" u2="&#x2026;" k="6" />
    <hkern u1="J" u2="&#x201e;" k="6" />
    <hkern u1="J" u2="&#x201a;" k="6" />
    <hkern u1="J" u2="&#x2e;" k="6" />
    <hkern u1="J" u2="&#x2c;" k="6" />
    <hkern u1="K" u2="&#x153;" k="7" />
    <hkern u1="K" u2="&#x152;" k="7" />
    <hkern u1="K" u2="q" k="7" />
    <hkern u1="K" u2="Q" k="7" />
    <hkern u1="L" u2="&#x201d;" k="75" />
    <hkern u1="L" u2="&#x201c;" k="75" />
    <hkern u1="L" u2="&#x2018;" k="75" />
    <hkern u1="L" u2="&#x7d;" k="-8" />
    <hkern u1="L" u2="v" k="33" />
    <hkern u1="L" u2="]" k="-8" />
    <hkern u1="L" u2="V" k="33" />
    <hkern u1="L" u2="&#x3f;" k="30" />
    <hkern u1="L" u2="&#x29;" k="-8" />
    <hkern u1="O" u2="&#x2026;" k="10" />
    <hkern u1="O" u2="&#x201e;" k="10" />
    <hkern u1="O" u2="&#x201d;" k="10" />
    <hkern u1="O" u2="&#x201c;" k="10" />
    <hkern u1="O" u2="&#x201a;" k="10" />
    <hkern u1="O" u2="&#x2018;" k="10" />
    <hkern u1="O" u2="x" k="15" />
    <hkern u1="O" u2="v" k="1" />
    <hkern u1="O" u2="X" k="15" />
    <hkern u1="O" u2="V" k="1" />
    <hkern u1="O" u2="&#x2e;" k="10" />
    <hkern u1="O" u2="&#x2c;" k="10" />
    <hkern u1="P" u2="&#x2026;" k="80" />
    <hkern u1="P" u2="&#x201e;" k="80" />
    <hkern u1="P" u2="&#x201a;" k="80" />
    <hkern u1="P" u2="x" k="11" />
    <hkern u1="P" u2="X" k="11" />
    <hkern u1="P" u2="&#x2e;" k="80" />
    <hkern u1="P" u2="&#x2c;" k="80" />
    <hkern u1="Q" u2="&#x201d;" k="10" />
    <hkern u1="Q" u2="&#x201c;" k="10" />
    <hkern u1="Q" u2="&#x2019;" k="10" />
    <hkern u1="Q" u2="&#x2018;" k="10" />
    <hkern u1="Q" u2="&#x1ef3;" k="13" />
    <hkern u1="Q" u2="&#x1ef2;" k="13" />
    <hkern u1="Q" u2="&#x2bc;" k="10" />
    <hkern u1="Q" u2="&#x178;" k="13" />
    <hkern u1="Q" u2="&#x177;" k="13" />
    <hkern u1="Q" u2="&#x176;" k="13" />
    <hkern u1="Q" u2="&#xff;" k="13" />
    <hkern u1="Q" u2="&#xfd;" k="13" />
    <hkern u1="Q" u2="&#xdd;" k="13" />
    <hkern u1="Q" u2="y" k="13" />
    <hkern u1="Q" u2="v" k="1" />
    <hkern u1="Q" u2="Y" k="13" />
    <hkern u1="Q" u2="V" k="1" />
    <hkern u1="R" u2="v" k="2" />
    <hkern u1="R" u2="V" k="2" />
    <hkern u1="S" u2="&#x2026;" k="5" />
    <hkern u1="S" u2="&#x201e;" k="5" />
    <hkern u1="S" u2="&#x201d;" k="5" />
    <hkern u1="S" u2="&#x201c;" k="5" />
    <hkern u1="S" u2="&#x201a;" k="5" />
    <hkern u1="S" u2="&#x2018;" k="5" />
    <hkern u1="S" u2="x" k="8" />
    <hkern u1="S" u2="v" k="1" />
    <hkern u1="S" u2="X" k="8" />
    <hkern u1="S" u2="V" k="1" />
    <hkern u1="S" u2="&#x2e;" k="5" />
    <hkern u1="S" u2="&#x2c;" k="5" />
    <hkern u1="T" u2="&#x2026;" k="60" />
    <hkern u1="T" u2="&#x201e;" k="60" />
    <hkern u1="T" u2="&#x201a;" k="60" />
    <hkern u1="T" u2="&#x7d;" k="-8" />
    <hkern u1="T" u2="]" k="-8" />
    <hkern u1="T" u2="&#x3b;" k="35" />
    <hkern u1="T" u2="&#x3a;" k="35" />
    <hkern u1="T" u2="&#x2e;" k="60" />
    <hkern u1="T" u2="&#x2c;" k="60" />
    <hkern u1="T" u2="&#x29;" k="-8" />
    <hkern u1="U" u2="&#x2026;" k="6" />
    <hkern u1="U" u2="&#x201e;" k="6" />
    <hkern u1="U" u2="&#x201a;" k="6" />
    <hkern u1="U" u2="&#x2e;" k="6" />
    <hkern u1="U" u2="&#x2c;" k="6" />
    <hkern u1="V" u2="&#x2026;" k="60" />
    <hkern u1="V" u2="&#x201e;" k="60" />
    <hkern u1="V" u2="&#x201a;" k="60" />
    <hkern u1="V" u2="&#x237;" k="25" />
    <hkern u1="V" u2="&#x1ff;" k="1" />
    <hkern u1="V" u2="&#x1fe;" k="1" />
    <hkern u1="V" u2="&#x1fd;" k="46" />
    <hkern u1="V" u2="&#x1fc;" k="46" />
    <hkern u1="V" u2="&#x153;" k="1" />
    <hkern u1="V" u2="&#x152;" k="1" />
    <hkern u1="V" u2="&#x151;" k="1" />
    <hkern u1="V" u2="&#x150;" k="1" />
    <hkern u1="V" u2="&#x14f;" k="1" />
    <hkern u1="V" u2="&#x14e;" k="1" />
    <hkern u1="V" u2="&#x14d;" k="1" />
    <hkern u1="V" u2="&#x14c;" k="1" />
    <hkern u1="V" u2="&#x135;" k="25" />
    <hkern u1="V" u2="&#x134;" k="25" />
    <hkern u1="V" u2="&#x133;" k="25" />
    <hkern u1="V" u2="&#x132;" k="25" />
    <hkern u1="V" u2="&#x123;" k="1" />
    <hkern u1="V" u2="&#x122;" k="1" />
    <hkern u1="V" u2="&#x121;" k="1" />
    <hkern u1="V" u2="&#x120;" k="1" />
    <hkern u1="V" u2="&#x11f;" k="1" />
    <hkern u1="V" u2="&#x11e;" k="1" />
    <hkern u1="V" u2="&#x11d;" k="1" />
    <hkern u1="V" u2="&#x11c;" k="1" />
    <hkern u1="V" u2="&#x10d;" k="1" />
    <hkern u1="V" u2="&#x10c;" k="1" />
    <hkern u1="V" u2="&#x10b;" k="1" />
    <hkern u1="V" u2="&#x10a;" k="1" />
    <hkern u1="V" u2="&#x109;" k="1" />
    <hkern u1="V" u2="&#x108;" k="1" />
    <hkern u1="V" u2="&#x107;" k="1" />
    <hkern u1="V" u2="&#x106;" k="1" />
    <hkern u1="V" u2="&#x105;" k="25" />
    <hkern u1="V" u2="&#x104;" k="25" />
    <hkern u1="V" u2="&#x103;" k="25" />
    <hkern u1="V" u2="&#x102;" k="25" />
    <hkern u1="V" u2="&#x101;" k="25" />
    <hkern u1="V" u2="&#x100;" k="25" />
    <hkern u1="V" u2="&#xf8;" k="1" />
    <hkern u1="V" u2="&#xf6;" k="1" />
    <hkern u1="V" u2="&#xf5;" k="1" />
    <hkern u1="V" u2="&#xf4;" k="1" />
    <hkern u1="V" u2="&#xf3;" k="1" />
    <hkern u1="V" u2="&#xf2;" k="1" />
    <hkern u1="V" u2="&#xe7;" k="1" />
    <hkern u1="V" u2="&#xe6;" k="46" />
    <hkern u1="V" u2="&#xe5;" k="25" />
    <hkern u1="V" u2="&#xe4;" k="25" />
    <hkern u1="V" u2="&#xe3;" k="25" />
    <hkern u1="V" u2="&#xe2;" k="25" />
    <hkern u1="V" u2="&#xe1;" k="25" />
    <hkern u1="V" u2="&#xe0;" k="25" />
    <hkern u1="V" u2="&#xd8;" k="1" />
    <hkern u1="V" u2="&#xd6;" k="1" />
    <hkern u1="V" u2="&#xd5;" k="1" />
    <hkern u1="V" u2="&#xd4;" k="1" />
    <hkern u1="V" u2="&#xd3;" k="1" />
    <hkern u1="V" u2="&#xd2;" k="1" />
    <hkern u1="V" u2="&#xc7;" k="1" />
    <hkern u1="V" u2="&#xc6;" k="46" />
    <hkern u1="V" u2="&#xc5;" k="25" />
    <hkern u1="V" u2="&#xc4;" k="25" />
    <hkern u1="V" u2="&#xc3;" k="25" />
    <hkern u1="V" u2="&#xc2;" k="25" />
    <hkern u1="V" u2="&#xc1;" k="25" />
    <hkern u1="V" u2="&#xc0;" k="25" />
    <hkern u1="V" u2="&#xad;" k="10" />
    <hkern u1="V" u2="&#x7d;" k="-3" />
    <hkern u1="V" u2="q" k="1" />
    <hkern u1="V" u2="o" k="1" />
    <hkern u1="V" u2="j" k="25" />
    <hkern u1="V" u2="g" k="1" />
    <hkern u1="V" u2="c" k="1" />
    <hkern u1="V" u2="a" k="25" />
    <hkern u1="V" u2="]" k="-3" />
    <hkern u1="V" u2="Q" k="1" />
    <hkern u1="V" u2="O" k="1" />
    <hkern u1="V" u2="J" k="25" />
    <hkern u1="V" u2="G" k="1" />
    <hkern u1="V" u2="C" k="1" />
    <hkern u1="V" u2="A" k="25" />
    <hkern u1="V" u2="&#x2e;" k="60" />
    <hkern u1="V" u2="&#x2d;" k="10" />
    <hkern u1="V" u2="&#x2c;" k="60" />
    <hkern u1="V" u2="&#x29;" k="-3" />
    <hkern u1="W" u2="&#x2026;" k="40" />
    <hkern u1="W" u2="&#x201e;" k="40" />
    <hkern u1="W" u2="&#x201a;" k="40" />
    <hkern u1="W" u2="&#x7d;" k="-3" />
    <hkern u1="W" u2="]" k="-3" />
    <hkern u1="W" u2="&#x2e;" k="40" />
    <hkern u1="W" u2="&#x2c;" k="40" />
    <hkern u1="W" u2="&#x29;" k="-3" />
    <hkern u1="X" u2="&#x201d;" k="2" />
    <hkern u1="X" u2="&#x201c;" k="2" />
    <hkern u1="X" u2="&#x2019;" k="2" />
    <hkern u1="X" u2="&#x2018;" k="2" />
    <hkern u1="X" u2="&#x1e61;" k="8" />
    <hkern u1="X" u2="&#x1e60;" k="8" />
    <hkern u1="X" u2="&#x2bc;" k="2" />
    <hkern u1="X" u2="&#x219;" k="8" />
    <hkern u1="X" u2="&#x218;" k="8" />
    <hkern u1="X" u2="&#x1ff;" k="15" />
    <hkern u1="X" u2="&#x1fe;" k="15" />
    <hkern u1="X" u2="&#x161;" k="8" />
    <hkern u1="X" u2="&#x160;" k="8" />
    <hkern u1="X" u2="&#x15f;" k="8" />
    <hkern u1="X" u2="&#x15e;" k="8" />
    <hkern u1="X" u2="&#x15d;" k="8" />
    <hkern u1="X" u2="&#x15c;" k="8" />
    <hkern u1="X" u2="&#x15b;" k="8" />
    <hkern u1="X" u2="&#x15a;" k="8" />
    <hkern u1="X" u2="&#x153;" k="15" />
    <hkern u1="X" u2="&#x152;" k="15" />
    <hkern u1="X" u2="&#x151;" k="15" />
    <hkern u1="X" u2="&#x150;" k="15" />
    <hkern u1="X" u2="&#x14f;" k="15" />
    <hkern u1="X" u2="&#x14e;" k="15" />
    <hkern u1="X" u2="&#x14d;" k="15" />
    <hkern u1="X" u2="&#x14c;" k="15" />
    <hkern u1="X" u2="&#x123;" k="15" />
    <hkern u1="X" u2="&#x122;" k="15" />
    <hkern u1="X" u2="&#x121;" k="15" />
    <hkern u1="X" u2="&#x120;" k="15" />
    <hkern u1="X" u2="&#x11f;" k="15" />
    <hkern u1="X" u2="&#x11e;" k="15" />
    <hkern u1="X" u2="&#x11d;" k="15" />
    <hkern u1="X" u2="&#x11c;" k="15" />
    <hkern u1="X" u2="&#x10d;" k="15" />
    <hkern u1="X" u2="&#x10c;" k="15" />
    <hkern u1="X" u2="&#x10b;" k="15" />
    <hkern u1="X" u2="&#x10a;" k="15" />
    <hkern u1="X" u2="&#x109;" k="15" />
    <hkern u1="X" u2="&#x108;" k="15" />
    <hkern u1="X" u2="&#x107;" k="15" />
    <hkern u1="X" u2="&#x106;" k="15" />
    <hkern u1="X" u2="&#xf8;" k="15" />
    <hkern u1="X" u2="&#xf6;" k="15" />
    <hkern u1="X" u2="&#xf5;" k="15" />
    <hkern u1="X" u2="&#xf4;" k="15" />
    <hkern u1="X" u2="&#xf3;" k="15" />
    <hkern u1="X" u2="&#xf2;" k="15" />
    <hkern u1="X" u2="&#xe7;" k="15" />
    <hkern u1="X" u2="&#xdf;" k="8" />
    <hkern u1="X" u2="&#xd8;" k="15" />
    <hkern u1="X" u2="&#xd6;" k="15" />
    <hkern u1="X" u2="&#xd5;" k="15" />
    <hkern u1="X" u2="&#xd4;" k="15" />
    <hkern u1="X" u2="&#xd3;" k="15" />
    <hkern u1="X" u2="&#xd2;" k="15" />
    <hkern u1="X" u2="&#xc7;" k="15" />
    <hkern u1="X" u2="&#xad;" k="15" />
    <hkern u1="X" u2="s" k="8" />
    <hkern u1="X" u2="q" k="15" />
    <hkern u1="X" u2="o" k="15" />
    <hkern u1="X" u2="g" k="15" />
    <hkern u1="X" u2="c" k="15" />
    <hkern u1="X" u2="S" k="8" />
    <hkern u1="X" u2="Q" k="15" />
    <hkern u1="X" u2="O" k="15" />
    <hkern u1="X" u2="G" k="15" />
    <hkern u1="X" u2="C" k="15" />
    <hkern u1="X" u2="&#x2d;" k="15" />
    <hkern u1="Y" u2="&#x2026;" k="80" />
    <hkern u1="Y" u2="&#x201e;" k="80" />
    <hkern u1="Y" u2="&#x201a;" k="80" />
    <hkern u1="Y" u2="&#x153;" k="15" />
    <hkern u1="Y" u2="&#x152;" k="15" />
    <hkern u1="Y" u2="&#x7d;" k="-5" />
    <hkern u1="Y" u2="q" k="15" />
    <hkern u1="Y" u2="]" k="-5" />
    <hkern u1="Y" u2="Q" k="15" />
    <hkern u1="Y" u2="&#x3b;" k="20" />
    <hkern u1="Y" u2="&#x3a;" k="20" />
    <hkern u1="Y" u2="&#x2e;" k="80" />
    <hkern u1="Y" u2="&#x2c;" k="80" />
    <hkern u1="Y" u2="&#x29;" k="-5" />
    <hkern u1="[" u2="&#x1ef3;" k="-5" />
    <hkern u1="[" u2="&#x1ef2;" k="-5" />
    <hkern u1="[" u2="&#x1e85;" k="-3" />
    <hkern u1="[" u2="&#x1e84;" k="-3" />
    <hkern u1="[" u2="&#x1e83;" k="-3" />
    <hkern u1="[" u2="&#x1e82;" k="-3" />
    <hkern u1="[" u2="&#x1e81;" k="-3" />
    <hkern u1="[" u2="&#x1e80;" k="-3" />
    <hkern u1="[" u2="&#x1e6b;" k="-8" />
    <hkern u1="[" u2="&#x1e6a;" k="-8" />
    <hkern u1="[" u2="&#x21b;" k="-8" />
    <hkern u1="[" u2="&#x21a;" k="-8" />
    <hkern u1="[" u2="&#x178;" k="-5" />
    <hkern u1="[" u2="&#x177;" k="-5" />
    <hkern u1="[" u2="&#x176;" k="-5" />
    <hkern u1="[" u2="&#x175;" k="-3" />
    <hkern u1="[" u2="&#x174;" k="-3" />
    <hkern u1="[" u2="&#x167;" k="-8" />
    <hkern u1="[" u2="&#x166;" k="-8" />
    <hkern u1="[" u2="&#x165;" k="-8" />
    <hkern u1="[" u2="&#x164;" k="-8" />
    <hkern u1="[" u2="&#x163;" k="-8" />
    <hkern u1="[" u2="&#x162;" k="-8" />
    <hkern u1="[" u2="&#xff;" k="-5" />
    <hkern u1="[" u2="&#xfd;" k="-5" />
    <hkern u1="[" u2="&#xdd;" k="-5" />
    <hkern u1="[" u2="y" k="-5" />
    <hkern u1="[" u2="w" k="-3" />
    <hkern u1="[" u2="v" k="-3" />
    <hkern u1="[" u2="t" k="-8" />
    <hkern u1="[" u2="Y" k="-5" />
    <hkern u1="[" u2="W" k="-3" />
    <hkern u1="[" u2="V" k="-3" />
    <hkern u1="[" u2="T" k="-8" />
    <hkern u1="a" u2="&#x201d;" k="60" />
    <hkern u1="a" u2="&#x201c;" k="60" />
    <hkern u1="a" u2="&#x2018;" k="60" />
    <hkern u1="a" u2="&#x153;" k="1" />
    <hkern u1="a" u2="&#x152;" k="1" />
    <hkern u1="a" u2="v" k="25" />
    <hkern u1="a" u2="q" k="1" />
    <hkern u1="a" u2="V" k="25" />
    <hkern u1="a" u2="Q" k="1" />
    <hkern u1="a" u2="&#x3f;" k="30" />
    <hkern u1="b" u2="&#x2026;" k="5" />
    <hkern u1="b" u2="&#x201e;" k="5" />
    <hkern u1="b" u2="&#x201d;" k="10" />
    <hkern u1="b" u2="&#x201c;" k="10" />
    <hkern u1="b" u2="&#x201a;" k="5" />
    <hkern u1="b" u2="&#x2018;" k="10" />
    <hkern u1="b" u2="x" k="3" />
    <hkern u1="b" u2="v" k="4" />
    <hkern u1="b" u2="X" k="3" />
    <hkern u1="b" u2="V" k="4" />
    <hkern u1="b" u2="&#x2e;" k="5" />
    <hkern u1="b" u2="&#x2c;" k="5" />
    <hkern u1="c" u2="&#x2026;" k="6" />
    <hkern u1="c" u2="&#x201e;" k="6" />
    <hkern u1="c" u2="&#x201d;" k="6" />
    <hkern u1="c" u2="&#x201c;" k="6" />
    <hkern u1="c" u2="&#x201a;" k="6" />
    <hkern u1="c" u2="&#x2018;" k="6" />
    <hkern u1="c" u2="x" k="11" />
    <hkern u1="c" u2="X" k="11" />
    <hkern u1="c" u2="&#x2e;" k="6" />
    <hkern u1="c" u2="&#x2c;" k="6" />
    <hkern u1="d" u2="&#x2026;" k="10" />
    <hkern u1="d" u2="&#x201e;" k="10" />
    <hkern u1="d" u2="&#x201d;" k="10" />
    <hkern u1="d" u2="&#x201c;" k="10" />
    <hkern u1="d" u2="&#x201a;" k="10" />
    <hkern u1="d" u2="&#x2018;" k="10" />
    <hkern u1="d" u2="x" k="15" />
    <hkern u1="d" u2="v" k="1" />
    <hkern u1="d" u2="X" k="15" />
    <hkern u1="d" u2="V" k="1" />
    <hkern u1="d" u2="&#x2e;" k="10" />
    <hkern u1="d" u2="&#x2c;" k="10" />
    <hkern u1="f" u2="&#x2026;" k="70" />
    <hkern u1="f" u2="&#x201e;" k="70" />
    <hkern u1="f" u2="&#x201a;" k="70" />
    <hkern u1="f" u2="&#x7d;" k="-5" />
    <hkern u1="f" u2="]" k="-5" />
    <hkern u1="f" u2="&#x2e;" k="70" />
    <hkern u1="f" u2="&#x2c;" k="70" />
    <hkern u1="f" u2="&#x29;" k="-5" />
    <hkern u1="g" u2="&#x2026;" k="10" />
    <hkern u1="g" u2="&#x201e;" k="10" />
    <hkern u1="g" u2="&#x201d;" k="10" />
    <hkern u1="g" u2="&#x201c;" k="10" />
    <hkern u1="g" u2="&#x201a;" k="10" />
    <hkern u1="g" u2="&#x2018;" k="10" />
    <hkern u1="g" u2="x" k="15" />
    <hkern u1="g" u2="v" k="1" />
    <hkern u1="g" u2="X" k="15" />
    <hkern u1="g" u2="V" k="1" />
    <hkern u1="g" u2="&#x2e;" k="10" />
    <hkern u1="g" u2="&#x2c;" k="10" />
    <hkern u1="j" u2="&#x2026;" k="6" />
    <hkern u1="j" u2="&#x201e;" k="6" />
    <hkern u1="j" u2="&#x201a;" k="6" />
    <hkern u1="j" u2="&#x2e;" k="6" />
    <hkern u1="j" u2="&#x2c;" k="6" />
    <hkern u1="k" u2="&#x153;" k="7" />
    <hkern u1="k" u2="&#x152;" k="7" />
    <hkern u1="k" u2="q" k="7" />
    <hkern u1="k" u2="Q" k="7" />
    <hkern u1="l" u2="&#x201d;" k="75" />
    <hkern u1="l" u2="&#x201c;" k="75" />
    <hkern u1="l" u2="&#x2018;" k="75" />
    <hkern u1="l" u2="&#x7d;" k="-8" />
    <hkern u1="l" u2="v" k="33" />
    <hkern u1="l" u2="]" k="-8" />
    <hkern u1="l" u2="V" k="33" />
    <hkern u1="l" u2="&#x3f;" k="30" />
    <hkern u1="l" u2="&#x29;" k="-8" />
    <hkern u1="o" u2="&#x2026;" k="10" />
    <hkern u1="o" u2="&#x201e;" k="10" />
    <hkern u1="o" u2="&#x201d;" k="10" />
    <hkern u1="o" u2="&#x201c;" k="10" />
    <hkern u1="o" u2="&#x201a;" k="10" />
    <hkern u1="o" u2="&#x2018;" k="10" />
    <hkern u1="o" u2="x" k="15" />
    <hkern u1="o" u2="v" k="1" />
    <hkern u1="o" u2="X" k="15" />
    <hkern u1="o" u2="V" k="1" />
    <hkern u1="o" u2="&#x2e;" k="10" />
    <hkern u1="o" u2="&#x2c;" k="10" />
    <hkern u1="p" u2="&#x2026;" k="80" />
    <hkern u1="p" u2="&#x201e;" k="80" />
    <hkern u1="p" u2="&#x201a;" k="80" />
    <hkern u1="p" u2="x" k="11" />
    <hkern u1="p" u2="X" k="11" />
    <hkern u1="p" u2="&#x2e;" k="80" />
    <hkern u1="p" u2="&#x2c;" k="80" />
    <hkern u1="q" u2="&#x201d;" k="10" />
    <hkern u1="q" u2="&#x201c;" k="10" />
    <hkern u1="q" u2="&#x2019;" k="10" />
    <hkern u1="q" u2="&#x2018;" k="10" />
    <hkern u1="q" u2="&#x1ef3;" k="13" />
    <hkern u1="q" u2="&#x1ef2;" k="13" />
    <hkern u1="q" u2="&#x2bc;" k="10" />
    <hkern u1="q" u2="&#x178;" k="13" />
    <hkern u1="q" u2="&#x177;" k="13" />
    <hkern u1="q" u2="&#x176;" k="13" />
    <hkern u1="q" u2="&#xff;" k="13" />
    <hkern u1="q" u2="&#xfd;" k="13" />
    <hkern u1="q" u2="&#xdd;" k="13" />
    <hkern u1="q" u2="y" k="13" />
    <hkern u1="q" u2="v" k="1" />
    <hkern u1="q" u2="Y" k="13" />
    <hkern u1="q" u2="V" k="1" />
    <hkern u1="r" u2="v" k="2" />
    <hkern u1="r" u2="V" k="2" />
    <hkern u1="s" u2="&#x2026;" k="5" />
    <hkern u1="s" u2="&#x201e;" k="5" />
    <hkern u1="s" u2="&#x201d;" k="5" />
    <hkern u1="s" u2="&#x201c;" k="5" />
    <hkern u1="s" u2="&#x201a;" k="5" />
    <hkern u1="s" u2="&#x2018;" k="5" />
    <hkern u1="s" u2="x" k="8" />
    <hkern u1="s" u2="v" k="1" />
    <hkern u1="s" u2="X" k="8" />
    <hkern u1="s" u2="V" k="1" />
    <hkern u1="s" u2="&#x2e;" k="5" />
    <hkern u1="s" u2="&#x2c;" k="5" />
    <hkern u1="t" u2="&#x2026;" k="60" />
    <hkern u1="t" u2="&#x201e;" k="60" />
    <hkern u1="t" u2="&#x201a;" k="60" />
    <hkern u1="t" u2="&#x7d;" k="-8" />
    <hkern u1="t" u2="]" k="-8" />
    <hkern u1="t" u2="&#x3b;" k="35" />
    <hkern u1="t" u2="&#x3a;" k="35" />
    <hkern u1="t" u2="&#x2e;" k="60" />
    <hkern u1="t" u2="&#x2c;" k="60" />
    <hkern u1="t" u2="&#x29;" k="-8" />
    <hkern u1="u" u2="&#x2026;" k="6" />
    <hkern u1="u" u2="&#x201e;" k="6" />
    <hkern u1="u" u2="&#x201a;" k="6" />
    <hkern u1="u" u2="&#x2e;" k="6" />
    <hkern u1="u" u2="&#x2c;" k="6" />
    <hkern u1="v" u2="&#x2026;" k="60" />
    <hkern u1="v" u2="&#x201e;" k="60" />
    <hkern u1="v" u2="&#x201a;" k="60" />
    <hkern u1="v" u2="&#x237;" k="25" />
    <hkern u1="v" u2="&#x1ff;" k="1" />
    <hkern u1="v" u2="&#x1fe;" k="1" />
    <hkern u1="v" u2="&#x1fd;" k="46" />
    <hkern u1="v" u2="&#x1fc;" k="46" />
    <hkern u1="v" u2="&#x153;" k="1" />
    <hkern u1="v" u2="&#x152;" k="1" />
    <hkern u1="v" u2="&#x151;" k="1" />
    <hkern u1="v" u2="&#x150;" k="1" />
    <hkern u1="v" u2="&#x14f;" k="1" />
    <hkern u1="v" u2="&#x14e;" k="1" />
    <hkern u1="v" u2="&#x14d;" k="1" />
    <hkern u1="v" u2="&#x14c;" k="1" />
    <hkern u1="v" u2="&#x135;" k="25" />
    <hkern u1="v" u2="&#x134;" k="25" />
    <hkern u1="v" u2="&#x133;" k="25" />
    <hkern u1="v" u2="&#x132;" k="25" />
    <hkern u1="v" u2="&#x123;" k="1" />
    <hkern u1="v" u2="&#x122;" k="1" />
    <hkern u1="v" u2="&#x121;" k="1" />
    <hkern u1="v" u2="&#x120;" k="1" />
    <hkern u1="v" u2="&#x11f;" k="1" />
    <hkern u1="v" u2="&#x11e;" k="1" />
    <hkern u1="v" u2="&#x11d;" k="1" />
    <hkern u1="v" u2="&#x11c;" k="1" />
    <hkern u1="v" u2="&#x10d;" k="1" />
    <hkern u1="v" u2="&#x10c;" k="1" />
    <hkern u1="v" u2="&#x10b;" k="1" />
    <hkern u1="v" u2="&#x10a;" k="1" />
    <hkern u1="v" u2="&#x109;" k="1" />
    <hkern u1="v" u2="&#x108;" k="1" />
    <hkern u1="v" u2="&#x107;" k="1" />
    <hkern u1="v" u2="&#x106;" k="1" />
    <hkern u1="v" u2="&#x105;" k="25" />
    <hkern u1="v" u2="&#x104;" k="25" />
    <hkern u1="v" u2="&#x103;" k="25" />
    <hkern u1="v" u2="&#x102;" k="25" />
    <hkern u1="v" u2="&#x101;" k="25" />
    <hkern u1="v" u2="&#x100;" k="25" />
    <hkern u1="v" u2="&#xf8;" k="1" />
    <hkern u1="v" u2="&#xf6;" k="1" />
    <hkern u1="v" u2="&#xf5;" k="1" />
    <hkern u1="v" u2="&#xf4;" k="1" />
    <hkern u1="v" u2="&#xf3;" k="1" />
    <hkern u1="v" u2="&#xf2;" k="1" />
    <hkern u1="v" u2="&#xe7;" k="1" />
    <hkern u1="v" u2="&#xe6;" k="46" />
    <hkern u1="v" u2="&#xe5;" k="25" />
    <hkern u1="v" u2="&#xe4;" k="25" />
    <hkern u1="v" u2="&#xe3;" k="25" />
    <hkern u1="v" u2="&#xe2;" k="25" />
    <hkern u1="v" u2="&#xe1;" k="25" />
    <hkern u1="v" u2="&#xe0;" k="25" />
    <hkern u1="v" u2="&#xd8;" k="1" />
    <hkern u1="v" u2="&#xd6;" k="1" />
    <hkern u1="v" u2="&#xd5;" k="1" />
    <hkern u1="v" u2="&#xd4;" k="1" />
    <hkern u1="v" u2="&#xd3;" k="1" />
    <hkern u1="v" u2="&#xd2;" k="1" />
    <hkern u1="v" u2="&#xc7;" k="1" />
    <hkern u1="v" u2="&#xc6;" k="46" />
    <hkern u1="v" u2="&#xc5;" k="25" />
    <hkern u1="v" u2="&#xc4;" k="25" />
    <hkern u1="v" u2="&#xc3;" k="25" />
    <hkern u1="v" u2="&#xc2;" k="25" />
    <hkern u1="v" u2="&#xc1;" k="25" />
    <hkern u1="v" u2="&#xc0;" k="25" />
    <hkern u1="v" u2="&#xad;" k="10" />
    <hkern u1="v" u2="&#x7d;" k="-3" />
    <hkern u1="v" u2="q" k="1" />
    <hkern u1="v" u2="o" k="1" />
    <hkern u1="v" u2="j" k="25" />
    <hkern u1="v" u2="g" k="1" />
    <hkern u1="v" u2="c" k="1" />
    <hkern u1="v" u2="a" k="25" />
    <hkern u1="v" u2="]" k="-3" />
    <hkern u1="v" u2="Q" k="1" />
    <hkern u1="v" u2="O" k="1" />
    <hkern u1="v" u2="J" k="25" />
    <hkern u1="v" u2="G" k="1" />
    <hkern u1="v" u2="C" k="1" />
    <hkern u1="v" u2="A" k="25" />
    <hkern u1="v" u2="&#x2e;" k="60" />
    <hkern u1="v" u2="&#x2d;" k="10" />
    <hkern u1="v" u2="&#x2c;" k="60" />
    <hkern u1="v" u2="&#x29;" k="-3" />
    <hkern u1="w" u2="&#x2026;" k="40" />
    <hkern u1="w" u2="&#x201e;" k="40" />
    <hkern u1="w" u2="&#x201a;" k="40" />
    <hkern u1="w" u2="&#x7d;" k="-3" />
    <hkern u1="w" u2="]" k="-3" />
    <hkern u1="w" u2="&#x2e;" k="40" />
    <hkern u1="w" u2="&#x2c;" k="40" />
    <hkern u1="w" u2="&#x29;" k="-3" />
    <hkern u1="x" u2="&#x201d;" k="2" />
    <hkern u1="x" u2="&#x201c;" k="2" />
    <hkern u1="x" u2="&#x2019;" k="2" />
    <hkern u1="x" u2="&#x2018;" k="2" />
    <hkern u1="x" u2="&#x1e61;" k="8" />
    <hkern u1="x" u2="&#x1e60;" k="8" />
    <hkern u1="x" u2="&#x2bc;" k="2" />
    <hkern u1="x" u2="&#x219;" k="8" />
    <hkern u1="x" u2="&#x218;" k="8" />
    <hkern u1="x" u2="&#x1ff;" k="15" />
    <hkern u1="x" u2="&#x1fe;" k="15" />
    <hkern u1="x" u2="&#x161;" k="8" />
    <hkern u1="x" u2="&#x160;" k="8" />
    <hkern u1="x" u2="&#x15f;" k="8" />
    <hkern u1="x" u2="&#x15e;" k="8" />
    <hkern u1="x" u2="&#x15d;" k="8" />
    <hkern u1="x" u2="&#x15c;" k="8" />
    <hkern u1="x" u2="&#x15b;" k="8" />
    <hkern u1="x" u2="&#x15a;" k="8" />
    <hkern u1="x" u2="&#x153;" k="15" />
    <hkern u1="x" u2="&#x152;" k="15" />
    <hkern u1="x" u2="&#x151;" k="15" />
    <hkern u1="x" u2="&#x150;" k="15" />
    <hkern u1="x" u2="&#x14f;" k="15" />
    <hkern u1="x" u2="&#x14e;" k="15" />
    <hkern u1="x" u2="&#x14d;" k="15" />
    <hkern u1="x" u2="&#x14c;" k="15" />
    <hkern u1="x" u2="&#x123;" k="15" />
    <hkern u1="x" u2="&#x122;" k="15" />
    <hkern u1="x" u2="&#x121;" k="15" />
    <hkern u1="x" u2="&#x120;" k="15" />
    <hkern u1="x" u2="&#x11f;" k="15" />
    <hkern u1="x" u2="&#x11e;" k="15" />
    <hkern u1="x" u2="&#x11d;" k="15" />
    <hkern u1="x" u2="&#x11c;" k="15" />
    <hkern u1="x" u2="&#x10d;" k="15" />
    <hkern u1="x" u2="&#x10c;" k="15" />
    <hkern u1="x" u2="&#x10b;" k="15" />
    <hkern u1="x" u2="&#x10a;" k="15" />
    <hkern u1="x" u2="&#x109;" k="15" />
    <hkern u1="x" u2="&#x108;" k="15" />
    <hkern u1="x" u2="&#x107;" k="15" />
    <hkern u1="x" u2="&#x106;" k="15" />
    <hkern u1="x" u2="&#xf8;" k="15" />
    <hkern u1="x" u2="&#xf6;" k="15" />
    <hkern u1="x" u2="&#xf5;" k="15" />
    <hkern u1="x" u2="&#xf4;" k="15" />
    <hkern u1="x" u2="&#xf3;" k="15" />
    <hkern u1="x" u2="&#xf2;" k="15" />
    <hkern u1="x" u2="&#xe7;" k="15" />
    <hkern u1="x" u2="&#xdf;" k="8" />
    <hkern u1="x" u2="&#xd8;" k="15" />
    <hkern u1="x" u2="&#xd6;" k="15" />
    <hkern u1="x" u2="&#xd5;" k="15" />
    <hkern u1="x" u2="&#xd4;" k="15" />
    <hkern u1="x" u2="&#xd3;" k="15" />
    <hkern u1="x" u2="&#xd2;" k="15" />
    <hkern u1="x" u2="&#xc7;" k="15" />
    <hkern u1="x" u2="&#xad;" k="15" />
    <hkern u1="x" u2="s" k="8" />
    <hkern u1="x" u2="q" k="15" />
    <hkern u1="x" u2="o" k="15" />
    <hkern u1="x" u2="g" k="15" />
    <hkern u1="x" u2="c" k="15" />
    <hkern u1="x" u2="S" k="8" />
    <hkern u1="x" u2="Q" k="15" />
    <hkern u1="x" u2="O" k="15" />
    <hkern u1="x" u2="G" k="15" />
    <hkern u1="x" u2="C" k="15" />
    <hkern u1="x" u2="&#x2d;" k="15" />
    <hkern u1="y" u2="&#x2026;" k="80" />
    <hkern u1="y" u2="&#x201e;" k="80" />
    <hkern u1="y" u2="&#x201a;" k="80" />
    <hkern u1="y" u2="&#x153;" k="15" />
    <hkern u1="y" u2="&#x152;" k="15" />
    <hkern u1="y" u2="&#x7d;" k="-5" />
    <hkern u1="y" u2="q" k="15" />
    <hkern u1="y" u2="]" k="-5" />
    <hkern u1="y" u2="Q" k="15" />
    <hkern u1="y" u2="&#x3b;" k="20" />
    <hkern u1="y" u2="&#x3a;" k="20" />
    <hkern u1="y" u2="&#x2e;" k="80" />
    <hkern u1="y" u2="&#x2c;" k="80" />
    <hkern u1="y" u2="&#x29;" k="-5" />
    <hkern u1="&#x7b;" u2="&#x1ef3;" k="-5" />
    <hkern u1="&#x7b;" u2="&#x1ef2;" k="-5" />
    <hkern u1="&#x7b;" u2="&#x1e85;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x1e84;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x1e83;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x1e82;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x1e81;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x1e80;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x1e6b;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x1e6a;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x21b;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x21a;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x178;" k="-5" />
    <hkern u1="&#x7b;" u2="&#x177;" k="-5" />
    <hkern u1="&#x7b;" u2="&#x176;" k="-5" />
    <hkern u1="&#x7b;" u2="&#x175;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x174;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x167;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x166;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x165;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x164;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x163;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x162;" k="-8" />
    <hkern u1="&#x7b;" u2="&#xff;" k="-5" />
    <hkern u1="&#x7b;" u2="&#xfd;" k="-5" />
    <hkern u1="&#x7b;" u2="&#xdd;" k="-5" />
    <hkern u1="&#x7b;" u2="y" k="-5" />
    <hkern u1="&#x7b;" u2="w" k="-3" />
    <hkern u1="&#x7b;" u2="v" k="-3" />
    <hkern u1="&#x7b;" u2="t" k="-8" />
    <hkern u1="&#x7b;" u2="Y" k="-5" />
    <hkern u1="&#x7b;" u2="W" k="-3" />
    <hkern u1="&#x7b;" u2="V" k="-3" />
    <hkern u1="&#x7b;" u2="T" k="-8" />
    <hkern u1="&#xad;" u2="x" k="15" />
    <hkern u1="&#xad;" u2="v" k="10" />
    <hkern u1="&#xad;" u2="X" k="15" />
    <hkern u1="&#xad;" u2="V" k="10" />
    <hkern u1="&#xbf;" u2="&#x1ef3;" k="30" />
    <hkern u1="&#xbf;" u2="&#x1ef2;" k="30" />
    <hkern u1="&#xbf;" u2="&#x1e85;" k="20" />
    <hkern u1="&#xbf;" u2="&#x1e84;" k="20" />
    <hkern u1="&#xbf;" u2="&#x1e83;" k="20" />
    <hkern u1="&#xbf;" u2="&#x1e82;" k="20" />
    <hkern u1="&#xbf;" u2="&#x1e81;" k="20" />
    <hkern u1="&#xbf;" u2="&#x1e80;" k="20" />
    <hkern u1="&#xbf;" u2="&#x1e6b;" k="42" />
    <hkern u1="&#xbf;" u2="&#x1e6a;" k="42" />
    <hkern u1="&#xbf;" u2="&#x21b;" k="42" />
    <hkern u1="&#xbf;" u2="&#x21a;" k="42" />
    <hkern u1="&#xbf;" u2="&#x178;" k="30" />
    <hkern u1="&#xbf;" u2="&#x177;" k="30" />
    <hkern u1="&#xbf;" u2="&#x176;" k="30" />
    <hkern u1="&#xbf;" u2="&#x175;" k="20" />
    <hkern u1="&#xbf;" u2="&#x174;" k="20" />
    <hkern u1="&#xbf;" u2="&#x167;" k="42" />
    <hkern u1="&#xbf;" u2="&#x166;" k="42" />
    <hkern u1="&#xbf;" u2="&#x165;" k="42" />
    <hkern u1="&#xbf;" u2="&#x164;" k="42" />
    <hkern u1="&#xbf;" u2="&#x163;" k="42" />
    <hkern u1="&#xbf;" u2="&#x162;" k="42" />
    <hkern u1="&#xbf;" u2="&#xff;" k="30" />
    <hkern u1="&#xbf;" u2="&#xfd;" k="30" />
    <hkern u1="&#xbf;" u2="&#xdd;" k="30" />
    <hkern u1="&#xbf;" u2="y" k="30" />
    <hkern u1="&#xbf;" u2="w" k="20" />
    <hkern u1="&#xbf;" u2="v" k="30" />
    <hkern u1="&#xbf;" u2="t" k="42" />
    <hkern u1="&#xbf;" u2="Y" k="30" />
    <hkern u1="&#xbf;" u2="W" k="20" />
    <hkern u1="&#xbf;" u2="V" k="30" />
    <hkern u1="&#xbf;" u2="T" k="42" />
    <hkern u1="&#xc0;" u2="&#x201d;" k="60" />
    <hkern u1="&#xc0;" u2="&#x201c;" k="60" />
    <hkern u1="&#xc0;" u2="&#x2018;" k="60" />
    <hkern u1="&#xc0;" u2="&#x153;" k="1" />
    <hkern u1="&#xc0;" u2="&#x152;" k="1" />
    <hkern u1="&#xc0;" u2="v" k="25" />
    <hkern u1="&#xc0;" u2="q" k="1" />
    <hkern u1="&#xc0;" u2="V" k="25" />
    <hkern u1="&#xc0;" u2="Q" k="1" />
    <hkern u1="&#xc0;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc1;" u2="&#x201d;" k="60" />
    <hkern u1="&#xc1;" u2="&#x201c;" k="60" />
    <hkern u1="&#xc1;" u2="&#x2018;" k="60" />
    <hkern u1="&#xc1;" u2="&#x153;" k="1" />
    <hkern u1="&#xc1;" u2="&#x152;" k="1" />
    <hkern u1="&#xc1;" u2="v" k="25" />
    <hkern u1="&#xc1;" u2="q" k="1" />
    <hkern u1="&#xc1;" u2="V" k="25" />
    <hkern u1="&#xc1;" u2="Q" k="1" />
    <hkern u1="&#xc1;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc2;" u2="&#x201d;" k="60" />
    <hkern u1="&#xc2;" u2="&#x201c;" k="60" />
    <hkern u1="&#xc2;" u2="&#x2018;" k="60" />
    <hkern u1="&#xc2;" u2="&#x153;" k="1" />
    <hkern u1="&#xc2;" u2="&#x152;" k="1" />
    <hkern u1="&#xc2;" u2="v" k="25" />
    <hkern u1="&#xc2;" u2="q" k="1" />
    <hkern u1="&#xc2;" u2="V" k="25" />
    <hkern u1="&#xc2;" u2="Q" k="1" />
    <hkern u1="&#xc2;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc3;" u2="&#x201d;" k="60" />
    <hkern u1="&#xc3;" u2="&#x201c;" k="60" />
    <hkern u1="&#xc3;" u2="&#x2018;" k="60" />
    <hkern u1="&#xc3;" u2="&#x153;" k="1" />
    <hkern u1="&#xc3;" u2="&#x152;" k="1" />
    <hkern u1="&#xc3;" u2="v" k="25" />
    <hkern u1="&#xc3;" u2="q" k="1" />
    <hkern u1="&#xc3;" u2="V" k="25" />
    <hkern u1="&#xc3;" u2="Q" k="1" />
    <hkern u1="&#xc3;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc4;" u2="&#x201d;" k="60" />
    <hkern u1="&#xc4;" u2="&#x201c;" k="60" />
    <hkern u1="&#xc4;" u2="&#x2018;" k="60" />
    <hkern u1="&#xc4;" u2="&#x153;" k="1" />
    <hkern u1="&#xc4;" u2="&#x152;" k="1" />
    <hkern u1="&#xc4;" u2="v" k="25" />
    <hkern u1="&#xc4;" u2="q" k="1" />
    <hkern u1="&#xc4;" u2="V" k="25" />
    <hkern u1="&#xc4;" u2="Q" k="1" />
    <hkern u1="&#xc4;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc5;" u2="&#x201d;" k="60" />
    <hkern u1="&#xc5;" u2="&#x201c;" k="60" />
    <hkern u1="&#xc5;" u2="&#x2018;" k="60" />
    <hkern u1="&#xc5;" u2="&#x153;" k="1" />
    <hkern u1="&#xc5;" u2="&#x152;" k="1" />
    <hkern u1="&#xc5;" u2="v" k="25" />
    <hkern u1="&#xc5;" u2="q" k="1" />
    <hkern u1="&#xc5;" u2="V" k="25" />
    <hkern u1="&#xc5;" u2="Q" k="1" />
    <hkern u1="&#xc5;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc7;" u2="&#x2026;" k="6" />
    <hkern u1="&#xc7;" u2="&#x201e;" k="6" />
    <hkern u1="&#xc7;" u2="&#x201d;" k="6" />
    <hkern u1="&#xc7;" u2="&#x201c;" k="6" />
    <hkern u1="&#xc7;" u2="&#x201a;" k="6" />
    <hkern u1="&#xc7;" u2="&#x2018;" k="6" />
    <hkern u1="&#xc7;" u2="x" k="11" />
    <hkern u1="&#xc7;" u2="X" k="11" />
    <hkern u1="&#xc7;" u2="&#x2e;" k="6" />
    <hkern u1="&#xc7;" u2="&#x2c;" k="6" />
    <hkern u1="&#xd0;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd0;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd0;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd0;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd0;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd0;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd0;" u2="x" k="15" />
    <hkern u1="&#xd0;" u2="v" k="1" />
    <hkern u1="&#xd0;" u2="X" k="15" />
    <hkern u1="&#xd0;" u2="V" k="1" />
    <hkern u1="&#xd0;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd0;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd2;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd2;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd2;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd2;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd2;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd2;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd2;" u2="x" k="15" />
    <hkern u1="&#xd2;" u2="v" k="1" />
    <hkern u1="&#xd2;" u2="X" k="15" />
    <hkern u1="&#xd2;" u2="V" k="1" />
    <hkern u1="&#xd2;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd2;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd3;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd3;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd3;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd3;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd3;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd3;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd3;" u2="x" k="15" />
    <hkern u1="&#xd3;" u2="v" k="1" />
    <hkern u1="&#xd3;" u2="X" k="15" />
    <hkern u1="&#xd3;" u2="V" k="1" />
    <hkern u1="&#xd3;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd3;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd4;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd4;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd4;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd4;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd4;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd4;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd4;" u2="x" k="15" />
    <hkern u1="&#xd4;" u2="v" k="1" />
    <hkern u1="&#xd4;" u2="X" k="15" />
    <hkern u1="&#xd4;" u2="V" k="1" />
    <hkern u1="&#xd4;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd4;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd5;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd5;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd5;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd5;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd5;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd5;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd5;" u2="x" k="15" />
    <hkern u1="&#xd5;" u2="v" k="1" />
    <hkern u1="&#xd5;" u2="X" k="15" />
    <hkern u1="&#xd5;" u2="V" k="1" />
    <hkern u1="&#xd5;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd5;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd6;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd6;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd6;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd6;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd6;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd6;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd6;" u2="x" k="15" />
    <hkern u1="&#xd6;" u2="v" k="1" />
    <hkern u1="&#xd6;" u2="X" k="15" />
    <hkern u1="&#xd6;" u2="V" k="1" />
    <hkern u1="&#xd6;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd6;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd8;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd8;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd8;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd8;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd8;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd8;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd8;" u2="x" k="15" />
    <hkern u1="&#xd8;" u2="v" k="1" />
    <hkern u1="&#xd8;" u2="X" k="15" />
    <hkern u1="&#xd8;" u2="V" k="1" />
    <hkern u1="&#xd8;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd8;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd9;" u2="&#x2026;" k="6" />
    <hkern u1="&#xd9;" u2="&#x201e;" k="6" />
    <hkern u1="&#xd9;" u2="&#x201a;" k="6" />
    <hkern u1="&#xd9;" u2="&#x2e;" k="6" />
    <hkern u1="&#xd9;" u2="&#x2c;" k="6" />
    <hkern u1="&#xda;" u2="&#x2026;" k="6" />
    <hkern u1="&#xda;" u2="&#x201e;" k="6" />
    <hkern u1="&#xda;" u2="&#x201a;" k="6" />
    <hkern u1="&#xda;" u2="&#x2e;" k="6" />
    <hkern u1="&#xda;" u2="&#x2c;" k="6" />
    <hkern u1="&#xdb;" u2="&#x2026;" k="6" />
    <hkern u1="&#xdb;" u2="&#x201e;" k="6" />
    <hkern u1="&#xdb;" u2="&#x201a;" k="6" />
    <hkern u1="&#xdb;" u2="&#x2e;" k="6" />
    <hkern u1="&#xdb;" u2="&#x2c;" k="6" />
    <hkern u1="&#xdc;" u2="&#x2026;" k="6" />
    <hkern u1="&#xdc;" u2="&#x201e;" k="6" />
    <hkern u1="&#xdc;" u2="&#x201a;" k="6" />
    <hkern u1="&#xdc;" u2="&#x2e;" k="6" />
    <hkern u1="&#xdc;" u2="&#x2c;" k="6" />
    <hkern u1="&#xdd;" u2="&#x2026;" k="80" />
    <hkern u1="&#xdd;" u2="&#x201e;" k="80" />
    <hkern u1="&#xdd;" u2="&#x201a;" k="80" />
    <hkern u1="&#xdd;" u2="&#x153;" k="15" />
    <hkern u1="&#xdd;" u2="&#x152;" k="15" />
    <hkern u1="&#xdd;" u2="&#x7d;" k="-5" />
    <hkern u1="&#xdd;" u2="q" k="15" />
    <hkern u1="&#xdd;" u2="]" k="-5" />
    <hkern u1="&#xdd;" u2="Q" k="15" />
    <hkern u1="&#xdd;" u2="&#x3b;" k="20" />
    <hkern u1="&#xdd;" u2="&#x3a;" k="20" />
    <hkern u1="&#xdd;" u2="&#x2e;" k="80" />
    <hkern u1="&#xdd;" u2="&#x2c;" k="80" />
    <hkern u1="&#xdd;" u2="&#x29;" k="-5" />
    <hkern u1="&#xde;" u2="&#x2026;" k="80" />
    <hkern u1="&#xde;" u2="&#x201e;" k="80" />
    <hkern u1="&#xde;" u2="&#x201a;" k="80" />
    <hkern u1="&#xde;" u2="&#x1ef3;" k="9" />
    <hkern u1="&#xde;" u2="&#x1ef2;" k="9" />
    <hkern u1="&#xde;" u2="&#x237;" k="8" />
    <hkern u1="&#xde;" u2="&#x1fd;" k="20" />
    <hkern u1="&#xde;" u2="&#x1fc;" k="20" />
    <hkern u1="&#xde;" u2="&#x17e;" k="10" />
    <hkern u1="&#xde;" u2="&#x17d;" k="10" />
    <hkern u1="&#xde;" u2="&#x17c;" k="10" />
    <hkern u1="&#xde;" u2="&#x17b;" k="10" />
    <hkern u1="&#xde;" u2="&#x17a;" k="10" />
    <hkern u1="&#xde;" u2="&#x179;" k="10" />
    <hkern u1="&#xde;" u2="&#x178;" k="9" />
    <hkern u1="&#xde;" u2="&#x177;" k="9" />
    <hkern u1="&#xde;" u2="&#x176;" k="9" />
    <hkern u1="&#xde;" u2="&#x135;" k="8" />
    <hkern u1="&#xde;" u2="&#x134;" k="8" />
    <hkern u1="&#xde;" u2="&#x133;" k="8" />
    <hkern u1="&#xde;" u2="&#x132;" k="8" />
    <hkern u1="&#xde;" u2="&#x105;" k="2" />
    <hkern u1="&#xde;" u2="&#x104;" k="2" />
    <hkern u1="&#xde;" u2="&#x103;" k="2" />
    <hkern u1="&#xde;" u2="&#x102;" k="2" />
    <hkern u1="&#xde;" u2="&#x101;" k="2" />
    <hkern u1="&#xde;" u2="&#x100;" k="2" />
    <hkern u1="&#xde;" u2="&#xff;" k="9" />
    <hkern u1="&#xde;" u2="&#xfd;" k="9" />
    <hkern u1="&#xde;" u2="&#xe6;" k="20" />
    <hkern u1="&#xde;" u2="&#xe5;" k="2" />
    <hkern u1="&#xde;" u2="&#xe4;" k="2" />
    <hkern u1="&#xde;" u2="&#xe3;" k="2" />
    <hkern u1="&#xde;" u2="&#xe2;" k="2" />
    <hkern u1="&#xde;" u2="&#xe1;" k="2" />
    <hkern u1="&#xde;" u2="&#xe0;" k="2" />
    <hkern u1="&#xde;" u2="&#xdd;" k="9" />
    <hkern u1="&#xde;" u2="&#xc6;" k="20" />
    <hkern u1="&#xde;" u2="&#xc5;" k="2" />
    <hkern u1="&#xde;" u2="&#xc4;" k="2" />
    <hkern u1="&#xde;" u2="&#xc3;" k="2" />
    <hkern u1="&#xde;" u2="&#xc2;" k="2" />
    <hkern u1="&#xde;" u2="&#xc1;" k="2" />
    <hkern u1="&#xde;" u2="&#xc0;" k="2" />
    <hkern u1="&#xde;" u2="z" k="10" />
    <hkern u1="&#xde;" u2="y" k="9" />
    <hkern u1="&#xde;" u2="x" k="20" />
    <hkern u1="&#xde;" u2="j" k="8" />
    <hkern u1="&#xde;" u2="a" k="2" />
    <hkern u1="&#xde;" u2="Z" k="10" />
    <hkern u1="&#xde;" u2="Y" k="9" />
    <hkern u1="&#xde;" u2="X" k="20" />
    <hkern u1="&#xde;" u2="J" k="8" />
    <hkern u1="&#xde;" u2="A" k="2" />
    <hkern u1="&#xde;" u2="&#x2e;" k="80" />
    <hkern u1="&#xde;" u2="&#x2c;" k="80" />
    <hkern u1="&#xdf;" u2="&#x2026;" k="5" />
    <hkern u1="&#xdf;" u2="&#x201e;" k="5" />
    <hkern u1="&#xdf;" u2="&#x201d;" k="5" />
    <hkern u1="&#xdf;" u2="&#x201c;" k="5" />
    <hkern u1="&#xdf;" u2="&#x201a;" k="5" />
    <hkern u1="&#xdf;" u2="&#x2018;" k="5" />
    <hkern u1="&#xdf;" u2="x" k="8" />
    <hkern u1="&#xdf;" u2="v" k="1" />
    <hkern u1="&#xdf;" u2="X" k="8" />
    <hkern u1="&#xdf;" u2="V" k="1" />
    <hkern u1="&#xdf;" u2="&#x2e;" k="5" />
    <hkern u1="&#xdf;" u2="&#x2c;" k="5" />
    <hkern u1="&#xe0;" u2="&#x201d;" k="60" />
    <hkern u1="&#xe0;" u2="&#x201c;" k="60" />
    <hkern u1="&#xe0;" u2="&#x2018;" k="60" />
    <hkern u1="&#xe0;" u2="&#x153;" k="1" />
    <hkern u1="&#xe0;" u2="&#x152;" k="1" />
    <hkern u1="&#xe0;" u2="v" k="25" />
    <hkern u1="&#xe0;" u2="q" k="1" />
    <hkern u1="&#xe0;" u2="V" k="25" />
    <hkern u1="&#xe0;" u2="Q" k="1" />
    <hkern u1="&#xe0;" u2="&#x3f;" k="30" />
    <hkern u1="&#xe1;" u2="&#x201d;" k="60" />
    <hkern u1="&#xe1;" u2="&#x201c;" k="60" />
    <hkern u1="&#xe1;" u2="&#x2018;" k="60" />
    <hkern u1="&#xe1;" u2="&#x153;" k="1" />
    <hkern u1="&#xe1;" u2="&#x152;" k="1" />
    <hkern u1="&#xe1;" u2="v" k="25" />
    <hkern u1="&#xe1;" u2="q" k="1" />
    <hkern u1="&#xe1;" u2="V" k="25" />
    <hkern u1="&#xe1;" u2="Q" k="1" />
    <hkern u1="&#xe1;" u2="&#x3f;" k="30" />
    <hkern u1="&#xe2;" u2="&#x201d;" k="60" />
    <hkern u1="&#xe2;" u2="&#x201c;" k="60" />
    <hkern u1="&#xe2;" u2="&#x2018;" k="60" />
    <hkern u1="&#xe2;" u2="&#x153;" k="1" />
    <hkern u1="&#xe2;" u2="&#x152;" k="1" />
    <hkern u1="&#xe2;" u2="v" k="25" />
    <hkern u1="&#xe2;" u2="q" k="1" />
    <hkern u1="&#xe2;" u2="V" k="25" />
    <hkern u1="&#xe2;" u2="Q" k="1" />
    <hkern u1="&#xe2;" u2="&#x3f;" k="30" />
    <hkern u1="&#xe3;" u2="&#x201d;" k="60" />
    <hkern u1="&#xe3;" u2="&#x201c;" k="60" />
    <hkern u1="&#xe3;" u2="&#x2018;" k="60" />
    <hkern u1="&#xe3;" u2="&#x153;" k="1" />
    <hkern u1="&#xe3;" u2="&#x152;" k="1" />
    <hkern u1="&#xe3;" u2="v" k="25" />
    <hkern u1="&#xe3;" u2="q" k="1" />
    <hkern u1="&#xe3;" u2="V" k="25" />
    <hkern u1="&#xe3;" u2="Q" k="1" />
    <hkern u1="&#xe3;" u2="&#x3f;" k="30" />
    <hkern u1="&#xe4;" u2="&#x201d;" k="60" />
    <hkern u1="&#xe4;" u2="&#x201c;" k="60" />
    <hkern u1="&#xe4;" u2="&#x2018;" k="60" />
    <hkern u1="&#xe4;" u2="&#x153;" k="1" />
    <hkern u1="&#xe4;" u2="&#x152;" k="1" />
    <hkern u1="&#xe4;" u2="v" k="25" />
    <hkern u1="&#xe4;" u2="q" k="1" />
    <hkern u1="&#xe4;" u2="V" k="25" />
    <hkern u1="&#xe4;" u2="Q" k="1" />
    <hkern u1="&#xe4;" u2="&#x3f;" k="30" />
    <hkern u1="&#xe5;" u2="&#x201d;" k="60" />
    <hkern u1="&#xe5;" u2="&#x201c;" k="60" />
    <hkern u1="&#xe5;" u2="&#x2018;" k="60" />
    <hkern u1="&#xe5;" u2="&#x153;" k="1" />
    <hkern u1="&#xe5;" u2="&#x152;" k="1" />
    <hkern u1="&#xe5;" u2="v" k="25" />
    <hkern u1="&#xe5;" u2="q" k="1" />
    <hkern u1="&#xe5;" u2="V" k="25" />
    <hkern u1="&#xe5;" u2="Q" k="1" />
    <hkern u1="&#xe5;" u2="&#x3f;" k="30" />
    <hkern u1="&#xe7;" u2="&#x2026;" k="6" />
    <hkern u1="&#xe7;" u2="&#x201e;" k="6" />
    <hkern u1="&#xe7;" u2="&#x201d;" k="6" />
    <hkern u1="&#xe7;" u2="&#x201c;" k="6" />
    <hkern u1="&#xe7;" u2="&#x201a;" k="6" />
    <hkern u1="&#xe7;" u2="&#x2018;" k="6" />
    <hkern u1="&#xe7;" u2="x" k="11" />
    <hkern u1="&#xe7;" u2="X" k="11" />
    <hkern u1="&#xe7;" u2="&#x2e;" k="6" />
    <hkern u1="&#xe7;" u2="&#x2c;" k="6" />
    <hkern u1="&#xf0;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf0;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf0;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf0;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf0;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf0;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf0;" u2="x" k="15" />
    <hkern u1="&#xf0;" u2="v" k="1" />
    <hkern u1="&#xf0;" u2="X" k="15" />
    <hkern u1="&#xf0;" u2="V" k="1" />
    <hkern u1="&#xf0;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf0;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf2;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf2;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf2;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf2;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf2;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf2;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf2;" u2="x" k="15" />
    <hkern u1="&#xf2;" u2="v" k="1" />
    <hkern u1="&#xf2;" u2="X" k="15" />
    <hkern u1="&#xf2;" u2="V" k="1" />
    <hkern u1="&#xf2;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf2;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf3;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf3;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf3;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf3;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf3;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf3;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf3;" u2="x" k="15" />
    <hkern u1="&#xf3;" u2="v" k="1" />
    <hkern u1="&#xf3;" u2="X" k="15" />
    <hkern u1="&#xf3;" u2="V" k="1" />
    <hkern u1="&#xf3;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf3;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf4;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf4;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf4;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf4;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf4;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf4;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf4;" u2="x" k="15" />
    <hkern u1="&#xf4;" u2="v" k="1" />
    <hkern u1="&#xf4;" u2="X" k="15" />
    <hkern u1="&#xf4;" u2="V" k="1" />
    <hkern u1="&#xf4;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf4;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf5;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf5;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf5;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf5;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf5;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf5;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf5;" u2="x" k="15" />
    <hkern u1="&#xf5;" u2="v" k="1" />
    <hkern u1="&#xf5;" u2="X" k="15" />
    <hkern u1="&#xf5;" u2="V" k="1" />
    <hkern u1="&#xf5;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf5;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf6;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf6;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf6;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf6;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf6;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf6;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf6;" u2="x" k="15" />
    <hkern u1="&#xf6;" u2="v" k="1" />
    <hkern u1="&#xf6;" u2="X" k="15" />
    <hkern u1="&#xf6;" u2="V" k="1" />
    <hkern u1="&#xf6;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf6;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf8;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf8;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf8;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf8;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf8;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf8;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf8;" u2="x" k="15" />
    <hkern u1="&#xf8;" u2="v" k="1" />
    <hkern u1="&#xf8;" u2="X" k="15" />
    <hkern u1="&#xf8;" u2="V" k="1" />
    <hkern u1="&#xf8;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf8;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf9;" u2="&#x2026;" k="6" />
    <hkern u1="&#xf9;" u2="&#x201e;" k="6" />
    <hkern u1="&#xf9;" u2="&#x201a;" k="6" />
    <hkern u1="&#xf9;" u2="&#x2e;" k="6" />
    <hkern u1="&#xf9;" u2="&#x2c;" k="6" />
    <hkern u1="&#xfa;" u2="&#x2026;" k="6" />
    <hkern u1="&#xfa;" u2="&#x201e;" k="6" />
    <hkern u1="&#xfa;" u2="&#x201a;" k="6" />
    <hkern u1="&#xfa;" u2="&#x2e;" k="6" />
    <hkern u1="&#xfa;" u2="&#x2c;" k="6" />
    <hkern u1="&#xfb;" u2="&#x2026;" k="6" />
    <hkern u1="&#xfb;" u2="&#x201e;" k="6" />
    <hkern u1="&#xfb;" u2="&#x201a;" k="6" />
    <hkern u1="&#xfb;" u2="&#x2e;" k="6" />
    <hkern u1="&#xfb;" u2="&#x2c;" k="6" />
    <hkern u1="&#xfc;" u2="&#x2026;" k="6" />
    <hkern u1="&#xfc;" u2="&#x201e;" k="6" />
    <hkern u1="&#xfc;" u2="&#x201a;" k="6" />
    <hkern u1="&#xfc;" u2="&#x2e;" k="6" />
    <hkern u1="&#xfc;" u2="&#x2c;" k="6" />
    <hkern u1="&#xfd;" u2="&#x2026;" k="80" />
    <hkern u1="&#xfd;" u2="&#x201e;" k="80" />
    <hkern u1="&#xfd;" u2="&#x201a;" k="80" />
    <hkern u1="&#xfd;" u2="&#x153;" k="15" />
    <hkern u1="&#xfd;" u2="&#x152;" k="15" />
    <hkern u1="&#xfd;" u2="&#x7d;" k="-5" />
    <hkern u1="&#xfd;" u2="q" k="15" />
    <hkern u1="&#xfd;" u2="]" k="-5" />
    <hkern u1="&#xfd;" u2="Q" k="15" />
    <hkern u1="&#xfd;" u2="&#x3b;" k="20" />
    <hkern u1="&#xfd;" u2="&#x3a;" k="20" />
    <hkern u1="&#xfd;" u2="&#x2e;" k="80" />
    <hkern u1="&#xfd;" u2="&#x2c;" k="80" />
    <hkern u1="&#xfd;" u2="&#x29;" k="-5" />
    <hkern u1="&#xfe;" u2="&#x2026;" k="80" />
    <hkern u1="&#xfe;" u2="&#x201e;" k="80" />
    <hkern u1="&#xfe;" u2="&#x201a;" k="80" />
    <hkern u1="&#xfe;" u2="&#x1ef3;" k="9" />
    <hkern u1="&#xfe;" u2="&#x1ef2;" k="9" />
    <hkern u1="&#xfe;" u2="&#x237;" k="8" />
    <hkern u1="&#xfe;" u2="&#x1fd;" k="20" />
    <hkern u1="&#xfe;" u2="&#x1fc;" k="20" />
    <hkern u1="&#xfe;" u2="&#x17e;" k="10" />
    <hkern u1="&#xfe;" u2="&#x17d;" k="10" />
    <hkern u1="&#xfe;" u2="&#x17c;" k="10" />
    <hkern u1="&#xfe;" u2="&#x17b;" k="10" />
    <hkern u1="&#xfe;" u2="&#x17a;" k="10" />
    <hkern u1="&#xfe;" u2="&#x179;" k="10" />
    <hkern u1="&#xfe;" u2="&#x178;" k="9" />
    <hkern u1="&#xfe;" u2="&#x177;" k="9" />
    <hkern u1="&#xfe;" u2="&#x176;" k="9" />
    <hkern u1="&#xfe;" u2="&#x135;" k="8" />
    <hkern u1="&#xfe;" u2="&#x134;" k="8" />
    <hkern u1="&#xfe;" u2="&#x133;" k="8" />
    <hkern u1="&#xfe;" u2="&#x132;" k="8" />
    <hkern u1="&#xfe;" u2="&#x105;" k="2" />
    <hkern u1="&#xfe;" u2="&#x104;" k="2" />
    <hkern u1="&#xfe;" u2="&#x103;" k="2" />
    <hkern u1="&#xfe;" u2="&#x102;" k="2" />
    <hkern u1="&#xfe;" u2="&#x101;" k="2" />
    <hkern u1="&#xfe;" u2="&#x100;" k="2" />
    <hkern u1="&#xfe;" u2="&#xff;" k="9" />
    <hkern u1="&#xfe;" u2="&#xfd;" k="9" />
    <hkern u1="&#xfe;" u2="&#xe6;" k="20" />
    <hkern u1="&#xfe;" u2="&#xe5;" k="2" />
    <hkern u1="&#xfe;" u2="&#xe4;" k="2" />
    <hkern u1="&#xfe;" u2="&#xe3;" k="2" />
    <hkern u1="&#xfe;" u2="&#xe2;" k="2" />
    <hkern u1="&#xfe;" u2="&#xe1;" k="2" />
    <hkern u1="&#xfe;" u2="&#xe0;" k="2" />
    <hkern u1="&#xfe;" u2="&#xdd;" k="9" />
    <hkern u1="&#xfe;" u2="&#xc6;" k="20" />
    <hkern u1="&#xfe;" u2="&#xc5;" k="2" />
    <hkern u1="&#xfe;" u2="&#xc4;" k="2" />
    <hkern u1="&#xfe;" u2="&#xc3;" k="2" />
    <hkern u1="&#xfe;" u2="&#xc2;" k="2" />
    <hkern u1="&#xfe;" u2="&#xc1;" k="2" />
    <hkern u1="&#xfe;" u2="&#xc0;" k="2" />
    <hkern u1="&#xfe;" u2="z" k="10" />
    <hkern u1="&#xfe;" u2="y" k="9" />
    <hkern u1="&#xfe;" u2="x" k="20" />
    <hkern u1="&#xfe;" u2="j" k="8" />
    <hkern u1="&#xfe;" u2="a" k="2" />
    <hkern u1="&#xfe;" u2="Z" k="10" />
    <hkern u1="&#xfe;" u2="Y" k="9" />
    <hkern u1="&#xfe;" u2="X" k="20" />
    <hkern u1="&#xfe;" u2="J" k="8" />
    <hkern u1="&#xfe;" u2="A" k="2" />
    <hkern u1="&#xfe;" u2="&#x2e;" k="80" />
    <hkern u1="&#xfe;" u2="&#x2c;" k="80" />
    <hkern u1="&#xff;" u2="&#x2026;" k="80" />
    <hkern u1="&#xff;" u2="&#x201e;" k="80" />
    <hkern u1="&#xff;" u2="&#x201a;" k="80" />
    <hkern u1="&#xff;" u2="&#x153;" k="15" />
    <hkern u1="&#xff;" u2="&#x152;" k="15" />
    <hkern u1="&#xff;" u2="&#x7d;" k="-5" />
    <hkern u1="&#xff;" u2="q" k="15" />
    <hkern u1="&#xff;" u2="]" k="-5" />
    <hkern u1="&#xff;" u2="Q" k="15" />
    <hkern u1="&#xff;" u2="&#x3b;" k="20" />
    <hkern u1="&#xff;" u2="&#x3a;" k="20" />
    <hkern u1="&#xff;" u2="&#x2e;" k="80" />
    <hkern u1="&#xff;" u2="&#x2c;" k="80" />
    <hkern u1="&#xff;" u2="&#x29;" k="-5" />
    <hkern u1="&#x100;" u2="&#x201d;" k="60" />
    <hkern u1="&#x100;" u2="&#x201c;" k="60" />
    <hkern u1="&#x100;" u2="&#x2018;" k="60" />
    <hkern u1="&#x100;" u2="&#x153;" k="1" />
    <hkern u1="&#x100;" u2="&#x152;" k="1" />
    <hkern u1="&#x100;" u2="v" k="25" />
    <hkern u1="&#x100;" u2="q" k="1" />
    <hkern u1="&#x100;" u2="V" k="25" />
    <hkern u1="&#x100;" u2="Q" k="1" />
    <hkern u1="&#x100;" u2="&#x3f;" k="30" />
    <hkern u1="&#x101;" u2="&#x201d;" k="60" />
    <hkern u1="&#x101;" u2="&#x201c;" k="60" />
    <hkern u1="&#x101;" u2="&#x2018;" k="60" />
    <hkern u1="&#x101;" u2="&#x153;" k="1" />
    <hkern u1="&#x101;" u2="&#x152;" k="1" />
    <hkern u1="&#x101;" u2="v" k="25" />
    <hkern u1="&#x101;" u2="q" k="1" />
    <hkern u1="&#x101;" u2="V" k="25" />
    <hkern u1="&#x101;" u2="Q" k="1" />
    <hkern u1="&#x101;" u2="&#x3f;" k="30" />
    <hkern u1="&#x102;" u2="&#x201d;" k="60" />
    <hkern u1="&#x102;" u2="&#x201c;" k="60" />
    <hkern u1="&#x102;" u2="&#x2018;" k="60" />
    <hkern u1="&#x102;" u2="&#x153;" k="1" />
    <hkern u1="&#x102;" u2="&#x152;" k="1" />
    <hkern u1="&#x102;" u2="v" k="25" />
    <hkern u1="&#x102;" u2="q" k="1" />
    <hkern u1="&#x102;" u2="V" k="25" />
    <hkern u1="&#x102;" u2="Q" k="1" />
    <hkern u1="&#x102;" u2="&#x3f;" k="30" />
    <hkern u1="&#x103;" u2="&#x201d;" k="60" />
    <hkern u1="&#x103;" u2="&#x201c;" k="60" />
    <hkern u1="&#x103;" u2="&#x2018;" k="60" />
    <hkern u1="&#x103;" u2="&#x153;" k="1" />
    <hkern u1="&#x103;" u2="&#x152;" k="1" />
    <hkern u1="&#x103;" u2="v" k="25" />
    <hkern u1="&#x103;" u2="q" k="1" />
    <hkern u1="&#x103;" u2="V" k="25" />
    <hkern u1="&#x103;" u2="Q" k="1" />
    <hkern u1="&#x103;" u2="&#x3f;" k="30" />
    <hkern u1="&#x104;" u2="&#x201d;" k="60" />
    <hkern u1="&#x104;" u2="&#x201c;" k="60" />
    <hkern u1="&#x104;" u2="&#x2018;" k="60" />
    <hkern u1="&#x104;" u2="&#x153;" k="1" />
    <hkern u1="&#x104;" u2="&#x152;" k="1" />
    <hkern u1="&#x104;" u2="v" k="25" />
    <hkern u1="&#x104;" u2="q" k="1" />
    <hkern u1="&#x104;" u2="V" k="25" />
    <hkern u1="&#x104;" u2="Q" k="1" />
    <hkern u1="&#x104;" u2="&#x3f;" k="30" />
    <hkern u1="&#x105;" u2="&#x201d;" k="60" />
    <hkern u1="&#x105;" u2="&#x201c;" k="60" />
    <hkern u1="&#x105;" u2="&#x2018;" k="60" />
    <hkern u1="&#x105;" u2="&#x153;" k="1" />
    <hkern u1="&#x105;" u2="&#x152;" k="1" />
    <hkern u1="&#x105;" u2="v" k="25" />
    <hkern u1="&#x105;" u2="q" k="1" />
    <hkern u1="&#x105;" u2="V" k="25" />
    <hkern u1="&#x105;" u2="Q" k="1" />
    <hkern u1="&#x105;" u2="&#x3f;" k="30" />
    <hkern u1="&#x106;" u2="&#x2026;" k="6" />
    <hkern u1="&#x106;" u2="&#x201e;" k="6" />
    <hkern u1="&#x106;" u2="&#x201d;" k="6" />
    <hkern u1="&#x106;" u2="&#x201c;" k="6" />
    <hkern u1="&#x106;" u2="&#x201a;" k="6" />
    <hkern u1="&#x106;" u2="&#x2018;" k="6" />
    <hkern u1="&#x106;" u2="x" k="11" />
    <hkern u1="&#x106;" u2="X" k="11" />
    <hkern u1="&#x106;" u2="&#x2e;" k="6" />
    <hkern u1="&#x106;" u2="&#x2c;" k="6" />
    <hkern u1="&#x107;" u2="&#x2026;" k="6" />
    <hkern u1="&#x107;" u2="&#x201e;" k="6" />
    <hkern u1="&#x107;" u2="&#x201d;" k="6" />
    <hkern u1="&#x107;" u2="&#x201c;" k="6" />
    <hkern u1="&#x107;" u2="&#x201a;" k="6" />
    <hkern u1="&#x107;" u2="&#x2018;" k="6" />
    <hkern u1="&#x107;" u2="x" k="11" />
    <hkern u1="&#x107;" u2="X" k="11" />
    <hkern u1="&#x107;" u2="&#x2e;" k="6" />
    <hkern u1="&#x107;" u2="&#x2c;" k="6" />
    <hkern u1="&#x108;" u2="&#x2026;" k="6" />
    <hkern u1="&#x108;" u2="&#x201e;" k="6" />
    <hkern u1="&#x108;" u2="&#x201d;" k="6" />
    <hkern u1="&#x108;" u2="&#x201c;" k="6" />
    <hkern u1="&#x108;" u2="&#x201a;" k="6" />
    <hkern u1="&#x108;" u2="&#x2018;" k="6" />
    <hkern u1="&#x108;" u2="x" k="11" />
    <hkern u1="&#x108;" u2="X" k="11" />
    <hkern u1="&#x108;" u2="&#x2e;" k="6" />
    <hkern u1="&#x108;" u2="&#x2c;" k="6" />
    <hkern u1="&#x109;" u2="&#x2026;" k="6" />
    <hkern u1="&#x109;" u2="&#x201e;" k="6" />
    <hkern u1="&#x109;" u2="&#x201d;" k="6" />
    <hkern u1="&#x109;" u2="&#x201c;" k="6" />
    <hkern u1="&#x109;" u2="&#x201a;" k="6" />
    <hkern u1="&#x109;" u2="&#x2018;" k="6" />
    <hkern u1="&#x109;" u2="x" k="11" />
    <hkern u1="&#x109;" u2="X" k="11" />
    <hkern u1="&#x109;" u2="&#x2e;" k="6" />
    <hkern u1="&#x109;" u2="&#x2c;" k="6" />
    <hkern u1="&#x10a;" u2="&#x2026;" k="6" />
    <hkern u1="&#x10a;" u2="&#x201e;" k="6" />
    <hkern u1="&#x10a;" u2="&#x201d;" k="6" />
    <hkern u1="&#x10a;" u2="&#x201c;" k="6" />
    <hkern u1="&#x10a;" u2="&#x201a;" k="6" />
    <hkern u1="&#x10a;" u2="&#x2018;" k="6" />
    <hkern u1="&#x10a;" u2="x" k="11" />
    <hkern u1="&#x10a;" u2="X" k="11" />
    <hkern u1="&#x10a;" u2="&#x2e;" k="6" />
    <hkern u1="&#x10a;" u2="&#x2c;" k="6" />
    <hkern u1="&#x10b;" u2="&#x2026;" k="6" />
    <hkern u1="&#x10b;" u2="&#x201e;" k="6" />
    <hkern u1="&#x10b;" u2="&#x201d;" k="6" />
    <hkern u1="&#x10b;" u2="&#x201c;" k="6" />
    <hkern u1="&#x10b;" u2="&#x201a;" k="6" />
    <hkern u1="&#x10b;" u2="&#x2018;" k="6" />
    <hkern u1="&#x10b;" u2="x" k="11" />
    <hkern u1="&#x10b;" u2="X" k="11" />
    <hkern u1="&#x10b;" u2="&#x2e;" k="6" />
    <hkern u1="&#x10b;" u2="&#x2c;" k="6" />
    <hkern u1="&#x10c;" u2="&#x2026;" k="6" />
    <hkern u1="&#x10c;" u2="&#x201e;" k="6" />
    <hkern u1="&#x10c;" u2="&#x201d;" k="6" />
    <hkern u1="&#x10c;" u2="&#x201c;" k="6" />
    <hkern u1="&#x10c;" u2="&#x201a;" k="6" />
    <hkern u1="&#x10c;" u2="&#x2018;" k="6" />
    <hkern u1="&#x10c;" u2="x" k="11" />
    <hkern u1="&#x10c;" u2="X" k="11" />
    <hkern u1="&#x10c;" u2="&#x2e;" k="6" />
    <hkern u1="&#x10c;" u2="&#x2c;" k="6" />
    <hkern u1="&#x10d;" u2="&#x2026;" k="6" />
    <hkern u1="&#x10d;" u2="&#x201e;" k="6" />
    <hkern u1="&#x10d;" u2="&#x201d;" k="6" />
    <hkern u1="&#x10d;" u2="&#x201c;" k="6" />
    <hkern u1="&#x10d;" u2="&#x201a;" k="6" />
    <hkern u1="&#x10d;" u2="&#x2018;" k="6" />
    <hkern u1="&#x10d;" u2="x" k="11" />
    <hkern u1="&#x10d;" u2="X" k="11" />
    <hkern u1="&#x10d;" u2="&#x2e;" k="6" />
    <hkern u1="&#x10d;" u2="&#x2c;" k="6" />
    <hkern u1="&#x10e;" u2="&#x2026;" k="10" />
    <hkern u1="&#x10e;" u2="&#x201e;" k="10" />
    <hkern u1="&#x10e;" u2="&#x201d;" k="10" />
    <hkern u1="&#x10e;" u2="&#x201c;" k="10" />
    <hkern u1="&#x10e;" u2="&#x201a;" k="10" />
    <hkern u1="&#x10e;" u2="&#x2018;" k="10" />
    <hkern u1="&#x10e;" u2="x" k="15" />
    <hkern u1="&#x10e;" u2="v" k="1" />
    <hkern u1="&#x10e;" u2="X" k="15" />
    <hkern u1="&#x10e;" u2="V" k="1" />
    <hkern u1="&#x10e;" u2="&#x2e;" k="10" />
    <hkern u1="&#x10e;" u2="&#x2c;" k="10" />
    <hkern u1="&#x10f;" u2="&#x2026;" k="10" />
    <hkern u1="&#x10f;" u2="&#x201e;" k="10" />
    <hkern u1="&#x10f;" u2="&#x201d;" k="10" />
    <hkern u1="&#x10f;" u2="&#x201c;" k="10" />
    <hkern u1="&#x10f;" u2="&#x201a;" k="10" />
    <hkern u1="&#x10f;" u2="&#x2018;" k="10" />
    <hkern u1="&#x10f;" u2="x" k="15" />
    <hkern u1="&#x10f;" u2="v" k="1" />
    <hkern u1="&#x10f;" u2="X" k="15" />
    <hkern u1="&#x10f;" u2="V" k="1" />
    <hkern u1="&#x10f;" u2="&#x2e;" k="10" />
    <hkern u1="&#x10f;" u2="&#x2c;" k="10" />
    <hkern u1="&#x110;" u2="&#x2026;" k="10" />
    <hkern u1="&#x110;" u2="&#x201e;" k="10" />
    <hkern u1="&#x110;" u2="&#x201d;" k="10" />
    <hkern u1="&#x110;" u2="&#x201c;" k="10" />
    <hkern u1="&#x110;" u2="&#x201a;" k="10" />
    <hkern u1="&#x110;" u2="&#x2018;" k="10" />
    <hkern u1="&#x110;" u2="x" k="15" />
    <hkern u1="&#x110;" u2="v" k="1" />
    <hkern u1="&#x110;" u2="X" k="15" />
    <hkern u1="&#x110;" u2="V" k="1" />
    <hkern u1="&#x110;" u2="&#x2e;" k="10" />
    <hkern u1="&#x110;" u2="&#x2c;" k="10" />
    <hkern u1="&#x111;" u2="&#x2026;" k="10" />
    <hkern u1="&#x111;" u2="&#x201e;" k="10" />
    <hkern u1="&#x111;" u2="&#x201d;" k="10" />
    <hkern u1="&#x111;" u2="&#x201c;" k="10" />
    <hkern u1="&#x111;" u2="&#x201a;" k="10" />
    <hkern u1="&#x111;" u2="&#x2018;" k="10" />
    <hkern u1="&#x111;" u2="x" k="15" />
    <hkern u1="&#x111;" u2="v" k="1" />
    <hkern u1="&#x111;" u2="X" k="15" />
    <hkern u1="&#x111;" u2="V" k="1" />
    <hkern u1="&#x111;" u2="&#x2e;" k="10" />
    <hkern u1="&#x111;" u2="&#x2c;" k="10" />
    <hkern u1="&#x11c;" u2="&#x2026;" k="10" />
    <hkern u1="&#x11c;" u2="&#x201e;" k="10" />
    <hkern u1="&#x11c;" u2="&#x201d;" k="10" />
    <hkern u1="&#x11c;" u2="&#x201c;" k="10" />
    <hkern u1="&#x11c;" u2="&#x201a;" k="10" />
    <hkern u1="&#x11c;" u2="&#x2018;" k="10" />
    <hkern u1="&#x11c;" u2="x" k="15" />
    <hkern u1="&#x11c;" u2="v" k="1" />
    <hkern u1="&#x11c;" u2="X" k="15" />
    <hkern u1="&#x11c;" u2="V" k="1" />
    <hkern u1="&#x11c;" u2="&#x2e;" k="10" />
    <hkern u1="&#x11c;" u2="&#x2c;" k="10" />
    <hkern u1="&#x11d;" u2="&#x2026;" k="10" />
    <hkern u1="&#x11d;" u2="&#x201e;" k="10" />
    <hkern u1="&#x11d;" u2="&#x201d;" k="10" />
    <hkern u1="&#x11d;" u2="&#x201c;" k="10" />
    <hkern u1="&#x11d;" u2="&#x201a;" k="10" />
    <hkern u1="&#x11d;" u2="&#x2018;" k="10" />
    <hkern u1="&#x11d;" u2="x" k="15" />
    <hkern u1="&#x11d;" u2="v" k="1" />
    <hkern u1="&#x11d;" u2="X" k="15" />
    <hkern u1="&#x11d;" u2="V" k="1" />
    <hkern u1="&#x11d;" u2="&#x2e;" k="10" />
    <hkern u1="&#x11d;" u2="&#x2c;" k="10" />
    <hkern u1="&#x11e;" u2="&#x2026;" k="10" />
    <hkern u1="&#x11e;" u2="&#x201e;" k="10" />
    <hkern u1="&#x11e;" u2="&#x201d;" k="10" />
    <hkern u1="&#x11e;" u2="&#x201c;" k="10" />
    <hkern u1="&#x11e;" u2="&#x201a;" k="10" />
    <hkern u1="&#x11e;" u2="&#x2018;" k="10" />
    <hkern u1="&#x11e;" u2="x" k="15" />
    <hkern u1="&#x11e;" u2="v" k="1" />
    <hkern u1="&#x11e;" u2="X" k="15" />
    <hkern u1="&#x11e;" u2="V" k="1" />
    <hkern u1="&#x11e;" u2="&#x2e;" k="10" />
    <hkern u1="&#x11e;" u2="&#x2c;" k="10" />
    <hkern u1="&#x11f;" u2="&#x2026;" k="10" />
    <hkern u1="&#x11f;" u2="&#x201e;" k="10" />
    <hkern u1="&#x11f;" u2="&#x201d;" k="10" />
    <hkern u1="&#x11f;" u2="&#x201c;" k="10" />
    <hkern u1="&#x11f;" u2="&#x201a;" k="10" />
    <hkern u1="&#x11f;" u2="&#x2018;" k="10" />
    <hkern u1="&#x11f;" u2="x" k="15" />
    <hkern u1="&#x11f;" u2="v" k="1" />
    <hkern u1="&#x11f;" u2="X" k="15" />
    <hkern u1="&#x11f;" u2="V" k="1" />
    <hkern u1="&#x11f;" u2="&#x2e;" k="10" />
    <hkern u1="&#x11f;" u2="&#x2c;" k="10" />
    <hkern u1="&#x120;" u2="&#x2026;" k="10" />
    <hkern u1="&#x120;" u2="&#x201e;" k="10" />
    <hkern u1="&#x120;" u2="&#x201d;" k="10" />
    <hkern u1="&#x120;" u2="&#x201c;" k="10" />
    <hkern u1="&#x120;" u2="&#x201a;" k="10" />
    <hkern u1="&#x120;" u2="&#x2018;" k="10" />
    <hkern u1="&#x120;" u2="x" k="15" />
    <hkern u1="&#x120;" u2="v" k="1" />
    <hkern u1="&#x120;" u2="X" k="15" />
    <hkern u1="&#x120;" u2="V" k="1" />
    <hkern u1="&#x120;" u2="&#x2e;" k="10" />
    <hkern u1="&#x120;" u2="&#x2c;" k="10" />
    <hkern u1="&#x121;" u2="&#x2026;" k="10" />
    <hkern u1="&#x121;" u2="&#x201e;" k="10" />
    <hkern u1="&#x121;" u2="&#x201d;" k="10" />
    <hkern u1="&#x121;" u2="&#x201c;" k="10" />
    <hkern u1="&#x121;" u2="&#x201a;" k="10" />
    <hkern u1="&#x121;" u2="&#x2018;" k="10" />
    <hkern u1="&#x121;" u2="x" k="15" />
    <hkern u1="&#x121;" u2="v" k="1" />
    <hkern u1="&#x121;" u2="X" k="15" />
    <hkern u1="&#x121;" u2="V" k="1" />
    <hkern u1="&#x121;" u2="&#x2e;" k="10" />
    <hkern u1="&#x121;" u2="&#x2c;" k="10" />
    <hkern u1="&#x122;" u2="&#x2026;" k="10" />
    <hkern u1="&#x122;" u2="&#x201e;" k="10" />
    <hkern u1="&#x122;" u2="&#x201d;" k="10" />
    <hkern u1="&#x122;" u2="&#x201c;" k="10" />
    <hkern u1="&#x122;" u2="&#x201a;" k="10" />
    <hkern u1="&#x122;" u2="&#x2018;" k="10" />
    <hkern u1="&#x122;" u2="x" k="15" />
    <hkern u1="&#x122;" u2="v" k="1" />
    <hkern u1="&#x122;" u2="X" k="15" />
    <hkern u1="&#x122;" u2="V" k="1" />
    <hkern u1="&#x122;" u2="&#x2e;" k="10" />
    <hkern u1="&#x122;" u2="&#x2c;" k="10" />
    <hkern u1="&#x123;" u2="&#x2026;" k="10" />
    <hkern u1="&#x123;" u2="&#x201e;" k="10" />
    <hkern u1="&#x123;" u2="&#x201d;" k="10" />
    <hkern u1="&#x123;" u2="&#x201c;" k="10" />
    <hkern u1="&#x123;" u2="&#x201a;" k="10" />
    <hkern u1="&#x123;" u2="&#x2018;" k="10" />
    <hkern u1="&#x123;" u2="x" k="15" />
    <hkern u1="&#x123;" u2="v" k="1" />
    <hkern u1="&#x123;" u2="X" k="15" />
    <hkern u1="&#x123;" u2="V" k="1" />
    <hkern u1="&#x123;" u2="&#x2e;" k="10" />
    <hkern u1="&#x123;" u2="&#x2c;" k="10" />
    <hkern u1="&#x134;" u2="&#x2026;" k="6" />
    <hkern u1="&#x134;" u2="&#x201e;" k="6" />
    <hkern u1="&#x134;" u2="&#x201a;" k="6" />
    <hkern u1="&#x134;" u2="&#x2e;" k="6" />
    <hkern u1="&#x134;" u2="&#x2c;" k="6" />
    <hkern u1="&#x135;" u2="&#x2026;" k="6" />
    <hkern u1="&#x135;" u2="&#x201e;" k="6" />
    <hkern u1="&#x135;" u2="&#x201a;" k="6" />
    <hkern u1="&#x135;" u2="&#x2e;" k="6" />
    <hkern u1="&#x135;" u2="&#x2c;" k="6" />
    <hkern u1="&#x136;" u2="&#x153;" k="7" />
    <hkern u1="&#x136;" u2="&#x152;" k="7" />
    <hkern u1="&#x136;" u2="q" k="7" />
    <hkern u1="&#x136;" u2="Q" k="7" />
    <hkern u1="&#x137;" u2="&#x153;" k="7" />
    <hkern u1="&#x137;" u2="&#x152;" k="7" />
    <hkern u1="&#x137;" u2="q" k="7" />
    <hkern u1="&#x137;" u2="Q" k="7" />
    <hkern u1="&#x138;" u2="&#x153;" k="7" />
    <hkern u1="&#x138;" u2="&#x152;" k="7" />
    <hkern u1="&#x138;" u2="q" k="7" />
    <hkern u1="&#x138;" u2="Q" k="7" />
    <hkern u1="&#x139;" u2="&#x201d;" k="75" />
    <hkern u1="&#x139;" u2="&#x201c;" k="75" />
    <hkern u1="&#x139;" u2="&#x2018;" k="75" />
    <hkern u1="&#x139;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x139;" u2="v" k="33" />
    <hkern u1="&#x139;" u2="]" k="-8" />
    <hkern u1="&#x139;" u2="V" k="33" />
    <hkern u1="&#x139;" u2="&#x3f;" k="30" />
    <hkern u1="&#x139;" u2="&#x29;" k="-8" />
    <hkern u1="&#x13a;" u2="&#x201d;" k="75" />
    <hkern u1="&#x13a;" u2="&#x201c;" k="75" />
    <hkern u1="&#x13a;" u2="&#x2018;" k="75" />
    <hkern u1="&#x13a;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x13a;" u2="v" k="33" />
    <hkern u1="&#x13a;" u2="]" k="-8" />
    <hkern u1="&#x13a;" u2="V" k="33" />
    <hkern u1="&#x13a;" u2="&#x3f;" k="30" />
    <hkern u1="&#x13a;" u2="&#x29;" k="-8" />
    <hkern u1="&#x13b;" u2="&#x201d;" k="75" />
    <hkern u1="&#x13b;" u2="&#x201c;" k="75" />
    <hkern u1="&#x13b;" u2="&#x2018;" k="75" />
    <hkern u1="&#x13b;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x13b;" u2="v" k="33" />
    <hkern u1="&#x13b;" u2="]" k="-8" />
    <hkern u1="&#x13b;" u2="V" k="33" />
    <hkern u1="&#x13b;" u2="&#x3f;" k="30" />
    <hkern u1="&#x13b;" u2="&#x29;" k="-8" />
    <hkern u1="&#x13c;" u2="&#x201d;" k="75" />
    <hkern u1="&#x13c;" u2="&#x201c;" k="75" />
    <hkern u1="&#x13c;" u2="&#x2018;" k="75" />
    <hkern u1="&#x13c;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x13c;" u2="v" k="33" />
    <hkern u1="&#x13c;" u2="]" k="-8" />
    <hkern u1="&#x13c;" u2="V" k="33" />
    <hkern u1="&#x13c;" u2="&#x3f;" k="30" />
    <hkern u1="&#x13c;" u2="&#x29;" k="-8" />
    <hkern u1="&#x13f;" u2="&#x201d;" k="75" />
    <hkern u1="&#x13f;" u2="&#x201c;" k="75" />
    <hkern u1="&#x13f;" u2="&#x2018;" k="75" />
    <hkern u1="&#x13f;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x13f;" u2="v" k="33" />
    <hkern u1="&#x13f;" u2="]" k="-8" />
    <hkern u1="&#x13f;" u2="V" k="33" />
    <hkern u1="&#x13f;" u2="&#x3f;" k="30" />
    <hkern u1="&#x13f;" u2="&#x29;" k="-8" />
    <hkern u1="&#x140;" u2="&#x201d;" k="75" />
    <hkern u1="&#x140;" u2="&#x201c;" k="75" />
    <hkern u1="&#x140;" u2="&#x2018;" k="75" />
    <hkern u1="&#x140;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x140;" u2="v" k="33" />
    <hkern u1="&#x140;" u2="]" k="-8" />
    <hkern u1="&#x140;" u2="V" k="33" />
    <hkern u1="&#x140;" u2="&#x3f;" k="30" />
    <hkern u1="&#x140;" u2="&#x29;" k="-8" />
    <hkern u1="&#x141;" u2="&#x201d;" k="75" />
    <hkern u1="&#x141;" u2="&#x201c;" k="75" />
    <hkern u1="&#x141;" u2="&#x2018;" k="75" />
    <hkern u1="&#x141;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x141;" u2="v" k="33" />
    <hkern u1="&#x141;" u2="]" k="-8" />
    <hkern u1="&#x141;" u2="V" k="33" />
    <hkern u1="&#x141;" u2="&#x3f;" k="30" />
    <hkern u1="&#x141;" u2="&#x29;" k="-8" />
    <hkern u1="&#x142;" u2="&#x201d;" k="75" />
    <hkern u1="&#x142;" u2="&#x201c;" k="75" />
    <hkern u1="&#x142;" u2="&#x2018;" k="75" />
    <hkern u1="&#x142;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x142;" u2="v" k="33" />
    <hkern u1="&#x142;" u2="]" k="-8" />
    <hkern u1="&#x142;" u2="V" k="33" />
    <hkern u1="&#x142;" u2="&#x3f;" k="30" />
    <hkern u1="&#x142;" u2="&#x29;" k="-8" />
    <hkern u1="&#x14c;" u2="&#x2026;" k="10" />
    <hkern u1="&#x14c;" u2="&#x201e;" k="10" />
    <hkern u1="&#x14c;" u2="&#x201d;" k="10" />
    <hkern u1="&#x14c;" u2="&#x201c;" k="10" />
    <hkern u1="&#x14c;" u2="&#x201a;" k="10" />
    <hkern u1="&#x14c;" u2="&#x2018;" k="10" />
    <hkern u1="&#x14c;" u2="x" k="15" />
    <hkern u1="&#x14c;" u2="v" k="1" />
    <hkern u1="&#x14c;" u2="X" k="15" />
    <hkern u1="&#x14c;" u2="V" k="1" />
    <hkern u1="&#x14c;" u2="&#x2e;" k="10" />
    <hkern u1="&#x14c;" u2="&#x2c;" k="10" />
    <hkern u1="&#x14d;" u2="&#x2026;" k="10" />
    <hkern u1="&#x14d;" u2="&#x201e;" k="10" />
    <hkern u1="&#x14d;" u2="&#x201d;" k="10" />
    <hkern u1="&#x14d;" u2="&#x201c;" k="10" />
    <hkern u1="&#x14d;" u2="&#x201a;" k="10" />
    <hkern u1="&#x14d;" u2="&#x2018;" k="10" />
    <hkern u1="&#x14d;" u2="x" k="15" />
    <hkern u1="&#x14d;" u2="v" k="1" />
    <hkern u1="&#x14d;" u2="X" k="15" />
    <hkern u1="&#x14d;" u2="V" k="1" />
    <hkern u1="&#x14d;" u2="&#x2e;" k="10" />
    <hkern u1="&#x14d;" u2="&#x2c;" k="10" />
    <hkern u1="&#x14e;" u2="&#x2026;" k="10" />
    <hkern u1="&#x14e;" u2="&#x201e;" k="10" />
    <hkern u1="&#x14e;" u2="&#x201d;" k="10" />
    <hkern u1="&#x14e;" u2="&#x201c;" k="10" />
    <hkern u1="&#x14e;" u2="&#x201a;" k="10" />
    <hkern u1="&#x14e;" u2="&#x2018;" k="10" />
    <hkern u1="&#x14e;" u2="x" k="15" />
    <hkern u1="&#x14e;" u2="v" k="1" />
    <hkern u1="&#x14e;" u2="X" k="15" />
    <hkern u1="&#x14e;" u2="V" k="1" />
    <hkern u1="&#x14e;" u2="&#x2e;" k="10" />
    <hkern u1="&#x14e;" u2="&#x2c;" k="10" />
    <hkern u1="&#x14f;" u2="&#x2026;" k="10" />
    <hkern u1="&#x14f;" u2="&#x201e;" k="10" />
    <hkern u1="&#x14f;" u2="&#x201d;" k="10" />
    <hkern u1="&#x14f;" u2="&#x201c;" k="10" />
    <hkern u1="&#x14f;" u2="&#x201a;" k="10" />
    <hkern u1="&#x14f;" u2="&#x2018;" k="10" />
    <hkern u1="&#x14f;" u2="x" k="15" />
    <hkern u1="&#x14f;" u2="v" k="1" />
    <hkern u1="&#x14f;" u2="X" k="15" />
    <hkern u1="&#x14f;" u2="V" k="1" />
    <hkern u1="&#x14f;" u2="&#x2e;" k="10" />
    <hkern u1="&#x14f;" u2="&#x2c;" k="10" />
    <hkern u1="&#x150;" u2="&#x2026;" k="10" />
    <hkern u1="&#x150;" u2="&#x201e;" k="10" />
    <hkern u1="&#x150;" u2="&#x201d;" k="10" />
    <hkern u1="&#x150;" u2="&#x201c;" k="10" />
    <hkern u1="&#x150;" u2="&#x201a;" k="10" />
    <hkern u1="&#x150;" u2="&#x2018;" k="10" />
    <hkern u1="&#x150;" u2="x" k="15" />
    <hkern u1="&#x150;" u2="v" k="1" />
    <hkern u1="&#x150;" u2="X" k="15" />
    <hkern u1="&#x150;" u2="V" k="1" />
    <hkern u1="&#x150;" u2="&#x2e;" k="10" />
    <hkern u1="&#x150;" u2="&#x2c;" k="10" />
    <hkern u1="&#x151;" u2="&#x2026;" k="10" />
    <hkern u1="&#x151;" u2="&#x201e;" k="10" />
    <hkern u1="&#x151;" u2="&#x201d;" k="10" />
    <hkern u1="&#x151;" u2="&#x201c;" k="10" />
    <hkern u1="&#x151;" u2="&#x201a;" k="10" />
    <hkern u1="&#x151;" u2="&#x2018;" k="10" />
    <hkern u1="&#x151;" u2="x" k="15" />
    <hkern u1="&#x151;" u2="v" k="1" />
    <hkern u1="&#x151;" u2="X" k="15" />
    <hkern u1="&#x151;" u2="V" k="1" />
    <hkern u1="&#x151;" u2="&#x2e;" k="10" />
    <hkern u1="&#x151;" u2="&#x2c;" k="10" />
    <hkern u1="&#x154;" u2="v" k="2" />
    <hkern u1="&#x154;" u2="V" k="2" />
    <hkern u1="&#x155;" u2="v" k="2" />
    <hkern u1="&#x155;" u2="V" k="2" />
    <hkern u1="&#x156;" u2="v" k="2" />
    <hkern u1="&#x156;" u2="V" k="2" />
    <hkern u1="&#x157;" u2="v" k="2" />
    <hkern u1="&#x157;" u2="V" k="2" />
    <hkern u1="&#x158;" u2="v" k="2" />
    <hkern u1="&#x158;" u2="V" k="2" />
    <hkern u1="&#x159;" u2="v" k="2" />
    <hkern u1="&#x159;" u2="V" k="2" />
    <hkern u1="&#x15a;" u2="&#x2026;" k="5" />
    <hkern u1="&#x15a;" u2="&#x201e;" k="5" />
    <hkern u1="&#x15a;" u2="&#x201d;" k="5" />
    <hkern u1="&#x15a;" u2="&#x201c;" k="5" />
    <hkern u1="&#x15a;" u2="&#x201a;" k="5" />
    <hkern u1="&#x15a;" u2="&#x2018;" k="5" />
    <hkern u1="&#x15a;" u2="x" k="8" />
    <hkern u1="&#x15a;" u2="v" k="1" />
    <hkern u1="&#x15a;" u2="X" k="8" />
    <hkern u1="&#x15a;" u2="V" k="1" />
    <hkern u1="&#x15a;" u2="&#x2e;" k="5" />
    <hkern u1="&#x15a;" u2="&#x2c;" k="5" />
    <hkern u1="&#x15b;" u2="&#x2026;" k="5" />
    <hkern u1="&#x15b;" u2="&#x201e;" k="5" />
    <hkern u1="&#x15b;" u2="&#x201d;" k="5" />
    <hkern u1="&#x15b;" u2="&#x201c;" k="5" />
    <hkern u1="&#x15b;" u2="&#x201a;" k="5" />
    <hkern u1="&#x15b;" u2="&#x2018;" k="5" />
    <hkern u1="&#x15b;" u2="x" k="8" />
    <hkern u1="&#x15b;" u2="v" k="1" />
    <hkern u1="&#x15b;" u2="X" k="8" />
    <hkern u1="&#x15b;" u2="V" k="1" />
    <hkern u1="&#x15b;" u2="&#x2e;" k="5" />
    <hkern u1="&#x15b;" u2="&#x2c;" k="5" />
    <hkern u1="&#x15c;" u2="&#x2026;" k="5" />
    <hkern u1="&#x15c;" u2="&#x201e;" k="5" />
    <hkern u1="&#x15c;" u2="&#x201d;" k="5" />
    <hkern u1="&#x15c;" u2="&#x201c;" k="5" />
    <hkern u1="&#x15c;" u2="&#x201a;" k="5" />
    <hkern u1="&#x15c;" u2="&#x2018;" k="5" />
    <hkern u1="&#x15c;" u2="x" k="8" />
    <hkern u1="&#x15c;" u2="v" k="1" />
    <hkern u1="&#x15c;" u2="X" k="8" />
    <hkern u1="&#x15c;" u2="V" k="1" />
    <hkern u1="&#x15c;" u2="&#x2e;" k="5" />
    <hkern u1="&#x15c;" u2="&#x2c;" k="5" />
    <hkern u1="&#x15d;" u2="&#x2026;" k="5" />
    <hkern u1="&#x15d;" u2="&#x201e;" k="5" />
    <hkern u1="&#x15d;" u2="&#x201d;" k="5" />
    <hkern u1="&#x15d;" u2="&#x201c;" k="5" />
    <hkern u1="&#x15d;" u2="&#x201a;" k="5" />
    <hkern u1="&#x15d;" u2="&#x2018;" k="5" />
    <hkern u1="&#x15d;" u2="x" k="8" />
    <hkern u1="&#x15d;" u2="v" k="1" />
    <hkern u1="&#x15d;" u2="X" k="8" />
    <hkern u1="&#x15d;" u2="V" k="1" />
    <hkern u1="&#x15d;" u2="&#x2e;" k="5" />
    <hkern u1="&#x15d;" u2="&#x2c;" k="5" />
    <hkern u1="&#x15e;" u2="&#x2026;" k="5" />
    <hkern u1="&#x15e;" u2="&#x201e;" k="5" />
    <hkern u1="&#x15e;" u2="&#x201d;" k="5" />
    <hkern u1="&#x15e;" u2="&#x201c;" k="5" />
    <hkern u1="&#x15e;" u2="&#x201a;" k="5" />
    <hkern u1="&#x15e;" u2="&#x2018;" k="5" />
    <hkern u1="&#x15e;" u2="x" k="8" />
    <hkern u1="&#x15e;" u2="v" k="1" />
    <hkern u1="&#x15e;" u2="X" k="8" />
    <hkern u1="&#x15e;" u2="V" k="1" />
    <hkern u1="&#x15e;" u2="&#x2e;" k="5" />
    <hkern u1="&#x15e;" u2="&#x2c;" k="5" />
    <hkern u1="&#x15f;" u2="&#x2026;" k="5" />
    <hkern u1="&#x15f;" u2="&#x201e;" k="5" />
    <hkern u1="&#x15f;" u2="&#x201d;" k="5" />
    <hkern u1="&#x15f;" u2="&#x201c;" k="5" />
    <hkern u1="&#x15f;" u2="&#x201a;" k="5" />
    <hkern u1="&#x15f;" u2="&#x2018;" k="5" />
    <hkern u1="&#x15f;" u2="x" k="8" />
    <hkern u1="&#x15f;" u2="v" k="1" />
    <hkern u1="&#x15f;" u2="X" k="8" />
    <hkern u1="&#x15f;" u2="V" k="1" />
    <hkern u1="&#x15f;" u2="&#x2e;" k="5" />
    <hkern u1="&#x15f;" u2="&#x2c;" k="5" />
    <hkern u1="&#x160;" u2="&#x2026;" k="5" />
    <hkern u1="&#x160;" u2="&#x201e;" k="5" />
    <hkern u1="&#x160;" u2="&#x201d;" k="5" />
    <hkern u1="&#x160;" u2="&#x201c;" k="5" />
    <hkern u1="&#x160;" u2="&#x201a;" k="5" />
    <hkern u1="&#x160;" u2="&#x2018;" k="5" />
    <hkern u1="&#x160;" u2="x" k="8" />
    <hkern u1="&#x160;" u2="v" k="1" />
    <hkern u1="&#x160;" u2="X" k="8" />
    <hkern u1="&#x160;" u2="V" k="1" />
    <hkern u1="&#x160;" u2="&#x2e;" k="5" />
    <hkern u1="&#x160;" u2="&#x2c;" k="5" />
    <hkern u1="&#x161;" u2="&#x2026;" k="5" />
    <hkern u1="&#x161;" u2="&#x201e;" k="5" />
    <hkern u1="&#x161;" u2="&#x201d;" k="5" />
    <hkern u1="&#x161;" u2="&#x201c;" k="5" />
    <hkern u1="&#x161;" u2="&#x201a;" k="5" />
    <hkern u1="&#x161;" u2="&#x2018;" k="5" />
    <hkern u1="&#x161;" u2="x" k="8" />
    <hkern u1="&#x161;" u2="v" k="1" />
    <hkern u1="&#x161;" u2="X" k="8" />
    <hkern u1="&#x161;" u2="V" k="1" />
    <hkern u1="&#x161;" u2="&#x2e;" k="5" />
    <hkern u1="&#x161;" u2="&#x2c;" k="5" />
    <hkern u1="&#x162;" u2="&#x2026;" k="60" />
    <hkern u1="&#x162;" u2="&#x201e;" k="60" />
    <hkern u1="&#x162;" u2="&#x201a;" k="60" />
    <hkern u1="&#x162;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x162;" u2="]" k="-8" />
    <hkern u1="&#x162;" u2="&#x3b;" k="35" />
    <hkern u1="&#x162;" u2="&#x3a;" k="35" />
    <hkern u1="&#x162;" u2="&#x2e;" k="60" />
    <hkern u1="&#x162;" u2="&#x2c;" k="60" />
    <hkern u1="&#x162;" u2="&#x29;" k="-8" />
    <hkern u1="&#x163;" u2="&#x2026;" k="60" />
    <hkern u1="&#x163;" u2="&#x201e;" k="60" />
    <hkern u1="&#x163;" u2="&#x201a;" k="60" />
    <hkern u1="&#x163;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x163;" u2="]" k="-8" />
    <hkern u1="&#x163;" u2="&#x3b;" k="35" />
    <hkern u1="&#x163;" u2="&#x3a;" k="35" />
    <hkern u1="&#x163;" u2="&#x2e;" k="60" />
    <hkern u1="&#x163;" u2="&#x2c;" k="60" />
    <hkern u1="&#x163;" u2="&#x29;" k="-8" />
    <hkern u1="&#x164;" u2="&#x2026;" k="60" />
    <hkern u1="&#x164;" u2="&#x201e;" k="60" />
    <hkern u1="&#x164;" u2="&#x201a;" k="60" />
    <hkern u1="&#x164;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x164;" u2="]" k="-8" />
    <hkern u1="&#x164;" u2="&#x3b;" k="35" />
    <hkern u1="&#x164;" u2="&#x3a;" k="35" />
    <hkern u1="&#x164;" u2="&#x2e;" k="60" />
    <hkern u1="&#x164;" u2="&#x2c;" k="60" />
    <hkern u1="&#x164;" u2="&#x29;" k="-8" />
    <hkern u1="&#x165;" u2="&#x2026;" k="60" />
    <hkern u1="&#x165;" u2="&#x201e;" k="60" />
    <hkern u1="&#x165;" u2="&#x201a;" k="60" />
    <hkern u1="&#x165;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x165;" u2="]" k="-8" />
    <hkern u1="&#x165;" u2="&#x3b;" k="35" />
    <hkern u1="&#x165;" u2="&#x3a;" k="35" />
    <hkern u1="&#x165;" u2="&#x2e;" k="60" />
    <hkern u1="&#x165;" u2="&#x2c;" k="60" />
    <hkern u1="&#x165;" u2="&#x29;" k="-8" />
    <hkern u1="&#x166;" u2="&#x2026;" k="60" />
    <hkern u1="&#x166;" u2="&#x201e;" k="60" />
    <hkern u1="&#x166;" u2="&#x201a;" k="60" />
    <hkern u1="&#x166;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x166;" u2="]" k="-8" />
    <hkern u1="&#x166;" u2="&#x3b;" k="35" />
    <hkern u1="&#x166;" u2="&#x3a;" k="35" />
    <hkern u1="&#x166;" u2="&#x2e;" k="60" />
    <hkern u1="&#x166;" u2="&#x2c;" k="60" />
    <hkern u1="&#x166;" u2="&#x29;" k="-8" />
    <hkern u1="&#x167;" u2="&#x2026;" k="60" />
    <hkern u1="&#x167;" u2="&#x201e;" k="60" />
    <hkern u1="&#x167;" u2="&#x201a;" k="60" />
    <hkern u1="&#x167;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x167;" u2="]" k="-8" />
    <hkern u1="&#x167;" u2="&#x3b;" k="35" />
    <hkern u1="&#x167;" u2="&#x3a;" k="35" />
    <hkern u1="&#x167;" u2="&#x2e;" k="60" />
    <hkern u1="&#x167;" u2="&#x2c;" k="60" />
    <hkern u1="&#x167;" u2="&#x29;" k="-8" />
    <hkern u1="&#x168;" u2="&#x2026;" k="6" />
    <hkern u1="&#x168;" u2="&#x201e;" k="6" />
    <hkern u1="&#x168;" u2="&#x201a;" k="6" />
    <hkern u1="&#x168;" u2="&#x2e;" k="6" />
    <hkern u1="&#x168;" u2="&#x2c;" k="6" />
    <hkern u1="&#x169;" u2="&#x2026;" k="6" />
    <hkern u1="&#x169;" u2="&#x201e;" k="6" />
    <hkern u1="&#x169;" u2="&#x201a;" k="6" />
    <hkern u1="&#x169;" u2="&#x2e;" k="6" />
    <hkern u1="&#x169;" u2="&#x2c;" k="6" />
    <hkern u1="&#x16a;" u2="&#x2026;" k="6" />
    <hkern u1="&#x16a;" u2="&#x201e;" k="6" />
    <hkern u1="&#x16a;" u2="&#x201a;" k="6" />
    <hkern u1="&#x16a;" u2="&#x2e;" k="6" />
    <hkern u1="&#x16a;" u2="&#x2c;" k="6" />
    <hkern u1="&#x16b;" u2="&#x2026;" k="6" />
    <hkern u1="&#x16b;" u2="&#x201e;" k="6" />
    <hkern u1="&#x16b;" u2="&#x201a;" k="6" />
    <hkern u1="&#x16b;" u2="&#x2e;" k="6" />
    <hkern u1="&#x16b;" u2="&#x2c;" k="6" />
    <hkern u1="&#x16c;" u2="&#x2026;" k="6" />
    <hkern u1="&#x16c;" u2="&#x201e;" k="6" />
    <hkern u1="&#x16c;" u2="&#x201a;" k="6" />
    <hkern u1="&#x16c;" u2="&#x2e;" k="6" />
    <hkern u1="&#x16c;" u2="&#x2c;" k="6" />
    <hkern u1="&#x16d;" u2="&#x2026;" k="6" />
    <hkern u1="&#x16d;" u2="&#x201e;" k="6" />
    <hkern u1="&#x16d;" u2="&#x201a;" k="6" />
    <hkern u1="&#x16d;" u2="&#x2e;" k="6" />
    <hkern u1="&#x16d;" u2="&#x2c;" k="6" />
    <hkern u1="&#x16e;" u2="&#x2026;" k="6" />
    <hkern u1="&#x16e;" u2="&#x201e;" k="6" />
    <hkern u1="&#x16e;" u2="&#x201a;" k="6" />
    <hkern u1="&#x16e;" u2="&#x2e;" k="6" />
    <hkern u1="&#x16e;" u2="&#x2c;" k="6" />
    <hkern u1="&#x16f;" u2="&#x2026;" k="6" />
    <hkern u1="&#x16f;" u2="&#x201e;" k="6" />
    <hkern u1="&#x16f;" u2="&#x201a;" k="6" />
    <hkern u1="&#x16f;" u2="&#x2e;" k="6" />
    <hkern u1="&#x16f;" u2="&#x2c;" k="6" />
    <hkern u1="&#x170;" u2="&#x2026;" k="6" />
    <hkern u1="&#x170;" u2="&#x201e;" k="6" />
    <hkern u1="&#x170;" u2="&#x201a;" k="6" />
    <hkern u1="&#x170;" u2="&#x2e;" k="6" />
    <hkern u1="&#x170;" u2="&#x2c;" k="6" />
    <hkern u1="&#x171;" u2="&#x2026;" k="6" />
    <hkern u1="&#x171;" u2="&#x201e;" k="6" />
    <hkern u1="&#x171;" u2="&#x201a;" k="6" />
    <hkern u1="&#x171;" u2="&#x2e;" k="6" />
    <hkern u1="&#x171;" u2="&#x2c;" k="6" />
    <hkern u1="&#x172;" u2="&#x2026;" k="6" />
    <hkern u1="&#x172;" u2="&#x201e;" k="6" />
    <hkern u1="&#x172;" u2="&#x201a;" k="6" />
    <hkern u1="&#x172;" u2="&#x2e;" k="6" />
    <hkern u1="&#x172;" u2="&#x2c;" k="6" />
    <hkern u1="&#x173;" u2="&#x2026;" k="6" />
    <hkern u1="&#x173;" u2="&#x201e;" k="6" />
    <hkern u1="&#x173;" u2="&#x201a;" k="6" />
    <hkern u1="&#x173;" u2="&#x2e;" k="6" />
    <hkern u1="&#x173;" u2="&#x2c;" k="6" />
    <hkern u1="&#x174;" u2="&#x2026;" k="40" />
    <hkern u1="&#x174;" u2="&#x201e;" k="40" />
    <hkern u1="&#x174;" u2="&#x201a;" k="40" />
    <hkern u1="&#x174;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x174;" u2="]" k="-3" />
    <hkern u1="&#x174;" u2="&#x2e;" k="40" />
    <hkern u1="&#x174;" u2="&#x2c;" k="40" />
    <hkern u1="&#x174;" u2="&#x29;" k="-3" />
    <hkern u1="&#x175;" u2="&#x2026;" k="40" />
    <hkern u1="&#x175;" u2="&#x201e;" k="40" />
    <hkern u1="&#x175;" u2="&#x201a;" k="40" />
    <hkern u1="&#x175;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x175;" u2="]" k="-3" />
    <hkern u1="&#x175;" u2="&#x2e;" k="40" />
    <hkern u1="&#x175;" u2="&#x2c;" k="40" />
    <hkern u1="&#x175;" u2="&#x29;" k="-3" />
    <hkern u1="&#x176;" u2="&#x2026;" k="80" />
    <hkern u1="&#x176;" u2="&#x201e;" k="80" />
    <hkern u1="&#x176;" u2="&#x201a;" k="80" />
    <hkern u1="&#x176;" u2="&#x153;" k="15" />
    <hkern u1="&#x176;" u2="&#x152;" k="15" />
    <hkern u1="&#x176;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x176;" u2="q" k="15" />
    <hkern u1="&#x176;" u2="]" k="-5" />
    <hkern u1="&#x176;" u2="Q" k="15" />
    <hkern u1="&#x176;" u2="&#x3b;" k="20" />
    <hkern u1="&#x176;" u2="&#x3a;" k="20" />
    <hkern u1="&#x176;" u2="&#x2e;" k="80" />
    <hkern u1="&#x176;" u2="&#x2c;" k="80" />
    <hkern u1="&#x176;" u2="&#x29;" k="-5" />
    <hkern u1="&#x177;" u2="&#x2026;" k="80" />
    <hkern u1="&#x177;" u2="&#x201e;" k="80" />
    <hkern u1="&#x177;" u2="&#x201a;" k="80" />
    <hkern u1="&#x177;" u2="&#x153;" k="15" />
    <hkern u1="&#x177;" u2="&#x152;" k="15" />
    <hkern u1="&#x177;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x177;" u2="q" k="15" />
    <hkern u1="&#x177;" u2="]" k="-5" />
    <hkern u1="&#x177;" u2="Q" k="15" />
    <hkern u1="&#x177;" u2="&#x3b;" k="20" />
    <hkern u1="&#x177;" u2="&#x3a;" k="20" />
    <hkern u1="&#x177;" u2="&#x2e;" k="80" />
    <hkern u1="&#x177;" u2="&#x2c;" k="80" />
    <hkern u1="&#x177;" u2="&#x29;" k="-5" />
    <hkern u1="&#x178;" u2="&#x2026;" k="80" />
    <hkern u1="&#x178;" u2="&#x201e;" k="80" />
    <hkern u1="&#x178;" u2="&#x201a;" k="80" />
    <hkern u1="&#x178;" u2="&#x153;" k="15" />
    <hkern u1="&#x178;" u2="&#x152;" k="15" />
    <hkern u1="&#x178;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x178;" u2="q" k="15" />
    <hkern u1="&#x178;" u2="]" k="-5" />
    <hkern u1="&#x178;" u2="Q" k="15" />
    <hkern u1="&#x178;" u2="&#x3b;" k="20" />
    <hkern u1="&#x178;" u2="&#x3a;" k="20" />
    <hkern u1="&#x178;" u2="&#x2e;" k="80" />
    <hkern u1="&#x178;" u2="&#x2c;" k="80" />
    <hkern u1="&#x178;" u2="&#x29;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x2026;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x201e;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x201d;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x201c;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x201a;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x2018;" k="10" />
    <hkern u1="&#x1fe;" u2="x" k="15" />
    <hkern u1="&#x1fe;" u2="v" k="1" />
    <hkern u1="&#x1fe;" u2="X" k="15" />
    <hkern u1="&#x1fe;" u2="V" k="1" />
    <hkern u1="&#x1fe;" u2="&#x2e;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x2c;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x2026;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x201e;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x201d;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x201c;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x201a;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x2018;" k="10" />
    <hkern u1="&#x1ff;" u2="x" k="15" />
    <hkern u1="&#x1ff;" u2="v" k="1" />
    <hkern u1="&#x1ff;" u2="X" k="15" />
    <hkern u1="&#x1ff;" u2="V" k="1" />
    <hkern u1="&#x1ff;" u2="&#x2e;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x2c;" k="10" />
    <hkern u1="&#x218;" u2="&#x2026;" k="5" />
    <hkern u1="&#x218;" u2="&#x201e;" k="5" />
    <hkern u1="&#x218;" u2="&#x201d;" k="5" />
    <hkern u1="&#x218;" u2="&#x201c;" k="5" />
    <hkern u1="&#x218;" u2="&#x201a;" k="5" />
    <hkern u1="&#x218;" u2="&#x2018;" k="5" />
    <hkern u1="&#x218;" u2="x" k="8" />
    <hkern u1="&#x218;" u2="v" k="1" />
    <hkern u1="&#x218;" u2="X" k="8" />
    <hkern u1="&#x218;" u2="V" k="1" />
    <hkern u1="&#x218;" u2="&#x2e;" k="5" />
    <hkern u1="&#x218;" u2="&#x2c;" k="5" />
    <hkern u1="&#x219;" u2="&#x2026;" k="5" />
    <hkern u1="&#x219;" u2="&#x201e;" k="5" />
    <hkern u1="&#x219;" u2="&#x201d;" k="5" />
    <hkern u1="&#x219;" u2="&#x201c;" k="5" />
    <hkern u1="&#x219;" u2="&#x201a;" k="5" />
    <hkern u1="&#x219;" u2="&#x2018;" k="5" />
    <hkern u1="&#x219;" u2="x" k="8" />
    <hkern u1="&#x219;" u2="v" k="1" />
    <hkern u1="&#x219;" u2="X" k="8" />
    <hkern u1="&#x219;" u2="V" k="1" />
    <hkern u1="&#x219;" u2="&#x2e;" k="5" />
    <hkern u1="&#x219;" u2="&#x2c;" k="5" />
    <hkern u1="&#x21a;" u2="&#x2026;" k="60" />
    <hkern u1="&#x21a;" u2="&#x201e;" k="60" />
    <hkern u1="&#x21a;" u2="&#x201a;" k="60" />
    <hkern u1="&#x21a;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x21a;" u2="]" k="-8" />
    <hkern u1="&#x21a;" u2="&#x3b;" k="35" />
    <hkern u1="&#x21a;" u2="&#x3a;" k="35" />
    <hkern u1="&#x21a;" u2="&#x2e;" k="60" />
    <hkern u1="&#x21a;" u2="&#x2c;" k="60" />
    <hkern u1="&#x21a;" u2="&#x29;" k="-8" />
    <hkern u1="&#x21b;" u2="&#x2026;" k="60" />
    <hkern u1="&#x21b;" u2="&#x201e;" k="60" />
    <hkern u1="&#x21b;" u2="&#x201a;" k="60" />
    <hkern u1="&#x21b;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x21b;" u2="]" k="-8" />
    <hkern u1="&#x21b;" u2="&#x3b;" k="35" />
    <hkern u1="&#x21b;" u2="&#x3a;" k="35" />
    <hkern u1="&#x21b;" u2="&#x2e;" k="60" />
    <hkern u1="&#x21b;" u2="&#x2c;" k="60" />
    <hkern u1="&#x21b;" u2="&#x29;" k="-8" />
    <hkern u1="&#x237;" u2="&#x2026;" k="6" />
    <hkern u1="&#x237;" u2="&#x201e;" k="6" />
    <hkern u1="&#x237;" u2="&#x201a;" k="6" />
    <hkern u1="&#x237;" u2="&#x2e;" k="6" />
    <hkern u1="&#x237;" u2="&#x2c;" k="6" />
    <hkern u1="&#x2bc;" u2="&#x153;" k="10" />
    <hkern u1="&#x2bc;" u2="&#x152;" k="10" />
    <hkern u1="&#x2bc;" u2="x" k="2" />
    <hkern u1="&#x2bc;" u2="q" k="10" />
    <hkern u1="&#x2bc;" u2="X" k="2" />
    <hkern u1="&#x2bc;" u2="Q" k="10" />
    <hkern u1="&#x1e02;" u2="&#x2026;" k="5" />
    <hkern u1="&#x1e02;" u2="&#x201e;" k="5" />
    <hkern u1="&#x1e02;" u2="&#x201d;" k="10" />
    <hkern u1="&#x1e02;" u2="&#x201c;" k="10" />
    <hkern u1="&#x1e02;" u2="&#x201a;" k="5" />
    <hkern u1="&#x1e02;" u2="&#x2018;" k="10" />
    <hkern u1="&#x1e02;" u2="x" k="3" />
    <hkern u1="&#x1e02;" u2="v" k="4" />
    <hkern u1="&#x1e02;" u2="X" k="3" />
    <hkern u1="&#x1e02;" u2="V" k="4" />
    <hkern u1="&#x1e02;" u2="&#x2e;" k="5" />
    <hkern u1="&#x1e02;" u2="&#x2c;" k="5" />
    <hkern u1="&#x1e03;" u2="&#x2026;" k="5" />
    <hkern u1="&#x1e03;" u2="&#x201e;" k="5" />
    <hkern u1="&#x1e03;" u2="&#x201d;" k="10" />
    <hkern u1="&#x1e03;" u2="&#x201c;" k="10" />
    <hkern u1="&#x1e03;" u2="&#x201a;" k="5" />
    <hkern u1="&#x1e03;" u2="&#x2018;" k="10" />
    <hkern u1="&#x1e03;" u2="x" k="3" />
    <hkern u1="&#x1e03;" u2="v" k="4" />
    <hkern u1="&#x1e03;" u2="X" k="3" />
    <hkern u1="&#x1e03;" u2="V" k="4" />
    <hkern u1="&#x1e03;" u2="&#x2e;" k="5" />
    <hkern u1="&#x1e03;" u2="&#x2c;" k="5" />
    <hkern u1="&#x1e0a;" u2="&#x2026;" k="10" />
    <hkern u1="&#x1e0a;" u2="&#x201e;" k="10" />
    <hkern u1="&#x1e0a;" u2="&#x201d;" k="10" />
    <hkern u1="&#x1e0a;" u2="&#x201c;" k="10" />
    <hkern u1="&#x1e0a;" u2="&#x201a;" k="10" />
    <hkern u1="&#x1e0a;" u2="&#x2018;" k="10" />
    <hkern u1="&#x1e0a;" u2="x" k="15" />
    <hkern u1="&#x1e0a;" u2="v" k="1" />
    <hkern u1="&#x1e0a;" u2="X" k="15" />
    <hkern u1="&#x1e0a;" u2="V" k="1" />
    <hkern u1="&#x1e0a;" u2="&#x2e;" k="10" />
    <hkern u1="&#x1e0a;" u2="&#x2c;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x2026;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x201e;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x201d;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x201c;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x201a;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x2018;" k="10" />
    <hkern u1="&#x1e0b;" u2="x" k="15" />
    <hkern u1="&#x1e0b;" u2="v" k="1" />
    <hkern u1="&#x1e0b;" u2="X" k="15" />
    <hkern u1="&#x1e0b;" u2="V" k="1" />
    <hkern u1="&#x1e0b;" u2="&#x2e;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x2c;" k="10" />
    <hkern u1="&#x1e1e;" u2="&#x2026;" k="70" />
    <hkern u1="&#x1e1e;" u2="&#x201e;" k="70" />
    <hkern u1="&#x1e1e;" u2="&#x201a;" k="70" />
    <hkern u1="&#x1e1e;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x1e1e;" u2="]" k="-5" />
    <hkern u1="&#x1e1e;" u2="&#x2e;" k="70" />
    <hkern u1="&#x1e1e;" u2="&#x2c;" k="70" />
    <hkern u1="&#x1e1e;" u2="&#x29;" k="-5" />
    <hkern u1="&#x1e1f;" u2="&#x2026;" k="70" />
    <hkern u1="&#x1e1f;" u2="&#x201e;" k="70" />
    <hkern u1="&#x1e1f;" u2="&#x201a;" k="70" />
    <hkern u1="&#x1e1f;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x1e1f;" u2="]" k="-5" />
    <hkern u1="&#x1e1f;" u2="&#x2e;" k="70" />
    <hkern u1="&#x1e1f;" u2="&#x2c;" k="70" />
    <hkern u1="&#x1e1f;" u2="&#x29;" k="-5" />
    <hkern u1="&#x1e56;" u2="&#x2026;" k="80" />
    <hkern u1="&#x1e56;" u2="&#x201e;" k="80" />
    <hkern u1="&#x1e56;" u2="&#x201a;" k="80" />
    <hkern u1="&#x1e56;" u2="x" k="11" />
    <hkern u1="&#x1e56;" u2="X" k="11" />
    <hkern u1="&#x1e56;" u2="&#x2e;" k="80" />
    <hkern u1="&#x1e56;" u2="&#x2c;" k="80" />
    <hkern u1="&#x1e57;" u2="&#x2026;" k="80" />
    <hkern u1="&#x1e57;" u2="&#x201e;" k="80" />
    <hkern u1="&#x1e57;" u2="&#x201a;" k="80" />
    <hkern u1="&#x1e57;" u2="x" k="11" />
    <hkern u1="&#x1e57;" u2="X" k="11" />
    <hkern u1="&#x1e57;" u2="&#x2e;" k="80" />
    <hkern u1="&#x1e57;" u2="&#x2c;" k="80" />
    <hkern u1="&#x1e60;" u2="&#x2026;" k="5" />
    <hkern u1="&#x1e60;" u2="&#x201e;" k="5" />
    <hkern u1="&#x1e60;" u2="&#x201d;" k="5" />
    <hkern u1="&#x1e60;" u2="&#x201c;" k="5" />
    <hkern u1="&#x1e60;" u2="&#x201a;" k="5" />
    <hkern u1="&#x1e60;" u2="&#x2018;" k="5" />
    <hkern u1="&#x1e60;" u2="x" k="8" />
    <hkern u1="&#x1e60;" u2="v" k="1" />
    <hkern u1="&#x1e60;" u2="X" k="8" />
    <hkern u1="&#x1e60;" u2="V" k="1" />
    <hkern u1="&#x1e60;" u2="&#x2e;" k="5" />
    <hkern u1="&#x1e60;" u2="&#x2c;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x2026;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x201e;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x201d;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x201c;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x201a;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x2018;" k="5" />
    <hkern u1="&#x1e61;" u2="x" k="8" />
    <hkern u1="&#x1e61;" u2="v" k="1" />
    <hkern u1="&#x1e61;" u2="X" k="8" />
    <hkern u1="&#x1e61;" u2="V" k="1" />
    <hkern u1="&#x1e61;" u2="&#x2e;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x2c;" k="5" />
    <hkern u1="&#x1e6a;" u2="&#x2026;" k="60" />
    <hkern u1="&#x1e6a;" u2="&#x201e;" k="60" />
    <hkern u1="&#x1e6a;" u2="&#x201a;" k="60" />
    <hkern u1="&#x1e6a;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x1e6a;" u2="]" k="-8" />
    <hkern u1="&#x1e6a;" u2="&#x3b;" k="35" />
    <hkern u1="&#x1e6a;" u2="&#x3a;" k="35" />
    <hkern u1="&#x1e6a;" u2="&#x2e;" k="60" />
    <hkern u1="&#x1e6a;" u2="&#x2c;" k="60" />
    <hkern u1="&#x1e6a;" u2="&#x29;" k="-8" />
    <hkern u1="&#x1e6b;" u2="&#x2026;" k="60" />
    <hkern u1="&#x1e6b;" u2="&#x201e;" k="60" />
    <hkern u1="&#x1e6b;" u2="&#x201a;" k="60" />
    <hkern u1="&#x1e6b;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x1e6b;" u2="]" k="-8" />
    <hkern u1="&#x1e6b;" u2="&#x3b;" k="35" />
    <hkern u1="&#x1e6b;" u2="&#x3a;" k="35" />
    <hkern u1="&#x1e6b;" u2="&#x2e;" k="60" />
    <hkern u1="&#x1e6b;" u2="&#x2c;" k="60" />
    <hkern u1="&#x1e6b;" u2="&#x29;" k="-8" />
    <hkern u1="&#x1e80;" u2="&#x2026;" k="40" />
    <hkern u1="&#x1e80;" u2="&#x201e;" k="40" />
    <hkern u1="&#x1e80;" u2="&#x201a;" k="40" />
    <hkern u1="&#x1e80;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x1e80;" u2="]" k="-3" />
    <hkern u1="&#x1e80;" u2="&#x2e;" k="40" />
    <hkern u1="&#x1e80;" u2="&#x2c;" k="40" />
    <hkern u1="&#x1e80;" u2="&#x29;" k="-3" />
    <hkern u1="&#x1e81;" u2="&#x2026;" k="40" />
    <hkern u1="&#x1e81;" u2="&#x201e;" k="40" />
    <hkern u1="&#x1e81;" u2="&#x201a;" k="40" />
    <hkern u1="&#x1e81;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x1e81;" u2="]" k="-3" />
    <hkern u1="&#x1e81;" u2="&#x2e;" k="40" />
    <hkern u1="&#x1e81;" u2="&#x2c;" k="40" />
    <hkern u1="&#x1e81;" u2="&#x29;" k="-3" />
    <hkern u1="&#x1e82;" u2="&#x2026;" k="40" />
    <hkern u1="&#x1e82;" u2="&#x201e;" k="40" />
    <hkern u1="&#x1e82;" u2="&#x201a;" k="40" />
    <hkern u1="&#x1e82;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x1e82;" u2="]" k="-3" />
    <hkern u1="&#x1e82;" u2="&#x2e;" k="40" />
    <hkern u1="&#x1e82;" u2="&#x2c;" k="40" />
    <hkern u1="&#x1e82;" u2="&#x29;" k="-3" />
    <hkern u1="&#x1e83;" u2="&#x2026;" k="40" />
    <hkern u1="&#x1e83;" u2="&#x201e;" k="40" />
    <hkern u1="&#x1e83;" u2="&#x201a;" k="40" />
    <hkern u1="&#x1e83;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x1e83;" u2="]" k="-3" />
    <hkern u1="&#x1e83;" u2="&#x2e;" k="40" />
    <hkern u1="&#x1e83;" u2="&#x2c;" k="40" />
    <hkern u1="&#x1e83;" u2="&#x29;" k="-3" />
    <hkern u1="&#x1e84;" u2="&#x2026;" k="40" />
    <hkern u1="&#x1e84;" u2="&#x201e;" k="40" />
    <hkern u1="&#x1e84;" u2="&#x201a;" k="40" />
    <hkern u1="&#x1e84;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x1e84;" u2="]" k="-3" />
    <hkern u1="&#x1e84;" u2="&#x2e;" k="40" />
    <hkern u1="&#x1e84;" u2="&#x2c;" k="40" />
    <hkern u1="&#x1e84;" u2="&#x29;" k="-3" />
    <hkern u1="&#x1e85;" u2="&#x2026;" k="40" />
    <hkern u1="&#x1e85;" u2="&#x201e;" k="40" />
    <hkern u1="&#x1e85;" u2="&#x201a;" k="40" />
    <hkern u1="&#x1e85;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x1e85;" u2="]" k="-3" />
    <hkern u1="&#x1e85;" u2="&#x2e;" k="40" />
    <hkern u1="&#x1e85;" u2="&#x2c;" k="40" />
    <hkern u1="&#x1e85;" u2="&#x29;" k="-3" />
    <hkern u1="&#x1ef2;" u2="&#x2026;" k="80" />
    <hkern u1="&#x1ef2;" u2="&#x201e;" k="80" />
    <hkern u1="&#x1ef2;" u2="&#x201a;" k="80" />
    <hkern u1="&#x1ef2;" u2="&#x153;" k="15" />
    <hkern u1="&#x1ef2;" u2="&#x152;" k="15" />
    <hkern u1="&#x1ef2;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x1ef2;" u2="q" k="15" />
    <hkern u1="&#x1ef2;" u2="]" k="-5" />
    <hkern u1="&#x1ef2;" u2="Q" k="15" />
    <hkern u1="&#x1ef2;" u2="&#x3b;" k="20" />
    <hkern u1="&#x1ef2;" u2="&#x3a;" k="20" />
    <hkern u1="&#x1ef2;" u2="&#x2e;" k="80" />
    <hkern u1="&#x1ef2;" u2="&#x2c;" k="80" />
    <hkern u1="&#x1ef2;" u2="&#x29;" k="-5" />
    <hkern u1="&#x1ef3;" u2="&#x2026;" k="80" />
    <hkern u1="&#x1ef3;" u2="&#x201e;" k="80" />
    <hkern u1="&#x1ef3;" u2="&#x201a;" k="80" />
    <hkern u1="&#x1ef3;" u2="&#x153;" k="15" />
    <hkern u1="&#x1ef3;" u2="&#x152;" k="15" />
    <hkern u1="&#x1ef3;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x1ef3;" u2="q" k="15" />
    <hkern u1="&#x1ef3;" u2="]" k="-5" />
    <hkern u1="&#x1ef3;" u2="Q" k="15" />
    <hkern u1="&#x1ef3;" u2="&#x3b;" k="20" />
    <hkern u1="&#x1ef3;" u2="&#x3a;" k="20" />
    <hkern u1="&#x1ef3;" u2="&#x2e;" k="80" />
    <hkern u1="&#x1ef3;" u2="&#x2c;" k="80" />
    <hkern u1="&#x1ef3;" u2="&#x29;" k="-5" />
    <hkern u1="&#x2018;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x2018;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x2018;" u2="&#x237;" k="40" />
    <hkern u1="&#x2018;" u2="&#x219;" k="5" />
    <hkern u1="&#x2018;" u2="&#x218;" k="5" />
    <hkern u1="&#x2018;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x2018;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x2018;" u2="&#x17e;" k="4" />
    <hkern u1="&#x2018;" u2="&#x17d;" k="4" />
    <hkern u1="&#x2018;" u2="&#x17c;" k="4" />
    <hkern u1="&#x2018;" u2="&#x17b;" k="4" />
    <hkern u1="&#x2018;" u2="&#x17a;" k="4" />
    <hkern u1="&#x2018;" u2="&#x179;" k="4" />
    <hkern u1="&#x2018;" u2="&#x161;" k="5" />
    <hkern u1="&#x2018;" u2="&#x160;" k="5" />
    <hkern u1="&#x2018;" u2="&#x15f;" k="5" />
    <hkern u1="&#x2018;" u2="&#x15e;" k="5" />
    <hkern u1="&#x2018;" u2="&#x15d;" k="5" />
    <hkern u1="&#x2018;" u2="&#x15c;" k="5" />
    <hkern u1="&#x2018;" u2="&#x15b;" k="5" />
    <hkern u1="&#x2018;" u2="&#x15a;" k="5" />
    <hkern u1="&#x2018;" u2="&#x153;" k="10" />
    <hkern u1="&#x2018;" u2="&#x152;" k="10" />
    <hkern u1="&#x2018;" u2="&#x151;" k="10" />
    <hkern u1="&#x2018;" u2="&#x150;" k="10" />
    <hkern u1="&#x2018;" u2="&#x14f;" k="10" />
    <hkern u1="&#x2018;" u2="&#x14e;" k="10" />
    <hkern u1="&#x2018;" u2="&#x14d;" k="10" />
    <hkern u1="&#x2018;" u2="&#x14c;" k="10" />
    <hkern u1="&#x2018;" u2="&#x135;" k="40" />
    <hkern u1="&#x2018;" u2="&#x134;" k="40" />
    <hkern u1="&#x2018;" u2="&#x133;" k="40" />
    <hkern u1="&#x2018;" u2="&#x132;" k="40" />
    <hkern u1="&#x2018;" u2="&#x123;" k="10" />
    <hkern u1="&#x2018;" u2="&#x122;" k="10" />
    <hkern u1="&#x2018;" u2="&#x121;" k="10" />
    <hkern u1="&#x2018;" u2="&#x120;" k="10" />
    <hkern u1="&#x2018;" u2="&#x11f;" k="10" />
    <hkern u1="&#x2018;" u2="&#x11e;" k="10" />
    <hkern u1="&#x2018;" u2="&#x11d;" k="10" />
    <hkern u1="&#x2018;" u2="&#x11c;" k="10" />
    <hkern u1="&#x2018;" u2="&#x10d;" k="10" />
    <hkern u1="&#x2018;" u2="&#x10c;" k="10" />
    <hkern u1="&#x2018;" u2="&#x10b;" k="10" />
    <hkern u1="&#x2018;" u2="&#x10a;" k="10" />
    <hkern u1="&#x2018;" u2="&#x109;" k="10" />
    <hkern u1="&#x2018;" u2="&#x108;" k="10" />
    <hkern u1="&#x2018;" u2="&#x107;" k="10" />
    <hkern u1="&#x2018;" u2="&#x106;" k="10" />
    <hkern u1="&#x2018;" u2="&#x105;" k="60" />
    <hkern u1="&#x2018;" u2="&#x104;" k="60" />
    <hkern u1="&#x2018;" u2="&#x103;" k="60" />
    <hkern u1="&#x2018;" u2="&#x102;" k="60" />
    <hkern u1="&#x2018;" u2="&#x101;" k="60" />
    <hkern u1="&#x2018;" u2="&#x100;" k="60" />
    <hkern u1="&#x2018;" u2="&#xf8;" k="10" />
    <hkern u1="&#x2018;" u2="&#xf6;" k="10" />
    <hkern u1="&#x2018;" u2="&#xf5;" k="10" />
    <hkern u1="&#x2018;" u2="&#xf4;" k="10" />
    <hkern u1="&#x2018;" u2="&#xf3;" k="10" />
    <hkern u1="&#x2018;" u2="&#xf2;" k="10" />
    <hkern u1="&#x2018;" u2="&#xe7;" k="10" />
    <hkern u1="&#x2018;" u2="&#xe5;" k="60" />
    <hkern u1="&#x2018;" u2="&#xe4;" k="60" />
    <hkern u1="&#x2018;" u2="&#xe3;" k="60" />
    <hkern u1="&#x2018;" u2="&#xe2;" k="60" />
    <hkern u1="&#x2018;" u2="&#xe1;" k="60" />
    <hkern u1="&#x2018;" u2="&#xe0;" k="60" />
    <hkern u1="&#x2018;" u2="&#xdf;" k="5" />
    <hkern u1="&#x2018;" u2="&#xd8;" k="10" />
    <hkern u1="&#x2018;" u2="&#xd6;" k="10" />
    <hkern u1="&#x2018;" u2="&#xd5;" k="10" />
    <hkern u1="&#x2018;" u2="&#xd4;" k="10" />
    <hkern u1="&#x2018;" u2="&#xd3;" k="10" />
    <hkern u1="&#x2018;" u2="&#xd2;" k="10" />
    <hkern u1="&#x2018;" u2="&#xc7;" k="10" />
    <hkern u1="&#x2018;" u2="&#xc5;" k="60" />
    <hkern u1="&#x2018;" u2="&#xc4;" k="60" />
    <hkern u1="&#x2018;" u2="&#xc3;" k="60" />
    <hkern u1="&#x2018;" u2="&#xc2;" k="60" />
    <hkern u1="&#x2018;" u2="&#xc1;" k="60" />
    <hkern u1="&#x2018;" u2="&#xc0;" k="60" />
    <hkern u1="&#x2018;" u2="z" k="4" />
    <hkern u1="&#x2018;" u2="x" k="2" />
    <hkern u1="&#x2018;" u2="s" k="5" />
    <hkern u1="&#x2018;" u2="q" k="10" />
    <hkern u1="&#x2018;" u2="o" k="10" />
    <hkern u1="&#x2018;" u2="j" k="40" />
    <hkern u1="&#x2018;" u2="g" k="10" />
    <hkern u1="&#x2018;" u2="c" k="10" />
    <hkern u1="&#x2018;" u2="a" k="60" />
    <hkern u1="&#x2018;" u2="Z" k="4" />
    <hkern u1="&#x2018;" u2="X" k="2" />
    <hkern u1="&#x2018;" u2="S" k="5" />
    <hkern u1="&#x2018;" u2="Q" k="10" />
    <hkern u1="&#x2018;" u2="O" k="10" />
    <hkern u1="&#x2018;" u2="J" k="40" />
    <hkern u1="&#x2018;" u2="G" k="10" />
    <hkern u1="&#x2018;" u2="C" k="10" />
    <hkern u1="&#x2018;" u2="A" k="60" />
    <hkern u1="&#x2019;" u2="&#x153;" k="10" />
    <hkern u1="&#x2019;" u2="&#x152;" k="10" />
    <hkern u1="&#x2019;" u2="x" k="2" />
    <hkern u1="&#x2019;" u2="q" k="10" />
    <hkern u1="&#x2019;" u2="X" k="2" />
    <hkern u1="&#x2019;" u2="Q" k="10" />
    <hkern u1="&#x201a;" u2="&#x1ef3;" k="80" />
    <hkern u1="&#x201a;" u2="&#x1ef2;" k="80" />
    <hkern u1="&#x201a;" u2="&#x1e85;" k="40" />
    <hkern u1="&#x201a;" u2="&#x1e84;" k="40" />
    <hkern u1="&#x201a;" u2="&#x1e83;" k="40" />
    <hkern u1="&#x201a;" u2="&#x1e82;" k="40" />
    <hkern u1="&#x201a;" u2="&#x1e81;" k="40" />
    <hkern u1="&#x201a;" u2="&#x1e80;" k="40" />
    <hkern u1="&#x201a;" u2="&#x1e6b;" k="60" />
    <hkern u1="&#x201a;" u2="&#x1e6a;" k="60" />
    <hkern u1="&#x201a;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x201a;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x201a;" u2="&#x21b;" k="60" />
    <hkern u1="&#x201a;" u2="&#x21a;" k="60" />
    <hkern u1="&#x201a;" u2="&#x219;" k="5" />
    <hkern u1="&#x201a;" u2="&#x218;" k="5" />
    <hkern u1="&#x201a;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x201a;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x201a;" u2="&#x178;" k="80" />
    <hkern u1="&#x201a;" u2="&#x177;" k="80" />
    <hkern u1="&#x201a;" u2="&#x176;" k="80" />
    <hkern u1="&#x201a;" u2="&#x175;" k="40" />
    <hkern u1="&#x201a;" u2="&#x174;" k="40" />
    <hkern u1="&#x201a;" u2="&#x173;" k="6" />
    <hkern u1="&#x201a;" u2="&#x172;" k="6" />
    <hkern u1="&#x201a;" u2="&#x171;" k="6" />
    <hkern u1="&#x201a;" u2="&#x170;" k="6" />
    <hkern u1="&#x201a;" u2="&#x16f;" k="6" />
    <hkern u1="&#x201a;" u2="&#x16e;" k="6" />
    <hkern u1="&#x201a;" u2="&#x16d;" k="6" />
    <hkern u1="&#x201a;" u2="&#x16c;" k="6" />
    <hkern u1="&#x201a;" u2="&#x16b;" k="6" />
    <hkern u1="&#x201a;" u2="&#x16a;" k="6" />
    <hkern u1="&#x201a;" u2="&#x169;" k="6" />
    <hkern u1="&#x201a;" u2="&#x168;" k="6" />
    <hkern u1="&#x201a;" u2="&#x167;" k="60" />
    <hkern u1="&#x201a;" u2="&#x166;" k="60" />
    <hkern u1="&#x201a;" u2="&#x165;" k="60" />
    <hkern u1="&#x201a;" u2="&#x164;" k="60" />
    <hkern u1="&#x201a;" u2="&#x163;" k="60" />
    <hkern u1="&#x201a;" u2="&#x162;" k="60" />
    <hkern u1="&#x201a;" u2="&#x161;" k="5" />
    <hkern u1="&#x201a;" u2="&#x160;" k="5" />
    <hkern u1="&#x201a;" u2="&#x15f;" k="5" />
    <hkern u1="&#x201a;" u2="&#x15e;" k="5" />
    <hkern u1="&#x201a;" u2="&#x15d;" k="5" />
    <hkern u1="&#x201a;" u2="&#x15c;" k="5" />
    <hkern u1="&#x201a;" u2="&#x15b;" k="5" />
    <hkern u1="&#x201a;" u2="&#x15a;" k="5" />
    <hkern u1="&#x201a;" u2="&#x153;" k="10" />
    <hkern u1="&#x201a;" u2="&#x152;" k="10" />
    <hkern u1="&#x201a;" u2="&#x151;" k="10" />
    <hkern u1="&#x201a;" u2="&#x150;" k="10" />
    <hkern u1="&#x201a;" u2="&#x14f;" k="10" />
    <hkern u1="&#x201a;" u2="&#x14e;" k="10" />
    <hkern u1="&#x201a;" u2="&#x14d;" k="10" />
    <hkern u1="&#x201a;" u2="&#x14c;" k="10" />
    <hkern u1="&#x201a;" u2="&#x123;" k="10" />
    <hkern u1="&#x201a;" u2="&#x122;" k="10" />
    <hkern u1="&#x201a;" u2="&#x121;" k="10" />
    <hkern u1="&#x201a;" u2="&#x120;" k="10" />
    <hkern u1="&#x201a;" u2="&#x11f;" k="10" />
    <hkern u1="&#x201a;" u2="&#x11e;" k="10" />
    <hkern u1="&#x201a;" u2="&#x11d;" k="10" />
    <hkern u1="&#x201a;" u2="&#x11c;" k="10" />
    <hkern u1="&#x201a;" u2="&#x10d;" k="10" />
    <hkern u1="&#x201a;" u2="&#x10c;" k="10" />
    <hkern u1="&#x201a;" u2="&#x10b;" k="10" />
    <hkern u1="&#x201a;" u2="&#x10a;" k="10" />
    <hkern u1="&#x201a;" u2="&#x109;" k="10" />
    <hkern u1="&#x201a;" u2="&#x108;" k="10" />
    <hkern u1="&#x201a;" u2="&#x107;" k="10" />
    <hkern u1="&#x201a;" u2="&#x106;" k="10" />
    <hkern u1="&#x201a;" u2="&#xff;" k="80" />
    <hkern u1="&#x201a;" u2="&#xfd;" k="80" />
    <hkern u1="&#x201a;" u2="&#xfc;" k="6" />
    <hkern u1="&#x201a;" u2="&#xfb;" k="6" />
    <hkern u1="&#x201a;" u2="&#xfa;" k="6" />
    <hkern u1="&#x201a;" u2="&#xf9;" k="6" />
    <hkern u1="&#x201a;" u2="&#xf8;" k="10" />
    <hkern u1="&#x201a;" u2="&#xf6;" k="10" />
    <hkern u1="&#x201a;" u2="&#xf5;" k="10" />
    <hkern u1="&#x201a;" u2="&#xf4;" k="10" />
    <hkern u1="&#x201a;" u2="&#xf3;" k="10" />
    <hkern u1="&#x201a;" u2="&#xf2;" k="10" />
    <hkern u1="&#x201a;" u2="&#xe7;" k="10" />
    <hkern u1="&#x201a;" u2="&#xdf;" k="5" />
    <hkern u1="&#x201a;" u2="&#xdd;" k="80" />
    <hkern u1="&#x201a;" u2="&#xdc;" k="6" />
    <hkern u1="&#x201a;" u2="&#xdb;" k="6" />
    <hkern u1="&#x201a;" u2="&#xda;" k="6" />
    <hkern u1="&#x201a;" u2="&#xd9;" k="6" />
    <hkern u1="&#x201a;" u2="&#xd8;" k="10" />
    <hkern u1="&#x201a;" u2="&#xd6;" k="10" />
    <hkern u1="&#x201a;" u2="&#xd5;" k="10" />
    <hkern u1="&#x201a;" u2="&#xd4;" k="10" />
    <hkern u1="&#x201a;" u2="&#xd3;" k="10" />
    <hkern u1="&#x201a;" u2="&#xd2;" k="10" />
    <hkern u1="&#x201a;" u2="&#xc7;" k="10" />
    <hkern u1="&#x201a;" u2="y" k="80" />
    <hkern u1="&#x201a;" u2="w" k="40" />
    <hkern u1="&#x201a;" u2="v" k="60" />
    <hkern u1="&#x201a;" u2="u" k="6" />
    <hkern u1="&#x201a;" u2="t" k="60" />
    <hkern u1="&#x201a;" u2="s" k="5" />
    <hkern u1="&#x201a;" u2="q" k="10" />
    <hkern u1="&#x201a;" u2="o" k="10" />
    <hkern u1="&#x201a;" u2="g" k="10" />
    <hkern u1="&#x201a;" u2="c" k="10" />
    <hkern u1="&#x201a;" u2="Y" k="80" />
    <hkern u1="&#x201a;" u2="W" k="40" />
    <hkern u1="&#x201a;" u2="V" k="60" />
    <hkern u1="&#x201a;" u2="U" k="6" />
    <hkern u1="&#x201a;" u2="T" k="60" />
    <hkern u1="&#x201a;" u2="S" k="5" />
    <hkern u1="&#x201a;" u2="Q" k="10" />
    <hkern u1="&#x201a;" u2="O" k="10" />
    <hkern u1="&#x201a;" u2="G" k="10" />
    <hkern u1="&#x201a;" u2="C" k="10" />
    <hkern u1="&#x201c;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x201c;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x201c;" u2="&#x237;" k="40" />
    <hkern u1="&#x201c;" u2="&#x219;" k="5" />
    <hkern u1="&#x201c;" u2="&#x218;" k="5" />
    <hkern u1="&#x201c;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x201c;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x201c;" u2="&#x17e;" k="4" />
    <hkern u1="&#x201c;" u2="&#x17d;" k="4" />
    <hkern u1="&#x201c;" u2="&#x17c;" k="4" />
    <hkern u1="&#x201c;" u2="&#x17b;" k="4" />
    <hkern u1="&#x201c;" u2="&#x17a;" k="4" />
    <hkern u1="&#x201c;" u2="&#x179;" k="4" />
    <hkern u1="&#x201c;" u2="&#x161;" k="5" />
    <hkern u1="&#x201c;" u2="&#x160;" k="5" />
    <hkern u1="&#x201c;" u2="&#x15f;" k="5" />
    <hkern u1="&#x201c;" u2="&#x15e;" k="5" />
    <hkern u1="&#x201c;" u2="&#x15d;" k="5" />
    <hkern u1="&#x201c;" u2="&#x15c;" k="5" />
    <hkern u1="&#x201c;" u2="&#x15b;" k="5" />
    <hkern u1="&#x201c;" u2="&#x15a;" k="5" />
    <hkern u1="&#x201c;" u2="&#x153;" k="10" />
    <hkern u1="&#x201c;" u2="&#x152;" k="10" />
    <hkern u1="&#x201c;" u2="&#x151;" k="10" />
    <hkern u1="&#x201c;" u2="&#x150;" k="10" />
    <hkern u1="&#x201c;" u2="&#x14f;" k="10" />
    <hkern u1="&#x201c;" u2="&#x14e;" k="10" />
    <hkern u1="&#x201c;" u2="&#x14d;" k="10" />
    <hkern u1="&#x201c;" u2="&#x14c;" k="10" />
    <hkern u1="&#x201c;" u2="&#x135;" k="40" />
    <hkern u1="&#x201c;" u2="&#x134;" k="40" />
    <hkern u1="&#x201c;" u2="&#x133;" k="40" />
    <hkern u1="&#x201c;" u2="&#x132;" k="40" />
    <hkern u1="&#x201c;" u2="&#x123;" k="10" />
    <hkern u1="&#x201c;" u2="&#x122;" k="10" />
    <hkern u1="&#x201c;" u2="&#x121;" k="10" />
    <hkern u1="&#x201c;" u2="&#x120;" k="10" />
    <hkern u1="&#x201c;" u2="&#x11f;" k="10" />
    <hkern u1="&#x201c;" u2="&#x11e;" k="10" />
    <hkern u1="&#x201c;" u2="&#x11d;" k="10" />
    <hkern u1="&#x201c;" u2="&#x11c;" k="10" />
    <hkern u1="&#x201c;" u2="&#x10d;" k="10" />
    <hkern u1="&#x201c;" u2="&#x10c;" k="10" />
    <hkern u1="&#x201c;" u2="&#x10b;" k="10" />
    <hkern u1="&#x201c;" u2="&#x10a;" k="10" />
    <hkern u1="&#x201c;" u2="&#x109;" k="10" />
    <hkern u1="&#x201c;" u2="&#x108;" k="10" />
    <hkern u1="&#x201c;" u2="&#x107;" k="10" />
    <hkern u1="&#x201c;" u2="&#x106;" k="10" />
    <hkern u1="&#x201c;" u2="&#x105;" k="60" />
    <hkern u1="&#x201c;" u2="&#x104;" k="60" />
    <hkern u1="&#x201c;" u2="&#x103;" k="60" />
    <hkern u1="&#x201c;" u2="&#x102;" k="60" />
    <hkern u1="&#x201c;" u2="&#x101;" k="60" />
    <hkern u1="&#x201c;" u2="&#x100;" k="60" />
    <hkern u1="&#x201c;" u2="&#xf8;" k="10" />
    <hkern u1="&#x201c;" u2="&#xf6;" k="10" />
    <hkern u1="&#x201c;" u2="&#xf5;" k="10" />
    <hkern u1="&#x201c;" u2="&#xf4;" k="10" />
    <hkern u1="&#x201c;" u2="&#xf3;" k="10" />
    <hkern u1="&#x201c;" u2="&#xf2;" k="10" />
    <hkern u1="&#x201c;" u2="&#xe7;" k="10" />
    <hkern u1="&#x201c;" u2="&#xe5;" k="60" />
    <hkern u1="&#x201c;" u2="&#xe4;" k="60" />
    <hkern u1="&#x201c;" u2="&#xe3;" k="60" />
    <hkern u1="&#x201c;" u2="&#xe2;" k="60" />
    <hkern u1="&#x201c;" u2="&#xe1;" k="60" />
    <hkern u1="&#x201c;" u2="&#xe0;" k="60" />
    <hkern u1="&#x201c;" u2="&#xdf;" k="5" />
    <hkern u1="&#x201c;" u2="&#xd8;" k="10" />
    <hkern u1="&#x201c;" u2="&#xd6;" k="10" />
    <hkern u1="&#x201c;" u2="&#xd5;" k="10" />
    <hkern u1="&#x201c;" u2="&#xd4;" k="10" />
    <hkern u1="&#x201c;" u2="&#xd3;" k="10" />
    <hkern u1="&#x201c;" u2="&#xd2;" k="10" />
    <hkern u1="&#x201c;" u2="&#xc7;" k="10" />
    <hkern u1="&#x201c;" u2="&#xc5;" k="60" />
    <hkern u1="&#x201c;" u2="&#xc4;" k="60" />
    <hkern u1="&#x201c;" u2="&#xc3;" k="60" />
    <hkern u1="&#x201c;" u2="&#xc2;" k="60" />
    <hkern u1="&#x201c;" u2="&#xc1;" k="60" />
    <hkern u1="&#x201c;" u2="&#xc0;" k="60" />
    <hkern u1="&#x201c;" u2="z" k="4" />
    <hkern u1="&#x201c;" u2="x" k="2" />
    <hkern u1="&#x201c;" u2="s" k="5" />
    <hkern u1="&#x201c;" u2="q" k="10" />
    <hkern u1="&#x201c;" u2="o" k="10" />
    <hkern u1="&#x201c;" u2="j" k="40" />
    <hkern u1="&#x201c;" u2="g" k="10" />
    <hkern u1="&#x201c;" u2="c" k="10" />
    <hkern u1="&#x201c;" u2="a" k="60" />
    <hkern u1="&#x201c;" u2="Z" k="4" />
    <hkern u1="&#x201c;" u2="X" k="2" />
    <hkern u1="&#x201c;" u2="S" k="5" />
    <hkern u1="&#x201c;" u2="Q" k="10" />
    <hkern u1="&#x201c;" u2="O" k="10" />
    <hkern u1="&#x201c;" u2="J" k="40" />
    <hkern u1="&#x201c;" u2="G" k="10" />
    <hkern u1="&#x201c;" u2="C" k="10" />
    <hkern u1="&#x201c;" u2="A" k="60" />
    <hkern u1="&#x201d;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x201d;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x201d;" u2="&#x237;" k="40" />
    <hkern u1="&#x201d;" u2="&#x219;" k="5" />
    <hkern u1="&#x201d;" u2="&#x218;" k="5" />
    <hkern u1="&#x201d;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x201d;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x201d;" u2="&#x17e;" k="4" />
    <hkern u1="&#x201d;" u2="&#x17d;" k="4" />
    <hkern u1="&#x201d;" u2="&#x17c;" k="4" />
    <hkern u1="&#x201d;" u2="&#x17b;" k="4" />
    <hkern u1="&#x201d;" u2="&#x17a;" k="4" />
    <hkern u1="&#x201d;" u2="&#x179;" k="4" />
    <hkern u1="&#x201d;" u2="&#x161;" k="5" />
    <hkern u1="&#x201d;" u2="&#x160;" k="5" />
    <hkern u1="&#x201d;" u2="&#x15f;" k="5" />
    <hkern u1="&#x201d;" u2="&#x15e;" k="5" />
    <hkern u1="&#x201d;" u2="&#x15d;" k="5" />
    <hkern u1="&#x201d;" u2="&#x15c;" k="5" />
    <hkern u1="&#x201d;" u2="&#x15b;" k="5" />
    <hkern u1="&#x201d;" u2="&#x15a;" k="5" />
    <hkern u1="&#x201d;" u2="&#x153;" k="10" />
    <hkern u1="&#x201d;" u2="&#x152;" k="10" />
    <hkern u1="&#x201d;" u2="&#x151;" k="10" />
    <hkern u1="&#x201d;" u2="&#x150;" k="10" />
    <hkern u1="&#x201d;" u2="&#x14f;" k="10" />
    <hkern u1="&#x201d;" u2="&#x14e;" k="10" />
    <hkern u1="&#x201d;" u2="&#x14d;" k="10" />
    <hkern u1="&#x201d;" u2="&#x14c;" k="10" />
    <hkern u1="&#x201d;" u2="&#x135;" k="40" />
    <hkern u1="&#x201d;" u2="&#x134;" k="40" />
    <hkern u1="&#x201d;" u2="&#x133;" k="40" />
    <hkern u1="&#x201d;" u2="&#x132;" k="40" />
    <hkern u1="&#x201d;" u2="&#x123;" k="10" />
    <hkern u1="&#x201d;" u2="&#x122;" k="10" />
    <hkern u1="&#x201d;" u2="&#x121;" k="10" />
    <hkern u1="&#x201d;" u2="&#x120;" k="10" />
    <hkern u1="&#x201d;" u2="&#x11f;" k="10" />
    <hkern u1="&#x201d;" u2="&#x11e;" k="10" />
    <hkern u1="&#x201d;" u2="&#x11d;" k="10" />
    <hkern u1="&#x201d;" u2="&#x11c;" k="10" />
    <hkern u1="&#x201d;" u2="&#x10d;" k="10" />
    <hkern u1="&#x201d;" u2="&#x10c;" k="10" />
    <hkern u1="&#x201d;" u2="&#x10b;" k="10" />
    <hkern u1="&#x201d;" u2="&#x10a;" k="10" />
    <hkern u1="&#x201d;" u2="&#x109;" k="10" />
    <hkern u1="&#x201d;" u2="&#x108;" k="10" />
    <hkern u1="&#x201d;" u2="&#x107;" k="10" />
    <hkern u1="&#x201d;" u2="&#x106;" k="10" />
    <hkern u1="&#x201d;" u2="&#x105;" k="60" />
    <hkern u1="&#x201d;" u2="&#x104;" k="60" />
    <hkern u1="&#x201d;" u2="&#x103;" k="60" />
    <hkern u1="&#x201d;" u2="&#x102;" k="60" />
    <hkern u1="&#x201d;" u2="&#x101;" k="60" />
    <hkern u1="&#x201d;" u2="&#x100;" k="60" />
    <hkern u1="&#x201d;" u2="&#xf8;" k="10" />
    <hkern u1="&#x201d;" u2="&#xf6;" k="10" />
    <hkern u1="&#x201d;" u2="&#xf5;" k="10" />
    <hkern u1="&#x201d;" u2="&#xf4;" k="10" />
    <hkern u1="&#x201d;" u2="&#xf3;" k="10" />
    <hkern u1="&#x201d;" u2="&#xf2;" k="10" />
    <hkern u1="&#x201d;" u2="&#xe7;" k="10" />
    <hkern u1="&#x201d;" u2="&#xe5;" k="60" />
    <hkern u1="&#x201d;" u2="&#xe4;" k="60" />
    <hkern u1="&#x201d;" u2="&#xe3;" k="60" />
    <hkern u1="&#x201d;" u2="&#xe2;" k="60" />
    <hkern u1="&#x201d;" u2="&#xe1;" k="60" />
    <hkern u1="&#x201d;" u2="&#xe0;" k="60" />
    <hkern u1="&#x201d;" u2="&#xdf;" k="5" />
    <hkern u1="&#x201d;" u2="&#xd8;" k="10" />
    <hkern u1="&#x201d;" u2="&#xd6;" k="10" />
    <hkern u1="&#x201d;" u2="&#xd5;" k="10" />
    <hkern u1="&#x201d;" u2="&#xd4;" k="10" />
    <hkern u1="&#x201d;" u2="&#xd3;" k="10" />
    <hkern u1="&#x201d;" u2="&#xd2;" k="10" />
    <hkern u1="&#x201d;" u2="&#xc7;" k="10" />
    <hkern u1="&#x201d;" u2="&#xc5;" k="60" />
    <hkern u1="&#x201d;" u2="&#xc4;" k="60" />
    <hkern u1="&#x201d;" u2="&#xc3;" k="60" />
    <hkern u1="&#x201d;" u2="&#xc2;" k="60" />
    <hkern u1="&#x201d;" u2="&#xc1;" k="60" />
    <hkern u1="&#x201d;" u2="&#xc0;" k="60" />
    <hkern u1="&#x201d;" u2="z" k="4" />
    <hkern u1="&#x201d;" u2="x" k="2" />
    <hkern u1="&#x201d;" u2="s" k="5" />
    <hkern u1="&#x201d;" u2="q" k="10" />
    <hkern u1="&#x201d;" u2="o" k="10" />
    <hkern u1="&#x201d;" u2="j" k="40" />
    <hkern u1="&#x201d;" u2="g" k="10" />
    <hkern u1="&#x201d;" u2="c" k="10" />
    <hkern u1="&#x201d;" u2="a" k="60" />
    <hkern u1="&#x201d;" u2="Z" k="4" />
    <hkern u1="&#x201d;" u2="X" k="2" />
    <hkern u1="&#x201d;" u2="S" k="5" />
    <hkern u1="&#x201d;" u2="Q" k="10" />
    <hkern u1="&#x201d;" u2="O" k="10" />
    <hkern u1="&#x201d;" u2="J" k="40" />
    <hkern u1="&#x201d;" u2="G" k="10" />
    <hkern u1="&#x201d;" u2="C" k="10" />
    <hkern u1="&#x201d;" u2="A" k="60" />
    <hkern u1="&#x201e;" u2="&#x1ef3;" k="80" />
    <hkern u1="&#x201e;" u2="&#x1ef2;" k="80" />
    <hkern u1="&#x201e;" u2="&#x1e85;" k="40" />
    <hkern u1="&#x201e;" u2="&#x1e84;" k="40" />
    <hkern u1="&#x201e;" u2="&#x1e83;" k="40" />
    <hkern u1="&#x201e;" u2="&#x1e82;" k="40" />
    <hkern u1="&#x201e;" u2="&#x1e81;" k="40" />
    <hkern u1="&#x201e;" u2="&#x1e80;" k="40" />
    <hkern u1="&#x201e;" u2="&#x1e6b;" k="60" />
    <hkern u1="&#x201e;" u2="&#x1e6a;" k="60" />
    <hkern u1="&#x201e;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x201e;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x201e;" u2="&#x21b;" k="60" />
    <hkern u1="&#x201e;" u2="&#x21a;" k="60" />
    <hkern u1="&#x201e;" u2="&#x219;" k="5" />
    <hkern u1="&#x201e;" u2="&#x218;" k="5" />
    <hkern u1="&#x201e;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x201e;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x201e;" u2="&#x178;" k="80" />
    <hkern u1="&#x201e;" u2="&#x177;" k="80" />
    <hkern u1="&#x201e;" u2="&#x176;" k="80" />
    <hkern u1="&#x201e;" u2="&#x175;" k="40" />
    <hkern u1="&#x201e;" u2="&#x174;" k="40" />
    <hkern u1="&#x201e;" u2="&#x173;" k="6" />
    <hkern u1="&#x201e;" u2="&#x172;" k="6" />
    <hkern u1="&#x201e;" u2="&#x171;" k="6" />
    <hkern u1="&#x201e;" u2="&#x170;" k="6" />
    <hkern u1="&#x201e;" u2="&#x16f;" k="6" />
    <hkern u1="&#x201e;" u2="&#x16e;" k="6" />
    <hkern u1="&#x201e;" u2="&#x16d;" k="6" />
    <hkern u1="&#x201e;" u2="&#x16c;" k="6" />
    <hkern u1="&#x201e;" u2="&#x16b;" k="6" />
    <hkern u1="&#x201e;" u2="&#x16a;" k="6" />
    <hkern u1="&#x201e;" u2="&#x169;" k="6" />
    <hkern u1="&#x201e;" u2="&#x168;" k="6" />
    <hkern u1="&#x201e;" u2="&#x167;" k="60" />
    <hkern u1="&#x201e;" u2="&#x166;" k="60" />
    <hkern u1="&#x201e;" u2="&#x165;" k="60" />
    <hkern u1="&#x201e;" u2="&#x164;" k="60" />
    <hkern u1="&#x201e;" u2="&#x163;" k="60" />
    <hkern u1="&#x201e;" u2="&#x162;" k="60" />
    <hkern u1="&#x201e;" u2="&#x161;" k="5" />
    <hkern u1="&#x201e;" u2="&#x160;" k="5" />
    <hkern u1="&#x201e;" u2="&#x15f;" k="5" />
    <hkern u1="&#x201e;" u2="&#x15e;" k="5" />
    <hkern u1="&#x201e;" u2="&#x15d;" k="5" />
    <hkern u1="&#x201e;" u2="&#x15c;" k="5" />
    <hkern u1="&#x201e;" u2="&#x15b;" k="5" />
    <hkern u1="&#x201e;" u2="&#x15a;" k="5" />
    <hkern u1="&#x201e;" u2="&#x153;" k="10" />
    <hkern u1="&#x201e;" u2="&#x152;" k="10" />
    <hkern u1="&#x201e;" u2="&#x151;" k="10" />
    <hkern u1="&#x201e;" u2="&#x150;" k="10" />
    <hkern u1="&#x201e;" u2="&#x14f;" k="10" />
    <hkern u1="&#x201e;" u2="&#x14e;" k="10" />
    <hkern u1="&#x201e;" u2="&#x14d;" k="10" />
    <hkern u1="&#x201e;" u2="&#x14c;" k="10" />
    <hkern u1="&#x201e;" u2="&#x123;" k="10" />
    <hkern u1="&#x201e;" u2="&#x122;" k="10" />
    <hkern u1="&#x201e;" u2="&#x121;" k="10" />
    <hkern u1="&#x201e;" u2="&#x120;" k="10" />
    <hkern u1="&#x201e;" u2="&#x11f;" k="10" />
    <hkern u1="&#x201e;" u2="&#x11e;" k="10" />
    <hkern u1="&#x201e;" u2="&#x11d;" k="10" />
    <hkern u1="&#x201e;" u2="&#x11c;" k="10" />
    <hkern u1="&#x201e;" u2="&#x10d;" k="10" />
    <hkern u1="&#x201e;" u2="&#x10c;" k="10" />
    <hkern u1="&#x201e;" u2="&#x10b;" k="10" />
    <hkern u1="&#x201e;" u2="&#x10a;" k="10" />
    <hkern u1="&#x201e;" u2="&#x109;" k="10" />
    <hkern u1="&#x201e;" u2="&#x108;" k="10" />
    <hkern u1="&#x201e;" u2="&#x107;" k="10" />
    <hkern u1="&#x201e;" u2="&#x106;" k="10" />
    <hkern u1="&#x201e;" u2="&#xff;" k="80" />
    <hkern u1="&#x201e;" u2="&#xfd;" k="80" />
    <hkern u1="&#x201e;" u2="&#xfc;" k="6" />
    <hkern u1="&#x201e;" u2="&#xfb;" k="6" />
    <hkern u1="&#x201e;" u2="&#xfa;" k="6" />
    <hkern u1="&#x201e;" u2="&#xf9;" k="6" />
    <hkern u1="&#x201e;" u2="&#xf8;" k="10" />
    <hkern u1="&#x201e;" u2="&#xf6;" k="10" />
    <hkern u1="&#x201e;" u2="&#xf5;" k="10" />
    <hkern u1="&#x201e;" u2="&#xf4;" k="10" />
    <hkern u1="&#x201e;" u2="&#xf3;" k="10" />
    <hkern u1="&#x201e;" u2="&#xf2;" k="10" />
    <hkern u1="&#x201e;" u2="&#xe7;" k="10" />
    <hkern u1="&#x201e;" u2="&#xdf;" k="5" />
    <hkern u1="&#x201e;" u2="&#xdd;" k="80" />
    <hkern u1="&#x201e;" u2="&#xdc;" k="6" />
    <hkern u1="&#x201e;" u2="&#xdb;" k="6" />
    <hkern u1="&#x201e;" u2="&#xda;" k="6" />
    <hkern u1="&#x201e;" u2="&#xd9;" k="6" />
    <hkern u1="&#x201e;" u2="&#xd8;" k="10" />
    <hkern u1="&#x201e;" u2="&#xd6;" k="10" />
    <hkern u1="&#x201e;" u2="&#xd5;" k="10" />
    <hkern u1="&#x201e;" u2="&#xd4;" k="10" />
    <hkern u1="&#x201e;" u2="&#xd3;" k="10" />
    <hkern u1="&#x201e;" u2="&#xd2;" k="10" />
    <hkern u1="&#x201e;" u2="&#xc7;" k="10" />
    <hkern u1="&#x201e;" u2="y" k="80" />
    <hkern u1="&#x201e;" u2="w" k="40" />
    <hkern u1="&#x201e;" u2="v" k="60" />
    <hkern u1="&#x201e;" u2="u" k="6" />
    <hkern u1="&#x201e;" u2="t" k="60" />
    <hkern u1="&#x201e;" u2="s" k="5" />
    <hkern u1="&#x201e;" u2="q" k="10" />
    <hkern u1="&#x201e;" u2="o" k="10" />
    <hkern u1="&#x201e;" u2="g" k="10" />
    <hkern u1="&#x201e;" u2="c" k="10" />
    <hkern u1="&#x201e;" u2="Y" k="80" />
    <hkern u1="&#x201e;" u2="W" k="40" />
    <hkern u1="&#x201e;" u2="V" k="60" />
    <hkern u1="&#x201e;" u2="U" k="6" />
    <hkern u1="&#x201e;" u2="T" k="60" />
    <hkern u1="&#x201e;" u2="S" k="5" />
    <hkern u1="&#x201e;" u2="Q" k="10" />
    <hkern u1="&#x201e;" u2="O" k="10" />
    <hkern u1="&#x201e;" u2="G" k="10" />
    <hkern u1="&#x201e;" u2="C" k="10" />
    <hkern u1="&#x2026;" u2="&#x1ef3;" k="80" />
    <hkern u1="&#x2026;" u2="&#x1ef2;" k="80" />
    <hkern u1="&#x2026;" u2="&#x1e85;" k="40" />
    <hkern u1="&#x2026;" u2="&#x1e84;" k="40" />
    <hkern u1="&#x2026;" u2="&#x1e83;" k="40" />
    <hkern u1="&#x2026;" u2="&#x1e82;" k="40" />
    <hkern u1="&#x2026;" u2="&#x1e81;" k="40" />
    <hkern u1="&#x2026;" u2="&#x1e80;" k="40" />
    <hkern u1="&#x2026;" u2="&#x1e6b;" k="60" />
    <hkern u1="&#x2026;" u2="&#x1e6a;" k="60" />
    <hkern u1="&#x2026;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x2026;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x2026;" u2="&#x21b;" k="60" />
    <hkern u1="&#x2026;" u2="&#x21a;" k="60" />
    <hkern u1="&#x2026;" u2="&#x219;" k="5" />
    <hkern u1="&#x2026;" u2="&#x218;" k="5" />
    <hkern u1="&#x2026;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x2026;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x2026;" u2="&#x178;" k="80" />
    <hkern u1="&#x2026;" u2="&#x177;" k="80" />
    <hkern u1="&#x2026;" u2="&#x176;" k="80" />
    <hkern u1="&#x2026;" u2="&#x175;" k="40" />
    <hkern u1="&#x2026;" u2="&#x174;" k="40" />
    <hkern u1="&#x2026;" u2="&#x173;" k="6" />
    <hkern u1="&#x2026;" u2="&#x172;" k="6" />
    <hkern u1="&#x2026;" u2="&#x171;" k="6" />
    <hkern u1="&#x2026;" u2="&#x170;" k="6" />
    <hkern u1="&#x2026;" u2="&#x16f;" k="6" />
    <hkern u1="&#x2026;" u2="&#x16e;" k="6" />
    <hkern u1="&#x2026;" u2="&#x16d;" k="6" />
    <hkern u1="&#x2026;" u2="&#x16c;" k="6" />
    <hkern u1="&#x2026;" u2="&#x16b;" k="6" />
    <hkern u1="&#x2026;" u2="&#x16a;" k="6" />
    <hkern u1="&#x2026;" u2="&#x169;" k="6" />
    <hkern u1="&#x2026;" u2="&#x168;" k="6" />
    <hkern u1="&#x2026;" u2="&#x167;" k="60" />
    <hkern u1="&#x2026;" u2="&#x166;" k="60" />
    <hkern u1="&#x2026;" u2="&#x165;" k="60" />
    <hkern u1="&#x2026;" u2="&#x164;" k="60" />
    <hkern u1="&#x2026;" u2="&#x163;" k="60" />
    <hkern u1="&#x2026;" u2="&#x162;" k="60" />
    <hkern u1="&#x2026;" u2="&#x161;" k="5" />
    <hkern u1="&#x2026;" u2="&#x160;" k="5" />
    <hkern u1="&#x2026;" u2="&#x15f;" k="5" />
    <hkern u1="&#x2026;" u2="&#x15e;" k="5" />
    <hkern u1="&#x2026;" u2="&#x15d;" k="5" />
    <hkern u1="&#x2026;" u2="&#x15c;" k="5" />
    <hkern u1="&#x2026;" u2="&#x15b;" k="5" />
    <hkern u1="&#x2026;" u2="&#x15a;" k="5" />
    <hkern u1="&#x2026;" u2="&#x153;" k="10" />
    <hkern u1="&#x2026;" u2="&#x152;" k="10" />
    <hkern u1="&#x2026;" u2="&#x151;" k="10" />
    <hkern u1="&#x2026;" u2="&#x150;" k="10" />
    <hkern u1="&#x2026;" u2="&#x14f;" k="10" />
    <hkern u1="&#x2026;" u2="&#x14e;" k="10" />
    <hkern u1="&#x2026;" u2="&#x14d;" k="10" />
    <hkern u1="&#x2026;" u2="&#x14c;" k="10" />
    <hkern u1="&#x2026;" u2="&#x123;" k="10" />
    <hkern u1="&#x2026;" u2="&#x122;" k="10" />
    <hkern u1="&#x2026;" u2="&#x121;" k="10" />
    <hkern u1="&#x2026;" u2="&#x120;" k="10" />
    <hkern u1="&#x2026;" u2="&#x11f;" k="10" />
    <hkern u1="&#x2026;" u2="&#x11e;" k="10" />
    <hkern u1="&#x2026;" u2="&#x11d;" k="10" />
    <hkern u1="&#x2026;" u2="&#x11c;" k="10" />
    <hkern u1="&#x2026;" u2="&#x10d;" k="10" />
    <hkern u1="&#x2026;" u2="&#x10c;" k="10" />
    <hkern u1="&#x2026;" u2="&#x10b;" k="10" />
    <hkern u1="&#x2026;" u2="&#x10a;" k="10" />
    <hkern u1="&#x2026;" u2="&#x109;" k="10" />
    <hkern u1="&#x2026;" u2="&#x108;" k="10" />
    <hkern u1="&#x2026;" u2="&#x107;" k="10" />
    <hkern u1="&#x2026;" u2="&#x106;" k="10" />
    <hkern u1="&#x2026;" u2="&#xff;" k="80" />
    <hkern u1="&#x2026;" u2="&#xfd;" k="80" />
    <hkern u1="&#x2026;" u2="&#xfc;" k="6" />
    <hkern u1="&#x2026;" u2="&#xfb;" k="6" />
    <hkern u1="&#x2026;" u2="&#xfa;" k="6" />
    <hkern u1="&#x2026;" u2="&#xf9;" k="6" />
    <hkern u1="&#x2026;" u2="&#xf8;" k="10" />
    <hkern u1="&#x2026;" u2="&#xf6;" k="10" />
    <hkern u1="&#x2026;" u2="&#xf5;" k="10" />
    <hkern u1="&#x2026;" u2="&#xf4;" k="10" />
    <hkern u1="&#x2026;" u2="&#xf3;" k="10" />
    <hkern u1="&#x2026;" u2="&#xf2;" k="10" />
    <hkern u1="&#x2026;" u2="&#xe7;" k="10" />
    <hkern u1="&#x2026;" u2="&#xdf;" k="5" />
    <hkern u1="&#x2026;" u2="&#xdd;" k="80" />
    <hkern u1="&#x2026;" u2="&#xdc;" k="6" />
    <hkern u1="&#x2026;" u2="&#xdb;" k="6" />
    <hkern u1="&#x2026;" u2="&#xda;" k="6" />
    <hkern u1="&#x2026;" u2="&#xd9;" k="6" />
    <hkern u1="&#x2026;" u2="&#xd8;" k="10" />
    <hkern u1="&#x2026;" u2="&#xd6;" k="10" />
    <hkern u1="&#x2026;" u2="&#xd5;" k="10" />
    <hkern u1="&#x2026;" u2="&#xd4;" k="10" />
    <hkern u1="&#x2026;" u2="&#xd3;" k="10" />
    <hkern u1="&#x2026;" u2="&#xd2;" k="10" />
    <hkern u1="&#x2026;" u2="&#xc7;" k="10" />
    <hkern u1="&#x2026;" u2="y" k="80" />
    <hkern u1="&#x2026;" u2="w" k="40" />
    <hkern u1="&#x2026;" u2="v" k="60" />
    <hkern u1="&#x2026;" u2="u" k="6" />
    <hkern u1="&#x2026;" u2="t" k="60" />
    <hkern u1="&#x2026;" u2="s" k="5" />
    <hkern u1="&#x2026;" u2="q" k="10" />
    <hkern u1="&#x2026;" u2="o" k="10" />
    <hkern u1="&#x2026;" u2="g" k="10" />
    <hkern u1="&#x2026;" u2="c" k="10" />
    <hkern u1="&#x2026;" u2="Y" k="80" />
    <hkern u1="&#x2026;" u2="W" k="40" />
    <hkern u1="&#x2026;" u2="V" k="60" />
    <hkern u1="&#x2026;" u2="U" k="6" />
    <hkern u1="&#x2026;" u2="T" k="60" />
    <hkern u1="&#x2026;" u2="S" k="5" />
    <hkern u1="&#x2026;" u2="Q" k="10" />
    <hkern u1="&#x2026;" u2="O" k="10" />
    <hkern u1="&#x2026;" u2="G" k="10" />
    <hkern u1="&#x2026;" u2="C" k="10" />
    <hkern g1="seven.alt" g2="seven.alt" k="-4" />
    <hkern g1="seven.alt" g2="four.alt" k="40" />
    <hkern u1="&#x22;" g2="four.alt" k="40" />
    <hkern g1="hyphen"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	k="10" />
    <hkern g1="hyphen"
	g2="T,uni0162,Tcaron,Tbar,uni021A,uni1E6A"
	k="32" />
    <hkern g1="hyphen"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="4" />
    <hkern g1="hyphen"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="18" />
    <hkern g1="hyphen"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="hyphen"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="10" />
    <hkern g1="hyphen"
	g2="t,uni0163,tcaron,tbar,uni021B,uni1E6B"
	k="32" />
    <hkern g1="hyphen"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="4" />
    <hkern g1="hyphen"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="18" />
    <hkern g1="hyphen"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="B,uni1E02"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="1" />
    <hkern g1="B,uni1E02"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="B,uni1E02"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="1" />
    <hkern g1="B,uni1E02"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="B,uni1E02"
	g2="uni02BC,quoteright"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="11" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="11" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="uni02BC,quoteright"
	k="6" />
    <hkern g1="D,Eth,Dcaron,Dcroat,uni1E0A"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	k="1" />
    <hkern g1="D,Eth,Dcaron,Dcroat,uni1E0A"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="D,Eth,Dcaron,Dcroat,uni1E0A"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="1" />
    <hkern g1="D,Eth,Dcaron,Dcroat,uni1E0A"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="D,Eth,Dcaron,Dcroat,uni1E0A"
	g2="uni02BC,quoteright"
	k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat,uni1E0A"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="D,Eth,Dcaron,Dcroat,uni1E0A"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="F,uni1E1E"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	k="13" />
    <hkern g1="F,uni1E1E"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="F,uni1E1E"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="13" />
    <hkern g1="F,uni1E1E"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="F,uni1E1E"
	g2="AE,AEacute"
	k="40" />
    <hkern g1="F,uni1E1E"
	g2="ae,aeacute"
	k="40" />
    <hkern g1="F,uni1E1E"
	g2="J,IJ,Jcircumflex"
	k="20" />
    <hkern g1="F,uni1E1E"
	g2="j,ij,jcircumflex,uni0237"
	k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	k="1" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="1" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="uni02BC,quoteright"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="J,Jcircumflex"
	g2="AE,AEacute"
	k="4" />
    <hkern g1="J,Jcircumflex"
	g2="ae,aeacute"
	k="4" />
    <hkern g1="K,uni0136"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="7" />
    <hkern g1="K,uni0136"
	g2="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	k="7" />
    <hkern g1="K,uni0136"
	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	k="7" />
    <hkern g1="K,uni0136"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,uni1E60"
	k="5" />
    <hkern g1="K,uni0136"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="4" />
    <hkern g1="K,uni0136"
	g2="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	k="7" />
    <hkern g1="K,uni0136"
	g2="g,gcircumflex,gbreve,gdotaccent,uni0123"
	k="7" />
    <hkern g1="K,uni0136"
	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	k="7" />
    <hkern g1="K,uni0136"
	g2="s,germandbls,sacute,scircumflex,scedilla,scaron,uni0219,uni1E61"
	k="5" />
    <hkern g1="K,uni0136"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="4" />
    <hkern g1="L,Lacute,uni013B,Ldot,Lslash"
	g2="T,uni0162,Tcaron,Tbar,uni021A,uni1E6A"
	k="50" />
    <hkern g1="L,Lacute,uni013B,Ldot,Lslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="17" />
    <hkern g1="L,Lacute,uni013B,Ldot,Lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="62" />
    <hkern g1="L,Lacute,uni013B,Ldot,Lslash"
	g2="t,uni0163,tcaron,tbar,uni021B,uni1E6B"
	k="50" />
    <hkern g1="L,Lacute,uni013B,Ldot,Lslash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="17" />
    <hkern g1="L,Lacute,uni013B,Ldot,Lslash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="62" />
    <hkern g1="L,Lacute,uni013B,Ldot,Lslash"
	g2="uni02BC,quoteright"
	k="75" />
    <hkern g1="L,Lacute,uni013B,Ldot,Lslash"
	g2="J,IJ,Jcircumflex"
	k="-5" />
    <hkern g1="L,Lacute,uni013B,Ldot,Lslash"
	g2="j,ij,jcircumflex,uni0237"
	k="-5" />
    <hkern g1="L,Lacute,uni013B,Ldot,Lslash"
	g2="hyphen,uni00AD"
	k="40" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	k="1" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="1" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="uni02BC,quoteright"
	k="10" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="P,uni1E56"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	k="13" />
    <hkern g1="P,uni1E56"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="2" />
    <hkern g1="P,uni1E56"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="13" />
    <hkern g1="P,uni1E56"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="2" />
    <hkern g1="P,uni1E56"
	g2="AE,AEacute"
	k="30" />
    <hkern g1="P,uni1E56"
	g2="ae,aeacute"
	k="30" />
    <hkern g1="P,uni1E56"
	g2="J,IJ,Jcircumflex"
	k="18" />
    <hkern g1="P,uni1E56"
	g2="j,ij,jcircumflex,uni0237"
	k="18" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,uni1E60"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="7" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,uni1E60"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="7" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,uni1E60"
	g2="uni02BC,quoteright"
	k="5" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,uni1E60"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,uni1E60"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A,uni1E6A"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	k="38" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A,uni1E6A"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="38" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A,uni1E6A"
	g2="AE,AEacute"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A,uni1E6A"
	g2="ae,aeacute"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A,uni1E6A"
	g2="J,IJ,Jcircumflex"
	k="30" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A,uni1E6A"
	g2="j,ij,jcircumflex,uni0237"
	k="30" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A,uni1E6A"
	g2="hyphen,uni00AD"
	k="32" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="AE,AEacute"
	k="4" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="ae,aeacute"
	k="4" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	k="15" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="15" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="AE,AEacute"
	k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="ae,aeacute"
	k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J,IJ,Jcircumflex"
	k="13" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="j,ij,jcircumflex,uni0237"
	k="13" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,uni00AD"
	k="4" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	k="49" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="49" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="AE,AEacute"
	k="63" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="ae,aeacute"
	k="63" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="J,IJ,Jcircumflex"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="j,ij,jcircumflex,uni0237"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,uni1E60"
	k="7" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="g,gcircumflex,gbreve,gdotaccent,uni0123"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="s,germandbls,sacute,scircumflex,scedilla,scaron,uni0219,uni1E61"
	k="7" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="hyphen,uni00AD"
	k="18" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="hyphen,uni00AD"
	k="15" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="T,uni0162,Tcaron,Tbar,uni021A,uni1E6A"
	k="38" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="15" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="49" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="t,uni0163,tcaron,tbar,uni021B,uni1E6B"
	k="38" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="15" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="49" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="uni02BC,quoteright"
	k="60" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="1" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	k="1" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	k="1" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	k="1" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="g,gcircumflex,gbreve,gdotaccent,uni0123"
	k="1" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	k="1" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="hyphen,uni00AD"
	k="10" />
    <hkern g1="b,uni1E03"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="1" />
    <hkern g1="b,uni1E03"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="b,uni1E03"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="1" />
    <hkern g1="b,uni1E03"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="b,uni1E03"
	g2="uni02BC,quoteright"
	k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="11" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="11" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="uni02BC,quoteright"
	k="6" />
    <hkern g1="d,eth,dcaron,dcroat,uni1E0B"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	k="1" />
    <hkern g1="d,eth,dcaron,dcroat,uni1E0B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="d,eth,dcaron,dcroat,uni1E0B"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="1" />
    <hkern g1="d,eth,dcaron,dcroat,uni1E0B"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="d,eth,dcaron,dcroat,uni1E0B"
	g2="uni02BC,quoteright"
	k="10" />
    <hkern g1="d,eth,dcaron,dcroat,uni1E0B"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="d,eth,dcaron,dcroat,uni1E0B"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="f,uni1E1F"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	k="13" />
    <hkern g1="f,uni1E1F"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="f,uni1E1F"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="13" />
    <hkern g1="f,uni1E1F"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="f,uni1E1F"
	g2="AE,AEacute"
	k="40" />
    <hkern g1="f,uni1E1F"
	g2="ae,aeacute"
	k="40" />
    <hkern g1="f,uni1E1F"
	g2="J,IJ,Jcircumflex"
	k="20" />
    <hkern g1="f,uni1E1F"
	g2="j,ij,jcircumflex,uni0237"
	k="20" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,uni0123"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	k="1" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,uni0123"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,uni0123"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="1" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,uni0123"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,uni0123"
	g2="uni02BC,quoteright"
	k="10" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,uni0123"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,uni0123"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="j,jcircumflex,uni0237"
	g2="AE,AEacute"
	k="4" />
    <hkern g1="j,jcircumflex,uni0237"
	g2="ae,aeacute"
	k="4" />
    <hkern g1="k,uni0137,kgreenlandic"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="7" />
    <hkern g1="k,uni0137,kgreenlandic"
	g2="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	k="7" />
    <hkern g1="k,uni0137,kgreenlandic"
	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	k="7" />
    <hkern g1="k,uni0137,kgreenlandic"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,uni1E60"
	k="5" />
    <hkern g1="k,uni0137,kgreenlandic"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="4" />
    <hkern g1="k,uni0137,kgreenlandic"
	g2="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	k="7" />
    <hkern g1="k,uni0137,kgreenlandic"
	g2="g,gcircumflex,gbreve,gdotaccent,uni0123"
	k="7" />
    <hkern g1="k,uni0137,kgreenlandic"
	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	k="7" />
    <hkern g1="k,uni0137,kgreenlandic"
	g2="s,germandbls,sacute,scircumflex,scedilla,scaron,uni0219,uni1E61"
	k="5" />
    <hkern g1="k,uni0137,kgreenlandic"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="4" />
    <hkern g1="l,lacute,uni013C,ldot,lslash"
	g2="T,uni0162,Tcaron,Tbar,uni021A,uni1E6A"
	k="50" />
    <hkern g1="l,lacute,uni013C,ldot,lslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="17" />
    <hkern g1="l,lacute,uni013C,ldot,lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="62" />
    <hkern g1="l,lacute,uni013C,ldot,lslash"
	g2="t,uni0163,tcaron,tbar,uni021B,uni1E6B"
	k="50" />
    <hkern g1="l,lacute,uni013C,ldot,lslash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="17" />
    <hkern g1="l,lacute,uni013C,ldot,lslash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="62" />
    <hkern g1="l,lacute,uni013C,ldot,lslash"
	g2="uni02BC,quoteright"
	k="75" />
    <hkern g1="l,lacute,uni013C,ldot,lslash"
	g2="J,IJ,Jcircumflex"
	k="-5" />
    <hkern g1="l,lacute,uni013C,ldot,lslash"
	g2="j,ij,jcircumflex,uni0237"
	k="-5" />
    <hkern g1="l,lacute,uni013C,ldot,lslash"
	g2="hyphen,uni00AD"
	k="40" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	k="1" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="1" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	g2="uni02BC,quoteright"
	k="10" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="p,uni1E57"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	k="13" />
    <hkern g1="p,uni1E57"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="2" />
    <hkern g1="p,uni1E57"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="13" />
    <hkern g1="p,uni1E57"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="2" />
    <hkern g1="p,uni1E57"
	g2="AE,AEacute"
	k="30" />
    <hkern g1="p,uni1E57"
	g2="ae,aeacute"
	k="30" />
    <hkern g1="p,uni1E57"
	g2="J,IJ,Jcircumflex"
	k="18" />
    <hkern g1="p,uni1E57"
	g2="j,ij,jcircumflex,uni0237"
	k="18" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,uni0219,uni1E61"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="7" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,uni0219,uni1E61"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="7" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,uni0219,uni1E61"
	g2="uni02BC,quoteright"
	k="5" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,uni0219,uni1E61"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,uni0219,uni1E61"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B,uni1E6B"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	k="38" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B,uni1E6B"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="38" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B,uni1E6B"
	g2="AE,AEacute"
	k="50" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B,uni1E6B"
	g2="ae,aeacute"
	k="50" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B,uni1E6B"
	g2="J,IJ,Jcircumflex"
	k="30" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B,uni1E6B"
	g2="j,ij,jcircumflex,uni0237"
	k="30" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B,uni1E6B"
	g2="hyphen,uni00AD"
	k="32" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="AE,AEacute"
	k="4" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="ae,aeacute"
	k="4" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	k="15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="AE,AEacute"
	k="25" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="ae,aeacute"
	k="25" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="J,IJ,Jcircumflex"
	k="13" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="j,ij,jcircumflex,uni0237"
	k="13" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="hyphen,uni00AD"
	k="4" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	k="49" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="49" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="AE,AEacute"
	k="63" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="ae,aeacute"
	k="63" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="J,IJ,Jcircumflex"
	k="35" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="j,ij,jcircumflex,uni0237"
	k="35" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="15" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	k="15" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	k="15" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,uni1E60"
	k="7" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	k="15" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="g,gcircumflex,gbreve,gdotaccent,uni0123"
	k="15" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	k="15" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="s,germandbls,sacute,scircumflex,scedilla,scaron,uni0219,uni1E61"
	k="7" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="hyphen,uni00AD"
	k="18" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="hyphen,uni00AD"
	k="15" />
    <hkern g1="uni02BC,quoteright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek"
	k="60" />
    <hkern g1="uni02BC,quoteright"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="4" />
    <hkern g1="uni02BC,quoteright"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="60" />
    <hkern g1="uni02BC,quoteright"
	g2="z,zacute,zdotaccent,zcaron"
	k="4" />
    <hkern g1="uni02BC,quoteright"
	g2="J,IJ,Jcircumflex"
	k="40" />
    <hkern g1="uni02BC,quoteright"
	g2="j,ij,jcircumflex,uni0237"
	k="40" />
    <hkern g1="uni02BC,quoteright"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="10" />
    <hkern g1="uni02BC,quoteright"
	g2="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	k="10" />
    <hkern g1="uni02BC,quoteright"
	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	k="10" />
    <hkern g1="uni02BC,quoteright"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,uni1E60"
	k="5" />
    <hkern g1="uni02BC,quoteright"
	g2="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	k="10" />
    <hkern g1="uni02BC,quoteright"
	g2="g,gcircumflex,gbreve,gdotaccent,uni0123"
	k="10" />
    <hkern g1="uni02BC,quoteright"
	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	k="10" />
    <hkern g1="uni02BC,quoteright"
	g2="s,germandbls,sacute,scircumflex,scedilla,scaron,uni0219,uni1E61"
	k="5" />
  </font>
</defs></svg>
