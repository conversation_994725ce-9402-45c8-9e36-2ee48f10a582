/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
*     { background:none !important; text-align:left !important; }
html  { margin:0 !important; padding:0 !important; }
body  { background:#fff !important; font-size:9pt !important; padding:0 !important; margin:10px !important; }
a     { color:#2976c9 !important; }
th,td { color:#2f2f2f !important;  border-color:#ccc !important; }

.header-container,
.nav-container,
.footer-container,
.pager,
.toolbar,
.actions,
.buttons-set { display:none !important; }

.page-print .data-table .cart-tax-total { background-position:100% -54px; }
.page-print .data-table .cart-tax-info { display:block !important; }
