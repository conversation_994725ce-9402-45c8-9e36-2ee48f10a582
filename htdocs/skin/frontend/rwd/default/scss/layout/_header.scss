//
// Magento
//
// NOTICE OF LICENSE
//
// This source file is subject to the Academic Free License (AFL 3.0)
// that is bundled with this package in the file LICENSE_AFL.txt.
// It is also available through the world-wide-web at this URL:
// http://opensource.org/licenses/afl-3.0.php
// If you did not receive a copy of the license and are unable to
// obtain it through the world-wide-web, please send an email
// to <EMAIL> so we can send you a copy immediately.
//
// DISCLAIMER
//
// Do not edit or add to this file if you wish to upgrade Magento to newer
// versions in the future. If you wish to customize Magento for your
// needs please refer to http://www.magento.com for more information.
//
// @category    design
// @package     rwd_default
// @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
// @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
//

/* ============================================ *
 * Header
 * ============================================ */

@include bp(min-width, $bp-medium + 1) {

    .page-header {
        max-width: $max-container-width;
        margin: 0 auto;
        padding: 0 $trim;
    }

    .page-header-container {
        position: relative;
    }

}

.header-language-container,
.page-header {
    font-family: $f-stack-special;
}

/* ============================================ *
 * Language switcher + welcome message
 * ============================================ */

.header-language-background {
    padding: $gap;
    background-color: $c-action;
    text-transform: uppercase;

    .header-language-container {
        max-width: $max-content-width;
        margin-left: auto;
        margin-right: auto;
    }

    &:after {
        @include clearfix;
    }

    .form-language,
    .currency-switcher {
        float: left;
        margin-right: $gap;
    }

    .welcome-msg {
        float: right;
    }
}

.page-header-container .store-language-container {
    float: right;
    max-width: 35%;
    padding: 13px 15px 10px 10px;

    label {
        display: none;
    }
    select {
        max-width: 100%;
    }
}

@include bp(max-width, $bp-xsmall) {
    .page-header-container .store-language-container {
        padding-top: 8px;

        select {
            // Setting this font-size will causing zooming on iOs devices, but it keeps header more tidy
            font-size: $f-size-xs;
        }
    }
}

.header-language-background,
.header-language-background a {
    color: $c-text-white;
}

@include bp(max-width, $bp-medium) {

    .header-language-background {
        display: none;
    }

}

/* ============================================ *
 * Logo
 * ============================================ */

.logo {
    display: block;
    float: left;
    width: 65%;
    min-width: 100px;
    min-height: 44px;
    text-align: center;
    padding: $gap;

    img {
        max-width: 100%;
    }

    .small {
        display: block;
    }
    .large {
        display: none;
    }
}

.logo:hover {
    opacity: 0.8;
}

// ---------------------------------------------

@include bp(min-width, $bp-medium + 1) {

    .logo {
        max-width: 300px;
        min-height: 90px;

        .small {
            display: none;
        }
        .large {
            display: block;
        }
    }

}

/* ============================================ *
 * Skip Links
 * ============================================ */

.skip-links {
    clear: both;
    overflow: hidden;
    border-top: 1px solid #EDEDED;
    border-bottom: 1px solid #EDEDED;
}

// ---------------------------------------------

@include bp(min-width, $bp-medium + 1) {

    .skip-links {
        border: 0;
    }

}

/* -------------------------------------------- *
 * Skip - Link
 */

.skip-link {
    position: relative;
    float: left;
    width: 20%;
    height: 44px;
    background: #FFFFFF;
    color: $c-text;
    line-height: 42px;
    text-align: center;
}

a.skip-link {
    text-decoration: none;
}

.skip-link:not(.skip-active):hover {
    opacity: 0.8;
}

// ---------------------------------------------

@include bp(max-width, $bp-medium) {

    .skip-link {
        border-right: 1px solid #EDEDED;
    }

    .skip-link:last-child {
        border-right: 0;
    }

}

// ---------------------------------------------

@include bp(min-width, 500px) {

    .skip-link {
        width: 25%;
    }

}

/* -------------------------------------------- *
 * Skip Link - Active
 */
@include bp(max-width, $bp-medium) {

    .skip-link.skip-active {
        background: #EDEDED;
        color: inherit;
    }

    .skip-link.skip-active span {
        color: inherit;
    }

}

/* -------------------------------------------- *
 * Skip Link - Icon
 */

.skip-link .icon {
    display: inline-block;
    width: 30px;
    height: 30px;
    vertical-align: middle;
}

.skip-link.skip-nav .icon,
.skip-link.skip-cart:not(.no-count) .icon {
    margin-right: 3px;
}

@include bp(min-width, 500px) {

    .skip-link .icon {
        margin-right: 3px;
    }

}

/* -------------------------------------------- *
 * Skip Link - Label
 */

.skip-link .label {
    display: none;
}

// ---------------------------------------------

@include bp(min-width, 500px) {

    .skip-link .label {
        display: inline;
    }

}

/* ============================================ *
 * Skip Content
 * ============================================ */

.skip-content {
    display: none;
}

.skip-content.skip-active {
    display: block;
    background: #EDEDED;
}

/* ============================================ *
 * Global Header Navigation
 * ============================================ */

@include bp(max-width, $bp-medium) {
    // ---------------------------------------------
    // Link

    #header-account li a,
    .nav-primary a.level0 {
        padding: 0 15px 0 25px;
        border-bottom: 1px solid $c-module-border;
        text-align: left;
        color: $c-text;
        text-transform: uppercase;
        line-height: 30px;
    }

    #header-account li:last-child a,
    .nav-primary li.level0:last-child a.level0 {
        border-bottom: 0;
    }

    .no-touch #header-account a:hover,
    .no-touch .nav-primary a:hover {
        background-color: $c-module-background;
        text-decoration: none;
    }

}

.account-cart-wrapper {
    position: absolute;
    top:0px;
    right:0px;

    @include bp(max-width, $bp-medium) {
        position: static;
    }
}
