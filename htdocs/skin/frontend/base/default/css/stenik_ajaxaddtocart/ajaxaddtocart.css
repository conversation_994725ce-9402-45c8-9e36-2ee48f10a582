@charset "utf-8";
/* CSS Document
   Author: <PERSON><PERSON><PERSON> as developer @ www.stenikgroup.com
*/


/* Stenik Ajax Add To Cart Popup Styles
******************************************/

#stenik-ajaxaddtocart-result { float: left; width: 100%; height: 100%; padding: 0px; margin: 0px; position: fixed; top: 0; left: 0; z-index: 5000; }
.stenik-ajaxAddToCart-result { float: left; width: 600px; height: auto; padding: 0px; margin: -150px 0px 0px -300px; background: #fff; position: absolute; left: 50%; top: 50%; z-index: 5000; }
.stenik-ajaxAddToCart-result .success-message { float: left; width: 100%; height: auto; padding: 12px 48px 12px 48px; margin: 0px; color: #fff; font-size: 16px; line-height: 18px; background: #7cb015 url(images/msgs-mark.svg) no-repeat 20px 16px; background-size: 16px 12px; }
.stenik-ajaxAddToCart-result .error-message { float: left; width: 100%; height: auto; padding: 0px; margin: 0px; color: #fff; font-size: 16px; line-height: 18px; background: #e7352b url(images/msgs-x.svg) no-repeat 20px 16px; background-size: 14px 14px; }
.stenik-ajaxAddToCart-result .ajax-cart-item { float: left; width: 50%; height: auto; padding: 25px 0px 25px 15px; margin: 0px; position: relative; }
.stenik-ajaxAddToCart-result .ajax-cart-item:after { content: ''; width: 1px; height: 100%; padding: 0px; margin: 0px; background: #f1eff0; position: absolute; right: 1px; top: 0px; z-index: 10; }
.stenik-ajaxAddToCart-result .cart-img-wrapper { float: left; width: 80px; height: 80px; padding: 0px; margin: 0px 10px 0px 0px; border: 1px solid #e5e5e5; }
.stenik-ajaxAddToCart-result .cart-img-wrapper img { float: left; width: 100%; height: auto; padding: 0px; margin: 0px; }
.stenik-ajaxAddToCart-result .item-info { float: left; width: 180px; height: auto; padding: 0px 0px 0px 0px; margin: 0px; }
.stenik-ajaxAddToCart-result .item-info .product-name { float: left; width: 100%; height: auto; padding: 0px; margin: 0px 0px 8px 0px; color: #333; font-size: 14px; line-height: 17px; font-weight: normal; }
.stenik-ajaxAddToCart-result .item-info .attributes { float: left; width: 100%; height: auto; padding: 0px; margin: 0px 0px 5px 0px; color: #8f8f8f; font-size: 13px; line-height: 15px; font-weight: normal; }
.stenik-ajaxAddToCart-result .item-info .attributes strong { font-weight: normal; color: #464646; }
.stenik-ajaxAddToCart-result .ajax-cart-total { float: right; width: 49%; margin: 0px; padding: 15px 0px 0px 0px; text-align: center; }
.stenik-ajaxAddToCart-result .ajax-cart-total .cart-summary-count { float: left; width: 100%; height: auto; padding: 0px; margin: 0px 0px 8px 0px; color: #333; font-size: 14px; line-height: 17px; font-weight: 600; text-align: center; }
.stenik-ajaxAddToCart-result .ajax-cart-total .cart-summary-count strong { color: #f27c21; font-weight: 700; }
.stenik-ajaxAddToCart-result .ajax-cart-total .itermediate-price { float: left; width: 100%; height: auto; padding: 0px; margin: 0px 0px 8px 0px; color: #333; font-size: 14px; line-height: 17px; font-weight: 700; text-align: center; }
.stenik-ajaxAddToCart-result .ajax-cart-total .itermediate-price .price-label { text-transform: uppercase; }
.stenik-ajaxAddToCart-result .delivery-price { float: left; width: 100%; height: auto; padding: 15px; margin: 0px; background: #f0f0f0; color: #333; font-size: 13px; line-height: 16px; text-decoration: none; font-weight: normal; text-align: center; }
.stenik-ajaxAddToCart-result .delivery-price.free-shipping { color: #7cb015; }

.stenik-ajaxAddToCart-result .ajax-cart-total .button.checkout-button { float: none; display: table; width: 180px; margin: 0px auto 0px auto; padding-top: 11px; padding-bottom: 11px; }

.stenik-ajaxAddToCart-result .close-popup { width: 43px; height: 43px; background: #ccc url(images/close-popup.svg) no-repeat center center; background-size: 24px 24px; position: absolute; right: -43px; top: 0px; z-index: 5100; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.stenik-ajaxAddToCart-result .close-popup:hover { background-color: #f1f1f1; }

#stenik-ajaxaddtocart-overlay { float: left; width: 100%; height: 100%; padding: 0px; margin: 0px; background: #000; opacity: 0.7; position: fixed; left: 0px; top: 0px; z-index: 4900; }