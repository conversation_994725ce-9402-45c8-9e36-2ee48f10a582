<?php

require_once __DIR__ . '/../abstract.php';

class SpecialPriceForOldProducts extends Mage_Shell_Abstract
{
    protected int $_totalCount = 0;
    protected Varien_Db_Adapter_Interface $_connection;
    private ?int $specialPriceAttrId;
    private ?int $specialPriceFromAttrId;
    private ?int $specialPriceToAttrId;

    public function run()
    {
        $start = microtime(true);
        $this->message("Start");

        $this->specialPriceAttrId = $this->getProductAttrId('special_price');
        $this->specialPriceFromAttrId = $this->getProductAttrId('special_from_date');
        $this->specialPriceToAttrId = $this->getProductAttrId('special_to_date');

        if (!$this->specialPriceAttrId || !$this->specialPriceFromAttrId || !$this->specialPriceToAttrId) {
            $this->message("Error: Attribute not found");
            return;
        }

        $this->_connection = Mage::getSingleton('core/resource')->getConnection('core_write');
        $this->_connection->beginTransaction();

        $this->iterateCollection();

        $this->_connection->commit();
        $this->message("Total count: {$this->_totalCount}");
        $this->message("End: " . (microtime(true) - $start) . " sec");
    }

    public function iterateCollection($page = 1, $limit = 10000)
    {
        $products = Mage::getModel('catalog/product')
            ->getCollection()
            ->addAttributeToSelect('price')
            ->addFieldToFilter('created_at', ['lt' => '2020-06-30 23:59:59'])
            ->setCurPage($page)
            ->setPageSize($limit);

        foreach ($products as $product) {
            $prodId = $product->getId();
            $price = $product->getPrice();
            $formattedPrice = $this->getSpecialPrice($price, 0.5);

            $this->message("SKU:{$product->getSku()} - Price: {$price} SPECIAL: $formattedPrice");

            $this->_connection->insertOnDuplicate('catalog_product_entity_decimal', [
                'entity_type_id' => 4,
                'attribute_id'   => $this->specialPriceAttrId,
                'store_id'       => 0,
                'entity_id'      => $prodId,
                'value'          => $formattedPrice,
            ]);
            $this->_connection->insertOnDuplicate('catalog_product_entity_datetime', [
                'entity_type_id' => 4,
                'attribute_id'   => $this->specialPriceFromAttrId,
                'store_id'       => 0,
                'entity_id'      => $prodId,
                'value'          => '2023-02-01 00:00:00',
            ]);
            $this->_connection->insertOnDuplicate('catalog_product_entity_datetime', [
                'entity_type_id' => 4,
                'attribute_id'   => $this->specialPriceToAttrId,
                'store_id'       => 0,
                'entity_id'      => $prodId,
                'value'          => '2023-03-31 23:59:59',
            ]);

            $this->_totalCount++;
        }

        if ($products->count() === $limit) {
            $this->message("Page: {$page}: processed: {$this->_totalCount}");
            $this->iterateCollection($page + 1, $limit);
        }
    }

    protected function getProductAttrId(string $attrCode): ?int
    {
        $attr = Mage::getModel('eav/config')->getAttribute('catalog_product', $attrCode);
        if ($attr) {
            return $attr->getId();
        }

        return null;
    }

    public function getSpecialPrice($price, $percentage)
    {
        return round($price - ($price * $percentage), 2);
    }

    protected function message($text): void
    {
        echo $text . PHP_EOL;
        Mage::log($text, null, 'pfg-car-473.log');
    }
}

$shell = new SpecialPriceForOldProducts();
$shell->run();
