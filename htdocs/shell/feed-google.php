<?php
/**
 * Shell script to create google or facebook products feed
 *
 * <AUTHOR> Magento Team <<EMAIL>>
 *
 * @since 1.0.3
 */

require_once 'abstract.php';

class Stenik_Shell_Google_Feed extends Mage_Shell_Abstract
{
    protected $_storeId    = null;

    protected $_categories = null;

    protected $_filename   = null;

    protected $_imageType = 'image';

    protected $_namespace  = 'http://base.google.com/ns/1.0';

    public function setStoreId($storeId)
    {
        $this->_storeId = $storeId;
    }

    public function getStoreId()
    {
        return $this->_storeId;
    }

    public function setFilename($filename)
    {
        $this->_filename = $filename;
    }

    public function getFilename()
    {
        return Mage::getBaseDir('media') . DS . 'feed' . DS . $this->_filename;
    }

    public function setImageType($v)
    {
        return $this->_imageType = $v;
    }

    public function getImageType()
    {
        return $this->_imageType;
    }

    public function __construct()
    {
        parent::__construct();
        $this->setStoreId(1);
        $this->setFilename('google-bg.xml');
    }

    /**
     * Run script
     *
     * @return void
     */
    public function run()
    {
        if ($this->getArg('store')) {
            $this->setStoreId($this->getArg('store'));
        }

        if ($this->getArg('file')) {
            $this->setFilename($this->getArg('file'));
        }

        if ($this->getArg('imageType')) {
            $this->setImageType($this->getArg('imageType'));
        }

        $storeId = $this->getStoreId();

        Mage::app()->getStore()->load($storeId);
        Mage::getSingleton('core/locale')->emulate($storeId);

        $this->_export();

        Mage::getSingleton('core/locale')->revert();
    }

    /**
     * [_getCategory description]
     *
     * @param  int $categoryId
     * @return [type]
     */
    protected function _getCategory($categoryId)
    {
        if (is_null($this->_categories)) {
            $this->_categories = Mage::getModel('catalog/category')->getCollection()->setStoreId($this->getStoreId());
            $this->_categories->addAttributeToSelect('*');
        }
        return $this->_categories->getItemById($categoryId);
    }

    /**
     * [_getCategoryName description]
     *
     * @param  Mage_Catalog_Model_Product $product
     * @return string
     */
    protected function _getCategoryName(Mage_Catalog_Model_Product $product)
    {
        $name = null;
        $categoryIds = $product->getCategoryIds();
        $deepestLevel = 0;
        $productCategories = array();
        foreach ($categoryIds as $categoryId) {
            // iterate over all product categories and get the name path of the deepes
            $categoryArray = array();
            $this->_getCategoriesNames($categoryId, $categoryArray);
            if (count($categoryArray) >= $deepestLevel) {
                $deepestLevel = count($categoryArray);
                $productCategories = $categoryArray;
            }
        }
        return implode(" > ", $productCategories); // sprintf("%s > %s", $pCat->getName(), $category->getName());
    }

    /**
     * Return category path as array of names
     *
     * @param  int $categoryId
     * @param  array &$categoryArray
     * @return array
     */
    protected function _getCategoriesNames($categoryId, &$categoryArray)
    {
        $category = $this->_getCategory($categoryId);
        if ($category && $category->getLevel() < 2) {
            return $categoryArray;
        }

        if ($category) {
            array_unshift($categoryArray, $category->getName()); // prepend the category to the array
            $this->_getCategoriesNames($category->getParentId(), $categoryArray);
        }
    }

    /**
     * [_addStoreData description]
     *
     * @param [type] $doc
     * @param [type] $root
     */
    protected function _addStoreData($doc, $root)
    {
        $root = $root->appendChild($doc->createElement('channel'));

        $root->appendChild($doc->createElement('title', Mage::getStoreConfig('general/store_information/name')));
        // Hardocdeed
        $root->appendChild($doc->createElement('link', Mage::getBaseUrl()));

        return $root;
    }

    /**
     * [_roundPrice description]
     *
     * @param  float $price
     * @return float
     */
    protected function _roundPrice($price)
    {
        return round($price, 2);
    }

    protected function getArchiveCategoryProducts()
    {
        $archiveCategoryId = Mage::getStoreConfig('archive_id/settings/archive_cat_id');
        if (!$archiveCategoryId) {
            return false;
        }

        $resource = Mage::getSingleton('core/resource');
        $write = $resource->getConnection('core_write');
        $table = 'catalog_category_product';

        $select = $write->select()
            ->from(['table1' => $table], ['product_id'])
            ->where('category_id = ?', "{$archiveCategoryId}");
        $results = $write->fetchAssoc($select);

        return array_keys($results);
    }

    /**
     * [_export description]
     *
     * @return void
     */
    protected function _export()
    {
        $storeId = $this->getStoreId();
        $doc  = new DOMDocument('1.0', 'utf-8');

        $rss = $doc->createElement('rss');
        $rss->setAttributeNS('http://www.w3.org/2000/xmlns/', 'xmlns:g', $this->_namespace);
        $rss->setAttribute('version', '2.0');

        $root = $doc->appendChild($rss);

        $root = $this->_addStoreData($doc, $root);

        // get archive category products
        $archiveCategoryProductIds = $this->getArchiveCategoryProducts();

        $collection = Mage::getModel('catalog/product')->getCollection();
        if (is_array($archiveCategoryProductIds) && count($archiveCategoryProductIds) > 0) {
            $collection->addIdFilter($archiveCategoryProductIds, true);
        }
        $collection->setStoreId($storeId);
        $collection->addStoreFilter($storeId);
        $collection->addAttributeToSelect(['name', 'description', 'manufacturer', 'sku', 'type_id', $this->getImageType()]);
        Mage::getSingleton('catalog/product_visibility')->addVisibleInCatalogFilterToCollection($collection);
        $collection->addAttributeToFilter('status', Mage_Catalog_Model_Product_Status::STATUS_ENABLED);
        $collection->addUrlRewrite();
        $collection->addFinalPrice();
        $collection->setFlag('require_stock_items', true);

        $i = 0;
        $total = $collection->getSize();
        $allIds = $collection->getAllIds();

        $allPagesIds = array_chunk($allIds, 5000);

        Mage::helper('catalog/product_flat')->disableFlatCollection(true);
        foreach ($allPagesIds as $pageIds) {
            /** @var $collection Mage_Catalog_Model_Resource_Product_Collection */
            $collection = Mage::getModel('catalog/product')->getCollection();
            $collection->setStoreId($storeId);
            $collection->addStoreFilter($storeId);
            $collection->addIdFilter($pageIds);
            Mage::getSingleton('catalog/product_visibility')->addVisibleInCatalogFilterToCollection($collection);
            $collection->addAttributeToFilter('status', Mage_Catalog_Model_Product_Status::STATUS_ENABLED);
            $collection->addUrlRewrite();
            $collection->addFinalPrice();
            $collection->addAttributeToSelect(['name', 'description', 'manufacturer', 'sku', 'type_id', $this->getImageType()]);
            $collection->setFlag('require_stock_items', true);

            foreach ($collection as $product) {
                $i++;
                if ($i % 10 == 0) {
                    printf("%s/%s Memory: %sM\n", $i, $total, number_format(memory_get_usage()/1024/1024, 2, '.', ''));
                }

                if (!$product->isAvailable()) {
                    $product->clearInstance();
                    unset($product);

                    continue;
                }

                if (!$product->getData($this->getImageType())) {
                    $product->clearInstance();
                    unset($product);

                    continue;
                }

                $el = $doc->createElement('item');

                // Create id
                $elId = $doc->createElement('g:id');
                $elId->appendChild($doc->createCDATASection($product->getSku()));
                $el->appendChild($elId);

                // Create title
                $elName = $doc->createElement('title');
                $elName->appendChild($doc->createCDATASection(mb_substr($product->getName(), 0, 140, 'UTF-8')));
                $el->appendChild($elName);

                // Create link
                $elUrl = $doc->createElement('link', $product->getUrlModel()->getUrl($product, array('_ignore_category'=>true)));
                $el->appendChild($elUrl);

                // Create price
                $currencyCode = Mage::app()->getStore()->getCurrentCurrencyCode();

                if ($product->getPrice() != $product->getFinalPrice()) {
                    $el->appendChild($doc->createElement('g:price', $this->_roundPrice($product->getPrice())." ".$currencyCode));
                    $el->appendChild($doc->createElement('g:sale_price', $this->_roundPrice($product->getFinalPrice())." ".$currencyCode));
                } else {
                    $el->appendChild($doc->createElement('g:price', $this->_roundPrice($product->getFinalPrice())." ".$currencyCode));
                }
                $el->appendChild($doc->createElement('g:currency', $currencyCode));
                // Create description
                $elDesc = $doc->createElement('description');
                // $elDesc->appendChild($doc->createCDATASection(mb_substr($product->getDescription(), 0, 9999, 'UTF-8')));
                $elDesc->appendChild($doc->createCDATASection($product->getName()));
                $el->appendChild($elDesc);

                // Create category
                $elCategory = $doc->createElement('g:product_type');
                $elCategory->appendChild($doc->createCDATASection($this->_getCategoryName($product)));
                $el->appendChild($elCategory);

                $elCategory = $doc->createElement('g:google_product_category');
                $elCategory->appendChild($doc->createCDATASection("Vehicles & Parts > Vehicle Parts & Accessories"));
                $el->appendChild($elCategory);

                // Create Image

                /*
                    If the image must be resized:
                    Optimize Varien_Image_Adapter_Gd2 to destruct and flush memory after each product
                 */

                // $imageUrl = Mage::helper('catalog/image')
                //          ->init($product, $this->getImageType(), $product->getData($this->getImageType()))
                //          ->resize(600, 600);

                $imageUrl = '';
                if ($this->getImageType() && $product->getData($this->getImageType()) && $product->getData($this->getImageType()) != 'no_selection') {
                    $imageUrl = Mage::getBaseUrl('media') . 'catalog/product' . $product->getData($this->getImageType());
                }

                $el->appendChild($doc->createElement('g:image_link', $imageUrl));

                $el->appendChild($doc->createElement('g:condition', 'used'));

                // Handle Quantity of simple products in case of configurable
                if ($product->getTypeId() == 'configurable') {
                    $qty = 0;

                    $configurableAttributes = $product->getTypeInstance()->getConfigurableAttributesAsArray();
                    // by default use only first attribute
                    $configurableAttributeCode = $configurableAttributes[0]['attribute_code'];
                    foreach ($product->getTypeInstance()->getUsedProducts() as $child) {
                        if (!$child->isAvailable()) {
                            continue;
                        }
                        $stockItem = Mage::getModel('cataloginventory/stock_item') ->loadByProduct($child);
                        if ($stockItem && $stockItem->getId() && $stockItem->getIsInStock()) {
                            $qty += $stockItem->getQty();
                        }
                    }
                    $el->appendChild($doc->createElement('g:quantity', $qty));
                } else {
                    $qty = 0;
                    $stockItem = Mage::getModel('cataloginventory/stock_item') ->loadByProduct($product);
                    if ($stockItem && $stockItem->getId() && $stockItem->getIsInStock()) {
                        $qty += $stockItem->getQty();
                    }
                    $el->appendChild($doc->createElement('g:quantity', $qty));
                }

                $el->appendChild($doc->createElement('g:availability', 'in stock'));

                $brand = $product->getAttributeText('manufacturer');

                $elManuf = $doc->createElement('g:manufacturer');
                $elManuf->appendChild($doc->createCDATASection($brand));
                $el->appendChild($elManuf);

                $elManuf = $doc->createElement('g:brand');
                $elManuf->appendChild($doc->createCDATASection($brand));
                $el->appendChild($elManuf);

                $el->appendChild($doc->createElement('g:mpn', $product->getSku()));
                $el->appendChild($doc->createElement('g:gtin', null));

                $root->appendChild($el);

                $product->clearInstance();
                unset($product);
            }
            $collection->clear();
        }

        $doc->formatOutput = true;
        $doc->save($this->getFilename());
    }

    /**
     * Retrieve Usage Help Message
     *
     * @return string
     */
    public function usageHelp()
    {
        return <<<USAGE
Usage:  php -f feed-google.php -- [OPTIONS]
    [OPTIONS]
        store           Pass store id for emulation.                                Default: 1
        file            Pass unique filename for saving.                            Default: google-bg.xml
        imageType       Pass image type of products: image, small_image, thumbnail. Default: image
        help            This help

USAGE;
    }
}

$shell = new Stenik_Shell_Google_Feed();
$shell->run();
